_wandb:
    value:
        cli_version: 0.19.11
        m: []
        python_version: 3.10.16
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
            "3":
                - 13
                - 16
                - 23
                - 55
            "4": 3.10.16
            "5": 0.19.11
            "6": 4.51.3
            "8":
                - 5
            "10":
                - 2
            "12": 0.19.11
            "13": linux-x86_64
best_checkpoint_dir:
    value: null
bf16:
    value: false
data_file:
    value: /work/tesi_ediluzio/data/processed/xml_format_optimized/train_set_100k_final_90000.json
dataloader_num_workers:
    value: 0
dataloader_pin_memory:
    value: false
dataset:
    value: data/processed/xml_format_optimized/train_set_100k_final_90000.json
ddp_bucket_cap_mb:
    value: 16
ddp_find_unused_parameters:
    value: false
early_stopping:
    value: false
early_stopping_min_delta:
    value: 0.01
early_stopping_on_validation:
    value: true
early_stopping_patience:
    value: 100
entity:
    value: 337543-unimore
eval_steps:
    value: 500
eval_strategy:
    value: steps
fp16:
    value: true
gradient_accumulation_steps:
    value: 16
gradient_checkpointing:
    value: true
greater_is_better:
    value: false
group_by_length:
    value: false
hub_model_id:
    value: null
hub_token:
    value: null
learning_rate:
    value: 2e-05
load_best_model_at_end:
    value: true
load_in_4bit:
    value: true
load_in_8bit:
    value: false
logging_steps:
    value: 10
lora_alpha:
    value: 16
lora_dropout:
    value: 0.05
lora_r:
    value: 8
lora_target_modules:
    value:
        - q_proj
        - v_proj
lr_scheduler_type:
    value: cosine
max_grad_norm:
    value: 1
max_length:
    value: 1200
max_steps:
    value: 50000
metric_for_best_model:
    value: eval_loss
min_delta:
    value: null
model_name:
    value: google/gemma-2-9b-it
model_name_or_path:
    value: google/gemma-2-9b-it
optim:
    value: adamw_torch
output_dir:
    value: /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma_ultra_optimized
patience:
    value: null
per_device_eval_batch_size:
    value: 1
per_device_train_batch_size:
    value: 1
project_name:
    value: svg_captioning_ultra_opt
push_to_hub:
    value: false
report_to:
    value: wandb
run_name:
    value: gemma_ultra_memory_optimized
save_steps:
    value: 500
save_strategy:
    value: steps
save_total_limit:
    value: 3
seed:
    value: 42
tf32:
    value: false
trainable_params:
    value: 4472832
trainable_percentage:
    value: 0.048374924268823484
val_file:
    value: /work/tesi_ediluzio/data/processed/xml_format_optimized/test_set_100k_final_10000.json
warmup_ratio:
    value: 0.05
weight_decay:
    value: 0.01
