triton==2.2.0
nvidia-nvtx-cu12==12.1.105
nvidia-nccl-cu12==2.19.3
nvidia-cusparse-cu12==12.1.0.106
nvidia-curand-cu12==10.3.2.106
nvidia-cufft-cu12==11.0.2.54
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
setuptools==75.8.0
wheel==0.45.1
urllib3==2.4.0
urllib3==2.3.0
typing_extensions==4.13.2
typing_extensions==4.12.2
tqdm==4.67.1
PyYAML==6.0.2
packaging==25.0
idna==3.10
idna==3.7
filelock==3.18.0
filelock==3.17.0
charset-normalizer==3.4.2
charset-normalizer==3.3.2
certifi==2025.4.26
requests==2.32.3
huggingface-hub==0.31.4
dill==0.3.8
nvidia-cusparselt-cu12==0.6.3
mpmath==1.3.0
triton==3.3.0
triton==3.1.0
webencodings==0.5.1
safetensors==0.5.3
regex==2024.11.6
psutil==7.0.0
nvidia-nvtx-cu12==12.6.77
nvidia-nvjitlink-cu12==12.6.85
nvidia-nccl-cu12==2.26.2
nvidia-curand-cu12==10.3.7.77
nvidia-cufile-cu12==1.11.1.6
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cublas-cu12==12.1.3.1
networkx==3.4.2
MarkupSafe==3.0.2
nvidia-cusparse-cu12==12.5.4.2
nvidia-cufft-cu12==11.3.0.4
nvidia-cudnn-cu12==8.9.2.26
Jinja2==3.1.6
smmap==5.0.2
nvidia-cusolver-cu12==11.4.5.107
torchvision==0.22.0
torchvision==0.17.0
nvidia-nvtx-cu11==11.8.86
nvidia-nccl-cu11==2.19.3
pytz==2025.2
xxhash==3.5.0
tzdata==2025.2
typing-inspection==0.4.1
six==1.17.0
setproctitle==1.3.6
sentry-sdk==2.29.1
pydantic_core==2.33.2
pyarrow==20.0.0
protobuf==6.31.0
propcache==0.3.1
platformdirs==4.3.8
multidict==6.4.4
frozenlist==1.6.0
fsspec==2025.3.0
click==8.2.1
attrs==25.3.0
async-timeout==5.0.1
annotated-types==0.7.0
aiohappyeyeballs==2.6.1
yarl==1.20.0
python-dateutil==2.9.0.post0
pydantic==2.11.4
multiprocess==0.70.16
gitdb==4.0.12
docker-pycreds==0.4.0
aiosignal==1.3.2
pandas==2.2.3
GitPython==3.1.44
aiohttp==3.11.18
wandb==0.19.11
bitsandbytes==0.45.5
accelerate==1.7.0
peft==0.15.1
datasets==3.6.0
pip==25.1.1
tinycss2==1.4.0
pycparser==2.22
pillow==11.2.1
pillow==11.1.0
defusedxml==0.7.1
cssselect2==0.8.0
cffi==1.17.1
cairocffi==1.7.1
CairoSVG==2.8.2
wcwidth==0.2.13
sympy==1.14.0
sympy==1.13.1
joblib==1.5.0
ftfy==6.3.1
nltk==3.9.1
transformers==4.51.3
clip-score==0.2.1
Brotli==1.0.9
gmpy2==2.2.1
mkl-service==2.4.0
PySocks==1.7.1
scipy==1.10.1
mkl_fft==1.3.11
mkl_random==1.2.8
clip==1.0
seaborn==0.13.2
tokenizers==0.21.1
pyparsing==3.2.3
kiwisolver==1.4.8
fonttools==4.58.0
cycler==0.12.1
contourpy==1.3.2
matplotlib==3.10.3
evaluate==0.4.3
threadpoolctl==3.6.0
chardet==5.2.0
absl-py==2.2.2
rouge_score==0.1.2
bert-score==0.3.13
numpy==1.24.3
scikit-learn==1.3.0
torchaudio==2.2.0
nvidia-cusparse-cu11==*********
nvidia-curand-cu11==*********
nvidia-cufft-cu11==*********
nvidia-cuda-runtime-cu11==11.8.89
nvidia-cuda-nvrtc-cu11==11.8.89
nvidia-cuda-cupti-cu11==11.8.87
nvidia-cublas-cu11==*********
nvidia-cusolver-cu11==*********
nvidia-cudnn-cu11==********
torch==2.2.0+cu118
pycocotools==2.0.9
pycocoevalcap==1.2
rouge==1.0.1
