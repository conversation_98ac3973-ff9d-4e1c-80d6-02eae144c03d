#!/usr/bin/env python3
"""
Script ottimizzato per creare il nuovo dataset TEST 5 con le 20.000 immagini più corte.
Versione ottimizzata per memoria con processing in batch.

Requisiti:
- Selezionare le 20.000 immagini SVG più corte (sotto i 1000 token)
- Split: 90% train (18.000), 10% test (2.000)
- Context window target: 1500-2000 token
- <PERSON><PERSON><PERSON> in data/new_dataset/

Autore: Progetto SVG Captioning
Data: Dicembre 2024
"""

import json
import os
import argparse
import logging
from typing import List, Dict, Any, Tuple
from transformers import AutoTokenizer
import numpy as np
from tqdm import tqdm
import gc

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_dataset_sample(file_path: str, sample_size: int = 100000) -> List[Dict[str, Any]]:
    """Carica un campione del dataset per test rapidi."""
    logger.info(f"Caricamento campione dataset da: {file_path} (primi {sample_size} esempi)")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Prendi solo un campione per test
    if len(data) > sample_size:
        data = data[:sample_size]
        logger.info(f"Campione ridotto a {len(data)} esempi per test")
    
    logger.info(f"Caricati {len(data)} esempi")
    return data

def analyze_token_lengths_batch(data: List[Dict[str, Any]], tokenizer_name: str, batch_size: int = 1000) -> List[Tuple[int, int]]:
    """
    Analizza la lunghezza in token di ogni SVG in batch per ottimizzare memoria.
    
    Returns:
        Lista di tuple (token_length, index) ordinata per lunghezza crescente
    """
    logger.info(f"Analisi lunghezze token con tokenizer: {tokenizer_name}")
    logger.info(f"Batch size: {batch_size}")
    
    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
    
    # Lista per memorizzare solo lunghezze e indici
    token_lengths_with_indices = []
    
    # Processa in batch
    for start_idx in tqdm(range(0, len(data), batch_size), desc="Batch processing"):
        end_idx = min(start_idx + batch_size, len(data))
        batch = data[start_idx:end_idx]
        
        # Processa il batch
        for i, example in enumerate(batch):
            actual_idx = start_idx + i
            svg_xml = example.get('xml', '')
            
            if not svg_xml:
                logger.warning(f"SVG vuoto per esempio {actual_idx}")
                continue
            
            # Tokenizza l'SVG
            tokens = tokenizer.encode(svg_xml)
            token_length = len(tokens)
            
            # Memorizza solo lunghezza e indice
            token_lengths_with_indices.append((token_length, actual_idx))
        
        # Libera memoria
        del batch
        gc.collect()
        
        # Log progresso
        if (end_idx) % 10000 == 0:
            logger.info(f"Processati {end_idx} esempi")
    
    # Ordina per lunghezza crescente
    token_lengths_with_indices.sort(key=lambda x: x[0])
    
    logger.info(f"Analisi completata. {len(token_lengths_with_indices)} esempi ordinati per lunghezza.")
    return token_lengths_with_indices

def select_shortest_examples(data: List[Dict[str, Any]], 
                           token_lengths_with_indices: List[Tuple[int, int]], 
                           max_tokens: int = 1000, 
                           target_count: int = 20000) -> List[Dict[str, Any]]:
    """
    Seleziona gli esempi più corti basandosi sugli indici.
    
    Args:
        data: Dataset originale
        token_lengths_with_indices: Lista di tuple (token_length, index)
        max_tokens: Soglia massima di token
        target_count: Numero target di esempi da selezionare
        
    Returns:
        Lista degli esempi selezionati
    """
    logger.info(f"Selezione esempi con <= {max_tokens} token, target: {target_count}")
    
    # Filtra esempi sotto la soglia
    short_indices = [(token_length, idx) for token_length, idx in token_lengths_with_indices 
                    if token_length <= max_tokens]
    
    logger.info(f"Trovati {len(short_indices)} esempi sotto {max_tokens} token")
    
    # Se abbiamo più esempi del target, prendi i primi (più corti)
    if len(short_indices) > target_count:
        selected_indices = short_indices[:target_count]
        logger.info(f"Selezionati i primi {target_count} esempi più corti")
    else:
        selected_indices = short_indices
        logger.warning(f"Solo {len(short_indices)} esempi disponibili, meno del target {target_count}")
    
    # Estrai gli esempi dal dataset originale
    selected_examples = []
    for token_length, idx in selected_indices:
        selected_examples.append(data[idx])
    
    # Statistiche sui token
    token_lengths = [token_length for token_length, _ in selected_indices]
    
    logger.info(f"Statistiche token degli esempi selezionati:")
    logger.info(f"  Min: {min(token_lengths)} token")
    logger.info(f"  Max: {max(token_lengths)} token")
    logger.info(f"  Media: {np.mean(token_lengths):.1f} token")
    logger.info(f"  Mediana: {np.median(token_lengths):.1f} token")
    
    return selected_examples

def create_train_test_split(examples: List[Dict[str, Any]], 
                           train_ratio: float = 0.9) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Crea split train/test.
    
    Args:
        examples: Lista degli esempi
        train_ratio: Percentuale per il training set
        
    Returns:
        Tuple (train_examples, test_examples)
    """
    logger.info(f"Creazione split train/test con ratio {train_ratio:.1%}")
    
    # Calcola dimensioni
    total_count = len(examples)
    train_count = int(total_count * train_ratio)
    test_count = total_count - train_count
    
    # Split
    train_examples = examples[:train_count]
    test_examples = examples[train_count:]
    
    logger.info(f"Split creato:")
    logger.info(f"  Train: {len(train_examples)} esempi ({len(train_examples)/total_count:.1%})")
    logger.info(f"  Test: {len(test_examples)} esempi ({len(test_examples)/total_count:.1%})")
    
    return train_examples, test_examples

def save_dataset(data: List[Dict[str, Any]], output_path: str) -> None:
    """Salva il dataset in formato JSON."""
    logger.info(f"Salvataggio dataset in: {output_path}")
    
    # Crea directory se non esiste
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Salva
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    # Calcola dimensione file
    file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
    logger.info(f"Dataset salvato: {len(data)} esempi, {file_size:.1f} MB")

def main():
    parser = argparse.ArgumentParser(description="Crea nuovo dataset TEST 5 con le 20.000 immagini più corte (versione ottimizzata)")
    parser.add_argument("--input_file", type=str, 
                       default="/work/tesi_ediluzio/data/processed/filtered_svg_all_sources_FULL.json",
                       help="File dataset di input")
    parser.add_argument("--output_dir", type=str,
                       default="/work/tesi_ediluzio/data/new_dataset",
                       help="Directory di output")
    parser.add_argument("--tokenizer", type=str,
                       default="meta-llama/Llama-3.1-8B-Instruct",
                       help="Tokenizer da usare per l'analisi")
    parser.add_argument("--max_tokens", type=int, default=1000,
                       help="Soglia massima di token per SVG")
    parser.add_argument("--target_count", type=int, default=20000,
                       help="Numero target di esempi da selezionare")
    parser.add_argument("--train_ratio", type=float, default=0.9,
                       help="Percentuale per il training set")
    parser.add_argument("--sample_size", type=int, default=None,
                       help="Dimensione campione per test (None = tutto il dataset)")
    parser.add_argument("--batch_size", type=int, default=1000,
                       help="Dimensione batch per processing")
    
    args = parser.parse_args()
    
    logger.info("🚀 CREAZIONE NUOVO DATASET TEST 5 (OTTIMIZZATO)")
    logger.info("=" * 60)
    logger.info(f"Input: {args.input_file}")
    logger.info(f"Output: {args.output_dir}")
    logger.info(f"Tokenizer: {args.tokenizer}")
    logger.info(f"Max token: {args.max_tokens}")
    logger.info(f"Target count: {args.target_count}")
    logger.info(f"Train ratio: {args.train_ratio:.1%}")
    logger.info(f"Batch size: {args.batch_size}")
    if args.sample_size:
        logger.info(f"Sample size: {args.sample_size}")
    
    # 1. Carica dataset
    if args.sample_size:
        data = load_dataset_sample(args.input_file, args.sample_size)
    else:
        with open(args.input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"Caricati {len(data)} esempi")
    
    # 2. Analizza lunghezze token in batch
    token_lengths_with_indices = analyze_token_lengths_batch(data, args.tokenizer, args.batch_size)
    
    # 3. Seleziona esempi corti
    selected_examples = select_shortest_examples(data, token_lengths_with_indices, args.max_tokens, args.target_count)
    
    # 4. Crea split train/test
    train_examples, test_examples = create_train_test_split(selected_examples, args.train_ratio)
    
    # 5. Salva i dataset
    train_path = os.path.join(args.output_dir, "train_set_test5.json")
    test_path = os.path.join(args.output_dir, "test_set_test5.json")
    
    save_dataset(train_examples, train_path)
    save_dataset(test_examples, test_path)
    
    # 6. Salva statistiche
    token_lengths = [token_length for token_length, _ in token_lengths_with_indices[:len(selected_examples)]]
    stats = {
        "total_original_examples": len(data),
        "selected_examples": len(selected_examples),
        "train_examples": len(train_examples),
        "test_examples": len(test_examples),
        "max_tokens_threshold": args.max_tokens,
        "tokenizer_used": args.tokenizer,
        "train_ratio": args.train_ratio,
        "batch_size": args.batch_size,
        "token_stats": {
            "min": int(min(token_lengths)),
            "max": int(max(token_lengths)),
            "mean": float(np.mean(token_lengths)),
            "median": float(np.median(token_lengths))
        }
    }
    
    stats_path = os.path.join(args.output_dir, "dataset_stats_test5.json")
    with open(stats_path, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2)
    
    logger.info(f"Statistiche salvate in: {stats_path}")
    
    logger.info("✅ CREAZIONE DATASET COMPLETATA!")
    logger.info(f"📁 File creati:")
    logger.info(f"  - Train: {train_path}")
    logger.info(f"  - Test: {test_path}")
    logger.info(f"  - Stats: {stats_path}")

if __name__ == "__main__":
    main()
