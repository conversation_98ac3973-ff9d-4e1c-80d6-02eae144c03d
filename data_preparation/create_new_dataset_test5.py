#!/usr/bin/env python3
"""
Script per creare il nuovo dataset TEST 5 con le 20.000 immagini più corte.

Requisiti:
- Selezionare le 20.000 immagini SVG più corte (sotto i 1000 token)
- Split: 90% train (18.000), 10% test (2.000)
- Context window target: 1500-2000 token
- <PERSON><PERSON><PERSON> in data/new_dataset/

Autore: Progetto SVG Captioning
Data: Dicembre 2024
"""

import json
import os
import argparse
import logging
from typing import List, Dict, Any, Tuple
from transformers import AutoTokenizer
import numpy as np
from tqdm import tqdm

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_dataset(file_path: str) -> List[Dict[str, Any]]:
    """Carica il dataset da file JSON."""
    logger.info(f"Caricamento dataset da: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"Caricati {len(data)} esempi")
    return data

def analyze_token_lengths(data: List[Dict[str, Any]], tokenizer_name: str) -> List[Tuple[int, Dict[str, Any]]]:
    """
    Analizza la lunghezza in token di ogni SVG e restituisce una lista ordinata.
    
    Returns:
        Lista di tuple (token_length, example) ordinata per lunghezza crescente
    """
    logger.info(f"Analisi lunghezze token con tokenizer: {tokenizer_name}")
    
    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
    
    # Analizza ogni esempio
    examples_with_lengths = []
    
    for i, example in enumerate(tqdm(data, desc="Analisi token")):
        svg_xml = example.get('xml', '')
        
        if not svg_xml:
            logger.warning(f"SVG vuoto per esempio {i}")
            continue
        
        # Tokenizza l'SVG
        tokens = tokenizer.encode(svg_xml)
        token_length = len(tokens)
        
        # Aggiungi alla lista
        examples_with_lengths.append((token_length, example))
        
        # Log ogni 10000 esempi
        if (i + 1) % 10000 == 0:
            logger.info(f"Processati {i + 1} esempi")
    
    # Ordina per lunghezza crescente
    examples_with_lengths.sort(key=lambda x: x[0])
    
    logger.info(f"Analisi completata. Esempi ordinati per lunghezza.")
    return examples_with_lengths

def filter_short_examples(examples_with_lengths: List[Tuple[int, Dict[str, Any]]], 
                         max_tokens: int = 1000, 
                         target_count: int = 20000) -> List[Dict[str, Any]]:
    """
    Filtra gli esempi più corti sotto la soglia di token.
    
    Args:
        examples_with_lengths: Lista di tuple (token_length, example)
        max_tokens: Soglia massima di token
        target_count: Numero target di esempi da selezionare
        
    Returns:
        Lista degli esempi selezionati
    """
    logger.info(f"Filtro esempi con <= {max_tokens} token, target: {target_count}")
    
    # Filtra esempi sotto la soglia
    short_examples = [example for token_length, example in examples_with_lengths 
                     if token_length <= max_tokens]
    
    logger.info(f"Trovati {len(short_examples)} esempi sotto {max_tokens} token")
    
    # Se abbiamo più esempi del target, prendi i primi (più corti)
    if len(short_examples) > target_count:
        selected_examples = short_examples[:target_count]
        logger.info(f"Selezionati i primi {target_count} esempi più corti")
    else:
        selected_examples = short_examples
        logger.warning(f"Solo {len(short_examples)} esempi disponibili, meno del target {target_count}")
    
    # Statistiche sui token
    token_lengths = [examples_with_lengths[i][0] for i in range(len(selected_examples))]
    
    logger.info(f"Statistiche token degli esempi selezionati:")
    logger.info(f"  Min: {min(token_lengths)} token")
    logger.info(f"  Max: {max(token_lengths)} token")
    logger.info(f"  Media: {np.mean(token_lengths):.1f} token")
    logger.info(f"  Mediana: {np.median(token_lengths):.1f} token")
    
    return selected_examples

def create_train_test_split(examples: List[Dict[str, Any]], 
                           train_ratio: float = 0.9) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Crea split train/test.
    
    Args:
        examples: Lista degli esempi
        train_ratio: Percentuale per il training set
        
    Returns:
        Tuple (train_examples, test_examples)
    """
    logger.info(f"Creazione split train/test con ratio {train_ratio:.1%}")
    
    # Calcola dimensioni
    total_count = len(examples)
    train_count = int(total_count * train_ratio)
    test_count = total_count - train_count
    
    # Split
    train_examples = examples[:train_count]
    test_examples = examples[train_count:]
    
    logger.info(f"Split creato:")
    logger.info(f"  Train: {len(train_examples)} esempi ({len(train_examples)/total_count:.1%})")
    logger.info(f"  Test: {len(test_examples)} esempi ({len(test_examples)/total_count:.1%})")
    
    return train_examples, test_examples

def save_dataset(data: List[Dict[str, Any]], output_path: str) -> None:
    """Salva il dataset in formato JSON."""
    logger.info(f"Salvataggio dataset in: {output_path}")
    
    # Crea directory se non esiste
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Salva
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    # Calcola dimensione file
    file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
    logger.info(f"Dataset salvato: {len(data)} esempi, {file_size:.1f} MB")

def main():
    parser = argparse.ArgumentParser(description="Crea nuovo dataset TEST 5 con le 20.000 immagini più corte")
    parser.add_argument("--input_file", type=str, 
                       default="/work/tesi_ediluzio/data/processed/filtered_svg_all_sources_FULL.json",
                       help="File dataset di input")
    parser.add_argument("--output_dir", type=str,
                       default="/work/tesi_ediluzio/data/new_dataset",
                       help="Directory di output")
    parser.add_argument("--tokenizer", type=str,
                       default="meta-llama/Llama-3.1-8B-Instruct",
                       help="Tokenizer da usare per l'analisi")
    parser.add_argument("--max_tokens", type=int, default=1000,
                       help="Soglia massima di token per SVG")
    parser.add_argument("--target_count", type=int, default=20000,
                       help="Numero target di esempi da selezionare")
    parser.add_argument("--train_ratio", type=float, default=0.9,
                       help="Percentuale per il training set")
    
    args = parser.parse_args()
    
    logger.info("🚀 CREAZIONE NUOVO DATASET TEST 5")
    logger.info("=" * 50)
    logger.info(f"Input: {args.input_file}")
    logger.info(f"Output: {args.output_dir}")
    logger.info(f"Tokenizer: {args.tokenizer}")
    logger.info(f"Max token: {args.max_tokens}")
    logger.info(f"Target count: {args.target_count}")
    logger.info(f"Train ratio: {args.train_ratio:.1%}")
    
    # 1. Carica dataset
    data = load_dataset(args.input_file)
    
    # 2. Analizza lunghezze token
    examples_with_lengths = analyze_token_lengths(data, args.tokenizer)
    
    # 3. Filtra esempi corti
    selected_examples = filter_short_examples(examples_with_lengths, args.max_tokens, args.target_count)
    
    # 4. Crea split train/test
    train_examples, test_examples = create_train_test_split(selected_examples, args.train_ratio)
    
    # 5. Salva i dataset
    train_path = os.path.join(args.output_dir, "train_set_test5.json")
    test_path = os.path.join(args.output_dir, "test_set_test5.json")
    
    save_dataset(train_examples, train_path)
    save_dataset(test_examples, test_path)
    
    # 6. Salva statistiche
    stats = {
        "total_original_examples": len(data),
        "selected_examples": len(selected_examples),
        "train_examples": len(train_examples),
        "test_examples": len(test_examples),
        "max_tokens_threshold": args.max_tokens,
        "tokenizer_used": args.tokenizer,
        "train_ratio": args.train_ratio,
        "token_stats": {
            "min": int(examples_with_lengths[0][0]),
            "max": int(examples_with_lengths[len(selected_examples)-1][0]),
            "mean": float(np.mean([examples_with_lengths[i][0] for i in range(len(selected_examples))])),
            "median": float(np.median([examples_with_lengths[i][0] for i in range(len(selected_examples))]))
        }
    }
    
    stats_path = os.path.join(args.output_dir, "dataset_stats_test5.json")
    with open(stats_path, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2)
    
    logger.info(f"Statistiche salvate in: {stats_path}")
    
    logger.info("✅ CREAZIONE DATASET COMPLETATA!")
    logger.info(f"📁 File creati:")
    logger.info(f"  - Train: {train_path}")
    logger.info(f"  - Test: {test_path}")
    logger.info(f"  - Stats: {stats_path}")

if __name__ == "__main__":
    main()
