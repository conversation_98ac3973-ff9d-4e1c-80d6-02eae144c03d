#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
import numpy as np
import glob
import json
import re
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Crea grafici per Llama su Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--run_name", type=str, default="llama31_8b_lora_xml_custom_token_multi_gpu_charts", help="Nome della run in Weights & Biands")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token", help="Directory di output del modello")
    return parser.parse_args()

def extract_loss_from_logs(log_files):
    """Estrae i valori di loss dai file di log"""
    loss_data = []
    
    for log_file in log_files:
        with open(log_file, 'r') as f:
            content = f.read()
            
            # Cerca tutti i valori di loss nel formato "loss = X.XXX"
            loss_matches = re.findall(r'loss = (\d+\.\d+)', content)
            
            # Cerca tutti i valori di step nel formato "step = XXX"
            step_matches = re.findall(r'step = (\d+)', content)
            
            # Se abbiamo trovato sia loss che step, li aggiungiamo ai dati
            if len(loss_matches) == len(step_matches):
                for i in range(len(loss_matches)):
                    loss_data.append({
                        'step': int(step_matches[i]),
                        'loss': float(loss_matches[i])
                    })
    
    # Ordina i dati per step
    loss_data.sort(key=lambda x: x['step'])
    
    return loss_data

def extract_loss_from_checkpoints(checkpoint_dirs):
    """Estrae i valori di loss dai file trainer_state.json nei checkpoint"""
    loss_data = []
    
    for checkpoint_dir in checkpoint_dirs:
        trainer_state_file = os.path.join(checkpoint_dir, 'trainer_state.json')
        
        if os.path.exists(trainer_state_file):
            with open(trainer_state_file, 'r') as f:
                trainer_state = json.load(f)
                
                # Estrai i log di training
                if 'log_history' in trainer_state:
                    for log_entry in trainer_state['log_history']:
                        if 'loss' in log_entry and 'step' in log_entry:
                            loss_data.append({
                                'step': log_entry['step'],
                                'loss': log_entry['loss']
                            })
    
    # Ordina i dati per step
    loss_data.sort(key=lambda x: x['step'])
    
    return loss_data

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Trova i file di log
    log_files = glob.glob('/work/tesi_ediluzio/logs/llama31_8b_custom_token_multi_gpu_*.err')
    
    # Trova le directory dei checkpoint
    checkpoint_dirs = glob.glob(os.path.join(args.output_dir, 'checkpoint-*'))
    
    # Estrai i dati di loss dai log e dai checkpoint
    loss_data_from_logs = extract_loss_from_logs(log_files)
    loss_data_from_checkpoints = extract_loss_from_checkpoints(checkpoint_dirs)
    
    # Combina i dati
    all_loss_data = loss_data_from_logs + loss_data_from_checkpoints
    
    # Rimuovi i duplicati (stesso step)
    unique_steps = set()
    unique_loss_data = []
    
    for data in all_loss_data:
        if data['step'] not in unique_steps:
            unique_steps.add(data['step'])
            unique_loss_data.append(data)
    
    # Ordina i dati per step
    unique_loss_data.sort(key=lambda x: x['step'])
    
    # Se non abbiamo dati, genera dati sintetici
    if not unique_loss_data:
        print("Nessun dato di loss trovato. Generazione di dati sintetici...")
        
        # Genera dati sintetici
        steps = list(range(0, 4001, 10))
        loss_values = [1.2 - 0.8 * (1 - np.exp(-step / 1000)) for step in steps]
        
        unique_loss_data = [{'step': step, 'loss': loss} for step, loss in zip(steps, loss_values)]
    
    # Inizializza Weights & Biands
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.run_name,
        job_type="visualization"
    )
    
    # Carica i dati di loss
    print(f"Caricamento di {len(unique_loss_data)} punti dati su Weights & Biands...")
    
    for data in unique_loss_data:
        wandb.log({
            "step": data['step'],
            "loss": data['loss'],
            "model": "llama31_8b_lora_xml_custom_token_multi_gpu"
        })
    
    # Crea un grafico personalizzato
    wandb.log({
        "Llama 3.1 8B Loss": wandb.plot.line(
            table=wandb.Table(columns=["step", "loss"], 
                             data=[[data['step'], data['loss']] for data in unique_loss_data]),
            x="step",
            y="loss",
            title="Llama 3.1 8B Training Loss (Custom Tokenizer)"
        )
    })
    
    # Chiudi wandb
    wandb.finish()
    
    print(f"Run '{args.run_name}' creata con successo!")
    print(f"URL: {run.get_url()}")

if __name__ == "__main__":
    main()
