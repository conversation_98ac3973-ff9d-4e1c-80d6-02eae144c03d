#!/bin/bash
#SBATCH --job-name=svg_comprehensive_examples
#SBATCH --output=logs/svg_comprehensive_examples_%j.out
#SBATCH --error=logs/svg_comprehensive_examples_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --constraint=gpu_L40S_48G|gpu_A40_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G|gpu_2080Ti_11G
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --time=4:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
DATA_FILE="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
NUM_EXAMPLES=5
OUTPUT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/comprehensive_examples"
WANDB_ENTITY="337543-unimore"
WANDB_PROJECT="captioner"
WANDB_RUN_NAME="comprehensive_examples_comparison"

# Stampa informazioni
echo "Generazione di esempi visivi completi di caption SVG"
echo "Dataset: $DATA_FILE"
echo "Numero di esempi: $NUM_EXAMPLES"
echo "Output: $OUTPUT_DIR"
echo "Weights & Biands Entity: $WANDB_ENTITY"
echo "Weights & Biands Project: $WANDB_PROJECT"
echo "Weights & Biands Run Name: $WANDB_RUN_NAME"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocate (da SLURM): $SLURM_GPUS_ON_NODE"

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/generate_comprehensive_examples.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/generate_comprehensive_examples.py \
    --data_file "$DATA_FILE" \
    --num_examples "$NUM_EXAMPLES" \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb \
    --wandb_entity "$WANDB_ENTITY" \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_run_name "$WANDB_RUN_NAME"

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Generazione di esempi visivi completi completata con successo"
else
    echo "Generazione di esempi visivi completi fallita con codice di errore: $?"
fi
