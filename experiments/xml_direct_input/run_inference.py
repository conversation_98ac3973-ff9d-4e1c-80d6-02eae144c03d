# Nome file suggerito: run_zero_shot_inference.py
# Modificato da run_inference_plain.py per:
# - <PERSON><PERSON><PERSON> campo "xml" invece di "xml_data"
# - Salvare output su file JSON Lines (--output_file)
# - Usare un prompt template fisso (modificabile nella funzione)

import os
import json
import argparse
import logging
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from tqdm import tqdm # Per barra di avanzamento
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- PROMPT TEMPLATE ---
# Modifica qui se vuoi cambiare il prompt standard zero-shot
ZERO_SHOT_PROMPT_TEMPLATE = """Genera una didascalia concisa e descrittiva per il seguente codice SVG:

{svg_data}

Didascal<PERSON>:"""
# ---------------------

def format_zero_shot_prompt(tokenizer, svg_data_string: str) -> str:
    """Crea il prompt zero-shot per un modello instruct."""
    # Applica il template definito sopra
    instruction = ZERO_SHOT_PROMPT_TEMPLATE.format(svg_data = svg_data_string)

    # Verifica se usare il chat template del tokenizer o il formato manuale
    has_chat_template = getattr(tokenizer, 'chat_template', None) is not None or \
                        getattr(tokenizer, 'apply_chat_template', None) is not None
    prompt = ""

    if has_chat_template:
        messages = [{"role": "user", "content": instruction}]
        try:
            # add_generation_prompt=True aggiunge il separatore per l'inizio della risposta
            prompt = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            logger.debug("Usato chat template del tokenizer.")
        except Exception as e:
            logger.warning(f"apply_chat_template fallito ({e}), uso template manuale User/Assistant.")
            has_chat_template = False # Fallback

    if not has_chat_template:
         # Template manuale User/Assistant (assicurati corrisponda al modello!)
         # L'Assistant: alla fine è importante per guidare la generazione
         prompt = f"User: {instruction}\nAssistant:"
         logger.debug("Usato template manuale User/Assistant.")

    logger.debug(f"Prompt generato (inizio):\n{prompt[:500]}...")
    return prompt

def main():
    parser = argparse.ArgumentParser(description="Run Zero-Shot Inference for SVG Captioning.")
    # Argomenti esistenti
    parser.add_argument("--model_name_or_path", type=str, required=True, help="HF ID or path to the pre-trained Instruct model.")
    parser.add_argument("--tokenizer_name", type=str, default=None, help="Optional: HF ID or path to the tokenizer (defaults to model name).")
    parser.add_argument("--data_file", type=str, required=True, help="Path to the JSON data file (e.g., test_set_final_2k.json).")
    parser.add_argument("--num_samples", type=int, default=-1, help="Number of samples to process. -1 processes all samples. (default: -1)")
    parser.add_argument("--max_new_tokens", type=int, default=100, help="Max new tokens to generate for the caption.")
    parser.add_argument("--load_in_8bit", action='store_true', help="Load model in 8-bit.")
    parser.add_argument("--load_in_4bit", action='store_true', help="Load model in 4-bit.")
    parser.add_argument("--use_bf16", action='store_true', help="Use bfloat16 dtype for 4-bit compute (if loading in 4-bit and supported).")
    # Parametri generazione
    parser.add_argument("--temperature", type=float, default=0.6, help="Sampling temperature.")
    parser.add_argument("--top_p", type=float, default=0.9, help="Nucleus sampling top-p.")
    parser.add_argument("--do_sample", action='store_true', help="Enable sampling (recommended). Set False for greedy.")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device ('cuda' or 'cpu').")
    # Nuovo argomento per file output
    parser.add_argument("--output_file", type=str, required=True, help="Path to save the generation results (JSON Lines file).")

    args = parser.parse_args()

    # Gestione conflitti argomenti (4bit vs 8bit)
    if args.load_in_4bit and args.load_in_8bit:
        logger.warning("Specificati sia --load_in_4bit che --load_in_8bit. Priorità a 4-bit.")
        args.load_in_8bit = False

    tokenizer_load_name = args.tokenizer_name if args.tokenizer_name else args.model_name_or_path
    logger.info(f"Caricamento tokenizer: {tokenizer_load_name}")
    try:
        # trust_remote_code=True potrebbe essere necessario per alcuni modelli
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_load_name, trust_remote_code=True)
        # Gestione pad token (necessario per batch generation se si usasse, ma buona pratica)
        if tokenizer.pad_token is None:
            if tokenizer.eos_token is not None:
                logger.info(f"Imposto pad_token a eos_token: {tokenizer.eos_token}")
                tokenizer.pad_token = tokenizer.eos_token
            else:
                 fallback_pad = '<|pad|>'
                 logger.warning(f"Tokenizer manca pad_token e eos_token. Aggiungo pad_token custom: {fallback_pad}")
                 tokenizer.add_special_tokens({'pad_token': fallback_pad})
        # Assicura pad_token_id sia settato
        if hasattr(tokenizer, 'pad_token_id') and tokenizer.pad_token_id is None and tokenizer.pad_token is not None:
             tokenizer.pad_token_id = tokenizer.convert_tokens_to_ids(tokenizer.pad_token)
        logger.info(f"Tokenizer Pad Token: {tokenizer.pad_token} (ID: {tokenizer.pad_token_id})")

    except Exception as e:
        logger.error(f"Errore caricamento tokenizer: {e}", exc_info=True)
        return

    logger.info(f"Caricamento modello: {args.model_name_or_path}")
    quantization_config = None
    model_dtype = torch.float16 # Default dtype se non quantizzato o bf16 non richiesto/supportato

    if args.load_in_4bit:
        logger.info("Caricamento modello in 4-bit.")
        compute_dtype = torch.float16
        if args.use_bf16 and torch.cuda.is_available() and torch.cuda.is_bf16_supported():
            compute_dtype = torch.bfloat16
            logger.info("Uso bfloat16 compute dtype per 4-bit.")
        else:
            logger.warning("bf16 non richiesto o non supportato, uso float16 compute dtype per 4-bit.")

        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_use_double_quant=True,
            bnb_4bit_compute_dtype=compute_dtype
        )
    elif args.load_in_8bit:
        logger.info("Caricamento modello in 8-bit.")
        quantization_config = BitsAndBytesConfig(load_in_8bit=True)
    elif args.use_bf16 and torch.cuda.is_available() and torch.cuda.is_bf16_supported():
         logger.info("Caricamento modello in bfloat16.")
         model_dtype = torch.bfloat16 # Imposta bfloat16 se richiesto e supportato (e non quantizzato)

    try:
        model = AutoModelForCausalLM.from_pretrained(
            args.model_name_or_path,
            trust_remote_code=True,
            quantization_config=quantization_config, # Passa la config o None
            torch_dtype=model_dtype if not quantization_config else None, # Imposta dtype solo se non quantizzato
            device_map="auto" # Lascia che accelerate gestisca il posizionamento
        )
        model.eval() # Modalità valutazione
        logger.info(f"Modello caricato su device: {model.device}") # Mostra device principale

    except Exception as e:
        logger.error(f"Errore caricamento modello: {e}", exc_info=True)
        return

    logger.info(f"Caricamento dati da: {args.data_file}")
    try:
        with open(args.data_file, 'r', encoding='utf-8') as f:
            all_data = json.load(f)

        if args.num_samples > 0 and args.num_samples < len(all_data):
            logger.info(f"Processando i primi {args.num_samples} campioni.")
            samples_to_process = all_data[:args.num_samples]
        else:
            logger.info(f"Processando tutti i {len(all_data)} campioni.")
            samples_to_process = all_data

    except Exception as e:
        logger.error(f"Errore caricamento o selezione dati: {e}", exc_info=True)
        return

    logger.info("Avvio inferenza zero-shot...")
    # Configurazione generazione
    generation_config = {
        "max_new_tokens": args.max_new_tokens,
        "pad_token_id": tokenizer.pad_token_id if tokenizer.pad_token_id is not None else tokenizer.eos_token_id,
        "eos_token_id": tokenizer.eos_token_id,
    }
    if args.do_sample:
         generation_config["temperature"] = args.temperature
         generation_config["top_p"] = args.top_p
         generation_config["do_sample"] = True
         logger.info(f"Generazione con sampling: temp={args.temperature}, top_p={args.top_p}")
    else:
         generation_config["do_sample"] = False
         logger.info("Generazione con greedy decoding.")

    # Apri file di output in modalità scrittura ('w')
    # Salva come JSON Lines: un oggetto JSON per riga
    try:
        os.makedirs(os.path.dirname(args.output_file) or '.', exist_ok=True)
        with open(args.output_file, 'w', encoding='utf-8') as f_out:
            start_time = time.time()
            for i, item in enumerate(tqdm(samples_to_process, desc="Generating Captions")):
                # --- MODIFICA CHIAVE: Legge campo "xml" ---
                svg_data_string = item.get("xml", "")
                # -----------------------------------------
                true_caption = item.get("caption", "[N/A]") # Caption reale per confronto
                item_id = item.get("id", f"sample_{i}") # ID del campione

                if not isinstance(svg_data_string, str) or not svg_data_string.strip():
                    logger.warning(f"Skipping item {item_id} due to invalid/empty svg_data_string.")
                    continue

                # Formatta il prompt usando la funzione definita sopra
                prompt = format_zero_shot_prompt(tokenizer, svg_data_string)

                # Tokenizza l'input
                # Non serve spostare su device manualmente se usi device_map='auto'
                # e il modello è già distribuito
                try:
                    inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048) # Aumentato max_length per input+prompt
                    inputs = inputs.to(model.device) # Sposta comunque sul device principale del modello
                except Exception as e:
                     logger.error(f"Errore tokenizzazione item {item_id}: {e}")
                     continue

                generated_part = "[GENERATION ERROR]" # Default in caso di errore
                try:
                    with torch.no_grad(): # Disabilita calcolo gradienti per inferenza
                         outputs = model.generate(**inputs, **generation_config)

                    # Decodifica solo i token generati (quelli dopo l'input)
                    output_ids = outputs[0][inputs['input_ids'].shape[1]:]
                    generated_part = tokenizer.decode(output_ids, skip_special_tokens=True).strip()

                except Exception as e:
                     logger.error(f"Errore durante generazione per item {item_id}: {e}", exc_info=False) # Meno verboso nel log per errori ripetuti

                # Crea il dizionario dei risultati
                result = {
                    "id": item_id,
                    "true_caption": true_caption,
                    "generated_caption": generated_part,
                    # "prompt": prompt # Decommenta se vuoi salvare anche il prompt
                }

                # Scrivi la riga JSON nel file di output
                f_out.write(json.dumps(result, ensure_ascii=False) + '\n')

            end_time = time.time()
            logger.info(f"Inferenza completata in {end_time - start_time:.2f} secondi.")
            logger.info(f"Risultati salvati in: {args.output_file}")

    except IOError as e:
         logger.error(f"Errore scrittura file di output {args.output_file}: {e}", exc_info=True)
    except Exception as e:
         logger.error(f"Errore imprevisto durante il loop di inferenza: {e}", exc_info=True)

    logger.info("Script inferenza zero-shot terminato.")

if __name__ == "__main__":
    main()