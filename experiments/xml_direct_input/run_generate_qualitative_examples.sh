#!/bin/bash

# Script per generare esempi qualitativi dai modelli addestrati

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Parametri
MODEL_NAME=${1:-"meta-llama/Llama-3.1-8B-Instruct"}
CHECKPOINT_PATH=${2:-"/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/checkpoint-best"}
TEST_FILE=${3:-"/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"}
OUTPUT_DIR=${4:-"/work/tesi_ediluzio/evaluation/examples"}
USE_CUSTOM_TOKENIZER=${5:-""}
LOAD_IN_8BIT=${6:-""}
LOAD_IN_4BIT=${7:-"--load_in_4bit"}
NUM_EXAMPLES=${8:-5}
MAX_LENGTH=${9:-512}

# Crea la directory di output
mkdir -p "$OUTPUT_DIR"

# Estrai il nome del modello per il nome del file di output
MODEL_SHORT_NAME=$(basename "$MODEL_NAME")
CHECKPOINT_SHORT_NAME=$(basename "$CHECKPOINT_PATH")
OUTPUT_FILE="$OUTPUT_DIR/${MODEL_SHORT_NAME}_${CHECKPOINT_SHORT_NAME}_examples.json"

# Stampa informazioni
echo "Generazione di Esempi Qualitativi"
echo "Modello: $MODEL_NAME"
echo "Checkpoint: $CHECKPOINT_PATH"
echo "File di test: $TEST_FILE"
echo "File di output: $OUTPUT_FILE"
echo "Tokenizer personalizzato: $USE_CUSTOM_TOKENIZER"
echo "Caricamento in 8-bit: $LOAD_IN_8BIT"
echo "Caricamento in 4-bit: $LOAD_IN_4BIT"
echo "Numero di esempi per fascia: $NUM_EXAMPLES"
echo "Lunghezza massima: $MAX_LENGTH"

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/generate_qualitative_examples.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/generate_qualitative_examples.py \
    --model_name_or_path "$MODEL_NAME" \
    --checkpoint_path "$CHECKPOINT_PATH" \
    --test_file "$TEST_FILE" \
    --output_file "$OUTPUT_FILE" \
    $USE_CUSTOM_TOKENIZER \
    $LOAD_IN_8BIT \
    $LOAD_IN_4BIT \
    --num_examples_per_complexity "$NUM_EXAMPLES" \
    --max_length "$MAX_LENGTH"

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Generazione di esempi qualitativi completata con successo"
    echo "Esempi salvati in: $OUTPUT_FILE"
else
    echo "Generazione di esempi qualitativi fallita con codice di errore: $?"
fi
