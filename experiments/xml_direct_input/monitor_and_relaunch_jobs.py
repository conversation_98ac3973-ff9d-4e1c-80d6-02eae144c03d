#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import argparse
import subprocess
import logging
from datetime import datetime, timedelta

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("/work/tesi_ediluzio/logs/job_monitor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Monitora e rilancia i job SLURM")
    parser.add_argument("--check_interval", type=int, default=3600, help="Intervallo di controllo in secondi (default: 1 ora)")
    parser.add_argument("--max_relaunches", type=int, default=10, help="Numero massimo di rilanci per job")
    return parser.parse_args()

def get_running_jobs():
    """Ottiene i job in esecuzione per l'utente corrente"""
    try:
        result = subprocess.run(["squeue", "-u", os.environ.get("USER"), "-h", "-o", "%i %j %T %M %N"], 
                               capture_output=True, text=True, check=True)
        jobs = []
        for line in result.stdout.strip().split("\n"):
            if line.strip():
                parts = line.strip().split()
                if len(parts) >= 3:
                    job_id = parts[0]
                    job_name = parts[1]
                    job_state = parts[2]
                    jobs.append((job_id, job_name, job_state))
        return jobs
    except subprocess.CalledProcessError as e:
        logger.error(f"Errore nell'ottenere i job in esecuzione: {e}")
        return []

def get_completed_jobs(since_hours=24):
    """Ottiene i job completati nelle ultime 'since_hours' ore"""
    since_time = datetime.now() - timedelta(hours=since_hours)
    since_str = since_time.strftime("%Y-%m-%d")
    try:
        result = subprocess.run(["sacct", "-u", os.environ.get("USER"), "-S", since_str, 
                                "-o", "JobID,JobName,State,Elapsed,NodeList", "-n"], 
                               capture_output=True, text=True, check=True)
        jobs = []
        for line in result.stdout.strip().split("\n"):
            if line.strip():
                parts = line.strip().split()
                if len(parts) >= 3 and not parts[0].endswith(("batch", "extern")):
                    job_id = parts[0]
                    job_name = parts[1]
                    job_state = parts[2]
                    jobs.append((job_id, job_name, job_state))
        return jobs
    except subprocess.CalledProcessError as e:
        logger.error(f"Errore nell'ottenere i job completati: {e}")
        return []

def submit_job(slurm_script):
    """Invia un job SLURM"""
    try:
        result = subprocess.run(["sbatch", slurm_script], 
                               capture_output=True, text=True, check=True)
        job_id = result.stdout.strip().split()[-1]
        logger.info(f"Job inviato con ID: {job_id} usando lo script {slurm_script}")
        return job_id
    except subprocess.CalledProcessError as e:
        logger.error(f"Errore nell'invio del job {slurm_script}: {e}")
        return None

def get_latest_checkpoint(model_name):
    """Ottiene l'ultimo checkpoint per un modello"""
    output_dir = f"/work/tesi_ediluzio/experiments/xml_direct_input/outputs/{model_name}_lora_xml_custom_token"
    try:
        result = subprocess.run(["find", output_dir, "-name", "checkpoint-*", "-type", "d"], 
                               capture_output=True, text=True, check=True)
        checkpoints = result.stdout.strip().split("\n")
        if not checkpoints or checkpoints[0] == '':
            logger.warning(f"Nessun checkpoint trovato per {model_name}")
            return None
        
        # Estrai il numero di step da ogni checkpoint
        checkpoint_steps = []
        for checkpoint in checkpoints:
            if checkpoint:
                step = int(checkpoint.split("-")[-1])
                checkpoint_steps.append((step, checkpoint))
        
        if not checkpoint_steps:
            logger.warning(f"Nessun checkpoint valido trovato per {model_name}")
            return None
        
        # Ordina per numero di step (dal più recente al più vecchio)
        checkpoint_steps.sort(reverse=True)
        latest_checkpoint = checkpoint_steps[0][1]
        logger.info(f"Ultimo checkpoint per {model_name}: {latest_checkpoint}")
        return latest_checkpoint
    except subprocess.CalledProcessError as e:
        logger.error(f"Errore nell'ottenere l'ultimo checkpoint per {model_name}: {e}")
        return None

def main():
    args = parse_args()
    
    # Definisci i job da monitorare
    jobs_to_monitor = [
        {
            "name": "llama31_8b_custom_token_multi_gpu",
            "script": "/work/tesi_ediluzio/experiments/xml_direct_input/run_llama31_8b_custom_tokenizer_multi_gpu.slurm",
            "model": "llama31_8b",
            "relaunch_count": 0
        },
        {
            "name": "gemma2_9b_it_custom_token_multi_gpu",
            "script": "/work/tesi_ediluzio/experiments/xml_direct_input/run_gemma2_9b_it_custom_tokenizer_multi_gpu.slurm",
            "model": "gemma2_9b_it",
            "relaunch_count": 0
        }
    ]
    
    logger.info("Avvio del monitoraggio dei job")
    
    while True:
        # Ottieni i job in esecuzione
        running_jobs = get_running_jobs()
        running_job_names = [job[1] for job in running_jobs]
        
        # Ottieni i job completati nelle ultime 24 ore
        completed_jobs = get_completed_jobs()
        
        for job in jobs_to_monitor:
            # Controlla se il job è in esecuzione
            if job["name"] in running_job_names:
                logger.info(f"Job {job['name']} è in esecuzione")
                continue
            
            # Controlla se il job ha raggiunto il limite di rilanci
            if job["relaunch_count"] >= args.max_relaunches:
                logger.warning(f"Job {job['name']} ha raggiunto il limite di rilanci ({args.max_relaunches})")
                continue
            
            # Controlla se il job è stato completato recentemente
            job_completed = False
            for completed_job in completed_jobs:
                if job["name"] in completed_job[1] and completed_job[2] in ["COMPLETED", "FAILED", "CANCELLED"]:
                    job_completed = True
                    logger.info(f"Job {job['name']} è stato completato recentemente con stato {completed_job[2]}")
                    break
            
            # Se il job non è in esecuzione e non è stato completato recentemente, rilancia
            if not job_completed:
                logger.info(f"Rilancio del job {job['name']}")
                
                # Ottieni l'ultimo checkpoint
                latest_checkpoint = get_latest_checkpoint(job["model"])
                
                # Rilancia il job
                new_job_id = submit_job(job["script"])
                if new_job_id:
                    job["relaunch_count"] += 1
                    logger.info(f"Job {job['name']} rilanciato con ID {new_job_id} (rilancio #{job['relaunch_count']})")
        
        # Attendi prima del prossimo controllo
        logger.info(f"Prossimo controllo tra {args.check_interval} secondi")
        time.sleep(args.check_interval)

if __name__ == "__main__":
    main()
