<!DOCTYPE html>
<html>
<head>
    <title>SVG Captioning Models Comparison</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .models-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .model-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s;
        }
        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .model-card h2 {
            margin-top: 0;
            color: #2c5282;
        }
        .model-card p {
            color: #4a5568;
        }
        .model-card a {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #4299e1;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
        }
        .model-card a:hover {
            background-color: #3182ce;
        }
    </style>
</head>
<body>
    <h1>SVG Captioning Models Comparison</h1>
    
    <div class="models-list">
        <div class="model-card">
            <h2>Llama 3.1 8B (Zero-shot)</h2>
            <p>This model uses Llama 3.1 8B in zero-shot mode to generate captions for SVG images without any fine-tuning.</p>
            <a href="model_examples_llama_zero_shot.html">View Examples</a>
        </div>
        
        <div class="model-card">
            <h2>Gemma 2 9B IT (Zero-shot)</h2>
            <p>This model uses Gemma 2 9B IT in zero-shot mode to generate captions for SVG images without any fine-tuning.</p>
            <a href="model_examples_gemma_zero_shot.html">View Examples</a>
        </div>
        
        <div class="model-card">
            <h2>Llama 3.1 8B (Fine-tuned)</h2>
            <p>This model is a fine-tuned version of Llama 3.1 8B that has been trained specifically on SVG captioning without a custom tokenizer.</p>
            <a href="model_examples_llama_finetuned.html">View Examples</a>
        </div>
        
        <div class="model-card">
            <h2>Gemma 2 9B IT (Fine-tuned)</h2>
            <p>This model is a fine-tuned version of Gemma 2 9B IT that has been trained specifically on SVG captioning without a custom tokenizer.</p>
            <a href="model_examples_gemma_finetuned.html">View Examples</a>
        </div>
    </div>
</body>
</html>
