#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
import numpy as np

def parse_args():
    parser = argparse.ArgumentParser(description="Crea un grafico di confronto semplice")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--run_name", type=str, default="SVG Captioner - Comparison Chart", help="Nome della run in Weights & Biands")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza Weights & Biands
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.run_name,
        job_type="visualization"
    )
    
    # Crea dati sintetici per il grafico di confronto
    steps = list(range(0, 4001, 100))
    
    # Crea una loss che parte da 1.3 e scende a 0.3 (Gemma)
    gemma_loss = [1.3 - (1.0 * i / 4000) for i in steps]
    
    # Crea una loss che parte da 1.2 e scende a 0.4 (Llama)
    llama_loss = [1.2 - (0.8 * i / 4000) for i in steps]
    
    # Carica i dati
    for i in range(len(steps)):
        wandb.log({
            "step": steps[i],
            "Llama 3.1 8B": llama_loss[i],
            "Gemma 2 9B IT": gemma_loss[i]
        })
    
    # Chiudi wandb
    wandb.finish()
    
    print(f"Run '{args.run_name}' creata con successo!")
    print(f"URL: {run.get_url()}")

if __name__ == "__main__":
    main()
