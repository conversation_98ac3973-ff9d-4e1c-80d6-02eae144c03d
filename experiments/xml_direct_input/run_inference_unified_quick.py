#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Versione modificata di run_inference_unified.py per supportare il parametro num_samples.
"""

import os
import json
import argparse
import logging
from typing import Dict, List, Any, Optional, Union

import torch
from tqdm import tqdm
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
    GenerationConfig
)
from peft import PeftModel, PeftConfig

import sys
sys.path.append("/work/tesi_ediluzio")  # Aggiungi il percorso del progetto a sys.path
from shared.svg_core.custom_tokenizer_utils import build_tokenizer, tokenize_svg

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Template per il prompt zero-shot
ZERO_SHOT_PROMPT_TEMPLATE = """Genera una didascalia concisa e descrittiva per il seguente codice SVG:

{svg_data}

Didascalia:"""

def load_data(file_path: str) -> List[Dict[str, Any]]:
    """Carica un file JSON o JSONL."""
    if file_path.endswith('.jsonl'):
        # Carica JSONL
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data.append(json.loads(line))
        return data
    else:
        # Carica JSON
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

def save_jsonl(data: List[Dict[str, Any]], file_path: str) -> None:
    """Salva i dati in formato JSONL."""
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def load_model_and_tokenizer(
    model_name_or_path: str,
    lora_path: Optional[str] = None,
    use_custom_tokenizer: bool = False,
    load_in_8bit: bool = False,
    load_in_4bit: bool = False,
    device_map: str = "auto"
) -> tuple:
    """
    Carica il modello e il tokenizer.
    
    Args:
        model_name_or_path: Nome o percorso del modello base
        lora_path: Percorso degli adattatori LoRA (opzionale)
        use_custom_tokenizer: Se True, utilizza il tokenizer custom per SVG
        load_in_8bit: Se True, carica il modello in 8-bit
        load_in_4bit: Se True, carica il modello in 4-bit
        device_map: Mappa dei dispositivi per il modello
        
    Returns:
        Tuple (model, tokenizer)
    """
    logger.info(f"Caricamento del modello: {model_name_or_path}")
    
    # Configurazione per la quantizzazione
    quantization_config = None
    if load_in_8bit or load_in_4bit:
        quantization_config = BitsAndBytesConfig(
            load_in_8bit=load_in_8bit,
            load_in_4bit=load_in_4bit,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4"
        )
    
    # Carica il modello base
    model = AutoModelForCausalLM.from_pretrained(
        model_name_or_path,
        quantization_config=quantization_config,
        device_map=device_map,
        trust_remote_code=True
    )
    
    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        model_name_or_path,
        trust_remote_code=True
    )
    
    # Aggiungi i token SVG se richiesto
    if use_custom_tokenizer:
        logger.info("Utilizzo del tokenizer custom per SVG")
        tokenizer = build_tokenizer(tokenizer)
        model.resize_token_embeddings(len(tokenizer))
    
    # Carica gli adattatori LoRA se specificati
    if lora_path:
        logger.info(f"Caricamento degli adattatori LoRA: {lora_path}")
        model = PeftModel.from_pretrained(model, lora_path)
    
    return model, tokenizer

def generate_caption(
    model: Any,
    tokenizer: Any,
    svg_data: str,
    use_custom_tokenizer: bool = False,
    max_new_tokens: int = 100,
    temperature: float = 0.7,
    top_p: float = 0.9,
    top_k: int = 50,
    repetition_penalty: float = 1.1
) -> str:
    """
    Genera una didascalia per un SVG.
    
    Args:
        model: Modello di linguaggio
        tokenizer: Tokenizer
        svg_data: Dati SVG
        use_custom_tokenizer: Se True, utilizza il tokenizer custom per SVG
        max_new_tokens: Numero massimo di token da generare
        temperature: Temperatura per la generazione
        top_p: Parametro top-p per la generazione
        top_k: Parametro top-k per la generazione
        repetition_penalty: Penalità per la ripetizione
        
    Returns:
        Didascalia generata
    """
    # Prepara il prompt
    if use_custom_tokenizer:
        # Tokenizza l'SVG con il tokenizer custom
        svg_data_tokenized = tokenize_svg(svg_data)
        prompt = ZERO_SHOT_PROMPT_TEMPLATE.format(svg_data=svg_data_tokenized)
    else:
        # Usa l'SVG raw
        prompt = ZERO_SHOT_PROMPT_TEMPLATE.format(svg_data=svg_data)
    
    # Tokenizza il prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
    
    # Configura la generazione
    generation_config = GenerationConfig(
        max_new_tokens=max_new_tokens,
        temperature=temperature,
        top_p=top_p,
        top_k=top_k,
        repetition_penalty=repetition_penalty,
        pad_token_id=tokenizer.eos_token_id
    )
    
    # Genera la didascalia
    with torch.no_grad():
        output = model.generate(
            **inputs,
            generation_config=generation_config
        )
    
    # Decodifica l'output
    generated_text = tokenizer.decode(output[0], skip_special_tokens=True)
    
    # Estrai la didascalia dal testo generato
    caption = generated_text.split("Didascalia:")[-1].strip()
    
    return caption

def run_inference(args: argparse.Namespace) -> None:
    """
    Esegue l'inferenza su un dataset di test.
    
    Args:
        args: Argomenti della linea di comando
    """
    # Carica il dataset di test
    logger.info(f"Caricamento del dataset di test: {args.test_file}")
    test_data = load_data(args.test_file)
    logger.info(f"Caricati {len(test_data)} esempi di test.")
    
    # Limita il numero di campioni se specificato
    if args.num_samples > 0 and args.num_samples < len(test_data):
        logger.info(f"Limitando a {args.num_samples} campioni.")
        test_data = test_data[:args.num_samples]
    
    # Carica il modello e il tokenizer
    model, tokenizer = load_model_and_tokenizer(
        args.model_name_or_path,
        args.lora_path,
        args.use_custom_tokenizer,
        args.load_in_8bit,
        args.load_in_4bit
    )
    
    # Esegui l'inferenza
    results = []
    for i, example in enumerate(tqdm(test_data, desc="Inferenza")):
        svg_data = example.get("xml", "")
        reference = example.get("caption", "")
        
        # Genera la didascalia
        generated = generate_caption(
            model,
            tokenizer,
            svg_data,
            args.use_custom_tokenizer,
            args.max_new_tokens,
            args.temperature,
            args.top_p,
            args.top_k,
            args.repetition_penalty
        )
        
        # Salva il risultato
        results.append({
            "id": example.get("id", i),
            "true_caption": reference,
            "generated_caption": generated
        })
        
        # Salva i risultati parziali ogni 100 esempi
        if (i + 1) % 100 == 0:
            logger.info(f"Completati {i + 1}/{len(test_data)} esempi.")
            save_jsonl(results, args.output_file)
    
    # Salva i risultati finali
    save_jsonl(results, args.output_file)
    logger.info(f"Risultati salvati in: {args.output_file}")

def main():
    parser = argparse.ArgumentParser(description="Inferenza unificata per SVG captioning.")
    
    # Parametri del modello
    parser.add_argument("--model_name_or_path", type=str, required=True, help="Nome o percorso del modello base.")
    parser.add_argument("--lora_path", type=str, default=None, help="Percorso degli adattatori LoRA (opzionale).")
    parser.add_argument("--use_custom_tokenizer", action="store_true", help="Utilizza il tokenizer custom per SVG.")
    parser.add_argument("--load_in_8bit", action="store_true", help="Carica il modello in 8-bit.")
    parser.add_argument("--load_in_4bit", action="store_true", help="Carica il modello in 4-bit.")
    
    # Parametri del dataset
    parser.add_argument("--test_file", type=str, default="/work/tesi_ediluzio/data/processed/test_set_final_2k.json", help="Path al file di test.")
    parser.add_argument("--output_file", type=str, required=True, help="Path al file di output.")
    parser.add_argument("--num_samples", type=int, default=-1, help="Numero di campioni da processare. -1 per processare tutti i campioni.")
    
    # Parametri di generazione
    parser.add_argument("--max_new_tokens", type=int, default=100, help="Numero massimo di token da generare.")
    parser.add_argument("--temperature", type=float, default=0.7, help="Temperatura per la generazione.")
    parser.add_argument("--top_p", type=float, default=0.9, help="Parametro top-p per la generazione.")
    parser.add_argument("--top_k", type=int, default=50, help="Parametro top-k per la generazione.")
    parser.add_argument("--repetition_penalty", type=float, default=1.1, help="Penalità per la ripetizione.")
    
    args = parser.parse_args()
    run_inference(args)

if __name__ == "__main__":
    main()
