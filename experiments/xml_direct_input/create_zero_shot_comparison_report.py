#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import random
from typing import List, Dict, Any, Optional

import torch
import wandb
from transformers import AutoModelForCausalLM, AutoTokenizer

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

def parse_args():
    parser = argparse.ArgumentParser(description="Crea un report di confronto tra inferenza zero-shot e fine-tuned")
    parser.add_argument("--test_data", type=str, required=True, help="Path al file di test in formato JSON")
    parser.add_argument("--zero_shot_model", type=str, required=True, help="Nome o path del modello zero-shot")
    parser.add_argument("--fine_tuned_model", type=str, required=True, help="Path al modello fine-tuned")
    parser.add_argument("--output_file", type=str, default="zero_shot_comparison.json", help="Path al file di output")
    parser.add_argument("--num_examples", type=int, default=10, help="Numero di esempi da generare")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default="zero_shot_comparison", help="Nome della run in Weights & Biands")
    parser.add_argument("--use_wandb", action="store_true", help="Usa Weights & Biands per il tracking")
    return parser.parse_args()

def load_test_data(test_data_path: str) -> List[Dict[str, Any]]:
    """Carica i dati di test da un file JSON."""
    with open(test_data_path, "r") as f:
        data = json.load(f)

    # Assicurati che ogni elemento abbia i campi necessari
    for item in data:
        if "xml" not in item:
            item["xml"] = ""
        if "caption" not in item:
            item["caption"] = ""

    return data

def sample_test_examples(test_data: List[Dict[str, Any]], num_examples: int) -> List[Dict[str, Any]]:
    """Campiona un sottoinsieme di esempi di test."""
    if num_examples >= len(test_data):
        return test_data
    return random.sample(test_data, num_examples)

def load_model_and_tokenizer(model_path: str) -> tuple:
    """Carica un modello e il suo tokenizer."""
    print(f"Caricamento del modello: {model_path}")

    # Imposta la variabile di ambiente per il token di Hugging Face
    os.environ["HF_TOKEN"] = "*************************************"

    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        model_path,
        token=os.environ.get("HF_TOKEN"),
        trust_remote_code=True
    )

    # Carica il modello
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        device_map="auto",
        token=os.environ.get("HF_TOKEN"),
        trust_remote_code=True,
        torch_dtype=torch.bfloat16
    )

    return model, tokenizer

def generate_caption(model, tokenizer, svg_content: str, max_length: int = 100) -> str:
    """Genera una caption per un SVG utilizzando un modello."""
    # Crea il prompt
    prompt = f"<s>[INST] Descrivi questa immagine SVG:\n{svg_content} [/INST]"

    # Tokenizza il prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

    # Genera la caption
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id
        )

    # Decodifica la caption
    caption = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # Estrai solo la risposta (dopo [/INST])
    response = caption.split("[/INST]")[-1].strip()

    return response

def create_comparison_report(args):
    """Crea un report di confronto tra inferenza zero-shot e fine-tuned."""
    # Carica i dati di test
    test_data = load_test_data(args.test_data)

    # Campiona un sottoinsieme di esempi
    examples = sample_test_examples(test_data, args.num_examples)

    # Carica i modelli e i tokenizer
    zero_shot_model, zero_shot_tokenizer = load_model_and_tokenizer(args.zero_shot_model)
    fine_tuned_model, fine_tuned_tokenizer = load_model_and_tokenizer(args.fine_tuned_model)

    # Inizializza Weights & Biands
    if args.use_wandb:
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=args.wandb_run_name,
            config={
                "zero_shot_model": args.zero_shot_model,
                "fine_tuned_model": args.fine_tuned_model,
                "num_examples": args.num_examples
            }
        )

    # Crea il report
    report = []

    for i, example in enumerate(examples):
        print(f"Elaborazione esempio {i+1}/{len(examples)}")

        # Estrai l'SVG e la caption di riferimento
        svg_content = example.get("xml", "")
        reference_caption = example.get("caption", "")

        # Genera le caption
        zero_shot_caption = generate_caption(zero_shot_model, zero_shot_tokenizer, svg_content)
        fine_tuned_caption = generate_caption(fine_tuned_model, fine_tuned_tokenizer, svg_content)

        # Aggiungi al report
        report_item = {
            "svg": svg_content,
            "reference_caption": reference_caption,
            "zero_shot_caption": zero_shot_caption,
            "fine_tuned_caption": fine_tuned_caption
        }

        report.append(report_item)

        # Carica su Weights & Biands
        if args.use_wandb:
            wandb.log({
                f"example_{i+1}/svg": wandb.Html(f'<div style="width:300px;height:300px">{svg_content}</div>'),
                f"example_{i+1}/reference_caption": reference_caption,
                f"example_{i+1}/zero_shot_caption": zero_shot_caption,
                f"example_{i+1}/fine_tuned_caption": fine_tuned_caption
            })

    # Salva il report
    with open(args.output_file, "w") as f:
        json.dump(report, f, indent=2)

    print(f"Report salvato in {args.output_file}")

    # Chiudi Weights & Biands
    if args.use_wandb:
        wandb.finish()

def main():
    args = parse_args()
    create_comparison_report(args)

if __name__ == "__main__":
    main()
