#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per la generazione di prompt template per SVG captioning.
Definisce diversi template per l'inferenza e il fine-tuning.
"""

import os
import json
import argparse
import logging
from typing import Dict, List, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Template per l'inferenza zero-shot
ZERO_SHOT_TEMPLATES = {
    "basic": """Genera una didascalia concisa e descrittiva per il seguente codice SVG:

{svg_data}

Didascalia:""",

    "detailed": """Analizza il seguente codice SVG e genera una didascalia dettagliata che descriva accuratamente l'immagine.
Considera forme, colori, posizioni e relazioni tra gli elementi.

{svg_data}

Didascalia dettagliata:""",

    "simple": """Descrivi brevemente cosa rappresenta questo SVG:

{svg_data}

Descrizione:""",

    "technical": """Esamina il seguente codice SVG e genera una didascalia tecnica che descriva gli elementi principali.
Includi informazioni su path, forme, colori e stili.

{svg_data}

Didascalia tecnica:""",

    "creative": """Osserva il seguente codice SVG e crea una didascalia creativa e coinvolgente.
Usa un linguaggio vivido e descrittivo.

{svg_data}

Didascalia creativa:"""
}

# Template per il fine-tuning
FINE_TUNING_TEMPLATES = {
    "instruction": """<|im_start|>system
Sei un assistente esperto nella generazione di didascalie per immagini SVG.
<|im_end|>
<|im_start|>user
Genera una didascalia concisa e descrittiva per il seguente codice SVG:

{svg_data}
<|im_end|>
<|im_start|>assistant
{caption}
<|im_end|>""",

    "completion": """{svg_data}

Didascalia: {caption}""",

    "qa": """Domanda: Genera una didascalia per questo SVG: {svg_data}

Risposta: {caption}""",

    "chat": """<|im_start|>user
Puoi generare una didascalia per questo SVG?

{svg_data}
<|im_end|>
<|im_start|>assistant
{caption}
<|im_end|>"""
}

def format_prompt(template: str, svg_data: str, caption: Optional[str] = None) -> str:
    """
    Formatta un prompt template.
    
    Args:
        template: Template del prompt
        svg_data: Dati SVG
        caption: Didascalia (opzionale, solo per fine-tuning)
        
    Returns:
        Prompt formattato
    """
    if caption is not None:
        return template.format(svg_data=svg_data, caption=caption)
    else:
        return template.format(svg_data=svg_data)

def generate_prompt_examples(
    template_type: str = "zero_shot",
    template_name: Optional[str] = None,
    svg_data: Optional[str] = None,
    caption: Optional[str] = None,
    output_file: Optional[str] = None
) -> Dict[str, Any]:
    """
    Genera esempi di prompt.
    
    Args:
        template_type: Tipo di template ("zero_shot" o "fine_tuning")
        template_name: Nome del template specifico (opzionale)
        svg_data: Dati SVG per l'esempio (opzionale)
        caption: Didascalia per l'esempio (opzionale)
        output_file: File di output (opzionale)
        
    Returns:
        Dizionario con gli esempi di prompt
    """
    # Seleziona i template in base al tipo
    if template_type == "zero_shot":
        templates = ZERO_SHOT_TEMPLATES
    elif template_type == "fine_tuning":
        templates = FINE_TUNING_TEMPLATES
    else:
        raise ValueError(f"Tipo di template non valido: {template_type}")
    
    # Usa un SVG di esempio se non specificato
    if svg_data is None:
        svg_data = """<?xml version="1.0" encoding="UTF-8"?>
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <circle cx="50" cy="50" r="40" stroke="black" stroke-width="2" fill="red" />
  <rect x="20" y="20" width="60" height="60" stroke="blue" stroke-width="2" fill="none" />
</svg>"""
    
    # Usa una didascalia di esempio se non specificata
    if caption is None:
        caption = "Un cerchio rosso con bordo nero sovrapposto a un quadrato blu vuoto."
    
    # Genera gli esempi
    examples = {}
    
    if template_name:
        # Genera un esempio per il template specificato
        if template_name in templates:
            template = templates[template_name]
            examples[template_name] = format_prompt(template, svg_data, caption if template_type == "fine_tuning" else None)
        else:
            raise ValueError(f"Template non trovato: {template_name}")
    else:
        # Genera esempi per tutti i template
        for name, template in templates.items():
            examples[name] = format_prompt(template, svg_data, caption if template_type == "fine_tuning" else None)
    
    # Salva gli esempi in un file se specificato
    if output_file:
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(examples, f, indent=2)
        logger.info(f"Esempi di prompt salvati in: {output_file}")
    
    return examples

def main():
    parser = argparse.ArgumentParser(description="Genera esempi di prompt per SVG captioning.")
    parser.add_argument("--template_type", type=str, choices=["zero_shot", "fine_tuning"], default="zero_shot", help="Tipo di template.")
    parser.add_argument("--template_name", type=str, default=None, help="Nome del template specifico (opzionale).")
    parser.add_argument("--svg_file", type=str, default=None, help="File SVG per l'esempio (opzionale).")
    parser.add_argument("--caption", type=str, default=None, help="Didascalia per l'esempio (opzionale).")
    parser.add_argument("--output_file", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/prompt_examples.json", help="File di output.")
    
    args = parser.parse_args()
    
    # Carica i dati SVG dal file se specificato
    svg_data = None
    if args.svg_file:
        with open(args.svg_file, 'r', encoding='utf-8') as f:
            svg_data = f.read()
    
    generate_prompt_examples(args.template_type, args.template_name, svg_data, args.caption, args.output_file)

if __name__ == "__main__":
    main()
