#!/bin/bash
#SBATCH --job-name=infer_gemma2_9b_it_custom_token
#SBATCH --output=logs/infer_gemma2_9b_it_custom_token_%j.out
#SBATCH --error=logs/infer_gemma2_9b_it_custom_token_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G"
#SBATCH --gres=gpu:1
#SBATCH --time=2:00:00
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio

# Attiva l'ambiente Python direttamente
source /work/tesi_ediluzio/svg_captioning_env/bin/activate

# Imposta la variabile di ambiente per il token di Hugging Face
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
MODEL_NAME="google/gemma-2-9b-it"
LORA_ADAPTER_PATH="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_custom_token_convergence"
DATA_FILE="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
OUTPUT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/inference_results/gemma2_9b_it_custom_token"
NUM_SAMPLES=100
MAX_NEW_TOKENS=150

# Crea la directory di output se non esiste
mkdir -p "$OUTPUT_DIR"

# Esegui l'inferenza
echo "Avvio dell'inferenza con il modello $MODEL_NAME e adattatori LoRA da $LORA_ADAPTER_PATH..."
python /work/tesi_ediluzio/experiments/xml_direct_input/run_inference_lora.py \
    --model_name_or_path "$MODEL_NAME" \
    --lora_adapter_path "$LORA_ADAPTER_PATH" \
    --data_file "$DATA_FILE" \
    --num_samples "$NUM_SAMPLES" \
    --max_new_tokens "$MAX_NEW_TOKENS" \
    --load_in_4bit \
    --use_bf16 \
    --do_sample \
    --temperature 0.7 \
    --top_p 0.9 \
    > "$OUTPUT_DIR/inference_results.txt" 2> "$OUTPUT_DIR/inference_errors.txt"

echo "Inferenza completata. Risultati salvati in $OUTPUT_DIR"
