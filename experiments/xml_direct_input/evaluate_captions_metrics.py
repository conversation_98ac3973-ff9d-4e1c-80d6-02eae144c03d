#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import logging
from typing import Dict, List, Any, Optional
import numpy as np
import pandas as pd
import torch
from tqdm import tqdm
from nltk.translate.bleu_score import corpus_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
from rouge_score import rouge_scorer
import nltk
import clip
from PIL import Image
import cairosvg
import io
import tempfile
import wandb
from transformers import AutoModelForCausalLM, AutoTokenizer

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

# Importa le utility personalizzate
from shared.svg_core.custom_tokenizer_utils import build_tokenizer

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Assicurati che NLTK abbia i dati necessari
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')
try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

def parse_args():
    parser = argparse.ArgumentParser(description="Valutazione delle didascalie generate con metriche avanzate")
    parser.add_argument("--results_file", type=str, required=True, help="File JSON con i risultati dell'inferenza")
    parser.add_argument("--output_file", type=str, required=True, help="File di output per i risultati della valutazione")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands (username o team)")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default=None, help="Nome della run in Weights & Biands")
    parser.add_argument("--use_clip", action="store_true", help="Calcola anche il CLIP score (richiede rendering SVG)")
    parser.add_argument("--clip_model", type=str, default="ViT-B/32", help="Modello CLIP da utilizzare")
    parser.add_argument("--render_size", type=int, default=224, help="Dimensione per il rendering SVG")
    parser.add_argument("--calculate_loss", action="store_true", help="Calcola la loss di inferenza (richiede modello e tokenizer)")
    parser.add_argument("--model_path", type=str, help="Path al modello per calcolare la loss di inferenza")
    parser.add_argument("--tokenizer_path", type=str, help="Path al tokenizer per calcolare la loss di inferenza")
    parser.add_argument("--use_custom_tokenizer", action="store_true", help="Usa tokenizer personalizzato per SVG")
    return parser.parse_args()

def load_results(results_file: str) -> List[Dict[str, Any]]:
    """Carica i risultati dell'inferenza da un file JSONL."""
    results = []
    with open(results_file, "r") as f:
        for line in f:
            if line.strip():
                results.append(json.loads(line))
    return results

def calculate_bleu(references: List[List[str]], hypotheses: List[str]) -> Dict[str, float]:
    """Calcola i punteggi BLEU-1, BLEU-2, BLEU-3 e BLEU-4."""
    # Tokenizza le ipotesi
    tokenized_hypotheses = [nltk.word_tokenize(hyp.lower()) for hyp in hypotheses]

    # Tokenizza i riferimenti
    tokenized_references = [[nltk.word_tokenize(ref.lower()) for ref in refs] for refs in references]

    # Funzione di smoothing per gestire il caso di n-grammi mancanti
    smoothing = SmoothingFunction().method1

    # Calcola BLEU-1, BLEU-2, BLEU-3 e BLEU-4
    bleu1 = corpus_bleu(tokenized_references, tokenized_hypotheses, weights=(1, 0, 0, 0), smoothing_function=smoothing)
    bleu2 = corpus_bleu(tokenized_references, tokenized_hypotheses, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
    bleu3 = corpus_bleu(tokenized_references, tokenized_hypotheses, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
    bleu4 = corpus_bleu(tokenized_references, tokenized_hypotheses, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)

    return {
        "bleu1": bleu1,
        "bleu2": bleu2,
        "bleu3": bleu3,
        "bleu4": bleu4
    }

def calculate_meteor(references: List[List[str]], hypotheses: List[str]) -> float:
    """Calcola il punteggio METEOR."""
    scores = []
    for i in range(len(hypotheses)):
        score = meteor_score(references[i], hypotheses[i])
        scores.append(score)
    return np.mean(scores)

def calculate_rouge(references: List[List[str]], hypotheses: List[str]) -> Dict[str, float]:
    """Calcola i punteggi ROUGE-1, ROUGE-2 e ROUGE-L."""
    scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)

    rouge1_precision = []
    rouge1_recall = []
    rouge1_fmeasure = []

    rouge2_precision = []
    rouge2_recall = []
    rouge2_fmeasure = []

    rougeL_precision = []
    rougeL_recall = []
    rougeL_fmeasure = []

    for i in range(len(hypotheses)):
        # Prendi il primo riferimento (se ce ne sono più di uno)
        reference = references[i][0]
        hypothesis = hypotheses[i]

        scores = scorer.score(reference, hypothesis)

        rouge1_precision.append(scores['rouge1'].precision)
        rouge1_recall.append(scores['rouge1'].recall)
        rouge1_fmeasure.append(scores['rouge1'].fmeasure)

        rouge2_precision.append(scores['rouge2'].precision)
        rouge2_recall.append(scores['rouge2'].recall)
        rouge2_fmeasure.append(scores['rouge2'].fmeasure)

        rougeL_precision.append(scores['rougeL'].precision)
        rougeL_recall.append(scores['rougeL'].recall)
        rougeL_fmeasure.append(scores['rougeL'].fmeasure)

    return {
        "rouge1_precision": np.mean(rouge1_precision),
        "rouge1_recall": np.mean(rouge1_recall),
        "rouge1_fmeasure": np.mean(rouge1_fmeasure),

        "rouge2_precision": np.mean(rouge2_precision),
        "rouge2_recall": np.mean(rouge2_recall),
        "rouge2_fmeasure": np.mean(rouge2_fmeasure),

        "rougeL_precision": np.mean(rougeL_precision),
        "rougeL_recall": np.mean(rougeL_recall),
        "rougeL_fmeasure": np.mean(rougeL_fmeasure)
    }

def render_svg_to_image(svg_string: str, size: int = 224) -> Image.Image:
    """Renderizza una stringa SVG in un'immagine PIL."""
    try:
        # Renderizza SVG in PNG
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'), output_width=size, output_height=size)

        # Converti i dati PNG in un'immagine PIL
        image = Image.open(io.BytesIO(png_data))
        return image
    except Exception as e:
        logger.error(f"Errore nel rendering SVG: {e}")
        # Restituisci un'immagine nera in caso di errore
        return Image.new('RGB', (size, size), color='black')

def calculate_clip_score(svgs: List[str], captions: List[str], model_name: str = "ViT-B/32", render_size: int = 224) -> List[float]:
    """Calcola il CLIP score per ogni coppia (SVG, didascalia)."""
    # Carica il modello CLIP
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model, preprocess = clip.load(model_name, device=device)

    scores = []

    for svg, caption in tqdm(zip(svgs, captions), total=len(svgs), desc="Calcolo CLIP score"):
        try:
            # Renderizza SVG in immagine
            image = render_svg_to_image(svg, render_size)

            # Preprocess dell'immagine
            image_input = preprocess(image).unsqueeze(0).to(device)

            # Tokenizza la didascalia
            text_input = clip.tokenize([caption]).to(device)

            # Calcola le features
            with torch.no_grad():
                image_features = model.encode_image(image_input)
                text_features = model.encode_text(text_input)

            # Normalizza le features
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)

            # Calcola la similarità coseno
            similarity = (100.0 * image_features @ text_features.T).item()
            scores.append(similarity)
        except Exception as e:
            logger.error(f"Errore nel calcolo del CLIP score: {e}")
            scores.append(0.0)

    return scores

def calculate_cider_score(references: List[List[str]], hypotheses: List[str]) -> float:
    """
    Implementazione semplificata di CIDEr.

    CIDEr è una metrica complessa che richiede il calcolo di TF-IDF su n-grammi.
    Questa è una versione semplificata che cattura l'essenza di CIDEr.
    """
    from collections import Counter
    import math

    def compute_tfidf(corpus):
        # Calcola il documento di frequenza (DF) per ogni n-gramma
        df = Counter()
        for doc in corpus:
            # Tokenizza il documento
            tokens = nltk.word_tokenize(doc.lower())
            # Estrai n-grammi (1-4)
            ngrams = []
            for n in range(1, 5):
                for i in range(len(tokens) - n + 1):
                    ngram = tuple(tokens[i:i+n])
                    ngrams.append(ngram)
            # Aggiorna il DF
            df.update(set(ngrams))

        # Calcola l'IDF
        N = len(corpus)
        idf = {ngram: math.log(N / (df[ngram] + 1)) for ngram in df}

        return idf

    # Crea un corpus con tutte le didascalie (riferimenti e ipotesi)
    corpus = []
    for refs in references:
        corpus.extend(refs)
    corpus.extend(hypotheses)

    # Calcola l'IDF
    idf = compute_tfidf(corpus)

    scores = []
    for i in range(len(hypotheses)):
        hyp = hypotheses[i]
        refs = references[i]

        # Tokenizza l'ipotesi
        hyp_tokens = nltk.word_tokenize(hyp.lower())

        # Estrai n-grammi dall'ipotesi
        hyp_ngrams = []
        for n in range(1, 5):
            for j in range(len(hyp_tokens) - n + 1):
                ngram = tuple(hyp_tokens[j:j+n])
                hyp_ngrams.append(ngram)

        # Calcola il TF per l'ipotesi
        hyp_tf = Counter(hyp_ngrams)

        # Normalizza il TF
        hyp_len = len(hyp_tokens)
        for ngram in hyp_tf:
            hyp_tf[ngram] = hyp_tf[ngram] / hyp_len

        # Calcola il TF-IDF per l'ipotesi
        hyp_tfidf = {ngram: hyp_tf[ngram] * idf.get(ngram, 0) for ngram in hyp_tf}

        # Calcola il punteggio per ogni riferimento
        ref_scores = []
        for ref in refs:
            # Tokenizza il riferimento
            ref_tokens = nltk.word_tokenize(ref.lower())

            # Estrai n-grammi dal riferimento
            ref_ngrams = []
            for n in range(1, 5):
                for j in range(len(ref_tokens) - n + 1):
                    ngram = tuple(ref_tokens[j:j+n])
                    ref_ngrams.append(ngram)

            # Calcola il TF per il riferimento
            ref_tf = Counter(ref_ngrams)

            # Normalizza il TF
            ref_len = len(ref_tokens)
            for ngram in ref_tf:
                ref_tf[ngram] = ref_tf[ngram] / ref_len

            # Calcola il TF-IDF per il riferimento
            ref_tfidf = {ngram: ref_tf[ngram] * idf.get(ngram, 0) for ngram in ref_tf}

            # Calcola la similarità coseno tra i vettori TF-IDF
            numerator = sum(hyp_tfidf.get(ngram, 0) * ref_tfidf.get(ngram, 0) for ngram in set(hyp_tfidf) | set(ref_tfidf))
            denominator1 = math.sqrt(sum(val**2 for val in hyp_tfidf.values()))
            denominator2 = math.sqrt(sum(val**2 for val in ref_tfidf.values()))

            if denominator1 == 0 or denominator2 == 0:
                similarity = 0
            else:
                similarity = numerator / (denominator1 * denominator2)

            ref_scores.append(similarity)

        # Prendi il punteggio massimo tra tutti i riferimenti
        scores.append(max(ref_scores) if ref_scores else 0)

    # Restituisci la media dei punteggi
    return np.mean(scores)

def calculate_inference_loss(model, tokenizer, svgs, references, max_length=512):
    """Calcola la loss di inferenza per ogni coppia (SVG, didascalia)."""
    device = model.device
    losses = []

    for svg, ref in tqdm(zip(svgs, references), total=len(svgs), desc="Calcolo loss di inferenza"):
        # Crea il prompt nel formato corretto per il modello
        prompt = f"<s>[INST] Descrivi questa immagine SVG:\n{svg} [/INST] {ref[0]}</s>"

        # Tokenizza l'input
        inputs = tokenizer(prompt, return_tensors="pt", max_length=max_length, truncation=True, padding="max_length")
        inputs = {k: v.to(device) for k, v in inputs.items()}

        # Imposta gli input_ids come labels per calcolare la loss
        inputs["labels"] = inputs["input_ids"].clone()

        # Calcola la loss
        with torch.no_grad():
            outputs = model(**inputs)
            loss = outputs.loss.item()

        losses.append(loss)

    return losses

def main():
    args = parse_args()

    # Carica i risultati
    logger.info(f"Caricamento dei risultati da {args.results_file}")
    results = load_results(args.results_file)

    if not results:
        logger.error(f"Nessun risultato trovato in {args.results_file}")
        return

    logger.info(f"Trovati {len(results)} risultati")

    # Estrai le didascalie generate e quelle di riferimento
    hypotheses = [result["generated_caption"] for result in results]
    references = [[result["true_caption"]] for result in results]
    svgs = [result["svg"] for result in results]

    # Calcola le metriche
    logger.info("Calcolo delle metriche BLEU")
    bleu_scores = calculate_bleu(references, hypotheses)

    logger.info("Calcolo della metrica METEOR")
    meteor_score_value = calculate_meteor(references, hypotheses)

    logger.info("Calcolo delle metriche ROUGE")
    rouge_scores = calculate_rouge(references, hypotheses)

    logger.info("Calcolo della metrica CIDEr")
    cider_score = calculate_cider_score(references, hypotheses)

    # Calcola il CLIP score se richiesto
    clip_scores = None
    if args.use_clip:
        logger.info("Calcolo del CLIP score")
        clip_scores = calculate_clip_score(svgs, hypotheses, args.clip_model, args.render_size)

    # Calcola la loss di inferenza se richiesto
    inference_losses = None
    if args.calculate_loss and args.model_path and args.tokenizer_path:
        logger.info(f"Caricamento del modello da {args.model_path}")
        model = AutoModelForCausalLM.from_pretrained(args.model_path, device_map="auto")

        logger.info(f"Caricamento del tokenizer da {args.tokenizer_path}")
        tokenizer = AutoTokenizer.from_pretrained(args.tokenizer_path)

        # Aggiungi token speciali per SVG se richiesto
        if args.use_custom_tokenizer:
            logger.info("Utilizzo del tokenizer custom per SVG")
            tokenizer = build_tokenizer(tokenizer)

        # Assicurati che il tokenizer abbia un token di padding
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        logger.info("Calcolo della loss di inferenza")
        inference_losses = calculate_inference_loss(model, tokenizer, svgs, references)

    # Combina tutte le metriche
    metrics = {
        "num_samples": len(results),
        "meteor": meteor_score_value,
        "cider": cider_score,
        **bleu_scores,
        **rouge_scores
    }

    if clip_scores:
        metrics["clip_score_mean"] = np.mean(clip_scores)
        metrics["clip_score_std"] = np.std(clip_scores)

    if inference_losses:
        metrics["inference_loss_mean"] = np.mean(inference_losses)
        metrics["inference_loss_std"] = np.std(inference_losses)
        metrics["inference_loss_min"] = np.min(inference_losses)
        metrics["inference_loss_max"] = np.max(inference_losses)

    # Inizializza Weights & Biands se richiesto
    if args.wandb_project:
        wandb_run_name = args.wandb_run_name or os.path.basename(args.results_file).split(".")[0]
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=wandb_run_name,
            config={
                "results_file": args.results_file,
                "num_samples": len(results),
                "use_clip": args.use_clip,
                "clip_model": args.clip_model if args.use_clip else None,
                "calculate_loss": args.calculate_loss,
                "model_path": args.model_path if args.calculate_loss else None,
                "use_custom_tokenizer": args.use_custom_tokenizer
            }
        )

        # Log delle metriche
        wandb.log(metrics)

        # Crea una tabella con esempi
        examples_table = wandb.Table(columns=["id", "true_caption", "generated_caption"])
        for i, result in enumerate(results[:20]):  # Limita a 20 esempi
            examples_table.add_data(i, result["true_caption"], result["generated_caption"])

        wandb.log({"examples": examples_table})

        # Se abbiamo calcolato la loss di inferenza, crea un istogramma
        if inference_losses:
            wandb.log({"inference_loss_histogram": wandb.Histogram(inference_losses)})

            # Crea una tabella con le loss di inferenza
            loss_table = wandb.Table(columns=["id", "true_caption", "generated_caption", "inference_loss"])
            for i, (result, loss) in enumerate(zip(results[:50], inference_losses[:50])):  # Limita a 50 esempi
                loss_table.add_data(i, result["true_caption"], result["generated_caption"], loss)

            wandb.log({"inference_loss_examples": loss_table})

        # Chiudi la sessione
        wandb.finish()

    # Salva i risultati
    logger.info(f"Salvataggio dei risultati in {args.output_file}")
    with open(args.output_file, "w") as f:
        json.dump(metrics, f, indent=2)

    # Stampa un riepilogo
    logger.info("Riepilogo delle metriche:")
    for metric, value in metrics.items():
        logger.info(f"{metric}: {value}")

if __name__ == "__main__":
    main()
