
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Esempi Qualitativi Visuali - Flores 2 base</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .header {
                text-align: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 15px;
                margin-bottom: 30px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .stats {
                background: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 30px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .example-container {
                background: white;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎨 Esempi Qualitativi Visuali</h1>
            <h2>Flores 2 base</h2>
            <p>Confronto SVG + Caption Generated vs Ground Truth</p>
        </div>

        <div class="stats">
            <h3>📊 Statistiche Generali:</h3>
            <ul>
                <li><strong>Esempi totali con SVG:</strong> 200</li>
                <li><strong>Esempi selezionati:</strong> 5</li>
                <li><strong>Quality score medio:</strong> 0.0687</li>
                <li><strong>Migliore score:</strong> 0.2597</li>
                <li><strong>Peggiore score:</strong> 0.0000</li>
            </ul>
        </div>
    <h2 style='color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px;'>Esempio 1</h2>
    <div class="example-container" style="border: 2px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 10px; background: #f9f9f9;">
        <h3 style="color: #333; margin-top: 0;">📊 Flores 2 base - Esempio ID: unknown_wd_1267078</h3>

        <div style="display: flex; gap: 20px; align-items: flex-start;">
            <!-- SVG Visualization -->
            <div style="flex: 0 0 300px;">
                <h4 style="color: #666; margin-bottom: 10px;">🎨 SVG Originale:</h4>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; text-align: center;">
    
<svg style="max-width: 280px; max-height: 280px; width: 100%; height: auto; border: 1px solid #eee;" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:0,0,0;stroke:None;stroke-width:1;opacity:1" d="M286,84 C229,193,229,193,256,219 C279,242,279,268,256,316 C233,363,216,410,216,428 C216,446,224,438,246,397 C255,379,265,363,267,361 C269,358,275,347,281,335 C286,323,293,308,296,301 C300,294,303,282,303,273 C303,260,283,204,268,173 C260,158,266,144,281,144 C288,144,298,133,306,115 C325,71,308,44,286,84Z" />
</svg>
                </div>
            </div>

            <!-- Captions Comparison -->
            <div style="flex: 1;">
                <h4 style="color: #666; margin-bottom: 15px;">📝 Confronto Captions:</h4>

                <!-- Ground Truth -->
                <div style="margin-bottom: 15px; padding: 15px; background: #e8f5e8; border-left: 4px solid #4CAF50; border-radius: 5px;">
                    <h5 style="color: #2E7D32; margin: 0 0 8px 0;">✅ Ground Truth:</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"The image depicts a black and white drawing of a woman's body"</p>
                </div>

                <!-- Generated -->
                <div style="margin-bottom: 15px; padding: 15px; background: #fff3e0; border-left: 4px solid #FF9800; border-radius: 5px;">
                    <h5 style="color: #E65100; margin: 0 0 8px 0;">🤖 Generated (Flores 2 base):</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"a black and white photo of a black and white bird flying "</p>
                </div>

                <!-- Quality Score -->
                <div style="padding: 10px; background: #f3e5f5; border-left: 4px solid #9C27B0; border-radius: 5px;">
                    <h5 style="color: #6A1B9A; margin: 0 0 5px 0;">📊 Quality Score:</h5>
                    <p style="margin: 0; font-weight: bold; font-size: 18px; color: #333;">0.260</p>
                </div>

                <!-- Analysis -->
                <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-left: 4px solid #2196F3; border-radius: 5px;">
                    <h5 style="color: #1565C0; margin: 0 0 8px 0;">🔍 Analisi:</h5>
                    <ul style="margin: 0; padding-left: 20px; color: #333;">
                        <li><strong>Lunghezza:</strong> Ref: 12 words, Gen: 12 words</li>
                        <li><strong>Parole comuni:</strong> a, and, white, of, black</li>
                        <li><strong>Problemi:</strong> Nessuno</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <h2 style='color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px;'>Esempio 2</h2>
    <div class="example-container" style="border: 2px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 10px; background: #f9f9f9;">
        <h3 style="color: #333; margin-top: 0;">📊 Flores 2 base - Esempio ID: unknown_wd_1249996</h3>

        <div style="display: flex; gap: 20px; align-items: flex-start;">
            <!-- SVG Visualization -->
            <div style="flex: 0 0 300px;">
                <h4 style="color: #666; margin-bottom: 10px;">🎨 SVG Originale:</h4>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; text-align: center;">
    
<svg style="max-width: 280px; max-height: 280px; width: 100%; height: auto; border: 1px solid #eee;" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:none;stroke:150,150,150;stroke-width:1.88;opacity:1" d="M28,299 L490,299 Z" />
<path style="fill:none;stroke:0,0,0;stroke-width:0.94;opacity:1" d="M28,369 L490,369 Z" />
<path style="fill:none;stroke:0,0,0;stroke-width:0.94;opacity:1" d="M28,230 L490,230 Z" />
<path style="fill:none;stroke:0,0,0;stroke-width:0.94;opacity:1" d="M28,160 L490,160 Z" />
<path style="fill:none;stroke:0,0,0;stroke-width:5.62;opacity:1" d="M260,229 C297,223,329,173,306,162 C299,159,285,170,262,223 C240,277,239,297,247,296 C252,296,261,289,270,278Z" />
</svg>
                </div>
            </div>

            <!-- Captions Comparison -->
            <div style="flex: 1;">
                <h4 style="color: #666; margin-bottom: 15px;">📝 Confronto Captions:</h4>

                <!-- Ground Truth -->
                <div style="margin-bottom: 15px; padding: 15px; background: #e8f5e8; border-left: 4px solid #4CAF50; border-radius: 5px;">
                    <h5 style="color: #2E7D32; margin: 0 0 8px 0;">✅ Ground Truth:</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"The image depicts the image contains a single line of text that reads "l"</p>
                </div>

                <!-- Generated -->
                <div style="margin-bottom: 15px; padding: 15px; background: #fff3e0; border-left: 4px solid #FF9800; border-radius: 5px;">
                    <h5 style="color: #E65100; margin: 0 0 8px 0;">🤖 Generated (Flores 2 base):</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"a black and white photo of a wire with a white background "</p>
                </div>

                <!-- Quality Score -->
                <div style="padding: 10px; background: #f3e5f5; border-left: 4px solid #9C27B0; border-radius: 5px;">
                    <h5 style="color: #6A1B9A; margin: 0 0 5px 0;">📊 Quality Score:</h5>
                    <p style="margin: 0; font-weight: bold; font-size: 18px; color: #333;">0.079</p>
                </div>

                <!-- Analysis -->
                <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-left: 4px solid #2196F3; border-radius: 5px;">
                    <h5 style="color: #1565C0; margin: 0 0 8px 0;">🔍 Analisi:</h5>
                    <ul style="margin: 0; padding-left: 20px; color: #333;">
                        <li><strong>Lunghezza:</strong> Ref: 14 words, Gen: 12 words</li>
                        <li><strong>Parole comuni:</strong> a, of</li>
                        <li><strong>Problemi:</strong> Nessuno</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <h2 style='color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px;'>Esempio 3</h2>
    <div class="example-container" style="border: 2px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 10px; background: #f9f9f9;">
        <h3 style="color: #333; margin-top: 0;">📊 Flores 2 base - Esempio ID: unknown_wd_469109</h3>

        <div style="display: flex; gap: 20px; align-items: flex-start;">
            <!-- SVG Visualization -->
            <div style="flex: 0 0 300px;">
                <h4 style="color: #666; margin-bottom: 10px;">🎨 SVG Originale:</h4>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; text-align: center;">
    
<svg style="max-width: 280px; max-height: 280px; width: 100%; height: auto; border: 1px solid #eee;" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:0,0,0;stroke:None;stroke-width:1;opacity:1" d="M319,70 L302,86 L356,140 L303,140 L279,140 L268,140 C184,140,116,207,116,291 C116,375,184,442,268,442 L303,442 L303,419 L268,419 C197,419,140,362,140,291 C140,220,197,163,268,163 L279,163 L303,163 L356,163 L302,216 L319,233 L395,156 L400,151 L395,146 L319,70Z" />
</svg>
                </div>
            </div>

            <!-- Captions Comparison -->
            <div style="flex: 1;">
                <h4 style="color: #666; margin-bottom: 15px;">📝 Confronto Captions:</h4>

                <!-- Ground Truth -->
                <div style="margin-bottom: 15px; padding: 15px; background: #e8f5e8; border-left: 4px solid #4CAF50; border-radius: 5px;">
                    <h5 style="color: #2E7D32; margin: 0 0 8px 0;">✅ Ground Truth:</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"The image depicts a simple, black arrow pointing to the right"</p>
                </div>

                <!-- Generated -->
                <div style="margin-bottom: 15px; padding: 15px; background: #fff3e0; border-left: 4px solid #FF9800; border-radius: 5px;">
                    <h5 style="color: #E65100; margin: 0 0 8px 0;">🤖 Generated (Flores 2 base):</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"a clock on a black background "</p>
                </div>

                <!-- Quality Score -->
                <div style="padding: 10px; background: #f3e5f5; border-left: 4px solid #9C27B0; border-radius: 5px;">
                    <h5 style="color: #6A1B9A; margin: 0 0 5px 0;">📊 Quality Score:</h5>
                    <p style="margin: 0; font-weight: bold; font-size: 18px; color: #333;">0.077</p>
                </div>

                <!-- Analysis -->
                <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-left: 4px solid #2196F3; border-radius: 5px;">
                    <h5 style="color: #1565C0; margin: 0 0 8px 0;">🔍 Analisi:</h5>
                    <ul style="margin: 0; padding-left: 20px; color: #333;">
                        <li><strong>Lunghezza:</strong> Ref: 11 words, Gen: 6 words</li>
                        <li><strong>Parole comuni:</strong> a, black</li>
                        <li><strong>Problemi:</strong> Nessuno</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <h2 style='color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px;'>Esempio 4</h2>
    <div class="example-container" style="border: 2px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 10px; background: #f9f9f9;">
        <h3 style="color: #333; margin-top: 0;">📊 Flores 2 base - Esempio ID: unknown_wd_462057</h3>

        <div style="display: flex; gap: 20px; align-items: flex-start;">
            <!-- SVG Visualization -->
            <div style="flex: 0 0 300px;">
                <h4 style="color: #666; margin-bottom: 10px;">🎨 SVG Originale:</h4>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; text-align: center;">
    
<svg style="max-width: 280px; max-height: 280px; width: 100%; height: auto; border: 1px solid #eee;" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:173,216,230;stroke:None;stroke-width:6;opacity:1" d="M512,0 L0,0 L0,512 L512,512 L512,0 M333,26 L333,102 L179,102 L179,26 L333,26 M333,154 L333,230 L179,230 L179,154 L333,154 M333,282 L333,358 L179,358 L179,282 L333,282 M333,410 L333,486 L179,486 L179,410 L333,410Z" />
</svg>
                </div>
            </div>

            <!-- Captions Comparison -->
            <div style="flex: 1;">
                <h4 style="color: #666; margin-bottom: 15px;">📝 Confronto Captions:</h4>

                <!-- Ground Truth -->
                <div style="margin-bottom: 15px; padding: 15px; background: #e8f5e8; border-left: 4px solid #4CAF50; border-radius: 5px;">
                    <h5 style="color: #2E7D32; margin: 0 0 8px 0;">✅ Ground Truth:</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"The image depicts "</p>
                </div>

                <!-- Generated -->
                <div style="margin-bottom: 15px; padding: 15px; background: #fff3e0; border-left: 4px solid #FF9800; border-radius: 5px;">
                    <h5 style="color: #E65100; margin: 0 0 8px 0;">🤖 Generated (Flores 2 base):</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"a close up of a picture of a blue and white striped wall "</p>
                </div>

                <!-- Quality Score -->
                <div style="padding: 10px; background: #f3e5f5; border-left: 4px solid #9C27B0; border-radius: 5px;">
                    <h5 style="color: #6A1B9A; margin: 0 0 5px 0;">📊 Quality Score:</h5>
                    <p style="margin: 0; font-weight: bold; font-size: 18px; color: #333;">0.000</p>
                </div>

                <!-- Analysis -->
                <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-left: 4px solid #2196F3; border-radius: 5px;">
                    <h5 style="color: #1565C0; margin: 0 0 8px 0;">🔍 Analisi:</h5>
                    <ul style="margin: 0; padding-left: 20px; color: #333;">
                        <li><strong>Lunghezza:</strong> Ref: 3 words, Gen: 13 words</li>
                        <li><strong>Parole comuni:</strong> Nessuna</li>
                        <li><strong>Problemi:</strong> Nessuno</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <h2 style='color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px;'>Esempio 5</h2>
    <div class="example-container" style="border: 2px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 10px; background: #f9f9f9;">
        <h3 style="color: #333; margin-top: 0;">📊 Flores 2 base - Esempio ID: unknown_sr_073961</h3>

        <div style="display: flex; gap: 20px; align-items: flex-start;">
            <!-- SVG Visualization -->
            <div style="flex: 0 0 300px;">
                <h4 style="color: #666; margin-bottom: 10px;">🎨 SVG Originale:</h4>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; text-align: center;">
    
<svg style="max-width: 280px; max-height: 280px; width: 100%; height: auto; border: 1px solid #eee;" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:none;stroke:None;stroke-width:1;opacity:1" d="M0,0 L512,0 L512,512 L0,512 L0,0 Z" />
<path style="fill:0,0,0;stroke:None;stroke-width:1;opacity:1" d="M435,128 L435,77 L205,77 L205,128 L435,128 M435,230 L435,179 L77,179 L77,230 L435,230 M435,333 L435,282 L205,282 L205,333 L435,333 M435,435 L435,384 L77,384 L77,435 L435,435Z" />
</svg>
                </div>
            </div>

            <!-- Captions Comparison -->
            <div style="flex: 1;">
                <h4 style="color: #666; margin-bottom: 15px;">📝 Confronto Captions:</h4>

                <!-- Ground Truth -->
                <div style="margin-bottom: 15px; padding: 15px; background: #e8f5e8; border-left: 4px solid #4CAF50; border-radius: 5px;">
                    <h5 style="color: #2E7D32; margin: 0 0 8px 0;">✅ Ground Truth:</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"The image depicts "</p>
                </div>

                <!-- Generated -->
                <div style="margin-bottom: 15px; padding: 15px; background: #fff3e0; border-left: 4px solid #FF9800; border-radius: 5px;">
                    <h5 style="color: #E65100; margin: 0 0 8px 0;">🤖 Generated (Flores 2 base):</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"a series of three pictures of a white and black piano "</p>
                </div>

                <!-- Quality Score -->
                <div style="padding: 10px; background: #f3e5f5; border-left: 4px solid #9C27B0; border-radius: 5px;">
                    <h5 style="color: #6A1B9A; margin: 0 0 5px 0;">📊 Quality Score:</h5>
                    <p style="margin: 0; font-weight: bold; font-size: 18px; color: #333;">0.000</p>
                </div>

                <!-- Analysis -->
                <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-left: 4px solid #2196F3; border-radius: 5px;">
                    <h5 style="color: #1565C0; margin: 0 0 8px 0;">🔍 Analisi:</h5>
                    <ul style="margin: 0; padding-left: 20px; color: #333;">
                        <li><strong>Lunghezza:</strong> Ref: 3 words, Gen: 11 words</li>
                        <li><strong>Parole comuni:</strong> Nessuna</li>
                        <li><strong>Problemi:</strong> Nessuno</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    </body>
    </html>
    