<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Esempi Qualitativi Visuali - Baseline Models</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .model-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .model-card h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.8em;
        }
        .model-card .stats {
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
        }
        .model-card .stats p {
            margin: 5px 0;
            color: #555;
        }
        .view-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .view-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }
        .summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
            border-left: 5px solid #667eea;
        }
        .summary h3 {
            color: #333;
            margin-bottom: 20px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .comparison-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:hover {
            background: #f5f5f5;
        }
        .emoji {
            font-size: 1.5em;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Esempi Qualitativi Visuali</h1>
            <p>Confronto SVG Renderizzati + Caption Generated vs Ground Truth</p>
            <p><strong>Modelli Baseline: Ide Fix 3, Flores 2 base, BLIP 2.7B</strong></p>
        </div>

        <div class="models-grid">
            <!-- Ide Fix 3 -->
            <div class="model-card">
                <h2><span class="emoji">🤖</span>Ide Fix 3</h2>
                <div class="stats">
                    <p><strong>📊 Esempi totali:</strong> 200</p>
                    <p><strong>📋 Esempi selezionati:</strong> 5</p>
                    <p><strong>📈 Qualità media:</strong> 0.0712</p>
                    <p><strong>🏆 Migliore score:</strong> 0.3077</p>
                    <p><strong>❌ Peggiore score:</strong> 0.0000</p>
                </div>
                <a href="Ide_Fix_3_visual_examples.html" class="view-button">
                    👀 Visualizza Esempi
                </a>
            </div>

            <!-- Flores 2 base -->
            <div class="model-card">
                <h2><span class="emoji">🌸</span>Flores 2 base</h2>
                <div class="stats">
                    <p><strong>📊 Esempi totali:</strong> 200</p>
                    <p><strong>📋 Esempi selezionati:</strong> 5</p>
                    <p><strong>📈 Qualità media:</strong> 0.0687</p>
                    <p><strong>🏆 Migliore score:</strong> 0.2597</p>
                    <p><strong>❌ Peggiore score:</strong> 0.0000</p>
                </div>
                <a href="Flores_2_base_visual_examples.html" class="view-button">
                    👀 Visualizza Esempi
                </a>
            </div>

            <!-- BLIP 2.7B -->
            <div class="model-card">
                <h2><span class="emoji">👁️</span>BLIP 2.7B</h2>
                <div class="stats">
                    <p><strong>📊 Esempi totali:</strong> 200</p>
                    <p><strong>📋 Esempi selezionati:</strong> 5</p>
                    <p><strong>📈 Qualità media:</strong> 0.0721</p>
                    <p><strong>🏆 Migliore score:</strong> 0.4444</p>
                    <p><strong>❌ Peggiore score:</strong> 0.0000</p>
                </div>
                <a href="BLIP_2.7B_visual_examples.html" class="view-button">
                    👀 Visualizza Esempi
                </a>
            </div>
        </div>

        <div class="summary">
            <h3>📊 Confronto Performance Baseline</h3>
            <p>Tabella comparativa delle performance dei tre modelli baseline sui dati di test SVG:</p>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>🤖 Modello</th>
                        <th>📈 Qualità Media</th>
                        <th>🏆 Migliore Score</th>
                        <th>📊 Esempi Totali</th>
                        <th>🎯 Ranking</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>BLIP 2.7B</strong></td>
                        <td>0.0721</td>
                        <td><strong>0.4444</strong></td>
                        <td>200</td>
                        <td>🥇 1°</td>
                    </tr>
                    <tr>
                        <td><strong>Ide Fix 3</strong></td>
                        <td>0.0712</td>
                        <td>0.3077</td>
                        <td>200</td>
                        <td>🥈 2°</td>
                    </tr>
                    <tr>
                        <td><strong>Flores 2 base</strong></td>
                        <td>0.0687</td>
                        <td>0.2597</td>
                        <td>200</td>
                        <td>🥉 3°</td>
                    </tr>
                </tbody>
            </table>

            <div style="margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 10px;">
                <h4 style="color: #2E7D32; margin-bottom: 15px;">🔍 Osservazioni Chiave:</h4>
                <ul style="color: #333;">
                    <li><strong>BLIP 2.7B</strong> mostra la performance migliore con il punteggio più alto (0.4444)</li>
                    <li><strong>Ide Fix 3</strong> ha una qualità media leggermente superiore a Flores 2</li>
                    <li><strong>Flores 2 base</strong> tende ad essere più verboso ma meno accurato</li>
                    <li>Tutti i modelli mostrano <strong>performance molto basse</strong> rispetto ai fine-tuned</li>
                    <li>Gli esempi visuali mostrano chiaramente i <strong>gap semantici</strong> tra SVG e caption</li>
                </ul>
            </div>

            <div style="margin-top: 20px; padding: 20px; background: #fff3e0; border-radius: 10px;">
                <h4 style="color: #E65100; margin-bottom: 15px;">⚠️ Problemi Comuni Identificati:</h4>
                <ul style="color: #333;">
                    <li>Mancanza di struttura descrittiva ("The image depicts...")</li>
                    <li>Interpretazioni completamente errate dell'SVG</li>
                    <li>Bassa sovrapposizione semantica con ground truth</li>
                    <li>Risposte troppo generiche o fuori contesto</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <p style="color: #666; font-style: italic;">
                📝 Questi esempi dimostrano chiaramente perché i modelli fine-tuned superano i baseline di 6-33x nelle metriche quantitative.
            </p>
            <p style="color: #666; font-style: italic;">
                🎨 Ogni esempio mostra l'SVG renderizzato affiancato al confronto caption per una valutazione qualitativa immediata.
            </p>
        </div>
    </div>
</body>
</html>
