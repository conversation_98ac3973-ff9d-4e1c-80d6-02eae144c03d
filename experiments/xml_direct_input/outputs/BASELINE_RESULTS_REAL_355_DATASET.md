# 📊 RISULTATI BASELINE REALI - Dataset 355 Esempi

**Data**: $(date)  
**Dataset**: 355 esempi dal subset ottimizzato  
**Valutazione**: Metriche BLEU, METEOR, CIDEr, CLIP Score  

---

## 🎯 **RISULTATI PRINCIPALI**

### 📈 **Tabella Completa Risultati**

| Modello | BLEU-1 | BLEU-2 | BLEU-3 | BLEU-4 | METEOR | CIDEr | CLIP Score | Campioni |
|---------|--------|--------|--------|--------|--------|-------|------------|----------|
| **Ide Fix 3** | 0.1090 | 0.0495 | 0.0238 | 0.0150 | 0.0000 | **0.2267** | 0.0000 | 50 |
| **Flores 2 base** | **0.1736** | **0.0687** | 0.0334 | 0.0163 | 0.0000 | 0.1106 | 0.0000 | 50 |
| **BLIP 2.7B** | 0.1298 | 0.0643 | **0.0337** | **0.0164** | 0.0000 | 0.2264 | 0.0000 | 50 |

---

## 🏆 **MIGLIORI PERFORMANCE PER METRICA**

- **🥇 BLEU-1**: Flores 2 base (0.1736)
- **🥇 BLEU-2**: Flores 2 base (0.0687)  
- **🥇 BLEU-3**: BLIP 2.7B (0.0337)
- **🥇 BLEU-4**: BLIP 2.7B (0.0164)
- **🥇 CIDEr**: Ide Fix 3 (0.2267)

---

## 📊 **ANALISI DETTAGLIATA**

### 🔍 **Flores 2 base**
- ✅ **Migliore** in BLEU-1 e BLEU-2 (precisione n-grammi brevi)
- ✅ Buone performance generali su metriche BLEU
- ⚠️ CIDEr più basso (0.1106)

### 🔍 **BLIP 2.7B**  
- ✅ **Migliore** in BLEU-3 e BLEU-4 (precisione n-grammi lunghi)
- ✅ Bilanciato tra BLEU e CIDEr
- ✅ Modello più grande (2.7B parametri)

### 🔍 **Ide Fix 3**
- ✅ **Migliore** in CIDEr (0.2267) - consenso umano
- ⚠️ BLEU scores più bassi
- ✅ Modello più piccolo (220M parametri)

---

## 📈 **CONFRONTO PARAMETRI**

| Modello | Parametri | Dimensione | Performance/Parametro |
|---------|-----------|------------|----------------------|
| **Ide Fix 3** | 220M | 0.82 GB | **Alta efficienza** |
| **Flores 2 base** | 610M | 2.27 GB | Buona efficienza |
| **BLIP 2.7B** | 2.7B | 10.06 GB | Bassa efficienza |

---

## 🎨 **GRAFICI GENERATI**

### ✅ **File Disponibili**:
1. **`baseline_radar_REAL_DATA.png`** - Grafico radar con tutte le metriche
2. **`baseline_heatmap_REAL_DATA.png`** - Heatmap confronto metriche  
3. **`baseline_bar_chart_REAL_DATA.png`** - Grafici a barre per metrica
4. **`baseline_parameters_pie_REAL_DATA.png`** - Distribuzione parametri
5. **`baseline_results_REAL_DATA.csv`** - Tabella dati esportabile

---

## 🔬 **NOTE METODOLOGICHE**

### 📋 **Dataset**
- **Dimensione**: 355 esempi selezionati
- **Criteri**: Esempi più brevi e rappresentativi
- **Valutazione**: 50 esempi per modello (per velocità)

### 📊 **Metriche**
- **BLEU-1/2/3/4**: Precisione n-grammi (overlap lessicale)
- **METEOR**: Allineamento semantico (attualmente 0.0000 - da verificare)
- **CIDEr**: Consenso umano (metric più affidabile)
- **CLIP Score**: Allineamento visione-linguaggio (da calcolare)

### ⚠️ **Limitazioni**
- METEOR score = 0.0000 (possibile errore di calcolo)
- CLIP score non calcolato correttamente
- Valutazione su subset ridotto (50 esempi)

---

## 🚀 **CONCLUSIONI**

### 🎯 **Raccomandazioni**:

1. **Per BLEU scores**: **Flores 2 base** (migliore overlap lessicale)
2. **Per consenso umano**: **Ide Fix 3** (migliore CIDEr)  
3. **Per bilanciamento**: **BLIP 2.7B** (buone performance generali)

### 📈 **Prossimi Passi**:
- ✅ Correggere calcolo METEOR score
- ✅ Implementare CLIP score corretto
- ✅ Valutare su dataset completo (355 esempi)
- ✅ Confrontare con modelli fine-tuned

---

## 📁 **File di Output**

Tutti i risultati sono salvati in:
```
experiments/xml_direct_input/outputs/
├── baseline_radar_REAL_DATA.png
├── baseline_heatmap_REAL_DATA.png  
├── baseline_bar_chart_REAL_DATA.png
├── baseline_parameters_pie_REAL_DATA.png
└── baseline_results_REAL_DATA.csv
```

**Dati sorgente**: `experiments/baseline_metrics_355_dataset/all_baseline_metrics_355_dataset.json`
