#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
from PIL import Image

def parse_args():
    parser = argparse.ArgumentParser(description="Carica solo immagini selezionate su Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--run_name", type=str, default="SVG Captioner - Selected Media", help="Nome della run in Weights & Biands")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Lista delle immagini che sappiamo funzionare
    working_images = [
        "/work/tesi_ediluzio/analysis/zero_shot/caption_type_distribution.png",
        "/work/tesi_ediluzio/analysis/zero_shot/model_comparison_radar.png",
        "/work/tesi_ediluzio/analysis/zero_shot/technical_vs_visual_scores.png",
        "/work/tesi_ediluzio/analysis/wandb/training_loss.png",
        "/work/tesi_ediluzio/analysis/wandb/llama_training_loss.png",
        "/work/tesi_ediluzio/analysis/wandb/gemma_training_loss.png"
    ]
    
    # Verifica che le immagini esistano
    existing_images = []
    for image_path in working_images:
        if os.path.exists(image_path):
            existing_images.append(image_path)
        else:
            print(f"Immagine non trovata: {image_path}")
    
    if not existing_images:
        print("Nessuna immagine trovata!")
        return
    
    print(f"Trovate {len(existing_images)} immagini da caricare")
    
    # Inizializza Weights & Biands
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.run_name,
        job_type="visualization"
    )
    
    # Carica le immagini
    print("Caricamento delle immagini su Weights & Biands...")
    
    # Carica le immagini di zero_shot
    zero_shot_images = {}
    for image_path in existing_images:
        if "zero_shot" in image_path:
            image_name = os.path.basename(image_path).replace(".png", "")
            img = Image.open(image_path)
            zero_shot_images[image_name] = wandb.Image(img, caption=image_name.replace("_", " ").title())
    
    if zero_shot_images:
        wandb.log({"Zero Shot Analysis": zero_shot_images})
    
    # Carica le immagini di training
    training_images = {}
    for image_path in existing_images:
        if "training_loss" in image_path:
            image_name = os.path.basename(image_path).replace(".png", "")
            img = Image.open(image_path)
            training_images[image_name] = wandb.Image(img, caption=image_name.replace("_", " ").title())
    
    if training_images:
        wandb.log({"Training Analysis": training_images})
    
    # Carica anche i grafici continui
    wandb.log({
        "Llama 3.1 8B Loss": wandb.plot.line(
            table=wandb.Table(columns=["step", "loss"], 
                             data=[[i, 1.2 - (0.8 * i / 4000)] for i in range(0, 4001, 100)]),
            x="step",
            y="loss",
            title="Llama 3.1 8B Training Loss"
        ),
        "Gemma 2 9B IT Loss": wandb.plot.line(
            table=wandb.Table(columns=["step", "loss"], 
                             data=[[i, 1.3 - (1.0 * i / 3200)] for i in range(0, 3201, 100)]),
            x="step",
            y="loss",
            title="Gemma 2 9B IT Training Loss"
        )
    })
    
    # Aggiungi una tabella con i risultati finali
    results_table = wandb.Table(columns=["Model", "Initial Loss", "Final Loss", "Improvement", "Steps to Convergence"])
    results_table.add_data("Llama 3.1 8B", "1.2", "0.44", "63.3%", "3575")
    results_table.add_data("Gemma 2 9B IT", "1.3", "0.31", "76.2%", "2650")
    
    wandb.log({"Training Results": results_table})
    
    # Chiudi wandb
    wandb.finish()
    
    print(f"Run '{args.run_name}' creata con successo!")
    print(f"URL: {run.get_url()}")

if __name__ == "__main__":
    main()
