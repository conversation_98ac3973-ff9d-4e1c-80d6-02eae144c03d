#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
from collections import Counter

def parse_args():
    parser = argparse.ArgumentParser(description="Analizza il dataset SVG e lo divide in fasce")
    parser.add_argument("--data_file", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json", 
                        help="File JSON con i dati")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_analysis",
                        help="Directory di output per l'analisi")
    parser.add_argument("--num_bins", type=int, default=5, 
                        help="Numero di fasce in cui dividere il dataset")
    return parser.parse_args()

def analyze_svg_complexity(svg_text):
    """Analizza la complessità di un SVG basandosi su vari fattori"""
    # Numero di path
    num_paths = svg_text.count("<path")
    
    # Numero di comandi SVG (M, L, C, A, ecc.)
    svg_commands = ["M", "L", "H", "V", "C", "S", "Q", "T", "A", "Z"]
    command_count = sum(svg_text.count(f" {cmd}") for cmd in svg_commands)
    
    # Lunghezza totale
    length = len(svg_text)
    
    # Calcola un punteggio di complessità
    complexity_score = (num_paths * 10) + (command_count * 2) + (length * 0.01)
    
    return {
        "length": length,
        "num_paths": num_paths,
        "command_count": command_count,
        "complexity_score": complexity_score
    }

def main():
    args = parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica i dati
    print(f"Caricamento dei dati da {args.data_file}")
    with open(args.data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Numero totale di esempi: {len(data)}")
    
    # Analizza la complessità di ogni SVG
    print("Analisi della complessità degli SVG...")
    complexity_data = []
    
    for i, item in enumerate(data):
        svg_text = item["xml"]
        complexity = analyze_svg_complexity(svg_text)
        complexity_data.append({
            "id": i,
            "svg": svg_text,
            "caption": item["caption"],
            **complexity
        })
    
    # Calcola statistiche
    lengths = [item["length"] for item in complexity_data]
    num_paths = [item["num_paths"] for item in complexity_data]
    command_counts = [item["command_count"] for item in complexity_data]
    complexity_scores = [item["complexity_score"] for item in complexity_data]
    
    print(f"Lunghezza SVG - Min: {min(lengths)}, Max: {max(lengths)}, Media: {np.mean(lengths):.2f}, Mediana: {np.median(lengths):.2f}")
    print(f"Numero di path - Min: {min(num_paths)}, Max: {max(num_paths)}, Media: {np.mean(num_paths):.2f}, Mediana: {np.median(num_paths):.2f}")
    print(f"Numero di comandi - Min: {min(command_counts)}, Max: {max(command_counts)}, Media: {np.mean(command_counts):.2f}, Mediana: {np.median(command_counts):.2f}")
    print(f"Punteggio di complessità - Min: {min(complexity_scores):.2f}, Max: {max(complexity_scores):.2f}, Media: {np.mean(complexity_scores):.2f}, Mediana: {np.median(complexity_scores):.2f}")
    
    # Crea istogrammi
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.hist(lengths, bins=20)
    plt.title("Distribuzione delle lunghezze SVG")
    plt.xlabel("Lunghezza (caratteri)")
    plt.ylabel("Frequenza")
    
    plt.subplot(2, 2, 2)
    plt.hist(num_paths, bins=20)
    plt.title("Distribuzione del numero di path")
    plt.xlabel("Numero di path")
    plt.ylabel("Frequenza")
    
    plt.subplot(2, 2, 3)
    plt.hist(command_counts, bins=20)
    plt.title("Distribuzione del numero di comandi SVG")
    plt.xlabel("Numero di comandi")
    plt.ylabel("Frequenza")
    
    plt.subplot(2, 2, 4)
    plt.hist(complexity_scores, bins=20)
    plt.title("Distribuzione dei punteggi di complessità")
    plt.xlabel("Punteggio di complessità")
    plt.ylabel("Frequenza")
    
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, "svg_complexity_histograms.png"))
    print(f"Istogrammi salvati in {os.path.join(args.output_dir, 'svg_complexity_histograms.png')}")
    
    # Dividi il dataset in fasce basate sulla complessità
    print(f"Divisione del dataset in {args.num_bins} fasce...")
    
    # Ordina per punteggio di complessità
    sorted_data = sorted(complexity_data, key=lambda x: x["complexity_score"])
    
    # Calcola i limiti delle fasce
    bin_size = len(sorted_data) // args.num_bins
    bins = []
    
    for i in range(args.num_bins):
        start_idx = i * bin_size
        end_idx = (i + 1) * bin_size if i < args.num_bins - 1 else len(sorted_data)
        bin_data = sorted_data[start_idx:end_idx]
        
        min_complexity = bin_data[0]["complexity_score"]
        max_complexity = bin_data[-1]["complexity_score"]
        
        bins.append({
            "bin_id": i,
            "min_complexity": min_complexity,
            "max_complexity": max_complexity,
            "num_examples": len(bin_data),
            "examples": bin_data
        })
        
        print(f"Fascia {i+1}: Complessità [{min_complexity:.2f} - {max_complexity:.2f}], Esempi: {len(bin_data)}")
    
    # Salva le fasce
    for i, bin_data in enumerate(bins):
        output_file = os.path.join(args.output_dir, f"bin_{i+1}.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                "bin_id": bin_data["bin_id"],
                "min_complexity": bin_data["min_complexity"],
                "max_complexity": bin_data["max_complexity"],
                "num_examples": bin_data["num_examples"],
                "examples": bin_data["examples"]
            }, f, indent=2)
        print(f"Fascia {i+1} salvata in {output_file}")
    
    # Crea un file di metadati con le informazioni sulle fasce
    metadata = {
        "num_bins": args.num_bins,
        "total_examples": len(data),
        "bins": [{
            "bin_id": bin_data["bin_id"],
            "min_complexity": bin_data["min_complexity"],
            "max_complexity": bin_data["max_complexity"],
            "num_examples": bin_data["num_examples"]
        } for bin_data in bins]
    }
    
    metadata_file = os.path.join(args.output_dir, "bins_metadata.json")
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, indent=2)
    print(f"Metadati delle fasce salvati in {metadata_file}")
    
    # Crea un grafico a torta con la distribuzione degli esempi nelle fasce
    plt.figure(figsize=(10, 8))
    plt.pie([bin_data["num_examples"] for bin_data in bins], 
            labels=[f"Fascia {i+1}" for i in range(args.num_bins)],
            autopct='%1.1f%%')
    plt.title("Distribuzione degli esempi nelle fasce")
    plt.savefig(os.path.join(args.output_dir, "bins_distribution.png"))
    print(f"Grafico a torta salvato in {os.path.join(args.output_dir, 'bins_distribution.png')}")
    
    # Crea un grafico a barre con le caratteristiche medie per fascia
    plt.figure(figsize=(12, 8))
    
    bin_ids = [i+1 for i in range(args.num_bins)]
    
    avg_lengths = [np.mean([ex["length"] for ex in bin_data["examples"]]) for bin_data in bins]
    avg_paths = [np.mean([ex["num_paths"] for ex in bin_data["examples"]]) for bin_data in bins]
    avg_commands = [np.mean([ex["command_count"] for ex in bin_data["examples"]]) for bin_data in bins]
    
    plt.subplot(3, 1, 1)
    plt.bar(bin_ids, avg_lengths)
    plt.title("Lunghezza media SVG per fascia")
    plt.xlabel("Fascia")
    plt.ylabel("Lunghezza media")
    
    plt.subplot(3, 1, 2)
    plt.bar(bin_ids, avg_paths)
    plt.title("Numero medio di path per fascia")
    plt.xlabel("Fascia")
    plt.ylabel("Numero medio di path")
    
    plt.subplot(3, 1, 3)
    plt.bar(bin_ids, avg_commands)
    plt.title("Numero medio di comandi per fascia")
    plt.xlabel("Fascia")
    plt.ylabel("Numero medio di comandi")
    
    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, "bins_characteristics.png"))
    print(f"Grafico a barre salvato in {os.path.join(args.output_dir, 'bins_characteristics.png')}")
    
    print("Analisi completata!")

if __name__ == "__main__":
    main()
