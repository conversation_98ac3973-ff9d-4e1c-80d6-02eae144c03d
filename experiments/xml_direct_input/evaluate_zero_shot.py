#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per valutare i modelli base (zero-shot) sulla generazione di didascalie SVG.
Calcola metriche come BLEU, METEOR, CIDEr, CLIP Score, ecc.
"""

import os
import sys
import json
import argparse
import logging
import time
import re
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import torch
from tqdm import tqdm
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
import nltk
import ssl
import clip
from PIL import Image
import cairosvg
import io
import wandb
from pycocoevalcap.cider.cider import Cider
from pycocoevalcap.meteor.meteor import Meteor
from pycocoevalcap.bleu.bleu import Bleu
from pycocoevalcap.tokenizer.ptbtokenizer import PTBTokenizer

# Configura SSL per NLTK
try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context

# Assicurati che NLTK abbia i dati necessari
nltk.download('punkt')
nltk.download('wordnet')
nltk.download('omw-1.4')
nltk.download('stopwords')

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Valutazione zero-shot dei modelli base")
    parser.add_argument("--model_name_or_path", type=str, required=True, help="Nome o path del modello base")
    parser.add_argument("--test_file", type=str, required=True, help="File JSON con i dati di test")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i risultati")
    parser.add_argument("--load_in_8bit", action="store_true", help="Carica il modello in 8-bit")
    parser.add_argument("--load_in_4bit", action="store_true", help="Carica il modello in 4-bit")
    parser.add_argument("--num_samples", type=int, default=100, help="Numero di campioni da valutare")
    parser.add_argument("--max_length", type=int, default=512, help="Lunghezza massima della sequenza")
    parser.add_argument("--use_clip", action="store_true", help="Calcola anche il CLIP score")
    parser.add_argument("--clip_model", type=str, default="ViT-B/32", help="Modello CLIP da utilizzare")
    parser.add_argument("--render_size", type=int, default=224, help="Dimensione per il rendering SVG")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner_zero_shot", help="Nome del progetto Weights & Biands")
    parser.add_argument("--seed", type=int, default=42, help="Seed per la riproducibilità")
    return parser.parse_args()

def load_test_data(test_file: str, num_samples: int = 100) -> List[Dict[str, Any]]:
    """Carica i dati di test da un file JSON."""
    with open(test_file, "r") as f:
        data = json.load(f)

    # Limita il numero di campioni se necessario
    if num_samples > 0 and num_samples < len(data):
        import random
        random.seed(42)  # Per riproducibilità
        data = random.sample(data, num_samples)

    return data

def categorize_complexity(svg_data: str) -> str:
    """Categorizza la complessità di un SVG in base al numero di elementi."""
    # Conta il numero di elementi SVG
    element_count = len(re.findall(r'<(path|rect|circle|ellipse|line|polyline|polygon)', svg_data))
    
    if element_count <= 5:
        return "simple"
    elif element_count <= 15:
        return "medium"
    else:
        return "complex"

def load_model_and_tokenizer(
    model_name_or_path: str,
    load_in_8bit: bool = False,
    load_in_4bit: bool = False
) -> Tuple[AutoModelForCausalLM, AutoTokenizer]:
    """Carica il modello e il tokenizer."""
    # Configura la quantizzazione
    quantization_config = None
    if load_in_8bit:
        logger.info("Utilizzo della quantizzazione a 8-bit")
        quantization_config = BitsAndBytesConfig(load_in_8bit=True)
    elif load_in_4bit:
        logger.info("Utilizzo della quantizzazione a 4-bit")
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.bfloat16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )

    # Carica il tokenizer
    logger.info(f"Caricamento del tokenizer: {model_name_or_path}")
    tokenizer = AutoTokenizer.from_pretrained(model_name_or_path)

    # Assicurati che il tokenizer abbia un token di padding
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Carica il modello base
    logger.info(f"Caricamento del modello base: {model_name_or_path}")
    model = AutoModelForCausalLM.from_pretrained(
        model_name_or_path,
        quantization_config=quantization_config,
        device_map="auto",
        torch_dtype=torch.bfloat16
    )

    return model, tokenizer

def generate_caption(model, tokenizer, svg: str, max_length: int = 512) -> str:
    """Genera una didascalia per un SVG in modalità zero-shot."""
    # Crea il prompt nel formato corretto per il modello
    prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg}\n\nDescrizione:"

    # Tokenizza l'input
    inputs = tokenizer(prompt, return_tensors="pt", max_length=max_length, truncation=True)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}

    # Genera la didascalia
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=150,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.pad_token_id
        )

    # Decodifica l'output
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # Estrai solo la didascalia generata (dopo "Descrizione:")
    caption = generated_text.split("Descrizione:")[1].strip() if "Descrizione:" in generated_text else generated_text

    return caption

def render_svg_to_image(svg_string: str, size: int = 224) -> Image.Image:
    """Renderizza una stringa SVG in un'immagine PIL."""
    try:
        # Renderizza SVG in PNG
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'), output_width=size, output_height=size)

        # Converti i dati PNG in un'immagine PIL
        image = Image.open(io.BytesIO(png_data))
        return image
    except Exception as e:
        logger.error(f"Errore nel rendering SVG: {e}")
        # Restituisci un'immagine nera in caso di errore
        return Image.new('RGB', (size, size), color='black')

def calculate_clip_score(svgs: List[str], captions: List[str], model_name: str = "ViT-B/32", render_size: int = 224) -> List[float]:
    """Calcola il CLIP score per ogni coppia (SVG, didascalia)."""
    # Carica il modello CLIP
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model, preprocess = clip.load(model_name, device=device)

    scores = []

    for svg, caption in tqdm(zip(svgs, captions), total=len(svgs), desc="Calcolo CLIP score"):
        try:
            # Renderizza SVG in immagine
            image = render_svg_to_image(svg, render_size)

            # Preprocess dell'immagine
            image_input = preprocess(image).unsqueeze(0).to(device)

            # Tokenizza la didascalia
            text_input = clip.tokenize([caption]).to(device)

            # Calcola le features
            with torch.no_grad():
                image_features = model.encode_image(image_input)
                text_features = model.encode_text(text_input)

            # Normalizza le features
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)

            # Calcola la similarità coseno
            similarity = (100.0 * image_features @ text_features.T).item()
            scores.append(similarity)
        except Exception as e:
            logger.error(f"Errore nel calcolo del CLIP score: {e}")
            scores.append(0.0)

    return scores

def calculate_caption_stats(hypotheses: List[str]) -> Dict[str, float]:
    """Calcola statistiche sulle didascalie generate."""
    # Lunghezza delle didascalie (in token)
    lengths = [len(nltk.word_tokenize(hyp)) for hyp in hypotheses]

    # Diversità del vocabolario
    all_tokens = []
    for hyp in hypotheses:
        all_tokens.extend(nltk.word_tokenize(hyp.lower()))

    unique_tokens = len(set(all_tokens))
    total_tokens = len(all_tokens)

    # Type-Token Ratio (misura di diversità lessicale)
    ttr = unique_tokens / total_tokens if total_tokens > 0 else 0

    # Self-BLEU (misura di diversità tra le didascalie)
    self_bleu = 0
    if len(hypotheses) > 1:
        from nltk.translate.bleu_score import corpus_bleu, SmoothingFunction
        self_bleu_scores = []
        for i, hyp in enumerate(hypotheses):
            other_hyps = [h for j, h in enumerate(hypotheses) if j != i]
            if other_hyps:
                # Prendi fino a 10 altre didascalie per efficienza
                other_hyps = other_hyps[:10]
                hyp_tokens = nltk.word_tokenize(hyp.lower())
                other_hyps_tokens = [nltk.word_tokenize(h.lower()) for h in other_hyps]

                # Calcola BLEU-4 tra questa didascalia e le altre
                smoothing = SmoothingFunction().method1
                bleu = corpus_bleu([other_hyps_tokens], [hyp_tokens],
                                   weights=(0.25, 0.25, 0.25, 0.25),
                                   smoothing_function=smoothing)
                self_bleu_scores.append(bleu)

        if self_bleu_scores:
            self_bleu = np.mean(self_bleu_scores)

    return {
        'caption_length_mean': np.mean(lengths),
        'caption_length_std': np.std(lengths),
        'caption_length_min': np.min(lengths),
        'caption_length_max': np.max(lengths),
        'vocabulary_size': unique_tokens,
        'type_token_ratio': ttr,
        'self_bleu': self_bleu
    }

def calculate_perplexity(model, tokenizer, svgs: List[str], references: List[str], max_length: int = 512) -> List[float]:
    """Calcola la perplexity per ogni coppia (SVG, didascalia)."""
    device = model.device
    perplexities = []

    for svg, ref in tqdm(zip(svgs, references), total=len(svgs), desc="Calcolo perplexity"):
        # Crea il prompt nel formato corretto per il modello
        prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg}\n\nDescrizione: {ref}"

        # Tokenizza l'input
        inputs = tokenizer(prompt, return_tensors="pt", max_length=max_length, truncation=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}

        # Imposta gli input_ids come labels per calcolare la loss
        inputs["labels"] = inputs["input_ids"].clone()

        # Calcola la loss
        with torch.no_grad():
            outputs = model(**inputs)
            loss = outputs.loss.item()

        # Converti la loss in perplexity
        perplexity = np.exp(loss)
        perplexities.append(perplexity)

    return perplexities

def evaluate_with_coco_metrics(references: List[str], hypotheses: List[str]) -> Dict[str, float]:
    """Valuta le didascalie con le metriche COCO (BLEU, METEOR, CIDEr)."""
    # Prepara i dati nel formato richiesto dalle metriche COCO
    gts = {}
    res = {}

    for i, (ref, hyp) in enumerate(zip(references, hypotheses)):
        gts[i] = [{'caption': ref}]
        res[i] = [{'caption': hyp}]

    # Tokenizza le didascalie
    tokenizer = PTBTokenizer()
    gts = tokenizer.tokenize(gts)
    res = tokenizer.tokenize(res)

    # Calcola BLEU
    scorer = Bleu(n=4)
    bleu_score, bleu_scores = scorer.compute_score(gts, res)

    # Calcola METEOR
    scorer = Meteor()
    meteor_score, meteor_scores = scorer.compute_score(gts, res)

    # Calcola CIDEr
    scorer = Cider()
    cider_score, cider_scores = scorer.compute_score(gts, res)

    # Restituisci le metriche
    return {
        'bleu1': bleu_score[0],
        'bleu2': bleu_score[1],
        'bleu3': bleu_score[2],
        'bleu4': bleu_score[3],
        'meteor': meteor_score,
        'cider': cider_score
    }

def evaluate_zero_shot(
    model_name_or_path: str,
    test_data: List[Dict[str, Any]],
    output_dir: str,
    load_in_8bit: bool = False,
    load_in_4bit: bool = False,
    max_length: int = 512,
    use_clip: bool = False,
    clip_model: str = "ViT-B/32",
    render_size: int = 224,
    wandb_entity: str = "337543-unimore",
    wandb_project: str = "captioner_zero_shot"
) -> Dict[str, Any]:
    """Valuta un modello base in modalità zero-shot."""
    # Crea la directory di output
    os.makedirs(output_dir, exist_ok=True)
    
    # Estrai il nome del modello per il nome della run di wandb
    model_name = os.path.basename(model_name_or_path)
    wandb_run_name = f"{model_name}_zero_shot"
    
    # Inizializza wandb
    wandb.init(
        entity=wandb_entity,
        project=wandb_project,
        name=wandb_run_name,
        config={
            "model_name": model_name_or_path,
            "num_samples": len(test_data),
            "max_length": max_length,
            "use_clip": use_clip,
            "clip_model": clip_model if use_clip else None,
            "render_size": render_size if use_clip else None,
            "load_in_8bit": load_in_8bit,
            "load_in_4bit": load_in_4bit
        }
    )
    
    # Carica il modello e il tokenizer
    model, tokenizer = load_model_and_tokenizer(
        model_name_or_path,
        load_in_8bit,
        load_in_4bit
    )
    
    # Estrai gli SVG e le caption di riferimento
    svgs = [item["xml"] for item in test_data]
    references = [item["caption"] for item in test_data]
    
    # Misura il tempo di inferenza
    logger.info(f"Generazione delle caption in modalità zero-shot per {model_name_or_path}")
    generated_captions = []
    inference_times = []
    
    for svg in tqdm(svgs, desc=f"Generazione caption"):
        start_time = time.time()
        caption = generate_caption(model, tokenizer, svg, max_length)
        end_time = time.time()
        
        inference_time = end_time - start_time
        generated_captions.append(caption)
        inference_times.append(inference_time)
    
    # Salva i risultati dell'inferenza
    results = []
    for i, (svg, ref, gen, inf_time) in enumerate(zip(svgs, references, generated_captions, inference_times)):
        complexity = categorize_complexity(svg)
        results.append({
            "id": i,
            "complexity": complexity,
            "svg": svg,
            "true_caption": ref,
            "generated_caption": gen,
            "inference_time": inf_time
        })
    
    results_file = os.path.join(output_dir, f"{model_name}_zero_shot_examples.json")
    with open(results_file, "w") as f:
        json.dump(results, f, indent=2)
    
    # Valuta le caption con le metriche COCO
    logger.info(f"Valutazione delle caption con le metriche COCO")
    coco_metrics = evaluate_with_coco_metrics(references, generated_captions)
    
    # Calcola statistiche sulle didascalie
    logger.info(f"Calcolo delle statistiche sulle didascalie")
    caption_stats = calculate_caption_stats(generated_captions)
    
    # Calcola la perplexity
    logger.info(f"Calcolo della perplexity")
    perplexities = calculate_perplexity(model, tokenizer, svgs, references, max_length)
    
    # Calcola il CLIP score se richiesto
    clip_scores = None
    if use_clip:
        logger.info(f"Calcolo del CLIP score")
        clip_scores = calculate_clip_score(svgs, generated_captions, clip_model, render_size)
        coco_metrics["clip_score"] = np.mean(clip_scores)
    
    # Statistiche sul tempo di inferenza
    inference_time_stats = {
        "inference_time_mean": np.mean(inference_times),
        "inference_time_std": np.std(inference_times),
        "inference_time_min": np.min(inference_times),
        "inference_time_max": np.max(inference_times),
        "inference_time_total": np.sum(inference_times)
    }
    
    # Statistiche sulla perplexity
    perplexity_stats = {
        "perplexity_mean": np.mean(perplexities),
        "perplexity_std": np.std(perplexities),
        "perplexity_min": np.min(perplexities),
        "perplexity_max": np.max(perplexities)
    }
    
    # Combina tutte le metriche
    metrics = {
        **coco_metrics,
        **caption_stats,
        **inference_time_stats,
        **perplexity_stats,
        "num_samples": len(test_data)
    }
    
    # Salva le metriche
    metrics_file = os.path.join(output_dir, f"{model_name}_zero_shot_metrics.json")
    with open(metrics_file, "w") as f:
        json.dump(metrics, f, indent=2)
    
    # Log delle metriche su wandb
    wandb.log(metrics)
    
    # Chiudi wandb
    wandb.finish()
    
    logger.info(f"Valutazione zero-shot completata per {model_name_or_path}")
    logger.info(f"Risultati salvati in: {output_dir}")
    
    return metrics

def main():
    args = parse_args()
    
    # Imposta il seed per la riproducibilità
    torch.manual_seed(args.seed)
    np.random.seed(args.seed)
    
    # Carica i dati di test
    test_data = load_test_data(args.test_file, args.num_samples)
    
    # Valuta il modello in modalità zero-shot
    metrics = evaluate_zero_shot(
        model_name_or_path=args.model_name_or_path,
        test_data=test_data,
        output_dir=args.output_dir,
        load_in_8bit=args.load_in_8bit,
        load_in_4bit=args.load_in_4bit,
        max_length=args.max_length,
        use_clip=args.use_clip,
        clip_model=args.clip_model,
        render_size=args.render_size,
        wandb_entity=args.wandb_entity,
        wandb_project=args.wandb_project
    )
    
    # Stampa le metriche principali
    print("\nRisultati della valutazione zero-shot:")
    print(f"BLEU-1: {metrics['bleu1']:.4f}")
    print(f"BLEU-4: {metrics['bleu4']:.4f}")
    print(f"METEOR: {metrics['meteor']:.4f}")
    print(f"CIDEr: {metrics['cider']:.4f}")
    if "clip_score" in metrics:
        print(f"CLIP Score: {metrics['clip_score']:.4f}")
    print(f"Tempo medio di inferenza: {metrics['inference_time_mean']:.4f}s")
    print(f"Perplexity media: {metrics['perplexity_mean']:.4f}")

if __name__ == "__main__":
    main()
