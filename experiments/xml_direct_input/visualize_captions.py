#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per visualizzare e confrontare le didascalie generate dai diversi modelli.
"""

import argparse
import json
import os
import random
import pandas as pd
from tabulate import tabulate

def parse_args():
    parser = argparse.ArgumentParser(description="Visualizza e confronta le didascalie generate")
    parser.add_argument("--results_files", type=str, nargs="+", required=True, help="File JSONL con le didascalie generate")
    parser.add_argument("--model_names", type=str, nargs="+", required=True, help="Nomi dei modelli")
    parser.add_argument("--output_file", type=str, required=True, help="File di output con il confronto")
    parser.add_argument("--num_samples", type=int, default=10, help="Numero di campioni da visualizzare")
    parser.add_argument("--random_seed", type=int, default=42, help="Seed per la selezione casuale")
    return parser.parse_args()

def load_results(results_file):
    """Carica i risultati dal file JSONL."""
    results = []
    with open(results_file, 'r', encoding='utf-8') as f:
        for line in f:
            results.append(json.loads(line))
    
    return results

def select_samples(results_list, model_names, num_samples, random_seed):
    """Seleziona campioni casuali per il confronto."""
    # Verifica che tutti i file di risultati abbiano gli stessi ID
    id_sets = [set(r["id"] for r in results) for results in results_list]
    common_ids = set.intersection(*id_sets)
    
    if len(common_ids) < num_samples:
        print(f"Attenzione: solo {len(common_ids)} campioni comuni trovati")
        num_samples = len(common_ids)
    
    # Seleziona campioni casuali
    random.seed(random_seed)
    selected_ids = random.sample(list(common_ids), num_samples)
    
    # Crea un dizionario di risultati per ID
    results_by_id = {}
    for model_idx, results in enumerate(results_list):
        results_dict = {r["id"]: r for r in results}
        for sample_id in selected_ids:
            if sample_id not in results_by_id:
                results_by_id[sample_id] = {}
            
            results_by_id[sample_id][model_names[model_idx]] = results_dict[sample_id]
    
    return results_by_id

def create_comparison_table(results_by_id, model_names):
    """Crea una tabella di confronto."""
    rows = []
    
    for sample_id, model_results in results_by_id.items():
        # Aggiungi l'ID del campione
        row = [sample_id]
        
        # Aggiungi la didascalia vera
        true_caption = model_results[model_names[0]]["true_caption"]
        row.append(true_caption)
        
        # Aggiungi le didascalie generate
        for model_name in model_names:
            generated_caption = model_results[model_name]["generated_caption"]
            row.append(generated_caption)
        
        rows.append(row)
    
    # Crea un DataFrame
    columns = ["ID", "Didascalia Vera"] + [f"Generata da {model}" for model in model_names]
    df = pd.DataFrame(rows, columns=columns)
    
    return df

def main():
    args = parse_args()
    
    # Verifica che il numero di file di risultati e nomi di modelli sia lo stesso
    if len(args.results_files) != len(args.model_names):
        raise ValueError("Il numero di file di risultati e nomi di modelli deve essere lo stesso")
    
    # Carica i risultati
    results_list = []
    for results_file in args.results_files:
        print(f"Caricamento dei risultati da {results_file}...")
        results_list.append(load_results(results_file))
    
    # Seleziona campioni casuali
    print(f"Selezione di {args.num_samples} campioni casuali...")
    results_by_id = select_samples(results_list, args.model_names, args.num_samples, args.random_seed)
    
    # Crea la tabella di confronto
    print("Creazione della tabella di confronto...")
    df = create_comparison_table(results_by_id, args.model_names)
    
    # Salva la tabella come CSV
    df.to_csv(args.output_file, index=False)
    print(f"Tabella salvata in {args.output_file}")
    
    # Salva la tabella come HTML
    html_file = os.path.splitext(args.output_file)[0] + ".html"
    df.to_html(html_file, index=False)
    print(f"Tabella HTML salvata in {html_file}")
    
    # Stampa la tabella
    print("\nTabella di confronto:")
    print(tabulate(df, headers="keys", tablefmt="grid", showindex=False))

if __name__ == "__main__":
    main()
