#!/bin/bash

#SBATCH --job-name=lora_quick   # Nome del job
#SBATCH --output=/work/tesi_ediluzio/logs/lora_quick_%A_%x.out # Log: %A=Job ID, %x=Job Name
#SBATCH --error=/work/tesi_ediluzio/logs/lora_quick_%A_%x.err
#SBATCH --partition=all_usr_prod     # Usa la stessa partizione
#SBATCH --account=tesi_ediluzio      # Usa lo stesso account
#SBATCH --qos=normal                 # Usa la stessa QoS
# !!! VERIFICA QUESTO CONSTRAINT !!! Deve corrispondere a GPU FUNZIONANTI
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G" # O rimuovi/modifica se necessario
# Richiede UNA SOLA GPU. L'inferenza zero-shot di solito non ne richiede di più.
#SBATCH --gpus=1
# Tempo ridotto a 8 ore
#SBATCH --time=08:00:00              # 8 ore
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8            # CPU e Mem dovrebbero bastare
#SBATCH --mem=64G
#SBATCH --mail-type=FAIL,END         # Ricevi email se fallisce o finisce
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE
################################################################################

PROJECT_ROOT="/work/tesi_ediluzio"
PYTHON_EXEC="${PROJECT_ROOT}/svg_captioning_env/bin/python"
# Assicurati che questo sia il percorso corretto dello script Python modificato!
SCRIPT_PATH="${PROJECT_ROOT}/experiments/xml_direct_input/train_xml_slurm_token_xml.py"

# --- Verifica Ambiente ---
if [ ! -f "$PYTHON_EXEC" ]; then echo "Errore: Python non trovato: ${PYTHON_EXEC}" ; exit 1; fi
if [ ! -f "$SCRIPT_PATH" ]; then echo "Errore: Script Python non trovato: ${SCRIPT_PATH}" ; exit 1; fi
echo "Using Python executable: ${PYTHON_EXEC}"

# Crea cartella logs se non esiste
mkdir -p "${PROJECT_ROOT}/logs"

# --- Configurazione Autenticazione Hugging Face ---
echo "Configurazione autenticazione Hugging Face..."
# Imposta direttamente la variabile di ambiente HF_TOKEN
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# --- Configurazione PYTHONPATH ---
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="${PROJECT_ROOT}:${PYTHONPATH}"
echo "PYTHONPATH: ${PYTHONPATH}"

# --- Gestione Argomenti ---
CONFIG_FILE=${1} # Prende il percorso del file di configurazione dal primo argomento sbatch

if [ -z "$CONFIG_FILE" ]; then
  echo "Errore: Specificare il percorso del file di configurazione come argomento."
  echo "Esempio: sbatch $0 /path/to/config.json"
  exit 1
fi

if [ ! -f "$CONFIG_FILE" ]; then
  echo "Errore: File di configurazione non trovato: ${CONFIG_FILE}"
  exit 1
fi

echo "Avvio Training LoRA con configurazione: ${CONFIG_FILE}"
echo "Job ID: $SLURM_JOB_ID" ; echo "Nodo: $SLURMD_NODENAME" ; echo "GPU Allocata (da SLURM): $CUDA_VISIBLE_DEVICES"

# --- Comando di Esecuzione ---
echo "Eseguo lo script Python: ${SCRIPT_PATH}"

# Esegui passando gli argomenti allo script Python
${PYTHON_EXEC} ${SCRIPT_PATH} \
    --config_file "${CONFIG_FILE}"

EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
  echo "Training LoRA completato con successo con configurazione: ${CONFIG_FILE}."
else
  echo "Training LoRA fallito con codice di errore: $EXIT_CODE con configurazione: ${CONFIG_FILE}"
fi
exit $EXIT_CODE
