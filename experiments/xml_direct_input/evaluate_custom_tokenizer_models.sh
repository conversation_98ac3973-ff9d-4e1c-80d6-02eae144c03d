#!/bin/bash

# Script per valutare i modelli con tokenizer personalizzato

# Crea le directory di output
mkdir -p /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_lora_xml_custom_token
mkdir -p /work/tesi_ediluzio/evaluation/checkpoint_metrics/gemma2_9b_it_lora_xml_custom_token

# Valutazione del checkpoint finale di Llama 3.1 8B con tokenizer personalizzato
echo "Avvio valutazione di Llama 3.1 8B con tokenizer personalizzato (checkpoint 1800)..."
sbatch experiments/xml_direct_input/run_checkpoint_evaluation.slurm \
    meta-llama/Llama-3.1-8B-Instruct \
    /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token/checkpoint-1800 \
    /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_lora_xml_custom_token/checkpoint-1800 \
    "--use_custom_tokenizer" \
    "" \
    "--load_in_4bit" \
    100 \
    "--use_clip"

# Valutazione del checkpoint finale di Gemma 2 9B IT con tokenizer personalizzato
echo "Avvio valutazione di Gemma 2 9B IT con tokenizer personalizzato (checkpoint 2400)..."
sbatch experiments/xml_direct_input/run_checkpoint_evaluation.slurm \
    google/gemma-2-9b-it \
    /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_custom_token/checkpoint-2400 \
    /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    /work/tesi_ediluzio/evaluation/checkpoint_metrics/gemma2_9b_it_lora_xml_custom_token/checkpoint-2400 \
    "--use_custom_tokenizer" \
    "" \
    "--load_in_4bit" \
    100 \
    "--use_clip"

echo "Job di valutazione avviati. Controlla lo stato con 'squeue -u \$USER'"
