#!/bin/bash
#SBATCH --job-name=model_evaluation
#SBATCH --output=logs/model_evaluation_%j.out
#SBATCH --error=logs/model_evaluation_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --constraint=gpu_L40S_48G|gpu_A40_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G|gpu_2080Ti_11G
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --time=8:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
TEST_FILE="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
OUTPUT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results"
LLAMA_FINETUNED_PATH="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence"
GEMMA_FINETUNED_PATH="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence"
NUM_SAMPLES=100
WANDB_ENTITY="337543-unimore"
WANDB_PROJECT="captioner"
WANDB_RUN_NAME="model_evaluation_comparison"

# Stampa informazioni
echo "Valutazione dei modelli"
echo "File di test: $TEST_FILE"
echo "Directory di output: $OUTPUT_DIR"
echo "Modello Llama fine-tuned: $LLAMA_FINETUNED_PATH"
echo "Modello Gemma fine-tuned: $GEMMA_FINETUNED_PATH"
echo "Numero di campioni: $NUM_SAMPLES"
echo "Weights & Biands Entity: $WANDB_ENTITY"
echo "Weights & Biands Project: $WANDB_PROJECT"
echo "Weights & Biands Run Name: $WANDB_RUN_NAME"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocate (da SLURM): $SLURM_GPUS_ON_NODE"

# Crea la directory di output
mkdir -p "$OUTPUT_DIR"

# Installa le dipendenze necessarie
pip install pycocoevalcap
pip install git+https://github.com/openai/CLIP.git

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/generate_and_evaluate_models.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/generate_and_evaluate_models.py \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --llama_finetuned_path "$LLAMA_FINETUNED_PATH" \
    --gemma_finetuned_path "$GEMMA_FINETUNED_PATH" \
    --num_samples "$NUM_SAMPLES" \
    --use_clip \
    --use_wandb \
    --wandb_entity "$WANDB_ENTITY" \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_run_name "$WANDB_RUN_NAME"

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Valutazione dei modelli completata con successo"
else
    echo "Valutazione dei modelli fallita con codice di errore: $?"
fi
