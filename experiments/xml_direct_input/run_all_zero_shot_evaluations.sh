#!/bin/bash

# Script per avviare la valutazione zero-shot di tutti i modelli base

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri comuni
TEST_FILE="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
OUTPUT_DIR="/work/tesi_ediluzio/evaluation/zero_shot"
LOAD_IN_4BIT="--load_in_4bit"
NUM_SAMPLES=100
USE_CLIP="--use_clip"
WANDB_ENTITY="337543-unimore"
WANDB_PROJECT="captioner_zero_shot"

# Crea la directory di output
mkdir -p "$OUTPUT_DIR"

# Definisci i modelli da valutare
declare -a MODELS=(
    "meta-llama/Llama-3.1-8B-Instruct"
    "google/gemma-2-9b-it"
)

# Avvia la valutazione per ogni modello
for MODEL_NAME in "${MODELS[@]}"; do
    echo "Avvio valutazione zero-shot per: $MODEL_NAME"
    
    # Avvia il job SLURM
    sbatch /work/tesi_ediluzio/experiments/xml_direct_input/run_evaluate_zero_shot.slurm \
        "$MODEL_NAME" \
        "$TEST_FILE" \
        "$OUTPUT_DIR" \
        "" \
        "$LOAD_IN_4BIT" \
        "$NUM_SAMPLES" \
        "$USE_CLIP" \
        "$WANDB_ENTITY" \
        "$WANDB_PROJECT"
    
    echo "Job avviato per $MODEL_NAME"
done

echo "Tutti i job di valutazione zero-shot sono stati avviati."
echo "Puoi monitorare lo stato dei job con 'squeue -u \$USER'"

# Attendi che tutti i job siano completati
echo "Attendo il completamento di tutti i job..."
echo "Questo script continuerà a monitorare lo stato dei job ogni 60 secondi."
echo "Premi Ctrl+C per interrompere il monitoraggio (i job continueranno in background)."

while true; do
    # Controlla se ci sono job in esecuzione con il nome eval_zero_shot
    NUM_JOBS=$(squeue -u $USER -n eval_zero_shot -h | wc -l)
    
    if [ "$NUM_JOBS" -eq 0 ]; then
        echo "Tutti i job di valutazione zero-shot sono completati."
        break
    else
        echo "$(date): $NUM_JOBS job di valutazione zero-shot ancora in esecuzione..."
        sleep 60
    fi
done

# Combina i risultati in un unico file
echo "Combinazione dei risultati zero-shot..."
$PYTHON_ENV -c "
import json
import glob
import os

# Carica tutti i file di metriche zero-shot
metrics_files = glob.glob('$OUTPUT_DIR/*_zero_shot_metrics.json')
all_metrics = {}

for file in metrics_files:
    model_key = os.path.basename(file).split('_zero_shot')[0]
    with open(file, 'r') as f:
        metrics = json.load(f)
    all_metrics[model_key] = metrics

# Salva il file combinato
with open('$OUTPUT_DIR/zero_shot_metrics.json', 'w') as f:
    json.dump(all_metrics, f, indent=2)

# Carica tutti i file di esempi zero-shot
examples_files = glob.glob('$OUTPUT_DIR/*_zero_shot_examples.json')
all_examples = []

for file in examples_files:
    model_key = os.path.basename(file).split('_zero_shot')[0]
    with open(file, 'r') as f:
        examples = json.load(f)
        for example in examples:
            example['model'] = model_key
        all_examples.extend(examples)

# Salva il file combinato
with open('$OUTPUT_DIR/zero_shot_examples.json', 'w') as f:
    json.dump(all_examples, f, indent=2)

print('Risultati zero-shot combinati con successo.')
"

echo "Valutazione zero-shot completata."
echo "Risultati disponibili in: $OUTPUT_DIR"
echo "Metriche combinate: $OUTPUT_DIR/zero_shot_metrics.json"
echo "Esempi combinati: $OUTPUT_DIR/zero_shot_examples.json"
