#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description="Crea una dashboard di Weights & Biands con visualizzazione corretta")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--dashboard_name", type=str, default="SVG Captioner - Training Progress (Fixed)", help="Nome della dashboard da creare")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza Weights & Biands
    wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.dashboard_name,
        job_type="visualization"
    )
    
    # Crea un report
    report = wandb.Report(
        title=args.dashboard_name,
        description="Dashboard per monitorare il training dei modelli"
    )
    
    # Aggiungi un pannello per la loss di training
    report.add_block(
        wandb.LinePlot(
            title="Training Loss",
            x="_step",
            y="train_loss",
            smoothing=0.0,  # Nessuno smoothing
            ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
            legend={"position": "bottom"}
        )
    )
    
    # Aggiungi un pannello per la loss
    report.add_block(
        wandb.LinePlot(
            title="Loss",
            x="_step",
            y="loss",
            smoothing=0.0,  # Nessuno smoothing
            ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
            legend={"position": "bottom"}
        )
    )
    
    # Aggiungi un pannello per la best train loss
    report.add_block(
        wandb.LinePlot(
            title="Best Train Loss",
            x="_step",
            y="best_train_loss",
            smoothing=0.0,  # Nessuno smoothing
            ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
            legend={"position": "bottom"}
        )
    )
    
    # Aggiungi un pannello per il learning rate
    report.add_block(
        wandb.LinePlot(
            title="Learning Rate",
            x="_step",
            y="learning_rate",
            smoothing=0.0,  # Nessuno smoothing
            legend={"position": "bottom"}
        )
    )
    
    # Aggiungi un pannello per il grad norm
    report.add_block(
        wandb.LinePlot(
            title="Gradient Norm",
            x="_step",
            y="grad_norm",
            smoothing=0.0,  # Nessuno smoothing
            legend={"position": "bottom"}
        )
    )
    
    # Aggiungi un pannello per l'epoca
    report.add_block(
        wandb.LinePlot(
            title="Epoch",
            x="_step",
            y="epoch",
            smoothing=0.0,  # Nessuno smoothing
            legend={"position": "bottom"}
        )
    )
    
    # Salva il report
    report.save()
    
    print(f"Report '{args.dashboard_name}' creato con successo!")
    
    # Chiudi wandb
    wandb.finish()

if __name__ == "__main__":
    main()
