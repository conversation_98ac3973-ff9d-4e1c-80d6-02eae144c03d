#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare caption con modelli zero-shot e fine-tuned senza tokenizer personalizzato,
e valutarli con le metriche BLEU, CIDER, METEOR e CLIP SCORE.
"""

import os
import sys
import json
import argparse
import logging
import torch
import numpy as np
from tqdm import tqdm
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import PeftModel
import nltk
import clip
from PIL import Image
import cairosvg
import io
import wandb
from pycocoevalcap.cider.cider import Cider
from pycocoevalcap.meteor.meteor import Meteor
from pycocoevalcap.bleu.bleu import Bleu
from pycocoevalcap.tokenizer.ptbtokenizer import PTBTokenizer

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Assicurati che NLTK abbia i dati necessari
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')
try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

def parse_args():
    parser = argparse.ArgumentParser(description="Genera e valuta caption con diversi modelli")
    parser.add_argument("--test_file", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json", 
                        help="File JSON con i dati di test")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results",
                        help="Directory di output per i risultati")
    parser.add_argument("--llama_finetuned_path", type=str, 
                        default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence",
                        help="Path al modello Llama fine-tuned senza tokenizer personalizzato")
    parser.add_argument("--gemma_finetuned_path", type=str, 
                        default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence",
                        help="Path al modello Gemma fine-tuned senza tokenizer personalizzato")
    parser.add_argument("--num_samples", type=int, default=100, 
                        help="Numero di campioni da valutare")
    parser.add_argument("--use_clip", action="store_true", 
                        help="Calcola anche il CLIP score")
    parser.add_argument("--clip_model", type=str, default="ViT-B/32", 
                        help="Modello CLIP da utilizzare")
    parser.add_argument("--render_size", type=int, default=224, 
                        help="Dimensione per il rendering SVG")
    parser.add_argument("--use_wandb", action="store_true", 
                        help="Usa Weights & Biands per tracciare i risultati")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore",
                        help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner",
                        help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default="model_evaluation",
                        help="Nome della run in Weights & Biands")
    return parser.parse_args()

def load_test_data(test_file, num_samples):
    """Carica i dati di test dal file JSON."""
    logger.info(f"Caricamento dei dati di test da {test_file}")
    with open(test_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if num_samples > 0 and num_samples < len(data):
        import random
        random.seed(42)  # Per riproducibilità
        data = random.sample(data, num_samples)
    
    logger.info(f"Caricati {len(data)} esempi di test")
    return data

def load_zero_shot_model(model_name):
    """Carica un modello base per zero-shot inference."""
    logger.info(f"Caricamento del modello zero-shot {model_name}")
    
    # Configurazione per quantizzazione a 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True
    )
    
    # Carica il modello e il tokenizer
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=bnb_config,
        device_map="auto",
        token=os.environ.get("HF_TOKEN", "*************************************")
    )
    
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        token=os.environ.get("HF_TOKEN", "*************************************")
    )
    
    # Assicurati che il tokenizer abbia un token di padding
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    return model, tokenizer

def load_finetuned_model(model_path, base_model_name):
    """Carica un modello fine-tuned con LoRA."""
    logger.info(f"Caricamento del modello fine-tuned da {model_path}")
    
    # Configurazione per quantizzazione a 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True
    )
    
    # Carica il modello base
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_name,
        quantization_config=bnb_config,
        device_map="auto",
        token=os.environ.get("HF_TOKEN", "*************************************")
    )
    
    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        base_model_name,
        token=os.environ.get("HF_TOKEN", "*************************************")
    )
    
    # Assicurati che il tokenizer abbia un token di padding
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Carica il modello LoRA
    model = PeftModel.from_pretrained(base_model, model_path)
    
    return model, tokenizer

def generate_caption(model, tokenizer, svg_content, max_length=200):
    """Genera una caption per un SVG utilizzando il modello specificato."""
    # Crea il prompt
    prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg_content}\n\nDescrizione:"
    
    # Tokenizza il prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
    
    # Genera la caption
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            num_return_sequences=1
        )
    
    # Decodifica la caption generata
    caption = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Estrai solo la parte della caption dopo il prompt
    caption = caption.split("Descrizione:")[1].strip() if "Descrizione:" in caption else caption
    
    return caption

def render_svg_to_image(svg_string, size=224):
    """Renderizza una stringa SVG in un'immagine PIL."""
    try:
        # Renderizza SVG in PNG
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'), output_width=size, output_height=size)
        
        # Converti i dati PNG in un'immagine PIL
        image = Image.open(io.BytesIO(png_data))
        return image
    except Exception as e:
        logger.error(f"Errore nel rendering SVG: {e}")
        # Restituisci un'immagine nera in caso di errore
        return Image.new('RGB', (size, size), color='black')

def calculate_clip_score(svgs, captions, model_name="ViT-B/32", render_size=224):
    """Calcola il CLIP score per ogni coppia (SVG, didascalia)."""
    # Carica il modello CLIP
    device = "cuda" if torch.cuda.is_available() else "cpu"
    model, preprocess = clip.load(model_name, device=device)
    
    scores = []
    
    for svg, caption in tqdm(zip(svgs, captions), total=len(svgs), desc="Calcolo CLIP score"):
        try:
            # Renderizza SVG in immagine
            image = render_svg_to_image(svg, render_size)
            
            # Preprocess dell'immagine
            image_input = preprocess(image).unsqueeze(0).to(device)
            
            # Tokenizza la didascalia
            text_input = clip.tokenize([caption]).to(device)
            
            # Calcola le features
            with torch.no_grad():
                image_features = model.encode_image(image_input)
                text_features = model.encode_text(text_input)
            
            # Normalizza le features
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            # Calcola la similarità coseno
            similarity = (100.0 * image_features @ text_features.T).item()
            scores.append(similarity)
        except Exception as e:
            logger.error(f"Errore nel calcolo del CLIP score: {e}")
            scores.append(0.0)
    
    return scores

def evaluate_with_coco_metrics(references, hypotheses):
    """Valuta le caption con le metriche COCO (BLEU, METEOR, CIDEr)."""
    # Prepara i dati nel formato richiesto dalle metriche COCO
    gts = {}
    res = {}
    
    for i, (ref, hyp) in enumerate(zip(references, hypotheses)):
        gts[i] = [ref]
        res[i] = [hyp]
    
    # Tokenizza le caption
    tokenizer = PTBTokenizer()
    gts = tokenizer.tokenize(gts)
    res = tokenizer.tokenize(res)
    
    # Calcola le metriche
    scorers = [
        (Bleu(4), ["Bleu_1", "Bleu_2", "Bleu_3", "Bleu_4"]),
        (Meteor(), "METEOR"),
        (Cider(), "CIDEr")
    ]
    
    scores = {}
    
    for scorer, method in scorers:
        score, scores_per_sample = scorer.compute_score(gts, res)
        if isinstance(method, list):
            for m, s in zip(method, score):
                scores[m] = s
        else:
            scores[method] = score
    
    return scores

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per il token di Hugging Face
    os.environ["HF_TOKEN"] = "*************************************"
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Inizializza Weights & Biands
    if args.use_wandb:
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=args.wandb_run_name,
            config=vars(args)
        )
    
    # Carica i dati di test
    test_data = load_test_data(args.test_file, args.num_samples)
    
    # Definisci i modelli da valutare
    models_to_evaluate = [
        {
            "name": "llama31_8b_zero_shot",
            "type": "zero_shot",
            "base_model": "meta-llama/Llama-3.1-8B-Instruct",
            "path": None
        },
        {
            "name": "gemma2_9b_it_zero_shot",
            "type": "zero_shot",
            "base_model": "google/gemma-2-9b-it",
            "path": None
        },
        {
            "name": "mistral_7b_zero_shot",
            "type": "zero_shot",
            "base_model": "mistralai/Mistral-7B-Instruct-v0.2",
            "path": None
        },
        {
            "name": "llama31_8b_finetuned",
            "type": "finetuned",
            "base_model": "meta-llama/Llama-3.1-8B-Instruct",
            "path": args.llama_finetuned_path
        },
        {
            "name": "gemma2_9b_it_finetuned",
            "type": "finetuned",
            "base_model": "google/gemma-2-9b-it",
            "path": args.gemma_finetuned_path
        }
    ]
    
    # Estrai gli SVG e le caption di riferimento
    svgs = [item["svg"] for item in test_data]
    references = [item["caption"] for item in test_data]
    
    # Valuta ogni modello
    all_results = {}
    
    for model_config in models_to_evaluate:
        logger.info(f"Valutazione del modello: {model_config['name']}")
        
        # Carica il modello
        if model_config["type"] == "zero_shot":
            model, tokenizer = load_zero_shot_model(model_config["base_model"])
        else:
            model, tokenizer = load_finetuned_model(model_config["path"], model_config["base_model"])
        
        # Genera le caption
        generated_captions = []
        for svg in tqdm(svgs, desc=f"Generazione caption con {model_config['name']}"):
            caption = generate_caption(model, tokenizer, svg)
            generated_captions.append(caption)
        
        # Salva le caption generate
        results_file = os.path.join(args.output_dir, f"{model_config['name']}_results.jsonl")
        with open(results_file, 'w', encoding='utf-8') as f:
            for i, (svg, ref, gen) in enumerate(zip(svgs, references, generated_captions)):
                result = {
                    "id": i,
                    "svg": svg,
                    "true_caption": ref,
                    "generated_caption": gen
                }
                f.write(json.dumps(result) + '\n')
        
        logger.info(f"Risultati salvati in {results_file}")
        
        # Valuta le caption con le metriche COCO
        logger.info(f"Valutazione delle caption con le metriche COCO")
        coco_metrics = evaluate_with_coco_metrics(references, generated_captions)
        
        # Calcola il CLIP score se richiesto
        clip_scores = None
        if args.use_clip:
            logger.info(f"Calcolo del CLIP score")
            clip_scores = calculate_clip_score(svgs, generated_captions, args.clip_model, args.render_size)
            coco_metrics["CLIP_SCORE"] = np.mean(clip_scores)
        
        # Salva le metriche
        metrics_file = os.path.join(args.output_dir, f"{model_config['name']}_metrics.json")
        with open(metrics_file, 'w', encoding='utf-8') as f:
            json.dump(coco_metrics, f, indent=2)
        
        logger.info(f"Metriche salvate in {metrics_file}")
        
        # Stampa le metriche
        logger.info(f"Metriche per {model_config['name']}:")
        for metric, value in coco_metrics.items():
            logger.info(f"{metric}: {value:.4f}")
        
        # Aggiungi i risultati al dizionario complessivo
        all_results[model_config['name']] = {
            "metrics": coco_metrics,
            "results_file": results_file
        }
        
        # Log su Weights & Biands
        if args.use_wandb:
            wandb.log({f"{model_config['name']}_{k}": v for k, v in coco_metrics.items()})
            
            # Crea una tabella con esempi
            examples_table = wandb.Table(columns=["id", "true_caption", "generated_caption"])
            for i, (ref, gen) in enumerate(zip(references[:20], generated_captions[:20])):  # Limita a 20 esempi
                examples_table.add_data(i, ref, gen)
            
            wandb.log({f"{model_config['name']}_examples": examples_table})
    
    # Crea un riepilogo comparativo
    comparison = {}
    for model_name, result in all_results.items():
        for metric, value in result["metrics"].items():
            if metric not in comparison:
                comparison[metric] = {}
            comparison[metric][model_name] = value
    
    # Salva il riepilogo comparativo
    comparison_file = os.path.join(args.output_dir, "models_comparison.json")
    with open(comparison_file, 'w', encoding='utf-8') as f:
        json.dump(comparison, f, indent=2)
    
    logger.info(f"Riepilogo comparativo salvato in {comparison_file}")
    
    # Crea una tabella comparativa su Weights & Biands
    if args.use_wandb:
        for metric in comparison:
            data = [[model, value] for model, value in comparison[metric].items()]
            table = wandb.Table(data=data, columns=["model", metric])
            wandb.log({f"comparison_{metric}": wandb.plot.bar(table, "model", metric, title=f"Comparison of {metric}")})
        
        wandb.finish()
    
    logger.info("Valutazione completata!")

if __name__ == "__main__":
    main()
