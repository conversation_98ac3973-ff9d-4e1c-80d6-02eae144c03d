#!/bin/bash
#SBATCH --job-name=lora_wandb
#SBATCH --output=logs/lora_wandb_%j_lora_wandb.out
#SBATCH --error=logs/lora_wandb_%j_lora_wandb.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G"
#SBATCH --gpus=1
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=64G
#SBATCH --time=12:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura autenticazione Hugging Face
echo "Configurazione autenticazione Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura HF_HOME
echo "Configurazione HF_HOME..."
export HF_HOME="${HF_HOME:-/work/tesi_ediluzio/.cache/huggingface}"
echo "Variabile di ambiente HF_HOME impostata a: $HF_HOME"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
echo "Variabile di ambiente WANDB_API_KEY impostata."

# Parametri
MODEL_NAME=${1:-"meta-llama/Llama-3.1-8B-Instruct"}
DATA_FILE=${2:-"/work/tesi_ediluzio/data/processed/xml_format/train_set_final_xml.json"}
CONFIG_PATH=${3:-"/work/tesi_ediluzio/experiments/xml_direct_input/configs/llama31_8b_lora_xml_custom_token_convergence.json"}
OUTPUT_DIR=${4:-"/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token_convergence"}
USE_CUSTOM_TOKENIZER=${5:-"--use_custom_tokenizer"}
WANDB_ENTITY=${6:-"337543"}
WANDB_PROJECT=${7:-"captioner"}
WANDB_RUN_NAME=${8:-"llama31_8b_lora_xml_custom_token_convergence"}
EARLY_STOPPING=${9:-"--early_stopping"}
PATIENCE=${10:-20}
MIN_DELTA=${11:-0.0005}
USE_WANDB=${12:-"--use_wandb"}

# Gestione degli argomenti
# Se USE_CUSTOM_TOKENIZER è vuoto o "", non lo passiamo
if [ "$USE_CUSTOM_TOKENIZER" = "" ]; then
    USE_CUSTOM_TOKENIZER_ARG=""
else
    USE_CUSTOM_TOKENIZER_ARG="$USE_CUSTOM_TOKENIZER"
fi

# Se USE_WANDB è vuoto o "", non lo passiamo
if [ "$USE_WANDB" = "" ]; then
    USE_WANDB_ARG=""
else
    USE_WANDB_ARG="$USE_WANDB"
fi

# Se EARLY_STOPPING è vuoto o "", non lo passiamo
if [ "$EARLY_STOPPING" = "" ]; then
    EARLY_STOPPING_ARG=""
else
    EARLY_STOPPING_ARG="$EARLY_STOPPING"
fi

# Stampa informazioni
echo "Avvio Training LoRA con Weights & Biands"
echo "Modello: $MODEL_NAME"
echo "Dataset: $DATA_FILE"
echo "Config: $CONFIG_PATH"
echo "Output: $OUTPUT_DIR"
echo "Custom Tokenizer: $USE_CUSTOM_TOKENIZER"
echo "Weights & Biands Entity: $WANDB_ENTITY"
echo "Weights & Biands Project: $WANDB_PROJECT"
echo "Weights & Biands Run Name: $WANDB_RUN_NAME"
echo "Early Stopping: $EARLY_STOPPING"
echo "Patience: $PATIENCE"
echo "Min Delta: $MIN_DELTA"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocata (da SLURM): $SLURM_GPUS_ON_NODE"

# Crea la directory di output se non esiste
mkdir -p "$OUTPUT_DIR"

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/train_lora_wandb.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/train_lora_wandb.py \
    --model_name_or_path "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    $USE_CUSTOM_TOKENIZER_ARG \
    --wandb_entity "$WANDB_ENTITY" \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_run_name "$WANDB_RUN_NAME" \
    $EARLY_STOPPING_ARG \
    --patience "$PATIENCE" \
    --min_delta "$MIN_DELTA" \
    $USE_WANDB_ARG

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Training LoRA completato con successo"
else
    echo "Training LoRA fallito con codice di errore: $?"
fi
