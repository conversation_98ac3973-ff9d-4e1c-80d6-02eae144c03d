#!/bin/bash

#SBATCH --job-name=lora_infer   # Nome del job
#SBATCH --output=/work/tesi_ediluzio/logs/lora_infer_%A_%x.out # Log: %A=Job ID, %x=Job Name
#SBATCH --error=/work/tesi_ediluzio/logs/lora_infer_%A_%x.err
#SBATCH --partition=all_usr_prod     # Usa la stessa partizione
#SBATCH --account=tesi_ediluzio      # Usa lo stesso account
#SBATCH --qos=normal                 # Usa la stessa QoS
# !!! VERIFICA QUESTO CONSTRAINT !!! Deve corrispondere a GPU FUNZIONANTI
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G" # O rimuovi/modifica se necessario
# Richiede UNA SOLA GPU. L'inferenza zero-shot di solito non ne richiede di più.
#SBATCH --gpus=1
# Tempo stimato per inferenza su 2000 campioni (aggiusta se necessario)
#SBATCH --time=02:00:00              # Es. 2 ore
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4            # CPU e Mem dovrebbero bastare
#SBATCH --mem=32G
#SBATCH --mail-type=FAIL,END         # Ricevi email se fallisce o finisce
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE
################################################################################

PROJECT_ROOT="/work/tesi_ediluzio"
PYTHON_EXEC="${PROJECT_ROOT}/svg_captioning_env/bin/python"
# Assicurati che questo sia il percorso corretto dello script Python modificato!
SCRIPT_PATH="${PROJECT_ROOT}/experiments/xml_direct_input/run_inference_unified.py"

# --- Verifica Ambiente ---
if [ ! -f "$PYTHON_EXEC" ]; then echo "Errore: Python non trovato: ${PYTHON_EXEC}" ; exit 1; fi
if [ ! -f "$SCRIPT_PATH" ]; then echo "Errore: Script Python non trovato: ${SCRIPT_PATH}" ; exit 1; fi
echo "Using Python executable: ${PYTHON_EXEC}"

# Crea cartella logs se non esiste
mkdir -p "${PROJECT_ROOT}/logs"

# --- Configurazione Autenticazione Hugging Face ---
echo "Configurazione autenticazione Hugging Face..."
# Imposta direttamente la variabile di ambiente HF_TOKEN
# Nota: Questo è un token di esempio, dovresti sostituirlo con un token valido
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# --- Gestione Argomenti ---
MODEL_NAME_OR_PATH=${1} # Prende il nome/percorso del modello dal primo argomento sbatch
OUTPUT_RESULTS_FILE=${2} # Prende il percorso del file JSONL di output dal secondo argomento sbatch
LORA_PATH=${3} # Prende il percorso dell'adattatore LoRA dal terzo argomento sbatch
USE_CUSTOM_TOKENIZER=${4:-"true"} # Usa il tokenizer custom (default: true)

if [ -z "$MODEL_NAME_OR_PATH" ] || [ -z "$OUTPUT_RESULTS_FILE" ] || [ -z "$LORA_PATH" ]; then
  echo "Errore: Specificare NOME_MODELLO, PERCORSO_OUTPUT_JSONL e PERCORSO_LORA come argomenti."
  echo "Esempio: sbatch $0 nvidia/Nemotron-Mini-4B-Instruct /path/to/results/nemotron_lora_results.jsonl /path/to/lora/adapter"
  exit 1
fi

DATASET_FILE="${PROJECT_ROOT}/data/processed/test_set_final_2k.json" # File di test fisso

# Imposta i flag in base ai parametri
CUSTOM_TOKENIZER_FLAG=""
if [ "$USE_CUSTOM_TOKENIZER" = "true" ]; then
    CUSTOM_TOKENIZER_FLAG="--use_custom_tokenizer"
fi

echo "Avvio Inferenza LoRA per Modello: ${MODEL_NAME_OR_PATH}"
echo "Adattatore LoRA: ${LORA_PATH}"
echo "Dataset: ${DATASET_FILE}"
echo "Output salvato in: ${OUTPUT_RESULTS_FILE}"
echo "Uso tokenizer custom: ${USE_CUSTOM_TOKENIZER}"
echo "Job ID: $SLURM_JOB_ID" ; echo "Nodo: $SLURMD_NODENAME" ; echo "GPU Allocata (da SLURM): $CUDA_VISIBLE_DEVICES"

# --- Comando di Esecuzione ---
echo "Eseguo lo script Python: ${SCRIPT_PATH}"

# Esegui passando gli argomenti allo script Python
${PYTHON_EXEC} ${SCRIPT_PATH} \
    --model_name_or_path "${MODEL_NAME_OR_PATH}" \
    --output_file "${OUTPUT_RESULTS_FILE}" \
    --lora_path "${LORA_PATH}" \
    ${CUSTOM_TOKENIZER_FLAG} \
    --load_in_8bit \
    --test_file "${DATASET_FILE}" \
    --max_new_tokens 100 \
    --temperature 0.7 \
    --top_p 0.9 \
    --top_k 50 \
    --repetition_penalty 1.1

EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
  echo "Inferenza LoRA completata con successo per ${MODEL_NAME_OR_PATH} con adattatore ${LORA_PATH}."
else
  echo "Inferenza LoRA fallita con codice di errore: $EXIT_CODE per ${MODEL_NAME_OR_PATH} con adattatore ${LORA_PATH}"
fi
exit $EXIT_CODE
