#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare un report completo sui risultati degli esperimenti.
"""

import argparse
import json
import os
import subprocess
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Genera un report completo sui risultati degli esperimenti")
    parser.add_argument("--results_dir", type=str, required=True, help="Directory con i file di risultati")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per il report")
    parser.add_argument("--num_samples", type=int, default=10, help="Numero di campioni da visualizzare")
    return parser.parse_args()

def run_command(command):
    """Esegue un comando shell e restituisce l'output."""
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()
    
    if process.returncode != 0:
        print(f"Errore nell'esecuzione del comando: {command}")
        print(f"Errore: {stderr.decode('utf-8')}")
        return None
    
    return stdout.decode('utf-8')

def find_result_files(results_dir):
    """Trova i file di risultati nella directory specificata."""
    result_files = []
    model_names = []
    
    # Cerca i file JSONL nella directory
    for file in os.listdir(results_dir):
        if file.endswith(".jsonl"):
            result_files.append(os.path.join(results_dir, file))
            # Estrai il nome del modello dal nome del file
            model_name = os.path.splitext(file)[0]
            model_names.append(model_name)
    
    return result_files, model_names

def evaluate_results(result_files, model_names, output_dir):
    """Valuta i risultati e genera le metriche."""
    metrics_files = []
    
    for result_file, model_name in zip(result_files, model_names):
        # Crea il file di output per le metriche
        metrics_file = os.path.join(output_dir, f"{model_name}_metrics.json")
        metrics_files.append(metrics_file)
        
        # Esegui lo script di valutazione
        command = f"python /work/tesi_ediluzio/experiments/xml_direct_input/evaluate_captions.py --results_file {result_file} --output_file {metrics_file}"
        print(f"Esecuzione del comando: {command}")
        output = run_command(command)
        
        if output:
            print(output)
    
    return metrics_files

def compare_models(metrics_files, model_names, output_dir):
    """Confronta i modelli e genera i grafici."""
    # Esegui lo script di confronto
    command = f"python /work/tesi_ediluzio/experiments/xml_direct_input/compare_models.py --metrics_files {' '.join(metrics_files)} --model_names {' '.join(model_names)} --output_dir {output_dir}/comparison"
    print(f"Esecuzione del comando: {command}")
    output = run_command(command)
    
    if output:
        print(output)

def visualize_captions(result_files, model_names, output_dir, num_samples):
    """Visualizza e confronta le didascalie generate."""
    # Esegui lo script di visualizzazione
    output_file = os.path.join(output_dir, "caption_comparison.csv")
    command = f"python /work/tesi_ediluzio/experiments/xml_direct_input/visualize_captions.py --results_files {' '.join(result_files)} --model_names {' '.join(model_names)} --output_file {output_file} --num_samples {num_samples}"
    print(f"Esecuzione del comando: {command}")
    output = run_command(command)
    
    if output:
        print(output)

def generate_html_report(metrics_files, model_names, output_dir):
    """Genera un report HTML."""
    # Carica le metriche
    metrics_dict = {}
    for metrics_file, model_name in zip(metrics_files, model_names):
        with open(metrics_file, 'r', encoding='utf-8') as f:
            metrics = json.load(f)
        metrics_dict[model_name] = metrics["average_metrics"]
    
    # Crea un DataFrame
    df = pd.DataFrame(metrics_dict).T
    
    # Crea il report HTML
    html_file = os.path.join(output_dir, "report.html")
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>Report Esperimenti SVG Captioning</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }}
        h1, h2, h3 {{
            color: #333;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .metrics-table {{
            margin-top: 20px;
        }}
        .comparison-images {{
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }}
        .comparison-image {{
            margin: 10px;
            text-align: center;
        }}
        .comparison-image img {{
            max-width: 100%;
            height: auto;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Report Esperimenti SVG Captioning</h1>
        <p>Data: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
        
        <h2>Confronto Metriche</h2>
        <div class="metrics-table">
            {df.to_html(classes='table table-striped', float_format='%.4f')}
        </div>
        
        <h2>Grafici di Confronto</h2>
        <div class="comparison-images">
""")
        
        # Aggiungi i grafici
        for metric in df.index:
            f.write(f"""
            <div class="comparison-image">
                <h3>{metric.upper()}</h3>
                <img src="comparison/comparison_{metric}.png" alt="Confronto {metric}">
            </div>
""")
        
        # Aggiungi il grafico radar
        f.write(f"""
            <div class="comparison-image">
                <h3>Confronto Radar</h3>
                <img src="comparison/comparison_radar.png" alt="Confronto Radar">
            </div>
        </div>
        
        <h2>Confronto Didascalie</h2>
        <iframe src="caption_comparison.html" width="100%" height="600px"></iframe>
        
        <h2>Conclusioni</h2>
        <p>
            Questo report presenta un confronto tra i modelli {', '.join(model_names)} per il task di generazione di didascalie per SVG.
            Le metriche utilizzate includono BLEU, METEOR e ROUGE, che misurano la similarità tra le didascalie generate e quelle di riferimento.
        </p>
        <p>
            Basandosi sui risultati, il modello con le migliori performance è <strong>{df['bleu_4'].idxmax()}</strong> per BLEU-4 e <strong>{df['meteor'].idxmax()}</strong> per METEOR.
        </p>
    </div>
</body>
</html>
""")
    
    print(f"Report HTML generato in {html_file}")

def main():
    args = parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(os.path.join(args.output_dir, "comparison"), exist_ok=True)
    
    # Trova i file di risultati
    print(f"Ricerca dei file di risultati in {args.results_dir}...")
    result_files, model_names = find_result_files(args.results_dir)
    
    if not result_files:
        print(f"Nessun file di risultati trovato in {args.results_dir}")
        return
    
    print(f"Trovati {len(result_files)} file di risultati:")
    for result_file, model_name in zip(result_files, model_names):
        print(f"  - {result_file} ({model_name})")
    
    # Valuta i risultati
    print("\nValutazione dei risultati...")
    metrics_files = evaluate_results(result_files, model_names, args.output_dir)
    
    # Confronta i modelli
    print("\nConfronto dei modelli...")
    compare_models(metrics_files, model_names, args.output_dir)
    
    # Visualizza le didascalie
    print("\nVisualizzazione delle didascalie...")
    visualize_captions(result_files, model_names, args.output_dir, args.num_samples)
    
    # Genera il report HTML
    print("\nGenerazione del report HTML...")
    generate_html_report(metrics_files, model_names, args.output_dir)
    
    print(f"\nReport completo generato in {args.output_dir}")

if __name__ == "__main__":
    main()
