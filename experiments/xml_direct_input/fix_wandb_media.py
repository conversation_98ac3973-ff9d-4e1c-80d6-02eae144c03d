#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
import glob
from PIL import Image
from tqdm import tqdm

def parse_args():
    parser = argparse.ArgumentParser(description="Ricarica le immagini su Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--run_name", type=str, default="SVG Captioner - Media Fix", help="Nome della run in Weights & Biands")
    parser.add_argument("--images_dir", type=str, default="/work/tesi_ediluzio/analysis",
                        help="Directory contenente le immagini da caricare")
    return parser.parse_args()

def main():
    args = parse_args()

    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"

    # Verifica che la directory delle immagini esista
    if not os.path.exists(args.images_dir):
        print(f"La directory {args.images_dir} non esiste!")
        return

    # Trova tutte le immagini nella directory
    image_files = []
    for ext in ["*.png", "*.jpg", "*.jpeg", "*.svg"]:
        image_files.extend(glob.glob(os.path.join(args.images_dir, "**", ext), recursive=True))

    if not image_files:
        print(f"Nessuna immagine trovata in {args.images_dir}!")
        return

    print(f"Trovate {len(image_files)} immagini da caricare")

    # Inizializza Weights & Biands
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.run_name,
        job_type="visualization"
    )

    # Carica le immagini
    print("Caricamento delle immagini su Weights & Biands...")

    for i, image_file in enumerate(tqdm(image_files, desc="Caricamento immagini")):
        try:
            # Ottieni il nome del file senza il percorso
            file_name = os.path.basename(image_file)

            # Carica l'immagine
            if file_name.endswith(".svg"):
                # Per i file SVG, crea un'immagine di testo
                wandb.log({
                    f"svg_{i}": wandb.Html(open(image_file, "r").read())
                })
            else:
                # Per le immagini raster
                img = Image.open(image_file)
                wandb.log({
                    f"image_{i}": wandb.Image(img, caption=file_name)
                })
        except Exception as e:
            print(f"Errore nel caricamento dell'immagine {image_file}: {e}")

    # Chiudi wandb
    wandb.finish()

    print(f"Run '{args.run_name}' creata con successo!")
    print(f"URL: {run.get_url()}")

if __name__ == "__main__":
    main()
