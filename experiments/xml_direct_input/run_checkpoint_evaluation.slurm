#!/bin/bash
#SBATCH --job-name=eval_checkpoints
#SBATCH --output=logs/eval_checkpoints_%j.out
#SBATCH --error=logs/eval_checkpoints_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --constraint=gpu_L40S_48G|gpu_A40_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G|gpu_2080Ti_11G
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --time=12:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
MODEL_NAME=${1:-"meta-llama/Llama-3.1-8B-Instruct"}
CHECKPOINT_DIR=${2:-"/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence"}
TEST_FILE=${3:-"/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"}
OUTPUT_DIR=${4:-"/work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence"}
USE_CUSTOM_TOKENIZER=${5:-""}
LOAD_IN_8BIT=${6:-""}
LOAD_IN_4BIT=${7:-"--load_in_4bit"}
NUM_SAMPLES=${8:-100}
USE_CLIP=${9:-"--use_clip"}
WANDB_ENTITY=${10:-"337543-unimore"}
WANDB_PROJECT=${11:-"captioner_checkpoints"}
CHECKPOINT_STEP=${12:-2}
CHECKPOINT_MIN=${13:-0}
CHECKPOINT_MAX=${14:-10000}

# Crea la directory di output
mkdir -p "$OUTPUT_DIR"

# Stampa informazioni
echo "Valutazione dei Checkpoint con Metriche Multiple"
echo "Modello: $MODEL_NAME"
echo "Directory checkpoint: $CHECKPOINT_DIR"
echo "File di test: $TEST_FILE"
echo "Directory di output: $OUTPUT_DIR"
echo "Tokenizer personalizzato: $USE_CUSTOM_TOKENIZER"
echo "Caricamento in 8-bit: $LOAD_IN_8BIT"
echo "Caricamento in 4-bit: $LOAD_IN_4BIT"
echo "Numero di campioni: $NUM_SAMPLES"
echo "Usa CLIP: $USE_CLIP"
echo "Weights & Biands Entity: $WANDB_ENTITY"
echo "Weights & Biands Project: $WANDB_PROJECT"
echo "Valuta ogni N checkpoint: $CHECKPOINT_STEP"
echo "Step minimo: $CHECKPOINT_MIN"
echo "Step massimo: $CHECKPOINT_MAX"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocate (da SLURM): $SLURM_GPUS_ON_NODE"

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/evaluate_checkpoints.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/evaluate_checkpoints.py \
    --model_name_or_path "$MODEL_NAME" \
    --checkpoint_dir "$CHECKPOINT_DIR" \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    $USE_CUSTOM_TOKENIZER \
    $LOAD_IN_8BIT \
    $LOAD_IN_4BIT \
    --num_samples "$NUM_SAMPLES" \
    $USE_CLIP \
    --wandb_entity "$WANDB_ENTITY" \
    --wandb_project "$WANDB_PROJECT" \
    --checkpoint_step "$CHECKPOINT_STEP" \
    --checkpoint_min "$CHECKPOINT_MIN" \
    --checkpoint_max "$CHECKPOINT_MAX"

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Valutazione dei checkpoint completata con successo"
else
    echo "Valutazione dei checkpoint fallita con codice di errore: $?"
fi
