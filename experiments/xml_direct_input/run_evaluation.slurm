#!/bin/bash
#SBATCH --job-name=eval_models
#SBATCH --output=logs/eval_models_%j.out
#SBATCH --error=logs/eval_models_%j.err
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G"
#SBATCH --gres=gpu:1
#SBATCH --time=2:00:00
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio

# Attiva l'ambiente Python direttamente
source /work/tesi_ediluzio/svg_captioning_env/bin/activate

# Imposta la variabile di ambiente per il token di Hugging Face
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
INFERENCE_RESULTS_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/inference_results"
EVALUATION_RESULTS_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results"
WANDB_ENTITY="337543-unimore"
WANDB_PROJECT="captioner"

# Crea la directory di output se non esiste
mkdir -p "$EVALUATION_RESULTS_DIR"

# Funzione per valutare un modello
evaluate_model() {
    local model_name=$1
    local results_file="$INFERENCE_RESULTS_DIR/$model_name/inference_results.txt"
    local output_file="$EVALUATION_RESULTS_DIR/$model_name.json"
    local wandb_run_name="eval_$model_name"

    echo "Valutazione del modello $model_name..."
    python /work/tesi_ediluzio/experiments/xml_direct_input/evaluate_captions_metrics.py \
        --results_file "$results_file" \
        --output_file "$output_file" \
        --wandb_entity "$WANDB_ENTITY" \
        --wandb_project "$WANDB_PROJECT" \
        --wandb_run_name "$wandb_run_name" \
        --use_clip \
        --clip_model "ViT-B/32"

    echo "Valutazione completata per $model_name. Risultati salvati in $output_file"
}

# Valuta tutti i modelli
evaluate_model "llama31_8b_no_token"
evaluate_model "gemma2_9b_it_no_token"
evaluate_model "llama31_8b_custom_token"
evaluate_model "gemma2_9b_it_custom_token"

echo "Valutazione completata per tutti i modelli."
