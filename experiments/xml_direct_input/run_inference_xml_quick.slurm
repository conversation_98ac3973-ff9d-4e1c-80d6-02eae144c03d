#!/bin/bash

#SBATCH --job-name=xml_test   # Nome del job
#SBATCH --output=/work/tesi_ediluzio/logs/xml_test_%A_%x_arg1.out # Log: %A=Job ID, %x=Job Name, %_arg1=Primo Arg
#SBATCH --error=/work/tesi_ediluzio/logs/xml_test_%A_%x_arg1.err
#SBATCH --partition=all_usr_prod     # Usa la stessa partizione
#SBATCH --account=tesi_ediluzio      # Usa lo stesso account
#SBATCH --qos=normal                 # Usa la stessa QoS
# !!! VERIFICA QUESTO CONSTRAINT !!! Deve corrispondere a GPU FUNZIONANTI
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G" # O rimuovi/modifica se necessario
# Richiede UNA SOLA GPU. L'inferenza zero-shot di solito non ne richiede di più.
#SBATCH --gpus=1
# Tempo ridotto per test rapido
#SBATCH --time=00:05:00              # Solo 5 minuti per test
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4            # CPU e Mem dovrebbero bastare
#SBATCH --mem=32G
#SBATCH --mail-type=FAIL,END         # Ricevi email se fallisce o finisce
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE
################################################################################

PROJECT_ROOT="/work/tesi_ediluzio"
PYTHON_EXEC="${PROJECT_ROOT}/svg_captioning_env/bin/python"
# Assicurati che questo sia il percorso corretto dello script Python modificato!
SCRIPT_PATH="${PROJECT_ROOT}/experiments/xml_direct_input/run_zero_shot_inference.py"
AUTH_SCRIPT="${PROJECT_ROOT}/setup_hf_auth.py"

# --- Verifica Ambiente ---
if [ ! -f "$PYTHON_EXEC" ]; then echo "Errore: Python non trovato: ${PYTHON_EXEC}" ; exit 1; fi
if [ ! -f "$SCRIPT_PATH" ]; then echo "Errore: Script Python non trovato: ${SCRIPT_PATH}" ; exit 1; fi
echo "Using Python executable: ${PYTHON_EXEC}"

# Crea cartella logs se non esiste
mkdir -p "${PROJECT_ROOT}/logs"

# --- Configurazione Autenticazione Hugging Face ---
echo "Configurazione autenticazione Hugging Face..."
# Imposta direttamente la variabile di ambiente HF_TOKEN
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# --- Gestione Argomenti ---
MODEL_NAME_OR_PATH=${1} # Prende il nome/percorso del modello dal primo argomento sbatch
OUTPUT_RESULTS_FILE=${2} # Prende il percorso del file JSONL di output dal secondo argomento sbatch

if [ -z "$MODEL_NAME_OR_PATH" ] || [ -z "$OUTPUT_RESULTS_FILE" ]; then
  echo "Errore: Specificare NOME_MODELLO e PERCORSO_OUTPUT_JSONL come argomenti."
  echo "Esempio: sbatch $0 deepseek-ai/DeepSeek-R1-Distill-Llama-8B /path/to/results/deepseek_results.jsonl"
  exit 1
fi

DATASET_FILE="${PROJECT_ROOT}/data/processed/xml_format/test_set_final_2k_xml.json" # File di test XML
NUM_SAMPLES="10" # Processa solo 10 campioni per test rapido

echo "XML TEST - Avvio Inferenza Zero-Shot per Modello: ${MODEL_NAME_OR_PATH}"
echo "Dataset XML: ${DATASET_FILE} (solo ${NUM_SAMPLES} campioni)"
echo "Output salvato in: ${OUTPUT_RESULTS_FILE}"
echo "Job ID: $SLURM_JOB_ID" ; echo "Nodo: $SLURMD_NODENAME" ; echo "GPU Allocata (da SLURM): $CUDA_VISIBLE_DEVICES"

# --- Comando di Esecuzione ---
echo "Eseguo lo script Python: ${SCRIPT_PATH}"

# Esegui passando gli argomenti allo script Python
# Aggiunto --load_in_4bit per risparmiare memoria (rimuovi se non necessario o se causa problemi)
# Aggiunto --do_sample per generazione più varia
${PYTHON_EXEC} ${SCRIPT_PATH} \
    --model_name_or_path "${MODEL_NAME_OR_PATH}" \
    --data_file "${DATASET_FILE}" \
    --output_file "${OUTPUT_RESULTS_FILE}" \
    --num_samples ${NUM_SAMPLES} \
    --max_new_tokens 100 \
    --load_in_4bit \
    --use_bf16 \
    --do_sample \
    --temperature 0.6 \
    --top_p 0.9

EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
  echo "XML TEST - Inferenza Zero-Shot completata con successo per ${MODEL_NAME_OR_PATH}."
else
  echo "XML TEST - Inferenza Zero-Shot fallita con codice di errore: $EXIT_CODE per ${MODEL_NAME_OR_PATH}"
fi
exit $EXIT_CODE
