{"zero_stage": 2, "offload_optimizer": {"device": "cpu", "pin_memory": true}, "offload_param": {"device": "cpu", "pin_memory": true}, "zero3_init_flag": false, "zero_force_ds_cpu_optimizer": false, "fp16": {"enabled": false}, "bf16": {"enabled": true}, "gradient_accumulation_steps": 4, "gradient_clipping": 1, "steps_per_print": 10, "train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "wall_clock_breakdown": false}