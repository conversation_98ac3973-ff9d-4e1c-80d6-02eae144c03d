{"model_name_or_path": "google/gemma-2-9b-it", "output_dir": "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/optimized_1000_token", "data_file": "/work/tesi_ediluzio/data/processed/xml_format_optimized/train_set_max1000_90000.json", "val_file": "/work/tesi_ediluzio/data/processed/xml_format_optimized/test_set_max1000_10000.json", "per_device_train_batch_size": 8, "per_device_eval_batch_size": 8, "gradient_accumulation_steps": 1, "learning_rate": 1e-05, "weight_decay": 0.01, "max_steps": 50000, "lr_scheduler_type": "cosine", "warmup_ratio": 0.05, "logging_steps": 10, "lora_r": 16, "lora_alpha": 32, "lora_dropout": 0.05, "lora_target_modules": ["q_proj", "v_proj", "k_proj", "o_proj"], "max_length": 1000, "bf16": true, "fp16": false, "tf32": true, "load_in_8bit": false, "load_in_4bit": false, "seed": 42, "push_to_hub": false, "hub_model_id": null, "hub_token": null, "gradient_checkpointing": true, "report_to": "wandb", "dataloader_num_workers": 2, "optim": "adamw_torch", "max_grad_norm": 1.0, "group_by_length": false, "ddp_find_unused_parameters": false, "ddp_bucket_cap_mb": 64, "dataloader_pin_memory": true, "eval_strategy": "steps", "eval_steps": 1000, "save_strategy": "steps", "save_steps": 1000, "save_total_limit": 5, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "early_stopping_patience": 10, "early_stopping_min_delta": 0.001, "early_stopping_on_validation": true, "run_name": "optimized_1000_token", "project_name": "svg_captioning_optimized"}