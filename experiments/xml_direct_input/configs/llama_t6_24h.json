{"model_name_or_path": "meta-llama/Llama-3.1-8B-Instruct", "output_dir": "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama_t6_24h", "data_file": "/work/tesi_ediluzio/data/processed/xml_format_optimized/train_set_100k_final_90000.json", "val_file": "/work/tesi_ediluzio/data/processed/xml_format_optimized/test_set_100k_final_10000.json", "per_device_train_batch_size": 1, "per_device_eval_batch_size": 1, "gradient_accumulation_steps": 16, "learning_rate": 2e-05, "weight_decay": 0.01, "max_steps": 50000, "lr_scheduler_type": "cosine", "warmup_ratio": 0.05, "logging_steps": 10, "lora_r": 16, "lora_alpha": 32, "lora_dropout": 0.05, "lora_target_modules": ["q_proj", "v_proj", "k_proj", "o_proj"], "max_length": 1500, "bf16": false, "fp16": true, "tf32": false, "load_in_8bit": false, "load_in_4bit": true, "seed": 42, "push_to_hub": false, "hub_model_id": null, "hub_token": null, "gradient_checkpointing": true, "report_to": "wandb", "dataloader_num_workers": 0, "optim": "adamw_torch", "max_grad_norm": 1.0, "group_by_length": false, "ddp_find_unused_parameters": false, "ddp_bucket_cap_mb": 32, "dataloader_pin_memory": false, "eval_strategy": "steps", "eval_steps": 500, "save_strategy": "steps", "save_steps": 500, "save_total_limit": 5, "load_best_model_at_end": true, "metric_for_best_model": "eval_loss", "greater_is_better": false, "early_stopping_patience": 100, "early_stopping_min_delta": 0.001, "early_stopping_on_validation": true, "run_name": "llama_t6_24h", "project_name": "svg_captioning_t6"}