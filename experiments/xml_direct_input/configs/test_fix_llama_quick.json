{"model_name_or_path": "meta-llama/Llama-3.1-8B-Instruct", "output_dir": "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/test_fix_quick_llama", "data_file": "/work/tesi_ediluzio/data/processed/xml_format_optimized/train_set_test5_32k.json", "per_device_train_batch_size": 4, "per_device_eval_batch_size": 4, "gradient_accumulation_steps": 1, "learning_rate": 1e-05, "weight_decay": 0.01, "max_steps": 50, "lr_scheduler_type": "cosine", "warmup_ratio": 0.05, "logging_steps": 5, "lora_r": 16, "lora_alpha": 32, "lora_dropout": 0.05, "lora_target_modules": ["q_proj", "v_proj", "k_proj", "o_proj"], "max_length": 2048, "bf16": false, "fp16": true, "tf32": true, "load_in_8bit": false, "load_in_4bit": false, "seed": 42, "push_to_hub": false, "hub_model_id": null, "hub_token": null, "gradient_checkpointing": true, "report_to": "wandb", "dataloader_num_workers": 1, "optim": "adamw_torch", "max_grad_norm": 1.0, "group_by_length": false, "ddp_find_unused_parameters": false, "ddp_bucket_cap_mb": 32, "dataloader_pin_memory": false, "save_strategy": "steps", "save_steps": 25, "save_total_limit": 2, "load_best_model_at_end": false, "run_name": "test_fix_quick_llama", "project_name": "svg_captioning_test_fix"}