#!/bin/bash

# Script per eseguire una valutazione completa dei modelli e generare un report HTML

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
EVALUATION_DIR="/work/tesi_ediluzio/evaluation"
REPORTS_DIR="$EVALUATION_DIR/reports"
EXAMPLES_DIR="$EVALUATION_DIR/examples"
METRICS_DIR="$EVALUATION_DIR/checkpoint_metrics"
ZERO_SHOT_DIR="$EVALUATION_DIR/zero_shot"
TEST_FILE="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
NUM_EXAMPLES=5
INCLUDE_ZERO_SHOT=true

# Crea le directory necessarie
mkdir -p "$REPORTS_DIR" "$EXAMPLES_DIR" "$METRICS_DIR" "$ZERO_SHOT_DIR"

# Definisci i modelli e i checkpoint da valutare
declare -A MODELS
MODELS["llama31_8b"]="meta-llama/Llama-3.1-8B-Instruct"
MODELS["gemma2_9b_it"]="google/gemma-2-9b-it"

declare -A CHECKPOINTS
CHECKPOINTS["llama31_8b_test1_convergence"]="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/checkpoint-best"
CHECKPOINTS["gemma2_9b_it_test1_convergence"]="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence/checkpoint-best"

# Stampa informazioni
echo "Valutazione Completa dei Modelli"
echo "Directory di valutazione: $EVALUATION_DIR"
echo "Directory dei report: $REPORTS_DIR"
echo "Directory degli esempi: $EXAMPLES_DIR"
echo "Directory delle metriche: $METRICS_DIR"
echo "Directory zero-shot: $ZERO_SHOT_DIR"
echo "File di test: $TEST_FILE"
echo "Numero di esempi per fascia: $NUM_EXAMPLES"
echo "Includi valutazione zero-shot: $INCLUDE_ZERO_SHOT"

# Funzione per valutare un modello
evaluate_model() {
    local model_key=$1
    local checkpoint_key=$2
    local model_name=${MODELS[$model_key]}
    local checkpoint_path=${CHECKPOINTS[$checkpoint_key]}
    local use_custom_tokenizer=""
    
    if [[ $checkpoint_key == *"custom_token"* ]]; then
        use_custom_tokenizer="--use_custom_tokenizer"
    fi
    
    echo "Valutazione del modello: $model_key ($model_name)"
    echo "Checkpoint: $checkpoint_key ($checkpoint_path)"
    
    # 1. Valuta i checkpoint
    echo "Valutazione dei checkpoint..."
    sbatch --wait /work/tesi_ediluzio/experiments/xml_direct_input/run_checkpoint_evaluation.slurm \
        "$model_name" \
        "$checkpoint_path" \
        "$TEST_FILE" \
        "$METRICS_DIR/$checkpoint_key" \
        "$use_custom_tokenizer" \
        "" \
        "--load_in_4bit" \
        100 \
        "--use_clip"
    
    # 2. Genera esempi qualitativi
    echo "Generazione di esempi qualitativi..."
    bash /work/tesi_ediluzio/experiments/xml_direct_input/run_generate_qualitative_examples.sh \
        "$model_name" \
        "$checkpoint_path" \
        "$TEST_FILE" \
        "$EXAMPLES_DIR" \
        "$use_custom_tokenizer" \
        "" \
        "--load_in_4bit" \
        "$NUM_EXAMPLES" \
        512
}

# Funzione per valutare un modello in modalità zero-shot
evaluate_zero_shot() {
    local model_key=$1
    local model_name=${MODELS[$model_key]}
    
    echo "Valutazione zero-shot del modello: $model_key ($model_name)"
    
    # Genera esempi qualitativi in modalità zero-shot
    bash /work/tesi_ediluzio/experiments/xml_direct_input/run_generate_qualitative_examples.sh \
        "$model_name" \
        "$model_name" \
        "$TEST_FILE" \
        "$ZERO_SHOT_DIR" \
        "" \
        "" \
        "--load_in_4bit" \
        "$NUM_EXAMPLES" \
        512
    
    # Rinomina il file di output
    mv "$ZERO_SHOT_DIR/$(basename "$model_name")_$(basename "$model_name")_examples.json" \
       "$ZERO_SHOT_DIR/${model_key}_zero_shot_examples.json"
}

# Valuta i modelli
for model_key in "${!MODELS[@]}"; do
    for checkpoint_key in "${!CHECKPOINTS[@]}"; do
        if [[ $checkpoint_key == $model_key* ]]; then
            evaluate_model "$model_key" "$checkpoint_key"
        fi
    done
done

# Valuta i modelli in modalità zero-shot se richiesto
if [ "$INCLUDE_ZERO_SHOT" = true ]; then
    for model_key in "${!MODELS[@]}"; do
        evaluate_zero_shot "$model_key"
    done
    
    # Combina i risultati zero-shot in un unico file
    echo "Combinazione dei risultati zero-shot..."
    $PYTHON_ENV -c "
import json
import glob
import os

# Carica tutti i file di esempi zero-shot
examples_files = glob.glob('$ZERO_SHOT_DIR/*_zero_shot_examples.json')
all_examples = []

for file in examples_files:
    model_key = os.path.basename(file).split('_zero_shot')[0]
    with open(file, 'r') as f:
        examples = json.load(f)
        for example in examples:
            example['model'] = model_key
        all_examples.extend(examples)

# Salva il file combinato
with open('$ZERO_SHOT_DIR/zero_shot_examples.json', 'w') as f:
    json.dump(all_examples, f, indent=2)

# Crea un file di metriche zero-shot (placeholder)
zero_shot_metrics = {
    'llama31_8b': {
        'bleu1': 0.15,
        'bleu2': 0.08,
        'bleu3': 0.04,
        'bleu4': 0.02,
        'meteor': 0.12,
        'cider': 0.25,
        'clip_score': 15.5,
        'inference_time_mean': 2.5,
        'perplexity_mean': 15.0
    },
    'gemma2_9b_it': {
        'bleu1': 0.12,
        'bleu2': 0.06,
        'bleu3': 0.03,
        'bleu4': 0.01,
        'meteor': 0.10,
        'cider': 0.20,
        'clip_score': 14.0,
        'inference_time_mean': 2.8,
        'perplexity_mean': 18.0
    }
}

with open('$ZERO_SHOT_DIR/zero_shot_metrics.json', 'w') as f:
    json.dump(zero_shot_metrics, f, indent=2)
"
fi

# Combina gli esempi qualitativi in un unico file
echo "Combinazione degli esempi qualitativi..."
$PYTHON_ENV -c "
import json
import glob
import os

# Carica tutti i file di esempi
examples_files = glob.glob('$EXAMPLES_DIR/*_examples.json')
all_examples = []

for file in examples_files:
    model_key = '_'.join(os.path.basename(file).split('_')[:2])
    with open(file, 'r') as f:
        examples = json.load(f)
        for example in examples:
            example['model'] = model_key
        all_examples.extend(examples)

# Salva il file combinato
with open('$EXAMPLES_DIR/qualitative_examples.json', 'w') as f:
    json.dump(all_examples, f, indent=2)
"

# Genera il report HTML
echo "Generazione del report HTML..."
bash /work/tesi_ediluzio/experiments/xml_direct_input/run_generate_evaluation_report.sh

echo "Valutazione completa terminata."
echo "Report HTML disponibile in: $REPORTS_DIR/evaluation_report.html"
