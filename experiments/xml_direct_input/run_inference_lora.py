# Nome file: run_inference_lora.py

import os
import json
import argparse
import logging
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from tqdm import tqdm # Per barra di avanzamento
# --- PEFT ---
# Importa PeftModel per caricare gli adattatori
from peft import PeftModel

# Assicurati: pip install transformers torch datasets accelerate bitsandbytes peft

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Funzione per formattare il prompt (IDENTICA A run_inference_plain.py)
def format_instruct_prompt(tokenizer, svg_data_string: str) -> str:
    """Crea il prompt per un modello instruct."""
    has_chat_template = getattr(tokenizer, 'chat_template', None) is not None or \
                        getattr(tokenizer, 'apply_chat_template', None) is not None
    instruction = f"Generate a caption for the following SVG path data string:\n\n{svg_data_string}"
    prompt = ""
    if has_chat_template:
        messages = [{"role": "user", "content": instruction}]
        try:
            prompt = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        except Exception as e:
            logger.warning(f"apply_chat_template fallito ({e}), uso template manuale.")
            has_chat_template = False
    if not has_chat_template:
         prompt = f"User: {instruction}\nAssistant: "
    return prompt

def main():
    parser = argparse.ArgumentParser(description="Run inference with LoRA adapters on SVG data.")
    # Argomenti ereditati da run_inference_plain.py
    parser.add_argument("--model_name_or_path", type=str, required=True, help="HF ID or path to the BASE pre-trained model.")
    parser.add_argument("--tokenizer_name", type=str, default=None, help="Optional: HF ID or path to the tokenizer (defaults to model name).")
    parser.add_argument("--data_file", type=str, required=True, help="Path to the JSON data file.")
    parser.add_argument("--num_samples", type=int, default=5, help="Number of samples to run inference on.")
    parser.add_argument("--max_new_tokens", type=int, default=100, help="Max new tokens for caption generation.")
    parser.add_argument("--load_in_8bit", action='store_true', help="Load BASE model in 8-bit.")
    parser.add_argument("--load_in_4bit", action='store_true', help="Load BASE model in 4-bit.")
    parser.add_argument("--use_bf16", action='store_true', help="Use bfloat16 compute dtype for 4-bit (if loading in 4-bit).")
    parser.add_argument("--temperature", type=float, default=0.6, help="Sampling temperature (lower might be better post-finetuning).") # Default leggermente ridotto
    parser.add_argument("--top_p", type=float, default=0.9, help="Nucleus sampling top-p.")
    parser.add_argument("--do_sample", action='store_true', help="Enable sampling (recommended for diverse outputs).")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device ('cuda' or 'cpu').")
    # --- PEFT ---
    # Nuovo argomento per specificare il percorso degli adattatori LoRA salvati
    parser.add_argument("--lora_adapter_path", type=str, required=True, help="Path to the saved LoRA adapters directory.")


    args = parser.parse_args()

    # Gestione conflitti argomenti (come prima)
    if args.load_in_4bit and args.load_in_8bit: logger.warning("Using 4-bit over 8-bit."); args.load_in_8bit = False
    if (args.load_in_4bit or args.load_in_8bit) and args.use_bf16 and not args.load_in_4bit: # bf16 compute dtype è solo per 4bit
         logger.warning("bf16 compute dtype only applicable when loading in 4-bit. Ignoring --use_bf16 for 8-bit/fp.")
         args.use_bf16 = False

    tokenizer_load_name = args.tokenizer_name if args.tokenizer_name else args.model_name_or_path
    logger.info(f"Caricamento tokenizer: {tokenizer_load_name}")
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_load_name, trust_remote_code=True)
        # Gestione pad token (come prima)
        if tokenizer.pad_token is None:
            if tokenizer.eos_token is not None: tokenizer.pad_token = tokenizer.eos_token; logger.info(f"Setting pad_token to eos_token: {tokenizer.eos_token}")
            else: fallback_pad = '<|pad|>'; tokenizer.add_special_tokens({'pad_token': fallback_pad}); logger.warning(f"Aggiungo pad_token custom: {fallback_pad}")
            if hasattr(tokenizer, 'pad_token_id'): tokenizer.pad_token_id = tokenizer.convert_tokens_to_ids(tokenizer.pad_token)
    except Exception as e: logger.error(f"Errore caricamento tokenizer: {e}", exc_info=True); return

    logger.info(f"Caricamento MODELLO BASE: {args.model_name_or_path}")
    quantization_config = None
    bnb_4bit_compute_dtype = torch.float16
    if args.use_bf16 and torch.cuda.is_available() and torch.cuda.is_bf16_supported(): bnb_4bit_compute_dtype = torch.bfloat16

    if args.load_in_4bit:
        logger.info("Loading base model in 4-bit.")
        quantization_config = BitsAndBytesConfig(load_in_4bit=True, bnb_4bit_compute_dtype=bnb_4bit_compute_dtype, bnb_4bit_use_double_quant=True, bnb_4bit_quant_type="nf4")
    elif args.load_in_8bit:
        logger.info("Loading base model in 8-bit.")
        quantization_config = BitsAndBytesConfig(load_in_8bit=True)

    model_dtype = torch.bfloat16 if args.use_bf16 and not quantization_config and torch.cuda.is_available() and torch.cuda.is_bf16_supported() else torch.float16

    try:
        base_model = AutoModelForCausalLM.from_pretrained(
            args.model_name_or_path,
            trust_remote_code=True,
            quantization_config=quantization_config,
            torch_dtype=model_dtype if not quantization_config else None,
            device_map="auto" # Lascia che gestisca il posizionamento
        )
        logger.info("Modello base caricato.")

        # --- PEFT ---
        # Carica gli adattatori LoRA SUL modello base
        logger.info(f"Caricamento adattatori LoRA da: {args.lora_adapter_path}")
        if not os.path.isdir(args.lora_adapter_path):
             logger.error(f"Directory adattatori LoRA non trovata: {args.lora_adapter_path}"); return

        # Carica il modello PEFT (base + adattatori)
        model = PeftModel.from_pretrained(base_model, args.lora_adapter_path)
        logger.info("Adattatori LoRA applicati al modello base.")

        # Opzionale: Merging degli adapters per inferenza potenzialmente più veloce
        # Se hai abbastanza memoria, puoi fare il merge. Altrimenti usa il modello wrappato.
        # try:
        #     logger.info("Tentativo di fare merge and unload degli adapters...")
        #     model = model.merge_and_unload()
        #     logger.info("Merge and unload completato.")
        # except Exception as e:
        #     logger.warning(f"Merge and unload fallito ({e}). Uso il modello PEFT wrappato.")

        model.eval() # Metti il modello combinato in modalità valutazione

    except Exception as e:
        logger.error(f"Errore caricamento modello base o adattatori LoRA: {e}", exc_info=True)
        return

    # Caricamento dati (come prima)
    logger.info(f"Caricamento dati da: {args.data_file}")
    try:
        with open(args.data_file, 'r', encoding='utf-8') as f: all_data = json.load(f)
        if args.num_samples <= 0 or args.num_samples > len(all_data): samples_to_process = all_data; logger.warning(f"Uso tutti i {len(all_data)} campioni.")
        else: samples_to_process = all_data[:args.num_samples]
        logger.info(f"Processando {len(samples_to_process)} campioni.")
    except Exception as e: logger.error(f"Errore caricamento dati: {e}", exc_info=True); return

    logger.info("Avvio inferenza con modello LoRA fine-tuned...")
    generation_config = {
        "max_new_tokens": args.max_new_tokens,
        "pad_token_id": tokenizer.pad_token_id if tokenizer.pad_token_id is not None else tokenizer.eos_token_id,
        "eos_token_id": tokenizer.eos_token_id,
    }
    if args.do_sample:
         generation_config["temperature"] = args.temperature
         generation_config["top_p"] = args.top_p
         generation_config["do_sample"] = True
    else:
         generation_config["do_sample"] = False


    for i, item in enumerate(tqdm(samples_to_process, desc="Generating Captions (LoRA)")):
        svg_data_string = item.get("xml_data", "")
        true_caption = item.get("caption", "[N/A]")

        if not isinstance(svg_data_string, str) or not svg_data_string.strip(): logger.warning(f"Skipping sample {i}"); continue

        # Determina se il modello base è instruct
        model_is_instruct = "instruct" in args.model_name_or_path.lower()

        if model_is_instruct: prompt = format_instruct_prompt(tokenizer, svg_data_string)
        else: logger.info(f"Model {args.model_name_or_path} non 'instruct'. Uso input diretto."); prompt = svg_data_string + (tokenizer.eos_token if tokenizer.eos_token else "")

        try:
            inputs = tokenizer(prompt, return_tensors="pt").to(model.device) # Usa model.device dopo PEFT

            with torch.no_grad():
                 outputs = model.generate(**inputs, **generation_config)

            full_decoded_output = tokenizer.decode(outputs[0], skip_special_tokens=True)
            prompt_decoded_for_stripping = tokenizer.decode(inputs['input_ids'][0], skip_special_tokens=True)

            if full_decoded_output.startswith(prompt_decoded_for_stripping):
                generated_part = full_decoded_output[len(prompt_decoded_for_stripping):].strip()
            else:
                 generated_part = "[Could not reliably strip prompt]"; logger.warning(f"Sample {i}: strip prompt fallito.")

            # Stampa risultati
            print("-" * 50)
            print(f"SAMPLE {i+1} (LoRA Tuned)")
            print(f"INPUT SVG String (inizio):\n{svg_data_string[:500]}...")
            print(f"\nGENERATED TEXT (solo parte nuova):\n{generated_part}")
            print(f"\nTRUE Caption (dal dataset):\n{true_caption}")
            print("-" * 50 + "\n")

        except Exception as e:
             logger.error(f"Errore generazione campione {i} [LoRA]: {e}", exc_info=True)
             print(f"Errore per campione {i}. Vedi log.")


    logger.info("Inferenza LoRA completata.")


if __name__ == "__main__":
    main()