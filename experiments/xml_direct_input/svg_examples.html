<!DOCTYPE html>
<html>
<head>
    <title>SVG Examples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
        }
        .caption {
            font-style: italic;
            margin-bottom: 10px;
        }
        .xml {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        h1, h2 {
            color: #333;
        }
    </style>
</head>
<body>
    <h1>SVG Examples from Training Dataset</h1>
    <p>These are examples of SVG images used for training the captioning models.</p>

    <div class="example">
        <h2>Example 1: Mathematical Expression</h2>
        <div class="svg-container">
            <svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M214,256 A88,88,0,1,1,214,253 Z" />
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M261,171 L262,341 Z" />
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M333,171 L334,343 Z" />
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M403,225 L404,288 Z" />
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M473,203 L475,311Z" />
            </svg>
        </div>
        <div class="caption">
            <strong>Caption:</strong> The image depicts the image contains a simple mathematical expression
        </div>
        <div class="xml">
            &lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg"&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:1;opacity:1" d="d=M214,256 A88,88,0,1,1,214,253 Z" /&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:1;opacity:1" d="d=M261,171 L262,341 Z" /&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:1;opacity:1" d="d=M333,171 L334,343 Z" /&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:1;opacity:1" d="d=M403,225 L404,288 Z" /&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:1;opacity:1" d="d=M473,203 L475,311Z" /&gt;
&lt;/svg&gt;
        </div>
    </div>

    <div class="example">
        <h2>Example 2: Geometric Symbol</h2>
        <div class="svg-container">
            <svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:0.6;opacity:1" d="M320,100 L192,100 M320,411 L192,411 M100,117 L100,245 M412,117 L412,245 M469,181 L43,181 M355,181 L256,352 L157,181 M256,352 L256,469 M256,43 L256,171Z" />
            </svg>
        </div>
        <div class="caption">
            <strong>Caption:</strong> The image depicts a geometric symbol that consists of a triangle with a cross at each of its vertices
        </div>
        <div class="xml">
            &lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg"&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:0.6;opacity:1" d="d=M320,100 L192,100 M320,411 L192,411 M100,117 L100,245 M412,117 L412,245 M469,181 L43,181 M355,181 L256,352 L157,181 M256,352 L256,469 M256,43 L256,171Z" /&gt;
&lt;/svg&gt;
        </div>
    </div>

    <div class="example">
        <h2>Example 3: Markdown Text</h2>
        <div class="svg-container">
            <svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:3;opacity:1" d="M238,141 L238,164 L238,141 Z" />
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:3;opacity:1" d="M222,153 L253,153 L222,153 Z" />
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:3;opacity:1" d="M259,153 L290,153 L259,153 Z" />
                <path style="fill:none;stroke:rgb(0,0,0);stroke-width:3;opacity:1" d="M274,141 L274,164 L274,141Z" />
            </svg>
        </div>
        <div class="caption">
            <strong>Caption:</strong> The image depicts this is a markdown text
        </div>
        <div class="xml">
            &lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg"&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:3;opacity:1" d="d=M238,141 L238,164 L238,141 Z" /&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:3;opacity:1" d="d=M222,153 L253,153 L222,153 Z" /&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:3;opacity:1" d="d=M259,153 L290,153 L259,153 Z" /&gt;
&lt;path style="fill:none;stroke:0,0,0;stroke-width:3;opacity:1" d="d=M274,141 L274,164 L274,141Z" /&gt;
&lt;/svg&gt;
        </div>
    </div>

    <div class="example">
        <h2>Example 4: Black Circle with White Dot</h2>
        <div class="svg-container">
            <svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <path style="fill:rgb(0,0,0);stroke:None;stroke-width:1;opacity:1" d="M256,0 C115,0,0,115,0,256 C0,397,115,512,256,512 C397,512,512,397,512,256 C512,115,397,0,256,0 M256,310 C226,310,202,286,202,256 C202,226,226,202,256,202 C286,202,310,226,310,256 C310,286,286,310,256,310Z" />
            </svg>
        </div>
        <div class="caption">
            <strong>Caption:</strong> The image depicts a black circle with a white dot in the center
        </div>
        <div class="xml">
            &lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg"&gt;
&lt;path style="fill:0,0,0;stroke:None;stroke-width:1;opacity:1" d="d=M256,0 C115,0,0,115,0,256 C0,397,115,512,256,512 C397,512,512,397,512,256 C512,115,397,0,256,0 M256,310 C226,310,202,286,202,256 C202,226,226,202,256,202 C286,202,310,226,310,256 C310,286,286,310,256,310Z" /&gt;
&lt;/svg&gt;
        </div>
    </div>

    <div class="example">
        <h2>Example 5: Red Line with Circular Loop</h2>
        <div class="svg-container">
            <svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                <path style="fill:rgb(0,0,0);stroke:rgb(215,127,126);stroke-width:100;opacity:1" d="M384,0 L384,256 M128,0 L128,512 Z" />
                <path style="fill:rgb(255,255,255);stroke:rgb(215,127,126);stroke-width:40;opacity:1" d="M302,256 A82,82,0,1,0,466,256 A82,82,0,1,0,302,256Z" />
            </svg>
        </div>
        <div class="caption">
            <strong>Caption:</strong> The image depicts a simple, geometric shape consisting of a vertical red line with a circular loop at the bottom
        </div>
        <div class="xml">
            &lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg"&gt;
&lt;path style="fill:0,0,0;stroke:215,127,126;stroke-width:100;opacity:1" d="d=M384,0 L384,256 M128,0 L128,512 Z" /&gt;
&lt;path style="fill:255,255,255;stroke:215,127,126;stroke-width:40;opacity:1" d="d=M302,256 A82,82,0,1,0,466,256 A82,82,0,1,0,302,256Z" /&gt;
&lt;/svg&gt;
        </div>
    </div>
</body>
</html>
