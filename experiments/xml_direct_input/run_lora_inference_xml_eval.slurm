#!/bin/bash

#SBATCH --job-name=lora_eval   # Nome del job
#SBATCH --output=/work/tesi_ediluzio/logs/lora_eval_%A_%x.out # Log: %A=Job ID, %x=Job Name
#SBATCH --error=/work/tesi_ediluzio/logs/lora_eval_%A_%x.err
#SBATCH --partition=all_usr_prod     # Usa la stessa partizione
#SBATCH --account=tesi_ediluzio      # Usa lo stesso account
#SBATCH --qos=normal                 # Usa la stessa QoS
# !!! VERIFICA QUESTO CONSTRAINT !!! Deve corrispondere a GPU FUNZIONANTI
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G" # O rimuovi/modifica se necessario
# Richiede UNA SOLA GPU. L'inferenza zero-shot di solito non ne richiede di più.
#SBATCH --gpus=1
# Tempo stimato per inferenza su 2000 campioni
#SBATCH --time=02:00:00              # Es. 2 ore
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8            # CPU e Mem dovrebbero bastare
#SBATCH --mem=64G
#SBATCH --mail-type=FAIL,END         # Ricevi email se fallisce o finisce
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE
################################################################################

PROJECT_ROOT="/work/tesi_ediluzio"
PYTHON_EXEC="${PROJECT_ROOT}/svg_captioning_env/bin/python"
# Assicurati che questo sia il percorso corretto dello script Python modificato!
SCRIPT_PATH="${PROJECT_ROOT}/experiments/xml_direct_input/run_inference_unified.py"

# --- Verifica Ambiente ---
if [ ! -f "$PYTHON_EXEC" ]; then echo "Errore: Python non trovato: ${PYTHON_EXEC}" ; exit 1; fi
if [ ! -f "$SCRIPT_PATH" ]; then echo "Errore: Script Python non trovato: ${SCRIPT_PATH}" ; exit 1; fi
echo "Using Python executable: ${PYTHON_EXEC}"

# Crea cartella logs se non esiste
mkdir -p "${PROJECT_ROOT}/logs"

# --- Configurazione Autenticazione Hugging Face ---
echo "Configurazione autenticazione Hugging Face..."
# Imposta direttamente la variabile di ambiente HF_TOKEN
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# --- Configurazione HF_HOME ---
echo "Configurazione HF_HOME..."
# Imposta direttamente la variabile di ambiente HF_HOME
export HF_HOME="/work/tesi_ediluzio/.cache/huggingface"
echo "Variabile di ambiente HF_HOME impostata a: ${HF_HOME}"

# --- Configurazione PYTHONPATH ---
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="${PROJECT_ROOT}:${PYTHONPATH}"
echo "PYTHONPATH: ${PYTHONPATH}"

# --- Gestione Argomenti ---
MODEL_NAME=${1}  # Nome del modello base (es. meta-llama/Llama-3.1-8B-Instruct)
OUTPUT_FILE=${2} # Percorso del file di output (es. /work/tesi_ediluzio/results/lora_xml/llama31_8b_lora_xml_eval.jsonl)
ADAPTER_PATH=${3} # Percorso dell'adapter LoRA (es. /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_quick_fixed3)

if [ -z "$MODEL_NAME" ] || [ -z "$OUTPUT_FILE" ] || [ -z "$ADAPTER_PATH" ]; then
  echo "Errore: Specificare tutti gli argomenti richiesti."
  echo "Esempio: sbatch $0 meta-llama/Llama-3.1-8B-Instruct /work/tesi_ediluzio/results/lora_xml/llama31_8b_lora_xml_eval.jsonl /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_quick_fixed3"
  exit 1
fi

# Verifica che l'adapter esista
if [ ! -d "$ADAPTER_PATH" ]; then
  echo "Errore: Adapter LoRA non trovato: ${ADAPTER_PATH}"
  exit 1
fi

echo "Avvio Inferenza LoRA con modello: ${MODEL_NAME}"
echo "Adapter LoRA: ${ADAPTER_PATH}"
echo "File di output: ${OUTPUT_FILE}"
echo "Job ID: $SLURM_JOB_ID" ; echo "Nodo: $SLURMD_NODENAME" ; echo "GPU Allocata (da SLURM): $CUDA_VISIBLE_DEVICES"

# --- Comando di Esecuzione ---
echo "Eseguo lo script Python: ${SCRIPT_PATH}"

# Esegui passando gli argomenti allo script Python
${PYTHON_EXEC} ${SCRIPT_PATH} \
    --model_name_or_path "${MODEL_NAME}" \
    --lora_path "${ADAPTER_PATH}" \
    --test_file "${PROJECT_ROOT}/data/processed/xml_format/test_set_final_2k_xml.json" \
    --output_file "${OUTPUT_FILE}" \
    --load_in_8bit \
    --use_custom_tokenizer \
    --num_samples 5

EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
  echo "Inferenza LoRA completata con successo. Risultati salvati in: ${OUTPUT_FILE}"
else
  echo "Inferenza LoRA fallita con codice di errore: $EXIT_CODE"
fi
exit $EXIT_CODE
