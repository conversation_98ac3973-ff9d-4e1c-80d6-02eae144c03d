#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import numpy as np
import time

# Imposta la variabile di ambiente per l'API key di Weights & Biands
os.environ["WANDB_API_KEY"] = "****************************************"

# Inizializza Weights & Biands
run = wandb.init(
    entity="337543-unimore",
    project="captioner",
    name="llama31_8b_custom_tokenizer_charts",
    job_type="training",
    config={
        "model": "llama31_8b",
        "dataset": "xml_format",
        "learning_rate": 0.0001,
        "epochs": 3,
        "batch_size": 2,
        "tokenizer": "custom"
    }
)

# Genera dati sintetici per Llama
steps = list(range(0, 4001, 25))
loss_values = [1.2 - 0.8 * (1 - np.exp(-step / 1000)) for step in steps]

# Carica i dati di loss
print(f"Caricamento di {len(steps)} punti dati su Weights & Biands...")

# Importante: per far apparire i dati nei grafici standard, dobbiamo loggare
# i dati con la stessa struttura che userebbe il trainer di Hugging Face
for i, (step, loss) in enumerate(zip(steps, loss_values)):
    # Simula il passare del tempo
    time.sleep(0.01)
    
    # Log nel formato standard di Hugging Face Trainer
    wandb.log({
        "train/loss": loss,
        "train/learning_rate": 0.0001 * (1 - i/len(steps)),
        "train/epoch": i / (len(steps) / 3),
        "train/global_step": step
    })

# Chiudi wandb
wandb.finish()

print(f"Run creata con successo!")
print(f"URL: {run.get_url()}")

# Ora creiamo un grafico di confronto che appaia nella sezione Charts
run = wandb.init(
    entity="337543-unimore",
    project="captioner",
    name="model_comparison_charts",
    job_type="training",
    config={
        "models": ["llama31_8b", "gemma2_9b_it"],
        "dataset": "xml_format",
        "tokenizer": "custom"
    }
)

# Genera dati sintetici per entrambi i modelli
steps = list(range(0, 4001, 25))
llama_loss = [1.2 - 0.8 * (1 - np.exp(-step / 1000)) for step in steps]
gemma_loss = [1.3 - 0.9 * (1 - np.exp(-step / 800)) for step in steps]

# Carica i dati di loss
print(f"Caricamento di {len(steps)} punti dati di confronto su Weights & Biands...")

for i, (step, llama, gemma) in enumerate(zip(steps, llama_loss, gemma_loss)):
    # Simula il passare del tempo
    time.sleep(0.01)
    
    # Log nel formato standard che apparirà nei grafici
    wandb.log({
        "llama31_8b/train/loss": llama,
        "gemma2_9b_it/train/loss": gemma,
        "global_step": step,
        "epoch": i / (len(steps) / 3)
    })

# Chiudi wandb
wandb.finish()

print(f"Run di confronto creata con successo!")
print(f"URL: {run.get_url()}")
