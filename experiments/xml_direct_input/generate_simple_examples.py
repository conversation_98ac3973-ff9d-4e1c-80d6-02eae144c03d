#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import argparse
import torch
import random
import matplotlib.pyplot as plt
import cairosvg
import io
import numpy as np
from PIL import Image
import wandb
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Genera esempi visivi semplici di SVG")
    parser.add_argument("--data_file", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json",
                        help="File JSON con i dati di test")
    parser.add_argument("--num_examples", type=int, default=5,
                        help="Numero di esempi da generare")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/simple_examples",
                        help="Directory di output per gli esempi visivi")
    parser.add_argument("--use_wandb", action="store_true",
                        help="Usa Weights & Biands per tracciare i risultati")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore",
                        help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner",
                        help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default="simple_visual_examples",
                        help="Nome della run in Weights & Biands")
    return parser.parse_args()

def load_data(data_file):
    """Carica i dati dal file JSON"""
    with open(data_file, 'r') as f:
        data = json.load(f)
    return data

def render_svg(svg_string):
    """Renderizza una stringa SVG come immagine"""
    try:
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'))
        image = Image.open(io.BytesIO(png_data))
        return image
    except Exception as e:
        print(f"Errore durante la renderizzazione dell'SVG: {e}")
        return None

def create_visual_example(svg_content, true_caption, output_path):
    """Crea un'immagine con l'SVG renderizzato e la caption vera"""
    # Renderizza l'SVG
    svg_image = render_svg(svg_content)
    if svg_image is None:
        return None

    # Crea la figura
    fig, ax = plt.subplots(figsize=(12, 10))

    # Mostra l'immagine SVG
    ax.imshow(np.array(svg_image))
    ax.axis('off')

    # Aggiungi la caption vera
    caption_text = f"Caption vera: {true_caption[:300]}..."

    # Aggiungi il testo sotto l'immagine
    plt.figtext(0.5, 0.01, caption_text, wrap=True, horizontalalignment='center', fontsize=10)

    # Salva la figura
    plt.tight_layout(rect=[0, 0.1, 1, 0.95])  # Lascia spazio per il testo
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

    return output_path

def main():
    args = parse_args()

    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"

    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)

    # Inizializza Weights & Biands
    if args.use_wandb:
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=args.wandb_run_name,
            job_type="visualization"
        )

    # Carica i dati
    data = load_data(args.data_file)

    # Seleziona esempi casuali
    examples = random.sample(data, min(args.num_examples, len(data)))

    # Genera esempi visivi
    for i, example in enumerate(examples):
        print(f"Generazione dell'esempio {i+1}/{len(examples)}...")

        # Estrai il contenuto SVG e la caption vera
        svg_content = example["xml"]
        true_caption = example["caption"]

        # Crea l'esempio visivo
        output_path = os.path.join(args.output_dir, f"example_{i+1}.png")

        example_path = create_visual_example(
            svg_content,
            true_caption,
            output_path
        )

        # Carica l'esempio su Weights & Biands
        if args.use_wandb and example_path:
            wandb.log({
                f"example_{i+1}": wandb.Image(
                    example_path,
                    caption=f"Example {i+1}"
                ),
                f"svg_{i+1}": svg_content,
                f"true_caption_{i+1}": true_caption
            })

    # Chiudi Weights & Biands
    if args.use_wandb:
        wandb.finish()

    print(f"Generati {len(examples)} esempi visivi in {args.output_dir}")

if __name__ == "__main__":
    main()
