#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
import numpy as np
import pandas as pd
from tqdm import tqdm

def parse_args():
    parser = argparse.ArgumentParser(description="Crea grafici continui per Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--run_name", type=str, default="SVG Captioner - Continuous Charts", help="Nome della run in Weights & Biands")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza l'API di Weights & Biands
    api = wandb.Api()
    
    # Ottieni tutte le run del progetto
    runs = api.runs(f"{args.wandb_entity}/{args.wandb_project}")
    
    # Filtra le run rilevanti (quelle di training)
    llama_runs = []
    gemma_runs = []
    
    for run in runs:
        if "llama31_8b_lora_xml" in run.name and not "zero_shot" in run.name and not "inference" in run.name:
            llama_runs.append(run)
        elif "gemma2_9b_it_lora_xml" in run.name and not "zero_shot" in run.name and not "inference" in run.name:
            gemma_runs.append(run)
    
    print(f"Trovate {len(llama_runs)} run di Llama e {len(gemma_runs)} run di Gemma")
    
    # Estrai i dati di training
    llama_data = []
    gemma_data = []
    
    # Estrai i dati di Llama
    for run in tqdm(llama_runs, desc="Estrazione dati Llama"):
        try:
            history = run.history()
            if "loss" in history.columns:
                for i, row in history.iterrows():
                    if "loss" in row and "_step" in row:
                        llama_data.append({
                            "step": row["_step"],
                            "loss": row["loss"],
                            "run_name": run.name
                        })
        except Exception as e:
            print(f"Errore nell'estrazione dei dati per la run {run.name}: {e}")
    
    # Estrai i dati di Gemma
    for run in tqdm(gemma_runs, desc="Estrazione dati Gemma"):
        try:
            history = run.history()
            if "loss" in history.columns:
                for i, row in history.iterrows():
                    if "loss" in row and "_step" in row:
                        gemma_data.append({
                            "step": row["_step"],
                            "loss": row["loss"],
                            "run_name": run.name
                        })
        except Exception as e:
            print(f"Errore nell'estrazione dei dati per la run {run.name}: {e}")
    
    # Crea DataFrame
    llama_df = pd.DataFrame(llama_data)
    gemma_df = pd.DataFrame(gemma_data)
    
    # Ordina per step
    if not llama_df.empty:
        llama_df = llama_df.sort_values("step")
    if not gemma_df.empty:
        gemma_df = gemma_df.sort_values("step")
    
    # Crea dati continui
    # Per Llama
    llama_continuous = []
    if not llama_df.empty:
        # Trova il range di step
        min_step = llama_df["step"].min()
        max_step = llama_df["step"].max()
        
        # Crea un array di step continui
        steps = np.arange(min_step, max_step + 1)
        
        # Interpola i valori di loss
        for step in steps:
            # Trova i punti più vicini
            closest = llama_df.iloc[(llama_df["step"] - step).abs().argsort()[:2]]
            
            if len(closest) == 1:
                # Se c'è solo un punto, usa quello
                loss = closest["loss"].values[0]
            else:
                # Interpola tra i due punti più vicini
                x = closest["step"].values
                y = closest["loss"].values
                
                # Interpola linearmente
                if x[0] == x[1]:
                    loss = y[0]
                else:
                    loss = y[0] + (step - x[0]) * (y[1] - y[0]) / (x[1] - x[0])
            
            llama_continuous.append({
                "step": step,
                "loss": loss
            })
    
    # Per Gemma
    gemma_continuous = []
    if not gemma_df.empty:
        # Trova il range di step
        min_step = gemma_df["step"].min()
        max_step = gemma_df["step"].max()
        
        # Crea un array di step continui
        steps = np.arange(min_step, max_step + 1)
        
        # Interpola i valori di loss
        for step in steps:
            # Trova i punti più vicini
            closest = gemma_df.iloc[(gemma_df["step"] - step).abs().argsort()[:2]]
            
            if len(closest) == 1:
                # Se c'è solo un punto, usa quello
                loss = closest["loss"].values[0]
            else:
                # Interpola tra i due punti più vicini
                x = closest["step"].values
                y = closest["loss"].values
                
                # Interpola linearmente
                if x[0] == x[1]:
                    loss = y[0]
                else:
                    loss = y[0] + (step - x[0]) * (y[1] - y[0]) / (x[1] - x[0])
            
            gemma_continuous.append({
                "step": step,
                "loss": loss
            })
    
    # Inizializza Weights & Biands
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.run_name,
        job_type="visualization"
    )
    
    # Carica i dati continui
    print("Caricamento dei dati continui su Weights & Biands...")
    
    # Carica i dati di Llama
    for data in tqdm(llama_continuous, desc="Caricamento dati Llama"):
        wandb.log({
            "step": data["step"],
            "llama_loss": data["loss"]
        })
    
    # Carica i dati di Gemma
    for data in tqdm(gemma_continuous, desc="Caricamento dati Gemma"):
        wandb.log({
            "step": data["step"],
            "gemma_loss": data["loss"]
        })
    
    # Chiudi wandb
    wandb.finish()
    
    print(f"Run '{args.run_name}' creata con successo!")
    print(f"URL: {run.get_url()}")

if __name__ == "__main__":
    main()
