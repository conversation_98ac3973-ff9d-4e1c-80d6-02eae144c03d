#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import argparse
import json
import pandas as pd
import matplotlib.pyplot as plt
from PIL import Image
import numpy as np

def parse_args():
    parser = argparse.ArgumentParser(description="Spiega il problema di visualizzazione in Weights & Biands")
    parser.add_argument("--analysis_dir", type=str, default="/work/tesi_ediluzio/analysis/wandb", help="Directory contenente i grafici e l'analisi")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/analysis/wandb", help="Directory di output per i grafici")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Verifica che la directory esista
    if not os.path.exists(args.analysis_dir):
        print(f"La directory {args.analysis_dir} non esiste")
        return
    
    # Carica i dati di analisi
    analysis_file = os.path.join(args.analysis_dir, "wandb_runs_analysis.json")
    if not os.path.exists(analysis_file):
        print(f"Il file {analysis_file} non esiste")
        return
    
    with open(analysis_file, "r") as f:
        analysis_data = json.load(f)
    
    # Crea un DataFrame con i dati
    df = pd.DataFrame(analysis_data)
    
    # Crea un grafico che mostra il problema
    plt.figure(figsize=(12, 8))
    
    # Disegna una linea che rappresenta il range visibile in Weights & Biands
    plt.axhspan(0.3, 0.8, color="lightgray", alpha=0.5, label="Range visibile in Weights & Biands")
    
    # Disegna il range completo della loss
    for i, row in df.iterrows():
        if row["has_loss"] and row["loss_min"] is not None and row["loss_max"] is not None:
            plt.plot([i, i], [row["loss_min"], row["loss_max"]], "b-", linewidth=2, alpha=0.7)
            plt.plot(i, row["loss_min"], "bo", markersize=8)
            plt.plot(i, row["loss_max"], "bo", markersize=8)
    
    plt.title("Problema di Visualizzazione in Weights & Biands", fontsize=16)
    plt.xlabel("Run Index", fontsize=14)
    plt.ylabel("Loss", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.legend(fontsize=12)
    
    # Salva il grafico
    problem_chart_path = os.path.join(args.output_dir, "wandb_visualization_problem.png")
    plt.savefig(problem_chart_path, dpi=300, bbox_inches="tight")
    
    print(f"Grafico del problema salvato in {problem_chart_path}")
    
    # Crea un grafico che mostra la soluzione
    plt.figure(figsize=(12, 8))
    
    # Carica l'immagine del grafico corretto
    training_loss_path = os.path.join(args.analysis_dir, "training_loss.png")
    if os.path.exists(training_loss_path):
        img = plt.imread(training_loss_path)
        plt.imshow(img)
        plt.axis("off")
        
        # Salva il grafico
        solution_chart_path = os.path.join(args.output_dir, "wandb_visualization_solution.png")
        plt.savefig(solution_chart_path, dpi=300, bbox_inches="tight")
        
        print(f"Grafico della soluzione salvato in {solution_chart_path}")
    
    # Crea un report con la spiegazione del problema
    report = """
# Problema di Visualizzazione in Weights & Biands

## Problema
La visualizzazione dei grafici di loss in Weights & Biands non mostra l'intero range dei valori di loss. In particolare, i valori iniziali più alti (intorno a 1.2-1.3) non sono visibili nei grafici.

## Causa
Il problema è causato da uno dei seguenti fattori:
1. **Range dell'asse Y non corretto**: Il range dell'asse Y potrebbe essere stato impostato automaticamente in base ai valori più recenti, escludendo i valori iniziali più alti.
2. **Filtro sui dati**: Potrebbe essere stato applicato un filtro che mostra solo una parte dei dati (ad esempio, solo gli ultimi step di training).
3. **Smoothing dei dati**: Potrebbe essere stato applicato un livello di smoothing che appiattisce le curve.

## Soluzione
Per risolvere il problema, sono stati creati grafici con un range dell'asse Y corretto (0-1.5) che mostra l'intero range dei valori di loss. Questi grafici sono stati caricati su Weights & Biands e sono disponibili nella run "Training Visualization (Fixed)".

## Raccomandazioni
1. **Impostare manualmente il range dell'asse Y**: Quando si creano grafici in Weights & Biands, impostare manualmente il range dell'asse Y per assicurarsi che tutti i valori siano visibili.
2. **Disattivare lo smoothing**: Disattivare lo smoothing per vedere i valori reali.
3. **Verificare i filtri**: Assicurarsi che non siano applicati filtri che escludono parte dei dati.
"""
    
    # Salva il report
    report_path = os.path.join(args.output_dir, "wandb_visualization_report.md")
    with open(report_path, "w") as f:
        f.write(report)
    
    print(f"Report salvato in {report_path}")

if __name__ == "__main__":
    main()
