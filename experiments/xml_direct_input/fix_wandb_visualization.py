#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description="Ripristina la visualizzazione corretta dei grafici in Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--dashboard_name", type=str, default="SVG Captioner - Training Progress", help="Nome della dashboard da creare")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza l'API di Weights & Biands
    api = wandb.Api()
    
    # Ottieni tutte le run del progetto
    runs = api.runs(f"{args.wandb_entity}/{args.wandb_project}")
    
    # Filtra le run rilevanti (quelle di training)
    training_runs = []
    for run in runs:
        if any(x in run.name for x in ["llama31_8b_lora_xml", "gemma2_9b_it_lora_xml"]):
            if not "zero_shot" in run.name and not "inference" in run.name:
                training_runs.append(run)
    
    print(f"Trovate {len(training_runs)} run di training")
    
    # Crea una nuova run per la dashboard
    wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.dashboard_name,
        job_type="visualization"
    )
    
    # Crea un pannello per la loss di training
    train_loss_panel = wandb.Panel(
        wandb.LinePlot(
            title="Training Loss",
            x="_step",
            y="train_loss",
            smoothing=0.0,  # Nessuno smoothing
            ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
            legend={"position": "bottom"}
        ),
        size=(12, 8)
    )
    
    # Crea un pannello per la loss
    loss_panel = wandb.Panel(
        wandb.LinePlot(
            title="Loss",
            x="_step",
            y="loss",
            smoothing=0.0,  # Nessuno smoothing
            ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
            legend={"position": "bottom"}
        ),
        size=(12, 8)
    )
    
    # Crea un pannello per la best train loss
    best_loss_panel = wandb.Panel(
        wandb.LinePlot(
            title="Best Train Loss",
            x="_step",
            y="best_train_loss",
            smoothing=0.0,  # Nessuno smoothing
            ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
            legend={"position": "bottom"}
        ),
        size=(12, 8)
    )
    
    # Crea un pannello per il learning rate
    lr_panel = wandb.Panel(
        wandb.LinePlot(
            title="Learning Rate",
            x="_step",
            y="learning_rate",
            smoothing=0.0,  # Nessuno smoothing
            legend={"position": "bottom"}
        ),
        size=(12, 8)
    )
    
    # Crea un pannello per il grad norm
    grad_norm_panel = wandb.Panel(
        wandb.LinePlot(
            title="Gradient Norm",
            x="_step",
            y="grad_norm",
            smoothing=0.0,  # Nessuno smoothing
            legend={"position": "bottom"}
        ),
        size=(12, 8)
    )
    
    # Crea un pannello per l'epoca
    epoch_panel = wandb.Panel(
        wandb.LinePlot(
            title="Epoch",
            x="_step",
            y="epoch",
            smoothing=0.0,  # Nessuno smoothing
            legend={"position": "bottom"}
        ),
        size=(12, 8)
    )
    
    # Crea la dashboard
    dashboard = wandb.Dashboard(
        title=args.dashboard_name,
        description="Dashboard per monitorare il training dei modelli",
        panels=[
            [train_loss_panel, loss_panel, lr_panel],
            [best_loss_panel, grad_norm_panel, epoch_panel]
        ]
    )
    
    # Salva la dashboard
    dashboard.save()
    
    print(f"Dashboard '{args.dashboard_name}' creata con successo!")
    
    # Chiudi wandb
    wandb.finish()

if __name__ == "__main__":
    main()
