#!/bin/bash
#SBATCH --job-name=gemma2_9b_zero_shot
#SBATCH --output=logs/gemma2_9b_zero_shot_comparison_%j.out
#SBATCH --error=logs/gemma2_9b_zero_shot_comparison_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G"
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --time=2:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
TEST_DATA="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_xml.json"
ZERO_SHOT_MODEL="google/gemma-2-9b-it"
FINE_TUNED_MODEL="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence"
OUTPUT_FILE="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/zero_shot_comparison_gemma2_9b_it.json"
NUM_EXAMPLES=10
WANDB_ENTITY="337543-unimore"
WANDB_PROJECT="captioner"
WANDB_RUN_NAME="zero_shot_comparison_gemma2_9b_it"

# Stampa informazioni
echo "Creazione report di confronto zero-shot vs fine-tuned"
echo "Test Data: $TEST_DATA"
echo "Zero-Shot Model: $ZERO_SHOT_MODEL"
echo "Fine-Tuned Model: $FINE_TUNED_MODEL"
echo "Output File: $OUTPUT_FILE"
echo "Num Examples: $NUM_EXAMPLES"
echo "Weights & Biands Entity: $WANDB_ENTITY"
echo "Weights & Biands Project: $WANDB_PROJECT"
echo "Weights & Biands Run Name: $WANDB_RUN_NAME"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocata (da SLURM): $SLURM_GPUS_ON_NODE"

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/create_zero_shot_comparison_report.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/create_zero_shot_comparison_report.py \
    --test_data "$TEST_DATA" \
    --zero_shot_model "$ZERO_SHOT_MODEL" \
    --fine_tuned_model "$FINE_TUNED_MODEL" \
    --output_file "$OUTPUT_FILE" \
    --num_examples "$NUM_EXAMPLES" \
    --wandb_entity "$WANDB_ENTITY" \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_run_name "$WANDB_RUN_NAME" \
    --use_wandb

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Creazione report completata con successo"
else
    echo "Creazione report fallita con codice di errore: $?"
fi
