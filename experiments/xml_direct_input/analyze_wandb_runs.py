#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
import pandas as pd
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns

def parse_args():
    parser = argparse.ArgumentParser(description="Analizza le run di Weights & Biands e diagnostica problemi")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/analysis/wandb", help="Directory di output per i grafici")
    parser.add_argument("--fix_visualization", action="store_true", help="Crea una nuova dashboard con visualizzazione corretta")
    parser.add_argument("--dashboard_name", type=str, default="SVG Captioner - Training Progress (Fixed)", help="Nome della dashboard da creare")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza l'API di Weights & Biands
    api = wandb.Api()
    
    # Ottieni tutte le run del progetto
    runs = api.runs(f"{args.wandb_entity}/{args.wandb_project}")
    
    # Filtra le run rilevanti (quelle di training)
    training_runs = []
    for run in runs:
        if any(x in run.name for x in ["llama31_8b_lora_xml", "gemma2_9b_it_lora_xml"]):
            if not "zero_shot" in run.name and not "inference" in run.name:
                training_runs.append(run)
    
    print(f"Trovate {len(training_runs)} run di training")
    
    # Analizza le run
    run_data = []
    for run in tqdm(training_runs, desc="Analisi delle run"):
        # Ottieni i dati della run
        history = run.history()
        
        # Verifica se ci sono dati di loss
        has_loss = "loss" in history.columns
        has_train_loss = "train_loss" in history.columns
        
        # Calcola il range della loss
        loss_min = history["loss"].min() if has_loss else None
        loss_max = history["loss"].max() if has_loss else None
        
        # Calcola il range della train_loss
        train_loss_min = history["train_loss"].min() if has_train_loss else None
        train_loss_max = history["train_loss"].max() if has_train_loss else None
        
        # Aggiungi i dati alla lista
        run_data.append({
            "run_id": run.id,
            "run_name": run.name,
            "has_loss": has_loss,
            "has_train_loss": has_train_loss,
            "loss_min": loss_min,
            "loss_max": loss_max,
            "train_loss_min": train_loss_min,
            "train_loss_max": train_loss_max,
            "num_steps": len(history)
        })
    
    # Crea un DataFrame con i dati
    df = pd.DataFrame(run_data)
    
    # Salva il DataFrame
    df.to_csv(os.path.join(args.output_dir, "wandb_runs_analysis.csv"), index=False)
    
    print("Analisi delle run completata. Risultati salvati in", os.path.join(args.output_dir, "wandb_runs_analysis.csv"))
    
    # Crea un grafico con i range della loss
    plt.figure(figsize=(12, 8))
    for i, row in df.iterrows():
        if row["has_loss"]:
            plt.plot([i, i], [row["loss_min"], row["loss_max"]], "b-", linewidth=2, alpha=0.7)
            plt.plot(i, row["loss_min"], "bo", markersize=8)
            plt.plot(i, row["loss_max"], "bo", markersize=8)
    
    plt.title("Range della Loss per ogni Run", fontsize=16)
    plt.xlabel("Run Index", fontsize=14)
    plt.ylabel("Loss", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.savefig(os.path.join(args.output_dir, "loss_range.png"), dpi=300, bbox_inches="tight")
    
    # Crea un grafico con i range della train_loss
    plt.figure(figsize=(12, 8))
    for i, row in df.iterrows():
        if row["has_train_loss"]:
            plt.plot([i, i], [row["train_loss_min"], row["train_loss_max"]], "r-", linewidth=2, alpha=0.7)
            plt.plot(i, row["train_loss_min"], "ro", markersize=8)
            plt.plot(i, row["train_loss_max"], "ro", markersize=8)
    
    plt.title("Range della Train Loss per ogni Run", fontsize=16)
    plt.xlabel("Run Index", fontsize=14)
    plt.ylabel("Train Loss", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.savefig(os.path.join(args.output_dir, "train_loss_range.png"), dpi=300, bbox_inches="tight")
    
    # Crea un grafico con il numero di step per ogni run
    plt.figure(figsize=(12, 8))
    plt.bar(range(len(df)), df["num_steps"], color="green", alpha=0.7)
    
    plt.title("Numero di Step per ogni Run", fontsize=16)
    plt.xlabel("Run Index", fontsize=14)
    plt.ylabel("Numero di Step", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.savefig(os.path.join(args.output_dir, "num_steps.png"), dpi=300, bbox_inches="tight")
    
    print("Grafici salvati in", args.output_dir)
    
    # Crea una nuova dashboard con visualizzazione corretta
    if args.fix_visualization:
        print("Creazione di una nuova dashboard con visualizzazione corretta...")
        
        # Crea una nuova run per la dashboard
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=args.dashboard_name,
            job_type="visualization"
        )
        
        # Crea un pannello per la loss di training
        train_loss_panel = wandb.Panel(
            wandb.LinePlot(
                title="Training Loss",
                x="_step",
                y="train_loss",
                smoothing=0.0,  # Nessuno smoothing
                ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
                legend={"position": "bottom"}
            ),
            size=(12, 8)
        )
        
        # Crea un pannello per la loss
        loss_panel = wandb.Panel(
            wandb.LinePlot(
                title="Loss",
                x="_step",
                y="loss",
                smoothing=0.0,  # Nessuno smoothing
                ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
                legend={"position": "bottom"}
            ),
            size=(12, 8)
        )
        
        # Crea un pannello per la best train loss
        best_loss_panel = wandb.Panel(
            wandb.LinePlot(
                title="Best Train Loss",
                x="_step",
                y="best_train_loss",
                smoothing=0.0,  # Nessuno smoothing
                ranges={"y": {"min": 0.0, "max": 1.5}},  # Imposta il range dell'asse Y
                legend={"position": "bottom"}
            ),
            size=(12, 8)
        )
        
        # Crea un pannello per il learning rate
        lr_panel = wandb.Panel(
            wandb.LinePlot(
                title="Learning Rate",
                x="_step",
                y="learning_rate",
                smoothing=0.0,  # Nessuno smoothing
                legend={"position": "bottom"}
            ),
            size=(12, 8)
        )
        
        # Crea un pannello per il grad norm
        grad_norm_panel = wandb.Panel(
            wandb.LinePlot(
                title="Gradient Norm",
                x="_step",
                y="grad_norm",
                smoothing=0.0,  # Nessuno smoothing
                legend={"position": "bottom"}
            ),
            size=(12, 8)
        )
        
        # Crea un pannello per l'epoca
        epoch_panel = wandb.Panel(
            wandb.LinePlot(
                title="Epoch",
                x="_step",
                y="epoch",
                smoothing=0.0,  # Nessuno smoothing
                legend={"position": "bottom"}
            ),
            size=(12, 8)
        )
        
        # Crea la dashboard
        dashboard = wandb.Dashboard(
            title=args.dashboard_name,
            description="Dashboard per monitorare il training dei modelli",
            panels=[
                [train_loss_panel, loss_panel, lr_panel],
                [best_loss_panel, grad_norm_panel, epoch_panel]
            ]
        )
        
        # Salva la dashboard
        dashboard.save()
        
        print(f"Dashboard '{args.dashboard_name}' creata con successo!")
        
        # Chiudi wandb
        wandb.finish()

if __name__ == "__main__":
    main()
