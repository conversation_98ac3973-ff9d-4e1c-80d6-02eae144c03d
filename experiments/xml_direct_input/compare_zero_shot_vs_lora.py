#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per confrontare i risultati zero-shot con i risultati del fine-tuning LoRA.
"""

import argparse
import json
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from tabulate import tabulate

def parse_args():
    parser = argparse.ArgumentParser(description="Confronta i risultati zero-shot con i risultati del fine-tuning LoRA")
    parser.add_argument("--zero_shot_file", type=str, required=True, help="File JSONL con i risultati zero-shot")
    parser.add_argument("--lora_file", type=str, required=True, help="File JSONL con i risultati del fine-tuning LoRA")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i risultati")
    parser.add_argument("--model_name", type=str, required=True, help="Nome del modello")
    parser.add_argument("--num_samples", type=int, default=10, help="Numero di campioni da visualizzare")
    return parser.parse_args()

def load_results(results_file):
    """Carica i risultati dal file JSONL."""
    results = []
    with open(results_file, 'r', encoding='utf-8') as f:
        for line in f:
            results.append(json.loads(line))
    
    return results

def find_common_samples(zero_shot_results, lora_results, num_samples):
    """Trova campioni comuni tra i risultati zero-shot e LoRA."""
    zero_shot_ids = set(r["id"] for r in zero_shot_results)
    lora_ids = set(r["id"] for r in lora_results)
    common_ids = zero_shot_ids.intersection(lora_ids)
    
    if len(common_ids) < num_samples:
        print(f"Attenzione: solo {len(common_ids)} campioni comuni trovati")
        num_samples = len(common_ids)
    
    # Seleziona i primi num_samples campioni comuni
    selected_ids = list(common_ids)[:num_samples]
    
    # Crea un dizionario di risultati per ID
    zero_shot_dict = {r["id"]: r for r in zero_shot_results}
    lora_dict = {r["id"]: r for r in lora_results}
    
    selected_samples = []
    for sample_id in selected_ids:
        selected_samples.append({
            "id": sample_id,
            "true_caption": zero_shot_dict[sample_id]["true_caption"],
            "zero_shot_caption": zero_shot_dict[sample_id]["generated_caption"],
            "lora_caption": lora_dict[sample_id]["generated_caption"]
        })
    
    return selected_samples

def create_comparison_table(selected_samples, model_name):
    """Crea una tabella di confronto."""
    rows = []
    
    for sample in selected_samples:
        row = [
            sample["id"],
            sample["true_caption"],
            sample["zero_shot_caption"],
            sample["lora_caption"]
        ]
        rows.append(row)
    
    # Crea un DataFrame
    columns = ["ID", "Didascalia Vera", f"{model_name} Zero-Shot", f"{model_name} LoRA"]
    df = pd.DataFrame(rows, columns=columns)
    
    return df

def main():
    args = parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica i risultati
    print(f"Caricamento dei risultati zero-shot da {args.zero_shot_file}...")
    zero_shot_results = load_results(args.zero_shot_file)
    print(f"Caricati {len(zero_shot_results)} risultati zero-shot.")
    
    print(f"Caricamento dei risultati LoRA da {args.lora_file}...")
    lora_results = load_results(args.lora_file)
    print(f"Caricati {len(lora_results)} risultati LoRA.")
    
    # Trova campioni comuni
    print(f"Ricerca di {args.num_samples} campioni comuni...")
    selected_samples = find_common_samples(zero_shot_results, lora_results, args.num_samples)
    
    # Crea la tabella di confronto
    print("Creazione della tabella di confronto...")
    df = create_comparison_table(selected_samples, args.model_name)
    
    # Salva la tabella come CSV
    csv_file = os.path.join(args.output_dir, f"{args.model_name}_zero_shot_vs_lora.csv")
    df.to_csv(csv_file, index=False)
    print(f"Tabella salvata in {csv_file}")
    
    # Salva la tabella come HTML
    html_file = os.path.join(args.output_dir, f"{args.model_name}_zero_shot_vs_lora.html")
    df.to_html(html_file, index=False)
    print(f"Tabella HTML salvata in {html_file}")
    
    # Stampa la tabella
    print("\nTabella di confronto:")
    print(tabulate(df, headers="keys", tablefmt="grid", showindex=False))

if __name__ == "__main__":
    main()
