import os
import json
import torch
import clip
import argparse
from PIL import Image
import numpy as np
from tqdm import tqdm
import logging

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_jsonl(file_path):
    """Carica un file JSONL."""
    data = []
    with open(file_path, 'r') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def load_json(file_path):
    """Carica un file JSON."""
    with open(file_path, 'r') as f:
        return json.load(f)

def calculate_clip_score(image_path, text, model, preprocess, device):
    """Calcola il CLIP score tra un'immagine e un testo."""
    try:
        # Carica e preprocessa l'immagine
        image = Image.open(image_path).convert('RGB')
        image_input = preprocess(image).unsqueeze(0).to(device)
        
        # Tokenizza il testo
        text_input = clip.tokenize([text]).to(device)
        
        # Calcola le features
        with torch.no_grad():
            image_features = model.encode_image(image_input)
            text_features = model.encode_text(text_input)
            
            # Normalizza le features
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            # Calcola la similarità
            similarity = (100.0 * image_features @ text_features.T).item()
            
        return similarity
    except Exception as e:
        logger.error(f"Errore nel calcolo del CLIP score per {image_path}: {str(e)}")
        return 0.0

def main():
    parser = argparse.ArgumentParser(description='Valutazione CLIP score per i modelli')
    parser.add_argument('--llama_results', type=str, required=True, help='File JSONL con i risultati di Llama')
    parser.add_argument('--gemma_results', type=str, required=True, help='File JSONL con i risultati di Gemma')
    parser.add_argument('--reference_file', type=str, required=True, help='File JSON con i dati di riferimento')
    parser.add_argument('--output_file', type=str, required=True, help='File di output per i CLIP score')
    args = parser.parse_args()

    # Setup device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Utilizzo device: {device}")

    # Carica il modello CLIP
    logger.info("Caricamento del modello CLIP...")
    model, preprocess = clip.load("ViT-B/32", device=device)
    logger.info("Modello CLIP caricato correttamente")

    # Carica i dati
    logger.info("Caricamento dei dati...")
    llama_results = load_jsonl(args.llama_results)
    gemma_results = load_jsonl(args.gemma_results)
    reference_data = load_json(args.reference_file)

    # Crea un dizionario per accedere facilmente ai dati di riferimento
    reference_dict = {item['id']: item for item in reference_data}

    # Calcola i CLIP score
    logger.info("Calcolo dei CLIP score...")
    results = {
        'llama': {'scores': [], 'mean': 0.0},
        'gemma': {'scores': [], 'mean': 0.0}
    }

    for llama_result, gemma_result in tqdm(zip(llama_results, gemma_results), total=len(llama_results)):
        item_id = llama_result['id']
        reference = reference_dict[item_id]
        
        # Calcola score per Llama
        llama_score = calculate_clip_score(
            reference['image_path'],
            llama_result['generated_caption'],
            model,
            preprocess,
            device
        )
        results['llama']['scores'].append(llama_score)
        
        # Calcola score per Gemma
        gemma_score = calculate_clip_score(
            reference['image_path'],
            gemma_result['generated_caption'],
            model,
            preprocess,
            device
        )
        results['gemma']['scores'].append(gemma_score)

    # Calcola le medie
    results['llama']['mean'] = np.mean(results['llama']['scores'])
    results['gemma']['mean'] = np.mean(results['gemma']['scores'])

    # Salva i risultati
    logger.info("Salvataggio dei risultati...")
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"CLIP score medi - Llama: {results['llama']['mean']:.2f}, Gemma: {results['gemma']['mean']:.2f}")
    logger.info("Valutazione CLIP completata!")

if __name__ == "__main__":
    main() 