#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare un report completo che includa il confronto tra zero-shot e LoRA.
"""

import argparse
import json
import os
import subprocess
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Genera un report completo che includa il confronto tra zero-shot e LoRA")
    parser.add_argument("--zero_shot_dir", type=str, required=True, help="Directory con i risultati zero-shot")
    parser.add_argument("--lora_dir", type=str, required=True, help="Directory con i risultati LoRA")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per il report")
    parser.add_argument("--model_names", type=str, nargs="+", required=True, help="Nomi dei modelli")
    parser.add_argument("--num_samples", type=int, default=10, help="Numero di campioni da visualizzare")
    return parser.parse_args()

def run_command(command):
    """Esegue un comando shell e restituisce l'output."""
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True)
    stdout, stderr = process.communicate()
    
    if process.returncode != 0:
        print(f"Errore nell'esecuzione del comando: {command}")
        print(f"Errore: {stderr.decode('utf-8')}")
        return None
    
    return stdout.decode('utf-8')

def find_result_files(directory, model_name, suffix=""):
    """Trova i file di risultati per un modello specifico."""
    for file in os.listdir(directory):
        if file.endswith(".jsonl") and model_name.lower() in file.lower() and (not suffix or suffix in file):
            return os.path.join(directory, file)
    
    return None

def evaluate_results(result_file, output_dir, model_name, suffix=""):
    """Valuta i risultati e genera le metriche."""
    # Crea il file di output per le metriche
    metrics_file = os.path.join(output_dir, f"{model_name}{suffix}_metrics.json")
    
    # Esegui lo script di valutazione
    command = f"python /work/tesi_ediluzio/experiments/xml_direct_input/evaluate_captions.py --results_file {result_file} --output_file {metrics_file}"
    print(f"Esecuzione del comando: {command}")
    output = run_command(command)
    
    if output:
        print(output)
    
    return metrics_file

def compare_zero_shot_vs_lora(zero_shot_file, lora_file, output_dir, model_name, num_samples):
    """Confronta i risultati zero-shot con i risultati LoRA."""
    # Esegui lo script di confronto
    command = f"python /work/tesi_ediluzio/experiments/xml_direct_input/compare_zero_shot_vs_lora.py --zero_shot_file {zero_shot_file} --lora_file {lora_file} --output_dir {output_dir}/comparisons --model_name {model_name} --num_samples {num_samples}"
    print(f"Esecuzione del comando: {command}")
    output = run_command(command)
    
    if output:
        print(output)
    
    return os.path.join(output_dir, "comparisons", f"{model_name}_zero_shot_vs_lora.html")

def compare_models(metrics_files, model_names, output_dir, suffix=""):
    """Confronta i modelli e genera i grafici."""
    # Esegui lo script di confronto
    command = f"python /work/tesi_ediluzio/experiments/xml_direct_input/compare_models.py --metrics_files {' '.join(metrics_files)} --model_names {' '.join(model_names)} --output_dir {output_dir}/comparison{suffix}"
    print(f"Esecuzione del comando: {command}")
    output = run_command(command)
    
    if output:
        print(output)

def generate_html_report(zero_shot_metrics_files, lora_metrics_files, comparison_files, model_names, output_dir):
    """Genera un report HTML."""
    # Carica le metriche zero-shot
    zero_shot_metrics_dict = {}
    for metrics_file, model_name in zip(zero_shot_metrics_files, model_names):
        with open(metrics_file, 'r', encoding='utf-8') as f:
            metrics = json.load(f)
        zero_shot_metrics_dict[model_name] = metrics["average_metrics"]
    
    # Carica le metriche LoRA
    lora_metrics_dict = {}
    for metrics_file, model_name in zip(lora_metrics_files, model_names):
        if os.path.exists(metrics_file):
            with open(metrics_file, 'r', encoding='utf-8') as f:
                metrics = json.load(f)
            lora_metrics_dict[model_name] = metrics["average_metrics"]
    
    # Crea i DataFrame
    zero_shot_df = pd.DataFrame(zero_shot_metrics_dict).T
    lora_df = pd.DataFrame(lora_metrics_dict).T if lora_metrics_dict else None
    
    # Crea il report HTML
    html_file = os.path.join(output_dir, "comprehensive_report.html")
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>Report Completo Esperimenti SVG Captioning</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }}
        h1, h2, h3 {{
            color: #333;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .metrics-table {{
            margin-top: 20px;
        }}
        .comparison-images {{
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }}
        .comparison-image {{
            margin: 10px;
            text-align: center;
        }}
        .comparison-image img {{
            max-width: 100%;
            height: auto;
        }}
        .tab {{
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
        }}
        .tab button {{
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
        }}
        .tab button:hover {{
            background-color: #ddd;
        }}
        .tab button.active {{
            background-color: #ccc;
        }}
        .tabcontent {{
            display: none;
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-top: none;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Report Completo Esperimenti SVG Captioning</h1>
        <p>Data: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
        
        <div class="tab">
            <button class="tablinks" onclick="openTab(event, 'ZeroShot')" id="defaultOpen">Zero-Shot</button>
            <button class="tablinks" onclick="openTab(event, 'LoRA')">LoRA</button>
            <button class="tablinks" onclick="openTab(event, 'Comparison')">Confronto</button>
        </div>
        
        <div id="ZeroShot" class="tabcontent">
            <h2>Metriche Zero-Shot</h2>
            <div class="metrics-table">
                {zero_shot_df.to_html(classes='table table-striped', float_format='%.4f')}
            </div>
            
            <h2>Grafici di Confronto Zero-Shot</h2>
            <div class="comparison-images">
""")
        
        # Aggiungi i grafici zero-shot
        for metric in zero_shot_df.index:
            f.write(f"""
                <div class="comparison-image">
                    <h3>{metric.upper()}</h3>
                    <img src="comparison_zero_shot/comparison_{metric}.png" alt="Confronto {metric}">
                </div>
""")
        
        # Aggiungi il grafico radar zero-shot
        f.write(f"""
                <div class="comparison-image">
                    <h3>Confronto Radar</h3>
                    <img src="comparison_zero_shot/comparison_radar.png" alt="Confronto Radar">
                </div>
            </div>
        </div>
        
        <div id="LoRA" class="tabcontent">
            <h2>Metriche LoRA</h2>
""")
        
        # Aggiungi le metriche LoRA se disponibili
        if lora_df is not None:
            f.write(f"""
            <div class="metrics-table">
                {lora_df.to_html(classes='table table-striped', float_format='%.4f')}
            </div>
            
            <h2>Grafici di Confronto LoRA</h2>
            <div class="comparison-images">
""")
            
            # Aggiungi i grafici LoRA
            for metric in lora_df.index:
                f.write(f"""
                <div class="comparison-image">
                    <h3>{metric.upper()}</h3>
                    <img src="comparison_lora/comparison_{metric}.png" alt="Confronto {metric}">
                </div>
""")
            
            # Aggiungi il grafico radar LoRA
            f.write(f"""
                <div class="comparison-image">
                    <h3>Confronto Radar</h3>
                    <img src="comparison_lora/comparison_radar.png" alt="Confronto Radar">
                </div>
            </div>
""")
        else:
            f.write(f"""
            <p>Nessun risultato LoRA disponibile al momento.</p>
""")
        
        f.write(f"""
        </div>
        
        <div id="Comparison" class="tabcontent">
            <h2>Confronto Zero-Shot vs LoRA</h2>
""")
        
        # Aggiungi i confronti zero-shot vs LoRA
        for model_name, comparison_file in zip(model_names, comparison_files):
            if os.path.exists(comparison_file):
                f.write(f"""
            <h3>{model_name}</h3>
            <iframe src="comparisons/{os.path.basename(comparison_file)}" width="100%" height="600px"></iframe>
""")
            else:
                f.write(f"""
            <h3>{model_name}</h3>
            <p>Confronto non disponibile al momento.</p>
""")
        
        f.write(f"""
        </div>
        
        <h2>Conclusioni</h2>
        <p>
            Questo report presenta un confronto tra i modelli {', '.join(model_names)} per il task di generazione di didascalie per SVG,
            sia in modalità zero-shot che dopo fine-tuning LoRA.
        </p>
        <p>
            Basandosi sui risultati zero-shot, il modello con le migliori performance è <strong>{zero_shot_df['bleu_4'].idxmax()}</strong> per BLEU-4 e <strong>{zero_shot_df['meteor'].idxmax()}</strong> per METEOR.
        </p>
""")
        
        if lora_df is not None:
            f.write(f"""
        <p>
            Dopo il fine-tuning LoRA, il modello con le migliori performance è <strong>{lora_df['bleu_4'].idxmax()}</strong> per BLEU-4 e <strong>{lora_df['meteor'].idxmax()}</strong> per METEOR.
        </p>
""")
        
        f.write(f"""
    </div>
    
    <script>
        function openTab(evt, tabName) {{
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {{
                tabcontent[i].style.display = "none";
            }}
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {{
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }}
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }}
        
        // Get the element with id="defaultOpen" and click on it
        document.getElementById("defaultOpen").click();
    </script>
</body>
</html>
""")
    
    print(f"Report HTML generato in {html_file}")

def main():
    args = parse_args()
    
    # Crea le directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(os.path.join(args.output_dir, "comparison_zero_shot"), exist_ok=True)
    os.makedirs(os.path.join(args.output_dir, "comparison_lora"), exist_ok=True)
    os.makedirs(os.path.join(args.output_dir, "comparisons"), exist_ok=True)
    
    # Trova i file di risultati zero-shot
    zero_shot_files = []
    for model_name in args.model_names:
        zero_shot_file = find_result_files(args.zero_shot_dir, model_name)
        if zero_shot_file:
            zero_shot_files.append(zero_shot_file)
        else:
            print(f"Nessun file di risultati zero-shot trovato per {model_name}")
            zero_shot_files.append(None)
    
    # Trova i file di risultati LoRA
    lora_files = []
    for model_name in args.model_names:
        lora_file = find_result_files(args.lora_dir, model_name)
        if lora_file:
            lora_files.append(lora_file)
        else:
            print(f"Nessun file di risultati LoRA trovato per {model_name}")
            lora_files.append(None)
    
    # Valuta i risultati zero-shot
    zero_shot_metrics_files = []
    for zero_shot_file, model_name in zip(zero_shot_files, args.model_names):
        if zero_shot_file:
            print(f"\nValutazione dei risultati zero-shot per {model_name}...")
            metrics_file = evaluate_results(zero_shot_file, args.output_dir, model_name, "_zero_shot")
            zero_shot_metrics_files.append(metrics_file)
        else:
            zero_shot_metrics_files.append(None)
    
    # Valuta i risultati LoRA
    lora_metrics_files = []
    for lora_file, model_name in zip(lora_files, args.model_names):
        if lora_file:
            print(f"\nValutazione dei risultati LoRA per {model_name}...")
            metrics_file = evaluate_results(lora_file, args.output_dir, model_name, "_lora")
            lora_metrics_files.append(metrics_file)
        else:
            lora_metrics_files.append(None)
    
    # Confronta i modelli zero-shot
    print("\nConfronto dei modelli zero-shot...")
    valid_metrics_files = [f for f in zero_shot_metrics_files if f is not None]
    valid_model_names = [model_name for model_name, metrics_file in zip(args.model_names, zero_shot_metrics_files) if metrics_file is not None]
    if valid_metrics_files:
        compare_models(valid_metrics_files, valid_model_names, args.output_dir, "_zero_shot")
    
    # Confronta i modelli LoRA
    print("\nConfronto dei modelli LoRA...")
    valid_metrics_files = [f for f in lora_metrics_files if f is not None]
    valid_model_names = [model_name for model_name, metrics_file in zip(args.model_names, lora_metrics_files) if metrics_file is not None]
    if valid_metrics_files:
        compare_models(valid_metrics_files, valid_model_names, args.output_dir, "_lora")
    
    # Confronta zero-shot vs LoRA
    comparison_files = []
    for zero_shot_file, lora_file, model_name in zip(zero_shot_files, lora_files, args.model_names):
        if zero_shot_file and lora_file:
            print(f"\nConfronto zero-shot vs LoRA per {model_name}...")
            comparison_file = compare_zero_shot_vs_lora(zero_shot_file, lora_file, args.output_dir, model_name, args.num_samples)
            comparison_files.append(comparison_file)
        else:
            comparison_files.append(None)
    
    # Genera il report HTML
    print("\nGenerazione del report HTML...")
    generate_html_report(zero_shot_metrics_files, lora_metrics_files, comparison_files, args.model_names, args.output_dir)
    
    print(f"\nReport completo generato in {args.output_dir}")

if __name__ == "__main__":
    main()
