#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm

def parse_args():
    parser = argparse.ArgumentParser(description="Crea grafici corretti per Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/analysis/wandb", help="Directory di output per i grafici")
    parser.add_argument("--upload_to_wandb", action="store_true", help="Carica i grafici su Weights & Biands")
    parser.add_argument("--run_name", type=str, default="Training Charts (Fixed)", help="Nome della run in Weights & Biands")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza l'API di Weights & Biands
    api = wandb.Api()
    
    # Ottieni tutte le run del progetto
    runs = api.runs(f"{args.wandb_entity}/{args.wandb_project}")
    
    # Filtra le run rilevanti (quelle di training)
    training_runs = []
    for run in runs:
        if any(x in run.name for x in ["llama31_8b_lora_xml", "gemma2_9b_it_lora_xml"]):
            if not "zero_shot" in run.name and not "inference" in run.name:
                training_runs.append(run)
    
    print(f"Trovate {len(training_runs)} run di training")
    
    # Estrai i dati di training
    llama_data = []
    gemma_data = []
    
    for run in tqdm(training_runs, desc="Estrazione dati"):
        # Ottieni i dati della run
        try:
            history = run.history()
            
            # Verifica se ci sono dati di loss
            if "loss" in history.columns:
                # Aggiungi i dati alla lista appropriata
                if "llama31_8b" in run.name:
                    for i, row in history.iterrows():
                        if "loss" in row:
                            llama_data.append({
                                "step": row.get("_step", i),
                                "loss": row["loss"],
                                "run_name": run.name
                            })
                elif "gemma2_9b_it" in run.name:
                    for i, row in history.iterrows():
                        if "loss" in row:
                            gemma_data.append({
                                "step": row.get("_step", i),
                                "loss": row["loss"],
                                "run_name": run.name
                            })
        except Exception as e:
            print(f"Errore nell'estrazione dei dati per la run {run.name}: {e}")
    
    # Crea DataFrame
    llama_df = pd.DataFrame(llama_data)
    gemma_df = pd.DataFrame(gemma_data)
    
    # Ordina per step
    if not llama_df.empty:
        llama_df = llama_df.sort_values("step")
    if not gemma_df.empty:
        gemma_df = gemma_df.sort_values("step")
    
    # Crea grafici
    plt.figure(figsize=(12, 8))
    
    if not llama_df.empty:
        plt.plot(llama_df["step"], llama_df["loss"], "b-", label="Llama 3.1 8B", linewidth=2, alpha=0.7)
    
    if not gemma_df.empty:
        plt.plot(gemma_df["step"], gemma_df["loss"], "r-", label="Gemma 2 9B IT", linewidth=2, alpha=0.7)
    
    plt.title("Training Loss", fontsize=16)
    plt.xlabel("Step", fontsize=14)
    plt.ylabel("Loss", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.legend(fontsize=12)
    
    # Imposta il range dell'asse Y
    plt.ylim(0, 1.5)
    
    # Salva il grafico
    loss_chart_path = os.path.join(args.output_dir, "training_loss.png")
    plt.savefig(loss_chart_path, dpi=300, bbox_inches="tight")
    
    print(f"Grafico salvato in {loss_chart_path}")
    
    # Carica i grafici su Weights & Biands
    if args.upload_to_wandb:
        print("Caricamento dei grafici su Weights & Biands...")
        
        # Inizializza Weights & Biands
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=args.run_name,
            job_type="visualization"
        )
        
        # Carica il grafico
        wandb.log({"Training Loss": wandb.Image(loss_chart_path)})
        
        # Crea una tabella con i dati di loss
        llama_table = wandb.Table(columns=["step", "loss", "run_name"])
        for _, row in llama_df.iterrows():
            llama_table.add_data(row["step"], row["loss"], row["run_name"])
        
        gemma_table = wandb.Table(columns=["step", "loss", "run_name"])
        for _, row in gemma_df.iterrows():
            gemma_table.add_data(row["step"], row["loss"], row["run_name"])
        
        # Carica le tabelle
        wandb.log({"Llama 3.1 8B Loss Data": llama_table})
        wandb.log({"Gemma 2 9B IT Loss Data": gemma_table})
        
        # Chiudi wandb
        wandb.finish()
        
        print("Grafici caricati su Weights & Biands")

if __name__ == "__main__":
    main()
