# Nome file: train_xml_slurm.py (modificato per LoRA)
# O salva come: train_lora_8bit.py

import os
import json
import argparse
import logging
from typing import Dict, List, Any

import torch
from torch.utils.data import Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    Trainer,
    TrainingArguments,
    HfArgumentParser,
    BitsAndBytesConfig # Per configurazione quantizzazione
)
# --- PEFT/LoRA ---
# Importa le classi necessarie da PEFT
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training

# Assicurati: pip install transformers torch datasets accelerate bitsandbytes peft

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- CLASSE XmlCaptioningDataset ---
#   (IDENTICA ALLA VERSIONE PRECEDENTE - COPIALA QUI DALLA RISPOSTA ANTERIORE)
#   (Assicurati che gestisca correttamente la formattazione del prompt per il tuo modello)
class XmlCaptioningDataset(Dataset):
    """
    Dataset per caricare coppie (stringa dati SVG, Didascalia) da un singolo file JSON.
    Prepara i dati per un modello Causal LM (decoder-only), gestendo
    la formattazione per modelli base o instruct.
    """
    def __init__(self, json_path: str, tokenizer: AutoTokenizer, max_length: int, model_is_instruct: bool = True):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.model_is_instruct = model_is_instruct

        self.has_chat_template = getattr(self.tokenizer, 'chat_template', None) is not None or \
                                 getattr(self.tokenizer, 'apply_chat_template', None) is not None
        if self.model_is_instruct and not self.has_chat_template:
             logger.warning("model_is_instruct=True ma il tokenizer non sembra avere un chat_template. Uso template manuale User/Assistant.")

        logger.info(f"Caricamento dataset da: {json_path}")
        try:
            with open(json_path, 'r', encoding='utf-8') as f: self.data = json.load(f)
            logger.info(f"Caricati {len(self.data)} campioni.")
        except FileNotFoundError: logger.error(f"File dataset non trovato: {json_path}"); raise
        except json.JSONDecodeError: logger.error(f"Errore nel decodificare JSON da: {json_path}"); raise
        if not self.data: raise ValueError(f"Nessun dato caricato da {json_path}")

        if self.tokenizer.pad_token is None:
            if self.tokenizer.eos_token is not None:
                logger.warning(f"Uso eos_token ({self.tokenizer.eos_token}) come pad_token.")
                self.tokenizer.pad_token = self.tokenizer.eos_token
                if hasattr(self.tokenizer, 'pad_token_id') and hasattr(self.tokenizer, 'eos_token_id'):
                     self.tokenizer.pad_token_id = self.tokenizer.eos_token_id
            else:
                fallback_pad = '<|pad|>'; logger.warning(f"Aggiungo pad_token custom: {fallback_pad}")
                self.tokenizer.add_special_tokens({'pad_token': fallback_pad})
                if hasattr(self.tokenizer, 'pad_token_id'):
                     self.tokenizer.pad_token_id = self.tokenizer.convert_tokens_to_ids(fallback_pad)

    def __len__(self): return len(self.data)
    def __getitem__(self, idx) -> Dict[str, torch.Tensor]:
        item = self.data[idx]; svg_data_string = item.get("xml_data", ""); caption = item.get("caption", "")
        if not isinstance(svg_data_string, str) or not svg_data_string.strip(): svg_data_string = "d=M0 0"; logger.warning(f"Item {idx} xml_data vuoto.")
        if not isinstance(caption, str) or not caption.strip(): caption = " "; logger.warning(f"Item {idx} caption vuota.")
        preprocessed_input = svg_data_string # Placeholder - Adatta se hai un tokenizer custom
        eos = self.tokenizer.eos_token if self.tokenizer.eos_token else "<|endoftext|>"; full_text = ""; prompt_for_masking = ""
        if self.model_is_instruct:
            can_apply_template = self.has_chat_template
            if can_apply_template:
                instruction = f"Generate a caption for the following SVG path data string:\n\n{preprocessed_input}"
                messages = [{"role": "user", "content": instruction}, {"role": "assistant", "content": caption}]; messages_prompt_only = messages[:-1]
                try:
                    full_text = self.tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=False)
                    prompt_for_masking = self.tokenizer.apply_chat_template(messages_prompt_only, tokenize=False, add_generation_prompt=True)
                except Exception as e: logger.warning(f"apply_chat_template fallito ({e}), uso template manuale."); can_apply_template = False
            if not can_apply_template: prompt_for_masking = f"User: Generate a caption for the following SVG path data string:\n\n{preprocessed_input}\nAssistant: "; full_text = prompt_for_masking + caption + eos
        else: prompt_for_masking = preprocessed_input + eos; full_text = prompt_for_masking + caption + eos
        tokenized_inputs = self.tokenizer(full_text, max_length=self.max_length, padding="max_length", truncation=True, return_tensors="pt")
        input_ids = tokenized_inputs["input_ids"].squeeze(0); attention_mask = tokenized_inputs["attention_mask"].squeeze(0); labels = input_ids.clone()
        if self.tokenizer.pad_token_id is not None: labels[labels == self.tokenizer.pad_token_id] = -100
        tokenized_prompt = self.tokenizer(prompt_for_masking, max_length=self.max_length, truncation=True, add_special_tokens=True, return_tensors="pt")
        prompt_len = tokenized_prompt['input_ids'].size(1); labels[:prompt_len] = -100
        return {"input_ids": input_ids, "attention_mask": attention_mask, "labels": labels}

def main():
    parser = argparse.ArgumentParser(description="Train (LoRA 8-bit) Causal LM for SVG Path Data Captioning.")
    parser.add_argument("--config_path", type=str, required=True, help="Path JSON config file.")
    script_args = parser.parse_args()

    logger.info(f"Caricamento configurazione da: {script_args.config_path}")
    try:
        with open(script_args.config_path, 'r', encoding='utf-8') as f: config = json.load(f)
    except Exception as e: logger.error(f"Errore caricamento config: {e}", exc_info=True); return

    # Estrai parametri dalla configurazione
    model_name = config.get("model_name_or_path")
    tokenizer_name = config.get("tokenizer_name", model_name)
    data_file = config.get("data_file")
    max_length = config.get("max_length", 512)
    output_dir = config.get("output_dir", f"runs/lora_8bit_{os.path.basename(model_name).replace('/','_')}")
    model_is_instruct = "instruct" in model_name.lower() if model_name else False
    model_is_instruct = config.get("model_is_instruct", model_is_instruct)

    # Estrai parametri LoRA dalla config (con valori di default)
    lora_r = config.get("lora_r", 16)
    lora_alpha = config.get("lora_alpha", 32)
    lora_dropout = config.get("lora_dropout", 0.05)
    # IMPORTANTISSIMO: Adatta lora_target_modules al tuo modello specifico!
    # Puoi scoprirli stampando model.named_modules() dopo aver caricato il modello base.
    default_targets = ["q_proj", "v_proj"] # Default molto minimale
    lora_target_modules = config.get("lora_target_modules", default_targets)

    if not model_name or not data_file: logger.error("Config manca 'model_name_or_path' o 'data_file'"); return
    if not os.path.isfile(data_file): logger.error(f"'data_file' ({data_file}) non trovato!"); return

    logger.info(f"Caricamento tokenizer: {tokenizer_name}")
    try:
        tokenizer = AutoTokenizer.from_pretrained(tokenizer_name, trust_remote_code=True)
        # Gestione pad token (come prima)
        added_pad_token = False
        if tokenizer.pad_token is None:
            if tokenizer.eos_token is not None: tokenizer.pad_token = tokenizer.eos_token
            else: fallback_pad = '<|pad|>'; tokenizer.add_special_tokens({'pad_token': fallback_pad}); added_pad_token = True
            if hasattr(tokenizer, 'pad_token_id'): tokenizer.pad_token_id = tokenizer.convert_tokens_to_ids(tokenizer.pad_token)

    except Exception as e: logger.error(f"Errore caricamento tokenizer: {e}"); return

    logger.info("Creazione dataset...")
    try:
        train_dataset = XmlCaptioningDataset(json_path=data_file, tokenizer=tokenizer, max_length=max_length, model_is_instruct=model_is_instruct)
        logger.info(f"Dataset creato con {len(train_dataset)} campioni.")
    except Exception as e: logger.error(f"Errore creazione Dataset: {e}", exc_info=True); return
    eval_dataset = None

    # --- CARICAMENTO MODELLO 8-BIT ---
    logger.info(f"Caricamento modello: {model_name} (con load_in_8bit=True)")
    # Configurazione per caricamento 8-bit
    quantization_config = BitsAndBytesConfig(load_in_8bit=True)
    try:
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            trust_remote_code=True,
            quantization_config=quantization_config, # Usa config per 8-bit
            device_map="auto" # Obbligatorio/Raccomandato per k-bit training
        )
        logger.info("Modello caricato in 8-bit con device_map='auto'.")
    except Exception as e: logger.error(f"Errore caricamento modello 8-bit: {e}", exc_info=True); return

    if added_pad_token:
         logger.info("Resize token embeddings per nuovo pad token.")
         model.resize_token_embeddings(len(tokenizer))
         if hasattr(model, 'config') and hasattr(tokenizer, 'pad_token_id'):
              model.config.pad_token_id = tokenizer.pad_token_id

    # --- PREPARAZIONE PEFT/LORA ---
    logger.info("Preparazione modello per K-bit training e aggiunta LoRA adapters...")
    try:
        # 1. Prepara il modello (necessario per gradient checkpointing con k-bit)
        model = prepare_model_for_kbit_training(model)
        logger.info("Modello preparato per k-bit training.")

        # 2. Definisci la configurazione LoRA dai parametri letti dalla config JSON
        lora_config = LoraConfig(
            r=lora_r,
            lora_alpha=lora_alpha,
            target_modules=lora_target_modules,
            lora_dropout=lora_dropout,
            bias="none",
            task_type="CAUSAL_LM"
        )
        logger.info(f"Configurazione LoRA applicata: R={lora_r}, Alpha={lora_alpha}, Dropout={lora_dropout}, Targets={lora_target_modules}")

        # 3. Applica PEFT al modello
        model = get_peft_model(model, lora_config)
        logger.info("Wrapper PEFT applicato al modello.")

        # 4. Stampa i parametri addestrabili
        model.print_trainable_parameters()

    except Exception as e: logger.error(f"Errore durante la configurazione PEFT/LoRA: {e}", exc_info=True); return

    logger.info("Configurazione Training Arguments...")
    parser = HfArgumentParser(TrainingArguments)
    # Rimuovi chiavi specifiche PEFT/LoRA dal dizionario per TrainingArguments
    peft_keys = ["lora_r", "lora_alpha", "lora_dropout", "lora_target_modules"]
    training_args_dict = {k: v for k, v in config.items() if k not in ["model_name_or_path", "tokenizer_name", "data_file", "max_length", "model_is_instruct"] + peft_keys}
    training_args_dict["output_dir"] = output_dir
    training_args_dict["remove_unused_columns"] = False
    try:
        training_args = parser.parse_dict(training_args_dict)[0]
        # Forza gradient checkpointing (anche se prepare_model lo fa, meglio esplicito)
        training_args.gradient_checkpointing = True
        logger.info("Gradient checkpointing impostato a True.")
        if config.get("fp16"): training_args.fp16 = True
        if config.get("bf16"): training_args.bf16 = True

    except Exception as e: logger.error(f"Errore config TrainingArguments: {e}\nDict: {training_args_dict}"); return

    logger.info("Inizializzazione Trainer...")
    trainer = Trainer(
        model=model, # <-- Passa il modello PEFT-wrapped!
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer
    )
    # Necessario per compatibilità tra PEFT k-bit e gradient checkpointing
    if hasattr(model, 'config'): model.config.use_cache = False

    logger.info("Avvio training (LoRA Fine-tuning)...")
    resume_from_checkpoint = None
    if os.path.isdir(training_args.output_dir) and not training_args.overwrite_output_dir:
        last_checkpoint = Trainer.get_last_checkpoint(training_args.output_dir)
        if last_checkpoint: logger.info(f"Trovato checkpoint: {last_checkpoint}. Ripresa."); resume_from_checkpoint = last_checkpoint
        elif len(os.listdir(training_args.output_dir)) > 0: logger.warning(f"Output dir {training_args.output_dir} esiste ma non è checkpoint. Rischio sovrascrittura.")

    try:
        trainer.train(resume_from_checkpoint=resume_from_checkpoint)
        logger.info("Training LoRA completato.")
    except Exception as e: logger.error(f"Errore durante trainer.train() [LoRA]: {e}", exc_info=True); return

    try:
        logger.info("Salvataggio adapters LoRA finali...")
        # Il Trainer con un modello PEFT salva automaticamente solo gli adapters
        trainer.save_model(training_args.output_dir)
        # Puoi anche salvare lo stato completo se necessario
        # trainer.save_state()
        logger.info(f"Adapters LoRA salvati in {training_args.output_dir}")
    except Exception as e: logger.error(f"Errore salvataggio finale [LoRA]: {e}", exc_info=True)

    logger.info("Script LoRA terminato.")

if __name__ == "__main__":
    main()