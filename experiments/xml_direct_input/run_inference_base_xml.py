# experiments/xml_direct_input/run_inference_base_xml.py

import os
import json
import argparse
import logging
from typing import Dict, List, Any

import torch
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    AutoConfig,
    BitsAndBytesConfig, # Opzionale per caricare anche base in 8bit se necessario
    set_seed
)

# Importa le utility e la classe Dataset dallo script di training/utils
# Assicurati che i percorsi siano corretti rispetto a dove esegui lo script
try:
    # Usa lo stesso codice per coerenza
    from shared.svg_core.custom_tokenizer_utils import build_tokenizer, tokenize_svg
    # Riadatta la classe Dataset dallo script di training per usarla qui
    # NOTA: Potrebbe essere meglio mettere la classe Dataset in un file condiviso
    #       e importarla sia qui che nello script di training.
    #       Per ora la ridefiniamo qui con le modifiche necessarie per inferenza.

    # COPIA/INCOLLA la classe XmlDataset dallo script di training qui
    # con una piccola modifica per l'inferenza (solo input_ids/mask)
    class XmlDatasetForInference(Dataset):
        """Dataset per inferenza: carica XML e lo pre-processa con tokenize_svg."""
        def __init__(self, json_path: str, tokenizer: AutoTokenizer, max_length: int):
            self.tokenizer = tokenizer
            self.max_length = max_length
            logger.info(f"Caricamento dataset per inferenza da: {json_path}")
            try:
                with open(json_path, 'r', encoding='utf-8') as f: self.data = json.load(f)
                logger.info(f"Caricati {len(self.data)} campioni.")
            except Exception as e:
                logger.error(f"Errore caricamento dataset {json_path}: {e}")
                raise
            if not self.data: raise ValueError("Nessun dato caricato.")

            if self.tokenizer.pad_token_id is None:
                 logger.warning("PAD TOKEN ID non definito nel tokenizer! Potrebbe causare problemi.")


        def __len__(self): return len(self.data)

        def __getitem__(self, idx) -> Dict[str, Any]:
            item = self.data[idx]
            xml_string_original = item.get("xml", "")
            caption = item.get("caption", "") # Manteniamo caption per riferimento

            if not isinstance(xml_string_original, str) or not xml_string_original.strip():
                 xml_string_original = '<svg></svg>' # Placeholder

            # Pre-processa l'XML con la funzione di Leonardo
            try:
                preprocessed_svg_text = tokenize_svg(xml_string_original)
            except Exception as e:
                logger.error(f"Errore in tokenize_svg per item {idx}: {e}")
                preprocessed_svg_text = "" # Fallback

            # NON aggiungere EOS qui, lo faremo in base al prompt
            input_text = preprocessed_svg_text

            # Tokenizza (SENZA padding qui, lo faremo nel batch o in generate)
            tokenized_output = self.tokenizer(
                input_text,
                max_length=self.max_length,
                truncation=True,
                return_tensors=None, # Liste Python
            )

            # Restituisce gli ID e l'XML originale/caption per riferimento
            return {
                "input_ids": tokenized_output["input_ids"],
                "attention_mask": tokenized_output["attention_mask"], # Potrebbe non servire se ricreato da generate
                "original_xml": xml_string_original,
                "caption": caption
            }

except ImportError as e:
    print(f"Errore nell'importare moduli necessari: {e}")
    print("Assicurati che shared/svg_core/custom_tokenizer_utils.py esista e sia nel PYTHONPATH.")
    exit(1)
except Exception as e:
    print(f"Errore inatteso durante l'import o definizione classe: {e}")
    exit(1)


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_inference(args):
    set_seed(args.seed)
    device = torch.device(args.device if torch.cuda.is_available() and args.device.startswith("cuda") else "cpu")
    logger.info(f"Using device: {device}")

    # --- TOKENIZER ---
    logger.info(f"Loading tokenizer: {args.tokenizer_name}")
    try:
        tokenizer = AutoTokenizer.from_pretrained(args.tokenizer_name, trust_remote_code=True, use_fast=False)
        tokenizer = build_tokenizer(tokenizer) # Aggiungi token custom

        # Configura pad token per generation (padding a sinistra è cruciale)
        if tokenizer.pad_token_id is None:
            if tokenizer.eos_token_id is not None:
                tokenizer.pad_token = tokenizer.eos_token
            else:
                tokenizer.add_special_tokens({'pad_token': '<|pad|>'})
        tokenizer.padding_side = "left" # Fondamentale per generation!

        logger.info(f"Tokenizer loaded. Vocab size: {len(tokenizer)}, Pad token ID: {tokenizer.pad_token_id}")
    except Exception as e:
        logger.error(f"Failed to load tokenizer: {e}", exc_info=True)
        return

    # --- MODELLO BASE (NO LoRA) ---
    logger.info(f"Loading BASE model: {args.model_name_or_path}")
    model_load_kwargs = {"trust_remote_code": True}
    if args.use_8bit:
        logger.info("Loading model in 8-bit.")
        quantization_config = BitsAndBytesConfig(load_in_8bit=True)
        model_load_kwargs["quantization_config"] = quantization_config
        model_load_kwargs["device_map"] = "auto" # Necessario per 8bit
    else:
        # Carica normalmente (es. bf16/fp16)
        dtype = torch.bfloat16 if args.bf16 else (torch.float16 if args.fp16 else torch.float32)
        logger.info(f"Loading model with dtype: {dtype}")
        model_load_kwargs["torch_dtype"] = dtype
        # Non usare device_map='auto' se non serve o se si sposta manualmente
        # model_load_kwargs["device_map"] = "auto"

    try:
        # Carica config per pad_token_id prima del modello
        model_config = AutoConfig.from_pretrained(args.model_name_or_path, trust_remote_code=True)
        if model_config.pad_token_id is None:
            model_config.pad_token_id = tokenizer.pad_token_id

        model = AutoModelForCausalLM.from_pretrained(
            args.model_name_or_path,
            config=model_config,
            **model_load_kwargs
        )

        # --- Ridimensiona embeddings DOPO caricamento modello ---
        logger.info("Resizing model token embeddings...")
        model.resize_token_embeddings(len(tokenizer))
        logger.info(f"Model embedding size after resizing: {model.get_input_embeddings().weight.size(0)}")
        if model.get_input_embeddings().weight.size(0) != len(tokenizer):
             logger.error("FATAL: Model embedding size differs from tokenizer vocab size!")
             return

        # Sposta su device se non usato device_map
        if "device_map" not in model_load_kwargs:
            model.to(device)

        model.eval() # Modalità valutazione
        logger.info("Base model loaded successfully.")
    except Exception as e:
        logger.error(f"Failed to load base model: {e}", exc_info=True)
        return

    # --- DATASET ---
    try:
        dataset = XmlDatasetForInference( # Usa la classe definita sopra
            json_path=args.dataset_json_path,
            tokenizer=tokenizer,
            max_length=args.max_length # Max length per tokenizzare input
        )
        # Prendi un subset se richiesto
        indices = list(range(min(args.num_samples, len(dataset))))
        subset = torch.utils.data.Subset(dataset, indices)
        # NOTA: DataLoader qui non è strettamente necessario se batch_size=1,
        # ma lo usiamo per coerenza se si volesse aumentare batch_size in futuro.
        # Il padding verrà gestito da model.generate, quindi non serve un collator complesso.
        dataloader = DataLoader(subset, batch_size=args.batch_size)
        logger.info(f"Dataloader ready for {len(subset)} samples.")
    except Exception as e:
        logger.error(f"Failed to load dataset: {e}", exc_info=True)
        return

    # --- GENERATION ---
    results = []
    logger.info(f"Starting generation for {len(subset)} samples...")
    for batch in tqdm(dataloader, desc="Generating"):
        # Estrai input IDs dal batch (senza padding applicato dal dataloader)
        # Assumiamo batch_size=1 per semplicità qui nel prompt truncation
        # Se batch_size > 1, questa parte va adattata per gestire il batch
        if args.batch_size > 1:
            logger.warning("Batch size > 1 non completamente testato in questo script per la creazione del prompt. Procedo assumendo batch_size=1 per logica prompt.")

        original_xml = batch["original_xml"][0] # Assume batch_size=1
        caption = batch["caption"][0] # Assume batch_size=1
        input_ids_full = batch["input_ids"][0].tolist() # Lista di ID, assume batch_size=1

        # --- Creazione Prompt (Esempio: XML Completion) ---
        # Prendiamo una porzione iniziale dell'SVG pre-processato come prompt
        # Es: primi N token o fino a un certo token speciale
        prompt_max_len = args.prompt_max_len # Es. 128 o 256
        prompt_ids = input_ids_full[:prompt_max_len]

        # Verifica che il prompt non sia vuoto
        if not prompt_ids:
            logger.warning("Prompt vuoto generato per un campione, skipping.")
            continue

        # Converti prompt_ids in tensore PyTorch e sposta su device
        input_ids_prompt = torch.tensor([prompt_ids], dtype=torch.long).to(device)
        # Crea attention mask per il prompt (tutti 1)
        attention_mask_prompt = torch.ones_like(input_ids_prompt)
        # --------------------------------------------------

        # Genera sequenza
        with torch.no_grad(): # Disabilita calcolo gradienti per inferenza
            try:
                # Parametri generazione
                gen_kwargs = {
                    "max_new_tokens": args.max_new_tokens,
                    "pad_token_id": tokenizer.pad_token_id,
                    "eos_token_id": tokenizer.eos_token_id,
                    "do_sample": args.do_sample,
                    "temperature": args.temperature,
                    "top_k": args.top_k,
                    "top_p": args.top_p,
                    # Aggiungi altri parametri se necessario (num_beams, etc.)
                }
                outputs = model.generate(
                    input_ids=input_ids_prompt,
                    attention_mask=attention_mask_prompt,
                    **gen_kwargs
                )
                # outputs contiene l'intera sequenza (prompt + generazione)
                # Decodifica solo la parte generata (togliendo il prompt iniziale)
                generated_ids = outputs[0][input_ids_prompt.shape[1]:]
                generated_text = tokenizer.decode(generated_ids, skip_special_tokens=False) # False per vedere i token <|...|>

                # Decodifica anche il prompt per riferimento
                prompt_text = tokenizer.decode(prompt_ids, skip_special_tokens=False)

                results.append({
                    "input_prompt_tokens": prompt_ids,
                    "input_prompt_text": prompt_text,
                    "generated_ids": generated_ids.tolist(),
                    "generated_text": generated_text,
                    "original_xml": original_xml,
                    "caption": caption
                })
            except Exception as e:
                logger.error(f"Errore durante model.generate: {e}", exc_info=True)
                results.append({
                     "input_prompt_tokens": prompt_ids,
                     "input_prompt_text": tokenizer.decode(prompt_ids, skip_special_tokens=False),
                     "generated_ids": [],
                     "generated_text": f"ERROR: {e}",
                     "original_xml": original_xml,
                     "caption": caption
                })

    # --- SALVA RISULTATI ---
    logger.info(f"Salvataggio risultati in: {args.output_file}")
    try:
        os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
        with open(args.output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logger.info("Risultati salvati con successo.")
    except Exception as e:
        logger.error(f"Errore salvataggio risultati: {e}", exc_info=True)

    logger.info("Script inferenza base terminato.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run Base Model Inference for SVG XML data with Custom Tokenizer.")
    parser.add_argument("--model_name_or_path", type=str, required=True, help="Path/name of the base model (e.g., nvidia/nemotron-4b-instruct).")
    parser.add_argument("--tokenizer_name", type=str, default=None, help="Path/name of the tokenizer (defaults to model name).")
    parser.add_argument("--dataset_json_path", type=str, required=True, help="Path to the NEW filtered dataset JSON file.")
    parser.add_argument("--output_file", type=str, required=True, help="Path to save the generation results (JSON).")
    parser.add_argument("--max_length", type=int, default=2048, help="Max sequence length for tokenization context.")
    parser.add_argument("--prompt_max_len", type=int, default=128, help="Maximum number of tokens from the input SVG sequence to use as prompt.")
    parser.add_argument("--max_new_tokens", type=int, default=512, help="Maximum number of new tokens to generate.")
    parser.add_argument("--num_samples", type=int, default=10, help="Number of samples from the dataset to run inference on.")
    parser.add_argument("--batch_size", type=int, default=1, help="Batch size for inference (currently logic assumes 1 for prompt creation).")
    parser.add_argument("--device", type=str, default="cuda:0", help="Device to use (e.g., 'cuda:0', 'cpu').")
    parser.add_argument("--seed", type=int, default=42, help="Random seed.")
    parser.add_argument("--use_8bit", action='store_true', help="Load model in 8-bit using BitsAndBytes.")
    parser.add_argument("--fp16", action='store_true', help="Use float16 precision.")
    parser.add_argument("--bf16", action='store_true', help="Use bfloat16 precision (recommended on Ampere+).")
    # Generation parameters
    parser.add_argument("--do_sample", action='store_true', help="Use sampling for generation.")
    parser.add_argument("--temperature", type=float, default=0.7, help="Temperature for sampling.")
    parser.add_argument("--top_k", type=int, default=50, help="Top-k for sampling.")
    parser.add_argument("--top_p", type=float, default=0.95, help="Top-p (nucleus) sampling.")

    args = parser.parse_args()

    # Set tokenizer name default
    if args.tokenizer_name is None:
        args.tokenizer_name = args.model_name_or_path

    run_inference(args)