#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
import json
import time

def parse_args():
    parser = argparse.ArgumentParser(description="Risolvi il problema di visualizzazione in Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--run_name", type=str, default="SVG Captioner - Fixed Charts", help="Nome della run in Weights & Biands")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza Weights & Biands
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.run_name,
        job_type="visualization"
    )
    
    # Carica le immagini dei grafici
    wandb.log({
        "Training Loss (Fixed)": wandb.Image("/work/tesi_ediluzio/analysis/wandb/training_loss.png"),
        "Llama 3.1 8B Training Loss (Fixed)": wandb.Image("/work/tesi_ediluzio/analysis/wandb/llama_training_loss.png"),
        "Gemma 2 9B IT Training Loss (Fixed)": wandb.Image("/work/tesi_ediluzio/analysis/wandb/gemma_training_loss.png"),
        "Visualization Problem": wandb.Image("/work/tesi_ediluzio/analysis/wandb/wandb_visualization_problem.png")
    })
    
    # Crea una tabella con la spiegazione del problema
    problem_table = wandb.Table(columns=["Problema", "Causa", "Soluzione"])
    problem_table.add_data(
        "Visualizzazione incompleta della loss",
        "Range dell'asse Y non corretto o filtro sui dati",
        "Creazione di grafici con range dell'asse Y corretto (0-1.5)"
    )
    wandb.log({"Analisi del Problema": problem_table})
    
    # Crea una tabella con le raccomandazioni
    recommendations_table = wandb.Table(columns=["Raccomandazione", "Descrizione"])
    recommendations_table.add_data(
        "Impostare manualmente il range dell'asse Y",
        "Quando si creano grafici in Weights & Biands, impostare manualmente il range dell'asse Y per assicurarsi che tutti i valori siano visibili."
    )
    recommendations_table.add_data(
        "Disattivare lo smoothing",
        "Disattivare lo smoothing per vedere i valori reali."
    )
    recommendations_table.add_data(
        "Verificare i filtri",
        "Assicurarsi che non siano applicati filtri che escludono parte dei dati."
    )
    wandb.log({"Raccomandazioni": recommendations_table})
    
    # Crea dati sintetici per mostrare il problema
    steps = list(range(1000))
    
    # Crea una loss che parte da 1.3 e scende a 0.3
    loss_values = [1.3 - (1.0 * i / 999) for i in range(1000)]
    
    # Crea una loss che parte da 1.2 e scende a 0.4
    loss_values2 = [1.2 - (0.8 * i / 999) for i in range(1000)]
    
    # Carica i dati
    for i in range(len(steps)):
        wandb.log({
            "step": steps[i],
            "synthetic_loss_gemma": loss_values[i],
            "synthetic_loss_llama": loss_values2[i]
        })
        # Aggiungi un piccolo ritardo per evitare di sovraccaricare l'API
        if i % 100 == 0:
            time.sleep(0.1)
    
    # Chiudi wandb
    wandb.finish()
    
    print(f"Run '{args.run_name}' creata con successo!")
    print(f"URL: {run.get_url()}")

if __name__ == "__main__":
    main()
