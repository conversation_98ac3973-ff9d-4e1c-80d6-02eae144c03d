#!/bin/bash
#SBATCH --job-name=job_monitor
#SBATCH --output=/work/tesi_ediluzio/logs/job_monitor_%j.out
#SBATCH --error=/work/tesi_ediluzio/logs/job_monitor_%j.err
#SBATCH --time=4:00:00
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=1
#SBATCH --mem=4G
#SBATCH --partition=all_serial
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal

# Imposta l'ambiente
source /work/tesi_ediluzio/svg_captioning_env/bin/activate

# Esegui lo script di monitoraggio
python /work/tesi_ediluzio/experiments/xml_direct_input/monitor_and_relaunch_jobs.py --check_interval 3600 --max_relaunches 10
