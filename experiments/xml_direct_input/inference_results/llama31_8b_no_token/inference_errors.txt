2025-04-22 11:58:59,599 - INFO - Caricamento tokenizer: meta-llama/Llama-3.1-8B-Instruct
2025-04-22 11:59:01,170 - INFO - Setting pad_token to eos_token: <|eot_id|>
2025-04-22 11:59:01,170 - INFO - Caricamento MODELLO BASE: meta-llama/Llama-3.1-8B-Instruct
2025-04-22 11:59:01,764 - INFO - Loading base model in 4-bit.
2025-04-22 11:59:08,693 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:22<01:08, 22.70s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:47<00:47, 23.89s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [01:10<00:23, 23.60s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [01:13<00:00, 15.47s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [01:13<00:00, 18.42s/it]
2025-04-22 12:00:22,784 - INFO - Modello base caricato.
2025-04-22 12:00:22,784 - INFO - Caricamento adattatori LoRA da: /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence
2025-04-22 12:00:22,787 - ERROR - Errore caricamento modello base o adattatori LoRA: Can't find 'adapter_config.json' at '/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence'
Traceback (most recent call last):
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/peft/config.py", line 260, in _get_peft_type
    config_file = hf_hub_download(
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/work/tesi_ediluzio/experiments/xml_direct_input/run_inference_lora.py", line 111, in main
    model = PeftModel.from_pretrained(base_model, args.lora_adapter_path)
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/peft/peft_model.py", line 439, in from_pretrained
    PeftConfig._get_peft_type(
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/peft/config.py", line 266, in _get_peft_type
    raise ValueError(f"Can't find '{CONFIG_NAME}' at '{model_id}'")
ValueError: Can't find 'adapter_config.json' at '/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence'
