2025-04-22 12:03:41,284 - INFO - Caricamento tokenizer: google/gemma-2-9b-it
2025-04-22 12:03:43,799 - INFO - Caricamento MODELLO BASE: google/gemma-2-9b-it
2025-04-22 12:03:44,080 - INFO - Loading base model in 4-bit.
2025-04-22 12:03:47,269 - INFO - We will use 90% of the memory on device 0 for storing the model, and 10% for the buffer to avoid OOM. You can set `max_memory` in to a higher value to use more memory (at your own risk).

Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:22<01:07, 22.57s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:44<00:44, 22.12s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [01:07<00:22, 22.71s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [01:24<00:00, 20.19s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [01:24<00:00, 21.03s/it]
2025-04-22 12:05:11,823 - INFO - Modello base caricato.
2025-04-22 12:05:11,823 - INFO - Caricamento adattatori LoRA da: /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence
2025-04-22 12:05:11,827 - ERROR - Errore caricamento modello base o adattatori LoRA: Can't find 'adapter_config.json' at '/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence'
Traceback (most recent call last):
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/peft/config.py", line 260, in _get_peft_type
    config_file = hf_hub_download(
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/huggingface_hub/utils/_validators.py", line 106, in _inner_fn
    validate_repo_id(arg_value)
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/huggingface_hub/utils/_validators.py", line 154, in validate_repo_id
    raise HFValidationError(
huggingface_hub.errors.HFValidationError: Repo id must be in the form 'repo_name' or 'namespace/repo_name': '/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence'. Use `repo_type` argument if needed.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/work/tesi_ediluzio/experiments/xml_direct_input/run_inference_lora.py", line 111, in main
    model = PeftModel.from_pretrained(base_model, args.lora_adapter_path)
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/peft/peft_model.py", line 439, in from_pretrained
    PeftConfig._get_peft_type(
  File "/work/tesi_ediluzio/svg_captioning_env/lib/python3.9/site-packages/peft/config.py", line 266, in _get_peft_type
    raise ValueError(f"Can't find '{CONFIG_NAME}' at '{model_id}'")
ValueError: Can't find 'adapter_config.json' at '/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence'
