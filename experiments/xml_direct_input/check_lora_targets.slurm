#!/bin/bash

#SBATCH --job-name=check_lora_targets  # Nome del job
#SBATCH --output=/work/tesi_ediluzio/logs/check_lora_targets_%j_%x_arg1.out # Log con nome job e argomento
#SBATCH --error=/work/tesi_ediluzio/logs/check_lora_targets_%j_%x_arg1.err  # Log con nome job e argomento
#SBATCH --partition=all_usr_prod         # Usa la stessa partizione del training
#SBATCH --account=tesi_ediluzio          # Usa lo stesso account
#SBATCH --qos=normal                     # Usa la stessa QoS
# !!! VERIFICA QUESTO CONSTRAINT !!! Deve corrispondere a GPU FUNZIONANTI disponibili
# Potrebbe essere A40/L40S oppure P100 a seconda del nodo/partizione
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G" # O rimuovi se non serve/non corretto
# Richiede UNA SOLA GPU. Caricare in 8bit di solito non richiede più GPU.
# Semplifica anche la selezione per evitare quella con ECC error.
#SBATCH --gpus=1
# Tempo sufficiente per caricare il modello (15-30 min dovrebbero bastare)
#SBATCH --time=00:20:00
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
# CPU e Memoria dovrebbero essere sufficienti per caricare il modello
#SBATCH --cpus-per-task=4
#SBATCH --mem=32G                         # Ridotta RAM CPU, potrebbe bastare
#SBATCH --mail-type=FAIL                  # Ricevi email solo se fallisce (opzionale)
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE
################################################################################

PROJECT_ROOT="/work/tesi_ediluzio"
PYTHON_EXEC="${PROJECT_ROOT}/svg_captioning_env/bin/python"
SCRIPT_PATH="${PROJECT_ROOT}/experiments/xml_direct_input/check_lora_targets.py"

# --- Verifica Ambiente ---
if [ ! -f "$PYTHON_EXEC" ]; then
    echo "Errore: Eseguibile Python non trovato in ${PYTHON_EXEC}" ; exit 1
fi
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "Errore: Script Python check_lora_targets.py non trovato in ${SCRIPT_PATH}" ; exit 1
fi
echo "Using Python executable: ${PYTHON_EXEC}"
${PYTHON_EXEC} --version

# Crea cartella logs se non esiste
mkdir -p "${PROJECT_ROOT}/logs"

# --- Gestione Argomento: Nome del Modello ---
MODEL_NAME_OR_PATH=${1} # Legge il PRIMO argomento passato a sbatch

if [ -z "$MODEL_NAME_OR_PATH" ]; then
  echo "Errore: Specificare il nome/percorso del modello Hugging Face come primo argomento."
  echo "Esempio: sbatch $0 meta-llama/Llama-3.1-8B-Instruct"
  exit 1
fi

echo "Controllo LoRA Targets per il modello: ${MODEL_NAME_OR_PATH}"
echo "Job ID: $SLURM_JOB_ID" ; echo "Nodo: $SLURMD_NODENAME"

# !!! SELEZIONE GPU MANUALE (SE NECESSARIO) !!!
# Se devi assicurarti di usare una GPU specifica (es. ID 1) perché le altre
# potrebbero avere l'errore ECC, decommenta e imposta la riga seguente.
# Altrimenti, SLURM userà la singola GPU che ti ha assegnato (che speriamo sia buona).
# export CUDA_VISIBLE_DEVICES=1
# echo "Forzato l'uso della GPU: $CUDA_VISIBLE_DEVICES"
# ------------------------------------------------

# --- Comando di Esecuzione ---
echo "Eseguo lo script: ${SCRIPT_PATH}"

# Esegui passando il nome del modello come argomento --model_name_or_path
${PYTHON_EXEC} ${SCRIPT_PATH} \
    --model_name_or_path "${MODEL_NAME_OR_PATH}" # Passa l'argomento allo script Python

EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
  echo "Controllo LoRA Targets completato con successo per ${MODEL_NAME_OR_PATH}."
else
  echo "Controllo LoRA Targets fallito con codice di errore: $EXIT_CODE per ${MODEL_NAME_OR_PATH}"
fi
exit $EXIT_CODE