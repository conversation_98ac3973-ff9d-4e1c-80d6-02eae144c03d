#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import logging
import argparse
from typing import Dict, List, Optional, Any

import torch
import wandb
from datasets import load_dataset
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    TrainerCallback,
)
from peft import (
    LoraConfig,
    get_peft_model,
    prepare_model_for_kbit_training,
    TaskType,
    PeftModel,
)

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Callback personalizzato per eliminare i checkpoint vecchi
class CheckpointCleanupCallback(TrainerCallback):
    def __init__(self, save_total_limit=None):
        self.save_total_limit = save_total_limit
        self.checkpoints = []

    def on_save(self, args, state, control, **kwargs):
        if self.save_total_limit is None or self.save_total_limit <= 0:
            return

        # Aggiungi il checkpoint corrente alla lista
        checkpoint_path = os.path.join(args.output_dir, f"checkpoint-{state.global_step}")
        self.checkpoints.append((state.global_step, checkpoint_path))

        # Ordina i checkpoint per step (dal più vecchio al più recente)
        self.checkpoints.sort(key=lambda x: x[0])

        # Rimuovi i checkpoint più vecchi se superiamo il limite
        while len(self.checkpoints) > self.save_total_limit:
            _, checkpoint_to_remove = self.checkpoints.pop(0)
            if os.path.exists(checkpoint_to_remove):
                logger.info(f"Rimozione checkpoint vecchio: {checkpoint_to_remove}")
                import shutil
                shutil.rmtree(checkpoint_to_remove)

# Callback per early stopping
class EarlyStoppingCallback(TrainerCallback):
    def __init__(self, patience=3, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.best_loss = float('inf')
        self.counter = 0
        self.should_stop = False

    def on_log(self, args, state, control, logs=None, **kwargs):
        if logs and 'loss' in logs:
            current_loss = logs['loss']

            # Se la loss è migliorata significativamente, resetta il contatore
            if self.best_loss - current_loss > self.min_delta:
                self.best_loss = current_loss
                self.counter = 0
                logger.info(f"Loss migliorata a {current_loss:.4f}")
            else:
                self.counter += 1
                logger.info(f"Loss non migliorata. Contatore: {self.counter}/{self.patience}")

                # Se la loss non è migliorata per 'patience' step consecutivi, ferma il training
                if self.counter >= self.patience:
                    logger.info(f"Early stopping attivato dopo {self.counter} step senza miglioramenti")
                    self.should_stop = True
                    control.should_training_stop = True

# Classe per il callback di Weights & Biands
class WandbCallback(TrainerCallback):
    def __init__(self, project_name, run_name, config):
        self.project_name = project_name
        self.run_name = run_name
        self.config = config
        self.initialized = False
        self.best_loss = float('inf')
        self.best_step = 0

    def on_init(self):
        if not self.initialized:
            try:
                logger.info(f"Inizializzazione wandb con entity={self.config.get('entity', '337543-unimore')}, project={self.project_name}, name={self.run_name}")
                wandb.init(
                    entity=self.config.get("entity", "337543-unimore"),
                    project=self.project_name,
                    name=self.run_name,
                    config=self.config
                )
                logger.info(f"wandb.init completato con successo. Run ID: {wandb.run.id}, Mode: {wandb.run.mode}")
                self.initialized = True
            except Exception as e:
                logger.error(f"Errore durante l'inizializzazione di wandb: {e}")
                import traceback
                logger.error(traceback.format_exc())

    def on_log(self, args, state, control, logs=None, **kwargs):
        if logs:
            # Traccia esplicitamente la loss di training
            if 'loss' in logs:
                logs['train_loss'] = logs['loss']

                # Traccia la best loss
                if logs['loss'] < self.best_loss:
                    self.best_loss = logs['loss']
                    self.best_step = state.global_step
                    logs['best_train_loss'] = self.best_loss
                    logs['best_train_loss_step'] = self.best_step

            wandb.log(logs, step=state.global_step)

    def on_evaluate(self, args, state, control, metrics=None, **kwargs):
        if metrics:
            # Prefisso "eval_" per distinguere le metriche di valutazione
            eval_metrics = {f"eval_{k}": v for k, v in metrics.items()}
            wandb.log(eval_metrics, step=state.global_step)

    def on_train_end(self, args, state, control, **kwargs):
        if self.initialized:
            # Log del riepilogo finale
            wandb.log({
                "final_train_loss": self.best_loss,
                "final_train_step": state.global_step if state else 0,
                "best_train_loss": self.best_loss,
                "best_train_loss_step": self.best_step
            })
            wandb.finish()

def parse_args():
    parser = argparse.ArgumentParser(description="Fine-tuning con LoRA e training multi-GPU")
    parser.add_argument("--model_name_or_path", type=str, required=True, help="Path o nome del modello pre-addestrato")
    parser.add_argument("--data_file", type=str, required=True, help="Path al file di training in formato JSON")
    parser.add_argument("--config_path", type=str, required=True, help="Path al file di configurazione JSON")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i checkpoint")
    parser.add_argument("--best_checkpoint_dir", type=str, default=None, help="Directory del miglior checkpoint")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands (username o team)")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default=None, help="Nome della run in Weights & Biands")
    parser.add_argument("--use_wandb", action="store_true", help="Usa Weights & Biands per il tracking")
    parser.add_argument("--early_stopping", action="store_true", help="Attiva early stopping")
    parser.add_argument("--patience", type=int, default=10, help="Numero di step senza miglioramenti prima di fermare il training")
    parser.add_argument("--min_delta", type=float, default=0.001, help="Miglioramento minimo della loss per resettare il contatore")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    with open(config_path, "r") as f:
        config = json.load(f)
    return config

def tokenize_svg(examples, tokenizer, max_length=512):
    prompts = []

    # Ottieni le liste di SVG e caption
    svgs = examples.get("xml", [])
    captions = examples.get("caption", [])

    # Verifica che le liste abbiano la stessa lunghezza
    if len(svgs) != len(captions):
        raise ValueError(f"Il numero di SVG ({len(svgs)}) non corrisponde al numero di caption ({len(captions)})")

    # Crea i prompt
    for svg, caption in zip(svgs, captions):
        # Crea il prompt nel formato corretto per il modello
        prompt = f"<s>[INST] Descrivi questa immagine SVG:\n{svg} [/INST] {caption}</s>"
        prompts.append(prompt)

    # Tokenizza con padding alla lunghezza massima
    tokenized = tokenizer(
        prompts,
        padding="max_length",
        truncation=True,
        max_length=max_length,
        return_tensors="pt"
    )

    # Imposta gli input_ids come labels per il training
    tokenized["labels"] = tokenized["input_ids"].clone()

    return tokenized

def main():
    args = parse_args()

    # Carica la configurazione
    config = load_config(args.config_path)
    logger.info(f"Configurazione caricata da {args.config_path}")

    # Imposta la variabile di ambiente per il token di Hugging Face
    os.environ["HF_TOKEN"] = "*************************************"

    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        args.model_name_or_path,
        token=os.environ.get("HF_TOKEN"),
        trust_remote_code=True
    )

    # Assicurati che il tokenizer abbia un token di padding
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Configura la quantizzazione
    compute_dtype = getattr(torch, config.get("compute_dtype", "float16"))

    quantization_config = None
    if config.get("load_in_8bit", False):
        logger.info("Utilizzo della quantizzazione a 8-bit")
        quantization_config = BitsAndBytesConfig(
            load_in_8bit=True,
            llm_int8_threshold=config.get("llm_int8_threshold", 6.0),
            llm_int8_has_fp16_weight=config.get("llm_int8_has_fp16_weight", False)
        )
    elif config.get("load_in_4bit", False):
        logger.info("Utilizzo della quantizzazione a 4-bit")
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=compute_dtype,
            bnb_4bit_use_double_quant=config.get("bnb_4bit_use_double_quant", True),
            bnb_4bit_quant_type=config.get("bnb_4bit_quant_type", "nf4")
        )

    # Carica il modello
    logger.info(f"Caricamento del modello: {args.model_name_or_path}")
    # Usa l'implementazione 'eager' dell'attenzione per Gemma2 come raccomandato
    attn_implementation = "eager" if "gemma" in args.model_name_or_path.lower() else None
    logger.info(f"Usando implementazione dell'attenzione: {attn_implementation if attn_implementation else 'default'}")

    # Carica il modello su CPU prima di distribuirlo
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name_or_path,
        quantization_config=quantization_config,
        device_map="auto",  # Usa auto per distribuire automaticamente su tutte le GPU disponibili
        token=os.environ.get("HF_TOKEN"),
        trust_remote_code=True,
        use_cache=False,  # Disabilita l'uso della cache per risparmiare memoria
        attn_implementation=attn_implementation,  # Usa 'eager' per Gemma2
        torch_dtype=torch.bfloat16  # Forza bfloat16 per risparmiare memoria
    )

    # Prepara il modello per il training con quantizzazione
    if config.get("load_in_8bit", False) or config.get("load_in_4bit", False):
        logger.info("Preparazione del modello per il training con quantizzazione")
        # Usa un approccio più conservativo per la memoria
        if "gemma" in args.model_name_or_path.lower():
            logger.info("Usando approccio a basso consumo di memoria per Gemma")
            # Non convertire i parametri in float32 durante la preparazione
            model.config.pretraining_tp = 1
            for param in model.parameters():
                if param.ndim == 1:  # Solo per i bias
                    param.data = param.data.to(torch.float32)
            model.gradient_checkpointing_enable()
        else:
            model = prepare_model_for_kbit_training(model, use_gradient_checkpointing=True)

    # Abilita il gradient checkpointing per risparmiare memoria
    if config.get("gradient_checkpointing", False):
        logger.info("Abilitazione del gradient checkpointing")
        model.gradient_checkpointing_enable()

    # Configura LoRA
    logger.info("Configurazione LoRA")
    lora_config = LoraConfig(
        r=config.get("lora_r", 16),
        lora_alpha=config.get("lora_alpha", 32),
        target_modules=config.get("lora_target_modules", ["q_proj", "v_proj"]),
        lora_dropout=config.get("lora_dropout", 0.05),
        bias=config.get("lora_bias", "none"),
        task_type=TaskType.CAUSAL_LM
    )

    # Applica LoRA al modello
    logger.info("Applicazione di LoRA al modello")
    model = get_peft_model(model, lora_config)

    # Carica i pesi LoRA dal miglior checkpoint se specificato
    if args.best_checkpoint_dir:
        logger.info(f"Caricamento dei pesi LoRA dal miglior checkpoint: {args.best_checkpoint_dir}")
        
        # Cerca il file adapter_model.bin o adapter_model.safetensors
        adapter_path = None
        if os.path.exists(os.path.join(args.best_checkpoint_dir, "adapter_model.bin")):
            adapter_path = os.path.join(args.best_checkpoint_dir, "adapter_model.bin")
            logger.info(f"Trovato file adapter_model.bin: {adapter_path}")
        elif os.path.exists(os.path.join(args.best_checkpoint_dir, "adapter_model.safetensors")):
            adapter_path = os.path.join(args.best_checkpoint_dir, "adapter_model.safetensors")
            logger.info(f"Trovato file adapter_model.safetensors: {adapter_path}")
        else:
            logger.warning(f"Non è stato trovato alcun file adapter_model nel checkpoint: {args.best_checkpoint_dir}")
            
        if adapter_path:
            try:
                # Carica i pesi dell'adapter
                if adapter_path.endswith(".safetensors"):
                    logger.info(f"Caricamento dei pesi da file safetensors: {adapter_path}")
                    from safetensors.torch import load_file
                    adapter_state_dict = load_file(adapter_path)
                else:
                    logger.info(f"Caricamento dei pesi da file bin: {adapter_path}")
                    adapter_state_dict = torch.load(adapter_path, map_location="cpu")
                
                # Carica i pesi nel modello
                missing_keys, unexpected_keys = model.load_state_dict(adapter_state_dict, strict=False)
                logger.info(f"Pesi caricati. Missing keys: {len(missing_keys)}, Unexpected keys: {len(unexpected_keys)}")
            except Exception as e:
                logger.error(f"Errore durante il caricamento dei pesi: {e}")
                import traceback
                logger.error(traceback.format_exc())

    # Stampa il numero di parametri addestrabili
    trainable_params = 0
    all_params = 0
    for _, param in model.named_parameters():
        all_params += param.numel()
        if param.requires_grad:
            trainable_params += param.numel()

    logger.info(f"Parametri addestrabili: {trainable_params}")
    logger.info(f"Tutti i parametri: {all_params}")
    logger.info(f"Percentuale parametri addestrabili: {100 * trainable_params / all_params:.4f}%")

    # Carica il dataset
    logger.info(f"Caricamento del dataset: {args.data_file}")
    dataset = load_dataset("json", data_files={"train": args.data_file})

    # Tokenizza il dataset
    logger.info("Tokenizzazione del dataset")
    tokenized_dataset = dataset.map(
        lambda examples: tokenize_svg(examples, tokenizer, max_length=config.get("max_length", 512)),
        batched=True,
        remove_columns=dataset["train"].column_names
    )

    # Configura gli argomenti di training
    logger.info("Configurazione degli argomenti di training")

    # Ottieni il numero di GPU disponibili
    num_gpus = torch.cuda.device_count()
    logger.info(f"Numero di GPU disponibili: {num_gpus}")

    # Configura DeepSpeed se specificato
    deepspeed_config = config.get("deepspeed", None)
    if deepspeed_config is not None:
        logger.info(f"Utilizzo di DeepSpeed con configurazione: {deepspeed_config}")

    training_args = TrainingArguments(
        output_dir=args.output_dir,
        per_device_train_batch_size=config.get("per_device_train_batch_size", 1),
        per_device_eval_batch_size=config.get("per_device_eval_batch_size", 1),
        gradient_accumulation_steps=config.get("gradient_accumulation_steps", 16),
        learning_rate=float(config.get("learning_rate", 2e-4)),
        num_train_epochs=float(config.get("num_train_epochs", 3.0)),
        max_steps=config.get("max_steps", -1),
        logging_steps=config.get("logging_steps", 10),
        save_steps=config.get("save_steps", 200),
        save_total_limit=config.get("save_total_limit", 3),
        optim=config.get("optim", "paged_adamw_8bit"),
        lr_scheduler_type=config.get("lr_scheduler_type", "cosine"),
        warmup_steps=config.get("warmup_steps", 0),
        warmup_ratio=float(config.get("warmup_ratio", 0.03)),
        weight_decay=float(config.get("weight_decay", 0.001)),
        fp16=config.get("fp16", False),
        bf16=config.get("bf16", True),
        gradient_checkpointing=config.get("gradient_checkpointing", True),
        gradient_checkpointing_kwargs={"use_reentrant": False},
        remove_unused_columns=config.get("remove_unused_columns", False),
        report_to="none",  # Disabilita il reporting integrato, useremo il nostro callback
        ddp_find_unused_parameters=config.get("ddp_find_unused_parameters", False),
        ddp_bucket_cap_mb=config.get("ddp_bucket_cap_mb", 128),
        dataloader_pin_memory=config.get("dataloader_pin_memory", True),
        dataloader_num_workers=config.get("dataloader_num_workers", 4),
        group_by_length=config.get("group_by_length", True),
        deepspeed=deepspeed_config
    )

    # Crea il data collator
    data_collator = DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)

    # Configura Weights & Biands
    wandb_run_name = args.wandb_run_name or f"{os.path.basename(args.model_name_or_path)}_lora_xml_multi_gpu_simple"

    # Crea i callback
    checkpoint_callback = CheckpointCleanupCallback(save_total_limit=config.get("save_total_limit", 3))
    early_stopping_callback = None
    if args.early_stopping:
        early_stopping_callback = EarlyStoppingCallback(patience=args.patience, min_delta=args.min_delta)
        logger.info(f"Early stopping attivato con patience={args.patience}, min_delta={args.min_delta}")

    # Forza l'uso di wandb indipendentemente dal flag
    logger.info("Forzando l'uso di wandb indipendentemente dal flag --use_wandb")
    wandb_callback = WandbCallback(
        project_name=args.wandb_project,
        run_name=wandb_run_name,
        config={
            "entity": args.wandb_entity,
            "model_name": args.model_name_or_path,
            "dataset": args.data_file,
            "best_checkpoint_dir": args.best_checkpoint_dir,
            "early_stopping": args.early_stopping,
            "patience": args.patience if args.early_stopping else None,
            "min_delta": args.min_delta if args.early_stopping else None,
            "trainable_params": trainable_params,
            "trainable_percentage": 100 * trainable_params / all_params,
            **config
        }
    )

    # Inizializza Weights & Biands
    if wandb_callback:
        wandb_callback.on_init()
        logger.info(f"Weights & Biands inizializzato con entity={args.wandb_entity}, project={args.wandb_project}, run_name={wandb_run_name}")
        logger.info(f"Weights & Biands mode: {wandb.run.mode}")

    # Crea il trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset["train"],
        data_collator=data_collator,
    )

    # Aggiungi i nostri callback personalizzati
    callbacks = []

    # Aggiungi il callback di checkpoint
    callbacks.append(checkpoint_callback)

    # Aggiungi Weights & Biands callback se richiesto
    if wandb_callback:
        callbacks.append(wandb_callback)

    # Aggiungi early stopping se richiesto
    if early_stopping_callback:
        callbacks.append(early_stopping_callback)

    # Aggiungi i callback al trainer
    for callback in callbacks:
        trainer.add_callback(callback)

    # Avvia il training
    logger.info("Avvio del training")
    trainer.train()

    # Salva il modello finale
    logger.info(f"Salvataggio del modello finale in {args.output_dir}")
    trainer.save_model(args.output_dir)

    # Chiudi Weights & Biands
    if wandb_callback:
        wandb_callback.on_train_end(None, None, None)

    logger.info("Training completato")

if __name__ == "__main__":
    main()
