#!/bin/bash
#SBATCH --job-name=gemma2_9b_large_batch
#SBATCH --output=logs/gemma2_9b_it_no_token_large_batch_%j.out
#SBATCH --error=logs/gemma2_9b_it_no_token_large_batch_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=16
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G"
#SBATCH --gres=gpu:1
#SBATCH --mem=64G
#SBATCH --time=24:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
MODEL_NAME="google/gemma-2-9b-it"
DATA_FILE="/work/tesi_ediluzio/data/processed/xml_format/train_set_final_xml.json"
CONFIG_PATH="/work/tesi_ediluzio/experiments/xml_direct_input/configs/gemma2_9b_it_lora_xml_no_token_large_batch.json"
OUTPUT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence"
ADAPTER_PATH="$OUTPUT_DIR/checkpoint-2200/adapter_model.safetensors"
WANDB_ENTITY="337543-unimore"
WANDB_PROJECT="captioner"
WANDB_RUN_NAME="gemma2_9b_it_lora_xml_no_token_large_batch"
EARLY_STOPPING="--early_stopping"
PATIENCE=20
MIN_DELTA=0.0005

# Stampa informazioni
echo "Training LoRA con Batch Size Grande"
echo "Modello: $MODEL_NAME"
echo "Dataset: $DATA_FILE"
echo "Config: $CONFIG_PATH"
echo "Output: $OUTPUT_DIR"
echo "Adapter Path: $ADAPTER_PATH"
echo "Weights & Biands Entity: $WANDB_ENTITY"
echo "Weights & Biands Project: $WANDB_PROJECT"
echo "Weights & Biands Run Name: $WANDB_RUN_NAME"
echo "Early Stopping: $EARLY_STOPPING"
echo "Patience: $PATIENCE"
echo "Min Delta: $MIN_DELTA"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocata (da SLURM): $SLURM_GPUS_ON_NODE"

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/train_lora_wandb.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/train_lora_wandb.py \
    --model_name_or_path "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --resume_from_checkpoint "$ADAPTER_PATH" \
    --wandb_entity "$WANDB_ENTITY" \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_run_name "$WANDB_RUN_NAME" \
    $EARLY_STOPPING \
    --patience "$PATIENCE" \
    --min_delta "$MIN_DELTA" \
    --use_wandb

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Training LoRA completato con successo"
else
    echo "Training LoRA fallito con codice di errore: $?"
fi
