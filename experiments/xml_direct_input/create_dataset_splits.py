#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import argparse
import random
import numpy as np
import matplotlib.pyplot as plt

def parse_args():
    parser = argparse.ArgumentParser(description="Crea split di train, validation e test per ogni fascia del dataset")
    parser.add_argument("--bins_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_analysis",
                        help="Directory contenente i file delle fasce")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_splits",
                        help="Directory di output per gli split")
    parser.add_argument("--train_ratio", type=float, default=0.7,
                        help="Percentuale di dati da usare per il training")
    parser.add_argument("--val_ratio", type=float, default=0.15,
                        help="Percentuale di dati da usare per la validazione")
    parser.add_argument("--test_ratio", type=float, default=0.15,
                        help="Percentuale di dati da usare per il test")
    parser.add_argument("--seed", type=int, default=42,
                        help="Seed per la generazione casuale")
    return parser.parse_args()

def main():
    args = parse_args()

    # Imposta il seed per la riproducibilità
    random.seed(args.seed)
    np.random.seed(args.seed)

    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)

    # Carica i metadati delle fasce
    metadata_file = os.path.join(args.bins_dir, "bins_metadata.json")
    with open(metadata_file, 'r', encoding='utf-8') as f:
        metadata = json.load(f)

    num_bins = metadata["num_bins"]
    print(f"Numero di fasce: {num_bins}")

    # Inizializza gli split
    train_data = []
    val_data = []
    test_data = []

    # Per ogni fascia, crea gli split
    for bin_id in range(num_bins):
        bin_file = os.path.join(args.bins_dir, f"bin_{bin_id+1}.json")
        with open(bin_file, 'r', encoding='utf-8') as f:
            bin_data = json.load(f)

        examples = bin_data["examples"]
        random.shuffle(examples)

        num_examples = len(examples)
        num_train = int(num_examples * args.train_ratio)
        num_val = int(num_examples * args.val_ratio)

        train_examples = examples[:num_train]
        val_examples = examples[num_train:num_train+num_val]
        test_examples = examples[num_train+num_val:]

        print(f"Fascia {bin_id+1}: {len(train_examples)} train, {len(val_examples)} val, {len(test_examples)} test")

        train_data.extend(train_examples)
        val_data.extend(val_examples)
        test_data.extend(test_examples)

    # Mescola gli esempi all'interno di ogni split
    random.shuffle(train_data)
    random.shuffle(val_data)
    random.shuffle(test_data)

    print(f"Totale: {len(train_data)} train, {len(val_data)} val, {len(test_data)} test")

    # Salva gli split
    train_file = os.path.join(args.output_dir, "train.json")
    val_file = os.path.join(args.output_dir, "val.json")
    test_file = os.path.join(args.output_dir, "test.json")

    # Converti in formato adatto per il training
    train_formatted = []
    for item in train_data:
        train_formatted.append({
            "xml": item["svg"],
            "caption": item["caption"]
        })

    val_formatted = []
    for item in val_data:
        val_formatted.append({
            "xml": item["svg"],
            "caption": item["caption"]
        })

    test_formatted = []
    for item in test_data:
        test_formatted.append({
            "xml": item["svg"],
            "caption": item["caption"]
        })

    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_formatted, f)
    print(f"Split di training salvato in {train_file}")

    with open(val_file, 'w', encoding='utf-8') as f:
        json.dump(val_formatted, f)
    print(f"Split di validazione salvato in {val_file}")

    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_formatted, f)
    print(f"Split di test salvato in {test_file}")

    # Crea un file di metadati con le informazioni sugli split
    splits_metadata = {
        "train_size": len(train_data),
        "val_size": len(val_data),
        "test_size": len(test_data),
        "train_ratio": args.train_ratio,
        "val_ratio": args.val_ratio,
        "test_ratio": args.test_ratio,
        "seed": args.seed
    }

    metadata_file = os.path.join(args.output_dir, "splits_metadata.json")
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(splits_metadata, f, indent=2)
    print(f"Metadati degli split salvati in {metadata_file}")

    # Crea un grafico a torta con la distribuzione degli esempi negli split
    plt.figure(figsize=(10, 8))
    plt.pie([len(train_data), len(val_data), len(test_data)],
            labels=["Train", "Validation", "Test"],
            autopct='%1.1f%%')
    plt.title("Distribuzione degli esempi negli split")
    plt.savefig(os.path.join(args.output_dir, "splits_distribution.png"))
    print(f"Grafico a torta salvato in {os.path.join(args.output_dir, 'splits_distribution.png')}")

    # Analizza la distribuzione delle fasce in ogni split
    train_bins = [item.get("bin_id", 0) for item in train_data]
    val_bins = [item.get("bin_id", 0) for item in val_data]
    test_bins = [item.get("bin_id", 0) for item in test_data]

    train_bin_counts = [train_bins.count(i) for i in range(num_bins)]
    val_bin_counts = [val_bins.count(i) for i in range(num_bins)]
    test_bin_counts = [test_bins.count(i) for i in range(num_bins)]

    bin_labels = [f"Fascia {i+1}" for i in range(num_bins)]

    plt.figure(figsize=(12, 8))

    x = np.arange(len(bin_labels))
    width = 0.25

    plt.bar(x - width, train_bin_counts, width, label='Train')
    plt.bar(x, val_bin_counts, width, label='Validation')
    plt.bar(x + width, test_bin_counts, width, label='Test')

    plt.xlabel('Fasce')
    plt.ylabel('Numero di esempi')
    plt.title('Distribuzione delle fasce negli split')
    plt.xticks(x, bin_labels)
    plt.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(args.output_dir, "bins_in_splits.png"))
    print(f"Grafico a barre salvato in {os.path.join(args.output_dir, 'bins_in_splits.png')}")

    print("Creazione degli split completata!")

if __name__ == "__main__":
    main()
