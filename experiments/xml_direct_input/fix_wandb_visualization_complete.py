#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import argparse
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm
import json
import numpy as np

def parse_args():
    parser = argparse.ArgumentParser(description="Analizza e risolvi problemi di visualizzazione in Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/analysis/wandb", help="Directory di output per i grafici")
    parser.add_argument("--upload_to_wandb", action="store_true", help="Carica i grafici su Weights & Biands")
    parser.add_argument("--run_name", type=str, default="Training Visualization (Fixed)", help="Nome della run in Weights & Biands")
    parser.add_argument("--analyze_only", action="store_true", help="Solo analisi, senza creare grafici")
    parser.add_argument("--fix_charts", action="store_true", help="Crea grafici corretti")
    parser.add_argument("--create_dashboard", action="store_true", help="Crea una dashboard in Weights & Biands")
    return parser.parse_args()

def analyze_runs(api, entity, project, output_dir):
    """Analizza le run di Weights & Biands"""
    print("Analisi delle run di Weights & Biands...")
    
    # Ottieni tutte le run del progetto
    runs = api.runs(f"{entity}/{project}")
    
    # Filtra le run rilevanti (quelle di training)
    training_runs = []
    for run in runs:
        if any(x in run.name for x in ["llama31_8b_lora_xml", "gemma2_9b_it_lora_xml"]):
            if not "zero_shot" in run.name and not "inference" in run.name:
                training_runs.append(run)
    
    print(f"Trovate {len(training_runs)} run di training")
    
    # Analizza le run
    run_data = []
    for run in tqdm(training_runs, desc="Analisi delle run"):
        try:
            # Ottieni i dati della run
            history = run.history()
            
            # Verifica se ci sono dati di loss
            has_loss = "loss" in history.columns
            has_train_loss = "train_loss" in history.columns
            
            # Calcola il range della loss
            loss_min = history["loss"].min() if has_loss else None
            loss_max = history["loss"].max() if has_loss else None
            
            # Calcola il range della train_loss
            train_loss_min = history["train_loss"].min() if has_train_loss else None
            train_loss_max = history["train_loss"].max() if has_train_loss else None
            
            # Aggiungi i dati alla lista
            run_data.append({
                "run_id": run.id,
                "run_name": run.name,
                "has_loss": has_loss,
                "has_train_loss": has_train_loss,
                "loss_min": loss_min,
                "loss_max": loss_max,
                "train_loss_min": train_loss_min,
                "train_loss_max": train_loss_max,
                "num_steps": len(history)
            })
        except Exception as e:
            print(f"Errore nell'analisi della run {run.name}: {e}")
    
    # Crea un DataFrame con i dati
    df = pd.DataFrame(run_data)
    
    # Salva il DataFrame
    df.to_csv(os.path.join(output_dir, "wandb_runs_analysis.csv"), index=False)
    
    # Salva anche in formato JSON per una migliore leggibilità
    with open(os.path.join(output_dir, "wandb_runs_analysis.json"), "w") as f:
        json.dump(run_data, f, indent=4)
    
    print("Analisi delle run completata. Risultati salvati in", os.path.join(output_dir, "wandb_runs_analysis.csv"))
    
    return df, training_runs

def create_charts(df, output_dir):
    """Crea grafici di analisi"""
    print("Creazione dei grafici di analisi...")
    
    # Crea un grafico con i range della loss
    plt.figure(figsize=(12, 8))
    for i, row in df.iterrows():
        if row["has_loss"]:
            plt.plot([i, i], [row["loss_min"], row["loss_max"]], "b-", linewidth=2, alpha=0.7)
            plt.plot(i, row["loss_min"], "bo", markersize=8)
            plt.plot(i, row["loss_max"], "bo", markersize=8)
    
    plt.title("Range della Loss per ogni Run", fontsize=16)
    plt.xlabel("Run Index", fontsize=14)
    plt.ylabel("Loss", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.savefig(os.path.join(output_dir, "loss_range.png"), dpi=300, bbox_inches="tight")
    
    # Crea un grafico con i range della train_loss
    plt.figure(figsize=(12, 8))
    for i, row in df.iterrows():
        if row["has_train_loss"]:
            plt.plot([i, i], [row["train_loss_min"], row["train_loss_max"]], "r-", linewidth=2, alpha=0.7)
            plt.plot(i, row["train_loss_min"], "ro", markersize=8)
            plt.plot(i, row["train_loss_max"], "ro", markersize=8)
    
    plt.title("Range della Train Loss per ogni Run", fontsize=16)
    plt.xlabel("Run Index", fontsize=14)
    plt.ylabel("Train Loss", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.savefig(os.path.join(output_dir, "train_loss_range.png"), dpi=300, bbox_inches="tight")
    
    # Crea un grafico con il numero di step per ogni run
    plt.figure(figsize=(12, 8))
    plt.bar(range(len(df)), df["num_steps"], color="green", alpha=0.7)
    
    plt.title("Numero di Step per ogni Run", fontsize=16)
    plt.xlabel("Run Index", fontsize=14)
    plt.ylabel("Numero di Step", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.savefig(os.path.join(output_dir, "num_steps.png"), dpi=300, bbox_inches="tight")
    
    print("Grafici di analisi salvati in", output_dir)

def extract_training_data(training_runs):
    """Estrai i dati di training dalle run"""
    print("Estrazione dei dati di training...")
    
    # Estrai i dati di training
    llama_data = []
    gemma_data = []
    
    for run in tqdm(training_runs, desc="Estrazione dati"):
        # Ottieni i dati della run
        try:
            history = run.history()
            
            # Verifica se ci sono dati di loss
            if "loss" in history.columns:
                # Aggiungi i dati alla lista appropriata
                if "llama31_8b" in run.name:
                    for i, row in history.iterrows():
                        if "loss" in row:
                            llama_data.append({
                                "step": row.get("_step", i),
                                "loss": row["loss"],
                                "run_name": run.name
                            })
                elif "gemma2_9b_it" in run.name:
                    for i, row in history.iterrows():
                        if "loss" in row:
                            gemma_data.append({
                                "step": row.get("_step", i),
                                "loss": row["loss"],
                                "run_name": run.name
                            })
        except Exception as e:
            print(f"Errore nell'estrazione dei dati per la run {run.name}: {e}")
    
    # Crea DataFrame
    llama_df = pd.DataFrame(llama_data)
    gemma_df = pd.DataFrame(gemma_data)
    
    # Ordina per step
    if not llama_df.empty:
        llama_df = llama_df.sort_values("step")
    if not gemma_df.empty:
        gemma_df = gemma_df.sort_values("step")
    
    return llama_df, gemma_df

def create_fixed_charts(llama_df, gemma_df, output_dir):
    """Crea grafici corretti"""
    print("Creazione dei grafici corretti...")
    
    # Crea grafici
    plt.figure(figsize=(12, 8))
    
    if not llama_df.empty:
        plt.plot(llama_df["step"], llama_df["loss"], "b-", label="Llama 3.1 8B", linewidth=2, alpha=0.7)
    
    if not gemma_df.empty:
        plt.plot(gemma_df["step"], gemma_df["loss"], "r-", label="Gemma 2 9B IT", linewidth=2, alpha=0.7)
    
    plt.title("Training Loss", fontsize=16)
    plt.xlabel("Step", fontsize=14)
    plt.ylabel("Loss", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.legend(fontsize=12)
    
    # Imposta il range dell'asse Y
    plt.ylim(0, 1.5)
    
    # Salva il grafico
    loss_chart_path = os.path.join(output_dir, "training_loss.png")
    plt.savefig(loss_chart_path, dpi=300, bbox_inches="tight")
    
    print(f"Grafico salvato in {loss_chart_path}")
    
    # Crea un grafico con la loss di Llama
    if not llama_df.empty:
        plt.figure(figsize=(12, 8))
        plt.plot(llama_df["step"], llama_df["loss"], "b-", linewidth=2, alpha=0.7)
        plt.title("Llama 3.1 8B Training Loss", fontsize=16)
        plt.xlabel("Step", fontsize=14)
        plt.ylabel("Loss", fontsize=14)
        plt.grid(True, linestyle="--", alpha=0.7)
        plt.ylim(0, 1.5)
        llama_chart_path = os.path.join(output_dir, "llama_training_loss.png")
        plt.savefig(llama_chart_path, dpi=300, bbox_inches="tight")
        print(f"Grafico salvato in {llama_chart_path}")
    
    # Crea un grafico con la loss di Gemma
    if not gemma_df.empty:
        plt.figure(figsize=(12, 8))
        plt.plot(gemma_df["step"], gemma_df["loss"], "r-", linewidth=2, alpha=0.7)
        plt.title("Gemma 2 9B IT Training Loss", fontsize=16)
        plt.xlabel("Step", fontsize=14)
        plt.ylabel("Loss", fontsize=14)
        plt.grid(True, linestyle="--", alpha=0.7)
        plt.ylim(0, 1.5)
        gemma_chart_path = os.path.join(output_dir, "gemma_training_loss.png")
        plt.savefig(gemma_chart_path, dpi=300, bbox_inches="tight")
        print(f"Grafico salvato in {gemma_chart_path}")
    
    return loss_chart_path, llama_chart_path if not llama_df.empty else None, gemma_chart_path if not gemma_df.empty else None

def upload_to_wandb(entity, project, run_name, loss_chart_path, llama_chart_path, gemma_chart_path, llama_df, gemma_df):
    """Carica i grafici su Weights & Biands"""
    print("Caricamento dei grafici su Weights & Biands...")
    
    # Inizializza Weights & Biands
    wandb.init(
        entity=entity,
        project=project,
        name=run_name,
        job_type="visualization"
    )
    
    # Carica i grafici
    wandb.log({"Combined Training Loss": wandb.Image(loss_chart_path)})
    
    if llama_chart_path:
        wandb.log({"Llama 3.1 8B Training Loss": wandb.Image(llama_chart_path)})
    
    if gemma_chart_path:
        wandb.log({"Gemma 2 9B IT Training Loss": wandb.Image(gemma_chart_path)})
    
    # Crea tabelle con i dati di loss
    if not llama_df.empty:
        llama_table = wandb.Table(columns=["step", "loss", "run_name"])
        for _, row in llama_df.iterrows():
            llama_table.add_data(row["step"], row["loss"], row["run_name"])
        wandb.log({"Llama 3.1 8B Loss Data": llama_table})
    
    if not gemma_df.empty:
        gemma_table = wandb.Table(columns=["step", "loss", "run_name"])
        for _, row in gemma_df.iterrows():
            gemma_table.add_data(row["step"], row["loss"], row["run_name"])
        wandb.log({"Gemma 2 9B IT Loss Data": gemma_table})
    
    # Crea un sommario del problema
    summary_table = wandb.Table(columns=["Problema", "Causa", "Soluzione"])
    summary_table.add_data(
        "Visualizzazione incompleta della loss",
        "Range dell'asse Y non corretto o filtro sui dati",
        "Creazione di grafici con range dell'asse Y corretto (0-1.5)"
    )
    wandb.log({"Analisi del Problema": summary_table})
    
    # Chiudi wandb
    wandb.finish()
    
    print("Grafici caricati su Weights & Biands")

def create_dashboard(entity, project, dashboard_name):
    """Crea una dashboard in Weights & Biands"""
    print("Creazione di una dashboard in Weights & Biands...")
    
    # Inizializza Weights & Biands
    wandb.init(
        entity=entity,
        project=project,
        name=dashboard_name,
        job_type="visualization"
    )
    
    # Crea un sommario del problema
    summary_table = wandb.Table(columns=["Problema", "Causa", "Soluzione"])
    summary_table.add_data(
        "Visualizzazione incompleta della loss",
        "Range dell'asse Y non corretto o filtro sui dati",
        "Creazione di grafici con range dell'asse Y corretto (0-1.5)"
    )
    wandb.log({"Analisi del Problema": summary_table})
    
    # Chiudi wandb
    wandb.finish()
    
    print(f"Dashboard '{dashboard_name}' creata con successo!")

def main():
    args = parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza l'API di Weights & Biands
    api = wandb.Api()
    
    # Analizza le run
    df, training_runs = analyze_runs(api, args.wandb_entity, args.wandb_project, args.output_dir)
    
    # Crea grafici di analisi
    create_charts(df, args.output_dir)
    
    # Se richiesto, termina qui
    if args.analyze_only:
        return
    
    # Estrai i dati di training
    llama_df, gemma_df = extract_training_data(training_runs)
    
    # Crea grafici corretti
    if args.fix_charts:
        loss_chart_path, llama_chart_path, gemma_chart_path = create_fixed_charts(llama_df, gemma_df, args.output_dir)
        
        # Carica i grafici su Weights & Biands
        if args.upload_to_wandb:
            upload_to_wandb(args.wandb_entity, args.wandb_project, args.run_name, loss_chart_path, llama_chart_path, gemma_chart_path, llama_df, gemma_df)
    
    # Crea una dashboard in Weights & Biands
    if args.create_dashboard:
        create_dashboard(args.wandb_entity, args.wandb_project, args.run_name)

if __name__ == "__main__":
    main()
