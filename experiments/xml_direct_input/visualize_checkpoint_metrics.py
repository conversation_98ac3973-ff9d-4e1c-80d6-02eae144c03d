#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per visualizzare i risultati delle valutazioni dei checkpoint.
Genera grafici e tabelle comparative.
"""

import os
import sys
import json
import glob
import argparse
import logging
from typing import Dict, List, Any
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import wandb

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Visualizzazione dei risultati delle valutazioni dei checkpoint")
    parser.add_argument("--metrics_dir", type=str, required=True, help="Directory contenente i risultati delle valutazioni")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i grafici")
    parser.add_argument("--model_name", type=str, default=None, help="Nome del modello per il titolo dei grafici")
    parser.add_argument("--use_wandb", action="store_true", help="Carica i grafici su Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner_analysis", help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default=None, help="Nome della run in Weights & Biands")
    return parser.parse_args()

def load_metrics(metrics_dir: str) -> List[Dict[str, Any]]:
    """Carica le metriche da tutti i file metrics.json nella directory."""
    # Cerca tutti i file metrics.json
    metrics_files = []
    for root, dirs, files in os.walk(metrics_dir):
        for file in files:
            if file == "metrics.json":
                metrics_files.append(os.path.join(root, file))

    # Carica le metriche
    all_metrics = []
    for file in metrics_files:
        try:
            with open(file, "r") as f:
                metrics = json.load(f)

                # Aggiungi il path del file
                metrics["file_path"] = file

                # Aggiungi il nome del checkpoint
                checkpoint_dir = os.path.basename(os.path.dirname(file))
                metrics["checkpoint"] = checkpoint_dir

                all_metrics.append(metrics)
        except Exception as e:
            logger.error(f"Errore nel caricamento del file {file}: {e}")

    # Ordina le metriche per step
    all_metrics.sort(key=lambda x: x.get("step", 0))

    return all_metrics

def create_metrics_dataframe(metrics: List[Dict[str, Any]]) -> pd.DataFrame:
    """Crea un DataFrame con le metriche."""
    # Estrai le metriche principali
    data = []
    for m in metrics:
        row = {
            # Informazioni sul checkpoint
            "checkpoint": m.get("checkpoint", ""),
            "step": m.get("step", 0),
            "epoch": m.get("epoch", 0),

            # Metriche di loss
            "training_loss": m.get("training_loss", None),
            "eval_loss": m.get("eval_loss", None),

            # Metriche BLEU
            "bleu1": m.get("bleu1", 0),
            "bleu2": m.get("bleu2", 0),
            "bleu3": m.get("bleu3", 0),
            "bleu4": m.get("bleu4", 0),

            # Altre metriche COCO
            "meteor": m.get("meteor", 0),
            "cider": m.get("cider", 0),

            # CLIP Score
            "clip_score": m.get("clip_score", None),

            # Statistiche sulle didascalie
            "caption_length_mean": m.get("caption_length_mean", None),
            "vocabulary_size": m.get("vocabulary_size", None),
            "type_token_ratio": m.get("type_token_ratio", None),
            "self_bleu": m.get("self_bleu", None),

            # Tempo di inferenza
            "inference_time_mean": m.get("inference_time_mean", None),
            "inference_time_total": m.get("inference_time_total", None),

            # Perplexity
            "perplexity_mean": m.get("perplexity_mean", None)
        }
        data.append(row)

    # Crea il DataFrame
    df = pd.DataFrame(data)

    return df

def plot_metrics_over_steps(df: pd.DataFrame, output_dir: str, model_name: str = None):
    """Crea grafici delle metriche in funzione degli step."""
    # Crea la directory di output
    os.makedirs(output_dir, exist_ok=True)

    # Imposta lo stile dei grafici
    sns.set(style="whitegrid")
    plt.rcParams.update({"font.size": 12})

    # Titolo base
    base_title = f"Metriche per {model_name}" if model_name else "Metriche"

    # Grafico delle loss
    plt.figure(figsize=(12, 6))
    if "training_loss" in df.columns and df["training_loss"].notna().any():
        plt.plot(df["step"], df["training_loss"], marker="o", label="Training Loss")
    if "eval_loss" in df.columns and df["eval_loss"].notna().any():
        plt.plot(df["step"], df["eval_loss"], marker="s", label="Validation Loss")
    plt.title(f"{base_title} - Loss")
    plt.xlabel("Step")
    plt.ylabel("Loss")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "loss_over_steps.png"))
    plt.close()

    # Grafico di BLEU
    plt.figure(figsize=(12, 6))
    plt.plot(df["step"], df["bleu1"], marker="o", label="BLEU-1")
    plt.plot(df["step"], df["bleu2"], marker="s", label="BLEU-2")
    plt.plot(df["step"], df["bleu3"], marker="^", label="BLEU-3")
    plt.plot(df["step"], df["bleu4"], marker="d", label="BLEU-4")
    plt.title(f"{base_title} - BLEU")
    plt.xlabel("Step")
    plt.ylabel("Score")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "bleu_over_steps.png"))
    plt.close()

    # Grafico di METEOR e CIDEr
    plt.figure(figsize=(12, 6))
    plt.plot(df["step"], df["meteor"], marker="o", label="METEOR")
    plt.plot(df["step"], df["cider"], marker="s", label="CIDEr")
    plt.title(f"{base_title} - METEOR e CIDEr")
    plt.xlabel("Step")
    plt.ylabel("Score")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "meteor_cider_over_steps.png"))
    plt.close()

    # Grafico di CLIP Score (se disponibile)
    if "clip_score" in df.columns and df["clip_score"].notna().any():
        plt.figure(figsize=(12, 6))
        plt.plot(df["step"], df["clip_score"], marker="o", label="CLIP Score")
        plt.title(f"{base_title} - CLIP Score")
        plt.xlabel("Step")
        plt.ylabel("Score")
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "clip_score_over_steps.png"))
        plt.close()

    # Grafico delle statistiche sulle didascalie
    if "caption_length_mean" in df.columns and df["caption_length_mean"].notna().any():
        plt.figure(figsize=(12, 6))
        plt.plot(df["step"], df["caption_length_mean"], marker="o", label="Lunghezza Media")
        if "vocabulary_size" in df.columns and df["vocabulary_size"].notna().any():
            # Normalizza la dimensione del vocabolario per il grafico
            vocab_size_norm = df["vocabulary_size"] / df["vocabulary_size"].max() * df["caption_length_mean"].max()
            plt.plot(df["step"], vocab_size_norm, marker="s", label=f"Dimensione Vocabolario (max: {df['vocabulary_size'].max():.0f})")
        if "type_token_ratio" in df.columns and df["type_token_ratio"].notna().any():
            # Normalizza il TTR per il grafico
            ttr_norm = df["type_token_ratio"] * df["caption_length_mean"].max()
            plt.plot(df["step"], ttr_norm, marker="^", label=f"Type-Token Ratio (max: {df['type_token_ratio'].max():.2f})")
        plt.title(f"{base_title} - Statistiche Didascalie")
        plt.xlabel("Step")
        plt.ylabel("Valore")
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "caption_stats_over_steps.png"))
        plt.close()

    # Grafico del tempo di inferenza
    if "inference_time_mean" in df.columns and df["inference_time_mean"].notna().any():
        plt.figure(figsize=(12, 6))
        plt.plot(df["step"], df["inference_time_mean"], marker="o", label="Tempo Medio (s)")
        plt.title(f"{base_title} - Tempo di Inferenza")
        plt.xlabel("Step")
        plt.ylabel("Secondi")
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "inference_time_over_steps.png"))
        plt.close()

    # Grafico della perplexity
    if "perplexity_mean" in df.columns and df["perplexity_mean"].notna().any():
        plt.figure(figsize=(12, 6))
        plt.plot(df["step"], df["perplexity_mean"], marker="o", label="Perplexity Media")
        plt.title(f"{base_title} - Perplexity")
        plt.xlabel("Step")
        plt.ylabel("Perplexity")
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, "perplexity_over_steps.png"))
        plt.close()

    # Grafico combinato di tutte le metriche (normalizzate)
    plt.figure(figsize=(14, 8))

    # Normalizza le metriche
    metrics_to_normalize = ["bleu1", "bleu2", "bleu3", "bleu4", "meteor", "cider"]
    if "clip_score" in df.columns and df["clip_score"].notna().any():
        metrics_to_normalize.append("clip_score")

    df_norm = df.copy()
    for metric in metrics_to_normalize:
        if df[metric].notna().any():
            min_val = df[metric].min()
            max_val = df[metric].max()
            if max_val > min_val:
                df_norm[metric] = (df[metric] - min_val) / (max_val - min_val)
            else:
                df_norm[metric] = 0

    # Plotta le metriche normalizzate
    for metric in metrics_to_normalize:
        if df_norm[metric].notna().any():
            plt.plot(df_norm["step"], df_norm[metric], marker="o", label=metric)

    plt.title(f"{base_title} - Metriche Normalizzate")
    plt.xlabel("Step")
    plt.ylabel("Score Normalizzato")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "normalized_metrics_over_steps.png"))
    plt.close()

    # Grafico radar per il miglior checkpoint
    best_checkpoint_idx = df["bleu4"].idxmax()
    best_checkpoint = df.iloc[best_checkpoint_idx]

    # Crea il grafico radar
    metrics_for_radar = ["bleu1", "bleu2", "bleu3", "bleu4", "meteor", "cider"]
    if "clip_score" in df.columns and df["clip_score"].notna().any():
        metrics_for_radar.append("clip_score")

    values = [best_checkpoint[metric] for metric in metrics_for_radar]

    # Normalizza i valori per il grafico radar
    max_values = df[metrics_for_radar].max()
    normalized_values = [best_checkpoint[metric] / max_values[metric] if max_values[metric] > 0 else 0 for metric in metrics_for_radar]

    # Crea il grafico radar
    angles = np.linspace(0, 2*np.pi, len(metrics_for_radar), endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il cerchio
    normalized_values += normalized_values[:1]  # Chiudi il cerchio

    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(polar=True))
    ax.plot(angles, normalized_values, "o-", linewidth=2)
    ax.fill(angles, normalized_values, alpha=0.25)
    ax.set_thetagrids(np.degrees(angles[:-1]), metrics_for_radar)
    ax.set_ylim(0, 1)
    plt.title(f"Miglior Checkpoint: {best_checkpoint['checkpoint']} (Step {best_checkpoint['step']})")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "best_checkpoint_radar.png"))
    plt.close()

    # Crea una tabella con i risultati
    fig, ax = plt.subplots(figsize=(12, len(df) * 0.5 + 2))
    ax.axis("off")

    # Seleziona le colonne da visualizzare
    display_cols = ["checkpoint", "step", "bleu4", "meteor", "cider"]
    if "clip_score" in df.columns and df["clip_score"].notna().any():
        display_cols.append("clip_score")

    # Crea la tabella
    table_data = df[display_cols].round(4)
    table = ax.table(
        cellText=table_data.values,
        colLabels=table_data.columns,
        loc="center",
        cellLoc="center",
        colColours=["#f2f2f2"] * len(display_cols)
    )
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1, 1.5)

    # Evidenzia il miglior checkpoint
    for i in range(len(df)):
        if i == best_checkpoint_idx:
            for j in range(len(display_cols)):
                table[(i+1, j)].set_facecolor("#c7f0d8")

    plt.title(f"{base_title} - Risultati")
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "results_table.png"))
    plt.close()

    logger.info(f"Grafici salvati in {output_dir}")

def main():
    args = parse_args()

    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)

    # Carica le metriche
    logger.info(f"Caricamento delle metriche da {args.metrics_dir}")
    metrics = load_metrics(args.metrics_dir)

    if not metrics:
        logger.error(f"Nessuna metrica trovata in {args.metrics_dir}")
        return

    logger.info(f"Trovate {len(metrics)} metriche")

    # Crea il DataFrame
    df = create_metrics_dataframe(metrics)

    # Salva il DataFrame come CSV
    csv_file = os.path.join(args.output_dir, "metrics.csv")
    df.to_csv(csv_file, index=False)
    logger.info(f"DataFrame salvato in {csv_file}")

    # Crea i grafici
    model_name = args.model_name or os.path.basename(args.metrics_dir)
    plot_metrics_over_steps(df, args.output_dir, model_name)

    # Carica i grafici su Weights & Biands se richiesto
    if args.use_wandb:
        wandb_run_name = args.wandb_run_name or f"checkpoint_analysis_{model_name}"

        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=wandb_run_name,
            config={
                "metrics_dir": args.metrics_dir,
                "model_name": model_name,
                "num_checkpoints": len(metrics)
            }
        )

        # Carica i grafici principali
        wandb.log({
            "loss_over_steps": wandb.Image(os.path.join(args.output_dir, "loss_over_steps.png")),
            "bleu_over_steps": wandb.Image(os.path.join(args.output_dir, "bleu_over_steps.png")),
            "meteor_cider_over_steps": wandb.Image(os.path.join(args.output_dir, "meteor_cider_over_steps.png")),
            "normalized_metrics_over_steps": wandb.Image(os.path.join(args.output_dir, "normalized_metrics_over_steps.png")),
            "best_checkpoint_radar": wandb.Image(os.path.join(args.output_dir, "best_checkpoint_radar.png")),
            "results_table": wandb.Image(os.path.join(args.output_dir, "results_table.png"))
        })

        # Carica i grafici aggiuntivi se disponibili
        additional_graphs = {
            "clip_score_over_steps": "clip_score_over_steps.png",
            "caption_stats_over_steps": "caption_stats_over_steps.png",
            "inference_time_over_steps": "inference_time_over_steps.png",
            "perplexity_over_steps": "perplexity_over_steps.png"
        }

        for graph_name, file_name in additional_graphs.items():
            graph_file = os.path.join(args.output_dir, file_name)
            if os.path.exists(graph_file):
                wandb.log({graph_name: wandb.Image(graph_file)})

        # Carica i dati come tabella
        wandb.log({"metrics_table": wandb.Table(dataframe=df)})

        # Chiudi la sessione
        wandb.finish()

        logger.info(f"Grafici caricati su Weights & Biands (run: {wandb_run_name})")

    logger.info("Visualizzazione completata")

if __name__ == "__main__":
    main()
