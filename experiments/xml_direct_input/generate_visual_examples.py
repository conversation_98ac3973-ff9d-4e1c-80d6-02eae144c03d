#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import argparse
import torch
import random
import matplotlib.pyplot as plt
import cairosvg
import io
import numpy as np
from PIL import Image
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
    pipeline
)
from peft import PeftModel, PeftConfig
import wandb
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Genera esempi visivi di caption SVG")
    parser.add_argument("--data_file", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_xml.json", 
                        help="File JSON con i dati di test")
    parser.add_argument("--llama_model_path", type=str, 
                        default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence",
                        help="Percorso del modello Llama fine-tuned senza tokenizer personalizzato")
    parser.add_argument("--gemma_model_path", type=str, 
                        default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence",
                        help="Percorso del modello Gemma fine-tuned senza tokenizer personalizzato")
    parser.add_argument("--num_examples", type=int, default=5, 
                        help="Numero di esempi da generare")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/visual_examples",
                        help="Directory di output per gli esempi visivi")
    parser.add_argument("--use_wandb", action="store_true", 
                        help="Usa Weights & Biands per tracciare i risultati")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore",
                        help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner",
                        help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default="visual_examples",
                        help="Nome della run in Weights & Biands")
    return parser.parse_args()

def load_data(data_file):
    """Carica i dati dal file JSON"""
    with open(data_file, 'r') as f:
        data = json.load(f)
    return data

def render_svg(svg_string):
    """Renderizza una stringa SVG come immagine"""
    try:
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'))
        image = Image.open(io.BytesIO(png_data))
        return image
    except Exception as e:
        print(f"Errore durante la renderizzazione dell'SVG: {e}")
        return None

def load_base_model(model_name):
    """Carica un modello base per zero-shot inference"""
    print(f"Caricamento del modello base {model_name}...")
    
    # Configurazione per quantizzazione a 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True
    )
    
    # Carica il modello e il tokenizer
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=bnb_config,
        device_map="auto",
        token=os.environ.get("HF_TOKEN")
    )
    
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        token=os.environ.get("HF_TOKEN")
    )
    
    return model, tokenizer

def load_finetuned_model(model_path, base_model_name):
    """Carica un modello fine-tuned con LoRA"""
    print(f"Caricamento del modello fine-tuned da {model_path}...")
    
    # Configurazione per quantizzazione a 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True
    )
    
    # Carica il modello base
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_name,
        quantization_config=bnb_config,
        device_map="auto",
        token=os.environ.get("HF_TOKEN")
    )
    
    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        base_model_name,
        token=os.environ.get("HF_TOKEN")
    )
    
    # Carica il modello LoRA
    model = PeftModel.from_pretrained(base_model, model_path)
    
    return model, tokenizer

def generate_caption(model, tokenizer, svg_content, max_length=200):
    """Genera una caption per un SVG utilizzando il modello specificato"""
    # Crea il prompt
    prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg_content}\n\nDescrizione:"
    
    # Tokenizza il prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
    
    # Genera la caption
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            num_return_sequences=1
        )
    
    # Decodifica la caption generata
    caption = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Estrai solo la parte della caption dopo il prompt
    caption = caption.split("Descrizione:")[1].strip() if "Descrizione:" in caption else caption
    
    return caption

def create_visual_example(svg_content, zero_shot_caption, llama_caption, gemma_caption, output_path):
    """Crea un'immagine con l'SVG renderizzato e le caption generate"""
    # Renderizza l'SVG
    svg_image = render_svg(svg_content)
    if svg_image is None:
        return None
    
    # Crea la figura
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # Mostra l'immagine SVG
    ax.imshow(np.array(svg_image))
    ax.axis('off')
    
    # Aggiungi le caption
    caption_text = (
        f"Zero-shot: {zero_shot_caption[:200]}...\n\n"
        f"Llama 3.1 8B: {llama_caption[:200]}...\n\n"
        f"Gemma 2 9B IT: {gemma_caption[:200]}..."
    )
    
    # Aggiungi il testo sotto l'immagine
    plt.figtext(0.5, 0.01, caption_text, wrap=True, horizontalalignment='center', fontsize=10)
    
    # Salva la figura
    plt.tight_layout(rect=[0, 0.1, 1, 0.95])  # Lascia spazio per il testo
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    return output_path

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per il token di Hugging Face
    os.environ["HF_TOKEN"] = "*************************************"
    
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Inizializza Weights & Biands
    if args.use_wandb:
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=args.wandb_run_name,
            job_type="visualization"
        )
    
    # Carica i dati
    data = load_data(args.data_file)
    
    # Seleziona esempi casuali
    examples = random.sample(data, min(args.num_examples, len(data)))
    
    # Carica i modelli
    llama_base_model_name = "meta-llama/Llama-3.1-8B-Instruct"
    gemma_base_model_name = "google/gemma-2-9b-it"
    
    # Carica il modello Llama base per zero-shot
    zero_shot_model, zero_shot_tokenizer = load_base_model(llama_base_model_name)
    
    # Carica i modelli fine-tuned
    llama_model, llama_tokenizer = load_finetuned_model(args.llama_model_path, llama_base_model_name)
    gemma_model, gemma_tokenizer = load_finetuned_model(args.gemma_model_path, gemma_base_model_name)
    
    # Genera esempi visivi
    for i, example in enumerate(examples):
        print(f"Generazione dell'esempio {i+1}/{len(examples)}...")
        
        # Estrai il contenuto SVG
        svg_content = example["svg"]
        
        # Genera le caption
        zero_shot_caption = generate_caption(zero_shot_model, zero_shot_tokenizer, svg_content)
        llama_caption = generate_caption(llama_model, llama_tokenizer, svg_content)
        gemma_caption = generate_caption(gemma_model, gemma_tokenizer, svg_content)
        
        # Crea l'esempio visivo
        output_path = os.path.join(args.output_dir, f"example_{i+1}.png")
        visual_example = create_visual_example(
            svg_content, 
            zero_shot_caption, 
            llama_caption, 
            gemma_caption, 
            output_path
        )
        
        # Carica l'esempio su Weights & Biands
        if args.use_wandb and visual_example:
            wandb.log({
                f"example_{i+1}": wandb.Image(
                    visual_example,
                    caption=f"Esempio {i+1}"
                ),
                f"svg_{i+1}": svg_content,
                f"zero_shot_caption_{i+1}": zero_shot_caption,
                f"llama_caption_{i+1}": llama_caption,
                f"gemma_caption_{i+1}": gemma_caption
            })
    
    # Chiudi Weights & Biands
    if args.use_wandb:
        wandb.finish()
    
    print(f"Generati {len(examples)} esempi visivi in {args.output_dir}")

if __name__ == "__main__":
    main()
