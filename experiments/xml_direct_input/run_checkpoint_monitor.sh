#!/bin/bash

# Script per avviare il monitoraggio dei checkpoint

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
MODEL_NAME=${1:-"meta-llama/Llama-3.1-8B-Instruct"}
CHECKPOINT_DIR=${2:-"/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence"}
TEST_FILE=${3:-"/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"}
OUTPUT_DIR=${4:-"/work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence"}
USE_CUSTOM_TOKENIZER=${5:-""}
LOAD_IN_4BIT=${6:-"--load_in_4bit"}
NUM_SAMPLES=${7:-100}
USE_CLIP=${8:-"--use_clip"}
CHECK_INTERVAL=${9:-1800}  # 30 minuti
MIN_STEP_INTERVAL=${10:-200}  # Valuta ogni 200 step
MAX_EVALUATIONS=${11:-5}  # Massimo 5 valutazioni

# Crea la directory di output
mkdir -p "$OUTPUT_DIR"

# Stampa informazioni
echo "Monitoraggio dei Checkpoint"
echo "Modello: $MODEL_NAME"
echo "Directory checkpoint: $CHECKPOINT_DIR"
echo "File di test: $TEST_FILE"
echo "Directory di output: $OUTPUT_DIR"
echo "Tokenizer personalizzato: $USE_CUSTOM_TOKENIZER"
echo "Caricamento in 4-bit: $LOAD_IN_4BIT"
echo "Numero di campioni: $NUM_SAMPLES"
echo "Usa CLIP: $USE_CLIP"
echo "Intervallo di controllo: $CHECK_INTERVAL secondi"
echo "Intervallo minimo di step: $MIN_STEP_INTERVAL"
echo "Numero massimo di valutazioni: $MAX_EVALUATIONS"

# Avvia il monitoraggio in background
nohup $PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/monitor_and_evaluate_checkpoints.py \
    --model_name "$MODEL_NAME" \
    --checkpoint_dir "$CHECKPOINT_DIR" \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    $USE_CUSTOM_TOKENIZER \
    $LOAD_IN_4BIT \
    --num_samples "$NUM_SAMPLES" \
    $USE_CLIP \
    --check_interval "$CHECK_INTERVAL" \
    --min_step_interval "$MIN_STEP_INTERVAL" \
    --max_evaluations "$MAX_EVALUATIONS" \
    > logs/checkpoint_monitor_$(date +%Y%m%d_%H%M%S).log 2>&1 &

# Ottieni il PID del processo
MONITOR_PID=$!
echo "Monitoraggio avviato con PID: $MONITOR_PID"
echo "Log disponibile in: logs/checkpoint_monitor_$(date +%Y%m%d_%H%M%S).log"
