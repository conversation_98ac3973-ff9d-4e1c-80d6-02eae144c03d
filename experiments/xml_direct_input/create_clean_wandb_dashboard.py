#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import argparse
import wandb
import pandas as pd
import numpy as np
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Crea una dashboard pulita su Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--dashboard_name", type=str, default=f"SVG Captioner Dashboard {datetime.now().strftime('%Y-%m-%d')}", help="Nome della dashboard")
    return parser.parse_args()

def create_dashboard(args):
    """Crea una dashboard pulita su Weights & Biands."""
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza wandb
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.dashboard_name,
        job_type="dashboard"
    )
    
    # Crea una tabella con le informazioni sui modelli
    models_table = wandb.Table(
        columns=["Model", "Best Loss", "Training Steps", "Status"]
    )
    
    # Aggiungi informazioni sui modelli
    models_table.add_data("Llama 3.1 8B", "0.4417", "4075", "Converged")
    models_table.add_data("Gemma 2 9B IT", "0.3051", "3150", "Converged")
    
    # Carica la tabella su wandb
    wandb.log({"Models Overview": models_table})
    
    # Crea una tabella con i risultati di convergenza
    convergence_table = wandb.Table(
        columns=["Model", "Initial Loss", "Final Loss", "Improvement %", "Training Time (hours)"]
    )
    
    # Aggiungi informazioni sulla convergenza
    convergence_table.add_data("Llama 3.1 8B", "1.2", "0.4417", "63.2%", "1.7")
    convergence_table.add_data("Gemma 2 9B IT", "1.3", "0.3051", "76.5%", "1.8")
    
    # Carica la tabella su wandb
    wandb.log({"Convergence Results": convergence_table})
    
    # Crea una tabella con i prossimi passi
    next_steps_table = wandb.Table(
        columns=["Step", "Status", "Priority"]
    )
    
    # Aggiungi informazioni sui prossimi passi
    next_steps_table.add_data("Fine-tune with custom tokenizers", "Ready to start", "High")
    next_steps_table.add_data("Evaluate models with BLEU, CIDER, METEOR, CLIP SCORE", "Pending", "Medium")
    next_steps_table.add_data("Compare zero-shot vs fine-tuned performance", "In Progress", "Medium")
    next_steps_table.add_data("Generate final report", "Pending", "Low")
    
    # Carica la tabella su wandb
    wandb.log({"Next Steps": next_steps_table})
    
    # Crea un pannello di testo con il riepilogo
    summary_text = f"""
    # SVG Captioner Project Summary
    
    ## Models Status
    - **Llama 3.1 8B**: Converged with loss 0.4417
    - **Gemma 2 9B IT**: Converged with loss 0.3051
    
    ## Key Findings
    - Gemma 2 9B IT performs better than Llama 3.1 8B
    - Single-GPU training with continuation from checkpoints is more effective than multi-GPU training
    - Both models have reached convergence and are ready for fine-tuning with custom tokenizers
    
    ## Next Steps
    1. Fine-tune with custom tokenizers
    2. Evaluate models with BLEU, CIDER, METEOR, CLIP SCORE
    3. Compare zero-shot vs fine-tuned performance
    4. Generate final report
    """
    
    # Carica il pannello di testo su wandb
    wandb.log({"Project Summary": wandb.Html(f"<pre>{summary_text}</pre>")})
    
    # Chiudi wandb
    wandb.finish()
    
    print(f"Dashboard '{args.dashboard_name}' creata con successo!")

def main():
    args = parse_args()
    create_dashboard(args)

if __name__ == "__main__":
    main()
