# Percorso file: tesi_ediluzio/experiments/xml_direct_input/utils/wrap_simple_data_to_svg.py
# Vnecessarionsolo per visualizzare i dati!

import json
import os
import argparse
import logging
import re
import xml.etree.ElementTree as ET
from xml.dom import minidom
from tqdm import tqdm

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_attributes_robust(attr_string: str) -> dict:
    """
    Tenta di estrarre 'style', 'd', e 'transform' da una stringa usando regex specifici.
    """
    attributes = {}
    if not isinstance(attr_string, str): return attributes

    # Cerca d="..." catturando il contenuto tra virgolette
    d_match = re.search(r'd\s*=\s*"([^"]*)"', attr_string)
    if d_match:
        d_content = d_match.group(1).strip()
        if d_content: attributes['d'] = d_content

    # Cerca style="..."
    style_match = re.search(r'style\s*=\s*"([^"]*)"', attr_string)
    if style_match:
        style_content = style_match.group(1).strip()
        if style_content: attributes['style'] = style_content

    # Cerca transform="..."
    transform_match = re.search(r'transform\s*=\s*"([^"]*)"', attr_string)
    if transform_match:
        transform_content = transform_match.group(1).strip()
        if transform_content: attributes['transform'] = transform_content

    if 'd' not in attributes:
        # Logga solo se 'd' non è stato trovato affatto
        logger.warning(f"Non è stato possibile estrarre l'attributo 'd' da: '{attr_string[:100]}...'")

    return attributes

def wrap_path_in_svg(path_attributes_string: str, width: int = 100, height: int = 100) -> tuple[str, bool]:
    """
    Prende una stringa di attributi path (da 'xml_data'), usa il parser robusto,
    e la inserisce in un template SVG minimale valido.
    Restituisce una tupla: (contenuto_svg_stringa, is_error_bool).
    """
    xml_declaration = '<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n'
    error_signature_text = '<text x="10" y="20" font-size="10" fill="red">Error creating SVG:'

    try:
        # Usa il parser robusto
        attributes = parse_attributes_robust(path_attributes_string)

        # Controlla se 'd' è stato estratto con successo
        if not attributes.get('d'):
             raise ValueError("Attributo 'd' (path data) mancante o non estraibile dalla stringa.")

        # Crea la struttura XML SVG
        svg_root = ET.Element('svg', {
            'width': str(width), 'height': str(height),
            'viewBox': f'0 0 {width} {height}',
            'xmlns': 'http://www.w3.org/2000/svg', 'version': '1.1'
        })

        # Aggiungi l'elemento <path>
        valid_attributes = {k: v for k, v in attributes.items() if v or k == 'd'}
        ET.SubElement(svg_root, 'path', valid_attributes)

        # Converte in stringa formattata
        rough_string = ET.tostring(svg_root, encoding='unicode', method='xml')
        reparsed = minidom.parseString(rough_string)
        pretty_xml_as_string = reparsed.toprettyxml(indent="  ")
        if pretty_xml_as_string.startswith('<?xml'):
            pretty_xml_as_string = pretty_xml_as_string.split('\n', 1)[1]

        return xml_declaration + pretty_xml_as_string, False # Successo

    except Exception as e:
        # Logga l'errore
        logger.error(f"Errore durante la generazione dell'SVG per stringa attributi '{path_attributes_string[:100]}...': {e}", exc_info=False)
        # Ritorna l'SVG di errore e True
        error_svg = f'{xml_declaration}<svg width="{width}" height="{height}" viewBox="0 0 {width} {height}" xmlns="http://www.w3.org/2000/svg" version="1.1">{error_signature_text} {e}</text></svg>'
        return error_svg, True # Errore

def process_processed_json_to_svg(input_json_path: str, output_dir: str, svg_width: int = 100, svg_height: int = 100):
    """
    Legge un file JSON processato (come ki_dcount_max10_2k.json), estrae 'xml_data',
    tenta di parsificarla (robustamente) e wrapparla in un SVG,
    e salva SOLO i risultati di successo, contando correttamente gli errori.
    """
    logger.info(f"Caricamento dati da: {input_json_path}")
    try:
        with open(input_json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"Caricati {len(data)} elementi.")
    except Exception as e:
        logger.error(f"Errore caricamento/lettura file JSON {input_json_path}: {e}")
        return
    if not isinstance(data, list):
        logger.error("Il file JSON di input non contiene una lista.")
        return

    logger.info(f"Verifica/creazione directory di output: {output_dir}")
    os.makedirs(output_dir, exist_ok=True)

    logger.info("Inizio generazione file SVG...")
    parse_generate_errors = 0 # Conta errori da wrap_path_in_svg
    io_errors = 0             # Conta errori di scrittura file
    skipped_items = 0         # Conta item saltati per dati mancanti
    saved_count = 0           # Conta SVG salvati con successo

    for i, item in enumerate(tqdm(data, desc="Processando items")):
        if not isinstance(item, dict):
            logger.warning(f"Elemento {i} saltato: non è un dizionario.")
            skipped_items += 1
            continue

        # Usa la chiave 'xml_data' del file processato
        xml_data_str = item.get("xml_data")
        caption = item.get("caption", "no_caption")

        if not xml_data_str or not xml_data_str.strip():
            logger.warning(f"Elemento {i} saltato: 'xml_data' mancante o vuoto.")
            skipped_items += 1
            continue

        # Genera il contenuto SVG e controlla se è un errore
        svg_content, is_error = wrap_path_in_svg(xml_data_str, width=svg_width, height=svg_height)

        # Se wrap_path_in_svg ha restituito un errore (es. parsing fallito)
        if is_error:
            parse_generate_errors += 1 # Incrementa il contatore corretto
            continue # NON salvare e passa al prossimo item

        # Se non c'è errore, procedi al salvataggio
        safe_caption_part = re.sub(r'[^\w\-]+', '_', caption[:30]).strip('_')
        filename = f"item_{i:04d}_{safe_caption_part}.svg" # Nome basato sull'indice originale
        output_path = os.path.join(output_dir, filename)

        try:
            with open(output_path, 'w', encoding='utf-8') as f_out:
                f_out.write(svg_content)
            saved_count += 1 # Incrementa solo se il salvataggio va a buon fine
        except IOError as e:
            logger.error(f"Errore I/O durante scrittura file {output_path}: {e}")
            io_errors += 1
        except Exception as e:
            logger.error(f"Errore imprevisto salvataggio file per item {i}: {e}")
            io_errors += 1 # Conta anche errori imprevisti

    # Riepilogo finale corretto
    logger.info(f"--- Riepilogo Processo ---")
    total_processed_items = len(data)
    logger.info(f"Item totali nel JSON: {total_processed_items}")
    logger.info(f"Item saltati (xml_data mancante/vuoto o non dict): {skipped_items}")
    logger.info(f"Errori durante generazione/parsing SVG (input malformato): {parse_generate_errors}") # Riflette fallimenti di wrap_path_in_svg
    logger.info(f"Errori I/O durante salvataggio file: {io_errors}")
    logger.info(f"File SVG salvati con successo: {saved_count}") # Numero di file effettivamente salvati
    logger.info(f"Directory di output: {output_dir}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Avvolge stringhe di attributi path SVG da un file JSON processato in file SVG individuali.")
    parser.add_argument("input_json", help="Percorso del file JSON di input processato (es. data/processed/ki_dcount_max10_2k.json).")
    parser.add_argument("output_dir", help="Percorso della directory dove salvare i file SVG generati.")
    parser.add_argument("--width", type=int, default=200, help="Larghezza del canvas SVG generato (default: 200).")
    parser.add_argument("--height", type=int, default=200, help="Altezza del canvas SVG generato (default: 200).")

    args = parser.parse_args()

    process_processed_json_to_svg(args.input_json, args.output_dir, args.width, args.height)