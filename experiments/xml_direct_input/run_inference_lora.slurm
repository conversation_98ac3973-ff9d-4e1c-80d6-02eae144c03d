#!/bin/bash
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G" # Mantenuto constraint GPU
#SBATCH --gpus=1
#SBATCH --time=00:15:00 # Tempo per inferenza LoRA (simile a plain)
#SBATCH --nodes=1
#SBATCH --cpus-per-task=2 # Mantenuti 2 CPU
#SBATCH --mem=24G         # Mantenuti 24G RAM
#SBATCH --job-name=xml_infer_lora # Aggiornato nome job
#SBATCH -o /work/tesi_ediluzio/experiments/xml_direct_input/outputs/slurm_infer_lora_%j.out # Aggiornato file output
#SBATCH -e /work/tesi_ediluzio/experiments/xml_direct_input/outputs/slurm_infer_lora_%j.err # Aggiornato file errore
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE
################################################################################

# --- Setup Ambiente ---
mkdir -p /work/tesi_ediluzio/experiments/xml_direct_input/outputs/
export CUDA_HOME=/usr/local/cuda-11.8
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
source /work/tesi_ediluzio/svg_captioning_env/bin/activate # Attiva ambiente

# --- Info Job ---
echo "Avvio Inferenza con ADATTATORI LoRA"
echo "Job ID: $SLURM_JOB_ID" ; echo "Nodo: $SLURMD_NODENAME" ; echo "GPU: $CUDA_VISIBLE_DEVICES"

# --- Comando di Esecuzione INFERENZA LoRA ---
# Modifica i parametri qui sotto secondo le tue necessità
BASE_MODEL="nvidia/Nemotron-Mini-4B-Instruct" # Modello BASE usato per training LoRA
LORA_ADAPTERS="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/nemotron_4b_lora_8bit_v1" # <-- PERCORSO ADATTATORI SALVATI!
DATA="/work/tesi_ediluzio/data/processed/ki_dcount_max10_2k.json" # Percorso dataset
NUM_SAMPLES=5 # Numero di esempi da processare
MAX_TOKENS=70 # Max token da generare per caption
LOAD_MODE="--load_in_8bit" # Usa stessa modalità caricamento base usata per training (--load_in_8bit o --load_in_4bit)
OTHER_ARGS="--do_sample --temperature 0.6" # Argomenti generazione (es. sampling, temp più bassa)

echo "Eseguo lo script: run_inference_lora.py"
echo "Modello Base: ${BASE_MODEL}"
echo "Adattatori LoRA: ${LORA_ADAPTERS}"
echo "Dati: ${DATA}"
echo "Campioni: ${NUM_SAMPLES}"
echo "Modalità Caricamento Base: ${LOAD_MODE}"
echo "Argomenti Generazione: ${OTHER_ARGS}"

# Esegui lo script Python di INFERENZA LoRA con i suoi argomenti
python /work/tesi_ediluzio/experiments/xml_direct_input/run_inference_lora.py \
    --model_name_or_path "${BASE_MODEL}" \
    --lora_adapter_path "${LORA_ADAPTERS}" \
    --data_file "${DATA}" \
    --num_samples ${NUM_SAMPLES} \
    --max_new_tokens ${MAX_TOKENS} \
    ${LOAD_MODE} \
    ${OTHER_ARGS} \
    --device "cuda"

echo "Inferenza LoRA completata o interrotta."
exit 0