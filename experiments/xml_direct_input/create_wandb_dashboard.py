#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import argparse
import wandb
import pandas as pd
import numpy as np
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Crea un report su Weights & Biands con i grafici più importanti")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--report_name", type=str, default=f"SVG Captioner Report {datetime.now().strftime('%Y-%m-%d')}", help="Nome del report")
    return parser.parse_args()

def create_report(args):
    """Crea un report su Weights & Biands con i grafici più importanti."""
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"

    # Inizializza wandb
    wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.report_name,
        job_type="report"
    )

    # Crea una tabella con le informazioni sui modelli
    models_table = wandb.Table(
        columns=["Model", "Type", "Status", "Best Loss", "Training Steps"]
    )

    # Aggiungi informazioni sui modelli
    models_table.add_data("Llama 3.1 8B", "No Custom Tokenizer", "Training", "0.45", "3500+")
    models_table.add_data("Gemma 2 9B IT", "No Custom Tokenizer", "Training", "0.32", "2250+")
    models_table.add_data("Llama 3.1 8B Multi-GPU", "No Custom Tokenizer", "Pending", "N/A", "0")
    models_table.add_data("Gemma 2 9B IT Multi-GPU", "No Custom Tokenizer", "Training", "0.84", "300+")

    # Carica la tabella su wandb
    wandb.log({"Models Overview": models_table})

    # Crea una tabella con le informazioni sui job zero-shot
    zero_shot_table = wandb.Table(
        columns=["Model", "Status", "Examples"]
    )

    # Aggiungi informazioni sui job zero-shot
    zero_shot_table.add_data("Llama 3.1 8B Zero-Shot vs Fine-Tuned", "Running", "10")
    zero_shot_table.add_data("Gemma 2 9B IT Zero-Shot vs Fine-Tuned", "Running", "10")

    # Carica la tabella su wandb
    wandb.log({"Zero-Shot Comparison": zero_shot_table})

    # Crea una tabella con le informazioni sui prossimi passi
    next_steps_table = wandb.Table(
        columns=["Step", "Status", "Priority"]
    )

    # Aggiungi informazioni sui prossimi passi
    next_steps_table.add_data("Complete training to convergence", "In Progress", "High")
    next_steps_table.add_data("Fine-tune with custom tokenizers", "Pending", "Medium")
    next_steps_table.add_data("Evaluate models with BLEU, CIDER, METEOR, CLIP SCORE", "Pending", "Medium")
    next_steps_table.add_data("Compare zero-shot vs fine-tuned performance", "In Progress", "Medium")

    # Carica la tabella su wandb
    wandb.log({"Next Steps": next_steps_table})

    # Chiudi wandb
    wandb.finish()

    print(f"Report '{args.report_name}' creato con successo!")

def main():
    args = parse_args()
    create_report(args)

if __name__ == "__main__":
    main()
