# Nome file: check_lora_targets.py

import argparse
import torch
from transformers import AutoModelForCausalLM, BitsAndBytesConfig
import bitsandbytes as bnb # Per controllare il tipo di layer quantizzato
import logging

# Setup logging minimale
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_lora_target_modules(model_name: str):
    """
    Carica un modello in 8-bit e identifica i nomi dei moduli Linear8bitLt,
    che sono i candidati comuni per target LoRA con quantizzazione 8-bit.
    """
    logger.info(f"Caricamento modello base: {model_name} (in 8-bit per ispezione)")

    quantization_config = BitsAndBytesConfig(load_in_8bit=True)

    try:
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=quantization_config,
            trust_remote_code=True,
            # device_map='auto' # Usa 'auto' se hai GPU, altrimenti prova 'cpu'
                              # Per ispezione potrebbe funzionare anche su CPU se hai abbastanza RAM
            device_map='auto' if torch.cuda.is_available() else 'cpu',
            low_cpu_mem_usage=True # Tenta di usare meno RAM CPU
        )
        logger.info("Modello caricato con successo.")
    except Exception as e:
        logger.error(f"Errore durante il caricamento del modello: {e}", exc_info=True)
        return

    lora_module_names = set()
    logger.info("Ispezione dei moduli del modello...")

    for name, module in model.named_modules():
        # Controlla se il modulo è del tipo specifico usato da bitsandbytes per 8-bit Linear
        if isinstance(module, bnb.nn.Linear8bitLt):
            # Separa il nome del modulo finale dal percorso completo
            # es: 'model.layers.0.self_attn.q_proj' -> prendiamo 'q_proj'
            # O forse è meglio tenere il nome relativo per chiarezza?
            # Proviamo a tenere i nomi che contengono parti comuni di LLM
            parts = name.split('.')
            module_simple_name = parts[-1] # Prende l'ultima parte (es. q_proj)

            # Aggiungiamo solo i nomi che sono tipicamente target per LoRA
            # Questo è un filtro euristico! Potrebbe essere necessario adattarlo.
            common_targets = ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj', 'dense', 'attention']
            is_common = any(target in module_simple_name or target in name for target in common_targets)

            if is_common:
                # Aggiungiamo il nome *semplice* del modulo al set per evitare duplicati
                # Se vedi che mancano layer importanti, potresti voler aggiungere
                # l'intero `name` o rimuovere il filtro `if is_common`.
                lora_module_names.add(module_simple_name)
                #logger.debug(f"Trovato Linear8bitLt: {name} (Tipo: {type(module)}, Nome Semplice: {module_simple_name})")


    logger.info("-" * 50)
    logger.info("Potenziali `lora_target_modules` trovati (basati su nomi comuni e tipo Linear8bitLt):")
    if lora_module_names:
        # Stampa i nomi univoci trovati, ordinati
        sorted_names = sorted(list(lora_module_names))
        print("\nLista suggerita per 'lora_target_modules' nel tuo JSON:")
        print("[")
        for name in sorted_names:
            print(f'    "{name}",')
        print("]")
    else:
        logger.warning("Nessun modulo Linear8bitLt con nomi comuni trovato. Prova a rimuovere il filtro 'is_common' nello script per vedere tutti i layer Linear8bitLt.")
    logger.info("-" * 50)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Trova potenziali target LoRA per un modello 8-bit.")
    parser.add_argument("--model_name_or_path", type=str, default="nvidia/Nemotron-Mini-4B-Instruct", help="HF ID o path del modello.")
    args = parser.parse_args()
    find_lora_target_modules(args.model_name_or_path)