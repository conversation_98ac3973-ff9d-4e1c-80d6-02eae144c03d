#!/bin/bash
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G"
#SBATCH --gpus=1
#SBATCH --time=00:30:00 # 30 min
#SBATCH --nodes=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=24G
#SBATCH --job-name=xml_infer_base_venv_fix # Aggiornato nome job
#SBATCH -o /work/tesi_ediluzio/experiments/xml_direct_input/outputs/slurm_infer_base_venv_fix_%j.out # Aggiornato file output
#SBATCH -e /work/tesi_ediluzio/experiments/xml_direct_input/outputs/slurm_infer_base_venv_fix_%j.err # Aggiornato file errore
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE
################################################################################
# --- Setup Ambiente ---
echo "----------------------------------------------------"
echo "Job ID: $SLURM_JOB_ID"
echo "Job Name: $SLURM_JOB_NAME"
echo "Running on host: $(hostname)"
echo "Working directory: $(pwd)"
echo "Submit directory: $SLURM_SUBMIT_DIR"
echo "----------------------------------------------------"

### MODIFICA ### Percorso all'eseguibile Python dentro l'ambiente venv
PYTHON_EXEC="/work/tesi_ediluzio/svg_captioning_env/bin/python"

# Verifica esistenza eseguibile Python
if [ ! -f "$PYTHON_EXEC" ]; then
    echo "Errore: Eseguibile Python non trovato in ${PYTHON_EXEC}"
    echo "Verifica che l'ambiente venv esista e sia stato creato correttamente."
    exit 1
fi
echo "Using Python executable: ${PYTHON_EXEC}"

# Verifica ambiente (opzionale, mostra la versione di python)
${PYTHON_EXEC} --version

nvidia-smi # Mostra info GPU assegnata
echo "----------------------------------------------------"

# --- Definizione Parametri Script Python (con percorsi assoluti corretti) ---
PROJECT_ROOT="/work/tesi_ediluzio" # Directory root del progetto

MODEL_NAME="nvidia/Nemotron-Mini-4B-Instruct"   # Modello corretto
DATASET_JSON="${PROJECT_ROOT}/data/processed/filtered_svg_1_3_paths_2k.json" # Percorso dataset CORRETTO
OUTPUT_DIR="${PROJECT_ROOT}/experiments/xml_direct_input/outputs" # Directory per output
OUTPUT_JSON="${OUTPUT_DIR}/base_inference_venv_fix_${SLURM_JOB_ID}.json" # File di output
SCRIPT_PATH="${PROJECT_ROOT}/experiments/xml_direct_input/run_inference_base_xml.py" # Percorso allo script Python

NUM_SAMPLES=50
MAX_NEW_TOKENS=768
PROMPT_MAX_LEN=256

PRECISION_ARGS="--bf16"
USE_8BIT_ARGS=""
SAMPLING_ARGS=""

TEMPERATURE=0.7
TOP_K=50
TOP_P=0.95

# --- Aggiungi la directory del progetto a PYTHONPATH (usando percorso assoluto) ---
export PYTHONPATH="${PROJECT_ROOT}:${PYTHONPATH}"
echo "PYTHONPATH: $PYTHONPATH"
# -------------------------------------------------------

# Verifica esistenza file dataset e script
if [ ! -f "$DATASET_JSON" ]; then
    echo "Errore: Il file dataset specificato non esiste: ${DATASET_JSON}"
    exit 1
fi
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "Errore: Lo script Python specificato non esiste: ${SCRIPT_PATH}"
    exit 1
fi
# Crea directory output se non esiste
mkdir -p "${OUTPUT_DIR}"

# --- Esecuzione Script Python (usando Python del venv) ---
echo "Avvio script Python: ${SCRIPT_PATH}"
echo "Modello: $MODEL_NAME"
echo "Dataset: $DATASET_JSON"
echo "Output: $OUTPUT_JSON"

# Costruisci il comando dinamicamente
# NOTA: Usiamo ${PYTHON_EXEC} invece di 'python'
CMD="${PYTHON_EXEC} ${SCRIPT_PATH} \
    --model_name_or_path \"$MODEL_NAME\" \
    --dataset_json_path \"$DATASET_JSON\" \
    --output_file \"$OUTPUT_JSON\" \
    --num_samples $NUM_SAMPLES \
    --max_new_tokens $MAX_NEW_TOKENS \
    --prompt_max_len $PROMPT_MAX_LEN \
    --device \"cuda\" \
    --temperature $TEMPERATURE \
    --top_k $TOP_K \
    --top_p $TOP_P"

# Aggiungi flags opzionali solo se non sono stringhe vuote
if [[ ! -z "$PRECISION_ARGS" ]]; then
  CMD+=" $PRECISION_ARGS"
fi
if [[ ! -z "$USE_8BIT_ARGS" ]]; then
  CMD+=" $USE_8BIT_ARGS"
fi
if [[ ! -z "$SAMPLING_ARGS" ]]; then
  CMD+=" $SAMPLING_ARGS"
fi

echo "Comando Esecuzione: $CMD"

# Esegui il comando costruito
eval $CMD

# Controlla codice di uscita
status=$?
if [ $status -ne 0 ]; then
  echo "Errore: lo script Python è terminato con codice $status"
  exit $status
fi

echo "----------------------------------------------------"
echo "Script Python completato."
echo "Output salvato in: $OUTPUT_JSON"
echo "Job SLURM terminato."
echo "----------------------------------------------------"