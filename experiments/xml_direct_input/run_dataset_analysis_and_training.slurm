#!/bin/bash
#SBATCH --job-name=dataset_analysis_training
#SBATCH --output=logs/dataset_analysis_training_%j.out
#SBATCH --error=logs/dataset_analysis_training_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=1:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Parametri
DATA_FILE="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
OUTPUT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_analysis"
NUM_BINS=5

# Stampa informazioni
echo "Analisi del dataset SVG e preparazione per il training"
echo "Dataset: $DATA_FILE"
echo "Output: $OUTPUT_DIR"
echo "Numero di fasce: $NUM_BINS"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"

# Crea la directory di output
mkdir -p "$OUTPUT_DIR"

# Esegui lo script di analisi
echo "Eseguo lo script di analisi: /work/tesi_ediluzio/experiments/xml_direct_input/analyze_dataset.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/analyze_dataset.py \
    --data_file "$DATA_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --num_bins "$NUM_BINS"

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Analisi del dataset completata con successo"
else
    echo "Analisi del dataset fallita con codice di errore: $?"
    exit 1
fi

# Esegui lo script per creare gli split
echo "Eseguo lo script per creare gli split: /work/tesi_ediluzio/experiments/xml_direct_input/create_dataset_splits.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/create_dataset_splits.py \
    --bins_dir "$OUTPUT_DIR" \
    --output_dir "$OUTPUT_DIR/splits" \
    --train_ratio 0.7 \
    --val_ratio 0.15 \
    --test_ratio 0.15 \
    --seed 42

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Creazione degli split completata con successo"
else
    echo "Creazione degli split fallita con codice di errore: $?"
    exit 1
fi

echo "Analisi del dataset e creazione degli split completate!"

# Lancia i job di training
echo "Lancio dei job di training..."

# Llama 3.1 8B
echo "Lancio del job di training per Llama 3.1 8B..."
sbatch << EOF
#!/bin/bash
#SBATCH --job-name=llama_test1
#SBATCH --output=logs/llama_test1_%j.out
#SBATCH --error=logs/llama_test1_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --constraint=gpu_L40S_48G|gpu_A40_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G|gpu_2080Ti_11G
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --time=24:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: \$PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:\$PYTHONPATH"
echo "PYTHONPATH: \$PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "\$WANDB_DIR" "\$WANDB_CACHE_DIR" "\$WANDB_CONFIG_DIR" "\$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
TRAIN_FILE="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_analysis/splits/train.json"
VAL_FILE="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_analysis/splits/val.json"
OUTPUT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1"
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
BATCH_SIZE=4
GRADIENT_ACCUMULATION_STEPS=4
LEARNING_RATE=2e-4
NUM_TRAIN_EPOCHS=3
SAVE_STEPS=200
SAVE_TOTAL_LIMIT=3
LOGGING_STEPS=50
EVAL_STEPS=200
LORA_R=64
LORA_ALPHA=16
LORA_DROPOUT=0.1
MAX_SEQ_LENGTH=1024
WANDB_ENTITY="337543-unimore"
WANDB_PROJECT="captioner"
WANDB_RUN_NAME="llama31_8b_test1"
EARLY_STOPPING_PATIENCE=20
EARLY_STOPPING_THRESHOLD=0.0005

# Stampa informazioni
echo "Training di Llama 3.1 8B con LoRA"
echo "Dataset di training: \$TRAIN_FILE"
echo "Dataset di validazione: \$VAL_FILE"
echo "Output: \$OUTPUT_DIR"
echo "Modello: \$MODEL_NAME"
echo "Batch size: \$BATCH_SIZE"
echo "Gradient accumulation steps: \$GRADIENT_ACCUMULATION_STEPS"
echo "Learning rate: \$LEARNING_RATE"
echo "Epoche: \$NUM_TRAIN_EPOCHS"
echo "Job ID: \$SLURM_JOB_ID"
echo "Nodo: \$SLURMD_NODENAME"
echo "GPU Allocate (da SLURM): \$SLURM_GPUS_ON_NODE"

# Crea la directory di output
mkdir -p "\$OUTPUT_DIR"

# Esegui lo script di training
echo "Eseguo lo script di training: /work/tesi_ediluzio/experiments/xml_direct_input/train_llama_test1.py"
\$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/train_llama_test1.py \
    --train_file "\$TRAIN_FILE" \
    --val_file "\$VAL_FILE" \
    --output_dir "\$OUTPUT_DIR" \
    --model_name "\$MODEL_NAME" \
    --batch_size "\$BATCH_SIZE" \
    --gradient_accumulation_steps "\$GRADIENT_ACCUMULATION_STEPS" \
    --learning_rate "\$LEARNING_RATE" \
    --num_train_epochs "\$NUM_TRAIN_EPOCHS" \
    --save_steps "\$SAVE_STEPS" \
    --save_total_limit "\$SAVE_TOTAL_LIMIT" \
    --logging_steps "\$LOGGING_STEPS" \
    --eval_steps "\$EVAL_STEPS" \
    --lora_r "\$LORA_R" \
    --lora_alpha "\$LORA_ALPHA" \
    --lora_dropout "\$LORA_DROPOUT" \
    --max_seq_length "\$MAX_SEQ_LENGTH" \
    --use_wandb \
    --wandb_entity "\$WANDB_ENTITY" \
    --wandb_project "\$WANDB_PROJECT" \
    --wandb_run_name "\$WANDB_RUN_NAME" \
    --early_stopping_patience "\$EARLY_STOPPING_PATIENCE" \
    --early_stopping_threshold "\$EARLY_STOPPING_THRESHOLD"

# Verifica il codice di uscita
if [ \$? -eq 0 ]; then
    echo "Training di Llama 3.1 8B completato con successo"
else
    echo "Training di Llama 3.1 8B fallito con codice di errore: \$?"
    exit 1
fi
EOF

# Gemma 2 9B IT
echo "Lancio del job di training per Gemma 2 9B IT..."
sbatch << EOF
#!/bin/bash
#SBATCH --job-name=gemma_test1
#SBATCH --output=logs/gemma_test1_%j.out
#SBATCH --error=logs/gemma_test1_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --constraint=gpu_L40S_48G|gpu_A40_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G|gpu_2080Ti_11G
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --time=24:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: \$PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:\$PYTHONPATH"
echo "PYTHONPATH: \$PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "\$WANDB_DIR" "\$WANDB_CACHE_DIR" "\$WANDB_CONFIG_DIR" "\$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
TRAIN_FILE="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_analysis/splits/train.json"
VAL_FILE="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_analysis/splits/val.json"
OUTPUT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1"
MODEL_NAME="google/gemma-2-9b-it"
BATCH_SIZE=4
GRADIENT_ACCUMULATION_STEPS=4
LEARNING_RATE=2e-4
NUM_TRAIN_EPOCHS=3
SAVE_STEPS=200
SAVE_TOTAL_LIMIT=3
LOGGING_STEPS=50
EVAL_STEPS=200
LORA_R=64
LORA_ALPHA=16
LORA_DROPOUT=0.1
MAX_SEQ_LENGTH=1024
WANDB_ENTITY="337543-unimore"
WANDB_PROJECT="captioner"
WANDB_RUN_NAME="gemma2_9b_it_test1"
EARLY_STOPPING_PATIENCE=20
EARLY_STOPPING_THRESHOLD=0.0005

# Stampa informazioni
echo "Training di Gemma 2 9B IT con LoRA"
echo "Dataset di training: \$TRAIN_FILE"
echo "Dataset di validazione: \$VAL_FILE"
echo "Output: \$OUTPUT_DIR"
echo "Modello: \$MODEL_NAME"
echo "Batch size: \$BATCH_SIZE"
echo "Gradient accumulation steps: \$GRADIENT_ACCUMULATION_STEPS"
echo "Learning rate: \$LEARNING_RATE"
echo "Epoche: \$NUM_TRAIN_EPOCHS"
echo "Job ID: \$SLURM_JOB_ID"
echo "Nodo: \$SLURMD_NODENAME"
echo "GPU Allocate (da SLURM): \$SLURM_GPUS_ON_NODE"

# Crea la directory di output
mkdir -p "\$OUTPUT_DIR"

# Esegui lo script di training
echo "Eseguo lo script di training: /work/tesi_ediluzio/experiments/xml_direct_input/train_gemma_test1.py"
\$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/train_gemma_test1.py \
    --train_file "\$TRAIN_FILE" \
    --val_file "\$VAL_FILE" \
    --output_dir "\$OUTPUT_DIR" \
    --model_name "\$MODEL_NAME" \
    --batch_size "\$BATCH_SIZE" \
    --gradient_accumulation_steps "\$GRADIENT_ACCUMULATION_STEPS" \
    --learning_rate "\$LEARNING_RATE" \
    --num_train_epochs "\$NUM_TRAIN_EPOCHS" \
    --save_steps "\$SAVE_STEPS" \
    --save_total_limit "\$SAVE_TOTAL_LIMIT" \
    --logging_steps "\$LOGGING_STEPS" \
    --eval_steps "\$EVAL_STEPS" \
    --lora_r "\$LORA_R" \
    --lora_alpha "\$LORA_ALPHA" \
    --lora_dropout "\$LORA_DROPOUT" \
    --max_seq_length "\$MAX_SEQ_LENGTH" \
    --use_wandb \
    --wandb_entity "\$WANDB_ENTITY" \
    --wandb_project "\$WANDB_PROJECT" \
    --wandb_run_name "\$WANDB_RUN_NAME" \
    --early_stopping_patience "\$EARLY_STOPPING_PATIENCE" \
    --early_stopping_threshold "\$EARLY_STOPPING_THRESHOLD"

# Verifica il codice di uscita
if [ \$? -eq 0 ]; then
    echo "Training di Gemma 2 9B IT completato con successo"
else
    echo "Training di Gemma 2 9B IT fallito con codice di errore: \$?"
    exit 1
fi
EOF

echo "Job di analisi del dataset e training completato!"
