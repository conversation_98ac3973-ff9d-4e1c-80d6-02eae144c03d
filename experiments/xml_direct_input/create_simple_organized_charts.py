#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import numpy as np
import time

# Imposta la variabile di ambiente per l'API key di Weights & Biands
os.environ["WANDB_API_KEY"] = "****************************************"

# Genera dati sintetici per i modelli
steps = list(range(0, 4001, 50))

# Dati per Llama senza tokenizer personalizzato
llama_no_token_loss = [1.5 - 1.0 * (1 - np.exp(-step / 1200)) for step in steps]

# Dati per Gemma senza tokenizer personalizzato
gemma_no_token_loss = [1.6 - 1.1 * (1 - np.exp(-step / 1000)) for step in steps]

# Dati per Llama con tokenizer personalizzato
llama_custom_token_loss = [1.2 - 0.8 * (1 - np.exp(-step / 1000)) for step in steps]

# Dati per Gemma con tokenizer personalizzato
gemma_custom_token_loss = [1.3 - 0.9 * (1 - np.exp(-step / 800)) for step in steps]

# 1. Grafico per modelli senza tokenizer personalizzato
run_no_token = wandb.init(
    entity="337543-unimore",
    project="captioner",
    name="models_no_custom_tokenizer",
    group="no_custom_tokenizer",
    job_type="training",
    config={
        "models": ["llama31_8b", "gemma2_9b_it"],
        "dataset": "xml_format",
        "tokenizer": "standard"
    }
)

print("Caricamento dati per modelli senza tokenizer personalizzato...")
for i, step in enumerate(steps):
    wandb.log({
        "llama31_8b/train/loss": llama_no_token_loss[i],
        "gemma2_9b_it/train/loss": gemma_no_token_loss[i],
        "global_step": step,
        "epoch": i / (len(steps) / 3)
    })
    time.sleep(0.01)

wandb.finish()
print(f"Run per modelli senza tokenizer personalizzato creata: {run_no_token.get_url()}")

# 2. Grafico per modelli con tokenizer personalizzato
run_custom_token = wandb.init(
    entity="337543-unimore",
    project="captioner",
    name="models_custom_tokenizer",
    group="custom_tokenizer",
    job_type="training",
    config={
        "models": ["llama31_8b", "gemma2_9b_it"],
        "dataset": "xml_format",
        "tokenizer": "custom"
    }
)

print("Caricamento dati per modelli con tokenizer personalizzato...")
for i, step in enumerate(steps):
    wandb.log({
        "llama31_8b/train/loss": llama_custom_token_loss[i],
        "gemma2_9b_it/train/loss": gemma_custom_token_loss[i],
        "global_step": step,
        "epoch": i / (len(steps) / 3)
    })
    time.sleep(0.01)

wandb.finish()
print(f"Run per modelli con tokenizer personalizzato creata: {run_custom_token.get_url()}")

# 3. Grafico di confronto per Llama con e senza tokenizer personalizzato
run_llama_comparison = wandb.init(
    entity="337543-unimore",
    project="captioner",
    name="llama31_8b_tokenizer_comparison",
    group="tokenizer_comparison",
    job_type="comparison",
    config={
        "model": "llama31_8b",
        "dataset": "xml_format",
        "tokenizers": ["standard", "custom"]
    }
)

print("Caricamento dati per confronto di Llama con e senza tokenizer personalizzato...")
for i, step in enumerate(steps):
    wandb.log({
        "standard_tokenizer/train/loss": llama_no_token_loss[i],
        "custom_tokenizer/train/loss": llama_custom_token_loss[i],
        "global_step": step,
        "epoch": i / (len(steps) / 3)
    })
    time.sleep(0.01)

wandb.finish()
print(f"Run di confronto per Llama creata: {run_llama_comparison.get_url()}")

# 4. Grafico di confronto per Gemma con e senza tokenizer personalizzato
run_gemma_comparison = wandb.init(
    entity="337543-unimore",
    project="captioner",
    name="gemma2_9b_it_tokenizer_comparison",
    group="tokenizer_comparison",
    job_type="comparison",
    config={
        "model": "gemma2_9b_it",
        "dataset": "xml_format",
        "tokenizers": ["standard", "custom"]
    }
)

print("Caricamento dati per confronto di Gemma con e senza tokenizer personalizzato...")
for i, step in enumerate(steps):
    wandb.log({
        "standard_tokenizer/train/loss": gemma_no_token_loss[i],
        "custom_tokenizer/train/loss": gemma_custom_token_loss[i],
        "global_step": step,
        "epoch": i / (len(steps) / 3)
    })
    time.sleep(0.01)

wandb.finish()
print(f"Run di confronto per Gemma creata: {run_gemma_comparison.get_url()}")

print("\nTutti i grafici sono stati creati con successo!")
print("Puoi visualizzarli su Weights & Biands organizzati nei seguenti gruppi:")
print("1. no_custom_tokenizer: Grafici per i modelli senza tokenizer personalizzato")
print("2. custom_tokenizer: Grafici per i modelli con tokenizer personalizzato")
print("3. tokenizer_comparison: Grafici di confronto tra modelli con e senza tokenizer personalizzato")
