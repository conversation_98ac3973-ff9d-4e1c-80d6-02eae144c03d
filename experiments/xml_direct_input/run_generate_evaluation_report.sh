#!/bin/bash

# Script per generare un report HTML completo di valutazione dei modelli

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Parametri
OUTPUT_DIR="/work/tesi_ediluzio/evaluation/reports"
MODELS_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs"
METRICS_DIR="/work/tesi_ediluzio/evaluation/checkpoint_metrics"
EXAMPLES_FILE="/work/tesi_ediluzio/evaluation/examples/qualitative_examples.json"
TITLE="Valutazione dei Modelli di Generazione Didascalie SVG"
LOGO_PATH="/work/tesi_ediluzio/evaluation/reports/unimore_logo.png"
NUM_EXAMPLES=5
INCLUDE_ZERO_SHOT="--include_zero_shot"
ZERO_SHOT_METRICS="/work/tesi_ediluzio/evaluation/zero_shot/zero_shot_metrics.json"
ZERO_SHOT_EXAMPLES="/work/tesi_ediluzio/evaluation/zero_shot/zero_shot_examples.json"

# Crea le directory necessarie
mkdir -p "$OUTPUT_DIR"

# Stampa informazioni
echo "Generazione Report HTML di Valutazione"
echo "Directory di output: $OUTPUT_DIR"
echo "Directory dei modelli: $MODELS_DIR"
echo "Directory delle metriche: $METRICS_DIR"
echo "File degli esempi: $EXAMPLES_FILE"
echo "Titolo: $TITLE"
echo "Logo: $LOGO_PATH"
echo "Numero di esempi per fascia: $NUM_EXAMPLES"
echo "Includi valutazione zero-shot: $INCLUDE_ZERO_SHOT"
echo "Metriche zero-shot: $ZERO_SHOT_METRICS"
echo "Esempi zero-shot: $ZERO_SHOT_EXAMPLES"

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/generate_evaluation_report.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/generate_evaluation_report.py \
    --output_dir "$OUTPUT_DIR" \
    --models_dir "$MODELS_DIR" \
    --metrics_dir "$METRICS_DIR" \
    --examples_file "$EXAMPLES_FILE" \
    --title "$TITLE" \
    --logo_path "$LOGO_PATH" \
    --num_examples "$NUM_EXAMPLES" \
    $INCLUDE_ZERO_SHOT \
    --zero_shot_metrics "$ZERO_SHOT_METRICS" \
    --zero_shot_examples "$ZERO_SHOT_EXAMPLES"

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Generazione del report HTML completata con successo"
    echo "Report disponibile in: $OUTPUT_DIR/evaluation_report.html"
    
    # Copia il report in una directory accessibile via web (se disponibile)
    if [ -d "/var/www/html/reports" ]; then
        cp "$OUTPUT_DIR/evaluation_report.html" "/var/www/html/reports/"
        echo "Report copiato in: /var/www/html/reports/evaluation_report.html"
        echo "Accessibile via: http://localhost/reports/evaluation_report.html"
    fi
else
    echo "Generazione del report HTML fallita con codice di errore: $?"
fi
