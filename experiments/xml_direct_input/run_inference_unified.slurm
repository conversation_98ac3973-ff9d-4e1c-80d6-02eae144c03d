#!/bin/bash
#SBATCH --job-name=unified_infer
#SBATCH --output=/work/tesi_ediluzio/logs/unified_infer_%j_%a.out
#SBATCH --error=/work/tesi_ediluzio/logs/unified_infer_%j_%a.err
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=12:00:00
#SBATCH --partition=all_usr_prod

# Attiva l'ambiente virtuale
source /work/tesi_ediluzio/svg_captioning_env/bin/activate

# Imposta le variabili di ambiente
export PYTHONPATH=/work/tesi_ediluzio:$PYTHONPATH
export TRANSFORMERS_CACHE=/work/tesi_ediluzio/.cache/huggingface/transformers
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

# Parametri da linea di comando
MODEL_NAME_OR_PATH=$1
OUTPUT_FILE=$2
LORA_PATH=${3:-""}
USE_CUSTOM_TOKENIZER=${4:-false}
LOAD_IN_8BIT=${5:-false}
LOAD_IN_4BIT=${6:-false}

# Imposta i flag in base ai parametri
CUSTOM_TOKENIZER_FLAG=""
if [ "$USE_CUSTOM_TOKENIZER" = true ]; then
    CUSTOM_TOKENIZER_FLAG="--use_custom_tokenizer"
fi

LOAD_8BIT_FLAG=""
if [ "$LOAD_IN_8BIT" = true ]; then
    LOAD_8BIT_FLAG="--load_in_8bit"
fi

LOAD_4BIT_FLAG=""
if [ "$LOAD_IN_4BIT" = true ]; then
    LOAD_4BIT_FLAG="--load_in_4bit"
fi

LORA_PATH_FLAG=""
if [ -n "$LORA_PATH" ]; then
    LORA_PATH_FLAG="--lora_path $LORA_PATH"
fi

# Esegui lo script di inferenza
echo "Esecuzione dell'inferenza con il modello: $MODEL_NAME_OR_PATH"
echo "Output: $OUTPUT_FILE"
echo "LoRA path: $LORA_PATH"
echo "Use custom tokenizer: $USE_CUSTOM_TOKENIZER"
echo "Load in 8-bit: $LOAD_IN_8BIT"
echo "Load in 4-bit: $LOAD_IN_4BIT"

python /work/tesi_ediluzio/experiments/xml_direct_input/run_inference_unified.py \
    --model_name_or_path "$MODEL_NAME_OR_PATH" \
    --output_file "$OUTPUT_FILE" \
    $LORA_PATH_FLAG \
    $CUSTOM_TOKENIZER_FLAG \
    $LOAD_8BIT_FLAG \
    $LOAD_4BIT_FLAG \
    --test_file /work/tesi_ediluzio/data/processed/test_set_final_2k.json \
    --max_new_tokens 100 \
    --temperature 0.7 \
    --top_p 0.9 \
    --top_k 50 \
    --repetition_penalty 1.1
