# Nome file: train_xml_slurm_token_xml.py (modificato per LoRA con supporto per tokenizer personalizzato)

import os
import json
import argparse
import logging
import shutil
import glob
from typing import Dict, List, Any

import torch
from torch.utils.data import Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    AutoConfig,
    Trainer,
    TrainingArguments,
    HfArgumentParser,
    BitsAndBytesConfig, # Per configurazione quantizzazione
    TrainerCallback # Per callback personalizzati
)
# --- PEFT/LoRA ---
# Importa le classi necessarie da PEFT
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training

# Importa le funzioni per il tokenizer personalizzato
import sys
sys.path.append("/work/tesi_ediluzio")
from shared.svg_core.custom_tokenizer_utils import build_tokenizer, tokenize_svg

# Assicurati: pip install transformers torch datasets accelerate bitsandbytes peft

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- CLASSE XmlCaptioningDataset ---
class XmlCaptioningDataset(Dataset):
    """
    Dataset per caricare coppie (stringa dati SVG, Didascalia) da un singolo file JSON.
    Prepara i dati per un modello Causal LM (decoder-only), gestendo
    la formattazione per modelli base o instruct.
    """
    def __init__(self, json_path: str, tokenizer: AutoTokenizer, max_length: int, model_is_instruct: bool = True):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.model_is_instruct = model_is_instruct

        self.has_chat_template = getattr(self.tokenizer, 'chat_template', None) is not None or \
                                 getattr(self.tokenizer, 'apply_chat_template', None) is not None
        if self.model_is_instruct and not self.has_chat_template:
             logger.warning("model_is_instruct=True ma il tokenizer non sembra avere un chat_template. Uso template manuale User/Assistant.")

        # Carica i dati dal file JSON
        logger.info(f"Caricamento dataset da: {json_path}")
        with open(json_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        logger.info(f"Caricati {len(self.data)} campioni.")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        svg_data = item.get("data", item.get("xml"))  # Stringa XML/SVG (supporta sia "data" che "xml")
        caption = item["caption"]  # Didascalia target

        # Formatta l'input in base al tipo di modello (instruct o base)
        if self.model_is_instruct:
            if self.has_chat_template:
                # Usa il chat template del modello se disponibile
                messages = [
                    {"role": "user", "content": f"Descrivi questa immagine SVG:\n\n{svg_data}"},
                    {"role": "assistant", "content": caption}
                ]
                text = self.tokenizer.apply_chat_template(messages, tokenize=False)
            else:
                # Template manuale per modelli instruct senza chat_template
                text = f"USER: Descrivi questa immagine SVG:\n\n{svg_data}\n\nASSISTANT: {caption}"
        else:
            # Per modelli base (non-instruct), usa un formato semplice
            text = f"SVG: {svg_data}\nDescrizione: {caption}"

        # Tokenizza l'input
        encodings = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding="max_length",
            return_tensors="pt"
        )

        # Rimuovi la dimensione batch (trasforma [1, seq_len] in [seq_len])
        input_ids = encodings.input_ids.squeeze(0)
        attention_mask = encodings.attention_mask.squeeze(0)

        # Crea le labels (identiche agli input_ids per causal LM)
        labels = input_ids.clone()

        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": labels
        }

def main():
    # --- ARGPARSE ---
    parser = argparse.ArgumentParser(description="Fine-tuning LoRA per modelli di linguaggio su dati SVG")
    parser.add_argument("--config_path", type=str, required=True, help="Percorso al file di configurazione JSON")
    args = parser.parse_args()

    # Carica configurazione da file JSON
    with open(args.config_path, 'r') as f:
        config = json.load(f)

    # --- PARAMETRI PRINCIPALI ---
    model_name = config.get("model_name_or_path", "meta-llama/Llama-3.1-8B-Instruct")
    tokenizer_name = config.get("tokenizer_name", model_name)  # Default: stesso del modello
    data_file = config.get("data_file", "/work/tesi_ediluzio/data/processed/xml_format/train_set_final_xml.json")
    output_dir = config.get("output_dir", f"/work/tesi_ediluzio/experiments/xml_direct_input/outputs/{model_name.split('/')[-1].lower()}_lora")
    max_length = config.get("max_length", 1024)
    model_is_instruct = config.get("model_is_instruct", True)

    # --- PARAMETRI LORA ---
    lora_r = config.get("lora_r", 16)
    lora_alpha = config.get("lora_alpha", 32)
    lora_dropout = config.get("lora_dropout", 0.05)
    lora_target_modules = config.get("lora_target_modules", ["q_proj", "v_proj"])

    # --- PARAMETRI TOKENIZER PERSONALIZZATO ---
    use_custom_tokenizer = config.get("use_custom_tokenizer", False) or config.get("_use_custom_tokenizer", False)

    # --- GRADIENT CHECKPOINTING ---
    use_gc = config.get("gradient_checkpointing", True)

    # --- CARICAMENTO TOKENIZER ---
    logger.info(f"Caricamento tokenizer base: {tokenizer_name}")
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)

    # Aggiungi token speciali se necessario
    if use_custom_tokenizer:
        logger.info("Aggiunta token SVG custom al tokenizer...")
        # Lista di token SVG da aggiungere
        svg_tokens = [
            '<|Z|>', '<|SEP|>', '<|H|>', '<|stroke|>', '<|begin_of_style|>',
            '<|stroke-width|>', '<|end_of_svg|>', '<|currentColor|>', '<|C|>',
            '<|S|>', '<|begin_of_svg|>', '<|end_of_path|>', '<|Q|>', '<|A|>',
            '<|none|>', '<|T|>', '<|M|>', '<|end_of_style|>', '<|opacity|>',
            '<|V|>', '<|L|>', '<|begin_of_path|><|M|>', '<|color|>'
        ]
        logger.info(f"Token da aggiungere: {svg_tokens}")

        # Aggiungi i token al tokenizer
        num_added = tokenizer.add_tokens(svg_tokens, special_tokens=True)
        logger.info(f"Aggiunti {num_added} nuovi token speciali.")
        logger.info(f"Esempio nuovi token: {svg_tokens[:5]}...")

    # Assicurati che il tokenizer abbia un pad_token
    if tokenizer.pad_token is None:
        if tokenizer.eos_token is not None:
            tokenizer.pad_token = tokenizer.eos_token
            logger.warning(f"Uso eos_token ({tokenizer.eos_token}) come pad_token.")
        else:
            tokenizer.pad_token = tokenizer.unk_token
            logger.warning(f"Uso unk_token ({tokenizer.unk_token}) come pad_token.")

    logger.info(f"Tokenizer final pad_token: {tokenizer.pad_token}, ID: {tokenizer.pad_token_id}")

    # --- CREAZIONE DATASET ---
    logger.info("Creazione dataset con tokenizer aggiornato...")
    train_dataset = XmlCaptioningDataset(
        json_path=data_file,
        tokenizer=tokenizer,
        max_length=max_length,
        model_is_instruct=model_is_instruct
    )
    logger.info(f"Dataset creato con {len(train_dataset)} campioni.")

    # Non usiamo eval_dataset per ora
    eval_dataset = None

    # --- CARICAMENTO MODELLO ---
    logger.info(f"Caricamento modello: {model_name} (4-bit NF4)")

    # Configurazione quantizzazione 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.bfloat16,
        bnb_4bit_use_double_quant=True
    )

    # Imposta pad_token_id nel config del modello
    model_config = AutoConfig.from_pretrained(model_name)
    model_config.pad_token_id = tokenizer.pad_token_id
    logger.info(f"Impostato model_config.pad_token_id a {model_config.pad_token_id} prima del caricamento.")

    # Carica il modello con quantizzazione
    try:
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=bnb_config,
            device_map="auto"
        )
        logger.info("Modello caricato in 4-bit (device_map='auto').")
    except Exception as e:
        logger.error(f"Errore caricamento modello: {e}", exc_info=True)
        return

    # --- RESIZE EMBEDDINGS ---
    if use_custom_tokenizer:
        logger.info("Controllo/Ridimensionamento token embeddings del modello...")
        # Ridimensiona gli embeddings se necessario
        if len(tokenizer) > model.get_input_embeddings().weight.shape[0]:
            logger.info(f"Ridimensiono embeddings da {model.get_input_embeddings().weight.shape[0]} a {len(tokenizer)}")
            model.resize_token_embeddings(len(tokenizer))

    # --- CONFIGURAZIONE PEFT/LORA ---
    logger.info("Preparazione modello per K-bit training e LoRA...")
    try:
        # Prepara il modello per il training in k-bit
        logger.info(f"Chiamo prepare_model_for_kbit_training con use_gradient_checkpointing={use_gc}")
        model = prepare_model_for_kbit_training(model, use_gradient_checkpointing=use_gc)

        # Configura LoRA
        lora_config = LoraConfig(
            r=lora_r,
            lora_alpha=lora_alpha,
            target_modules=lora_target_modules,
            lora_dropout=lora_dropout,
            bias="none",
            task_type="CAUSAL_LM"
        )
        model = get_peft_model(model, lora_config)
        logger.info("Wrapper PEFT/LoRA applicato.")
        model.print_trainable_parameters() # Stampa parametri addestrabili
    except Exception as e:
        logger.error(f"Errore configurazione PEFT/LoRA: {e}", exc_info=True)
        return

    # --- TRAINING ARGUMENTS ---
    logger.info("Configurazione Training Arguments...")
    # Leggi tutti gli argomenti di training dal dizionario 'config' tranne quelli già usati
    peft_keys = ["lora_r", "lora_alpha", "lora_dropout", "lora_target_modules"]
    base_keys = ["model_name_or_path", "tokenizer_name", "data_file", "max_length", "model_is_instruct", "use_custom_tokenizer", "_use_custom_tokenizer"]
    training_args_dict = {k: v for k, v in config.items() if k not in base_keys + peft_keys}
    training_args_dict["output_dir"] = output_dir # Assicurati che output_dir sia qui
    training_args_dict["remove_unused_columns"] = False # Da non rimuovere per Trainer PEFT

    # Crea istanza TrainingArguments
    try:
        training_args = TrainingArguments(**training_args_dict) # Istanziazione diretta

        # Forza/Verifica gradient checkpointing (dovrebbe corrispondere a use_gc)
        if use_gc != training_args.gradient_checkpointing:
             logger.warning(f"Valore gradient_checkpointing in config ({use_gc}) non corrisponde a TrainingArguments ({training_args.gradient_checkpointing}). Verifico coerenza.")

        logger.info(f"Training Arguments finali: {training_args}")
    except Exception as e:
        logger.error(f"Errore config TrainingArguments: {e}\nDict: {training_args_dict}", exc_info=True)
        return

    # --- CLEANUP CALLBACK ---
    class CheckpointCleanupCallback(TrainerCallback):
        """Callback per eliminare i checkpoint vecchi e mantenere solo gli ultimi N."""
        def __init__(self, output_dir, save_total_limit=2):
            self.output_dir = output_dir
            self.save_total_limit = save_total_limit

        def on_save(self, args, state, control, **kwargs):
            """Chiamato dopo ogni salvataggio di checkpoint."""
            # Trova tutti i checkpoint
            checkpoint_dirs = glob.glob(os.path.join(self.output_dir, "checkpoint-*"))

            # Ordina per numero di step (dal più vecchio al più recente)
            checkpoint_dirs.sort(key=lambda x: int(x.split("-")[-1]))

            # Se abbiamo più checkpoint del limite, elimina i più vecchi
            if len(checkpoint_dirs) > self.save_total_limit:
                checkpoints_to_remove = checkpoint_dirs[:-self.save_total_limit]
                for checkpoint in checkpoints_to_remove:
                    logger.info(f"Eliminazione checkpoint vecchio: {checkpoint}")
                    try:
                        shutil.rmtree(checkpoint)
                    except Exception as e:
                        logger.warning(f"Errore durante l'eliminazione del checkpoint {checkpoint}: {e}")

            return control

    # --- TRAINER ---
    logger.info("Inizializzazione Trainer...")
    try:
        # Crea il callback per la pulizia dei checkpoint
        cleanup_callback = CheckpointCleanupCallback(
            output_dir=output_dir,
            save_total_limit=config.get("save_total_limit", 2)
        )

        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            tokenizer=tokenizer, # Passa il tokenizer per padding/saving corretto
            callbacks=[cleanup_callback] # Aggiungi il callback per la pulizia
            # data_collator non serve se si fa padding nel dataset/tokenizer
        )
        # Disabilita use_cache se si usa gradient checkpointing
        if training_args.gradient_checkpointing:
             if hasattr(model, 'config'): model.config.use_cache = False
             logger.info("Disabilitato model.config.use_cache per gradient checkpointing.")
    except Exception as e:
        logger.error(f"Errore inizializzazione Trainer: {e}", exc_info=True)
        return

    # --- TRAINING ---
    logger.info("Avvio training...")
    try:
        train_result = trainer.train()
        logger.info(f"Training completato. Metriche: {train_result.metrics}")

        # Salva modello finale
        logger.info(f"Salvataggio modello finale in {output_dir}")
        trainer.save_model(output_dir)

        # Salva metriche
        trainer.log_metrics("train", train_result.metrics)
        trainer.save_metrics("train", train_result.metrics)
        trainer.save_state()

        logger.info("Modello e metriche salvati con successo.")
    except Exception as e:
        logger.error(f"Errore durante il training: {e}", exc_info=True)
        return

if __name__ == "__main__":
    main()
