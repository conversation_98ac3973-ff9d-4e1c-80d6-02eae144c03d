#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import argparse
import wandb
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Crea un grafico combinato della loss per tutti i modelli")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs", help="Directory di output per i grafici")
    return parser.parse_args()

def get_run_data(api, run_name):
    """Ottiene i dati di una run da Weights & Biands."""
    runs = api.runs("337543-unimore/captioner", {"display_name": run_name})
    if runs:
        run = runs[0]
        history = run.history(keys=["train/loss", "_step"])
        return history
    return None

def create_combined_loss_plot(args):
    """Crea un grafico combinato della loss per tutti i modelli."""
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza l'API di Weights & Biands
    api = wandb.Api()
    
    # Ottieni i dati delle run
    llama_single_data = get_run_data(api, "llama31_8b_lora_xml_no_token")
    llama_continue_data = get_run_data(api, "llama31_8b_lora_xml_no_token_continue")
    gemma_single_data = get_run_data(api, "gemma2_9b_it_lora_xml_no_token")
    gemma_continue_data = get_run_data(api, "gemma2_9b_it_lora_xml_no_token_continue")
    
    # Crea un nuovo dataframe per Llama combinando i dati
    llama_combined = pd.DataFrame()
    if llama_single_data is not None:
        llama_combined = llama_single_data.copy()
    if llama_continue_data is not None:
        # Aggiungi un offset agli step per continuare da dove è terminato il training precedente
        if not llama_combined.empty:
            max_step = llama_combined["_step"].max()
            llama_continue_data["_step"] = llama_continue_data["_step"] + max_step
        llama_combined = pd.concat([llama_combined, llama_continue_data])
    
    # Crea un nuovo dataframe per Gemma combinando i dati
    gemma_combined = pd.DataFrame()
    if gemma_single_data is not None:
        gemma_combined = gemma_single_data.copy()
    if gemma_continue_data is not None:
        # Aggiungi un offset agli step per continuare da dove è terminato il training precedente
        if not gemma_combined.empty:
            max_step = gemma_combined["_step"].max()
            gemma_continue_data["_step"] = gemma_continue_data["_step"] + max_step
        gemma_combined = pd.concat([gemma_combined, gemma_continue_data])
    
    # Crea il grafico
    plt.figure(figsize=(12, 8))
    
    if not llama_combined.empty:
        plt.plot(llama_combined["_step"], llama_combined["train/loss"], label="Llama 3.1 8B", color="blue")
    
    if not gemma_combined.empty:
        plt.plot(gemma_combined["_step"], gemma_combined["train/loss"], label="Gemma 2 9B IT", color="red")
    
    plt.title("Training Loss - Llama 3.1 8B vs Gemma 2 9B IT", fontsize=16)
    plt.xlabel("Training Steps", fontsize=14)
    plt.ylabel("Loss", fontsize=14)
    plt.grid(True, linestyle="--", alpha=0.7)
    plt.legend(fontsize=12)
    
    # Salva il grafico
    os.makedirs(args.output_dir, exist_ok=True)
    output_path = os.path.join(args.output_dir, "combined_loss_plot.png")
    plt.savefig(output_path, dpi=300, bbox_inches="tight")
    
    print(f"Grafico salvato in {output_path}")
    
    # Carica il grafico su Weights & Biands
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=f"Combined Loss Plot {datetime.now().strftime('%Y-%m-%d')}",
        job_type="visualization"
    )
    
    # Carica l'immagine su wandb
    wandb.log({"Combined Loss Plot": wandb.Image(output_path)})
    
    # Crea una tabella con i risultati finali
    results_table = wandb.Table(
        columns=["Model", "Best Loss", "Steps to Convergence"]
    )
    
    # Aggiungi i risultati finali
    if not llama_combined.empty:
        best_loss = llama_combined["train/loss"].min()
        steps_to_convergence = llama_combined.loc[llama_combined["train/loss"] == best_loss, "_step"].iloc[0]
        results_table.add_data("Llama 3.1 8B", f"{best_loss:.4f}", f"{steps_to_convergence}")
    
    if not gemma_combined.empty:
        best_loss = gemma_combined["train/loss"].min()
        steps_to_convergence = gemma_combined.loc[gemma_combined["train/loss"] == best_loss, "_step"].iloc[0]
        results_table.add_data("Gemma 2 9B IT", f"{best_loss:.4f}", f"{steps_to_convergence}")
    
    # Carica la tabella su wandb
    wandb.log({"Final Results": results_table})
    
    # Chiudi wandb
    wandb.finish()

def main():
    args = parse_args()
    create_combined_loss_plot(args)

if __name__ == "__main__":
    main()
