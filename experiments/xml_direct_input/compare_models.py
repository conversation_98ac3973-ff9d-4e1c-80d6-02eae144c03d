#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per confrontare i risultati di diversi modelli.
"""

import argparse
import json
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from tabulate import tabulate

def parse_args():
    parser = argparse.ArgumentParser(description="Confronta i risultati di diversi modelli")
    parser.add_argument("--metrics_files", type=str, nargs="+", required=True, help="File JSON con le metriche")
    parser.add_argument("--model_names", type=str, nargs="+", required=True, help="Nomi dei modelli")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i grafici")
    return parser.parse_args()

def load_metrics(metrics_file):
    """Carica le metriche dal file JSON."""
    with open(metrics_file, 'r', encoding='utf-8') as f:
        metrics = json.load(f)
    
    return metrics["average_metrics"]

def create_comparison_table(metrics_dict, model_names):
    """Crea una tabella di confronto."""
    # Crea un DataFrame
    df = pd.DataFrame(metrics_dict).T
    df.columns = model_names
    
    # Formatta la tabella
    table = tabulate(df, headers="keys", tablefmt="grid", floatfmt=".4f")
    
    return table, df

def create_comparison_plots(df, output_dir):
    """Crea grafici di confronto."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Imposta lo stile
    sns.set(style="whitegrid")
    plt.rcParams.update({'font.size': 12})
    
    # Crea un grafico a barre per ogni metrica
    for metric in df.index:
        plt.figure(figsize=(10, 6))
        ax = sns.barplot(x=df.columns, y=df.loc[metric])
        plt.title(f"Confronto {metric.upper()}")
        plt.ylabel(metric)
        plt.xlabel("Modello")
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # Aggiungi i valori sopra le barre
        for i, v in enumerate(df.loc[metric]):
            ax.text(i, v + 0.01, f"{v:.4f}", ha='center')
        
        # Salva il grafico
        plt.savefig(os.path.join(output_dir, f"comparison_{metric}.png"), dpi=300)
        plt.close()
    
    # Crea un grafico radar per confrontare tutte le metriche
    metrics = df.index.tolist()
    models = df.columns.tolist()
    
    # Normalizza i valori per il grafico radar
    df_normalized = df.copy()
    for metric in metrics:
        max_val = df.loc[metric].max()
        min_val = df.loc[metric].min()
        if max_val > min_val:
            df_normalized.loc[metric] = (df.loc[metric] - min_val) / (max_val - min_val)
    
    # Crea il grafico radar
    plt.figure(figsize=(10, 10))
    
    # Calcola gli angoli per ogni metrica
    angles = [n / float(len(metrics)) * 2 * 3.14159 for n in range(len(metrics))]
    angles += angles[:1]  # Chiudi il cerchio
    
    # Crea il subplot
    ax = plt.subplot(111, polar=True)
    
    # Aggiungi ogni modello
    for i, model in enumerate(models):
        values = df_normalized.loc[:, model].tolist()
        values += values[:1]  # Chiudi il cerchio
        
        # Disegna il poligono
        ax.plot(angles, values, linewidth=2, linestyle='solid', label=model)
        ax.fill(angles, values, alpha=0.1)
    
    # Imposta le etichette
    plt.xticks(angles[:-1], metrics, size=12)
    
    # Aggiungi la legenda
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    # Salva il grafico
    plt.savefig(os.path.join(output_dir, "comparison_radar.png"), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    args = parse_args()
    
    # Verifica che il numero di file di metriche e nomi di modelli sia lo stesso
    if len(args.metrics_files) != len(args.model_names):
        raise ValueError("Il numero di file di metriche e nomi di modelli deve essere lo stesso")
    
    # Carica le metriche
    metrics_dict = {}
    for metrics_file, model_name in zip(args.metrics_files, args.model_names):
        print(f"Caricamento delle metriche da {metrics_file}...")
        metrics_dict[model_name] = load_metrics(metrics_file)
    
    # Crea la tabella di confronto
    print("Creazione della tabella di confronto...")
    table, df = create_comparison_table(metrics_dict, args.model_names)
    
    # Stampa la tabella
    print("\nTabella di confronto:")
    print(table)
    
    # Crea i grafici di confronto
    print("\nCreazione dei grafici di confronto...")
    create_comparison_plots(df, args.output_dir)
    
    # Salva la tabella come CSV
    csv_file = os.path.join(args.output_dir, "comparison_table.csv")
    df.to_csv(csv_file)
    print(f"Tabella salvata in {csv_file}")
    
    # Salva la tabella come HTML
    html_file = os.path.join(args.output_dir, "comparison_table.html")
    df.to_html(html_file)
    print(f"Tabella HTML salvata in {html_file}")
    
    print(f"\nGrafici salvati in {args.output_dir}")

if __name__ == "__main__":
    main()
