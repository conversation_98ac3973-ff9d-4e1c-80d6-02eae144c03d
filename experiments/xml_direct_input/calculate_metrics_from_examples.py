#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per calcolare le metriche dai file di esempi esistenti.
"""

import os
import sys
import json
import argparse
import logging
import numpy as np
import torch
import nltk
import ssl
from tqdm import tqdm
import wandb
from pycocoevalcap.cider.cider import Cider
from pycocoevalcap.meteor.meteor import Meteor
from pycocoevalcap.bleu.bleu import Ble<PERSON>
from pycocoevalcap.tokenizer.ptbtokenizer import PTBTokenizer

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Assicurati che NLTK abbia i dati necessari
try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context

nltk.download("punkt")
nltk.download("wordnet")
nltk.download("omw-1.4")
nltk.download("stopwords")

def parse_args():
    parser = argparse.ArgumentParser(description="Calcolo delle metriche dai file di esempi esistenti")
    parser.add_argument("--examples_file", type=str, required=True, help="File JSON con gli esempi generati")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i risultati")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner_zero_shot", help="Nome del progetto Weights & Biands")
    return parser.parse_args()

def calculate_caption_stats(hypotheses):
    """Calcola statistiche sulle didascalie generate."""
    # Lunghezza delle didascalie (in token)
    lengths = [len(nltk.word_tokenize(hyp)) for hyp in hypotheses]

    # Diversità del vocabolario
    all_tokens = []
    for hyp in hypotheses:
        all_tokens.extend(nltk.word_tokenize(hyp.lower()))

    unique_tokens = len(set(all_tokens))
    total_tokens = len(all_tokens)

    # Type-Token Ratio (misura di diversità lessicale)
    ttr = unique_tokens / total_tokens if total_tokens > 0 else 0

    # Self-BLEU (misura di diversità tra le didascalie)
    self_bleu = 0
    if len(hypotheses) > 1:
        from nltk.translate.bleu_score import corpus_bleu, SmoothingFunction
        self_bleu_scores = []
        for i, hyp in enumerate(hypotheses):
            other_hyps = [h for j, h in enumerate(hypotheses) if j != i]
            if other_hyps:
                # Prendi fino a 10 altre didascalie per efficienza
                other_hyps = other_hyps[:10]
                hyp_tokens = nltk.word_tokenize(hyp.lower())
                other_hyps_tokens = [nltk.word_tokenize(h.lower()) for h in other_hyps]

                # Calcola BLEU-4 tra questa didascalia e le altre
                smoothing = SmoothingFunction().method1
                bleu = corpus_bleu([other_hyps_tokens], [hyp_tokens],
                                   weights=(0.25, 0.25, 0.25, 0.25),
                                   smoothing_function=smoothing)
                self_bleu_scores.append(bleu)

        if self_bleu_scores:
            self_bleu = np.mean(self_bleu_scores)

    return {
        'caption_length_mean': np.mean(lengths),
        'caption_length_std': np.std(lengths),
        'caption_length_min': np.min(lengths),
        'caption_length_max': np.max(lengths),
        'vocabulary_size': unique_tokens,
        'type_token_ratio': ttr,
        'self_bleu': self_bleu
    }

def evaluate_with_coco_metrics(references, hypotheses):
    """Valuta le didascalie con le metriche COCO (BLEU, METEOR, CIDEr)."""
    # Prepara i dati nel formato richiesto dalle metriche COCO
    gts = {}
    res = {}

    for i, (ref, hyp) in enumerate(zip(references, hypotheses)):
        gts[i] = [{'caption': ref}]
        res[i] = [{'caption': hyp}]

    # Tokenizza le didascalie
    tokenizer = PTBTokenizer()
    gts = tokenizer.tokenize(gts)
    res = tokenizer.tokenize(res)

    # Calcola BLEU
    scorer = Bleu(n=4)
    bleu_score, bleu_scores = scorer.compute_score(gts, res)
    print(scorer.compute_score(gts, res))

    # Calcola METEOR
    scorer = Meteor()
    meteor_score, meteor_scores = scorer.compute_score(gts, res)

    # Calcola CIDEr
    scorer = Cider()
    cider_score, cider_scores = scorer.compute_score(gts, res)

    # Restituisci le metriche
    return {
        'bleu1': bleu_score[0],
        'bleu2': bleu_score[1],
        'bleu3': bleu_score[2],
        'bleu4': bleu_score[3],
        'meteor': meteor_score,
        'cider': cider_score
    }

def calculate_metrics_from_examples(examples_file, output_dir, wandb_entity, wandb_project):
    """Calcola le metriche dai file di esempi esistenti."""
    # Crea la directory di output
    os.makedirs(output_dir, exist_ok=True)
    
    # Carica gli esempi
    with open(examples_file, "r") as f:
        examples = json.load(f)
    
    # Estrai il nome del modello dal nome del file
    model_name = os.path.basename(examples_file).split("_zero_shot")[0]
    wandb_run_name = f"{model_name}_zero_shot_metrics"
    
    # Inizializza wandb
    wandb.init(
        entity=wandb_entity,
        project=wandb_project,
        name=wandb_run_name,
        config={
            "model_name": model_name,
            "num_samples": len(examples)
        }
    )
    
    # Estrai le didascalie di riferimento e generate
    references = [example["true_caption"] for example in examples]
    hypotheses = [example["generated_caption"] for example in examples]
    inference_times = [example["inference_time"] for example in examples]
    
    # Calcola le metriche COCO
    logger.info(f"Calcolo delle metriche COCO per {model_name}")
    coco_metrics = evaluate_with_coco_metrics(references, hypotheses)
    
    # Calcola statistiche sulle didascalie
    logger.info(f"Calcolo delle statistiche sulle didascalie per {model_name}")
    caption_stats = calculate_caption_stats(hypotheses)
    
    # Statistiche sul tempo di inferenza
    inference_time_stats = {
        "inference_time_mean": np.mean(inference_times),
        "inference_time_std": np.std(inference_times),
        "inference_time_min": np.min(inference_times),
        "inference_time_max": np.max(inference_times),
        "inference_time_total": np.sum(inference_times)
    }
    
    # Combina tutte le metriche
    metrics = {
        **coco_metrics,
        **caption_stats,
        **inference_time_stats,
        "num_samples": len(examples)
    }
    
    # Salva le metriche
    metrics_file = os.path.join(output_dir, f"{model_name}_zero_shot_metrics.json")
    with open(metrics_file, "w") as f:
        json.dump(metrics, f, indent=2)
    
    # Log delle metriche su wandb
    wandb.log(metrics)
    
    # Chiudi wandb
    wandb.finish()
    
    logger.info(f"Metriche calcolate e salvate in: {metrics_file}")
    
    return metrics

def main():
    args = parse_args()
    
    # Calcola le metriche dai file di esempi esistenti
    metrics = calculate_metrics_from_examples(
        examples_file=args.examples_file,
        output_dir=args.output_dir,
        wandb_entity=args.wandb_entity,
        wandb_project=args.wandb_project
    )
    
    # Stampa le metriche principali
    print("\nRisultati del calcolo delle metriche:")
    print(f"BLEU-1: {metrics['bleu1']:.4f}")
    print(f"BLEU-4: {metrics['bleu4']:.4f}")
    print(f"METEOR: {metrics['meteor']:.4f}")
    print(f"CIDEr: {metrics['cider']:.4f}")
    print(f"Tempo medio di inferenza: {metrics['inference_time_mean']:.4f}s")

if __name__ == "__main__":
    main()
