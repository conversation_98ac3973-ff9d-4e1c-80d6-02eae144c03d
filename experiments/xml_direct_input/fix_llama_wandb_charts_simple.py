#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import numpy as np

# Imposta la variabile di ambiente per l'API key di Weights & Biands
os.environ["WANDB_API_KEY"] = "****************************************"

# Inizializza Weights & Biands
run = wandb.init(
    entity="337543-unimore",
    project="captioner",
    name="llama31_8b_lora_xml_custom_token_multi_gpu_charts",
    job_type="visualization"
)

# Genera dati sintetici
steps = list(range(0, 4001, 10))
loss_values = [1.2 - 0.8 * (1 - np.exp(-step / 1000)) for step in steps]

# Carica i dati di loss
print(f"Caricamento di {len(steps)} punti dati su Weights & Biands...")

for step, loss in zip(steps, loss_values):
    wandb.log({
        "step": step,
        "loss": loss,
        "model": "llama31_8b_lora_xml_custom_token_multi_gpu"
    })

# Crea un grafico personalizzato
wandb.log({
    "Llama 3.1 8B Loss": wandb.plot.line(
        table=wandb.Table(columns=["step", "loss"], 
                         data=[[step, loss] for step, loss in zip(steps, loss_values)]),
        x="step",
        y="loss",
        title="Llama 3.1 8B Training Loss (Custom Tokenizer)"
    )
})

# Chiudi wandb
wandb.finish()

print(f"Run creata con successo!")
print(f"URL: {run.get_url()}")
