#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per monitorare la convergenza dei modelli in training e avviare automaticamente
il training successivo con tokenizer personalizzato utilizzando i pesi convergenti.
"""

import os
import sys
import json
import glob
import time
import argparse
import logging
import subprocess
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("logs/convergence_monitor.log")
    ],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Monitoraggio della convergenza e avvio del training successivo")
    parser.add_argument("--llama_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence",
                        help="Directory dei checkpoint di Llama 3.1 8B")
    parser.add_argument("--gemma_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence",
                        help="Directory dei checkpoint di Gemma 2 9B IT")
    parser.add_argument("--llama_job_id", type=str, default="2565298",
                        help="ID del job SLURM di Llama 3.1 8B")
    parser.add_argument("--gemma_job_id", type=str, default="2565300",
                        help="ID del job SLURM di Gemma 2 9B IT")
    parser.add_argument("--check_interval", type=int, default=1800,
                        help="Intervallo di controllo in secondi (default: 30 minuti)")
    parser.add_argument("--patience", type=int, default=5,
                        help="Numero di controlli consecutivi senza cambiamenti prima di considerare il training completato")
    parser.add_argument("--llama_config", type=str,
                        default="/work/tesi_ediluzio/experiments/xml_direct_input/configs/llama31_8b_custom_token_from_convergence_multi_gpu.json",
                        help="File di configurazione per il training di Llama con tokenizer personalizzato")
    parser.add_argument("--gemma_config", type=str,
                        default="/work/tesi_ediluzio/experiments/xml_direct_input/configs/gemma2_9b_it_custom_token_from_convergence_multi_gpu.json",
                        help="File di configurazione per il training di Gemma con tokenizer personalizzato")
    parser.add_argument("--use_multi_gpu", type=bool, default=True,
                        help="Utilizzare il training multi-GPU")
    return parser.parse_args()

def check_job_status(job_id: str) -> bool:
    """Verifica se un job SLURM è ancora in esecuzione."""
    try:
        result = subprocess.run(["squeue", "-j", job_id], capture_output=True, text=True)

        # Se il job non è più nella coda, è terminato
        return job_id in result.stdout
    except Exception as e:
        logger.error(f"Errore nel controllo dello stato del job {job_id}: {e}")
        return False

def find_best_checkpoint(checkpoint_dir: str) -> Optional[str]:
    """Trova il miglior checkpoint in base al file trainer_state.json."""
    # Cerca il file trainer_state.json
    trainer_state_path = os.path.join(checkpoint_dir, "trainer_state.json")

    if not os.path.exists(trainer_state_path):
        logger.warning(f"File trainer_state.json non trovato in {checkpoint_dir}")
        return None

    try:
        with open(trainer_state_path, "r") as f:
            trainer_state = json.load(f)

        # Cerca il miglior checkpoint
        best_model_checkpoint = trainer_state.get("best_model_checkpoint")

        if best_model_checkpoint:
            logger.info(f"Miglior checkpoint trovato: {best_model_checkpoint}")
            return best_model_checkpoint
        else:
            logger.warning("Nessun miglior checkpoint trovato nel trainer_state.json")
            return None
    except Exception as e:
        logger.error(f"Errore nella lettura del file trainer_state.json: {e}")
        return None

def find_checkpoint_best(checkpoint_dir: str) -> Optional[str]:
    """Cerca la directory checkpoint-best."""
    checkpoint_best_path = os.path.join(checkpoint_dir, "checkpoint-best")

    if os.path.exists(checkpoint_best_path) and os.path.isdir(checkpoint_best_path):
        logger.info(f"Directory checkpoint-best trovata: {checkpoint_best_path}")
        return checkpoint_best_path
    else:
        logger.warning(f"Directory checkpoint-best non trovata in {checkpoint_dir}")
        return None

def update_config_with_best_checkpoint(config_path: str, best_checkpoint: str) -> str:
    """Aggiorna il file di configurazione con il percorso del miglior checkpoint."""
    try:
        with open(config_path, "r") as f:
            config = json.load(f)

        # Aggiorna il percorso del checkpoint
        config["base_model_adapter_path"] = best_checkpoint

        # Crea un nuovo file di configurazione
        new_config_path = config_path.replace(".json", f"_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

        with open(new_config_path, "w") as f:
            json.dump(config, f, indent=4)

        logger.info(f"Configurazione aggiornata salvata in {new_config_path}")
        return new_config_path
    except Exception as e:
        logger.error(f"Errore nell'aggiornamento della configurazione: {e}")
        return config_path

def launch_training_job(config_path: str, model_type: str, use_multi_gpu: bool = True) -> Optional[str]:
    """Avvia un job di training con la configurazione specificata."""
    try:
        # Determina quale script SLURM utilizzare
        if model_type.lower() == "llama":
            if use_multi_gpu:
                slurm_script = "/work/tesi_ediluzio/experiments/xml_direct_input/run_llama_custom_token_from_convergence_multi_gpu.slurm"
            else:
                slurm_script = "/work/tesi_ediluzio/experiments/xml_direct_input/run_llama_custom_token_from_convergence.slurm"
        elif model_type.lower() == "gemma":
            if use_multi_gpu:
                slurm_script = "/work/tesi_ediluzio/experiments/xml_direct_input/run_gemma_custom_token_from_convergence_multi_gpu.slurm"
            else:
                slurm_script = "/work/tesi_ediluzio/experiments/xml_direct_input/run_gemma_custom_token_from_convergence.slurm"
        else:
            logger.error(f"Tipo di modello non supportato: {model_type}")
            return None

        logger.info(f"Utilizzo dello script SLURM: {slurm_script}")

        # Avvia il job SLURM
        result = subprocess.run(["sbatch", slurm_script, config_path], capture_output=True, text=True)

        # Estrai l'ID del job
        if result.returncode == 0:
            job_id = result.stdout.strip().split()[-1]
            logger.info(f"Job di training avviato con ID: {job_id}")
            return job_id
        else:
            logger.error(f"Errore nell'avvio del job: {result.stderr}")
            return None
    except Exception as e:
        logger.error(f"Errore nell'avvio del job di training: {e}")
        return None

def main():
    args = parse_args()

    # Inizializza i contatori di pazienza
    llama_patience_counter = 0
    gemma_patience_counter = 0

    # Inizializza le variabili per tenere traccia dei checkpoint
    last_llama_best_checkpoint = None
    last_gemma_best_checkpoint = None

    # Inizializza le variabili per tenere traccia dei job avviati
    llama_new_job_id = None
    gemma_new_job_id = None

    logger.info("Avvio del monitoraggio della convergenza")

    while True:
        try:
            # Verifica lo stato dei job originali
            llama_job_running = check_job_status(args.llama_job_id)
            gemma_job_running = check_job_status(args.gemma_job_id)

            # Verifica se i job sono terminati e se è stato avviato un nuovo job
            if not llama_job_running and llama_new_job_id is None:
                logger.info(f"Il job di Llama {args.llama_job_id} è terminato")

                # Cerca il miglior checkpoint
                llama_best_checkpoint = find_checkpoint_best(args.llama_dir)

                if llama_best_checkpoint:
                    # Aggiorna la configurazione
                    llama_config_path = update_config_with_best_checkpoint(args.llama_config, llama_best_checkpoint)

                    # Avvia il nuovo job
                    llama_new_job_id = launch_training_job(llama_config_path, "llama", args.use_multi_gpu)

                    if llama_new_job_id:
                        logger.info(f"Nuovo job di training per Llama avviato con ID: {llama_new_job_id}")
                    else:
                        logger.error("Impossibile avviare il nuovo job di training per Llama")
                else:
                    logger.warning("Nessun checkpoint-best trovato per Llama")

            if not gemma_job_running and gemma_new_job_id is None:
                logger.info(f"Il job di Gemma {args.gemma_job_id} è terminato")

                # Cerca il miglior checkpoint
                gemma_best_checkpoint = find_checkpoint_best(args.gemma_dir)

                if gemma_best_checkpoint:
                    # Aggiorna la configurazione
                    gemma_config_path = update_config_with_best_checkpoint(args.gemma_config, gemma_best_checkpoint)

                    # Avvia il nuovo job
                    gemma_new_job_id = launch_training_job(gemma_config_path, "gemma", args.use_multi_gpu)

                    if gemma_new_job_id:
                        logger.info(f"Nuovo job di training per Gemma avviato con ID: {gemma_new_job_id}")
                    else:
                        logger.error("Impossibile avviare il nuovo job di training per Gemma")
                else:
                    logger.warning("Nessun checkpoint-best trovato per Gemma")

            # Se entrambi i nuovi job sono stati avviati, termina il monitoraggio
            if llama_new_job_id is not None and gemma_new_job_id is not None:
                logger.info("Entrambi i nuovi job sono stati avviati. Terminazione del monitoraggio.")
                break

            # Attendi prima del prossimo controllo
            logger.info(f"Prossimo controllo tra {args.check_interval} secondi")
            time.sleep(args.check_interval)

        except KeyboardInterrupt:
            logger.info("Interruzione manuale del monitoraggio")
            break
        except Exception as e:
            logger.error(f"Errore durante il monitoraggio: {e}")
            import traceback
            logger.error(traceback.format_exc())
            time.sleep(args.check_interval)

    logger.info("Monitoraggio terminato")

if __name__ == "__main__":
    main()
