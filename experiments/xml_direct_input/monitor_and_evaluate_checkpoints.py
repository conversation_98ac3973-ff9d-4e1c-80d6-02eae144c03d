#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per monitorare i checkpoint e avviare automaticamente la valutazione.
"""

import os
import sys
import time
import glob
import json
import argparse
import logging
import subprocess
from typing import List, Dict, Any, Optional
from datetime import datetime

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("logs/checkpoint_monitor.log")
    ],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Monitoraggio e valutazione automatica dei checkpoint")
    parser.add_argument("--model_name", type=str, required=True, help="Nome del modello base")
    parser.add_argument("--checkpoint_dir", type=str, required=True, help="Directory contenente i checkpoint da monitorare")
    parser.add_argument("--test_file", type=str, required=True, help="File JSON con i dati di test")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i risultati")
    parser.add_argument("--use_custom_tokenizer", action="store_true", help="Usa tokenizer personalizzato per SVG")
    parser.add_argument("--load_in_8bit", action="store_true", help="Carica il modello in 8-bit")
    parser.add_argument("--load_in_4bit", action="store_true", help="Carica il modello in 4-bit")
    parser.add_argument("--num_samples", type=int, default=100, help="Numero di campioni da valutare")
    parser.add_argument("--use_clip", action="store_true", help="Calcola anche il CLIP score")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner_checkpoints", help="Nome del progetto Weights & Biands")
    parser.add_argument("--check_interval", type=int, default=600, help="Intervallo di controllo in secondi (default: 10 minuti)")
    parser.add_argument("--min_step_interval", type=int, default=100, help="Intervallo minimo di step tra le valutazioni")
    parser.add_argument("--max_evaluations", type=int, default=10, help="Numero massimo di valutazioni da eseguire")
    return parser.parse_args()

def get_checkpoints(checkpoint_dir: str) -> List[Dict[str, Any]]:
    """Ottiene la lista dei checkpoint con le relative informazioni."""
    checkpoint_pattern = os.path.join(checkpoint_dir, "checkpoint-*")
    checkpoint_paths = glob.glob(checkpoint_pattern)
    
    checkpoints = []
    for path in checkpoint_paths:
        checkpoint_name = os.path.basename(path)
        step = int(checkpoint_name.split("-")[1]) if "-" in checkpoint_name else 0
        
        # Verifica se il checkpoint è completo
        adapter_model_path = os.path.join(path, "adapter_model.bin")
        if not os.path.exists(adapter_model_path):
            continue
        
        # Carica il file trainer_state.json se esiste
        trainer_state_path = os.path.join(path, "trainer_state.json")
        trainer_info = {}
        
        if os.path.exists(trainer_state_path):
            with open(trainer_state_path, "r") as f:
                trainer_state = json.load(f)
                
                # Estrai informazioni rilevanti
                trainer_info = {
                    "step": trainer_state.get("global_step", step),
                    "epoch": trainer_state.get("epoch", 0),
                    "best_metric": trainer_state.get("best_metric", None),
                    "training_loss": None,
                    "eval_loss": None
                }
                
                # Estrai l'ultima loss di training e validation
                log_history = trainer_state.get("log_history", [])
                for log in reversed(log_history):
                    if "loss" in log and trainer_info["training_loss"] is None:
                        trainer_info["training_loss"] = log["loss"]
                    if "eval_loss" in log and trainer_info["eval_loss"] is None:
                        trainer_info["eval_loss"] = log["eval_loss"]
                    if trainer_info["training_loss"] is not None and trainer_info["eval_loss"] is not None:
                        break
        
        # Se non abbiamo trovato le informazioni nel trainer_state.json, usa solo lo step
        if not trainer_info:
            trainer_info = {"step": step}
        
        checkpoints.append({
            "path": path,
            "name": checkpoint_name,
            "step": step,
            "info": trainer_info,
            "evaluated": False
        })
    
    # Ordina i checkpoint per numero di step
    checkpoints.sort(key=lambda x: x["step"])
    
    return checkpoints

def submit_evaluation_job(
    model_name: str,
    checkpoint_dir: str,
    test_file: str,
    output_dir: str,
    use_custom_tokenizer: bool = False,
    load_in_8bit: bool = False,
    load_in_4bit: bool = False,
    num_samples: int = 100,
    use_clip: bool = False,
    wandb_entity: str = "337543-unimore",
    wandb_project: str = "captioner_checkpoints",
    checkpoint_min: int = 0,
    checkpoint_max: int = 1000000
) -> Optional[str]:
    """Invia un job SLURM per la valutazione dei checkpoint."""
    # Prepara i parametri per lo script SLURM
    use_custom_tokenizer_flag = "--use_custom_tokenizer" if use_custom_tokenizer else ""
    load_in_8bit_flag = "--load_in_8bit" if load_in_8bit else ""
    load_in_4bit_flag = "--load_in_4bit" if load_in_4bit else ""
    use_clip_flag = "--use_clip" if use_clip else ""
    
    # Costruisci il comando sbatch
    cmd = [
        "sbatch",
        "/work/tesi_ediluzio/experiments/xml_direct_input/run_checkpoint_evaluation.slurm",
        model_name,
        checkpoint_dir,
        test_file,
        output_dir,
        use_custom_tokenizer_flag,
        load_in_8bit_flag,
        load_in_4bit_flag,
        str(num_samples),
        use_clip_flag,
        wandb_entity,
        wandb_project,
        "1",  # checkpoint_step (valuta ogni checkpoint)
        str(checkpoint_min),
        str(checkpoint_max)
    ]
    
    try:
        # Esegui il comando sbatch
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # Estrai l'ID del job
        job_id = result.stdout.strip().split()[-1]
        logger.info(f"Job di valutazione inviato con ID: {job_id}")
        
        return job_id
    except subprocess.CalledProcessError as e:
        logger.error(f"Errore nell'invio del job: {e}")
        logger.error(f"Output: {e.stdout}")
        logger.error(f"Error: {e.stderr}")
        return None

def check_job_status(job_id: str) -> bool:
    """Verifica se un job SLURM è ancora in esecuzione."""
    try:
        result = subprocess.run(["squeue", "-j", job_id], capture_output=True, text=True)
        
        # Se il job non è più nella coda, è terminato
        return job_id in result.stdout
    except Exception as e:
        logger.error(f"Errore nel controllo dello stato del job: {e}")
        return False

def main():
    args = parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Crea la directory di log
    os.makedirs("logs", exist_ok=True)
    
    logger.info(f"Avvio del monitoraggio dei checkpoint in {args.checkpoint_dir}")
    logger.info(f"Intervallo di controllo: {args.check_interval} secondi")
    logger.info(f"Intervallo minimo di step: {args.min_step_interval}")
    logger.info(f"Numero massimo di valutazioni: {args.max_evaluations}")
    
    # Inizializza le variabili di stato
    last_evaluated_step = 0
    evaluation_count = 0
    active_job_id = None
    
    # Loop principale
    while evaluation_count < args.max_evaluations:
        try:
            # Verifica se c'è un job attivo
            if active_job_id and check_job_status(active_job_id):
                logger.info(f"Job di valutazione {active_job_id} ancora in esecuzione")
            else:
                active_job_id = None
                
                # Ottieni la lista dei checkpoint
                checkpoints = get_checkpoints(args.checkpoint_dir)
                
                if not checkpoints:
                    logger.info(f"Nessun checkpoint trovato in {args.checkpoint_dir}")
                else:
                    logger.info(f"Trovati {len(checkpoints)} checkpoint")
                    
                    # Trova il checkpoint più recente che soddisfa i criteri
                    latest_checkpoint = None
                    for checkpoint in reversed(checkpoints):
                        if checkpoint["step"] > last_evaluated_step + args.min_step_interval:
                            latest_checkpoint = checkpoint
                            break
                    
                    if latest_checkpoint:
                        logger.info(f"Trovato nuovo checkpoint da valutare: {latest_checkpoint['name']} (step {latest_checkpoint['step']})")
                        
                        # Invia un job per valutare solo questo checkpoint
                        job_id = submit_evaluation_job(
                            model_name=args.model_name,
                            checkpoint_dir=latest_checkpoint["path"],  # Usa il path del checkpoint specifico
                            test_file=args.test_file,
                            output_dir=os.path.join(args.output_dir, latest_checkpoint["name"]),
                            use_custom_tokenizer=args.use_custom_tokenizer,
                            load_in_8bit=args.load_in_8bit,
                            load_in_4bit=args.load_in_4bit,
                            num_samples=args.num_samples,
                            use_clip=args.use_clip,
                            wandb_entity=args.wandb_entity,
                            wandb_project=args.wandb_project,
                            checkpoint_min=latest_checkpoint["step"],
                            checkpoint_max=latest_checkpoint["step"]
                        )
                        
                        if job_id:
                            active_job_id = job_id
                            last_evaluated_step = latest_checkpoint["step"]
                            evaluation_count += 1
                            
                            logger.info(f"Valutazione {evaluation_count}/{args.max_evaluations} avviata per il checkpoint {latest_checkpoint['name']}")
                        else:
                            logger.error(f"Impossibile avviare la valutazione per il checkpoint {latest_checkpoint['name']}")
                    else:
                        logger.info(f"Nessun nuovo checkpoint da valutare (ultimo step valutato: {last_evaluated_step})")
            
            # Attendi prima del prossimo controllo
            logger.info(f"Prossimo controllo tra {args.check_interval} secondi")
            time.sleep(args.check_interval)
            
        except KeyboardInterrupt:
            logger.info("Interruzione manuale del monitoraggio")
            break
        except Exception as e:
            logger.error(f"Errore durante il monitoraggio: {e}")
            import traceback
            logger.error(traceback.format_exc())
            time.sleep(args.check_interval)
    
    logger.info(f"Monitoraggio terminato dopo {evaluation_count} valutazioni")

if __name__ == "__main__":
    main()
