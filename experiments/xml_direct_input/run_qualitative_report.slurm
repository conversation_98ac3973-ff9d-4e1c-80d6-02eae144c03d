#!/bin/bash
#SBATCH --job-name=qual_report
#SBATCH --output=logs/qual_report_%j_qual_report.out
#SBATCH --error=logs/qual_report_%j_qual_report.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G"
#SBATCH --gpus=1
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=00:30:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
echo "Variabile di ambiente WANDB_API_KEY impostata."

# Parametri
RESULTS_FILES=${1:-"/work/tesi_ediluzio/results/lora_xml/llama31_8b_base_eval_test_manual.jsonl"}
MODEL_NAMES=${2:-"llama31_8b_base"}
OUTPUT_DIR=${3:-"/work/tesi_ediluzio/results/reports"}
NUM_EXAMPLES=${4:-10}
WANDB_ENTITY=${5:-"337543"}
WANDB_PROJECT=${6:-"svg_captioning_report"}
WANDB_RUN_NAME=${7:-"qualitative_report"}
RENDER_SVG=${8:-"--render_svg"}

# Converti i parametri in array se necessario
if [[ ! "$RESULTS_FILES" == *" "* ]]; then
    RESULTS_FILES_ARRAY=($RESULTS_FILES)
else
    RESULTS_FILES_ARRAY=($RESULTS_FILES)
fi

if [[ ! "$MODEL_NAMES" == *" "* ]]; then
    MODEL_NAMES_ARRAY=($MODEL_NAMES)
else
    MODEL_NAMES_ARRAY=($MODEL_NAMES)
fi

# Crea la directory di output se non esiste
mkdir -p "$OUTPUT_DIR"

# Stampa informazioni
echo "Avvio Generazione Report Qualitativo"
echo "File Risultati: ${RESULTS_FILES_ARRAY[@]}"
echo "Nomi Modelli: ${MODEL_NAMES_ARRAY[@]}"
echo "Directory Output: $OUTPUT_DIR"
echo "Numero Esempi: $NUM_EXAMPLES"
echo "Weights & Biands Entity: $WANDB_ENTITY"
echo "Weights & Biands Project: $WANDB_PROJECT"
echo "Weights & Biands Run Name: $WANDB_RUN_NAME"
echo "Render SVG: $RENDER_SVG"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocata (da SLURM): $SLURM_GPUS_ON_NODE"

# Installa le dipendenze necessarie
echo "Installazione dipendenze..."
$PYTHON_ENV -m pip install matplotlib seaborn cairosvg

# Costruisci i parametri per lo script Python
RESULTS_FILES_PARAM=""
for file in "${RESULTS_FILES_ARRAY[@]}"; do
    RESULTS_FILES_PARAM="$RESULTS_FILES_PARAM --results_files $file"
done

MODEL_NAMES_PARAM=""
for name in "${MODEL_NAMES_ARRAY[@]}"; do
    MODEL_NAMES_PARAM="$MODEL_NAMES_PARAM --model_names $name"
done

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/generate_qualitative_report.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/generate_qualitative_report.py \
    $RESULTS_FILES_PARAM \
    $MODEL_NAMES_PARAM \
    --output_dir "$OUTPUT_DIR" \
    --num_examples "$NUM_EXAMPLES" \
    --wandb_entity "$WANDB_ENTITY" \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_run_name "$WANDB_RUN_NAME" \
    $RENDER_SVG

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Generazione report qualitativo completata con successo"
else
    echo "Generazione report qualitativo fallita con codice di errore: $?"
fi
