#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare esempi qualitativi dai modelli addestrati.
Seleziona SVG rappresentativi per ogni fascia di complessità e genera didascalie.
"""

import os
import sys
import json
import glob
import argparse
import logging
import random
import time
import re
from typing import Dict, List, Any, Optional, Tuple
import torch
from tqdm import tqdm
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import PeftModel

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

# Importa le utility personalizzate
from shared.svg_core.custom_tokenizer_utils import build_tokenizer

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Generazione di esempi qualitativi dai modelli addestrati")
    parser.add_argument("--model_name_or_path", type=str, required=True, help="Nome o path del modello base")
    parser.add_argument("--checkpoint_path", type=str, required=True, help="Path al checkpoint del modello")
    parser.add_argument("--test_file", type=str, required=True, help="File JSON con i dati di test")
    parser.add_argument("--output_file", type=str, required=True, help="File di output per gli esempi qualitativi")
    parser.add_argument("--use_custom_tokenizer", action="store_true", help="Usa tokenizer personalizzato per SVG")
    parser.add_argument("--load_in_8bit", action="store_true", help="Carica il modello in 8-bit")
    parser.add_argument("--load_in_4bit", action="store_true", help="Carica il modello in 4-bit")
    parser.add_argument("--num_examples_per_complexity", type=int, default=5, help="Numero di esempi per fascia di complessità")
    parser.add_argument("--max_length", type=int, default=512, help="Lunghezza massima della sequenza")
    parser.add_argument("--seed", type=int, default=42, help="Seed per la riproducibilità")
    return parser.parse_args()

def load_test_data(test_file: str) -> List[Dict[str, Any]]:
    """Carica i dati di test da un file JSON."""
    with open(test_file, "r") as f:
        data = json.load(f)
    
    return data

def categorize_complexity(svg_data: str) -> str:
    """Categorizza la complessità di un SVG in base al numero di elementi."""
    # Conta il numero di elementi SVG
    element_count = len(re.findall(r'<(path|rect|circle|ellipse|line|polyline|polygon)', svg_data))
    
    if element_count <= 5:
        return "simple"
    elif element_count <= 15:
        return "medium"
    else:
        return "complex"

def load_model_and_tokenizer(
    model_name_or_path: str,
    checkpoint_path: str,
    use_custom_tokenizer: bool = False,
    load_in_8bit: bool = False,
    load_in_4bit: bool = False
) -> Tuple[AutoModelForCausalLM, AutoTokenizer]:
    """Carica il modello e il tokenizer."""
    # Configura la quantizzazione
    quantization_config = None
    if load_in_8bit:
        logger.info("Utilizzo della quantizzazione a 8-bit")
        quantization_config = BitsAndBytesConfig(load_in_8bit=True)
    elif load_in_4bit:
        logger.info("Utilizzo della quantizzazione a 4-bit")
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.bfloat16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )

    # Carica il tokenizer
    logger.info(f"Caricamento del tokenizer: {model_name_or_path}")
    tokenizer = AutoTokenizer.from_pretrained(model_name_or_path)

    # Aggiungi token speciali per SVG se richiesto
    if use_custom_tokenizer:
        logger.info("Utilizzo del tokenizer custom per SVG")
        tokenizer = build_tokenizer(tokenizer)

    # Assicurati che il tokenizer abbia un token di padding
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Carica il modello base
    logger.info(f"Caricamento del modello base: {model_name_or_path}")
    model = AutoModelForCausalLM.from_pretrained(
        model_name_or_path,
        quantization_config=quantization_config,
        device_map="auto",
        torch_dtype=torch.bfloat16
    )

    # Carica i pesi del checkpoint LoRA
    logger.info(f"Caricamento del checkpoint LoRA: {checkpoint_path}")
    model = PeftModel.from_pretrained(model, checkpoint_path)

    return model, tokenizer

def generate_caption(model, tokenizer, svg: str, max_length: int = 512) -> str:
    """Genera una didascalia per un SVG."""
    # Crea il prompt nel formato corretto per il modello
    prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg}\n\nDescrizione:"

    # Tokenizza l'input
    inputs = tokenizer(prompt, return_tensors="pt", max_length=max_length, truncation=True)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}

    # Genera la didascalia
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=150,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.pad_token_id
        )

    # Decodifica l'output
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # Estrai solo la didascalia generata (dopo "Descrizione:")
    caption = generated_text.split("Descrizione:")[1].strip()

    return caption

def generate_qualitative_examples(
    model_name_or_path: str,
    checkpoint_path: str,
    test_data: List[Dict[str, Any]],
    output_file: str,
    use_custom_tokenizer: bool = False,
    load_in_8bit: bool = False,
    load_in_4bit: bool = False,
    num_examples_per_complexity: int = 5,
    max_length: int = 512,
    seed: int = 42
) -> None:
    """Genera esempi qualitativi dai modelli addestrati."""
    # Imposta il seed per la riproducibilità
    random.seed(seed)
    
    # Carica il modello e il tokenizer
    model, tokenizer = load_model_and_tokenizer(
        model_name_or_path,
        checkpoint_path,
        use_custom_tokenizer,
        load_in_8bit,
        load_in_4bit
    )
    
    # Categorizza i dati di test per complessità
    data_by_complexity = {
        "simple": [],
        "medium": [],
        "complex": []
    }
    
    for item in test_data:
        svg_data = item["xml"]
        complexity = categorize_complexity(svg_data)
        data_by_complexity[complexity].append(item)
    
    # Seleziona esempi casuali per ogni fascia di complessità
    selected_examples = []
    
    for complexity, items in data_by_complexity.items():
        logger.info(f"Fascia di complessità {complexity}: {len(items)} esempi disponibili")
        
        # Seleziona esempi casuali
        if len(items) > num_examples_per_complexity:
            selected_items = random.sample(items, num_examples_per_complexity)
        else:
            selected_items = items
        
        # Genera didascalie per gli esempi selezionati
        for i, item in enumerate(tqdm(selected_items, desc=f"Generazione didascalie per complessità {complexity}")):
            svg_data = item["xml"]
            true_caption = item["caption"]
            
            # Misura il tempo di inferenza
            start_time = time.time()
            generated_caption = generate_caption(model, tokenizer, svg_data, max_length)
            inference_time = time.time() - start_time
            
            # Aggiungi l'esempio ai risultati
            selected_examples.append({
                "id": f"{complexity}_{i+1}",
                "complexity": complexity,
                "svg": svg_data,
                "true_caption": true_caption,
                "generated_caption": generated_caption,
                "inference_time": inference_time
            })
    
    # Salva gli esempi qualitativi
    with open(output_file, "w") as f:
        json.dump(selected_examples, f, indent=2)
    
    logger.info(f"Esempi qualitativi salvati in: {output_file}")
    logger.info(f"Totale esempi generati: {len(selected_examples)}")

def main():
    args = parse_args()
    
    # Carica i dati di test
    test_data = load_test_data(args.test_file)
    
    # Genera esempi qualitativi
    generate_qualitative_examples(
        model_name_or_path=args.model_name_or_path,
        checkpoint_path=args.checkpoint_path,
        test_data=test_data,
        output_file=args.output_file,
        use_custom_tokenizer=args.use_custom_tokenizer,
        load_in_8bit=args.load_in_8bit,
        load_in_4bit=args.load_in_4bit,
        num_examples_per_complexity=args.num_examples_per_complexity,
        max_length=args.max_length,
        seed=args.seed
    )

if __name__ == "__main__":
    main()
