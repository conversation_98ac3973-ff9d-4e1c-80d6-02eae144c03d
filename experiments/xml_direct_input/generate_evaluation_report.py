#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare un report HTML completo con valutazioni, grafici e esempi qualitativi
dei modelli addestrati per la generazione di didascalie SVG.
"""

import os
import sys
import json
import glob
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import cairosvg
import io
import base64
from PIL import Image
from typing import Dict, List, Any, Optional, Tuple

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

def parse_args():
    parser = argparse.ArgumentParser(description="Generazione di un report HTML completo di valutazione")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per il report HTML")
    parser.add_argument("--models_dir", type=str, required=True, help="Directory contenente i modelli da valutare")
    parser.add_argument("--metrics_dir", type=str, required=True, help="Directory contenente le metriche di valutazione")
    parser.add_argument("--examples_file", type=str, required=True, help="File JSON con gli esempi qualitativi")
    parser.add_argument("--title", type=str, default="Valutazione dei Modelli di Generazione Didascalie SVG", help="Titolo del report")
    parser.add_argument("--logo_path", type=str, default=None, help="Path al logo da includere nel report")
    parser.add_argument("--num_examples", type=int, default=5, help="Numero di esempi qualitativi per fascia")
    parser.add_argument("--include_zero_shot", action="store_true", help="Includi valutazione zero-shot")
    parser.add_argument("--zero_shot_metrics", type=str, default=None, help="File JSON con le metriche zero-shot")
    parser.add_argument("--zero_shot_examples", type=str, default=None, help="File JSON con gli esempi zero-shot")
    return parser.parse_args()

def load_metrics(metrics_dir: str) -> Dict[str, Dict[str, Any]]:
    """Carica le metriche di valutazione da una directory."""
    metrics = {}
    
    # Cerca tutti i file metrics.json nelle sottodirectory
    metrics_files = glob.glob(os.path.join(metrics_dir, "**/metrics.json"), recursive=True)
    
    for metrics_file in metrics_files:
        # Estrai il nome del checkpoint dalla path
        checkpoint_dir = os.path.dirname(metrics_file)
        checkpoint_name = os.path.basename(checkpoint_dir)
        
        # Carica le metriche
        with open(metrics_file, "r") as f:
            checkpoint_metrics = json.load(f)
        
        metrics[checkpoint_name] = checkpoint_metrics
    
    return metrics

def load_examples(examples_file: str) -> List[Dict[str, Any]]:
    """Carica gli esempi qualitativi da un file JSON."""
    with open(examples_file, "r") as f:
        examples = json.load(f)
    
    return examples

def categorize_complexity(svg_data: str) -> str:
    """Categorizza la complessità di un SVG in base al numero di elementi."""
    import re
    
    # Conta il numero di elementi SVG
    element_count = len(re.findall(r'<(path|rect|circle|ellipse|line|polyline|polygon)', svg_data))
    
    if element_count <= 5:
        return "simple"
    elif element_count <= 15:
        return "medium"
    else:
        return "complex"

def svg_to_data_uri(svg_string: str) -> str:
    """Converte una stringa SVG in un data URI."""
    try:
        # Renderizza SVG in PNG
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'), output_width=300, output_height=300)
        
        # Converti i dati PNG in base64
        encoded = base64.b64encode(png_data).decode('utf-8')
        
        # Crea il data URI
        data_uri = f"data:image/png;base64,{encoded}"
        
        return data_uri
    except Exception as e:
        logger.error(f"Errore nella conversione SVG in data URI: {e}")
        return ""

def create_loss_plot(metrics: Dict[str, Dict[str, Any]], model_name: str) -> str:
    """Crea un grafico della loss di training e validation."""
    # Estrai i dati per il grafico
    steps = []
    train_losses = []
    eval_losses = []
    
    for checkpoint_name, checkpoint_metrics in metrics.items():
        if "step" in checkpoint_metrics and "training_loss" in checkpoint_metrics and "eval_loss" in checkpoint_metrics:
            steps.append(checkpoint_metrics["step"])
            train_losses.append(checkpoint_metrics["training_loss"])
            eval_losses.append(checkpoint_metrics["eval_loss"])
    
    # Ordina i dati per step
    sorted_indices = np.argsort(steps)
    steps = [steps[i] for i in sorted_indices]
    train_losses = [train_losses[i] for i in sorted_indices]
    eval_losses = [eval_losses[i] for i in sorted_indices]
    
    # Crea il grafico con Plotly
    fig = make_subplots(specs=[[{"secondary_y": False}]])
    
    fig.add_trace(
        go.Scatter(x=steps, y=train_losses, name="Training Loss", line=dict(color="blue")),
        secondary_y=False,
    )
    
    fig.add_trace(
        go.Scatter(x=steps, y=eval_losses, name="Validation Loss", line=dict(color="red")),
        secondary_y=False,
    )
    
    # Aggiungi una linea di tendenza LOESS per la validation loss
    from statsmodels.nonparametric.smoothers_lowess import lowess
    
    if len(steps) > 5:  # Serve un numero minimo di punti per LOESS
        loess_smoothed = lowess(eval_losses, steps, frac=0.5)
        fig.add_trace(
            go.Scatter(
                x=loess_smoothed[:, 0],
                y=loess_smoothed[:, 1],
                name="Validation Loss (LOESS)",
                line=dict(color="darkred", dash="dash"),
            ),
            secondary_y=False,
        )
    
    fig.update_layout(
        title=f"Training e Validation Loss - {model_name}",
        xaxis_title="Step",
        yaxis_title="Loss",
        legend=dict(x=0.01, y=0.99, bgcolor="rgba(255,255,255,0.8)"),
        template="plotly_white",
    )
    
    # Converti il grafico in HTML
    return fig.to_html(full_html=False, include_plotlyjs='cdn')

def create_metrics_plot(metrics: Dict[str, Dict[str, Any]], model_name: str) -> str:
    """Crea un grafico radar delle metriche di valutazione."""
    # Trova il checkpoint con la validation loss più bassa
    best_checkpoint = None
    best_eval_loss = float('inf')
    
    for checkpoint_name, checkpoint_metrics in metrics.items():
        if "eval_loss" in checkpoint_metrics and checkpoint_metrics["eval_loss"] < best_eval_loss:
            best_eval_loss = checkpoint_metrics["eval_loss"]
            best_checkpoint = checkpoint_name
    
    if best_checkpoint is None:
        logger.warning(f"Nessun checkpoint trovato con eval_loss per {model_name}")
        return ""
    
    # Estrai le metriche per il miglior checkpoint
    best_metrics = metrics[best_checkpoint]
    
    # Seleziona le metriche da visualizzare
    metrics_to_plot = {
        "BLEU-1": best_metrics.get("bleu1", 0),
        "BLEU-2": best_metrics.get("bleu2", 0),
        "BLEU-3": best_metrics.get("bleu3", 0),
        "BLEU-4": best_metrics.get("bleu4", 0),
        "METEOR": best_metrics.get("meteor", 0),
        "CIDEr": best_metrics.get("cider", 0) / 10,  # Normalizza CIDEr per il grafico radar
        "CLIP Score": best_metrics.get("clip_score", 0) / 100,  # Normalizza CLIP Score
    }
    
    # Crea il grafico radar con Plotly
    fig = go.Figure()
    
    categories = list(metrics_to_plot.keys())
    values = list(metrics_to_plot.values())
    
    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name=f"{model_name} (Best: {best_checkpoint})"
    ))
    
    fig.update_layout(
        title=f"Metriche di Valutazione - {model_name} (Best: {best_checkpoint})",
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )
        ),
        showlegend=True,
        template="plotly_white",
    )
    
    # Converti il grafico in HTML
    return fig.to_html(full_html=False, include_plotlyjs='cdn')

def create_metrics_comparison_table(metrics_dict: Dict[str, Dict[str, Dict[str, Any]]], zero_shot_metrics: Optional[Dict[str, Any]] = None) -> str:
    """Crea una tabella di confronto delle metriche tra i modelli."""
    # Trova il miglior checkpoint per ogni modello
    best_checkpoints = {}
    
    for model_name, model_metrics in metrics_dict.items():
        best_checkpoint = None
        best_eval_loss = float('inf')
        
        for checkpoint_name, checkpoint_metrics in model_metrics.items():
            if "eval_loss" in checkpoint_metrics and checkpoint_metrics["eval_loss"] < best_eval_loss:
                best_eval_loss = checkpoint_metrics["eval_loss"]
                best_checkpoint = checkpoint_name
        
        if best_checkpoint is not None:
            best_checkpoints[model_name] = best_checkpoint
    
    # Crea una tabella con le metriche dei migliori checkpoint
    table_rows = []
    
    # Aggiungi le metriche zero-shot se disponibili
    if zero_shot_metrics is not None:
        for model_name, metrics in zero_shot_metrics.items():
            row = {
                "Model": f"{model_name} (Zero-Shot)",
                "BLEU-1": f"{metrics.get('bleu1', 0):.4f}",
                "BLEU-2": f"{metrics.get('bleu2', 0):.4f}",
                "BLEU-3": f"{metrics.get('bleu3', 0):.4f}",
                "BLEU-4": f"{metrics.get('bleu4', 0):.4f}",
                "METEOR": f"{metrics.get('meteor', 0):.4f}",
                "CIDEr": f"{metrics.get('cider', 0):.4f}",
                "CLIP Score": f"{metrics.get('clip_score', 0):.4f}",
                "Inference Time (s)": f"{metrics.get('inference_time_mean', 0):.4f}",
                "Perplexity": f"{metrics.get('perplexity_mean', 0):.4f}",
            }
            table_rows.append(row)
    
    # Aggiungi le metriche dei modelli fine-tuned
    for model_name, model_metrics in metrics_dict.items():
        if model_name in best_checkpoints:
            best_checkpoint = best_checkpoints[model_name]
            metrics = model_metrics[best_checkpoint]
            
            row = {
                "Model": f"{model_name} (Best: {best_checkpoint})",
                "BLEU-1": f"{metrics.get('bleu1', 0):.4f}",
                "BLEU-2": f"{metrics.get('bleu2', 0):.4f}",
                "BLEU-3": f"{metrics.get('bleu3', 0):.4f}",
                "BLEU-4": f"{metrics.get('bleu4', 0):.4f}",
                "METEOR": f"{metrics.get('meteor', 0):.4f}",
                "CIDEr": f"{metrics.get('cider', 0):.4f}",
                "CLIP Score": f"{metrics.get('clip_score', 0):.4f}",
                "Inference Time (s)": f"{metrics.get('inference_time_mean', 0):.4f}",
                "Perplexity": f"{metrics.get('perplexity_mean', 0):.4f}",
            }
            table_rows.append(row)
    
    # Crea la tabella HTML
    table_html = "<table class='table table-striped table-bordered'>\n"
    table_html += "<thead>\n<tr>\n"
    
    # Intestazioni della tabella
    headers = list(table_rows[0].keys())
    for header in headers:
        table_html += f"<th>{header}</th>\n"
    
    table_html += "</tr>\n</thead>\n<tbody>\n"
    
    # Righe della tabella
    for row in table_rows:
        table_html += "<tr>\n"
        for header in headers:
            table_html += f"<td>{row[header]}</td>\n"
        table_html += "</tr>\n"
    
    table_html += "</tbody>\n</table>\n"
    
    return table_html

def create_examples_section(examples: List[Dict[str, Any]], num_examples: int = 5) -> str:
    """Crea una sezione con esempi qualitativi per ogni fascia di complessità."""
    # Categorizza gli esempi per complessità
    examples_by_complexity = {
        "simple": [],
        "medium": [],
        "complex": []
    }
    
    for example in examples:
        complexity = categorize_complexity(example["svg"])
        examples_by_complexity[complexity].append(example)
    
    # Limita il numero di esempi per categoria
    for complexity in examples_by_complexity:
        if len(examples_by_complexity[complexity]) > num_examples:
            examples_by_complexity[complexity] = examples_by_complexity[complexity][:num_examples]
    
    # Crea la sezione HTML
    html = ""
    
    for complexity, complexity_examples in examples_by_complexity.items():
        html += f"<h3>Esempi di Complessità {complexity.capitalize()}</h3>\n"
        html += "<div class='row'>\n"
        
        for example in complexity_examples:
            # Converti SVG in data URI
            svg_data_uri = svg_to_data_uri(example["svg"])
            
            html += "<div class='col-md-4 mb-4'>\n"
            html += "<div class='card'>\n"
            html += f"<img src='{svg_data_uri}' class='card-img-top' alt='SVG Example'>\n"
            html += "<div class='card-body'>\n"
            html += f"<h5 class='card-title'>Esempio {example.get('id', '')}</h5>\n"
            html += "<div class='card-text'>\n"
            html += "<p><strong>Ground Truth:</strong></p>\n"
            html += f"<p>{example.get('true_caption', '')}</p>\n"
            html += "<p><strong>Generated Caption:</strong></p>\n"
            html += f"<p>{example.get('generated_caption', '')}</p>\n"
            html += "</div>\n"
            html += "<div class='card-footer'>\n"
            html += f"<small class='text-muted'>Tempo di Inferenza: {example.get('inference_time', 0):.4f}s</small>\n"
            html += "</div>\n"
            html += "</div>\n"
            html += "</div>\n"
            html += "</div>\n"
        
        html += "</div>\n"
    
    return html

def create_model_info_section(model_name: str, metrics: Dict[str, Dict[str, Any]]) -> str:
    """Crea una sezione con informazioni sul modello e i parametri di training."""
    # Trova il checkpoint con la validation loss più bassa
    best_checkpoint = None
    best_eval_loss = float('inf')
    
    for checkpoint_name, checkpoint_metrics in metrics.items():
        if "eval_loss" in checkpoint_metrics and checkpoint_metrics["eval_loss"] < best_eval_loss:
            best_eval_loss = checkpoint_metrics["eval_loss"]
            best_checkpoint = checkpoint_name
    
    if best_checkpoint is None:
        logger.warning(f"Nessun checkpoint trovato con eval_loss per {model_name}")
        return ""
    
    # Estrai le metriche per il miglior checkpoint
    best_metrics = metrics[best_checkpoint]
    
    # Crea la sezione HTML
    html = f"<h3>Informazioni sul Modello: {model_name}</h3>\n"
    html += "<div class='card mb-4'>\n"
    html += "<div class='card-body'>\n"
    html += "<h5 class='card-title'>Parametri di Training</h5>\n"
    html += "<ul class='list-group list-group-flush'>\n"
    html += f"<li class='list-group-item'><strong>Modello Base:</strong> {model_name}</li>\n"
    html += f"<li class='list-group-item'><strong>Miglior Checkpoint:</strong> {best_checkpoint}</li>\n"
    html += f"<li class='list-group-item'><strong>Step:</strong> {best_metrics.get('step', 'N/A')}</li>\n"
    html += f"<li class='list-group-item'><strong>Epoca:</strong> {best_metrics.get('epoch', 'N/A')}</li>\n"
    html += f"<li class='list-group-item'><strong>Training Loss:</strong> {best_metrics.get('training_loss', 'N/A')}</li>\n"
    html += f"<li class='list-group-item'><strong>Validation Loss:</strong> {best_metrics.get('eval_loss', 'N/A')}</li>\n"
    html += f"<li class='list-group-item'><strong>Batch Size:</strong> 2 (per device) * 4 (gradient accumulation) = 8</li>\n"
    html += f"<li class='list-group-item'><strong>Learning Rate:</strong> 1e-4</li>\n"
    html += f"<li class='list-group-item'><strong>LoRA Rank:</strong> 64</li>\n"
    html += f"<li class='list-group-item'><strong>LoRA Alpha:</strong> 16</li>\n"
    html += f"<li class='list-group-item'><strong>Quantizzazione:</strong> 4-bit</li>\n"
    html += "</ul>\n"
    html += "</div>\n"
    html += "</div>\n"
    
    return html

def generate_html_report(
    output_dir: str,
    models_dir: str,
    metrics_dir: str,
    examples_file: str,
    title: str = "Valutazione dei Modelli di Generazione Didascalie SVG",
    logo_path: Optional[str] = None,
    num_examples: int = 5,
    include_zero_shot: bool = False,
    zero_shot_metrics: Optional[str] = None,
    zero_shot_examples: Optional[str] = None
) -> str:
    """Genera un report HTML completo con valutazioni, grafici e esempi qualitativi."""
    # Crea la directory di output
    os.makedirs(output_dir, exist_ok=True)
    
    # Carica le metriche per ogni modello
    metrics_dict = {}
    model_dirs = [d for d in os.listdir(metrics_dir) if os.path.isdir(os.path.join(metrics_dir, d))]
    
    for model_dir in model_dirs:
        model_metrics_dir = os.path.join(metrics_dir, model_dir)
        metrics_dict[model_dir] = load_metrics(model_metrics_dir)
    
    # Carica gli esempi qualitativi
    examples = load_examples(examples_file)
    
    # Carica le metriche e gli esempi zero-shot se richiesto
    zero_shot_metrics_data = None
    zero_shot_examples_data = None
    
    if include_zero_shot and zero_shot_metrics is not None:
        with open(zero_shot_metrics, "r") as f:
            zero_shot_metrics_data = json.load(f)
    
    if include_zero_shot and zero_shot_examples is not None:
        with open(zero_shot_examples, "r") as f:
            zero_shot_examples_data = json.load(f)
    
    # Crea il contenuto HTML
    html = f"""
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{title}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{
                padding-top: 2rem;
                padding-bottom: 2rem;
            }}
            .header {{
                margin-bottom: 2rem;
                text-align: center;
            }}
            .logo {{
                max-height: 100px;
                margin-bottom: 1rem;
            }}
            .section {{
                margin-bottom: 3rem;
            }}
            .card {{
                height: 100%;
            }}
            .card-img-top {{
                height: 200px;
                object-fit: contain;
                background-color: #f8f9fa;
                padding: 1rem;
            }}
            .metrics-table {{
                margin-top: 2rem;
                margin-bottom: 2rem;
            }}
            .footer {{
                margin-top: 3rem;
                padding-top: 1rem;
                border-top: 1px solid #dee2e6;
                text-align: center;
                font-size: 0.9rem;
                color: #6c757d;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
    """
    
    # Aggiungi il logo se specificato
    if logo_path is not None and os.path.exists(logo_path):
        # Converti il logo in base64
        with open(logo_path, "rb") as f:
            logo_data = f.read()
        logo_base64 = base64.b64encode(logo_data).decode('utf-8')
        logo_ext = os.path.splitext(logo_path)[1][1:]  # Estensione senza il punto
        html += f'<img src="data:image/{logo_ext};base64,{logo_base64}" alt="Logo" class="logo">\n'
    
    html += f"""
                <h1>{title}</h1>
                <p class="lead">Report generato il {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Sommario</h2>
                <p>
                    Questo report presenta una valutazione completa dei modelli addestrati per la generazione di didascalie
                    per immagini SVG. Include grafici delle loss, metriche di valutazione, e esempi qualitativi per ogni
                    fascia di complessità.
                </p>
            </div>
            
            <div class="section">
                <h2>Confronto delle Metriche</h2>
                <div class="metrics-table">
                    {create_metrics_comparison_table(metrics_dict, zero_shot_metrics_data)}
                </div>
            </div>
    """
    
    # Aggiungi sezioni per ogni modello
    for model_name, model_metrics in metrics_dict.items():
        html += f"""
            <div class="section">
                <h2>Modello: {model_name}</h2>
                
                {create_model_info_section(model_name, model_metrics)}
                
                <div class="row">
                    <div class="col-md-6">
                        {create_loss_plot(model_metrics, model_name)}
                    </div>
                    <div class="col-md-6">
                        {create_metrics_plot(model_metrics, model_name)}
                    </div>
                </div>
                
                <h3>Esempi Qualitativi</h3>
                {create_examples_section(examples, num_examples)}
            </div>
        """
    
    # Aggiungi sezione zero-shot se richiesto
    if include_zero_shot and zero_shot_examples_data is not None:
        html += f"""
            <div class="section">
                <h2>Valutazione Zero-Shot</h2>
                <p>
                    Questa sezione presenta i risultati della valutazione zero-shot dei modelli, senza fine-tuning.
                </p>
                
                <h3>Esempi Qualitativi Zero-Shot</h3>
                {create_examples_section(zero_shot_examples_data, num_examples)}
            </div>
        """
    
    # Aggiungi il footer
    html += f"""
            <div class="footer">
                <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
                <p>© {datetime.now().year} Università di Modena e Reggio Emilia</p>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    
    # Salva il report HTML
    output_file = os.path.join(output_dir, "evaluation_report.html")
    with open(output_file, "w") as f:
        f.write(html)
    
    logger.info(f"Report HTML generato con successo: {output_file}")
    
    return output_file

def main():
    args = parse_args()
    
    # Genera il report HTML
    report_file = generate_html_report(
        output_dir=args.output_dir,
        models_dir=args.models_dir,
        metrics_dir=args.metrics_dir,
        examples_file=args.examples_file,
        title=args.title,
        logo_path=args.logo_path,
        num_examples=args.num_examples,
        include_zero_shot=args.include_zero_shot,
        zero_shot_metrics=args.zero_shot_metrics,
        zero_shot_examples=args.zero_shot_examples
    )
    
    logger.info(f"Report HTML generato con successo: {report_file}")

if __name__ == "__main__":
    main()
