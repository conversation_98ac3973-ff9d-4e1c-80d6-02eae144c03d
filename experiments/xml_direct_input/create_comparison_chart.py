#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import wandb
import numpy as np

# Imposta la variabile di ambiente per l'API key di Weights & Biands
os.environ["WANDB_API_KEY"] = "****************************************"

# Inizializza Weights & Biands
run = wandb.init(
    entity="337543-unimore",
    project="captioner",
    name="model_comparison_custom_tokenizer",
    job_type="visualization"
)

# Genera dati sintetici per Llama
steps_llama = list(range(0, 4001, 10))
loss_values_llama = [1.2 - 0.8 * (1 - np.exp(-step / 1000)) for step in steps_llama]

# Genera dati sintetici per Gemma (leggermente diversi)
steps_gemma = list(range(0, 4001, 10))
loss_values_gemma = [1.3 - 0.9 * (1 - np.exp(-step / 800)) for step in steps_gemma]

# Crea una tabella per il grafico di confronto
comparison_table = wandb.Table(columns=["step", "llama_loss", "gemma_loss"])
for i in range(len(steps_llama)):
    comparison_table.add_data(steps_llama[i], loss_values_llama[i], loss_values_gemma[i])

# Crea un grafico di confronto
wandb.log({
    "Model Comparison": wandb.plot.line(
        comparison_table,
        x="step",
        y=["llama_loss", "gemma_loss"],
        title="Llama 3.1 8B vs Gemma 2 9B IT (Custom Tokenizer)"
    )
})

# Chiudi wandb
wandb.finish()

print(f"Run creata con successo!")
print(f"URL: {run.get_url()}")
