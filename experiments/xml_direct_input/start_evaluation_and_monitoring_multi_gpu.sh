#!/bin/bash

# Script per avviare la valutazione dei modelli con tokenizer personalizzato
# e il monitoraggio della convergenza dei modelli in training
# Versione per training multi-GPU

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Configura Weights & Biands
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Crea le directory necessarie
mkdir -p /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_lora_xml_custom_token
mkdir -p /work/tesi_ediluzio/evaluation/checkpoint_metrics/gemma2_9b_it_lora_xml_custom_token
mkdir -p /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_custom_token_from_convergence_multi_gpu
mkdir -p /work/tesi_ediluzio/evaluation/checkpoint_metrics/gemma2_9b_it_custom_token_from_convergence_multi_gpu

# 1. Avvia la valutazione dei modelli con tokenizer personalizzato
echo "Avvio della valutazione dei modelli con tokenizer personalizzato..."
bash /work/tesi_ediluzio/experiments/xml_direct_input/evaluate_custom_tokenizer_models.sh

# 2. Avvia il monitoraggio della convergenza
echo "Avvio del monitoraggio della convergenza..."
nohup $PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/monitor_convergence_and_launch_custom_token.py \
    --llama_dir /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence \
    --gemma_dir /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence \
    --llama_job_id 2565298 \
    --gemma_job_id 2565300 \
    --check_interval 1800 \
    --llama_config /work/tesi_ediluzio/experiments/xml_direct_input/configs/llama31_8b_custom_token_from_convergence_multi_gpu.json \
    --gemma_config /work/tesi_ediluzio/experiments/xml_direct_input/configs/gemma2_9b_it_custom_token_from_convergence_multi_gpu.json \
    --use_multi_gpu True \
    > logs/convergence_monitor_multi_gpu_$(date +%Y%m%d_%H%M%S).log 2>&1 &

# Ottieni il PID del processo
MONITOR_PID=$!
echo "Monitoraggio avviato con PID: $MONITOR_PID"
echo "Log disponibile in: logs/convergence_monitor_multi_gpu_$(date +%Y%m%d_%H%M%S).log"

echo "Processo completato. I job di valutazione sono stati avviati e il monitoraggio della convergenza è in esecuzione."
echo "Puoi controllare lo stato dei job con 'squeue -u \$USER'"
