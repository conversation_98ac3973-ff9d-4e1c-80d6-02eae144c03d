#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per valutare la qualità delle didascalie generate.
Calcola metriche come BLEU, ROUGE, METEOR, CIDEr, ecc.
"""

import argparse
import json
import os
import numpy as np
from tqdm import tqdm
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
from rouge_score import rouge_scorer
import nltk

# Assicurati che NLTK abbia i dati necessari
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')
try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

def parse_args():
    parser = argparse.ArgumentParser(description="Valuta la qualità delle didascalie generate")
    parser.add_argument("--results_file", type=str, required=True, help="File JSONL con le didascalie generate")
    parser.add_argument("--output_file", type=str, required=True, help="File di output con le metriche")
    parser.add_argument("--num_samples", type=int, default=-1, help="Numero di campioni da valutare (-1 per tutti)")
    return parser.parse_args()

def load_results(results_file, num_samples=-1):
    """Carica i risultati dal file JSONL."""
    results = []
    with open(results_file, 'r', encoding='utf-8') as f:
        for line in f:
            results.append(json.loads(line))
    
    if num_samples > 0:
        results = results[:num_samples]
    
    return results

def tokenize(text):
    """Tokenizza il testo in parole."""
    return nltk.word_tokenize(text.lower())

def calculate_bleu(reference, hypothesis):
    """Calcola il punteggio BLEU."""
    smoothing = SmoothingFunction().method1
    reference_tokens = [tokenize(reference)]
    hypothesis_tokens = tokenize(hypothesis)
    
    # Calcola BLEU-1, BLEU-2, BLEU-3, BLEU-4
    bleu_1 = sentence_bleu(reference_tokens, hypothesis_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
    bleu_2 = sentence_bleu(reference_tokens, hypothesis_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
    bleu_3 = sentence_bleu(reference_tokens, hypothesis_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
    bleu_4 = sentence_bleu(reference_tokens, hypothesis_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
    
    return {
        "bleu_1": bleu_1,
        "bleu_2": bleu_2,
        "bleu_3": bleu_3,
        "bleu_4": bleu_4
    }

def calculate_meteor(reference, hypothesis):
    """Calcola il punteggio METEOR."""
    return meteor_score([tokenize(reference)], tokenize(hypothesis))

def calculate_rouge(reference, hypothesis):
    """Calcola i punteggi ROUGE."""
    scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
    scores = scorer.score(reference, hypothesis)
    
    return {
        "rouge_1_precision": scores['rouge1'].precision,
        "rouge_1_recall": scores['rouge1'].recall,
        "rouge_1_f1": scores['rouge1'].fmeasure,
        "rouge_2_precision": scores['rouge2'].precision,
        "rouge_2_recall": scores['rouge2'].recall,
        "rouge_2_f1": scores['rouge2'].fmeasure,
        "rouge_l_precision": scores['rougeL'].precision,
        "rouge_l_recall": scores['rougeL'].recall,
        "rouge_l_f1": scores['rougeL'].fmeasure
    }

def evaluate_captions(results):
    """Valuta la qualità delle didascalie generate."""
    metrics = []
    
    for result in tqdm(results, desc="Valutazione didascalie"):
        reference = result["true_caption"]
        hypothesis = result["generated_caption"]
        
        # Calcola le metriche
        bleu_scores = calculate_bleu(reference, hypothesis)
        meteor_score = calculate_meteor(reference, hypothesis)
        rouge_scores = calculate_rouge(reference, hypothesis)
        
        # Combina le metriche
        sample_metrics = {
            "id": result["id"],
            "meteor": meteor_score,
            **bleu_scores,
            **rouge_scores
        }
        
        metrics.append(sample_metrics)
    
    return metrics

def calculate_average_metrics(metrics):
    """Calcola le metriche medie."""
    avg_metrics = {}
    
    # Inizializza le metriche
    for key in metrics[0].keys():
        if key != "id":
            avg_metrics[key] = []
    
    # Raccoglie tutte le metriche
    for sample_metrics in metrics:
        for key, value in sample_metrics.items():
            if key != "id":
                avg_metrics[key].append(value)
    
    # Calcola le medie
    for key in avg_metrics.keys():
        avg_metrics[key] = np.mean(avg_metrics[key])
    
    return avg_metrics

def main():
    args = parse_args()
    
    # Carica i risultati
    print(f"Caricamento dei risultati da {args.results_file}...")
    results = load_results(args.results_file, args.num_samples)
    print(f"Caricati {len(results)} risultati.")
    
    # Valuta le didascalie
    print("Valutazione delle didascalie...")
    metrics = evaluate_captions(results)
    
    # Calcola le metriche medie
    print("Calcolo delle metriche medie...")
    avg_metrics = calculate_average_metrics(metrics)
    
    # Salva le metriche
    output = {
        "average_metrics": avg_metrics,
        "sample_metrics": metrics
    }
    
    with open(args.output_file, 'w', encoding='utf-8') as f:
        json.dump(output, f, indent=2)
    
    print(f"Metriche salvate in {args.output_file}")
    
    # Stampa le metriche medie
    print("\nMetriche medie:")
    for key, value in avg_metrics.items():
        print(f"{key}: {value:.4f}")

if __name__ == "__main__":
    main()
