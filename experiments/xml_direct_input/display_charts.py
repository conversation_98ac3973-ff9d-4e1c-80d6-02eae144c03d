#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import argparse
import matplotlib.pyplot as plt
from PIL import Image

def parse_args():
    parser = argparse.ArgumentParser(description="Visualizza i grafici generati")
    parser.add_argument("--charts_dir", type=str, default="/work/tesi_ediluzio/analysis/wandb", help="Directory contenente i grafici")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Verifica che la directory esista
    if not os.path.exists(args.charts_dir):
        print(f"La directory {args.charts_dir} non esiste")
        return
    
    # Trova tutti i file PNG nella directory
    png_files = [f for f in os.listdir(args.charts_dir) if f.endswith(".png")]
    
    if not png_files:
        print(f"Nessun file PNG trovato in {args.charts_dir}")
        return
    
    print(f"Trovati {len(png_files)} file PNG:")
    for i, png_file in enumerate(png_files):
        print(f"{i+1}. {png_file}")
    
    # Visualizza il primo grafico
    img_path = os.path.join(args.charts_dir, png_files[0])
    img = Image.open(img_path)
    img.show()
    
    print(f"Visualizzazione del grafico {png_files[0]}")
    print(f"Per visualizzare gli altri grafici, aprire i file manualmente in {args.charts_dir}")

if __name__ == "__main__":
    main()
