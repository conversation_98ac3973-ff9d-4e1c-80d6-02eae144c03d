#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per l'ottimizzazione delle configurazioni LoRA.
Genera configurazioni LoRA ottimizzate basate sui risultati dell'inferenza zero-shot.
"""

import os
import json
import argparse
import logging
from typing import Dict, List, Any, Optional
import copy
from tqdm import tqdm

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Carica un file JSONL."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def load_json(file_path: str) -> Dict[str, Any]:
    """Carica un file JSON."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_json(data: Dict[str, Any], file_path: str) -> None:
    """Salva i dati in formato JSON."""
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2)

def extract_model_name(results_file: str) -> str:
    """
    Estrae il nome del modello dal percorso del file di risultati.
    
    Args:
        results_file: Percorso al file di risultati
        
    Returns:
        Nome del modello
    """
    # Estrai il nome del file senza estensione
    file_name = os.path.basename(results_file)
    model_name = file_name.replace("_results.jsonl", "").replace(".jsonl", "")
    
    # Mappa dei nomi dei modelli
    model_map = {
        "deepseek_r1_8b": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
        "mistral_7b_v03": "mistralai/Mistral-7B-Instruct-v0.3",
        "llama31_8b": "meta-llama/Llama-3.1-8B-Instruct",
        "llama32_3b": "meta-llama/Llama-3.2-3B-Instruct",
        "gemma2_9b_it": "google/gemma-2-9b-it"
    }
    
    # Restituisci il nome completo del modello se disponibile, altrimenti il nome del file
    return model_map.get(model_name, model_name)

def get_target_modules(model_name: str) -> List[str]:
    """
    Ottiene i target modules per un modello.
    
    Args:
        model_name: Nome del modello
        
    Returns:
        Lista di target modules
    """
    # Mappa dei target modules per modello
    target_modules_map = {
        "deepseek-ai/DeepSeek-R1-Distill-Llama-8B": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        "mistralai/Mistral-7B-Instruct-v0.3": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        "meta-llama/Llama-3.1-8B-Instruct": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        "meta-llama/Llama-3.2-3B-Instruct": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
        "google/gemma-2-9b-it": ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    }
    
    # Restituisci i target modules per il modello specificato
    return target_modules_map.get(model_name, ["q_proj", "k_proj", "v_proj", "o_proj"])

def create_lora_config(
    model_name: str,
    train_file: str,
    output_dir: str,
    target_modules: Optional[List[str]] = None,
    r: int = 16,
    lora_alpha: int = 32,
    lora_dropout: float = 0.05,
    learning_rate: float = 1e-4,
    num_train_epochs: int = 3,
    per_device_train_batch_size: int = 1,
    gradient_accumulation_steps: int = 8,
    use_8bit: bool = True
) -> Dict[str, Any]:
    """
    Crea una configurazione LoRA ottimizzata.
    
    Args:
        model_name: Nome del modello
        train_file: Percorso al file di training
        output_dir: Directory di output per il modello
        target_modules: Lista di target modules (opzionale)
        r: Rango LoRA
        lora_alpha: Alpha LoRA
        lora_dropout: Dropout LoRA
        learning_rate: Learning rate
        num_train_epochs: Numero di epoche
        per_device_train_batch_size: Batch size per dispositivo
        gradient_accumulation_steps: Passi di accumulazione del gradiente
        use_8bit: Se True, usa la quantizzazione a 8 bit
        
    Returns:
        Configurazione LoRA
    """
    # Usa i target modules predefiniti se non specificati
    if target_modules is None:
        target_modules = get_target_modules(model_name)
    
    # Crea la configurazione LoRA
    config = {
        "model_name_or_path": model_name,
        "train_file": train_file,
        "validation_file": None,
        "per_device_train_batch_size": per_device_train_batch_size,
        "per_device_eval_batch_size": per_device_train_batch_size,
        "gradient_accumulation_steps": gradient_accumulation_steps,
        "num_train_epochs": num_train_epochs,
        "learning_rate": learning_rate,
        "weight_decay": 0.01,
        "warmup_ratio": 0.03,
        "lr_scheduler_type": "cosine",
        "evaluation_strategy": "no",
        "save_strategy": "epoch",
        "save_total_limit": 3,
        "load_best_model_at_end": False,
        "push_to_hub": False,
        "hub_model_id": None,
        "hub_token": None,
        "trust_remote_code": True,
        "use_cache": False,
        "max_seq_length": 2048,
        "preprocessing_num_workers": 4,
        "output_dir": output_dir,
        "overwrite_output_dir": True,
        "do_train": True,
        "do_eval": False,
        "logging_steps": 10,
        "logging_dir": os.path.join(output_dir, "logs"),
        "remove_unused_columns": False,
        "bf16": False,
        "fp16": True,
        "tf32": False,
        "torch_compile": False,
        "optim": "paged_adamw_8bit" if use_8bit else "adamw_torch",
        "seed": 42,
        "report_to": "tensorboard",
        "gradient_checkpointing": True,
        "lora_r": r,
        "lora_alpha": lora_alpha,
        "lora_dropout": lora_dropout,
        "target_modules": target_modules
    }
    
    return config

def optimize_lora_configs(
    results_dir: str,
    metrics_dir: str,
    output_dir: str,
    train_file: str,
    top_k: int = 3,
    metric_name: str = "bleu-4"
) -> None:
    """
    Ottimizza le configurazioni LoRA basandosi sui risultati dell'inferenza zero-shot.
    
    Args:
        results_dir: Directory con i risultati dell'inferenza zero-shot
        metrics_dir: Directory con le metriche di valutazione
        output_dir: Directory di output per le configurazioni LoRA
        train_file: Percorso al file di training
        top_k: Numero di modelli da selezionare
        metric_name: Nome della metrica da utilizzare per la selezione
    """
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Trova tutti i file di metriche
    metrics_files = []
    for file_name in os.listdir(metrics_dir):
        if file_name.endswith("_metrics.json"):
            metrics_files.append(os.path.join(metrics_dir, file_name))
    
    logger.info(f"Trovati {len(metrics_files)} file di metriche.")
    
    # Carica le metriche e ordina i modelli
    model_metrics = []
    for metrics_file in metrics_files:
        try:
            metrics = load_json(metrics_file)
            model_name = os.path.basename(metrics_file).replace("_metrics.json", "")
            
            # Estrai la metrica specificata
            metric_value = metrics.get("aggregated", {}).get(metric_name, 0.0)
            
            model_metrics.append({
                "model_name": model_name,
                "metric_value": metric_value,
                "metrics_file": metrics_file
            })
        except Exception as e:
            logger.warning(f"Errore nel caricamento del file {metrics_file}: {e}")
    
    # Ordina i modelli per valore della metrica (decrescente)
    model_metrics.sort(key=lambda x: x["metric_value"], reverse=True)
    
    # Seleziona i top-k modelli
    top_models = model_metrics[:top_k]
    logger.info(f"Selezionati i top {top_k} modelli:")
    for i, model in enumerate(top_models):
        logger.info(f"{i+1}. {model['model_name']}: {model['metric_value']:.4f}")
    
    # Crea configurazioni LoRA ottimizzate per i top-k modelli
    for model in top_models:
        # Trova il file di risultati corrispondente
        results_file = os.path.join(results_dir, f"{model['model_name']}_results.jsonl")
        if not os.path.exists(results_file):
            logger.warning(f"File di risultati non trovato: {results_file}")
            continue
        
        # Estrai il nome completo del modello
        full_model_name = extract_model_name(results_file)
        
        # Crea la directory di output per il modello
        model_output_dir = os.path.join("/work/tesi_ediluzio/models", f"{model['model_name']}_lora")
        
        # Crea configurazioni con diversi parametri
        configs = []
        
        # Configurazione base
        configs.append(create_lora_config(
            model_name=full_model_name,
            train_file=train_file,
            output_dir=model_output_dir,
            r=16,
            lora_alpha=32,
            lora_dropout=0.05,
            learning_rate=1e-4,
            num_train_epochs=3,
            per_device_train_batch_size=1,
            gradient_accumulation_steps=8,
            use_8bit=True
        ))
        
        # Configurazione con tokenizer custom
        model_output_dir_custom = os.path.join("/work/tesi_ediluzio/models", f"{model['model_name']}_lora_custom_token")
        custom_config = copy.deepcopy(configs[0])
        custom_config["output_dir"] = model_output_dir_custom
        custom_config["logging_dir"] = os.path.join(model_output_dir_custom, "logs")
        custom_config["use_custom_tokenizer"] = True
        configs.append(custom_config)
        
        # Salva le configurazioni
        for i, config in enumerate(configs):
            config_name = f"{model['model_name']}_lora_config{'_custom' if i > 0 else ''}.json"
            config_path = os.path.join(output_dir, config_name)
            save_json(config, config_path)
            logger.info(f"Configurazione salvata in: {config_path}")

def main():
    parser = argparse.ArgumentParser(description="Ottimizza le configurazioni LoRA.")
    parser.add_argument("--results_dir", type=str, default="/work/tesi_ediluzio/results/zero_shot", help="Directory con i risultati dell'inferenza zero-shot.")
    parser.add_argument("--metrics_dir", type=str, default="/work/tesi_ediluzio/evaluation/metrics", help="Directory con le metriche di valutazione.")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/configs", help="Directory di output per le configurazioni LoRA.")
    parser.add_argument("--train_file", type=str, default="/work/tesi_ediluzio/data/processed/train_set_final.json", help="Percorso al file di training.")
    parser.add_argument("--top_k", type=int, default=3, help="Numero di modelli da selezionare.")
    parser.add_argument("--metric_name", type=str, default="bleu-4", help="Nome della metrica da utilizzare per la selezione.")
    
    args = parser.parse_args()
    optimize_lora_configs(args.results_dir, args.metrics_dir, args.output_dir, args.train_file, args.top_k, args.metric_name)

if __name__ == "__main__":
    main()
