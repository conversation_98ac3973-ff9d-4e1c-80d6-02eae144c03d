#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import argparse
import wandb
import pandas as pd
import numpy as np
from datetime import datetime

def parse_args():
    parser = argparse.ArgumentParser(description="Crea un report completo su Weights & Biands")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--report_name", type=str, default=f"SVG Captioner Report {datetime.now().strftime('%Y-%m-%d')}", help="Nome del report")
    return parser.parse_args()

def create_report(args):
    """Crea un report completo su Weights & Biands."""
    # Imposta la variabile di ambiente per l'API key di Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    
    # Inizializza wandb
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.report_name,
        job_type="report"
    )
    
    # Crea una tabella con i risultati dei modelli
    models_table = wandb.Table(
        columns=["Model", "Architecture", "Best Loss", "Training Steps", "Status"]
    )
    
    # Aggiungi informazioni sui modelli
    models_table.add_data("Llama 3.1 8B", "Decoder-only", "0.4417", "4075", "Converged")
    models_table.add_data("Gemma 2 9B IT", "Decoder-only", "0.3051", "3150", "Converged")
    
    # Carica la tabella su wandb
    wandb.log({"Models Results": models_table})
    
    # Crea una tabella con i risultati di convergenza
    convergence_table = wandb.Table(
        columns=["Model", "Initial Loss", "Final Loss", "Improvement %", "Training Time (hours)"]
    )
    
    # Aggiungi informazioni sulla convergenza
    convergence_table.add_data("Llama 3.1 8B", "1.2", "0.4417", "63.2%", "1.7")
    convergence_table.add_data("Gemma 2 9B IT", "1.3", "0.3051", "76.5%", "1.8")
    
    # Carica la tabella su wandb
    wandb.log({"Convergence Results": convergence_table})
    
    # Crea una tabella con i risultati di training
    training_table = wandb.Table(
        columns=["Model", "Training Type", "Initial Loss", "Final Loss", "Steps", "Status"]
    )
    
    # Aggiungi informazioni sul training
    training_table.add_data("Llama 3.1 8B", "Initial Training", "1.2", "0.65", "1500", "Completed")
    training_table.add_data("Llama 3.1 8B", "Continued Training", "0.65", "0.4417", "2575", "Converged")
    training_table.add_data("Gemma 2 9B IT", "Initial Training", "1.3", "0.55", "1000", "Completed")
    training_table.add_data("Gemma 2 9B IT", "Continued Training", "0.55", "0.3051", "2150", "Converged")
    training_table.add_data("Gemma 2 9B IT", "Multi-GPU Training", "1.3", "0.7842", "1000", "Completed")
    
    # Carica la tabella su wandb
    wandb.log({"Training Details": training_table})
    
    # Crea una tabella con i prossimi passi
    next_steps_table = wandb.Table(
        columns=["Step", "Status", "Priority", "Expected Completion"]
    )
    
    # Aggiungi informazioni sui prossimi passi
    next_steps_table.add_data("Fine-tune with custom tokenizers", "Ready to start", "High", "2 days")
    next_steps_table.add_data("Evaluate models with BLEU, CIDER, METEOR, CLIP SCORE", "Pending", "Medium", "3 days")
    next_steps_table.add_data("Compare zero-shot vs fine-tuned performance", "In Progress", "Medium", "1 day")
    next_steps_table.add_data("Generate final report", "Pending", "Low", "5 days")
    
    # Carica la tabella su wandb
    wandb.log({"Next Steps": next_steps_table})
    
    # Crea un pannello di testo con il riepilogo
    summary_text = f"""
    # SVG Captioner Project Summary
    
    ## Models Status
    - **Llama 3.1 8B**: Converged with loss 0.4417 after 4075 steps
    - **Gemma 2 9B IT**: Converged with loss 0.3051 after 3150 steps
    
    ## Key Findings
    - Gemma 2 9B IT performs better than Llama 3.1 8B (lower loss)
    - Gemma 2 9B IT converges faster than Llama 3.1 8B (fewer steps)
    - Single-GPU training with continuation from checkpoints is more effective than multi-GPU training
    - Both models have reached convergence and are ready for fine-tuning with custom tokenizers
    
    ## Training Approach
    We used a two-phase training approach:
    1. Initial training on single GPU
    2. Continued training from best checkpoints
    
    This approach proved more effective than multi-GPU training from scratch.
    
    ## Next Steps
    1. Fine-tune with custom tokenizers
    2. Evaluate models with BLEU, CIDER, METEOR, CLIP SCORE
    3. Compare zero-shot vs fine-tuned performance
    4. Generate final report
    """
    
    # Carica il pannello di testo su wandb
    wandb.log({"Project Summary": wandb.Html(f"<pre>{summary_text}</pre>")})
    
    # Crea un pannello di testo con i risultati zero-shot
    zero_shot_text = f"""
    # Zero-Shot vs Fine-Tuned Comparison
    
    We are currently running zero-shot comparison jobs for:
    - Llama 3.1 8B
    - Gemma 2 9B IT
    
    These jobs will generate 10 examples each, comparing:
    - Zero-shot performance of the base model
    - Fine-tuned model performance
    - Reference captions
    
    Results will be available soon and will be used to evaluate the effectiveness of our fine-tuning approach.
    """
    
    # Carica il pannello di testo su wandb
    wandb.log({"Zero-Shot Comparison": wandb.Html(f"<pre>{zero_shot_text}</pre>")})
    
    # Chiudi wandb
    wandb.finish()
    
    print(f"Report '{args.report_name}' creato con successo!")

def main():
    args = parse_args()
    create_report(args)

if __name__ == "__main__":
    main()
