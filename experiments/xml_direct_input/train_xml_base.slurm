#!/bin/bash
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
# MODIFICA 1: Aggiungi gpu_L40S_48G al constraint
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G"
#SBATCH --gpus=1
#SBATCH --time=00:05:00 # Lascia pure 5 min per il test
#SBATCH --nodes=1
# MODIFICA 2: Riduci le CPU richieste
#SBATCH --cpus-per-task=2 # <-- Prova con 4 (o anche 2 se 4 non basta)
#SBATCH --mem=24G
#SBATCH --job-name=xml_train_exp_v2 # Nome aggiornato (opzionale)
#SBATCH -o /work/tesi_ediluzio/experiments/xml_direct_input/outputs/slurm_train_xml_%j.out
#SBATCH -e /work/tesi_ediluzio/experiments/xml_direct_input/outputs/slurm_train_xml_%j.err
#SBATCH --mail-type=ALL
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE (lascia il resto invariato)
################################################################################

# --- Setup Ambiente ---
# Assicurati che la directory di output esista
mkdir -p /work/tesi_ediluzio/experiments/xml_direct_input/outputs/

export CUDA_HOME=/usr/local/cuda-11.8 # Usa la versione corretta
export LD_LIBRARY_PATH=$CUDA_HOME/lib64:/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH # Aggiorna anche LD_LIBRARY_PATH se imposti CUDA_HOME
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
# export LD_LIBRARY_PATH=/usr/local/cuda-11.8/lib64:/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH
# Attiva il tuo ambiente Conda/virtualenv (verifica percorso!)
source /work/tesi_ediluzio/svg_captioning_env/bin/activate # <--- Assicurati sia corretto

# --- Selezione Configurazione ---
CONFIG_FILE=${1} # Legge il primo argomento passato a sbatch

if [ -z "$CONFIG_FILE" ]; then
  echo "Errore: Specificare il percorso del file di configurazione come primo argomento."
  # MODIFICA 2: Aggiorna esempio nel messaggio di errore
  echo "Esempio: sbatch train_xml_base.slurm experiments/xml_direct_input/configs/NOME_CONFIG.json"
  exit 1
fi

echo "Avvio training con configurazione: ${CONFIG_FILE}"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Assegnate: $CUDA_VISIBLE_DEVICES"

# --- Comando di Esecuzione ---
echo "Directory di lavoro corrente: $(pwd)" # Utile per debug
echo "Contenuto directory esperimento:"
ls -l experiments/xml_direct_input/ # Utile per debug

# MODIFICA 3: Aggiorna percorso script Python
python experiments/xml_direct_input/train_xml_slurm.py --config_path "${CONFIG_FILE}"

echo "Training completato o interrotto."

# Disattiva ambiente (opzionale)
deactivate

exit 0