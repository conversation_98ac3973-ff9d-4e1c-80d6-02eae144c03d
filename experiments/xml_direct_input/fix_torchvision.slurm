#!/bin/bash
#SBATCH --job-name=fix_torchvision
#SBATCH --output=logs/fix_torchvision_%j.out
#SBATCH --error=logs/fix_torchvision_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=1:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Stampa informazioni
echo "Fixing torchvision CUDA version mismatch"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"

# Verifica le versioni attuali
echo "Versioni attuali:"
$PYTHON_ENV -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA version: {torch.version.cuda}')"
$PYTHON_ENV -c "import torchvision; print(f'TorchVision version: {torchvision.__version__}')" || echo "TorchVision non compatibile con PyTorch"

# Disinstalla torchvision
echo "Disinstallazione di torchvision..."
pip uninstall -y torchvision

# Installa torchvision con la versione CUDA corretta
echo "Installazione di torchvision con CUDA 12.4..."
pip install torchvision --no-cache-dir

# Verifica le nuove versioni
echo "Nuove versioni:"
$PYTHON_ENV -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA version: {torch.version.cuda}')"
$PYTHON_ENV -c "import torchvision; print(f'TorchVision version: {torchvision.__version__}')"

echo "Fix completato!"
