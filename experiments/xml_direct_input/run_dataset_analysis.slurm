#!/bin/bash
#SBATCH --job-name=dataset_analysis
#SBATCH --output=logs/dataset_analysis_%j.out
#SBATCH --error=logs/dataset_analysis_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=1:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Parametri
DATA_FILE="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
OUTPUT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/dataset_analysis"
NUM_BINS=5

# Stampa informazioni
echo "Analisi del dataset SVG"
echo "Dataset: $DATA_FILE"
echo "Output: $OUTPUT_DIR"
echo "Numero di fasce: $NUM_BINS"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"

# Crea la directory di output
mkdir -p "$OUTPUT_DIR"

# Esegui lo script di analisi
echo "Eseguo lo script di analisi: /work/tesi_ediluzio/experiments/xml_direct_input/analyze_dataset.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/analyze_dataset.py \
    --data_file "$DATA_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --num_bins "$NUM_BINS"

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Analisi del dataset completata con successo"
else
    echo "Analisi del dataset fallita con codice di errore: $?"
    exit 1
fi

# Esegui lo script per creare gli split
echo "Eseguo lo script per creare gli split: /work/tesi_ediluzio/experiments/xml_direct_input/create_dataset_splits.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/create_dataset_splits.py \
    --bins_dir "$OUTPUT_DIR" \
    --output_dir "$OUTPUT_DIR/splits" \
    --train_ratio 0.7 \
    --val_ratio 0.15 \
    --test_ratio 0.15 \
    --seed 42

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Creazione degli split completata con successo"
else
    echo "Creazione degli split fallita con codice di errore: $?"
    exit 1
fi

echo "Analisi del dataset e creazione degli split completate!"
