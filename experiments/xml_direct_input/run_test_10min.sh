#!/bin/bash

# Imposta le variabili d'ambiente
export HF_HOME="/work/tesi_ediluzio/.cache/huggingface"
export HF_TOKEN="*************************************"
export PYTHONPATH="/work/tesi_ediluzio:${PYTHONPATH}"

# Attiva l'ambiente Python
source /work/tesi_ediluzio/svg_captioning_env/bin/activate

# Esegui lo script di valutazione per Llama 3.1 8B (solo 5 campioni)
echo "Esecuzione valutazione per Llama 3.1 8B..."
python /work/tesi_ediluzio/experiments/xml_direct_input/run_inference_unified.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --lora_path /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_quick_fixed3 \
    --test_file /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    --output_file /work/tesi_ediluzio/results/lora_xml/llama31_8b_lora_xml_eval_test_direct.jsonl \
    --load_in_8bit \
    --use_custom_tokenizer \
    --num_samples 5

echo "Script completato."
