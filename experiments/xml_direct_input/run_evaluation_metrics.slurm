#!/bin/bash
#SBATCH --job-name=eval_metrics
#SBATCH --output=logs/eval_metrics_%j_eval_metrics.out
#SBATCH --error=logs/eval_metrics_%j_eval_metrics.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --constraint="gpu_A40_48G|gpu_L40S_48G"
#SBATCH --gpus=1
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=01:00:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
echo "Variabile di ambiente WANDB_API_KEY impostata."

# Parametri
RESULTS_FILE=${1:-"/work/tesi_ediluzio/results/lora_xml/llama31_8b_base_eval_test_manual.jsonl"}
OUTPUT_FILE=${2:-"/work/tesi_ediluzio/results/metrics/llama31_8b_base_metrics.json"}
WANDB_ENTITY=${3:-"337543"}
WANDB_PROJECT=${4:-"svg_captioning_eval"}
WANDB_RUN_NAME=${5:-"llama31_8b_base_metrics"}
USE_CLIP=${6:-"--use_clip"}
CALCULATE_LOSS=${7:-"--calculate_loss"}
MODEL_PATH=${8:-"/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token/checkpoint-600"}
TOKENIZER_PATH=${9:-"meta-llama/Llama-3.1-8B-Instruct"}
USE_CUSTOM_TOKENIZER=${10:-"--use_custom_tokenizer"}

# Crea la directory di output se non esiste
mkdir -p "$(dirname "$OUTPUT_FILE")"

# Stampa informazioni
echo "Avvio Valutazione Metriche"
echo "File Risultati: $RESULTS_FILE"
echo "File Output: $OUTPUT_FILE"
echo "Weights & Biands Entity: $WANDB_ENTITY"
echo "Weights & Biands Project: $WANDB_PROJECT"
echo "Weights & Biands Run Name: $WANDB_RUN_NAME"
echo "Use CLIP: $USE_CLIP"
echo "Calculate Loss: $CALCULATE_LOSS"
echo "Model Path: $MODEL_PATH"
echo "Tokenizer Path: $TOKENIZER_PATH"
echo "Use Custom Tokenizer: $USE_CUSTOM_TOKENIZER"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocata (da SLURM): $SLURM_GPUS_ON_NODE"

# Installa le dipendenze necessarie
echo "Installazione dipendenze..."
$PYTHON_ENV -m pip install nltk rouge-score clip cairosvg

# Esegui lo script Python
echo "Eseguo lo script Python: /work/tesi_ediluzio/experiments/xml_direct_input/evaluate_captions_metrics.py"
$PYTHON_ENV /work/tesi_ediluzio/experiments/xml_direct_input/evaluate_captions_metrics.py \
    --results_file "$RESULTS_FILE" \
    --output_file "$OUTPUT_FILE" \
    --wandb_entity "$WANDB_ENTITY" \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_run_name "$WANDB_RUN_NAME" \
    $USE_CLIP \
    $CALCULATE_LOSS \
    --model_path "$MODEL_PATH" \
    --tokenizer_path "$TOKENIZER_PATH" \
    $USE_CUSTOM_TOKENIZER

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Valutazione metriche completata con successo"
else
    echo "Valutazione metriche fallita con codice di errore: $?"
fi
