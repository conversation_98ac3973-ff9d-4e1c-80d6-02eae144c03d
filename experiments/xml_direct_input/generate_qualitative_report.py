#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import logging
from typing import Dict, List, Any, Optional
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import wandb
from PIL import Image
import cairosvg
import io
import tempfile
import random

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Generazione di un report qualitativo per il confronto delle didascalie")
    parser.add_argument("--results_files", type=str, nargs="+", required=True, help="Lista di file JSON con i risultati dell'inferenza")
    parser.add_argument("--model_names", type=str, nargs="+", required=True, help="Nomi dei modelli corrispondenti ai file di risultati")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per il report")
    parser.add_argument("--num_examples", type=int, default=10, help="Numero di esempi da includere nel report")
    parser.add_argument("--wandb_entity", type=str, default="337543", help="Entity di Weights & Biands (username o team)")
    parser.add_argument("--wandb_project", type=str, default="svg_captioning_report", help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default=None, help="Nome della run in Weights & Biands")
    parser.add_argument("--render_svg", action="store_true", help="Renderizza gli SVG come immagini nel report")
    parser.add_argument("--render_size", type=int, default=224, help="Dimensione per il rendering SVG")
    parser.add_argument("--random_seed", type=int, default=42, help="Seed per la selezione casuale degli esempi")
    return parser.parse_args()

def load_results(results_file: str) -> List[Dict[str, Any]]:
    """Carica i risultati dell'inferenza da un file JSONL."""
    results = []
    with open(results_file, "r") as f:
        for line in f:
            if line.strip():
                results.append(json.loads(line))
    return results

def render_svg_to_image(svg_string: str, size: int = 224) -> Image.Image:
    """Renderizza una stringa SVG in un'immagine PIL."""
    try:
        # Renderizza SVG in PNG
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'), output_width=size, output_height=size)

        # Converti i dati PNG in un'immagine PIL
        image = Image.open(io.BytesIO(png_data))
        return image
    except Exception as e:
        logger.error(f"Errore nel rendering SVG: {e}")
        # Restituisci un'immagine nera in caso di errore
        return Image.new('RGB', (size, size), color='black')

def generate_html_report(examples: List[Dict[str, Any]], model_names: List[str], output_file: str, render_svg: bool = False, render_size: int = 224):
    """Genera un report HTML con gli esempi selezionati."""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report Qualitativo Didascalie SVG</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            h1 {
                color: #2c3e50;
                text-align: center;
                margin-bottom: 30px;
            }
            h2 {
                color: #3498db;
                margin-top: 40px;
            }
            .example {
                margin-bottom: 50px;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
            }
            .svg-container {
                margin: 20px 0;
                text-align: center;
            }
            .caption {
                margin: 10px 0;
                padding: 10px;
                background-color: #fff;
                border: 1px solid #eee;
                border-radius: 3px;
            }
            .model-name {
                font-weight: bold;
                color: #2980b9;
            }
            .true-caption {
                font-weight: bold;
                color: #27ae60;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            th, td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #f2f2f2;
            }
            tr:hover {
                background-color: #f5f5f5;
            }
        </style>
    </head>
    <body>
        <h1>Report Qualitativo Didascalie SVG</h1>
    """

    # Aggiungi ogni esempio al report
    for i, example in enumerate(examples):
        html += f"""
        <div class="example">
            <h2>Esempio {i+1} (ID: {example.get("id", "N/A")})</h2>
        """

        # Aggiungi l'SVG o l'immagine renderizzata
        if render_svg:
            try:
                # Crea una directory temporanea per salvare l'immagine
                images_dir = os.path.join(os.path.dirname(output_file), "images")
                os.makedirs(images_dir, exist_ok=True)
                # Usare l'ID dell'esempio per un nome file univoco, se disponibile
                example_id_for_filename = str(example.get("id", i+1)).replace("/", "_").replace("\\", "_")
                image_filename = f"example_{example_id_for_filename}.png"
                image_path = os.path.join(images_dir, image_filename)
                
                relative_image_path = os.path.join("images", image_filename)


                # Renderizza l'SVG e salva l'immagine
                svg_content = example.get("svg", example.get("svg_string"))
                if not svg_content:
                    raise ValueError("Contenuto SVG non trovato nell'esempio.")

                image = render_svg_to_image(svg_content, render_size)
                image.save(image_path)

                # Aggiungi l'immagine al report
                html += f"""
                <div class="svg-container">
                    <img src="{relative_image_path}" alt="SVG Example {example_id_for_filename}" style="max-width: 100%; height: auto; border: 1px solid #ccc;">
                </div>
                """
            except Exception as e:
                logger.error(f"Errore nel rendering dell'SVG per l'esempio {example.get('id', i+1)}: {e}")
                html += f"""
                <div class="svg-container">
                    <p>Errore nel rendering dell'SVG</p>
                    <pre>{example.get("svg", "SVG non disponibile")[:500]}...</pre>
                </div>
                """
        else:
            html += f"""
            <div class="svg-container">
                <p>Visualizzazione SVG (testo, primi 500 caratteri):</p>
                <pre>{example.get("svg", "SVG non disponibile")[:500]}...</pre>
            </div>
            """

        # Aggiungi la didascalia vera
        html += f"""
        <div class="caption">
            <span class="true-caption">Didascalia Vera:</span> {example.get("true_caption", "N/A")}
        </div>
        """

        # Aggiungi le didascalie generate dai diversi modelli e i CLIP score
        html += """
        <table>
            <tr>
                <th>Modello</th>
                <th>Didascalia Generata</th>
                <th>CLIP Score</th>
            </tr>
        """

        for model_name in model_names:
            generated_caption = example.get(f"generated_{model_name}", "N/A")
            # Prova a ottenere il CLIP score specifico per il modello.
            # Se non c'è una chiave tipo "clip_score_NOME_MODELLO",
            # cerca una chiave generica "clip_score" se presente (es. se il JSON è già per un solo modello)
            clip_score_key_specific = f"clip_score_{model_name}"
            clip_score_key_generic = "clip_score" # Per compatibilità se il JSON contiene già lo score di quel modello
            
            clip_score_val = example.get(clip_score_key_specific)
            if clip_score_val is None:
                clip_score_val = example.get(clip_score_key_generic, "N/A")
            
            if isinstance(clip_score_val, float):
                clip_score_display = f"{clip_score_val:.4f}"
            else:
                clip_score_display = str(clip_score_val)

            html += f"""
            <tr>
                <td class="model-name">{model_name}</td>
                <td>{generated_caption}</td>
                <td>{clip_score_display}</td>
            </tr>
            """

        html += """
        </table>
        </div>
        """

    html += """
    </body>
    </html>
    """

    # Salva il report HTML
    with open(output_file, "w", encoding='utf-8') as f:
        f.write(html)

    logger.info(f"Report HTML salvato in {output_file}")

def main():
    args = parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Verifica che tutti i file di risultati esistano
    for results_file in args.results_files:
        if not os.path.exists(results_file):
            logger.error(f"File di risultati non trovato: {results_file}")
            sys.exit(1)
    
    # Carica i risultati per ogni modello
    all_results = []
    for results_file in args.results_files:
        logger.info(f"Caricamento dei risultati da {results_file}")
        try:
            results = load_results(results_file)
            all_results.append(results)
        except Exception as e:
            logger.error(f"Errore nel caricamento dei risultati da {results_file}: {e}")
            sys.exit(1)
    
    # Verifica che tutti i risultati abbiano la stessa lunghezza
    if not all(len(r) == len(all_results[0]) for r in all_results):
        logger.error("I file di risultati hanno lunghezze diverse")
        sys.exit(1)
    
    # Seleziona esempi casuali
    random.seed(args.random_seed)
    num_examples = min(args.num_examples, len(all_results[0]))
    selected_indices = random.sample(range(len(all_results[0])), num_examples)
    
    # Prepara gli esempi per il report
    examples = []
    for idx in selected_indices:
        example = {
            "id": all_results[0][idx].get("id", f"example_{idx}"),
            "svg": all_results[0][idx].get("svg", ""),
            "true_caption": all_results[0][idx].get("true_caption", ""),
            "predictions": {}
        }
        
        # Aggiungi le predizioni di ogni modello
        for model_name, results in zip(args.model_names, all_results):
            example["predictions"][model_name] = {
                "caption": results[idx].get("predicted_caption", ""),
                "clip_score": results[idx].get("clip_score", 0.0)
            }
        
        examples.append(example)
    
    # Genera il report HTML
    output_file = os.path.join(args.output_dir, "qualitative_report.html")
    logger.info(f"Generazione del report HTML in {output_file}")
    generate_html_report(examples, args.model_names, output_file, args.render_svg, args.render_size)
    
    # Inizializza wandb se richiesto
    if args.wandb_run_name:
        try:
            wandb.init(
                entity=args.wandb_entity,
                project=args.wandb_project,
                name=args.wandb_run_name
            )
            
            # Logga il report come artefatto
            wandb.log({
                "qualitative_report": wandb.Html(open(output_file).read())
            })
            
            wandb.finish()
        except Exception as e:
            logger.error(f"Errore durante l'inizializzazione di wandb: {e}")
    
    logger.info("Report generato con successo!")

if __name__ == "__main__":
    main()
