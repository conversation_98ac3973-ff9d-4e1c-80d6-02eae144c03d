#!/bin/bash

echo "🚀 LANCIO AUTOMATICO JOB CON AMBIENTE RIPARATO 🚀"
echo "Data: $(date)"

# Vai nella directory del progetto
cd /work/tesi_ediluzio

# STEP 1: CANCELLA JOB VECCHI FALLITI
echo "🗑️ Cancellazione job vecchi falliti..."
scancel -u ediluzio --state=PENDING

# STEP 2: RIPARA AMBIENTE
echo "🔧 Lancio riparazione ambiente..."
FIX_JOB_ID=$(sbatch --parsable scripts/slurm/fix_environment_complete.slurm)
echo "Job riparazione ambiente: $FIX_JOB_ID"

# STEP 3: LANCIA TRAINING (dipende dalla riparazione)
echo "🎯 Lancio training models..."
LLAMA_JOB_ID=$(sbatch --parsable --dependency=afterok:$FIX_JOB_ID scripts/slurm/run_llama_l4_fixed.slurm)
echo "Job training Llama: $LLAMA_JOB_ID"

GEMMA_JOB_ID=$(sbatch --parsable --dependency=afterok:$FIX_JOB_ID scripts/slurm/run_gemma_l4_fixed.slurm)
echo "Job training Gemma: $GEMMA_JOB_ID"

# STEP 4: LANCIA VALUTAZIONI (dipendono dalla riparazione)
echo "📊 Lancio valutazioni..."
BASELINE_JOB_ID=$(sbatch --parsable --dependency=afterok:$FIX_JOB_ID scripts/slurm/run_baseline_evaluation_fixed.slurm)
echo "Job baseline evaluation: $BASELINE_JOB_ID"

CLIP_JOB_ID=$(sbatch --parsable --dependency=afterok:$FIX_JOB_ID scripts/slurm/run_clip_evaluation_fixed.slurm)
echo "Job CLIP evaluation: $CLIP_JOB_ID"

echo ""
echo "✅ TUTTI I JOB LANCIATI CON SUCCESSO!"
echo ""
echo "📋 RIEPILOGO JOB:"
echo "   Riparazione ambiente: $FIX_JOB_ID"
echo "   Training Llama:       $LLAMA_JOB_ID"
echo "   Training Gemma:       $GEMMA_JOB_ID"
echo "   Baseline evaluation:  $BASELINE_JOB_ID"
echo "   CLIP evaluation:      $CLIP_JOB_ID"
echo ""
echo "🔍 Per monitorare: squeue -u ediluzio"
echo "📊 Per vedere i log: tail -f logs/<job_name>_<job_id>.log"
echo ""
echo "🎉 PIPELINE AUTOMATICA AVVIATA!"
