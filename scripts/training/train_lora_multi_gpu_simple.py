#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import logging
import argparse
from typing import Dict, List, Optional, Any

import torch
import wandb
from datasets import load_dataset
from transformers import (
    AutoConfig,
    AutoModelForCausalLM,
    AutoTokenizer,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    TrainerCallback,
    EarlyStoppingCallback,
)
from peft import (
    LoraConfig,
    get_peft_model,
    TaskType,
    PeftModel,
)

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def cleanup_checkpoint_rng_files(checkpoint_dir):
    """
    Rimuove i file rng_state_*.pth problematici da un checkpoint.
    Questi file possono causare errori durante il resume con weights_only=True.
    """
    if not os.path.exists(checkpoint_dir):
        return

    rng_files = []
    for file in os.listdir(checkpoint_dir):
        if file.startswith("rng_state_") and file.endswith(".pth"):
            rng_files.append(file)

    if rng_files:
        logger.warning(f"🧹 Rimozione di {len(rng_files)} file rng_state problematici da {checkpoint_dir}")
        for rng_file in rng_files:
            rng_path = os.path.join(checkpoint_dir, rng_file)
            try:
                os.remove(rng_path)
                logger.info(f"   ✅ Rimosso: {rng_file}")
            except Exception as e:
                logger.error(f"   ❌ Errore rimozione {rng_file}: {e}")
    else:
        logger.info(f"✅ Nessun file rng_state trovato in {checkpoint_dir}")

# Callback personalizzato per eliminare i checkpoint vecchi
class CheckpointCleanupCallback(TrainerCallback):
    def __init__(self, save_total_limit=None):
        self.save_total_limit = save_total_limit
        self.checkpoints = []

    def on_save(self, args, state, control, **kwargs):
        if self.save_total_limit is None or self.save_total_limit <= 0:
            return

        # Aggiungi il checkpoint corrente alla lista
        checkpoint_path = os.path.join(args.output_dir, f"checkpoint-{state.global_step}")
        self.checkpoints.append((state.global_step, checkpoint_path))

        # Ordina i checkpoint per step (dal più vecchio al più recente)
        self.checkpoints.sort(key=lambda x: x[0])

        # Rimuovi i checkpoint più vecchi se superiamo il limite
        while len(self.checkpoints) > self.save_total_limit:
            _, checkpoint_to_remove = self.checkpoints.pop(0)
            if os.path.exists(checkpoint_to_remove):
                logger.info(f"Rimozione checkpoint vecchio: {checkpoint_to_remove}")
                import shutil
                shutil.rmtree(checkpoint_to_remove)

# RIMOSSO: Early stopping personalizzato che causava conflitti
# Ora usiamo solo l'early stopping integrato di Transformers che usa eval_loss

# Classe per il callback di Weights & Biands
class WandbCallback(TrainerCallback):
    def __init__(self, project_name, run_name, config, should_init=True):
        self.project_name = project_name
        self.run_name = run_name
        self.config = config
        self.initialized = False
        self.best_loss = float('inf')
        self.best_step = 0
        self.should_init = should_init  # 🔧 FIX: Controlla se questo rank può inizializzare wandb

    def on_init(self):
        if not self.initialized:
            try:
                logger.info(f"Inizializzazione wandb con entity={self.config.get('entity', '337543-unimore')}, project={self.project_name}, name={self.run_name}")
                wandb.init(
                    entity=self.config.get("entity", "337543-unimore"),
                    project=self.project_name,
                    name=self.run_name,
                    config=self.config
                )
                logger.info(f"wandb.init completato con successo. Run ID: {wandb.run.id}, Mode: {wandb.run.mode}")
                self.initialized = True
            except Exception as e:
                logger.error(f"Errore durante l'inizializzazione di wandb: {e}")
                import traceback
                logger.error(traceback.format_exc())

    def on_log(self, args, state, control, logs=None, **kwargs):
        # 🔧 FIX: Solo rank 0 può loggare su wandb in multi-GPU
        if not self.should_init:
            return

        if logs:
            # Traccia esplicitamente la loss di training
            if 'loss' in logs:
                logs['train_loss'] = logs['loss']

                # Traccia la best loss
                if logs['loss'] < self.best_loss:
                    self.best_loss = logs['loss']
                    self.best_step = state.global_step
                    logs['best_train_loss'] = self.best_loss
                    logs['best_train_loss_step'] = self.best_step

            try:
                wandb.log(logs, step=state.global_step)
            except Exception as e:
                logger.warning(f"⚠️ Errore wandb.log: {e}")

    def on_evaluate(self, args, state, control, metrics=None, **kwargs):
        # 🔧 FIX: Solo rank 0 può loggare su wandb in multi-GPU
        if not self.should_init:
            return

        if metrics:
            # Prefisso "eval_" per distinguere le metriche di valutazione
            eval_metrics = {f"eval_{k}": v for k, v in metrics.items()}
            try:
                wandb.log(eval_metrics, step=state.global_step)
            except Exception as e:
                logger.warning(f"⚠️ Errore wandb.log eval: {e}")

    def on_train_end(self, args, state, control, **kwargs):
        # 🔧 FIX: Solo rank 0 può loggare su wandb in multi-GPU
        if not self.should_init:
            return

        if self.initialized:
            # Log del riepilogo finale
            try:
                wandb.log({
                    "final_train_loss": self.best_loss,
                    "final_train_step": state.global_step if state else 0,
                    "best_train_loss": self.best_loss,
                    "best_train_loss_step": self.best_step
                })
                wandb.finish()
            except Exception as e:
                logger.warning(f"⚠️ Errore wandb.finish: {e}")

def parse_args():
    parser = argparse.ArgumentParser(description="Fine-tuning con LoRA e training multi-GPU")
    parser.add_argument("--model_name_or_path", type=str, required=True, help="Path o nome del modello pre-addestrato")
    parser.add_argument("--data_file", type=str, required=True, help="Path al file di training in formato JSON")
    parser.add_argument("--config_path", type=str, required=True, help="Path al file di configurazione JSON")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i checkpoint")
    parser.add_argument("--best_checkpoint_dir", type=str, default=None, help="Directory del miglior checkpoint")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands (username o team)")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, default=None, help="Nome della run in Weights & Biands")
    parser.add_argument("--use_wandb", action="store_true", help="Usa Weights & Biands per il tracking")
    parser.add_argument("--early_stopping", action="store_true", help="Attiva early stopping")
    parser.add_argument("--patience", type=int, default=10, help="Numero di step senza miglioramenti prima di fermare il training")
    parser.add_argument("--min_delta", type=float, default=0.001, help="Miglioramento minimo della loss per resettare il contatore")
    return parser.parse_args()

def load_config(config_path: str) -> Dict[str, Any]:
    with open(config_path, "r") as f:
        config = json.load(f)
    return config

def tokenize_svg(examples, tokenizer, max_length=512):
    prompts = []

    # Ottieni le liste di SVG e caption
    svgs = examples.get("xml", [])
    captions = examples.get("caption", [])

    # Verifica che le liste abbiano la stessa lunghezza
    if len(svgs) != len(captions):
        raise ValueError(f"Il numero di SVG ({len(svgs)}) non corrisponde al numero di caption ({len(captions)})")

    # Crea i prompt
    for svg, caption in zip(svgs, captions):
        # Crea il prompt nel formato corretto per il modello
        prompt = f"<s>[INST] Descrivi questa immagine SVG:\n{svg} [/INST] {caption}</s>"
        prompts.append(prompt)

    # Tokenizza con padding alla lunghezza massima
    tokenized = tokenizer(
        prompts,
        padding="max_length",
        truncation=True,
        max_length=max_length,
        return_tensors="pt"
    )

    # Imposta gli input_ids come labels per il training
    tokenized["labels"] = tokenized["input_ids"].clone()

    return tokenized

def main():
    args = parse_args()

    # 🚨 FIX CRITICO: Inizializzazione distribuita per torchrun multi-GPU
    if torch.cuda.device_count() > 1:
        if "RANK" in os.environ and "WORLD_SIZE" in os.environ:
            import torch.distributed as dist
            if not dist.is_initialized():
                logger.info("🔧 Inizializzazione distributed training...")
                dist.init_process_group(backend="nccl")
            local_rank = int(os.environ.get("LOCAL_RANK", 0))
            torch.cuda.set_device(local_rank)
            logger.info(f"🔧 Distributed setup: Rank {dist.get_rank()}/{dist.get_world_size()}, Local rank: {local_rank}")

    # Carica la configurazione
    config = load_config(args.config_path)
    logger.info(f"Configurazione caricata da {args.config_path}")

    # Imposta la variabile di ambiente per il token di Hugging Face
    os.environ["HF_TOKEN"] = "*************************************"

    # --------------------------------------------------------------------------
    # 🔧 FIX ROPE SCALING E CARICAMENTO MODELLO
    # --------------------------------------------------------------------------
    model_kwargs = {
        "trust_remote_code": True,
        # Aggiungi qui altri kwargs di default se necessario
    }

    # 🔧 FIX CRITICO per Llama 3.1: Disabilita rope_scaling problematico
    if "Llama-3.1" in args.model_name_or_path:
        logger.info("🔧 Applicazione fix per Llama 3.1 - DISABILITO rope_scaling...")

        # Carica la configurazione senza rope_scaling
        try:
            model_config = AutoConfig.from_pretrained(args.model_name_or_path, **model_kwargs)
            # Rimuovi completamente rope_scaling per evitare problemi
            if hasattr(model_config, "rope_scaling"):
                logger.info(f"🔧 Rimozione rope_scaling: {model_config.rope_scaling}")
                setattr(model_config, "rope_scaling", None)
                logger.info("✅ Rope scaling disabilitato")
            model_kwargs["config"] = model_config
        except Exception as e:
            logger.error(f"❌ Errore nel fix rope_scaling: {e}")
            # Prova senza config personalizzata
            logger.info("🔧 Fallback: caricamento senza config personalizzata")
    else:
        # Per tutti gli altri modelli, usa il metodo normale
        model_config = AutoConfig.from_pretrained(args.model_name_or_path, **model_kwargs)
        model_kwargs["config"] = model_config
    # --------------------------------------------------------------------------

    # Caricamento modello e tokenizer
    quantization_config = config.get("quantization", {})
    if quantization_config and quantization_config.get("load_in_4bit"):
        logger.info(f"Quantizzazione a 4-bit attivata")
        # bnb_config = BitsAndBytesConfig(...) # Mantenuto commentato per ora
        # model_kwargs['quantization_config'] = bnb_config # Mantenuto commentato
        logger.warning("BitsAndBytes è attualmente disabilitato manualmente nello script.")

    else:
        logger.info("Nessuna quantizzazione richiesta - usando LoRA FP16")
        model_kwargs["torch_dtype"] = torch.float16

    logger.info(f"Caricamento del modello: {args.model_name_or_path}")

    # Usa l'implementazione dell'attenzione specificata nella config
    attention_impl = config.get("attention_implementation", "default")
    logger.info(f"Usando implementazione dell'attenzione: {attention_impl}")
    # RIMOSSO: attention_driver non è un parametro valido per Gemma2ForCausalLM

    # Gestione distributed training
    if torch.distributed.is_initialized() and torch.cuda.device_count() > 1:
        current_device = torch.cuda.current_device()
        logger.info(f"🔧 Distributed training rilevato: device_map impostato a CUDA ID {current_device}")
        model_kwargs["device_map"] = {"": current_device}
    else:
        logger.info(f"🔧 Single device o training non distribuito: device_map=auto")
        model_kwargs["device_map"] = "auto"
    
    # Carica il modello con tutti i parametri configurati
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name_or_path,
        **model_kwargs
    )
    
    tokenizer = AutoTokenizer.from_pretrained(args.model_name_or_path, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Preparazione per il training
    # Abilita il gradient checkpointing se non si usa la quantizzazione completa
    if hasattr(model, "enable_input_require_grads"):
        model.enable_input_require_grads()
    else:
        def make_inputs_require_grad(module, input, output):
            output.requires_grad_(True)
        model.get_input_embeddings().register_forward_hook(make_inputs_require_grad)

    # Abilita il gradient checkpointing per risparmiare memoria
    if config.get("gradient_checkpointing", False):
        logger.info("Abilitazione del gradient checkpointing")
        model.gradient_checkpointing_enable()

    # Configura LoRA
    logger.info("Configurazione LoRA")
    lora_config = LoraConfig(
        r=config.get("lora_r", 16),
        lora_alpha=config.get("lora_alpha", 32),
        target_modules=config.get("lora_target_modules", ["q_proj", "v_proj"]),
        lora_dropout=config.get("lora_dropout", 0.05),
        bias=config.get("lora_bias", "none"),
        task_type=TaskType.CAUSAL_LM
    )

    # Applica LoRA al modello
    logger.info("Applicazione di LoRA al modello")
    model = get_peft_model(model, lora_config)

    # Carica i pesi LoRA dal miglior checkpoint se specificato
    if args.best_checkpoint_dir:
        logger.info(f"Caricamento dei pesi LoRA dal miglior checkpoint: {args.best_checkpoint_dir}")
        
        # Cerca il file adapter_model.bin o adapter_model.safetensors
        adapter_path = None
        if os.path.exists(os.path.join(args.best_checkpoint_dir, "adapter_model.bin")):
            adapter_path = os.path.join(args.best_checkpoint_dir, "adapter_model.bin")
            logger.info(f"Trovato file adapter_model.bin: {adapter_path}")
        elif os.path.exists(os.path.join(args.best_checkpoint_dir, "adapter_model.safetensors")):
            adapter_path = os.path.join(args.best_checkpoint_dir, "adapter_model.safetensors")
            logger.info(f"Trovato file adapter_model.safetensors: {adapter_path}")
        else:
            logger.warning(f"Non è stato trovato alcun file adapter_model nel checkpoint: {args.best_checkpoint_dir}")
            
        if adapter_path:
            try:
                # Carica i pesi dell'adapter
                if adapter_path.endswith(".safetensors"):
                    logger.info(f"Caricamento dei pesi da file safetensors: {adapter_path}")
                    from safetensors.torch import load_file
                    adapter_state_dict = load_file(adapter_path)
                else:
                    logger.info(f"Caricamento dei pesi da file bin: {adapter_path}")
                    adapter_state_dict = torch.load(adapter_path, map_location="cpu")
                
                # Carica i pesi nel modello
                missing_keys, unexpected_keys = model.load_state_dict(adapter_state_dict, strict=False)
                logger.info(f"Pesi caricati. Missing keys: {len(missing_keys)}, Unexpected keys: {len(unexpected_keys)}")
            except Exception as e:
                logger.error(f"Errore durante il caricamento dei pesi: {e}")
                import traceback
                logger.error(traceback.format_exc())

    # Stampa il numero di parametri addestrabili
    trainable_params = 0
    all_params = 0
    for _, param in model.named_parameters():
        all_params += param.numel()
        if param.requires_grad:
            trainable_params += param.numel()

    logger.info(f"Parametri addestrabili: {trainable_params}")
    logger.info(f"Tutti i parametri: {all_params}")
    logger.info(f"Percentuale parametri addestrabili: {100 * trainable_params / all_params:.4f}%")

    # Carica il dataset
    logger.info(f"Caricamento del dataset: {args.data_file}")
    data_files = {"train": args.data_file}

    # Carica anche il dataset di validazione se specificato
    val_file = config.get("val_file")
    if val_file and os.path.exists(val_file):
        logger.info(f"Caricamento del dataset di validazione: {val_file}")
        data_files["validation"] = val_file
    else:
        logger.info("Nessun dataset di validazione specificato")

    dataset = load_dataset("json", data_files=data_files)

    # Tokenizza il dataset
    logger.info("Tokenizzazione del dataset")
    tokenized_dataset = dataset.map(
        lambda examples: tokenize_svg(examples, tokenizer, max_length=config.get("max_length", 512)),
        batched=True,
        remove_columns=dataset["train"].column_names
    )

    # Configura gli argomenti di training
    logger.info("Configurazione degli argomenti di training")

    # Ottieni il numero di GPU disponibili
    num_gpus = torch.cuda.device_count()
    logger.info(f"Numero di GPU disponibili: {num_gpus}")

    # Configura DeepSpeed se specificato
    deepspeed_config = config.get("deepspeed", None)
    if deepspeed_config is not None:
        logger.info(f"Utilizzo di DeepSpeed con configurazione: {deepspeed_config}")

    training_args = TrainingArguments(
        output_dir=args.output_dir,
        per_device_train_batch_size=config.get("per_device_train_batch_size", 1),
        per_device_eval_batch_size=config.get("per_device_eval_batch_size", 1),
        gradient_accumulation_steps=config.get("gradient_accumulation_steps", 16),
        learning_rate=float(config.get("learning_rate", 2e-4)),
        num_train_epochs=float(config.get("num_train_epochs", 3.0)),
        max_steps=config.get("max_steps", -1),
        logging_steps=config.get("logging_steps", 10),
        save_steps=config.get("save_steps", 200),
        save_total_limit=config.get("save_total_limit", 3),
        optim=config.get("optim", "adamw_torch"),
        lr_scheduler_type=config.get("lr_scheduler_type", "cosine"),
        warmup_steps=config.get("warmup_steps", 0),
        warmup_ratio=float(config.get("warmup_ratio", 0.03)),
        weight_decay=float(config.get("weight_decay", 0.001)),
        fp16=config.get("fp16", False),
        bf16=config.get("bf16", True),
        gradient_checkpointing=config.get("gradient_checkpointing", True),
        gradient_checkpointing_kwargs={"use_reentrant": False},
        remove_unused_columns=config.get("remove_unused_columns", False),
        report_to="none",  # Disabilita il reporting integrato, useremo il nostro callback
        ddp_find_unused_parameters=config.get("ddp_find_unused_parameters", False),
        ddp_bucket_cap_mb=config.get("ddp_bucket_cap_mb", 128),
        dataloader_pin_memory=config.get("dataloader_pin_memory", True),
        dataloader_num_workers=config.get("dataloader_num_workers", 4),
        group_by_length=config.get("group_by_length", True),
        # Evaluation settings - NECESSARIE per early stopping integrato
        eval_strategy=config.get("eval_strategy", "steps"),
        eval_steps=config.get("eval_steps", 500),
        load_best_model_at_end=config.get("load_best_model_at_end", True),
        metric_for_best_model=config.get("metric_for_best_model", "eval_loss"),
        greater_is_better=config.get("greater_is_better", False),
        # Early stopping integrato di Transformers
        early_stopping_patience=config.get("early_stopping_patience", 10),
        deepspeed=deepspeed_config
    )

    # Crea il data collator
    data_collator = DataCollatorForLanguageModeling(tokenizer=tokenizer, mlm=False)

    # Configura Weights & Biands
    wandb_run_name = args.wandb_run_name or f"{os.path.basename(args.model_name_or_path)}_lora_xml_multi_gpu_simple"

    # Crea i callback
    checkpoint_callback = CheckpointCleanupCallback(save_total_limit=config.get("save_total_limit", 3))

    # RIMOSSO: Early stopping personalizzato - ora usiamo solo quello integrato di Transformers
    if args.early_stopping:
        logger.info(f"Early stopping configurato nei TrainingArguments con patience={config.get('early_stopping_patience', 10)}")
        logger.info("NOTA: Usando early stopping integrato di Transformers che monitora eval_loss")

    # Forza l'uso di wandb indipendentemente dal flag
    logger.info("Forzando l'uso di wandb indipendentemente dal flag --use_wandb")

    # 🔧 FIX: Determina se questo rank può inizializzare wandb
    should_init_wandb = True
    if torch.cuda.device_count() > 1 and "RANK" in os.environ:
        import torch.distributed as dist
        if dist.is_initialized():
            should_init_wandb = dist.get_rank() == 0
            logger.info(f"🔧 Wandb init: Rank {dist.get_rank()}, should_init={should_init_wandb}")

    wandb_callback = WandbCallback(
        project_name=args.wandb_project,
        run_name=wandb_run_name,
        config={
            "entity": args.wandb_entity,
            "model_name": args.model_name_or_path,
            "dataset": args.data_file,
            "best_checkpoint_dir": args.best_checkpoint_dir,
            "early_stopping": args.early_stopping,
            "patience": args.patience if args.early_stopping else None,
            "min_delta": args.min_delta if args.early_stopping else None,
            "trainable_params": trainable_params,
            "trainable_percentage": 100 * trainable_params / all_params,
            **config
        },
        should_init=should_init_wandb  # 🔧 FIX: Passa should_init al callback
    )

    # Inizializza Weights & Biands solo su rank 0 per evitare run duplicati
    if wandb_callback and wandb_callback.should_init:
        wandb_callback.on_init()
        logger.info(f"Weights & Biands inizializzato con entity={args.wandb_entity}, project={args.wandb_project}, run_name={wandb_run_name}")
        logger.info(f"Weights & Biands mode: {wandb.run.mode}")
    elif wandb_callback:
        logger.info("🔧 Wandb init saltato (non rank 0)")

    # Crea il trainer
    eval_dataset = None
    if "validation" in tokenized_dataset:
        eval_dataset = tokenized_dataset["validation"]
        logger.info(f"Dataset di validazione configurato con {len(eval_dataset)} esempi")

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset["train"],
        eval_dataset=eval_dataset,
        data_collator=data_collator,
    )

    # Aggiungi i nostri callback personalizzati
    callbacks = []

    # Aggiungi il callback di checkpoint
    callbacks.append(checkpoint_callback)

    # Aggiungi Weights & Biands callback se richiesto
    if wandb_callback:
        callbacks.append(wandb_callback)

    # Aggiungi early stopping integrato di Transformers se richiesto
    if args.early_stopping:
        early_stopping_patience = config.get("early_stopping_patience", args.patience)
        early_stopping_callback = EarlyStoppingCallback(
            early_stopping_patience=early_stopping_patience,
            early_stopping_threshold=args.min_delta
        )
        callbacks.append(early_stopping_callback)
        logger.info(f"✅ Early stopping integrato aggiunto con patience={early_stopping_patience}, threshold={args.min_delta}")

    # Aggiungi i callback al trainer
    for callback in callbacks:
        trainer.add_callback(callback)

    # Avvia il training (con resume automatico se ci sono checkpoint)
    logger.info("Avvio del training")

    # Controlla se ci sono checkpoint esistenti per il resume automatico
    resume_from_checkpoint = None
    if os.path.exists(args.output_dir):
        checkpoints = [d for d in os.listdir(args.output_dir) if d.startswith("checkpoint-")]
        if checkpoints:
            # Trova l'ultimo checkpoint
            latest_checkpoint = max(checkpoints, key=lambda x: int(x.split("-")[1]))
            resume_from_checkpoint = os.path.join(args.output_dir, latest_checkpoint)
            logger.info(f"🔄 RESUME: Trovato checkpoint esistente: {resume_from_checkpoint}")

            # 🔧 FIX: Rimuovi i file rng_state_*.pth problematici se esistono
            cleanup_checkpoint_rng_files(resume_from_checkpoint)
        else:
            logger.info("🆕 NUOVO TRAINING: Nessun checkpoint trovato")

    try:
        trainer.train(resume_from_checkpoint=resume_from_checkpoint)
    except Exception as e:
        logger.error(f"Errore durante il training: {e}")
        # Se l'errore è legato ai file rng_state, prova a rimuoverli e riavviare
        if "rng_state" in str(e) and resume_from_checkpoint:
            logger.warning("Errore legato a rng_state rilevato. Tentativo di pulizia e riavvio...")
            cleanup_checkpoint_rng_files(resume_from_checkpoint)
            logger.info("Riavvio del training dopo la pulizia dei file rng_state...")
            trainer.train(resume_from_checkpoint=resume_from_checkpoint)
        else:
            raise

    # Salva il modello finale
    logger.info(f"Salvataggio del modello finale in {args.output_dir}")
    trainer.save_model(args.output_dir)

    # Chiudi Weights & Biands
    if wandb_callback:
        wandb_callback.on_train_end(None, None, None)

    logger.info("Training completato")

if __name__ == "__main__":
    main()
