#!/bin/bash

# Script per comprimere i file di log vecchi

# Crea la directory per i log compressi
COMPRESSED_LOGS_DIR="/work/tesi_ediluzio/logs/compressed"
mkdir -p "$COMPRESSED_LOGS_DIR"

# Ottieni la data di oggi
TODAY=$(date +%Y%m%d)

# Comprimi i file di log vecchi (non degli ultimi 3 giorni)
echo "Compressione dei file di log vecchi..."

# Trova i file di log vecchi (non degli ultimi 3 giorni e non dei job attivi)
find /work/tesi_ediluzio/logs -type f -name "*.out" -o -name "*.err" | grep -v "2567" | grep -v "2568" > /tmp/old_logs.txt

# Conta il numero di file trovati
NUM_FILES=$(wc -l < /tmp/old_logs.txt)
echo "Trovati $NUM_FILES file di log vecchi"

# Se non ci sono file, esci
if [ "$NUM_FILES" -eq 0 ]; then
    echo "Nessun file di log vecchio da comprimere"
    exit 0
fi

# Crea un archivio con i file di log vecchi
ARCHIVE_NAME="logs_archive_${TODAY}.tar.gz"
echo "Creazione dell'archivio $ARCHIVE_NAME..."

# Crea l'archivio
tar -czf "$COMPRESSED_LOGS_DIR/$ARCHIVE_NAME" -T /tmp/old_logs.txt

# Verifica che l'archivio sia stato creato correttamente
if [ $? -eq 0 ]; then
    echo "Archivio creato con successo: $COMPRESSED_LOGS_DIR/$ARCHIVE_NAME"
    
    # Elimina i file originali
    echo "Eliminazione dei file originali..."
    xargs rm -f < /tmp/old_logs.txt
    
    echo "Compressione completata con successo"
else
    echo "Errore durante la creazione dell'archivio"
    exit 1
fi

# Elimina il file temporaneo
rm -f /tmp/old_logs.txt

echo "Operazione completata"
