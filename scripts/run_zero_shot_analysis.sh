#!/bin/bash

# Script per eseguire l'analisi completa delle inferenze zero-shot

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
RESULTS_DIR="/work/tesi_ediluzio/results"
ANALYSIS_DIR="/work/tesi_ediluzio/analysis/zero_shot"
SAMPLE_DATA_PATH="$RESULTS_DIR/sample_zero_shot_results.jsonl"
WANDB_PROJECT="captioner"
WANDB_ENTITY="337543-unimore"
WANDB_RUN_NAME="zero_shot_analysis_$(date +%Y%m%d_%H%M%S)"

# Crea le directory necessarie
mkdir -p "$RESULTS_DIR"
mkdir -p "$ANALYSIS_DIR"

# Verifica se esistono dati reali di inferenza zero-shot
REAL_DATA_FOUND=false
REAL_DATA_PATH=""

# Cerca file di risultati esistenti
for file in "$RESULTS_DIR"/*zero_shot*.jsonl "$RESULTS_DIR"/*_eval_*.jsonl; do
    if [ -f "$file" ]; then
        echo "Trovato file di risultati reali: $file"
        REAL_DATA_FOUND=true
        REAL_DATA_PATH="$file"
        break
    fi
done

# Se non sono stati trovati dati reali, genera dati di esempio
if [ "$REAL_DATA_FOUND" = false ]; then
    echo "Nessun dato reale trovato. Generazione di dati di esempio..."
    $PYTHON_ENV scripts/generate_sample_data.py --num_samples 200 --output_path "$SAMPLE_DATA_PATH"
    DATA_PATH="$SAMPLE_DATA_PATH"
else
    echo "Utilizzo dei dati reali trovati."
    DATA_PATH="$REAL_DATA_PATH"
fi

# Esegui l'analisi
echo "Esecuzione dell'analisi zero-shot..."
$PYTHON_ENV scripts/analyze_zero_shot.py \
    --results_path "$DATA_PATH" \
    --output_dir "$ANALYSIS_DIR" \
    --use_wandb \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_entity "$WANDB_ENTITY"

# Verifica il codice di uscita
if [ $? -eq 0 ]; then
    echo "Analisi completata con successo!"
    echo "I grafici sono stati salvati in: $ANALYSIS_DIR"
    echo "I risultati sono stati caricati su Weights & Biands: $WANDB_PROJECT/$WANDB_RUN_NAME"
else
    echo "Errore durante l'analisi. Controlla i log per maggiori dettagli."
fi
