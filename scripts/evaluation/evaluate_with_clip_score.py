#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per calcolare il CLIP Score per i risultati di inferenza SVG.
Supporta sia modelli plain (zero-shot) che fine-tuned.
"""

import os
import sys
import json
import argparse
import logging
import torch
import clip
import numpy as np
import pandas as pd
from PIL import Image
import cairosvg
import io
from tqdm import tqdm
import matplotlib.pyplot as plt
import wandb

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def render_svg_to_image(svg_string: str, size: int = 224) -> Image.Image:
    """
    Renderizza una stringa SVG in un'immagine PIL.

    Args:
        svg_string: Stringa SVG da renderizzare
        size: Dimensione dell'immagine di output

    Returns:
        Immagine PIL renderizzata
    """
    try:
        # Renderizza SVG in PNG
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'),
                                    output_width=size,
                                    output_height=size)

        # Converti i dati PNG in un'immagine PIL
        image = Image.open(io.BytesIO(png_data))
        return image
    except Exception as e:
        logger.error(f"Errore nel rendering SVG: {e}")
        # Restituisci un'immagine nera in caso di errore
        return Image.new('RGB', (size, size), color='black')

def calculate_clip_score(image: Image.Image, caption: str, model, preprocess, device: str) -> float:
    """
    Calcola il CLIP score tra un'immagine e una caption.

    Args:
        image: Immagine PIL
        caption: Testo della caption
        model: Modello CLIP
        preprocess: Funzione di preprocessing CLIP
        device: Dispositivo su cui eseguire il calcolo

    Returns:
        CLIP score (similarità coseno * 100)
    """
    try:
        # Preprocess dell'immagine
        image_input = preprocess(image).unsqueeze(0).to(device)

        # Tokenizza la caption
        text_input = clip.tokenize([caption]).to(device)

        # Calcola le features
        with torch.no_grad():
            image_features = model.encode_image(image_input)
            text_features = model.encode_text(text_input)

        # Normalizza le features
        image_features = image_features / image_features.norm(dim=-1, keepdim=True)
        text_features = text_features / text_features.norm(dim=-1, keepdim=True)

        # Calcola la similarità coseno
        similarity = (100.0 * image_features @ text_features.T).item()
        return similarity
    except Exception as e:
        logger.error(f"Errore nel calcolo del CLIP score: {e}")
        return 0.0

def load_results(results_file: str) -> list:
    """
    Carica i risultati dell'inferenza da un file JSON o JSONL.

    Args:
        results_file: Percorso del file dei risultati

    Returns:
        Lista di risultati
    """
    results = []

    try:
        # Determina se il file è JSON o JSONL
        with open(results_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()

            # Riavvolgi il file
            f.seek(0)

            if first_line.startswith('[') or first_line.endswith(']'):
                # File JSON
                results = json.load(f)
            else:
                # File JSONL
                for line in f:
                    results.append(json.loads(line))

        logger.info(f"Caricati {len(results)} risultati da {results_file}")
        return results
    except Exception as e:
        logger.error(f"Errore nel caricamento dei risultati: {e}")
        return []

def load_svg_data(data_file: str) -> dict:
    """
    Carica i dati SVG dal file originale.

    Args:
        data_file: Percorso del file dei dati

    Returns:
        Dizionario con ID come chiave e dati SVG come valore
    """
    svg_data = {}

    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        for item in data:
            item_id = item.get('id')
            svg = item.get('xml', '')

            if item_id and svg:
                svg_data[item_id] = svg

        logger.info(f"Caricati dati SVG per {len(svg_data)} esempi da {data_file}")
        return svg_data
    except Exception as e:
        logger.error(f"Errore nel caricamento dei dati SVG: {e}")
        return {}

def main():
    parser = argparse.ArgumentParser(description="Calcola il CLIP Score per i risultati di inferenza SVG.")
    parser.add_argument("--results_file", type=str, required=True, help="File JSON o JSONL con i risultati dell'inferenza.")
    parser.add_argument("--data_file", type=str, required=True, help="File JSON con i dati SVG originali.")
    parser.add_argument("--output_file", type=str, required=True, help="File di output per i risultati con CLIP Score.")
    parser.add_argument("--model_name", type=str, required=True, help="Nome del modello (per il report).")
    parser.add_argument("--clip_model", type=str, default="ViT-B/32", help="Modello CLIP da utilizzare.")
    parser.add_argument("--render_size", type=int, default=224, help="Dimensione per il rendering SVG.")
    parser.add_argument("--use_wandb", action="store_true", help="Usa Weights & Biands per il logging.")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands.")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands.")
    parser.add_argument("--num_samples", type=int, default=100, help="Numero di campioni da valutare. -1 per tutti.")

    args = parser.parse_args()

    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Inizializza Weights & Biands se richiesto
    if args.use_wandb:
        os.environ["WANDB_API_KEY"] = "****************************************"
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=f"{args.model_name}_clip_evaluation",
            job_type="evaluation"
        )

    # Carica il modello CLIP
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Caricamento del modello CLIP {args.clip_model} su {device}")
    model, preprocess = clip.load(args.clip_model, device=device)

    # Carica i risultati dell'inferenza
    results = load_results(args.results_file)

    # Carica i dati SVG originali
    svg_data = load_svg_data(args.data_file)

    # Limita il numero di campioni se richiesto
    if args.num_samples > 0 and args.num_samples < len(results):
        results = results[:args.num_samples]
        logger.info(f"Limitato a {args.num_samples} campioni")

    # Calcola il CLIP score per ogni risultato
    clip_scores = []

    for result in tqdm(results, desc="Calcolo CLIP Score"):
        item_id = result.get('id')
        generated_caption = result.get('generated_caption', '')
        true_caption = result.get('true_caption', '')

        # Ottieni i dati SVG
        svg = svg_data.get(item_id, '')

        if not svg:
            logger.warning(f"Dati SVG non trovati per l'ID {item_id}")
            continue

        # Renderizza l'SVG
        image = render_svg_to_image(svg, args.render_size)

        # Calcola il CLIP score per la caption generata
        generated_clip_score = calculate_clip_score(image, generated_caption, model, preprocess, device)

        # Calcola il CLIP score per la caption di riferimento
        true_clip_score = calculate_clip_score(image, true_caption, model, preprocess, device)

        # Aggiungi i CLIP score al risultato
        result['generated_clip_score'] = generated_clip_score
        result['true_clip_score'] = true_clip_score
        result['clip_score_ratio'] = generated_clip_score / true_clip_score if true_clip_score > 0 else 0

        clip_scores.append({
            'id': item_id,
            'generated_clip_score': generated_clip_score,
            'true_clip_score': true_clip_score,
            'clip_score_ratio': result['clip_score_ratio']
        })

    # Calcola statistiche aggregate
    generated_scores = [r['generated_clip_score'] for r in results if 'generated_clip_score' in r]
    true_scores = [r['true_clip_score'] for r in results if 'true_clip_score' in r]
    ratios = [r['clip_score_ratio'] for r in results if 'clip_score_ratio' in r]

    aggregated_metrics = {
        'mean_generated_clip_score': np.mean(generated_scores) if generated_scores else 0,
        'mean_true_clip_score': np.mean(true_scores) if true_scores else 0,
        'mean_clip_score_ratio': np.mean(ratios) if ratios else 0,
        'median_generated_clip_score': np.median(generated_scores) if generated_scores else 0,
        'median_true_clip_score': np.median(true_scores) if true_scores else 0,
        'median_clip_score_ratio': np.median(ratios) if ratios else 0,
        'min_generated_clip_score': np.min(generated_scores) if generated_scores else 0,
        'max_generated_clip_score': np.max(generated_scores) if generated_scores else 0,
        'std_generated_clip_score': np.std(generated_scores) if generated_scores else 0,
        'num_samples': len(generated_scores)
    }

    # Crea il risultato finale
    final_result = {
        'model_name': args.model_name,
        'clip_model': args.clip_model,
        'aggregated': aggregated_metrics,
        'samples': results
    }

    # Salva i risultati
    with open(args.output_file, 'w', encoding='utf-8') as f:
        json.dump(final_result, f, indent=2)

    logger.info(f"Risultati salvati in {args.output_file}")

    # Crea un grafico di distribuzione dei CLIP score
    plt.figure(figsize=(10, 6))
    plt.hist(generated_scores, bins=20, alpha=0.7, label='Generated Captions')
    plt.hist(true_scores, bins=20, alpha=0.7, label='True Captions')
    plt.xlabel('CLIP Score')
    plt.ylabel('Frequency')
    plt.title(f'CLIP Score Distribution - {args.model_name}')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Salva il grafico
    plot_path = os.path.join(os.path.dirname(args.output_file), f"{args.model_name}_clip_score_distribution.png")
    plt.savefig(plot_path, dpi=300)
    plt.close()

    logger.info(f"Grafico salvato in {plot_path}")

    # Log su Weights & Biands
    if args.use_wandb:
        # Log delle metriche aggregate
        for metric, value in aggregated_metrics.items():
            wandb.log({f"{args.model_name}_{metric}": value})

        # Log del grafico
        wandb.log({f"{args.model_name}_clip_score_distribution": wandb.Image(plot_path)})

        # Crea una tabella con i CLIP score
        clip_scores_df = pd.DataFrame(clip_scores)
        wandb_table = wandb.Table(dataframe=clip_scores_df)
        wandb.log({f"{args.model_name}_clip_scores": wandb_table})

        # Chiudi wandb
        wandb.finish()

    logger.info("Valutazione con CLIP completata!")

if __name__ == "__main__":
    main()
