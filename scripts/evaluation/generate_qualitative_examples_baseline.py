#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare esempi qualitativi per i modelli baseline:
Ide Fix 3, Flores 2 base, BLIP 2.7B
"""

import os
import json
import random
import argparse
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_baseline_predictions(predictions_file):
    """Carica le predizioni baseline da file JSONL."""
    predictions = []
    try:
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    predictions.append(json.loads(line))
        logger.info(f"Caricate {len(predictions)} predizioni da {predictions_file}")
        return predictions
    except Exception as e:
        logger.error(f"Errore nel caricamento {predictions_file}: {e}")
        return []

def load_test_data(test_file):
    """Carica i dati di test con SVG."""
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        logger.info(f"Caricati {len(test_data)} esempi di test")
        return test_data
    except Exception as e:
        logger.error(f"Errore nel caricamento test data: {e}")
        return []

def calculate_simple_quality_score(reference, generated):
    """Calcola un punteggio di qualità semplice."""
    if not reference or not generated:
        return 0.0
    
    # Tokenizza
    ref_words = set(reference.lower().split())
    gen_words = set(generated.lower().split())
    
    # Jaccard similarity
    intersection = ref_words.intersection(gen_words)
    union = ref_words.union(gen_words)
    
    jaccard = len(intersection) / len(union) if union else 0.0
    
    # Penalizza risposte troppo corte o troppo lunghe
    length_ratio = min(len(gen_words), len(ref_words)) / max(len(gen_words), len(ref_words), 1)
    
    return jaccard * length_ratio

def categorize_examples(predictions, num_best=5, num_worst=5, num_typical=5):
    """Categorizza esempi in migliori, peggiori e tipici."""
    
    # Calcola punteggi di qualità
    scored_predictions = []
    for pred in predictions:
        reference = pred.get('reference', '')
        generated = pred.get('generated', '')
        score = calculate_simple_quality_score(reference, generated)
        
        scored_predictions.append({
            **pred,
            'quality_score': score
        })
    
    # Ordina per punteggio
    scored_predictions.sort(key=lambda x: x['quality_score'], reverse=True)
    
    # Seleziona esempi
    best_examples = scored_predictions[:num_best]
    worst_examples = scored_predictions[-num_worst:]
    
    # Esempi tipici (dal centro della distribuzione)
    mid_start = len(scored_predictions) // 2 - num_typical // 2
    mid_end = mid_start + num_typical
    typical_examples = scored_predictions[mid_start:mid_end]
    
    return best_examples, typical_examples, worst_examples

def generate_qualitative_report(model_name, predictions, test_data, output_dir):
    """Genera report qualitativo per un modello."""
    
    logger.info(f"🎯 Generazione report qualitativo per {model_name}...")
    
    if not predictions:
        logger.error(f"Nessuna predizione per {model_name}")
        return None
    
    # Crea mappa SVG
    svg_map = {}
    for item in test_data:
        item_id = item.get('id', item.get('example_id'))
        svg_data = item.get('xml', item.get('svg', item.get('svg_data')))
        true_caption = item.get('caption', item.get('reference_caption', ''))
        
        if item_id and svg_data:
            svg_map[str(item_id)] = {
                'svg': svg_data,
                'true_caption': true_caption
            }
    
    # Categorizza esempi
    best_examples, typical_examples, worst_examples = categorize_examples(predictions)
    
    # Crea report
    report = {
        'model_name': model_name,
        'total_predictions': len(predictions),
        'examples': {
            'best': [],
            'typical': [],
            'worst': []
        },
        'statistics': {
            'avg_quality_score': sum(p.get('quality_score', 0) for p in predictions) / len(predictions),
            'best_score': max(p.get('quality_score', 0) for p in predictions),
            'worst_score': min(p.get('quality_score', 0) for p in predictions)
        }
    }
    
    # Processa esempi per ogni categoria
    for category, examples in [('best', best_examples), ('typical', typical_examples), ('worst', worst_examples)]:
        for example in examples:
            pred_id = str(example.get('id', 'unknown'))
            reference = example.get('reference', '')
            generated = example.get('generated', '')
            quality_score = example.get('quality_score', 0)
            
            # Trova SVG corrispondente
            svg_info = svg_map.get(pred_id, {})
            svg_data = svg_info.get('svg', '')
            true_caption = svg_info.get('true_caption', reference)
            
            example_data = {
                'id': pred_id,
                'reference_caption': reference,
                'true_caption': true_caption,
                'generated_caption': generated,
                'quality_score': quality_score,
                'svg_data': svg_data[:500] + '...' if len(svg_data) > 500 else svg_data,  # Tronca SVG lungo
                'analysis': analyze_example(reference, generated)
            }
            
            report['examples'][category].append(example_data)
    
    # Salva report
    output_file = Path(output_dir) / f"{model_name}_qualitative_examples.json"
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    logger.info(f"✅ Report qualitativo salvato in {output_file}")
    
    return report

def analyze_example(reference, generated):
    """Analizza un esempio specifico."""
    analysis = {
        'length_comparison': f"Ref: {len(reference.split())} words, Gen: {len(generated.split())} words",
        'common_words': [],
        'missing_concepts': [],
        'extra_concepts': [],
        'issues': []
    }
    
    ref_words = set(reference.lower().split())
    gen_words = set(generated.lower().split())
    
    # Parole comuni
    common = ref_words.intersection(gen_words)
    analysis['common_words'] = list(common)[:10]  # Prime 10
    
    # Concetti mancanti
    missing = ref_words - gen_words
    analysis['missing_concepts'] = list(missing)[:10]  # Prime 10
    
    # Concetti extra
    extra = gen_words - ref_words
    analysis['extra_concepts'] = list(extra)[:10]  # Prime 10
    
    # Identifica problemi comuni
    if len(generated.split()) < 3:
        analysis['issues'].append("Risposta troppo corta")
    
    if len(generated.split()) > len(reference.split()) * 2:
        analysis['issues'].append("Risposta troppo lunga")
    
    if not any(word in generated.lower() for word in ['image', 'depicts', 'shows', 'contains']):
        analysis['issues'].append("Manca struttura descrittiva")
    
    if len(common) / len(ref_words) < 0.1:
        analysis['issues'].append("Bassa sovrapposizione semantica")
    
    return analysis

def create_markdown_report(all_reports, output_dir):
    """Crea un report Markdown riassuntivo."""
    
    md_content = """# 📊 ESEMPI QUALITATIVI BASELINE
## **Analisi Dettagliata Performance Modelli Baseline**

---

"""
    
    for model_name, report in all_reports.items():
        md_content += f"""## 🎯 **{model_name.upper()}**

### **📈 Statistiche Generali:**
- **Predizioni totali**: {report['total_predictions']}
- **Punteggio qualità medio**: {report['statistics']['avg_quality_score']:.4f}
- **Migliore punteggio**: {report['statistics']['best_score']:.4f}
- **Peggiore punteggio**: {report['statistics']['worst_score']:.4f}

### **🏆 ESEMPI MIGLIORI:**

"""
        
        for i, example in enumerate(report['examples']['best'], 1):
            md_content += f"""#### **Esempio {i} (Score: {example['quality_score']:.3f})**
- **ID**: {example['id']}
- **Reference**: "{example['reference_caption']}"
- **Generated**: "{example['generated_caption']}"
- **Analisi**: {example['analysis']['length_comparison']}
- **Problemi**: {', '.join(example['analysis']['issues']) if example['analysis']['issues'] else 'Nessuno'}

"""
        
        md_content += f"""### **📊 ESEMPI TIPICI:**

"""
        
        for i, example in enumerate(report['examples']['typical'], 1):
            md_content += f"""#### **Esempio {i} (Score: {example['quality_score']:.3f})**
- **ID**: {example['id']}
- **Reference**: "{example['reference_caption']}"
- **Generated**: "{example['generated_caption']}"
- **Problemi**: {', '.join(example['analysis']['issues']) if example['analysis']['issues'] else 'Nessuno'}

"""
        
        md_content += f"""### **❌ ESEMPI PEGGIORI:**

"""
        
        for i, example in enumerate(report['examples']['worst'], 1):
            md_content += f"""#### **Esempio {i} (Score: {example['quality_score']:.3f})**
- **ID**: {example['id']}
- **Reference**: "{example['reference_caption']}"
- **Generated**: "{example['generated_caption']}"
- **Problemi**: {', '.join(example['analysis']['issues']) if example['analysis']['issues'] else 'Nessuno'}

"""
        
        md_content += "---\n\n"
    
    # Salva report Markdown
    md_file = Path(output_dir) / "ESEMPI_QUALITATIVI_BASELINE.md"
    with open(md_file, 'w', encoding='utf-8') as f:
        f.write(md_content)
    
    logger.info(f"✅ Report Markdown salvato in {md_file}")

def main():
    parser = argparse.ArgumentParser(description='Genera esempi qualitativi per modelli baseline')
    parser.add_argument('--test_file', default='data/processed/xml_format/test_set_final_xml_reduced_rgb.json',
                       help='File dati di test con SVG')
    parser.add_argument('--baseline_dir', default='experiments/baseline_svg_fixed_final', 
                       help='Directory con predizioni baseline')
    parser.add_argument('--output_dir', default='experiments/xml_direct_input/outputs/qualitative_baseline', 
                       help='Directory output')
    parser.add_argument('--models', nargs='+', 
                       choices=['idefix', 'flores2', 'blip2'], 
                       default=['idefix', 'flores2', 'blip2'],
                       help='Modelli da analizzare')
    
    args = parser.parse_args()
    
    # Carica dati di test
    test_data = load_test_data(args.test_file)
    if not test_data:
        logger.error("Impossibile caricare dati di test")
        return
    
    # Mappa nomi modelli
    model_mapping = {
        'idefix': 'baseline_idefix_svg_fixed',
        'flores2': 'baseline_flores2_svg_fixed', 
        'blip2': 'baseline_blip2_svg_fixed'
    }
    
    model_names = {
        'idefix': 'Ide Fix 3',
        'flores2': 'Flores 2 base',
        'blip2': 'BLIP 2.7B'
    }
    
    # Genera report per ogni modello
    all_reports = {}
    
    for model in args.models:
        model_dir = model_mapping[model]
        predictions_file = Path(args.baseline_dir) / model_dir / 'predictions.jsonl'
        
        if not predictions_file.exists():
            logger.warning(f"File predizioni non trovato: {predictions_file}")
            continue
        
        predictions = load_baseline_predictions(predictions_file)
        if not predictions:
            continue
        
        report = generate_qualitative_report(
            model_names[model], predictions, test_data, args.output_dir
        )
        
        if report:
            all_reports[model_names[model]] = report
    
    # Crea report Markdown riassuntivo
    if all_reports:
        create_markdown_report(all_reports, args.output_dir)
    
    # Stampa riassunto
    print("\n" + "="*80)
    print("📊 ESEMPI QUALITATIVI BASELINE - RIASSUNTO")
    print("="*80)
    
    for model_name, report in all_reports.items():
        stats = report['statistics']
        print(f"\n🎯 {model_name}:")
        print(f"   📊 Predizioni: {report['total_predictions']}")
        print(f"   📈 Qualità media: {stats['avg_quality_score']:.4f}")
        print(f"   🏆 Migliore: {stats['best_score']:.4f}")
        print(f"   ❌ Peggiore: {stats['worst_score']:.4f}")
    
    print("\n" + "="*80)
    print("✅ Esempi qualitativi generati con successo!")

if __name__ == "__main__":
    main()
