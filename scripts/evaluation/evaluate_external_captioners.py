#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import json
import os
import logging
from io import BytesIO

import torch
from PIL import Image
from tqdm import tqdm
import matplotlib.pyplot as plt
import numpy as np

# Import per la rasterizzazione SVG
try:
    from cairosvg import svg2png
except ImportError:
    print("ATTENZIONE: cairosvg non trovato. Per favore installalo con: pip install cairosvg")
    svg2png = None

# Import per i modelli e le metriche
try:
    from transformers import AutoProcessor, AutoModelForCausalLM, AutoModelForVision2Seq, pipeline
    import evaluate
except ImportError:
    print("ATTENZIONE: transformers o evaluate non trovati. Per favore installali con: pip install transformers evaluate")
    AutoProcessor, AutoModelForCausalLM, AutoModelForVision2Seq, pipeline, evaluate = [None]*5

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Modelli da testare
MODEL_CONFIG = {
    "git-large-coco": {
        "model_id": "microsoft/git-large-coco",
        "type": "git" # o pipeline
    },
    "blip-large-captioning": {
        "model_id": "Salesforce/blip-image-captioning-large",
        "type": "blip" # o pipeline
    },
    "vit-gpt2-captioning": {
        "model_id": "nlpconnect/vit-gpt2-image-captioning",
        "type": "vit-gpt2" # o pipeline
    }
}

# Metriche da calcolare
METRICS_TO_COMPUTE = ["bleu", "meteor", "cider", "clip_score", "rouge"] # Aggiunto rouge

def rasterize_svg(svg_string, dpi=96, output_format='PNG'):
    if svg2png is None:
        raise ImportError("cairosvg non è installato. Impossibile rasterizzare SVG.")
    try:
        png_bytes = svg2png(bytestring=svg_string.encode('utf-8'), dpi=dpi)
        return Image.open(BytesIO(png_bytes)).convert("RGB")
    except Exception as e:
        logger.error(f"Errore durante la rasterizzazione SVG: {e}")
        # Logga una porzione dell'SVG per debug
        logger.error(f"SVG problematico (prime 200 chars): {svg_string[:200]}")
        return None

def load_model(model_name):
    if model_name not in MODEL_CONFIG:
        raise ValueError(f"Configurazione per il modello {model_name} non trovata.")
    
    config = MODEL_CONFIG[model_name]
    model_id = config["model_id"]
    model_type = config["type"]
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Caricamento del modello {model_id} su {device}...")

    try:
        if model_type == "pipeline": # Approccio generico con pipeline
             img_captioner = pipeline("image-to-text", model=model_id, device=0 if device=="cuda" else -1) # pipeline usa device index
             return lambda image_obj: img_captioner(image_obj)[0]['generated_text']

        elif model_type == "git":
            processor = AutoProcessor.from_pretrained(model_id)
            model = AutoModelForCausalLM.from_pretrained(model_id).to(device)
            
            def generate_caption_git(image_obj):
                pixel_values = processor(images=image_obj, return_tensors="pt").pixel_values.to(device)
                generated_ids = model.generate(pixel_values=pixel_values, max_length=50)
                return processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            return generate_caption_git

        elif model_type == "blip":
            processor = AutoProcessor.from_pretrained(model_id)
            model = AutoModelForVision2Seq.from_pretrained(model_id).to(device)

            def generate_caption_blip(image_obj):
                inputs = processor(images=image_obj, return_tensors="pt").to(device)
                pixel_values = inputs.pixel_values
                generated_ids = model.generate(pixel_values=pixel_values, max_length=50)
                return processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            return generate_caption_blip
        
        elif model_type == "vit-gpt2":
            img_captioner = pipeline("image-to-text", model=model_id, device=0 if device=="cuda" else -1) # pipeline usa device index
            logger.info(f"Modello {model_id} caricato con pipeline.")
            return lambda image_obj: img_captioner(image_obj)[0]['generated_text']

        else:
            raise ValueError(f"Tipo di modello {model_type} non supportato per {model_id}")
            
    except Exception as e:
        logger.error(f"Errore durante il caricamento del modello {model_id}: {e}")
        raise

def calculate_metrics(predictions, references, pil_images):
    logger.info("Calcolo delle metriche...")
    results = {}
    # Assicuriamo che references sia list[list[str]]
    formatted_references = [[ref] if isinstance(ref, str) else ref for ref in references]

    for metric_name in METRICS_TO_COMPUTE:
        try:
            metric = evaluate.load(metric_name)
            
            if metric_name == "bleu":
                bleu_scores = metric.compute(predictions=predictions, references=formatted_references)
                results["BLEU-1"] = bleu_scores['precisions'][0]
                results["BLEU-2"] = bleu_scores['precisions'][1]
                results["BLEU-3"] = bleu_scores['precisions'][2]
                results["BLEU-4"] = bleu_scores['precisions'][3]
            elif metric_name == "rouge":
                rouge_scores = metric.compute(predictions=predictions, references=formatted_references)
                results["ROUGE-L"] = rouge_scores['rougeL']
            elif metric_name == "clip_score":
                # CLIPScore richiede le immagini PIL e le predizioni (caption generate)
                if len(pil_images) != len(predictions):
                    logger.warning(f"CLIPScore: Numero di immagini ({len(pil_images)}) non corrisponde al numero di predizioni ({len(predictions)}). Salto.")
                    results[metric_name.upper()] = 0.0
                    continue
                logger.info(f"Calcolo CLIPScore per {len(pil_images)} immagini...")
                clip_results = metric.compute(images=pil_images, texts=predictions)
                results["CLIP SCORE"] = clip_results['clip_score']
            else: # METEOR, CIDEr
                score = metric.compute(predictions=predictions, references=formatted_references)
                results[metric_name.upper()] = score[metric_name] 
                
        except Exception as e:
            logger.error(f"Errore durante il calcolo della metrica {metric_name}: {e}")
            results[metric_name.upper()] = 0.0 
    return results

def plot_radar_chart(metrics_data, output_path):
    # Estrai le etichette delle metriche dal primo modello (assumendo che tutti abbiano le stesse metriche)
    # e assicurati che siano in un ordine consistente per il grafico.
    # Ordine desiderato per il grafico radar: BLEU-1, BLEU-2, BLEU-3, BLEU-4, METEOR, CIDEr, ROUGE-L, CLIP SCORE
    
    fixed_label_order = ["BLEU-1", "BLEU-2", "BLEU-3", "BLEU-4", "METEOR", "CIDEr", "ROUGE-L", "CLIP SCORE"]
    
    # Filtra le etichette presenti nei dati
    if not metrics_data:
        logger.warning("Nessun dato per il grafico radar.")
        return
        
    # Prendi le etichette dal primo modello come fallback, ma usa fixed_label_order se possibile
    sample_labels = list(metrics_data[list(metrics_data.keys())[0]].keys())
    
    labels_for_plot = [label for label in fixed_label_order if label in sample_labels]
    # Aggiungi eventuali altre metriche trovate che non erano nell'ordine fisso (improbabile con la logica attuale)
    for label in sample_labels:
        if label not in labels_for_plot:
            labels_for_plot.append(label)

    if not labels_for_plot:
        logger.warning("Nessuna etichetta di metrica trovata per il grafico radar.")
        return

    num_vars = len(labels_for_plot)
    angles = np.linspace(0, 2 * np.pi, num_vars, endpoint=False).tolist()
    angles += angles[:1] 

    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(polar=True)) # Aumentata dimensione per più etichette

    for model_name, scores_dict in metrics_data.items():
        values = [scores_dict.get(label, 0.0) for label in labels_for_plot] 
        values += values[:1] 
        ax.plot(angles, values, linewidth=2, linestyle='solid', label=model_name)
        ax.fill(angles, values, alpha=0.25)

    # Impostazioni assi e etichette
    ax.set_yticks([]) # Nascondere i tick radiali di default
    
    # Calcola dinamicamente i tick radiali e le etichette
    max_val_overall = 0
    for scores_dict in metrics_data.values():
        for label in labels_for_plot:
            val = scores_dict.get(label, 0.0)
            if val > max_val_overall:
                max_val_overall = val
    
    # Determina i tick radiali. Esempio: 5 ticks. Potrebbe essere necessario aggiustare.
    # CIDEr e CLIP Score possono avere scale diverse. BLEU/METEOR sono ~0-1. CLIP Score ~0-100 (se non normalizzato) o ~0-0.4.
    # L'output di 'evaluate' per 'clip_score' è solitamente su una scala più piccola (es. 0.2-0.4).
    # Se max_val_overall è piccolo (es. <=1), usiamo step più piccoli.
    # Se è più grande (es. CIDEr > 1), adattiamo.
    # Per ora, manteniamo la logica semplice e vediamo il grafico.
    # Il grafico originale fornito ha una scala fino a 1.0, con tick 0.2, 0.4, 0.6, 0.8, 1.0.
    # Replicare questo stile se possibile, ma CLIP Score potrebbe richiedere una scala diversa.
    # Il grafico dell'utente sembra avere tutte le metriche normalizzate visivamente tra 0 e 1.
    # Forziamo la scala a 0-1 per ora per coerenza con il grafico esempio.
    # Se una metrica (es. CIDEr) supera 1, sarà "fuori scala" nel grafico.
    # Altrimenti, dovremmo normalizzare tutte le metriche prima del plot o usare scale separate.
    
    y_ticks_values = np.array([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticks(y_ticks_values)
    ax.set_yticklabels([f"{val:.1f}" for val in y_ticks_values])
    ax.set_ylim(0, 1.05) # Un po' di buffer sopra 1.0


    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(labels_for_plot, fontsize=10) # Aggiustata dimensione font
    
    plt.title("Confronto Metriche di Valutazione - Captioner Esterni", size=18, y=1.12) # Aumentata dimensione e y
    ax.legend(loc='lower center', bbox_to_anchor=(0.5, -0.20), ncol=len(metrics_data)) # Spostata legenda in basso
    plt.tight_layout() # Per evitare sovrapposizioni
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    logger.info(f"Grafico radar salvato in {output_path}")


def main(args):
    if not all([AutoProcessor, AutoModelForCausalLM, AutoModelForVision2Seq, pipeline, evaluate, svg2png]):
        logger.error("Alcune dipendenze non sono state caricate correttamente. Uscita.")
        return

    os.makedirs(args.output_dir, exist_ok=True)

    logger.info(f"Caricamento del dataset da {args.dataset_path}...")
    try:
        with open(args.dataset_path, 'r', encoding='utf-8') as f:
            dataset_full = json.load(f)
    except Exception as e:
        logger.error(f"Impossibile caricare il dataset: {e}")
        return

    # Applica sample_size se specificato
    dataset_to_process = dataset_full
    if args.sample_size and args.sample_size > 0 and args.sample_size < len(dataset_full):
        logger.info(f"Utilizzo di un campione di {args.sample_size} elementi dal dataset.")
        dataset_to_process = dataset_full[:args.sample_size]
    
    # Creazione di un lookup per gli item del dataset per ID per una facile ri-rasterizzazione
    dataset_item_lookup = {item.get("id", f"image_{idx}"): item for idx, item in enumerate(dataset_full)}

    # Inizializza i risultati per ogni modello
    all_results = {}
    all_predictions = {}
    all_references = []
    all_images = []

    # Rasterizza tutte le immagini una volta sola
    logger.info("Rasterizzazione delle immagini SVG...")
    rasterized_images = {}
    for item in tqdm(dataset_to_process, desc="Rasterizzazione"):
        svg_data = item.get("svg_xml", "")
        if svg_data:
            image = rasterize_svg(svg_data, dpi=args.dpi_rasterization)
            if image:
                rasterized_images[item.get("id", f"image_{len(rasterized_images)}")] = image

    # Processa ogni modello
    for model_name in MODEL_CONFIG.keys():
        logger.info(f"\nValutazione del modello {model_name}...")
        try:
            # Carica il modello
            generate_caption = load_model(model_name)
            
            # Inizializza liste per questo modello
            model_predictions = []
            model_references = []
            model_images = []
            
            # Processa in batch
            batch_size = args.batch_size if hasattr(args, 'batch_size') else 1
            num_workers = args.num_workers if hasattr(args, 'num_workers') else 1
            
            for i in range(0, len(dataset_to_process), batch_size):
                batch = dataset_to_process[i:i + batch_size]
                batch_images = []
                batch_references = []
                
                for item in batch:
                    item_id = item.get("id", f"image_{i}")
                    if item_id in rasterized_images:
                        batch_images.append(rasterized_images[item_id])
                        batch_references.append(item.get("reference_caption", ""))
                
                if batch_images:
                    # Genera caption per il batch
                    try:
                        batch_predictions = generate_caption(batch_images)
                        if isinstance(batch_predictions, str):
                            batch_predictions = [batch_predictions]
                        
                        model_predictions.extend(batch_predictions)
                        model_references.extend(batch_references)
                        model_images.extend(batch_images)
                    except Exception as e:
                        logger.error(f"Errore durante la generazione delle caption per il batch {i}: {e}")
                        continue

            # Calcola le metriche
            if model_predictions and model_references:
                metrics = calculate_metrics(
                    predictions=model_predictions,
                    references=model_references,
                    pil_images=model_images if args.compute_clip_score else None
                )
                all_results[model_name] = metrics
                all_predictions[model_name] = model_predictions
                
                # Salva le predizioni
                predictions_file = os.path.join(args.output_dir, f"{model_name}_predictions.jsonl")
                with open(predictions_file, 'w', encoding='utf-8') as f:
                    for pred, ref in zip(model_predictions, model_references):
                        f.write(json.dumps({"prediction": pred, "reference": ref}) + "\n")

                # Salva le metriche
                metrics_file = os.path.join(args.output_dir, f"{model_name}_metrics.json")
                with open(metrics_file, 'w', encoding='utf-8') as f:
                    json.dump(metrics, f, indent=2)
                
                logger.info(f"Risultati per {model_name} salvati in {metrics_file}")
            else:
                logger.warning(f"Nessuna predizione generata per {model_name}")

        except Exception as e:
            logger.error(f"Errore durante la valutazione di {model_name}: {e}")
            continue

    # Genera il grafico radar se ci sono risultati
    if all_results:
        radar_chart_path = os.path.join(args.output_dir, "metrics_radar_chart.png")
        plot_radar_chart(all_results, radar_chart_path)
    else:
        logger.warning("Nessun risultato disponibile per generare il grafico radar.")

    logger.info("Valutazione completata.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Valutazione dei captioner esterni su immagini SVG")
    parser.add_argument("--dataset_path", required=True, help="Percorso al file JSON del dataset")
    parser.add_argument("--output_dir", required=True, help="Directory di output per i risultati")
    parser.add_argument("--sample_size", type=int, help="Numero di elementi da processare (opzionale)")
    parser.add_argument("--dpi_rasterization", type=int, default=96, help="DPI per la rasterizzazione SVG")
    parser.add_argument("--compute_clip_score", action="store_true", help="Calcola il CLIP score")
    parser.add_argument("--batch_size", type=int, default=1, help="Dimensione del batch per l'inferenza")
    parser.add_argument("--num_workers", type=int, default=1, help="Numero di worker per il processing")
    
    args = parser.parse_args()
    main(args) 