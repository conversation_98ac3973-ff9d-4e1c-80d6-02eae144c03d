#!/usr/bin/env python3
"""
CLIP Evaluation con SVG riparati (compatibile con baseline)
"""

import os
import json
import torch
import clip
import argparse
from PIL import Image
import numpy as np
from tqdm import tqdm
import logging
import cairosvg
import io
import re
import signal
from contextlib import contextmanager

# Configurazione logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@contextmanager
def timeout_context(seconds):
    """Context manager per timeout"""
    def timeout_handler(signum, frame):
        raise TimeoutError(f"Operazione timeout dopo {seconds}s")

    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(seconds)
    try:
        yield
    finally:
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)

def fix_malformed_svg(svg_data):
    """Ripara SVG malformati (stesso del baseline)"""
    try:
        # Fix path malformati
        svg_data = re.sub(r'd="d=', 'd="', svg_data)
        # Fix colori
        svg_data = re.sub(r'fill:(\d+),(\d+),(\d+)', r'fill:rgb(\1,\2,\3)', svg_data)
        svg_data = re.sub(r'stroke:(\d+),(\d+),(\d+)', r'stroke:rgb(\1,\2,\3)', svg_data)
        # Rimuovi stroke None
        svg_data = re.sub(r';stroke:None', '', svg_data)
        svg_data = re.sub(r'stroke:None;', '', svg_data)
        # Namespace
        if 'xmlns=' not in svg_data:
            svg_data = svg_data.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"')
        # Self-closing tags
        svg_data = re.sub(r'<path([^>]*[^/])>', r'<path\1 />', svg_data)
        return svg_data
    except Exception as e:
        logger.warning(f"Errore fix SVG: {e}")
        return svg_data

def convert_rgba_to_rgb(image):
    """Converte RGBA a RGB"""
    if image.mode == 'RGBA':
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3])
        return bg
    elif image.mode == 'P':
        image = image.convert('RGBA')
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3] if len(image.split()) == 4 else None)
        return bg
    else:
        return image.convert('RGB')

def rasterize_svg_fixed(svg_data, dpi=150, timeout_sec=3):
    """Converte SVG riparato in immagine"""
    try:
        fixed_svg = fix_malformed_svg(svg_data)
        if len(fixed_svg) > 50000:
            return None

        with timeout_context(timeout_sec):
            png_data = cairosvg.svg2png(
                bytestring=fixed_svg.encode('utf-8'),
                dpi=dpi,
                output_width=224,
                output_height=224
            )
            image = Image.open(io.BytesIO(png_data))
            return convert_rgba_to_rgb(image)
    except:
        return None

def load_jsonl(file_path):
    """Carica file JSONL se esiste"""
    if not os.path.exists(file_path):
        logger.warning(f"File non trovato: {file_path}")
        return None

    data = []
    with open(file_path, 'r') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def load_json(file_path):
    """Carica file JSON"""
    with open(file_path, 'r') as f:
        return json.load(f)

def calculate_clip_score_svg(svg_data, text, model, preprocess, device):
    """Calcola CLIP score tra SVG e testo"""
    try:
        # Rasterizza SVG
        image = rasterize_svg_fixed(svg_data)
        if image is None:
            return 0.0

        # Preprocessa immagine
        image_input = preprocess(image).unsqueeze(0).to(device)

        # Tokenizza testo
        text_input = clip.tokenize([text[:77]]).to(device)  # CLIP limit

        # Calcola features
        with torch.no_grad():
            image_features = model.encode_image(image_input)
            text_features = model.encode_text(text_input)

            # Normalizza
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)

            # Similarità
            similarity = (100.0 * image_features @ text_features.T).item()

        return similarity
    except Exception as e:
        logger.warning(f"Errore CLIP score: {str(e)[:50]}")
        return 0.0

def main():
    parser = argparse.ArgumentParser(description='CLIP evaluation con SVG riparati')
    parser.add_argument('--llama_results', type=str, help='File JSONL risultati Llama')
    parser.add_argument('--gemma_results', type=str, help='File JSONL risultati Gemma')
    parser.add_argument('--reference_file', type=str, required=True, help='File JSON dati riferimento')
    parser.add_argument('--output_file', type=str, required=True, help='File output CLIP scores')
    parser.add_argument('--device', type=str, default='auto', choices=['auto', 'cuda', 'cpu'])

    args = parser.parse_args()

    # Setup device
    if args.device == 'auto':
        device = "cuda" if torch.cuda.is_available() else "cpu"
    else:
        device = args.device
    logger.info(f"Device: {device}")

    # Carica modello CLIP con fix per timeout
    logger.info("Caricamento CLIP con fix...")
    try:
        with timeout_context(30):
            # FIX: jit=False risolve il problema di timeout!
            model, preprocess = clip.load("ViT-B/32", device=device, jit=False)
            logger.info("✅ CLIP caricato con successo!")
    except Exception as e:
        logger.error(f"❌ Errore caricamento CLIP: {e}")
        raise

    # Carica dati
    logger.info("Caricamento dati...")
    llama_results = load_jsonl(args.llama_results) if args.llama_results else None
    gemma_results = load_jsonl(args.gemma_results) if args.gemma_results else None
    reference_data = load_json(args.reference_file)

    if llama_results is None and gemma_results is None:
        logger.error("Nessun file di risultati trovato!")
        return

    # Crea dizionario riferimenti
    reference_dict = {item['id']: item for item in reference_data}

    # Determina quale dataset usare
    if llama_results and gemma_results:
        logger.info("Valutazione ENTRAMBI i modelli")
        dataset_size = min(len(llama_results), len(gemma_results))
        eval_both = True
    elif llama_results:
        logger.info("Valutazione SOLO Llama")
        dataset_size = len(llama_results)
        eval_both = False
    else:
        logger.info("Valutazione SOLO Gemma")
        dataset_size = len(gemma_results)
        eval_both = False

    # Calcola CLIP scores
    logger.info(f"Calcolo CLIP scores per {dataset_size} esempi...")
    results = {}

    if llama_results:
        results['llama'] = {'scores': [], 'mean': 0.0, 'count': 0}
    if gemma_results:
        results['gemma'] = {'scores': [], 'mean': 0.0, 'count': 0}

    for i in tqdm(range(dataset_size), desc="CLIP Evaluation"):
        try:
            # Determina ID esempio
            if eval_both:
                item_id = llama_results[i]['id']
            elif llama_results:
                item_id = llama_results[i]['id']
            else:
                item_id = gemma_results[i]['id']

            if item_id not in reference_dict:
                logger.warning(f"ID {item_id} non trovato nei riferimenti")
                continue

            reference = reference_dict[item_id]
            svg_data = reference.get('xml') or reference.get('svg_xml')

            if not svg_data:
                logger.warning(f"SVG non trovato per {item_id}")
                continue

            # Valuta Llama
            if llama_results and i < len(llama_results):
                llama_caption = llama_results[i].get('generated_caption', '')
                if llama_caption:
                    llama_score = calculate_clip_score_svg(svg_data, llama_caption, model, preprocess, device)
                    results['llama']['scores'].append(llama_score)
                    results['llama']['count'] += 1

            # Valuta Gemma
            if gemma_results and i < len(gemma_results):
                gemma_caption = gemma_results[i].get('generated_caption', '')
                if gemma_caption:
                    gemma_score = calculate_clip_score_svg(svg_data, gemma_caption, model, preprocess, device)
                    results['gemma']['scores'].append(gemma_score)
                    results['gemma']['count'] += 1

        except Exception as e:
            logger.error(f"Errore esempio {i}: {e}")
            continue

    # Calcola medie
    for model_name in results:
        if results[model_name]['scores']:
            results[model_name]['mean'] = np.mean(results[model_name]['scores'])
            logger.info(f"{model_name.upper()}: {results[model_name]['mean']:.2f} (n={results[model_name]['count']})")
        else:
            logger.warning(f"Nessun score calcolato per {model_name}")

    # Salva risultati
    logger.info("Salvataggio risultati...")
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info("🎉 CLIP evaluation completata!")

if __name__ == "__main__":
    main()
