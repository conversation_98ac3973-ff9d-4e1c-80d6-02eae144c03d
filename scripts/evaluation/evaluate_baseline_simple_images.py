#!/usr/bin/env python3
"""
Baseline evaluation con immagini semplici generate (test funzionalità)
"""

import argparse
import json
import os
from pathlib import Path
from tqdm import tqdm
import torch
from PIL import Image, ImageDraw
import logging
import time

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_simple_test_images(num_images=10):
    """Crea immagini di test semplici"""
    images = []
    captions = []
    
    for i in range(num_images):
        # Crea immagine 224x224
        img = Image.new('RGB', (224, 224), color='white')
        draw = ImageDraw.Draw(img)
        
        if i % 4 == 0:
            # Cerchio rosso
            draw.ellipse([50, 50, 174, 174], fill='red')
            caption = "a red circle"
        elif i % 4 == 1:
            # Quadrato blu
            draw.rectangle([50, 50, 174, 174], fill='blue')
            caption = "a blue square"
        elif i % 4 == 2:
            # Triangolo verde (approssimato)
            draw.polygon([(112, 50), (50, 174), (174, 174)], fill='green')
            caption = "a green triangle"
        else:
            # Linee gialle
            draw.line([(0, 0), (224, 224)], fill='yellow', width=10)
            draw.line([(0, 224), (224, 0)], fill='yellow', width=10)
            caption = "yellow lines crossing"
        
        images.append(img)
        captions.append(caption)
    
    return images, captions

def create_pipeline_safe(model_name):
    """Crea pipeline con gestione errori"""
    try:
        from transformers import pipeline
        logger.info(f"Creazione pipeline {model_name}...")
        
        if model_name == "idefix":
            return pipeline("image-to-text",
                          model="microsoft/git-base",
                          device=-1)
        elif model_name == "flores2":
            return pipeline("image-to-text",
                          model="nlpconnect/vit-gpt2-image-captioning",
                          device=-1)
        elif model_name == "blip2":
            return pipeline("image-to-text",
                          model="Salesforce/blip-image-captioning-base",
                          device=-1)
        else:
            return None
            
    except Exception as e:
        logger.error(f"Errore pipeline {model_name}: {e}")
        return None

def evaluate_model_simple(model_name, output_dir):
    """Valuta modello con immagini semplici"""
    logger.info(f"Valutazione modello {model_name} con immagini semplici...")
    
    start_time = time.time()
    
    # Crea pipeline
    captioner = create_pipeline_safe(model_name)
    if captioner is None:
        return None
    
    pipeline_time = time.time() - start_time
    logger.info(f"Pipeline {model_name} creata in {pipeline_time:.1f}s")
    
    # Crea immagini di test
    logger.info("Creazione immagini di test...")
    images, reference_captions = create_simple_test_images(10)
    
    results_data = []
    
    # Processa le immagini
    for i, (image, ref_caption) in enumerate(tqdm(zip(images, reference_captions), desc=f"Test {model_name}")):
        try:
            # Genera caption
            start_inference = time.time()
            result = captioner(image)
            inference_time = time.time() - start_inference
            
            generated_caption = result[0]['generated_text'] if result else ""
            
            logger.info(f"Esempio {i}: {inference_time:.2f}s")
            logger.info(f"  Reference: '{ref_caption}'")
            logger.info(f"  Generated: '{generated_caption}'")
            
            # Salva risultati
            results_data.append({
                'id': f'simple_test_{i}',
                'reference': ref_caption,
                'generated': generated_caption,
                'inference_time': inference_time
            })
                
        except Exception as e:
            logger.error(f"Errore processando immagine {i}: {e}")
            continue

    # Salva risultati
    output_path = Path(output_dir) / f"baseline_{model_name}_simple"
    output_path.mkdir(parents=True, exist_ok=True)

    with open(output_path / 'predictions.jsonl', 'w') as f:
        for pred in results_data:
            f.write(json.dumps(pred) + '\n')
    
    # Salva immagini di test
    test_images_dir = output_path / 'test_images'
    test_images_dir.mkdir(exist_ok=True)
    for i, image in enumerate(images):
        image.save(test_images_dir / f'test_image_{i}.png')

    total_time = time.time() - start_time
    avg_inference = sum(r['inference_time'] for r in results_data) / len(results_data)
    
    logger.info(f"Risultati salvati in {output_path}")
    logger.info(f"Processate {len(results_data)} immagini in {total_time:.1f}s")
    logger.info(f"Tempo medio inferenza: {avg_inference:.2f}s")
    
    return results_data

def main():
    parser = argparse.ArgumentParser(description='Baseline test con immagini semplici')
    parser.add_argument('--output_dir', required=True)
    parser.add_argument('--models', nargs='+', choices=['idefix', 'flores2', 'blip2'], default=['idefix'])

    args = parser.parse_args()

    # Verifica versione
    import transformers
    logger.info(f"🚀 Transformers: {transformers.__version__}")

    # Valuta modelli
    for model_name in args.models:
        logger.info(f"Inizio test {model_name} con immagini semplici")
        
        try:
            results = evaluate_model_simple(model_name, args.output_dir)
            if results:
                logger.info(f"✅ {model_name} completato: {len(results)} immagini")
            else:
                logger.error(f"❌ {model_name} fallito")
        except Exception as e:
            logger.error(f"Errore test {model_name}: {e}")

    logger.info("🎉 Test baseline con immagini semplici completato!")

if __name__ == '__main__':
    main()
