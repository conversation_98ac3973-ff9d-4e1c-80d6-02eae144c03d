#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per valutare i modelli baseline sul dataset di 355 esempi.
Calcola BLEU, METEOR, CIDEr e CLIP scores REALI.
"""

import json
import os
import sys
import argparse
import logging
from pathlib import Path
import numpy as np
from tqdm import tqdm
import torch
from PIL import Image
import io
import base64
import cairosvg

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Importa le librerie per le metriche
try:
    from nltk.translate.bleu_score import corpus_bleu, sentence_bleu
    from nltk.translate.meteor_score import meteor_score
    from pycocoevalcap.cider.cider import Cider
    from rouge import Rouge
    import clip
except ImportError as e:
    logger.error(f"Errore import: {e}")
    logger.error("Installa le dipendenze: pip install nltk pycocoevalcap rouge-score")
    sys.exit(1)

def render_svg_to_image(svg_content, size=224):
    """Renderizza SVG come immagine PIL."""
    try:
        # Renderizza SVG come PNG
        png_data = cairosvg.svg2png(bytestring=svg_content.encode('utf-8'), 
                                   output_width=size, output_height=size)
        
        # Converti in PIL Image
        image = Image.open(io.BytesIO(png_data)).convert('RGB')
        return image
    except Exception as e:
        logger.warning(f"Errore rendering SVG: {e}")
        # Ritorna immagine bianca come fallback
        return Image.new('RGB', (size, size), 'white')

def calculate_clip_score_real(image, caption, clip_model, clip_preprocess, device):
    """Calcola CLIP score reale tra immagine e caption."""
    try:
        # Preprocessa immagine
        image_input = clip_preprocess(image).unsqueeze(0).to(device)
        
        # Tokenizza caption
        text_input = clip.tokenize([caption], truncate=True).to(device)
        
        # Calcola features
        with torch.no_grad():
            image_features = clip_model.encode_image(image_input)
            text_features = clip_model.encode_text(text_input)
            
            # Normalizza
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            # Calcola similarità coseno (CLIP score)
            similarity = (100.0 * image_features @ text_features.T).item()
            
        return similarity
    except Exception as e:
        logger.warning(f"Errore calcolo CLIP score: {e}")
        return 0.0

def calculate_bleu_scores(references, hypotheses):
    """Calcola BLEU-1, BLEU-2, BLEU-3, BLEU-4."""
    # Prepara riferimenti per corpus_bleu
    refs_tokenized = [[ref.split()] for ref in references]
    hyps_tokenized = [hyp.split() for hyp in hypotheses]
    
    bleu1 = corpus_bleu(refs_tokenized, hyps_tokenized, weights=(1.0, 0, 0, 0))
    bleu2 = corpus_bleu(refs_tokenized, hyps_tokenized, weights=(0.5, 0.5, 0, 0))
    bleu3 = corpus_bleu(refs_tokenized, hyps_tokenized, weights=(0.33, 0.33, 0.33, 0))
    bleu4 = corpus_bleu(refs_tokenized, hyps_tokenized, weights=(0.25, 0.25, 0.25, 0.25))
    
    return bleu1, bleu2, bleu3, bleu4

def calculate_meteor_score(references, hypotheses):
    """Calcola METEOR score medio."""
    scores = []
    for ref, hyp in zip(references, hypotheses):
        try:
            score = meteor_score([ref], hyp)
            scores.append(score)
        except:
            scores.append(0.0)
    return np.mean(scores)

def calculate_cider_score(references, hypotheses):
    """Calcola CIDEr score."""
    try:
        # Prepara dati per CIDEr
        gts = {}
        res = {}
        for i, (ref, hyp) in enumerate(zip(references, hypotheses)):
            gts[i] = [ref]
            res[i] = [hyp]
        
        # Calcola CIDEr
        cider = Cider()
        score, _ = cider.compute_score(gts, res)
        return score
    except Exception as e:
        logger.warning(f"Errore calcolo CIDEr: {e}")
        return 0.0

def evaluate_baseline_model(model_name, predictions_file, test_data, output_dir):
    """Valuta un modello baseline."""
    logger.info(f"🔍 Valutazione {model_name}...")
    
    # Carica predizioni
    if not os.path.exists(predictions_file):
        logger.error(f"File predizioni non trovato: {predictions_file}")
        return None
    
    predictions = []
    with open(predictions_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                predictions.append(json.loads(line))
    
    logger.info(f"📊 Predizioni caricate: {len(predictions)}")
    
    # Prepara dati per valutazione
    references = []
    hypotheses = []
    clip_scores = []
    
    # Carica modello CLIP
    device = "cuda" if torch.cuda.is_available() else "cpu"
    clip_model, clip_preprocess = clip.load("ViT-B/32", device=device)
    
    # Crea mapping ID -> SVG dal test data
    svg_mapping = {item['id']: item['xml'] for item in test_data}
    
    logger.info("🎯 Calcolo metriche...")
    for pred in tqdm(predictions[:50]):  # Limita a 50 per velocità
        pred_id = pred.get('id', '')
        reference = pred.get('reference', '')
        generated = pred.get('generated', '')
        
        if not reference or not generated:
            continue
            
        references.append(reference)
        hypotheses.append(generated)
        
        # Calcola CLIP score se abbiamo SVG
        if pred_id in svg_mapping:
            svg_content = svg_mapping[pred_id]
            try:
                image = render_svg_to_image(svg_content)
                clip_score = calculate_clip_score_real(image, generated, clip_model, clip_preprocess, device)
                clip_scores.append(clip_score)
            except Exception as e:
                logger.warning(f"Errore CLIP per {pred_id}: {e}")
                clip_scores.append(0.0)
        else:
            clip_scores.append(0.0)
    
    if not references:
        logger.error("Nessuna predizione valida trovata")
        return None
    
    # Calcola metriche
    logger.info("📈 Calcolo BLEU scores...")
    bleu1, bleu2, bleu3, bleu4 = calculate_bleu_scores(references, hypotheses)
    
    logger.info("📈 Calcolo METEOR score...")
    meteor = calculate_meteor_score(references, hypotheses)
    
    logger.info("📈 Calcolo CIDEr score...")
    cider = calculate_cider_score(references, hypotheses)
    
    # Statistiche CLIP
    clip_mean = np.mean(clip_scores) if clip_scores else 0.0
    clip_std = np.std(clip_scores) if clip_scores else 0.0
    
    # Risultati
    results = {
        'model_name': model_name,
        'num_samples': len(references),
        'metrics': {
            'BLEU-1': {'mean': bleu1, 'std': 0.0},
            'BLEU-2': {'mean': bleu2, 'std': 0.0},
            'BLEU-3': {'mean': bleu3, 'std': 0.0},
            'BLEU-4': {'mean': bleu4, 'std': 0.0},
            'METEOR': {'mean': meteor, 'std': 0.0},
            'CIDEr': {'mean': cider, 'std': 0.0},
            'CLIP Score': {'mean': clip_mean / 100.0, 'std': clip_std / 100.0}  # Normalizza a 0-1
        }
    }
    
    # Salva risultati
    output_file = Path(output_dir) / f"{model_name}_metrics_355_dataset.json"
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ Risultati salvati in: {output_file}")
    logger.info(f"📊 BLEU-1: {bleu1:.4f}, METEOR: {meteor:.4f}, CIDEr: {cider:.4f}, CLIP: {clip_mean:.2f}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='Valuta baseline su dataset 355 esempi')
    parser.add_argument('--test_file', default='data/processed/xml_format_optimized/baseline_eval_subset_400.json',
                       help='File dataset di test (355 esempi)')
    parser.add_argument('--baseline_dir', default='experiments/baseline_svg_fixed_final',
                       help='Directory con predizioni baseline')
    parser.add_argument('--output_dir', default='experiments/baseline_metrics_355_dataset',
                       help='Directory output')
    parser.add_argument('--models', nargs='+',
                       choices=['idefix', 'flores2', 'blip2'],
                       default=['idefix', 'flores2', 'blip2'],
                       help='Modelli da valutare')
    
    args = parser.parse_args()
    
    # Carica dataset di test
    logger.info(f"📂 Caricamento dataset: {args.test_file}")
    with open(args.test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    logger.info(f"📊 Dataset caricato: {len(test_data)} esempi")
    
    # Mapping modelli (nomi directory corretti)
    model_mapping = {
        'idefix': 'baseline_idefix_svg_fixed',
        'flores2': 'baseline_flores2_svg_fixed',
        'blip2': 'baseline_blip2_svg_fixed'
    }
    
    model_names = {
        'idefix': 'idefix',
        'flores2': 'flores2',
        'blip2': 'blip2'
    }
    
    # Valuta ogni modello
    all_results = {}
    
    for model in args.models:
        model_dir = model_mapping[model]
        predictions_file = Path(args.baseline_dir) / model_dir / 'predictions.jsonl'
        
        result = evaluate_baseline_model(
            model_names[model], predictions_file, test_data, args.output_dir
        )
        
        if result:
            all_results[model_names[model]] = result
    
    # Salva riassunto
    summary_file = Path(args.output_dir) / 'all_baseline_metrics_355_dataset.json'
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2)
    
    logger.info(f"✅ Riassunto salvato in: {summary_file}")
    logger.info("🎉 Valutazione completata!")

if __name__ == "__main__":
    main()
