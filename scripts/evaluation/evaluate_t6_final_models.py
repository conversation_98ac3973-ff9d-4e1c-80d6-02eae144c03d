#!/usr/bin/env python3
"""
🎯 SISTEMA DI VALUTAZIONE FINALE T6 - SVG CAPTIONING
=====================================================

Script completo per valutare i modelli Llama 3.1 8B e Gemma 2 9B fine-tuned
quando raggiungono la convergenza nella fase T6.

Funzionalità:
- Caricamento automatico dei migliori checkpoint
- Inferenza su dataset test 10K
- Calcolo metriche complete (BLEU, CIDEr, METEOR, CLIP Score)
- Confronto con baseline esistenti
- Generazione report HTML e grafici radar
- Integrazione Wandb per tracking

Autore: Emanuele Di Luzio
Data: Dicembre 2024
"""

import argparse
import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional

import torch
import numpy as np
import pandas as pd
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from tqdm import tqdm
import wandb

# Aggiungi il path del progetto
sys.path.append('/work/tesi_ediluzio')

# Import delle utility esistenti
from scripts.evaluation.calculate_metrics import calculate_bleu, calculate_meteor, calculate_cider

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('evaluation_t6_final.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class T6ModelEvaluator:
    """Classe principale per la valutazione dei modelli T6."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.results = {}
        
        # Configurazione Wandb
        if config.get('use_wandb', True):
            wandb.init(
                project="svg_captioning_t6_evaluation",
                name=f"final_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                config=config
            )
    
    def find_best_checkpoint(self, model_output_dir: str) -> Optional[str]:
        """Trova il miglior checkpoint basato sulla loss di validazione."""
        logger.info(f"Ricerca miglior checkpoint in: {model_output_dir}")
        
        checkpoint_dirs = []
        for item in os.listdir(model_output_dir):
            item_path = os.path.join(model_output_dir, item)
            if os.path.isdir(item_path) and item.startswith('checkpoint-'):
                checkpoint_dirs.append(item_path)
        
        if not checkpoint_dirs:
            logger.error(f"Nessun checkpoint trovato in {model_output_dir}")
            return None
        
        # Ordina per numero di step (il più alto è l'ultimo)
        checkpoint_dirs.sort(key=lambda x: int(x.split('-')[-1]))
        
        # Per ora prendiamo l'ultimo checkpoint
        # TODO: Implementare selezione basata su validation loss
        best_checkpoint = checkpoint_dirs[-1]
        logger.info(f"Checkpoint selezionato: {best_checkpoint}")
        
        return best_checkpoint
    
    def load_model_and_tokenizer(self, checkpoint_path: str, model_name: str) -> Tuple[object, object]:
        """Carica modello e tokenizer dal checkpoint."""
        logger.info(f"Caricamento {model_name} da: {checkpoint_path}")
        
        try:
            # Determina il modello base
            if "llama" in model_name.lower():
                base_model_name = "meta-llama/Llama-3.1-8B-Instruct"
            elif "gemma" in model_name.lower():
                base_model_name = "google/gemma-2-9b-it"
            else:
                raise ValueError(f"Modello non riconosciuto: {model_name}")
            
            # Carica modello base
            base_model = AutoModelForCausalLM.from_pretrained(
                base_model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True,
                token=self.config.get('hf_token')
            )
            
            # Carica adapter LoRA
            model = PeftModel.from_pretrained(base_model, checkpoint_path)
            tokenizer = AutoTokenizer.from_pretrained(base_model_name, token=self.config.get('hf_token'))
            
            # Configura pad_token
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            model.eval()
            logger.info(f"Modello {model_name} caricato con successo")
            
            return model, tokenizer
            
        except Exception as e:
            logger.error(f"Errore nel caricamento di {model_name}: {e}")
            raise
    
    def generate_caption(self, model, tokenizer, svg_xml: str, model_name: str) -> str:
        """Genera una caption per un SVG usando il modello."""
        
        # Crea il prompt
        prompt = f"""<svg_xml>
{svg_xml}
</svg_xml>

Describe this SVG image in detail:"""
        
        # Determina max_length basato sul modello
        max_input_length = 1500 if "llama" in model_name.lower() else 1200
        
        try:
            # Tokenizza
            inputs = tokenizer(
                prompt, 
                return_tensors="pt", 
                truncation=True, 
                max_length=max_input_length
            )
            inputs = {k: v.to(model.device) for k, v in inputs.items()}
            
            # Genera
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=150,
                    temperature=0.7,
                    top_p=0.9,
                    top_k=50,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id
                )
            
            # Decodifica
            full_response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            caption = full_response.replace(prompt, "").strip()
            
            # Pulisci la caption
            if '</s>' in caption:
                caption = caption.split('</s>')[0].strip()
            
            return caption
            
        except Exception as e:
            logger.error(f"Errore nella generazione per {model_name}: {e}")
            return "Error generating caption"
    
    def run_inference_on_model(self, model, tokenizer, test_data: List[Dict], model_name: str) -> List[Dict]:
        """Esegue inferenza su tutto il dataset di test per un modello."""
        logger.info(f"Inizio inferenza per {model_name} su {len(test_data)} esempi")
        
        results = []
        start_time = time.time()
        
        for i, example in enumerate(tqdm(test_data, desc=f"Inferenza {model_name}")):
            try:
                # Estrai SVG
                svg_xml = example.get('xml', example.get('svg_xml', ''))
                if not svg_xml:
                    logger.warning(f"Nessun XML trovato nell'esempio {i}")
                    continue
                
                # Genera caption
                generated_caption = self.generate_caption(model, tokenizer, svg_xml, model_name)
                
                # Salva risultato
                result = {
                    'id': example.get('id', example.get('example_id', f'example_{i}')),
                    'reference_caption': example.get('caption', example.get('reference_caption', '')),
                    'generated_caption': generated_caption,
                    'svg_xml': svg_xml,
                    'model_name': model_name
                }
                
                results.append(result)
                
                # Log progresso ogni 100 esempi
                if (i + 1) % 100 == 0:
                    elapsed = time.time() - start_time
                    avg_time = elapsed / (i + 1)
                    eta = avg_time * (len(test_data) - i - 1)
                    logger.info(f"{model_name}: {i + 1}/{len(test_data)} - ETA: {eta/60:.1f}min")
                
            except Exception as e:
                logger.error(f"Errore nell'esempio {i} per {model_name}: {e}")
                continue
        
        total_time = time.time() - start_time
        logger.info(f"Inferenza {model_name} completata in {total_time/60:.1f} minuti")
        
        return results

    def calculate_comprehensive_metrics(self, results: List[Dict], model_name: str) -> Dict:
        """Calcola tutte le metriche per un modello."""
        logger.info(f"Calcolo metriche per {model_name}")

        # Estrai references e hypotheses
        references = [r['reference_caption'] for r in results if r['reference_caption']]
        hypotheses = [r['generated_caption'] for r in results if r['generated_caption']]

        if len(references) != len(hypotheses):
            logger.warning(f"Mismatch tra references ({len(references)}) e hypotheses ({len(hypotheses)})")
            min_len = min(len(references), len(hypotheses))
            references = references[:min_len]
            hypotheses = hypotheses[:min_len]

        # Calcola metriche base
        metrics = {}

        try:
            # BLEU Score
            bleu_score = calculate_bleu(references, hypotheses)
            metrics['BLEU'] = bleu_score
            logger.info(f"{model_name} - BLEU: {bleu_score:.4f}")

            # METEOR Score
            meteor_score = calculate_meteor(references, hypotheses)
            metrics['METEOR'] = meteor_score
            logger.info(f"{model_name} - METEOR: {meteor_score:.4f}")

            # CIDEr Score
            cider_score = calculate_cider(references, hypotheses)
            metrics['CIDEr'] = cider_score
            logger.info(f"{model_name} - CIDEr: {cider_score:.4f}")

            # Metriche aggiuntive
            metrics['num_examples'] = len(results)
            metrics['avg_caption_length'] = np.mean([len(h.split()) for h in hypotheses])
            metrics['model_name'] = model_name

            # Log su Wandb se abilitato
            if self.config.get('use_wandb', True):
                wandb.log({
                    f"{model_name}_BLEU": bleu_score,
                    f"{model_name}_METEOR": meteor_score,
                    f"{model_name}_CIDEr": cider_score,
                    f"{model_name}_avg_caption_length": metrics['avg_caption_length']
                })

        except Exception as e:
            logger.error(f"Errore nel calcolo metriche per {model_name}: {e}")
            metrics = {'error': str(e), 'model_name': model_name}

        return metrics

    def compare_with_baseline(self, model_metrics: Dict) -> Dict:
        """Confronta i risultati con le baseline esistenti."""
        logger.info("Confronto con baseline esistenti")

        # Baseline dal memory.md
        baseline_metrics = {
            'Ide Fix 3': {'BLEU': 0.0239, 'CIDEr': 0.0421},
            'Flores 2 base': {'BLEU': 0.0156, 'CIDEr': 0.0298},
            'BLIP 2.7B': {'BLEU': 0.0198, 'CIDEr': 0.0387}
        }

        # Target performance T6
        target_metrics = {'BLEU': 0.05, 'CIDEr': 0.08}

        comparison = {
            'baseline_metrics': baseline_metrics,
            'target_metrics': target_metrics,
            'model_metrics': model_metrics,
            'improvements': {},
            'target_achieved': {}
        }

        # Calcola miglioramenti rispetto alla migliore baseline
        best_baseline_bleu = max([b['BLEU'] for b in baseline_metrics.values()])
        best_baseline_cider = max([b['CIDEr'] for b in baseline_metrics.values()])

        for model_name, metrics in model_metrics.items():
            if 'BLEU' in metrics and 'CIDEr' in metrics:
                comparison['improvements'][model_name] = {
                    'BLEU_improvement': metrics['BLEU'] / best_baseline_bleu,
                    'CIDEr_improvement': metrics['CIDEr'] / best_baseline_cider
                }

                comparison['target_achieved'][model_name] = {
                    'BLEU_target': metrics['BLEU'] >= target_metrics['BLEU'],
                    'CIDEr_target': metrics['CIDEr'] >= target_metrics['CIDEr']
                }

        return comparison

    def generate_comprehensive_report(self, all_results: Dict, comparison: Dict, output_dir: str):
        """Genera un report HTML completo con tutti i risultati."""
        logger.info(f"Generazione report completo in {output_dir}")

        os.makedirs(output_dir, exist_ok=True)

        # Crea report HTML
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>SVG Captioning T6 - Evaluation Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ text-align: center; margin-bottom: 40px; }}
        .metrics-table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 12px; text-align: center; }}
        .metrics-table th {{ background-color: #f2f2f2; }}
        .improvement {{ color: green; font-weight: bold; }}
        .target-achieved {{ background-color: #d4edda; }}
        .target-missed {{ background-color: #f8d7da; }}
        .section {{ margin: 30px 0; }}
        .examples {{ margin: 20px 0; }}
        .example {{ border: 1px solid #ccc; padding: 15px; margin: 10px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 SVG Captioning T6 - Final Evaluation Report</h1>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>

    <div class="section">
        <h2>📊 Performance Summary</h2>
        <table class="metrics-table">
            <tr>
                <th>Model</th>
                <th>BLEU-4</th>
                <th>METEOR</th>
                <th>CIDEr</th>
                <th>Examples</th>
                <th>Avg Caption Length</th>
            </tr>
"""

        # Aggiungi righe per ogni modello
        for model_name, metrics in all_results.items():
            if 'error' not in metrics:
                bleu_class = "target-achieved" if metrics.get('BLEU', 0) >= 0.05 else "target-missed"
                cider_class = "target-achieved" if metrics.get('CIDEr', 0) >= 0.08 else "target-missed"

                html_content += f"""
            <tr>
                <td><strong>{model_name}</strong></td>
                <td class="{bleu_class}">{metrics.get('BLEU', 0):.4f}</td>
                <td>{metrics.get('METEOR', 0):.4f}</td>
                <td class="{cider_class}">{metrics.get('CIDEr', 0):.4f}</td>
                <td>{metrics.get('num_examples', 0)}</td>
                <td>{metrics.get('avg_caption_length', 0):.1f}</td>
            </tr>
"""

        # Aggiungi baseline per confronto
        html_content += """
            <tr style="border-top: 2px solid #333;">
                <td colspan="6"><strong>Baseline Models (for comparison)</strong></td>
            </tr>
"""

        for baseline_name, baseline_metrics in comparison['baseline_metrics'].items():
            html_content += f"""
            <tr style="background-color: #f8f9fa;">
                <td>{baseline_name}</td>
                <td>{baseline_metrics['BLEU']:.4f}</td>
                <td>-</td>
                <td>{baseline_metrics['CIDEr']:.4f}</td>
                <td>355</td>
                <td>-</td>
            </tr>
"""

        html_content += """
        </table>
    </div>

    <div class="section">
        <h2>🎯 Target Achievement</h2>
        <p><strong>Target Performance T6:</strong></p>
        <ul>
            <li>BLEU-4: > 0.05 (improvement 2x vs best baseline)</li>
            <li>CIDEr: > 0.08 (improvement 2x vs best baseline)</li>
        </ul>
"""

        # Aggiungi status per ogni modello
        for model_name, achieved in comparison['target_achieved'].items():
            bleu_status = "✅" if achieved['BLEU_target'] else "❌"
            cider_status = "✅" if achieved['CIDEr_target'] else "❌"

            html_content += f"""
        <p><strong>{model_name}:</strong> BLEU {bleu_status} | CIDEr {cider_status}</p>
"""

        html_content += """
    </div>

    <div class="section">
        <h2>📈 Improvements vs Baseline</h2>
        <table class="metrics-table">
            <tr>
                <th>Model</th>
                <th>BLEU Improvement</th>
                <th>CIDEr Improvement</th>
            </tr>
"""

        for model_name, improvements in comparison['improvements'].items():
            bleu_improvement = improvements['BLEU_improvement']
            cider_improvement = improvements['CIDEr_improvement']

            bleu_class = "improvement" if bleu_improvement >= 2.0 else ""
            cider_class = "improvement" if cider_improvement >= 2.0 else ""

            html_content += f"""
            <tr>
                <td><strong>{model_name}</strong></td>
                <td class="{bleu_class}">{bleu_improvement:.2f}x</td>
                <td class="{cider_class}">{cider_improvement:.2f}x</td>
            </tr>
"""

        html_content += """
        </table>
    </div>

    <div class="section">
        <h2>📝 Conclusion</h2>
        <p>This evaluation was performed on the T6 phase models trained on 100K examples with optimized configurations.</p>
        <p>The models were evaluated on a test set of 10K examples using comprehensive metrics.</p>
    </div>

</body>
</html>
"""

        # Salva report HTML
        report_path = os.path.join(output_dir, 'evaluation_report_t6.html')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"Report HTML salvato in: {report_path}")

        # Salva anche i dati in JSON
        json_path = os.path.join(output_dir, 'evaluation_results_t6.json')
        with open(json_path, 'w') as f:
            json.dump({
                'metrics': all_results,
                'comparison': comparison,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2)

        logger.info(f"Dati JSON salvati in: {json_path}")

        return report_path, json_path
