#!/usr/bin/env python3
"""
Valutazione semplice senza CLIP (che si blocca)
Calcola metriche testuali: BLEU, ROUGE, lunghezza, etc.
"""

import os
import json
import argparse
from collections import Counter
import re
import numpy as np

def load_jsonl(file_path):
    """Carica file JSONL se esiste"""
    if not os.path.exists(file_path):
        print(f"❌ File non trovato: {file_path}")
        return None
    
    data = []
    with open(file_path, 'r') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def load_json(file_path):
    """Carica file JSON"""
    with open(file_path, 'r') as f:
        return json.load(f)

def calculate_bleu_simple(reference, candidate):
    """BLEU semplificato (unigram precision)"""
    ref_words = reference.lower().split()
    cand_words = candidate.lower().split()
    
    if not cand_words:
        return 0.0
    
    ref_counter = Counter(ref_words)
    cand_counter = Counter(cand_words)
    
    overlap = 0
    for word in cand_counter:
        overlap += min(cand_counter[word], ref_counter.get(word, 0))
    
    return overlap / len(cand_words)

def calculate_rouge_l(reference, candidate):
    """ROUGE-L semplificato"""
    ref_words = reference.lower().split()
    cand_words = candidate.lower().split()
    
    if not ref_words or not cand_words:
        return 0.0
    
    # LCS (Longest Common Subsequence)
    m, n = len(ref_words), len(cand_words)
    dp = [[0] * (n + 1) for _ in range(m + 1)]
    
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if ref_words[i-1] == cand_words[j-1]:
                dp[i][j] = dp[i-1][j-1] + 1
            else:
                dp[i][j] = max(dp[i-1][j], dp[i][j-1])
    
    lcs_length = dp[m][n]
    
    if lcs_length == 0:
        return 0.0
    
    precision = lcs_length / len(cand_words)
    recall = lcs_length / len(ref_words)
    
    if precision + recall == 0:
        return 0.0
    
    f1 = 2 * precision * recall / (precision + recall)
    return f1

def analyze_text_quality(text):
    """Analizza qualità del testo"""
    if not text:
        return {
            'length': 0,
            'words': 0,
            'repetition_ratio': 1.0,
            'malformed': True
        }
    
    words = text.split()
    word_count = len(words)
    
    # Calcola ripetizioni
    if word_count == 0:
        repetition_ratio = 1.0
    else:
        unique_words = len(set(words))
        repetition_ratio = 1.0 - (unique_words / word_count)
    
    # Rileva testo malformato
    malformed = (
        repetition_ratio > 0.7 or  # Troppe ripetizioni
        '<' in text or '>' in text or  # Tag HTML/XML
        text.count('</') > 2 or  # Tag di chiusura
        len(text) > 500  # Troppo lungo
    )
    
    return {
        'length': len(text),
        'words': word_count,
        'repetition_ratio': repetition_ratio,
        'malformed': malformed
    }

def main():
    parser = argparse.ArgumentParser(description='Valutazione semplice senza CLIP')
    parser.add_argument('--llama_results', type=str, help='File JSONL risultati Llama')
    parser.add_argument('--gemma_results', type=str, help='File JSONL risultati Gemma')
    parser.add_argument('--baseline_idefix', type=str, help='File JSONL baseline Ide Fix 3')
    parser.add_argument('--baseline_flores2', type=str, help='File JSONL baseline Flores 2')
    parser.add_argument('--baseline_blip2', type=str, help='File JSONL baseline BLIP 2.7B')
    parser.add_argument('--reference_file', type=str, required=True, help='File JSON dati riferimento')
    parser.add_argument('--output_file', type=str, required=True, help='File output risultati')
    
    args = parser.parse_args()

    print("🔍 VALUTAZIONE SEMPLICE (SENZA CLIP)")
    print("=" * 50)

    # Carica dati
    print("📂 Caricamento dati...")
    
    models = {}
    
    if args.llama_results:
        models['llama'] = load_jsonl(args.llama_results)
        print(f"✅ Llama: {len(models['llama']) if models['llama'] else 0} esempi")
    
    if args.gemma_results:
        models['gemma'] = load_jsonl(args.gemma_results)
        print(f"✅ Gemma: {len(models['gemma']) if models['gemma'] else 0} esempi")
    
    if args.baseline_idefix:
        models['idefix'] = load_jsonl(args.baseline_idefix)
        print(f"✅ Ide Fix 3: {len(models['idefix']) if models['idefix'] else 0} esempi")
    
    if args.baseline_flores2:
        models['flores2'] = load_jsonl(args.baseline_flores2)
        print(f"✅ Flores 2: {len(models['flores2']) if models['flores2'] else 0} esempi")
    
    if args.baseline_blip2:
        models['blip2'] = load_jsonl(args.baseline_blip2)
        print(f"✅ BLIP 2.7B: {len(models['blip2']) if models['blip2'] else 0} esempi")
    
    reference_data = load_json(args.reference_file)
    reference_dict = {item['id']: item for item in reference_data}
    print(f"✅ Reference: {len(reference_data)} esempi")

    # Valuta ogni modello
    results = {}
    
    for model_name, model_data in models.items():
        if model_data is None:
            continue
            
        print(f"\n🔍 Valutazione {model_name.upper()}...")
        
        bleu_scores = []
        rouge_scores = []
        quality_stats = {
            'malformed_count': 0,
            'avg_length': 0,
            'avg_words': 0,
            'avg_repetition': 0
        }
        
        valid_examples = 0
        
        for item in model_data:
            item_id = item['id']
            
            if item_id not in reference_dict:
                continue
            
            # Estrai caption
            if 'generated_caption' in item:
                generated = item['generated_caption']
            elif 'generated' in item:
                generated = item['generated']
            else:
                continue
            
            # Reference caption
            reference = reference_dict[item_id].get('true_caption', '')
            if not reference:
                reference = reference_dict[item_id].get('caption', '')
            
            if not reference:
                continue
            
            # Calcola metriche
            bleu = calculate_bleu_simple(reference, generated)
            rouge = calculate_rouge_l(reference, generated)
            quality = analyze_text_quality(generated)
            
            bleu_scores.append(bleu)
            rouge_scores.append(rouge)
            
            quality_stats['avg_length'] += quality['length']
            quality_stats['avg_words'] += quality['words']
            quality_stats['avg_repetition'] += quality['repetition_ratio']
            
            if quality['malformed']:
                quality_stats['malformed_count'] += 1
            
            valid_examples += 1
        
        if valid_examples > 0:
            # Calcola medie
            avg_bleu = np.mean(bleu_scores)
            avg_rouge = np.mean(rouge_scores)
            
            quality_stats['avg_length'] /= valid_examples
            quality_stats['avg_words'] /= valid_examples
            quality_stats['avg_repetition'] /= valid_examples
            quality_stats['malformed_percentage'] = (quality_stats['malformed_count'] / valid_examples) * 100
            
            results[model_name] = {
                'valid_examples': valid_examples,
                'avg_bleu': avg_bleu,
                'avg_rouge': avg_rouge,
                'quality_stats': quality_stats,
                'bleu_scores': bleu_scores,
                'rouge_scores': rouge_scores
            }
            
            print(f"  📊 Esempi validi: {valid_examples}")
            print(f"  📊 BLEU medio: {avg_bleu:.4f}")
            print(f"  📊 ROUGE-L medio: {avg_rouge:.4f}")
            print(f"  📊 Lunghezza media: {quality_stats['avg_length']:.1f} caratteri")
            print(f"  📊 Parole medie: {quality_stats['avg_words']:.1f}")
            print(f"  📊 Ripetizioni: {quality_stats['avg_repetition']:.3f}")
            print(f"  📊 Malformati: {quality_stats['malformed_percentage']:.1f}%")
        else:
            print(f"  ❌ Nessun esempio valido per {model_name}")

    # Salva risultati
    print(f"\n💾 Salvataggio risultati in {args.output_file}...")
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=2)

    # Riepilogo finale
    print(f"\n🎉 VALUTAZIONE COMPLETATA!")
    print("📊 CLASSIFICA MODELLI:")
    
    # Ordina per BLEU score
    sorted_models = sorted(
        [(name, data['avg_bleu']) for name, data in results.items()],
        key=lambda x: x[1],
        reverse=True
    )
    
    for i, (model_name, bleu_score) in enumerate(sorted_models, 1):
        print(f"  {i}. {model_name.upper()}: BLEU {bleu_score:.4f}")

if __name__ == "__main__":
    main()
