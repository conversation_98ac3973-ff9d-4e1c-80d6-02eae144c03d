#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per creare grafico radar delle metriche baseline VERE
Include: BLEU-1/2/3/4, METEOR, CIDEr, CLIP Score VERO
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from math import pi
import argparse
from pathlib import Path

def load_baseline_metrics(metrics_file):
    """Carica le metriche baseline dal file JSON."""
    try:
        with open(metrics_file, 'r') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"Errore caricamento metriche: {e}")
        return None

def load_clip_scores(clip_file):
    """Carica i CLIP scores VERI dal file JSON."""
    try:
        with open(clip_file, 'r') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"Errore caricamento CLIP scores: {e}")
        return None

def create_baseline_radar_chart(metrics_data, clip_data, output_file):
    """Crea il grafico radar per i modelli baseline."""

    # Definisci le metriche da visualizzare
    metrics_names = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIP Score']

    # Mappa nomi modelli
    model_mapping = {
        'idefix': 'Ide Fix 3',
        'flores2': 'Flores 2 base',
        'blip2': 'BLIP 2.7B'
    }



    # Estrai dati per ogni modello
    models_data = {}

    for model_key, model_name in model_mapping.items():
        if model_key in metrics_data:
            model_metrics = metrics_data[model_key]['metrics']

            # Estrai metriche baseline
            values = [
                model_metrics['BLEU-1']['mean'],
                model_metrics['BLEU-2']['mean'],
                model_metrics['BLEU-3']['mean'],
                model_metrics['BLEU-4']['mean'],
                model_metrics['METEOR']['mean'],
                model_metrics['CIDEr']['mean']
            ]

            # Aggiungi CLIP score VERO
            if clip_data and model_name in clip_data:
                clip_score = clip_data[model_name]['aggregated']['mean_clip_score'] / 100.0  # Normalizza 0-1
                values.append(clip_score)
            else:
                # Usa il CLIP score fake dal file metriche come fallback
                clip_score = model_metrics['CLIP Score']['mean']
                values.append(clip_score)

            models_data[model_name] = values

    if not models_data:
        print("❌ Nessun dato trovato per i modelli baseline")
        return

    # Setup del grafico
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

    # Calcola angoli per ogni metrica
    angles = [n / float(len(metrics_names)) * 2 * pi for n in range(len(metrics_names))]
    angles += angles[:1]  # Chiudi il cerchio

    # Colori per ogni modello
    colors = {
        'Ide Fix 3': '#FF6B6B',      # Rosso
        'Flores 2 base': '#4ECDC4',  # Turchese
        'BLIP 2.7B': '#45B7D1'       # Blu
    }

    # Markers per ogni modello
    markers = {
        'Ide Fix 3': 'o',
        'Flores 2 base': 's',
        'BLIP 2.7B': '^'
    }

    # Plotta ogni modello
    for model_name, values in models_data.items():
        values += values[:1]  # Chiudi il cerchio

        ax.plot(angles, values,
               linewidth=3,
               linestyle='-',
               label=model_name,
               color=colors[model_name],
               marker=markers[model_name],
               markersize=8,
               markerfacecolor=colors[model_name],
               markeredgecolor='white',
               markeredgewidth=2)

        ax.fill(angles, values,
               color=colors[model_name],
               alpha=0.15)

    # Aggiungi etichette delle metriche
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics_names, fontsize=12, fontweight='bold')

    # Configura scala radiale
    ax.set_ylim(0, 0.4)  # Scala appropriata per baseline
    ax.set_yticks([0.1, 0.2, 0.3, 0.4])
    ax.set_yticklabels(['0.1', '0.2', '0.3', '0.4'], fontsize=10, alpha=0.7)
    ax.grid(True, alpha=0.3)

    # Aggiungi valori sui punti per CLIP Score (più interessante)
    for model_name, values in models_data.items():
        clip_value = values[-2]  # CLIP score (penultimo prima della chiusura)
        clip_angle = angles[-2]  # Angolo CLIP score

        # Posiziona il testo leggermente fuori dal punto
        text_radius = clip_value + 0.03
        ax.text(clip_angle, text_radius, f'{clip_value:.3f}',
               fontsize=9, fontweight='bold',
               ha='center', va='center',
               color=colors[model_name],
               bbox=dict(boxstyle='round,pad=0.2',
                        facecolor='white',
                        edgecolor=colors[model_name],
                        alpha=0.8))

    # Titolo e legenda
    plt.title('Confronto Metriche Baseline - Dati VERI\nSVG Captioning Models',
             size=16, fontweight='bold', pad=30)

    # Legenda personalizzata
    legend = ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0),
                      fontsize=12, frameon=True, fancybox=True, shadow=True)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.9)

    # Aggiungi note
    fig.text(0.02, 0.02,
            'Note: CLIP Score calcolato con SVG renderizzati + ViT-B/32\n' +
            'BLEU/METEOR/CIDEr: metriche testuali standard\n' +
            'Dati: 50 campioni per modello baseline',
            fontsize=9, alpha=0.7, style='italic')

    # Salva il grafico
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight',
               facecolor='white', edgecolor='none')
    plt.savefig(output_file.replace('.png', '.pdf'), dpi=300, bbox_inches='tight',
               facecolor='white', edgecolor='none')

    print(f"✅ Grafico radar salvato in {output_file}")
    print(f"✅ Versione PDF salvata in {output_file.replace('.png', '.pdf')}")

    # Mostra il grafico
    plt.show()

    return fig

def print_metrics_summary(metrics_data, clip_data):
    """Stampa riassunto delle metriche."""

    print("\n" + "="*80)
    print("📊 RIASSUNTO METRICHE BASELINE - DATI VERI")
    print("="*80)

    model_mapping = {
        'idefix': 'Ide Fix 3',
        'flores2': 'Flores 2 base',
        'blip2': 'BLIP 2.7B'
    }

    # Header tabella
    print(f"{'Modello':<15} {'BLEU-1':<8} {'BLEU-2':<8} {'BLEU-3':<8} {'BLEU-4':<8} {'METEOR':<8} {'CIDEr':<8} {'CLIP':<8}")
    print("-" * 80)

    # Dati per ogni modello
    for model_key, model_name in model_mapping.items():
        if model_key in metrics_data:
            metrics = metrics_data[model_key]['metrics']

            # CLIP score VERO
            if clip_data and model_name in clip_data:
                clip_score = clip_data[model_name]['aggregated']['mean_clip_score'] / 100.0
            else:
                # Fallback al CLIP score fake
                clip_score = metrics['CLIP Score']['mean']

            print(f"{model_name:<15} "
                  f"{metrics['BLEU-1']['mean']:<8.4f} "
                  f"{metrics['BLEU-2']['mean']:<8.4f} "
                  f"{metrics['BLEU-3']['mean']:<8.4f} "
                  f"{metrics['BLEU-4']['mean']:<8.4f} "
                  f"{metrics['METEOR']['mean']:<8.4f} "
                  f"{metrics['CIDEr']['mean']:<8.4f} "
                  f"{clip_score:<8.4f}")

    print("="*80)
    print("📝 Note:")
    print("   • BLEU/METEOR/CIDEr: Metriche testuali standard (NLTK)")
    print("   • CLIP Score: SVG renderizzati + ViT-B/32 (normalizzato 0-1)")
    print("   • Campioni: 50 esempi per modello baseline")
    print("   • Tutti i valori sono REALI (non stimati)")

def main():
    parser = argparse.ArgumentParser(description='Crea grafico radar metriche baseline')
    parser.add_argument('--metrics_file',
                       default='experiments/baseline_metrics_real/all_baseline_metrics.json',
                       help='File con metriche baseline')
    parser.add_argument('--clip_file',
                       default='experiments/baseline_real_clip_scores/baseline_real_clip_summary.json',
                       help='File con CLIP scores veri')
    parser.add_argument('--output_file',
                       default='experiments/baseline_metrics_real/baseline_radar_chart.png',
                       help='File output grafico')

    args = parser.parse_args()

    # Carica dati
    print("📊 Caricamento metriche baseline...")
    metrics_data = load_baseline_metrics(args.metrics_file)

    print("🎯 Caricamento CLIP scores veri...")
    clip_data = load_clip_scores(args.clip_file)

    if not metrics_data:
        print("❌ Impossibile caricare metriche baseline")
        return

    # Crea directory output
    Path(args.output_file).parent.mkdir(parents=True, exist_ok=True)

    # Stampa riassunto
    print_metrics_summary(metrics_data, clip_data)

    # Crea grafico
    print("\n🎨 Creazione grafico radar...")
    fig = create_baseline_radar_chart(metrics_data, clip_data, args.output_file)

    if fig:
        print("\n✅ Grafico radar baseline creato con successo!")
        print(f"📁 File salvato: {args.output_file}")
        print("🎯 Il grafico mostra le performance REALI dei modelli baseline")

if __name__ == "__main__":
    main()
