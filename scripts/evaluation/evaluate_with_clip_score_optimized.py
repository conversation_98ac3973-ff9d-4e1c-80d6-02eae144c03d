#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script ottimizzato per calcolare il CLIP Score per i risultati di inferenza SVG.
Implementa batch processing per un utilizzo più efficiente della GPU.
"""

import os
import sys
import json
import argparse
import logging
import torch
import clip
import numpy as np
import pandas as pd
from PIL import Image
import cairosvg
import io
from tqdm import tqdm
import matplotlib.pyplot as plt
import wandb
import time

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def render_svg_to_image(svg_string: str, size: int = 224) -> Image.Image:
    """
    Renderizza una stringa SVG in un'immagine PIL.

    Args:
        svg_string: Stringa SVG da renderizzare
        size: Dimensione dell'immagine di output

    Returns:
        Immagine PIL renderizzata
    """
    try:
        # Renderizza SVG in PNG
        png_data = cairosvg.svg2png(bytestring=svg_string.encode('utf-8'),
                                    output_width=size,
                                    output_height=size)

        # Converti i dati PNG in un'immagine PIL
        image = Image.open(io.BytesIO(png_data))
        return image
    except Exception as e:
        logger.error(f"Errore nel rendering SVG: {e}")
        # Restituisci un'immagine nera in caso di errore
        return Image.new('RGB', (size, size), color='black')

def calculate_clip_scores_batch(images, captions, model, preprocess, device, batch_size=16):
    """
    Calcola il CLIP score per un batch di immagini e caption.

    Args:
        images: Lista di immagini PIL
        captions: Lista di caption
        model: Modello CLIP
        preprocess: Funzione di preprocessing CLIP
        device: Dispositivo su cui eseguire il calcolo
        batch_size: Dimensione del batch

    Returns:
        Lista di CLIP scores (similarità coseno * 100)
    """
    total_samples = len(images)
    scores = []
    
    # Preprocessing di tutte le immagini
    processed_images = []
    for img in images:
        try:
            processed_images.append(preprocess(img))
        except Exception as e:
            logger.error(f"Errore nel preprocessing dell'immagine: {e}")
            # In caso di errore, inserisci un tensore vuoto
            processed_images.append(torch.zeros((3, 224, 224)))
    
    # Elabora in batch
    for i in tqdm(range(0, total_samples, batch_size), desc="Batch processing"):
        end_idx = min(i + batch_size, total_samples)
        batch_images = processed_images[i:end_idx]
        batch_captions = captions[i:end_idx]
        
        try:
            # Stack delle immagini in un unico tensore
            image_batch = torch.stack(batch_images).to(device)
            
            # Tokenizza le caption
            text_batch = clip.tokenize(batch_captions).to(device)
            
            # Calcola le features
            with torch.no_grad():
                image_features = model.encode_image(image_batch)
                text_features = model.encode_text(text_batch)
            
            # Normalizza le features
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            # Calcola la similarità coseno (batch)
            similarities = (100.0 * torch.matmul(image_features, text_features.T)).diag().cpu().numpy()
            scores.extend(similarities.tolist())
        except Exception as e:
            logger.error(f"Errore nell'elaborazione del batch {i}:{end_idx}: {e}")
            # In caso di errore, inserisci valori nulli
            scores.extend([0.0] * (end_idx - i))
    
    return scores

def load_results(results_file: str) -> list:
    """
    Carica i risultati dell'inferenza da un file JSON o JSONL.

    Args:
        results_file: Percorso del file dei risultati

    Returns:
        Lista di risultati
    """
    results = []

    try:
        # Determina se il file è JSON o JSONL
        with open(results_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()

            # Riavvolgi il file
            f.seek(0)

            if first_line.startswith('[') or first_line.endswith(']'):
                # File JSON
                results = json.load(f)
            else:
                # File JSONL
                for line in f:
                    try:
                        results.append(json.loads(line))
                    except json.JSONDecodeError as e:
                        logger.error(f"Errore nel parsing della linea JSONL: {e}")

        logger.info(f"Caricati {len(results)} risultati da {results_file}")
        return results
    except Exception as e:
        logger.error(f"Errore nel caricamento dei risultati: {e}")
        return []

def load_svg_data(data_file: str) -> dict:
    """
    Carica i dati SVG dal file originale.

    Args:
        data_file: Percorso del file dei dati

    Returns:
        Dizionario con ID come chiave e dati SVG come valore
    """
    svg_data = {}

    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        for item in data:
            item_id = item.get('id')
            svg = item.get('xml', '')

            if item_id and svg:
                svg_data[item_id] = svg

        logger.info(f"Caricati dati SVG per {len(svg_data)} esempi da {data_file}")
        return svg_data
    except Exception as e:
        logger.error(f"Errore nel caricamento dei dati SVG: {e}")
        return {}

def save_intermediate_results(results, output_file, suffix="_partial"):
    """
    Salva i risultati intermedi in un file JSON.

    Args:
        results: Lista dei risultati
        output_file: Percorso del file di output
        suffix: Suffisso da aggiungere al nome del file per i risultati intermedi
    """
    try:
        # Crea il nome file per i risultati intermedi
        base_name, ext = os.path.splitext(output_file)
        intermediate_file = f"{base_name}{suffix}{ext}"
        
        with open(intermediate_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Salvati risultati intermedi in {intermediate_file}")
    except Exception as e:
        logger.error(f"Errore nel salvataggio dei risultati intermedi: {e}")

def load_checkpoint(output_file, suffix="_partial"):
    """
    Carica un checkpoint da un file di risultati intermedi.

    Args:
        output_file: Percorso del file di output
        suffix: Suffisso utilizzato per i risultati intermedi

    Returns:
        Tuple (risultati, set degli ID elaborati)
    """
    base_name, ext = os.path.splitext(output_file)
    checkpoint_file = f"{base_name}{suffix}{ext}"
    
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # Estrai gli ID già elaborati
            processed_ids = set(r.get('id') for r in results if 'id' in r)
            
            logger.info(f"Caricato checkpoint con {len(results)} risultati da {checkpoint_file}")
            return results, processed_ids
        except Exception as e:
            logger.error(f"Errore nel caricamento del checkpoint: {e}")
    
    return [], set()

def main():
    parser = argparse.ArgumentParser(description="Calcola il CLIP Score per i risultati di inferenza SVG con batch processing.")
    parser.add_argument("--results_file", type=str, required=True, help="File JSON o JSONL con i risultati dell'inferenza.")
    parser.add_argument("--data_file", type=str, required=True, help="File JSON con i dati SVG originali.")
    parser.add_argument("--output_file", type=str, required=True, help="File di output per i risultati con CLIP Score.")
    parser.add_argument("--model_name", type=str, required=True, help="Nome del modello (per il report).")
    parser.add_argument("--clip_model", type=str, default="ViT-B/32", help="Modello CLIP da utilizzare.")
    parser.add_argument("--render_size", type=int, default=224, help="Dimensione per il rendering SVG.")
    parser.add_argument("--use_wandb", action="store_true", help="Usa Weights & Biands per il logging.")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands.")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto Weights & Biands.")
    parser.add_argument("--num_samples", type=int, default=-1, help="Numero di campioni da valutare. -1 per tutti.")
    parser.add_argument("--batch_size", type=int, default=16, help="Dimensione del batch per l'elaborazione.")
    parser.add_argument("--checkpoint_interval", type=int, default=50, help="Intervallo per il salvataggio dei checkpoint (numero di campioni).")
    parser.add_argument("--resume", action="store_true", help="Riprendi da checkpoint esistente.")

    args = parser.parse_args()

    start_time = time.time()

    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Inizializza Weights & Biands se richiesto
    if args.use_wandb:
        os.environ["WANDB_API_KEY"] = "****************************************"
        wandb.init(
            entity=args.wandb_entity,
            project=args.wandb_project,
            name=f"{args.model_name}_clip_evaluation_optimized",
            job_type="evaluation",
            config={
                "model": args.model_name,
                "clip_model": args.clip_model,
                "batch_size": args.batch_size,
                "num_samples": args.num_samples
            }
        )

    # Carica il modello CLIP
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Caricamento del modello CLIP {args.clip_model} su {device}")
    model, preprocess = clip.load(args.clip_model, device=device)

    # Carica i risultati dell'inferenza
    results = load_results(args.results_file)

    # Carica i dati SVG originali
    svg_data = load_svg_data(args.data_file)

    # Limita il numero di campioni se richiesto
    if args.num_samples > 0 and args.num_samples < len(results):
        results = results[:args.num_samples]
        logger.info(f"Limitato a {args.num_samples} campioni")

    # Verifica se riprendere da un checkpoint
    processed_results = []
    processed_ids = set()
    if args.resume:
        processed_results, processed_ids = load_checkpoint(args.output_file)

    # Filtra i risultati per elaborare solo quelli non ancora processati
    results_to_process = [r for r in results if r.get('id') not in processed_ids]
    logger.info(f"Elaborazione di {len(results_to_process)} risultati rimanenti su {len(results)} totali")

    if not results_to_process:
        logger.info("Tutti i risultati sono già stati elaborati!")
        # Se tutti i risultati sono già stati elaborati, salviamo direttamente il file finale
        with open(args.output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_results, f, indent=2)
        logger.info(f"Risultati salvati in {args.output_file}")
        if args.use_wandb:
            wandb.finish()
        return

    # Prepara gli array per il batch processing
    item_ids = []
    images = []
    generated_captions = []
    true_captions = []

    # Prepara i dati per il batch processing
    logger.info("Preparazione dei dati per il batch processing...")
    for result in tqdm(results_to_process, desc="Rendering SVG"):
        item_id = result.get('id')
        generated_caption = result.get('generated_caption', '')
        true_caption = result.get('true_caption', '')
        
        # Ottieni i dati SVG
        svg = svg_data.get(item_id, '')

        if not svg:
            logger.warning(f"Dati SVG non trovati per l'ID {item_id}")
            continue

        # Renderizza l'SVG
        image = render_svg_to_image(svg, args.render_size)
        
        # Aggiungi i dati agli array
        item_ids.append(item_id)
        images.append(image)
        generated_captions.append(generated_caption)
        true_captions.append(true_caption)

    # Calcola il CLIP score per le caption generate in batch
    logger.info("Calcolo CLIP score per caption generate...")
    generated_clip_scores = calculate_clip_scores_batch(
        images, generated_captions, model, preprocess, device, args.batch_size
    )
    
    # Calcola il CLIP score per le caption di riferimento in batch
    logger.info("Calcolo CLIP score per caption di riferimento...")
    true_clip_scores = calculate_clip_scores_batch(
        images, true_captions, model, preprocess, device, args.batch_size
    )
    
    # Abbina i risultati ai risultati originali
    clip_scores = []
    for i, item_id in enumerate(item_ids):
        result = next((r for r in results_to_process if r.get('id') == item_id), None)
        if result:
            result['generated_clip_score'] = generated_clip_scores[i]
            result['true_clip_score'] = true_clip_scores[i]
            result['clip_score_ratio'] = (
                generated_clip_scores[i] / true_clip_scores[i] if true_clip_scores[i] > 0 else 0
            )
            clip_scores.append({
                'id': item_id,
                'generated_clip_score': generated_clip_scores[i],
                'true_clip_score': true_clip_scores[i],
                'clip_score_ratio': result['clip_score_ratio']
            })
            processed_results.append(result)
        
        # Salva checkpoint intermedi
        if (i + 1) % args.checkpoint_interval == 0:
            save_intermediate_results(processed_results, args.output_file)
            if args.use_wandb:
                wandb.log({
                    "processed_items": i + 1,
                    "elapsed_time": time.time() - start_time
                })

    # Calcola statistiche aggregate
    all_results = processed_results
    generated_scores = [r.get('generated_clip_score', 0) for r in all_results]
    true_scores = [r.get('true_clip_score', 0) for r in all_results]
    ratios = [r.get('clip_score_ratio', 0) for r in all_results]

    aggregated_metrics = {
        'mean_generated_clip_score': np.mean(generated_scores) if generated_scores else 0,
        'mean_true_clip_score': np.mean(true_scores) if true_scores else 0,
        'mean_clip_score_ratio': np.mean(ratios) if ratios else 0,
        'median_generated_clip_score': np.median(generated_scores) if generated_scores else 0,
        'median_true_clip_score': np.median(true_scores) if true_scores else 0,
        'median_clip_score_ratio': np.median(ratios) if ratios else 0,
        'min_generated_clip_score': np.min(generated_scores) if generated_scores else 0,
        'max_generated_clip_score': np.max(generated_scores) if generated_scores else 0,
        'std_generated_clip_score': np.std(generated_scores) if generated_scores else 0,
        'std_true_clip_score': np.std(true_scores) if true_scores else 0,
        'processed_samples': len(all_results),
        'total_samples': len(results),
        'elapsed_time_seconds': time.time() - start_time
    }

    # Log delle metriche su Weights & Biands
    if args.use_wandb:
        wandb.log(aggregated_metrics)
        
        # Crea istogrammi e box plot
        fig, axs = plt.subplots(2, 2, figsize=(12, 10))
        
        # Istogramma dei CLIP score generati
        axs[0, 0].hist(generated_scores, bins=20, alpha=0.7)
        axs[0, 0].set_title('Distribuzione CLIP Score Generati')
        axs[0, 0].set_xlabel('CLIP Score')
        axs[0, 0].set_ylabel('Frequenza')
        
        # Istogramma dei CLIP score veri
        axs[0, 1].hist(true_scores, bins=20, alpha=0.7)
        axs[0, 1].set_title('Distribuzione CLIP Score Veri')
        axs[0, 1].set_xlabel('CLIP Score')
        axs[0, 1].set_ylabel('Frequenza')
        
        # Box plot dei CLIP score
        axs[1, 0].boxplot([generated_scores, true_scores], labels=['Generati', 'Veri'])
        axs[1, 0].set_title('Box Plot CLIP Scores')
        axs[1, 0].set_ylabel('CLIP Score')
        
        # Istogramma dei rapporti
        axs[1, 1].hist(ratios, bins=20, alpha=0.7)
        axs[1, 1].set_title('Distribuzione Rapporti CLIP Score (Generato/Vero)')
        axs[1, 1].set_xlabel('Rapporto')
        axs[1, 1].set_ylabel('Frequenza')
        
        plt.tight_layout()
        wandb.log({"clip_score_plots": wandb.Image(fig)})
        plt.close(fig)
        
        # Top e bottom 10 esempi
        df = pd.DataFrame(clip_scores)
        top_10 = df.nlargest(10, 'generated_clip_score')
        bottom_10 = df.nsmallest(10, 'generated_clip_score')
        
        wandb.log({
            "top_10_examples": wandb.Table(dataframe=top_10),
            "bottom_10_examples": wandb.Table(dataframe=bottom_10)
        })

    # Salva i risultati finali
    output_data = {
        'model_name': args.model_name,
        'clip_model': args.clip_model,
        'results': all_results,
        'aggregated_metrics': aggregated_metrics
    }

    with open(args.output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2)

    logger.info(f"Risultati salvati in {args.output_file}")
    logger.info(f"Tempo totale di elaborazione: {aggregated_metrics['elapsed_time_seconds']:.2f} secondi")

    # Chiudi Weights & Biands
    if args.use_wandb:
        wandb.finish()

if __name__ == "__main__":
    main() 