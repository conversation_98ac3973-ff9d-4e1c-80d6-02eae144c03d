#!/usr/bin/env python3
"""
Script baseline con Pipeline Hugging Face (più robusto)
"""

import argparse
import json
import os
from pathlib import Path
from tqdm import tqdm
import torch
from PIL import Image
import cairosvg
import io
import numpy as np
from transformers import pipeline
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def convert_rgba_to_rgb(image):
    """Converte un'immagine da RGBA a RGB con sfondo bianco"""
    if image.mode == 'RGBA':
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3])
        return bg
    elif image.mode == 'P':
        image = image.convert('RGBA')
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3] if len(image.split()) == 4 else None)
        return bg
    else:
        return image.convert('RGB')

def rasterize_svg(svg_data, dpi=150):
    """Converte SVG in immagine PNG con sfondo bianco."""
    try:
        png_data = cairosvg.svg2png(bytestring=svg_data.encode('utf-8'), dpi=dpi)
        image = Image.open(io.BytesIO(png_data))
        return convert_rgba_to_rgb(image)
    except Exception as e:
        logger.error(f"Errore durante la rasterizzazione SVG: {e}")
        return None

def create_pipeline(model_name):
    """Crea pipeline Hugging Face per il modello specificato."""
    device = 0 if torch.cuda.is_available() else -1

    try:
        # Configurazioni ottimizzate per memoria GPU
        if model_name == "blip":
            return pipeline("image-to-text",
                          model="Salesforce/blip-image-captioning-base",
                          device=device,
                          torch_dtype=torch.float16,  # Riduce memoria
                          device_map="auto")

        elif model_name == "vit-gpt2":
            return pipeline("image-to-text",
                          model="nlpconnect/vit-gpt2-image-captioning",
                          device=device,
                          torch_dtype=torch.float16)

        elif model_name == "idefix":
            # Ide Fix 3 - usando microsoft/git-large
            return pipeline("image-to-text",
                          model="microsoft/git-large",
                          device=device,
                          torch_dtype=torch.float16)

        elif model_name == "flores2":
            # Flores 2 base - usando microsoft/git-base come proxy
            return pipeline("image-to-text",
                          model="microsoft/git-base",
                          device=device,
                          torch_dtype=torch.float16)

        elif model_name == "blip2":
            # BLIP 2.7B con ottimizzazioni memoria
            return pipeline("image-to-text",
                          model="Salesforce/blip2-opt-2.7b",
                          device=device,
                          torch_dtype=torch.float16,
                          device_map="auto",
                          max_memory={0: "10GB"})  # Limita memoria GPU

        else:
            logger.error(f"Modello non supportato: {model_name}")
            return None

    except Exception as e:
        logger.error(f"Errore creazione pipeline {model_name}: {e}")
        return None

def evaluate_model_pipeline(model_name, test_data, output_dir):
    """Valuta un modello usando Pipeline Hugging Face."""
    logger.info(f"Valutazione modello {model_name} con Pipeline...")

    # Crea pipeline
    captioner = create_pipeline(model_name)
    if captioner is None:
        return None

    results_data = []

    # Processa i dati
    for i, item in enumerate(tqdm(test_data, desc=f"Evaluating {model_name}")):
        try:
            # Rasterizza SVG
            if 'xml' in item:
                svg_data = item['xml']
            elif 'svg_xml' in item:
                svg_data = item['svg_xml']
            else:
                logger.warning(f"Nessun campo SVG trovato nell'esempio {i}")
                continue

            image = rasterize_svg(svg_data)
            if image is None:
                continue

            # Genera caption con pipeline (con timeout e gestione errori)
            try:
                # Ridimensiona immagine se troppo grande
                if image.size[0] > 512 or image.size[1] > 512:
                    image.thumbnail((512, 512), Image.Resampling.LANCZOS)

                result = captioner(image,
                                 max_new_tokens=50,
                                 do_sample=False,  # Deterministic
                                 num_beams=1)      # Veloce
                generated_caption = result[0]['generated_text'] if result else ""

            except torch.cuda.OutOfMemoryError:
                logger.warning(f"GPU OOM per esempio {i}, liberando memoria...")
                torch.cuda.empty_cache()
                generated_caption = "[GPU_OOM_ERROR]"
            except Exception as e:
                logger.warning(f"Errore generazione caption esempio {i}: {e}")
                generated_caption = "[GENERATION_ERROR]"

            # Salva risultati
            reference_caption = item.get('caption', item.get('reference_caption', ''))

            results_data.append({
                'id': item.get('id', item.get('example_id', f'example_{i}')),
                'reference': reference_caption,
                'generated': generated_caption
            })

            # Log e pulizia memoria ogni 25 esempi
            if (i + 1) % 25 == 0:
                logger.info(f"Processati {i + 1}/{len(test_data)} esempi")
                torch.cuda.empty_cache()  # Pulizia memoria periodica

        except Exception as e:
            logger.error(f"Errore processando esempio {i}: {e}")
            continue

    # Salva risultati
    output_path = Path(output_dir) / f"baseline_{model_name}_pipeline"
    output_path.mkdir(parents=True, exist_ok=True)

    with open(output_path / 'predictions.jsonl', 'w') as f:
        for pred in results_data:
            f.write(json.dumps(pred) + '\n')

    logger.info(f"Risultati salvati in {output_path}")
    logger.info(f"Processati {len(results_data)} esempi per {model_name}")

    # Libera memoria
    del captioner
    torch.cuda.empty_cache()

    return results_data

def main():
    parser = argparse.ArgumentParser(description='Valuta modelli baseline con Pipeline HF')
    parser.add_argument('--test_file', required=True, help='Percorso del file di test')
    parser.add_argument('--output_dir', required=True, help='Directory di output')
    parser.add_argument('--models', nargs='+',
                       choices=['idefix', 'flores2', 'blip2'],
                       default=['idefix'],  # Inizia con Ide Fix 3
                       help='Lista dei modelli da valutare (Ide Fix 3, Flores 2, BLIP 2.7B)')

    args = parser.parse_args()

    # Carica il dataset di test
    logger.info(f"Caricamento dataset da {args.test_file}")
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)

    logger.info(f"Dataset caricato: {len(test_data)} esempi")

    # Valuta ogni modello
    for model_name in args.models:
        logger.info(f"Inizio valutazione {model_name}")

        try:
            results = evaluate_model_pipeline(
                model_name,
                test_data,
                args.output_dir
            )

            if results:
                logger.info(f"✅ {model_name} completato: {len(results)} esempi")
            else:
                logger.error(f"❌ {model_name} fallito")

        except Exception as e:
            logger.error(f"Errore nella valutazione di {model_name}: {e}")
            continue

    logger.info("Valutazione completata!")

if __name__ == '__main__':
    main()
