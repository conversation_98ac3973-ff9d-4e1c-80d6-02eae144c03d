#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare esempi qualitativi VISUALI per i modelli baseline:
SVG renderizzati + caption generated vs ground truth
"""

import os
import json
import random
import argparse
from pathlib import Path
import logging
import base64
from io import BytesIO

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_baseline_predictions(predictions_file):
    """Carica le predizioni baseline da file JSONL."""
    predictions = []
    try:
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    predictions.append(json.loads(line))
        logger.info(f"Caricate {len(predictions)} predizioni da {predictions_file}")
        return predictions
    except Exception as e:
        logger.error(f"Errore nel caricamento {predictions_file}: {e}")
        return []

def load_test_data(test_file):
    """Carica i dati di test con SVG."""
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        logger.info(f"Caricati {len(test_data)} esempi di test")
        return test_data
    except Exception as e:
        logger.error(f"Errore nel caricamento test data: {e}")
        return []

def calculate_simple_quality_score(reference, generated):
    """Calcola un punteggio di qualità semplice."""
    if not reference or not generated:
        return 0.0

    # Tokenizza
    ref_words = set(reference.lower().split())
    gen_words = set(generated.lower().split())

    # Jaccard similarity
    intersection = ref_words.intersection(gen_words)
    union = ref_words.union(gen_words)

    jaccard = len(intersection) / len(union) if union else 0.0

    # Penalizza risposte troppo corte o troppo lunghe
    length_ratio = min(len(gen_words), len(ref_words)) / max(len(gen_words), len(ref_words), 1)

    return jaccard * length_ratio

def save_svg_file(svg_content, svg_id, output_dir):
    """Salva SVG come file separato."""
    try:
        # Pulisci SVG
        svg_clean = svg_content.strip()
        if not svg_clean.startswith('<?xml'):
            svg_clean = '<?xml version="1.0" encoding="utf-8"?>\n' + svg_clean

        # Correggi il problema del doppio d= nei path
        svg_clean = svg_clean.replace('d="d=', 'd="')

        # Salva file SVG
        svg_file = Path(output_dir) / "svgs" / f"{svg_id}.svg"
        svg_file.parent.mkdir(parents=True, exist_ok=True)

        with open(svg_file, 'w', encoding='utf-8') as f:
            f.write(svg_clean)

        return f"svgs/{svg_id}.svg"
    except Exception as e:
        logger.warning(f"Errore salvataggio SVG {svg_id}: {e}")
        return None

def create_html_example(example_data, model_name, svg_path, output_dir):
    """Crea HTML per un singolo esempio."""

    html = f"""
    <div class="example-container" style="border: 2px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 10px; background: #f9f9f9;">
        <h3 style="color: #333; margin-top: 0;">📊 {model_name} - Esempio ID: {example_data['id']}</h3>

        <div style="display: flex; gap: 20px; align-items: flex-start;">
            <!-- SVG Visualization -->
            <div style="flex: 0 0 300px;">
                <h4 style="color: #666; margin-bottom: 10px;">🎨 SVG Originale:</h4>
                <div style="border: 1px solid #ccc; padding: 10px; background: white; text-align: center;">
    """

    if svg_path:
        # Leggi il contenuto SVG e inseriscilo direttamente
        try:
            svg_file_path = Path(output_dir) / svg_path
            with open(svg_file_path, 'r', encoding='utf-8') as f:
                svg_content = f.read()
            # Rimuovi la dichiarazione XML e aggiungi style
            svg_content = svg_content.replace('<?xml version="1.0" encoding="utf-8"?>', '')
            svg_content = svg_content.replace('<svg', '<svg style="max-width: 280px; max-height: 280px; width: 100%; height: auto; border: 1px solid #eee;"')
            # Correggi il problema del doppio d= nei path
            svg_content = svg_content.replace('d="d=', 'd="')
            html += svg_content
        except Exception as e:
            html += f'<p style="color: #999;">Errore caricamento SVG: {e}</p>'
    else:
        html += '<p style="color: #999;">SVG non disponibile</p>'

    html += f"""
                </div>
            </div>

            <!-- Captions Comparison -->
            <div style="flex: 1;">
                <h4 style="color: #666; margin-bottom: 15px;">📝 Confronto Captions:</h4>

                <!-- Ground Truth -->
                <div style="margin-bottom: 15px; padding: 15px; background: #e8f5e8; border-left: 4px solid #4CAF50; border-radius: 5px;">
                    <h5 style="color: #2E7D32; margin: 0 0 8px 0;">✅ Ground Truth:</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"{example_data['reference_caption']}"</p>
                </div>

                <!-- Generated -->
                <div style="margin-bottom: 15px; padding: 15px; background: #fff3e0; border-left: 4px solid #FF9800; border-radius: 5px;">
                    <h5 style="color: #E65100; margin: 0 0 8px 0;">🤖 Generated ({model_name}):</h5>
                    <p style="margin: 0; font-style: italic; color: #333;">"{example_data['generated_caption']}"</p>
                </div>

                <!-- Quality Score -->
                <div style="padding: 10px; background: #f3e5f5; border-left: 4px solid #9C27B0; border-radius: 5px;">
                    <h5 style="color: #6A1B9A; margin: 0 0 5px 0;">📊 Quality Score:</h5>
                    <p style="margin: 0; font-weight: bold; font-size: 18px; color: #333;">{example_data['quality_score']:.3f}</p>
                </div>

                <!-- Analysis -->
                <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-left: 4px solid #2196F3; border-radius: 5px;">
                    <h5 style="color: #1565C0; margin: 0 0 8px 0;">🔍 Analisi:</h5>
                    <ul style="margin: 0; padding-left: 20px; color: #333;">
                        <li><strong>Lunghezza:</strong> {example_data['analysis']['length_comparison']}</li>
                        <li><strong>Parole comuni:</strong> {', '.join(example_data['analysis']['common_words'][:5]) if example_data['analysis']['common_words'] else 'Nessuna'}</li>
                        <li><strong>Problemi:</strong> {', '.join(example_data['analysis']['issues']) if example_data['analysis']['issues'] else 'Nessuno'}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    """

    return html

def generate_visual_qualitative_report(model_name, predictions, test_data, output_dir, num_examples=10):
    """Genera report qualitativo VISUALE per un modello."""

    logger.info(f"🎯 Generazione report qualitativo VISUALE per {model_name}...")

    if not predictions:
        logger.error(f"Nessuna predizione per {model_name}")
        return None

    # Crea mappa SVG
    svg_map = {}
    for item in test_data:
        item_id = item.get('id', item.get('example_id'))
        svg_data = item.get('xml', item.get('svg', item.get('svg_data')))
        true_caption = item.get('caption', item.get('reference_caption', ''))

        if item_id and svg_data:
            svg_map[str(item_id)] = {
                'svg': svg_data,
                'true_caption': true_caption
            }

    # Calcola quality scores e seleziona esempi diversificati
    scored_predictions = []
    for pred in predictions:
        reference = pred.get('reference', '')
        generated = pred.get('generated', '')
        pred_id = str(pred.get('id', 'unknown'))

        # Trova SVG corrispondente
        svg_info = svg_map.get(pred_id, {})
        svg_data = svg_info.get('svg', '')

        if not svg_data:
            continue  # Salta se non ha SVG

        score = calculate_simple_quality_score(reference, generated)

        # Salva SVG come file separato
        svg_path = save_svg_file(svg_data, pred_id, output_dir)

        scored_predictions.append({
            'id': pred_id,
            'reference_caption': reference,
            'generated_caption': generated,
            'quality_score': score,
            'svg_data': svg_data,
            'svg_path': svg_path,
            'analysis': {
                'length_comparison': f"Ref: {len(reference.split())} words, Gen: {len(generated.split())} words",
                'common_words': list(set(reference.lower().split()).intersection(set(generated.lower().split()))),
                'issues': []
            }
        })

    if not scored_predictions:
        logger.error(f"Nessun esempio con SVG trovato per {model_name}")
        return None

    # Ordina per punteggio
    scored_predictions.sort(key=lambda x: x['quality_score'], reverse=True)

    # Seleziona esempi diversificati
    total_examples = len(scored_predictions)
    selected_examples = []

    # Migliori (top 30%)
    top_count = max(1, num_examples // 3)
    selected_examples.extend(scored_predictions[:top_count])

    # Medi (middle 40%)
    mid_start = total_examples // 3
    mid_end = 2 * total_examples // 3
    mid_count = max(1, num_examples // 2)
    if mid_start < mid_end:
        mid_examples = scored_predictions[mid_start:mid_end]
        selected_examples.extend(mid_examples[:mid_count])

    # Peggiori (bottom 30%)
    worst_count = num_examples - len(selected_examples)
    if worst_count > 0:
        selected_examples.extend(scored_predictions[-worst_count:])

    # Limita al numero richiesto
    selected_examples = selected_examples[:num_examples]

    # Crea HTML report
    html_content = f"""
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Esempi Qualitativi Visuali - {model_name}</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .header {{
                text-align: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 15px;
                margin-bottom: 30px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }}
            .stats {{
                background: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 30px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .example-container {{
                background: white;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🎨 Esempi Qualitativi Visuali</h1>
            <h2>{model_name}</h2>
            <p>Confronto SVG + Caption Generated vs Ground Truth</p>
        </div>

        <div class="stats">
            <h3>📊 Statistiche Generali:</h3>
            <ul>
                <li><strong>Esempi totali con SVG:</strong> {len(scored_predictions)}</li>
                <li><strong>Esempi selezionati:</strong> {len(selected_examples)}</li>
                <li><strong>Quality score medio:</strong> {sum(ex['quality_score'] for ex in scored_predictions) / len(scored_predictions):.4f}</li>
                <li><strong>Migliore score:</strong> {max(ex['quality_score'] for ex in scored_predictions):.4f}</li>
                <li><strong>Peggiore score:</strong> {min(ex['quality_score'] for ex in scored_predictions):.4f}</li>
            </ul>
        </div>
    """

    # Aggiungi esempi
    for i, example in enumerate(selected_examples, 1):
        html_content += f"<h2 style='color: #333; border-bottom: 2px solid #ddd; padding-bottom: 10px;'>Esempio {i}</h2>"
        html_content += create_html_example(example, model_name, example.get('svg_path'), output_dir)

    html_content += """
    </body>
    </html>
    """

    # Salva HTML
    output_file = Path(output_dir) / f"{model_name.replace(' ', '_')}_visual_examples.html"
    output_file.parent.mkdir(parents=True, exist_ok=True)

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

    logger.info(f"✅ Report visuale salvato in {output_file}")

    return {
        'model_name': model_name,
        'output_file': str(output_file),
        'total_examples': len(scored_predictions),
        'selected_examples': len(selected_examples),
        'avg_quality': sum(ex['quality_score'] for ex in scored_predictions) / len(scored_predictions),
        'best_score': max(ex['quality_score'] for ex in scored_predictions),
        'worst_score': min(ex['quality_score'] for ex in scored_predictions)
    }

def main():
    parser = argparse.ArgumentParser(description='Genera esempi qualitativi VISUALI per modelli baseline')
    parser.add_argument('--test_file', default='data/processed/xml_format/test_set_final_xml_reduced_rgb.json',
                       help='File dati di test con SVG')
    parser.add_argument('--baseline_dir', default='experiments/baseline_svg_fixed_final',
                       help='Directory con predizioni baseline')
    parser.add_argument('--output_dir', default='experiments/xml_direct_input/outputs/visual_qualitative_baseline',
                       help='Directory output')
    parser.add_argument('--models', nargs='+',
                       choices=['idefix', 'flores2', 'blip2'],
                       default=['idefix', 'flores2', 'blip2'],
                       help='Modelli da analizzare')
    parser.add_argument('--num_examples', type=int, default=15,
                       help='Numero di esempi per modello')

    args = parser.parse_args()

    # Carica dati di test
    test_data = load_test_data(args.test_file)
    if not test_data:
        logger.error("Impossibile caricare dati di test")
        return

    # Mappa nomi modelli
    model_mapping = {
        'idefix': 'baseline_idefix_svg_fixed',
        'flores2': 'baseline_flores2_svg_fixed',
        'blip2': 'baseline_blip2_svg_fixed'
    }

    model_names = {
        'idefix': 'Ide Fix 3',
        'flores2': 'Flores 2 base',
        'blip2': 'BLIP 2.7B'
    }

    # Genera report per ogni modello
    all_reports = {}

    for model in args.models:
        model_dir = model_mapping[model]
        predictions_file = Path(args.baseline_dir) / model_dir / 'predictions.jsonl'

        if not predictions_file.exists():
            logger.warning(f"File predizioni non trovato: {predictions_file}")
            continue

        predictions = load_baseline_predictions(predictions_file)
        if not predictions:
            continue

        report = generate_visual_qualitative_report(
            model_names[model], predictions, test_data, args.output_dir, args.num_examples
        )

        if report:
            all_reports[model_names[model]] = report

    # Stampa riassunto
    print("\n" + "="*80)
    print("🎨 ESEMPI QUALITATIVI VISUALI - RIASSUNTO")
    print("="*80)

    for model_name, report in all_reports.items():
        print(f"\n🎯 {model_name}:")
        print(f"   📊 Esempi totali: {report['total_examples']}")
        print(f"   📋 Esempi selezionati: {report['selected_examples']}")
        print(f"   📈 Qualità media: {report['avg_quality']:.4f}")
        print(f"   🏆 Migliore: {report['best_score']:.4f}")
        print(f"   ❌ Peggiore: {report['worst_score']:.4f}")
        print(f"   📄 File HTML: {report['output_file']}")

    print("\n" + "="*80)
    print("✅ Esempi qualitativi visuali generati con successo!")
    print("🌐 Apri i file HTML nel browser per vedere gli SVG renderizzati!")

if __name__ == "__main__":
    main()
