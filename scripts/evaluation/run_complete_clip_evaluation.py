#!/usr/bin/env python3
"""
Script completo per CLIP evaluation di tutti i modelli:
- 6 Baseline models (BLIP, BLIP2, ViT-GPT2, GIT-base, Ide Fix 3, Flores 2)
- Llama 3.1 8B fine-tuned
- Gemma 2 9B fine-tuned

Genera inferenza dai checkpoint e calcola CLIP scores per confronto completo.
"""

import argparse
import json
import os
import sys
import time
import subprocess
from pathlib import Path
from datetime import datetime
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def wait_for_baseline_completion():
    """Attende che la baseline evaluation sia completata."""
    baseline_dir = Path("experiments/baseline_evaluation")
    expected_models = ['blip', 'vit-gpt2', 'blip2', 'git-base', 'idefix', 'flores2']

    logger.info("🔍 Controllo completamento baseline evaluation...")

    while True:
        completed_models = []
        for model in expected_models:
            metrics_file = baseline_dir / f"baseline_{model}" / "metrics.json"
            if metrics_file.exists():
                completed_models.append(model)

        logger.info(f"📊 Baseline completati: {len(completed_models)}/{len(expected_models)} - {completed_models}")

        if len(completed_models) == len(expected_models):
            logger.info("✅ Baseline evaluation completata!")
            return True

        logger.info("⏳ Attendo completamento baseline... (controllo ogni 2 minuti)")
        time.sleep(120)  # Attende 2 minuti

def run_inference_from_checkpoint(model_name, checkpoint_path, output_file, test_file):
    """Esegue inferenza da un checkpoint specifico."""
    logger.info(f"🚀 Avvio inferenza {model_name} da {checkpoint_path}")

    cmd = [
        "python", "scripts/inference/run_inference_from_checkpoints.py",
        "--checkpoint_path", checkpoint_path,
        "--test_file", test_file,
        "--output_file", output_file,
        "--device", "auto",
        "--max_length", "150",
        "--temperature", "0.7",
        "--top_p", "0.9",
        "--top_k", "50"
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1 ora timeout

        if result.returncode == 0:
            logger.info(f"✅ Inferenza {model_name} completata: {output_file}")
            return True
        else:
            logger.error(f"❌ Errore inferenza {model_name}: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logger.error(f"⏰ Timeout inferenza {model_name}")
        return False
    except Exception as e:
        logger.error(f"💥 Errore esecuzione inferenza {model_name}: {e}")
        return False

def run_clip_evaluation(inference_files, baseline_dir, output_dir, test_file):
    """Esegue CLIP evaluation su tutti i modelli."""
    logger.info("🎯 Avvio CLIP evaluation completa...")

    # Prepara lista di tutti i file di inferenza
    all_models = {}

    # Aggiungi baseline models
    baseline_models = ['blip', 'vit-gpt2', 'blip2', 'git-base', 'idefix', 'flores2']
    for model in baseline_models:
        predictions_file = baseline_dir / f"baseline_{model}" / "predictions.jsonl"
        if predictions_file.exists():
            all_models[f"baseline_{model}"] = str(predictions_file)
            logger.info(f"📊 Aggiunto baseline: {model}")

    # Aggiungi modelli fine-tuned
    for model_name, file_path in inference_files.items():
        if Path(file_path).exists():
            all_models[f"finetuned_{model_name}"] = file_path
            logger.info(f"🔥 Aggiunto fine-tuned: {model_name}")

    logger.info(f"📈 Totale modelli per CLIP evaluation: {len(all_models)}")

    # Esegui CLIP evaluation
    cmd = [
        "python", "clip_module/clip_score_multi_models.py",
        "--test_file", test_file,
        "--output_dir", output_dir,
        "--batch_size", "8",
        "--models_data", json.dumps(all_models)
    ]

    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=7200)  # 2 ore timeout

        if result.returncode == 0:
            logger.info("✅ CLIP evaluation completata!")
            return True
        else:
            logger.error(f"❌ Errore CLIP evaluation: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        logger.error("⏰ Timeout CLIP evaluation")
        return False
    except Exception as e:
        logger.error(f"💥 Errore esecuzione CLIP evaluation: {e}")
        return False

def generate_comparison_report(output_dir):
    """Genera report di confronto finale."""
    logger.info("📋 Generazione report di confronto...")

    # Carica risultati CLIP
    clip_results_file = Path(output_dir) / "clip_scores_summary.json"
    if not clip_results_file.exists():
        logger.error("❌ File risultati CLIP non trovato")
        return False

    with open(clip_results_file) as f:
        clip_results = json.load(f)

    # Genera report
    report = {
        "timestamp": datetime.now().isoformat(),
        "evaluation_type": "complete_clip_evaluation",
        "models_evaluated": len(clip_results),
        "results": clip_results,
        "summary": {
            "best_model": max(clip_results.items(), key=lambda x: x[1].get('clip_score_mean', 0)),
            "baseline_avg": sum([v.get('clip_score_mean', 0) for k, v in clip_results.items() if k.startswith('baseline_')]) /
                           len([k for k in clip_results.keys() if k.startswith('baseline_')]),
            "finetuned_avg": sum([v.get('clip_score_mean', 0) for k, v in clip_results.items() if k.startswith('finetuned_')]) /
                            max(1, len([k for k in clip_results.keys() if k.startswith('finetuned_')]))
        }
    }

    # Salva report
    report_file = Path(output_dir) / "complete_evaluation_report.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)

    logger.info(f"📊 Report salvato: {report_file}")

    # Log risultati principali
    logger.info("🏆 RISULTATI FINALI:")
    logger.info(f"   Miglior modello: {report['summary']['best_model'][0]} (CLIP: {report['summary']['best_model'][1].get('clip_score_mean', 0):.4f})")
    logger.info(f"   Media baseline: {report['summary']['baseline_avg']:.4f}")
    logger.info(f"   Media fine-tuned: {report['summary']['finetuned_avg']:.4f}")

    return True

def main():
    parser = argparse.ArgumentParser(description='CLIP evaluation completa di tutti i modelli')
    parser.add_argument('--test_file', default='data/processed/xml_format/test_set_final_xml_reduced_rgb.json',
                       help='File di test per evaluation')
    parser.add_argument('--output_dir', default='experiments/complete_clip_evaluation',
                       help='Directory output per risultati')
    parser.add_argument('--llama_checkpoint',
                       default='experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/checkpoint-23900',
                       help='Checkpoint Llama da usare')
    parser.add_argument('--gemma_checkpoint',
                       default='experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/checkpoint-36200',
                       help='Checkpoint Gemma da usare')
    parser.add_argument('--skip_baseline_wait', action='store_true',
                       help='Salta attesa baseline evaluation')
    parser.add_argument('--skip_inference', action='store_true',
                       help='Salta generazione inferenza (usa file esistenti)')

    args = parser.parse_args()

    # Crea directory output
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    logger.info("🎯 AVVIO CLIP EVALUATION COMPLETA")
    logger.info(f"   Test file: {args.test_file}")
    logger.info(f"   Output dir: {args.output_dir}")
    logger.info(f"   Llama checkpoint: {args.llama_checkpoint}")
    logger.info(f"   Gemma checkpoint: {args.gemma_checkpoint}")

    # Step 1: Attendi baseline completion
    if not args.skip_baseline_wait:
        if not wait_for_baseline_completion():
            logger.error("❌ Errore nell'attesa baseline completion")
            return 1

    # Step 2: Genera inferenza dai checkpoint
    inference_files = {}

    if not args.skip_inference:
        # Inferenza Llama
        llama_output = output_dir / "llama_inference_results.jsonl"
        if run_inference_from_checkpoint("llama", args.llama_checkpoint, str(llama_output), args.test_file):
            inference_files["llama"] = str(llama_output)

        # Inferenza Gemma
        gemma_output = output_dir / "gemma_inference_results.jsonl"
        if run_inference_from_checkpoint("gemma", args.gemma_checkpoint, str(gemma_output), args.test_file):
            inference_files["gemma"] = str(gemma_output)
    else:
        # Usa file esistenti
        llama_output = output_dir / "llama_inference_results.jsonl"
        gemma_output = output_dir / "gemma_inference_results.jsonl"
        if llama_output.exists():
            inference_files["llama"] = str(llama_output)
        if gemma_output.exists():
            inference_files["gemma"] = str(gemma_output)

    logger.info(f"📁 File inferenza generati: {len(inference_files)}")

    # Step 3: CLIP evaluation
    baseline_dir = Path("experiments/baseline_evaluation")
    if not run_clip_evaluation(inference_files, baseline_dir, str(output_dir), args.test_file):
        logger.error("❌ Errore nella CLIP evaluation")
        return 1

    # Step 4: Report finale
    if not generate_comparison_report(str(output_dir)):
        logger.error("❌ Errore nella generazione report")
        return 1

    logger.info("🎉 CLIP EVALUATION COMPLETA TERMINATA CON SUCCESSO!")
    return 0

if __name__ == '__main__':
    sys.exit(main())
