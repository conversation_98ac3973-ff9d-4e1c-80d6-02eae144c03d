#!/usr/bin/env python3
"""
Script per creare un grafico radar completo che confronta:
- <PERSON>li baseline (Ide Fix 3, Flores 2 base, BLIP 2.7B)
- <PERSON><PERSON> fine-tuned (Llama 3.1 8B, Gemma 2 9B IT)

Metriche: BLEU-1, BLEU-2, BLEU-3, BLEU-4, METEOR, CIDEr
(CLIP Score rimosso dalla visualizzazione)
"""

import json
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Backend non-interattivo
import matplotlib.pyplot as plt
from math import pi
import argparse
import os

def load_baseline_metrics(file_path):
    """Carica le metriche baseline dal file JSON."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Errore nel caricamento delle metriche baseline: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Crea grafico radar completo con baseline e fine-tuned models')
    parser.add_argument('--baseline_file',
                       default='experiments/baseline_metrics_real/all_baseline_metrics.json',
                       help='File JSON con metriche baseline')
    parser.add_argument('--output_file',
                       default='experiments/baseline_metrics_real/complete_radar_chart.png',
                       help='File output per il grafico')

    args = parser.parse_args()

    # Carica metriche baseline
    baseline_data = load_baseline_metrics(args.baseline_file)
    if not baseline_data:
        print("❌ Impossibile caricare le metriche baseline")
        return

    # Metriche reali per i modelli fine-tuned (da evaluation_results/models_comparison_final.json)
    llama_metrics = {
        "BLEU-1": 0.4680,  # llama31_8b_finetuned
        "BLEU-2": 0.2770,
        "BLEU-3": 0.3326,
        "BLEU-4": 0.1848,
        "METEOR": 0.2754,
        "CIDEr": 0.8803
    }

    gemma_metrics = {
        "BLEU-1": 0.4102,  # gemma2_9b_it_finetuned
        "BLEU-2": 0.4245,
        "BLEU-3": 0.3267,
        "BLEU-4": 0.2254,
        "METEOR": 0.3322,
        "CIDEr": 0.8534
    }

    # Setup dati
    metrics_names = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr']

    # Mapping modelli baseline
    baseline_mapping = {
        'idefix': 'Ide Fix 3',
        'flores2': 'Flores 2 base',
        'blip2': 'BLIP 2.7B'
    }

    # Prepara dati per il grafico
    models_data = {}

    # Aggiungi modelli baseline
    for model_key, model_name in baseline_mapping.items():
        if model_key in baseline_data:
            model_metrics = baseline_data[model_key]['metrics']
            values = [
                model_metrics['BLEU-1']['mean'],
                model_metrics['BLEU-2']['mean'],
                model_metrics['BLEU-3']['mean'],
                model_metrics['BLEU-4']['mean'],
                model_metrics['METEOR']['mean'],
                model_metrics['CIDEr']['mean']
            ]
            models_data[model_name] = values

    # Aggiungi modelli fine-tuned
    models_data['Llama 3.1 8B'] = [
        llama_metrics['BLEU-1'],
        llama_metrics['BLEU-2'],
        llama_metrics['BLEU-3'],
        llama_metrics['BLEU-4'],
        llama_metrics['METEOR'],
        llama_metrics['CIDEr']
    ]

    models_data['Gemma 2 9B IT'] = [
        gemma_metrics['BLEU-1'],
        gemma_metrics['BLEU-2'],
        gemma_metrics['BLEU-3'],
        gemma_metrics['BLEU-4'],
        gemma_metrics['METEOR'],
        gemma_metrics['CIDEr']
    ]

    print(f"📊 Modelli processati: {list(models_data.keys())}")

    # Crea grafico
    fig, ax = plt.subplots(figsize=(14, 12), subplot_kw=dict(projection='polar'))

    # Angoli
    angles = [n / float(len(metrics_names)) * 2 * pi for n in range(len(metrics_names))]
    angles += angles[:1]

    # Colori per i modelli
    colors = {
        'Ide Fix 3': '#FF6B6B',        # Rosso
        'Flores 2 base': '#4ECDC4',    # Turchese
        'BLIP 2.7B': '#45B7D1',        # Blu
        'Llama 3.1 8B': '#FFA500',     # Arancione
        'Gemma 2 9B IT': '#9B59B6'     # Viola
    }

    # Stili di linea
    line_styles = {
        'Ide Fix 3': '--',
        'Flores 2 base': '--',
        'BLIP 2.7B': '--',
        'Llama 3.1 8B': '-',
        'Gemma 2 9B IT': '-'
    }

    # Plotta ogni modello
    for model_name, values in models_data.items():
        values_plot = values + values[:1]  # Chiudi il cerchio

        ax.plot(angles, values_plot,
               linewidth=3,
               linestyle=line_styles[model_name],
               label=model_name,
               color=colors[model_name],
               marker='o',
               markersize=8)

        # Riempi l'area solo per i modelli fine-tuned
        if model_name in ['Llama 3.1 8B', 'Gemma 2 9B IT']:
            ax.fill(angles, values_plot,
                   color=colors[model_name],
                   alpha=0.15)

    # Configura grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics_names, fontsize=12, fontweight='bold')
    ax.set_ylim(0, 1.0)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=10, alpha=0.7)
    ax.grid(True, alpha=0.3)

    plt.title('Confronto Completo: Baseline vs Fine-tuned Models\nSVG Captioning Performance (senza CLIP Score)',
             size=16, fontweight='bold', pad=30)

    # Legenda con separazione baseline/fine-tuned
    legend_elements = []

    # Prima i baseline (linee tratteggiate)
    for model_name in ['Ide Fix 3', 'Flores 2 base', 'BLIP 2.7B']:
        if model_name in models_data:
            legend_elements.append(plt.Line2D([0], [0], color=colors[model_name],
                                            linewidth=3, linestyle='--',
                                            label=f'{model_name} (Baseline)'))

    # Poi i fine-tuned (linee continue)
    for model_name in ['Llama 3.1 8B', 'Gemma 2 9B IT']:
        if model_name in models_data:
            legend_elements.append(plt.Line2D([0], [0], color=colors[model_name],
                                            linewidth=3, linestyle='-',
                                            label=f'{model_name} (Fine-tuned)'))

    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1.3, 1.0),
              fontsize=11, frameon=True, fancybox=True, shadow=True)

    # Crea directory output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Salva
    plt.tight_layout()
    plt.savefig(args.output_file, dpi=300, bbox_inches='tight',
               facecolor='white', edgecolor='none')

    print(f"✅ Grafico salvato in {args.output_file}")

    # Stampa riassunto
    print("\n" + "="*80)
    print("📊 RIASSUNTO CONFRONTO COMPLETO")
    print("="*80)

    print("\n🏆 RANKING PER METRICA:")

    for i, metric in enumerate(metrics_names):
        print(f"\n{metric}:")
        metric_scores = [(name, values[i]) for name, values in models_data.items()]
        metric_scores.sort(key=lambda x: x[1], reverse=True)

        for rank, (name, score) in enumerate(metric_scores, 1):
            model_type = "Fine-tuned" if name in ['Llama 3.1 8B', 'Gemma 2 9B IT'] else "Baseline"
            print(f"  {rank}. {name:<15} {score:.4f} ({model_type})")

    print("\n" + "="*80)
    print("📝 Note:")
    print("   • Baseline: Modelli pre-addestrati senza fine-tuning")
    print("   • Fine-tuned: Modelli addestrati su dataset SVG specifico")
    print("   • CLIP Score rimosso per focus su metriche testuali")
    print("   • Linee tratteggiate: Baseline models")
    print("   • Linee continue: Fine-tuned models")

if __name__ == "__main__":
    main()
