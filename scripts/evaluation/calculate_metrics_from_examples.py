#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per calcolare le metriche dai file di esempi esistenti.
"""

import os
import sys
import json
import argparse
import logging
import numpy as np
import torch
import nltk
import ssl
from tqdm import tqdm
import wandb
from nltk.translate.bleu_score import corpus_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Configura SSL per NLTK
try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context

# Assicurati che NLTK abbia i dati necessari
nltk.download("punkt")
nltk.download("wordnet")
nltk.download("omw-1.4")
nltk.download("stopwords")

def parse_args():
    parser = argparse.ArgumentParser(description="Calcolo delle metriche dai file di esempi esistenti")
    parser.add_argument("--examples_file", type=str, required=True, help="File JSON con gli esempi generati")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i risultati")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner_zero_shot", help="Nome del progetto Weights & Biands")
    return parser.parse_args()

def calculate_caption_stats(hypotheses):
    """Calcola statistiche sulle didascalie generate."""
    # Lunghezza delle didascalie (in token)
    lengths = [len(nltk.word_tokenize(hyp)) for hyp in hypotheses]

    # Diversità del vocabolario
    all_tokens = []
    for hyp in hypotheses:
        all_tokens.extend(nltk.word_tokenize(hyp.lower()))

    unique_tokens = len(set(all_tokens))
    total_tokens = len(all_tokens)

    # Type-Token Ratio (misura di diversità lessicale)
    ttr = unique_tokens / total_tokens if total_tokens > 0 else 0

    # Self-BLEU (misura di diversità tra le didascalie)
    self_bleu = 0
    if len(hypotheses) > 1:
        self_bleu_scores = []
        for i, hyp in enumerate(hypotheses):
            other_hyps = [h for j, h in enumerate(hypotheses) if j != i]
            if other_hyps:
                # Prendi fino a 10 altre didascalie per efficienza
                other_hyps = other_hyps[:10]
                hyp_tokens = nltk.word_tokenize(hyp.lower())
                other_hyps_tokens = [nltk.word_tokenize(h.lower()) for h in other_hyps]

                # Calcola BLEU-4 tra questa didascalia e le altre
                smoothing = SmoothingFunction().method1
                bleu = corpus_bleu([other_hyps_tokens], [hyp_tokens],
                                   weights=(0.25, 0.25, 0.25, 0.25),
                                   smoothing_function=smoothing)
                self_bleu_scores.append(bleu)

        if self_bleu_scores:
            self_bleu = np.mean(self_bleu_scores)

    return {
        'caption_length_mean': np.mean(lengths),
        'caption_length_std': np.std(lengths),
        'caption_length_min': np.min(lengths),
        'caption_length_max': np.max(lengths),
        'vocabulary_size': unique_tokens,
        'type_token_ratio': ttr,
        'self_bleu': self_bleu
    }

def calculate_bleu(references, hypotheses):
    """Calcola il BLEU score utilizzando NLTK."""
    # Tokenizza le didascalie
    tokenized_refs = [[nltk.word_tokenize(ref.lower())] for ref in references]
    tokenized_hyps = [nltk.word_tokenize(hyp.lower()) for hyp in hypotheses]
    
    # Calcola BLEU-1, BLEU-2, BLEU-3, BLEU-4
    smoothing = SmoothingFunction().method1
    bleu1 = corpus_bleu(tokenized_refs, tokenized_hyps, weights=(1, 0, 0, 0), smoothing_function=smoothing)
    bleu2 = corpus_bleu(tokenized_refs, tokenized_hyps, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
    bleu3 = corpus_bleu(tokenized_refs, tokenized_hyps, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
    bleu4 = corpus_bleu(tokenized_refs, tokenized_hyps, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
    
    return {
        'bleu1': bleu1,
        'bleu2': bleu2,
        'bleu3': bleu3,
        'bleu4': bleu4
    }

def calculate_meteor(references, hypotheses):
    """Calcola il METEOR score utilizzando NLTK."""
    scores = []
    
    for ref, hyp in tqdm(zip(references, hypotheses), total=len(references), desc="Calcolo METEOR"):
        # Tokenizza le didascalie
        tokenized_ref = nltk.word_tokenize(ref.lower())
        tokenized_hyp = nltk.word_tokenize(hyp.lower())
        
        # Calcola METEOR
        score = meteor_score([tokenized_ref], tokenized_hyp)
        scores.append(score)
    
    return np.mean(scores)

def calculate_cider(references, hypotheses):
    """Calcola il CIDEr score utilizzando una implementazione semplificata."""
    # Implementazione semplificata di CIDEr
    # Questa è una versione molto semplificata e non è accurata come la versione completa
    # Per una valutazione più accurata, si dovrebbe utilizzare la libreria pycocoevalcap
    
    from collections import Counter
    import math
    
    def compute_cider(refs, hyps):
        # Calcola TF-IDF per ogni n-gram
        def compute_tf_idf(n_grams, n_grams_counts, ref_len):
            tf = Counter()
            for n_gram in n_grams:
                tf[n_gram] = tf.get(n_gram, 0) + 1
            
            # Normalizza TF
            for n_gram, count in tf.items():
                tf[n_gram] = count / float(len(n_grams))
            
            # Calcola IDF
            idf = {}
            for n_gram in tf:
                idf[n_gram] = math.log(float(ref_len) / (n_grams_counts.get(n_gram, 0) + 1.0))
            
            # Calcola TF-IDF
            tf_idf = {}
            for n_gram in tf:
                tf_idf[n_gram] = tf[n_gram] * idf[n_gram]
            
            return tf_idf
        
        # Estrai n-grams
        def get_n_grams(tokens, n):
            return [tuple(tokens[i:i+n]) for i in range(len(tokens) - n + 1)]
        
        # Tokenizza le didascalie
        tokenized_refs = [nltk.word_tokenize(ref.lower()) for ref in refs]
        tokenized_hyp = nltk.word_tokenize(hyps.lower())
        
        # Calcola n-grams per le didascalie di riferimento
        ref_n_grams = []
        for n in range(1, 5):  # n-grams da 1 a 4
            ref_n_grams.append([get_n_grams(ref, n) for ref in tokenized_refs])
        
        # Calcola n-grams per la didascalia generata
        hyp_n_grams = []
        for n in range(1, 5):  # n-grams da 1 a 4
            hyp_n_grams.append(get_n_grams(tokenized_hyp, n))
        
        # Calcola i conteggi degli n-grams nelle didascalie di riferimento
        ref_n_grams_counts = []
        for n in range(4):
            counts = Counter()
            for ref in ref_n_grams[n]:
                counts.update(ref)
            ref_n_grams_counts.append(counts)
        
        # Calcola CIDEr per ogni n
        cider_n = []
        for n in range(4):
            # Calcola TF-IDF per la didascalia generata
            hyp_tf_idf = compute_tf_idf(hyp_n_grams[n], ref_n_grams_counts[n], len(refs))
            
            # Calcola TF-IDF per le didascalie di riferimento
            ref_tf_idfs = []
            for ref in ref_n_grams[n]:
                ref_tf_idf = compute_tf_idf(ref, ref_n_grams_counts[n], len(refs))
                ref_tf_idfs.append(ref_tf_idf)
            
            # Calcola la similarità coseno
            cider_n_i = []
            for ref_tf_idf in ref_tf_idfs:
                # Calcola il prodotto scalare
                numerator = 0.0
                for n_gram, tf_idf in hyp_tf_idf.items():
                    numerator += tf_idf * ref_tf_idf.get(n_gram, 0)
                
                # Calcola le norme
                hyp_norm = math.sqrt(sum([tf_idf ** 2 for tf_idf in hyp_tf_idf.values()]))
                ref_norm = math.sqrt(sum([tf_idf ** 2 for tf_idf in ref_tf_idf.values()]))
                
                # Calcola la similarità coseno
                if hyp_norm > 0 and ref_norm > 0:
                    cider_n_i.append(numerator / (hyp_norm * ref_norm))
                else:
                    cider_n_i.append(0.0)
            
            # Prendi il massimo tra le didascalie di riferimento
            if cider_n_i:
                cider_n.append(max(cider_n_i))
            else:
                cider_n.append(0.0)
        
        # Calcola CIDEr come media dei CIDEr per ogni n
        cider = sum(cider_n) / len(cider_n) if cider_n else 0.0
        
        return cider
    
    # Calcola CIDEr per ogni coppia di didascalie
    scores = []
    for ref, hyp in tqdm(zip(references, hypotheses), total=len(references), desc="Calcolo CIDEr"):
        score = compute_cider([ref], hyp)
        scores.append(score)
    
    return np.mean(scores) * 10.0  # Moltiplica per 10 per allinearsi alla scala standard di CIDEr

def calculate_metrics_from_examples(examples_file, output_dir, wandb_entity, wandb_project):
    """Calcola le metriche dai file di esempi esistenti."""
    # Crea la directory di output
    os.makedirs(output_dir, exist_ok=True)
    
    # Carica gli esempi
    with open(examples_file, "r") as f:
        examples = json.load(f)
    
    # Estrai il nome del modello dal nome del file
    model_name = os.path.basename(examples_file).split("_zero_shot")[0]
    wandb_run_name = f"{model_name}_zero_shot_metrics"
    
    # Inizializza wandb
    wandb.init(
        entity=wandb_entity,
        project=wandb_project,
        name=wandb_run_name,
        config={
            "model_name": model_name,
            "num_samples": len(examples)
        }
    )
    
    # Estrai le didascalie di riferimento e generate
    references = [example["true_caption"] for example in examples]
    hypotheses = [example["generated_caption"] for example in examples]
    inference_times = [example["inference_time"] for example in examples]
    
    # Calcola le metriche BLEU
    logger.info(f"Calcolo delle metriche BLEU per {model_name}")
    bleu_metrics = calculate_bleu(references, hypotheses)
    
    # Calcola METEOR
    logger.info(f"Calcolo di METEOR per {model_name}")
    meteor = calculate_meteor(references, hypotheses)
    
    # Calcola CIDEr
    logger.info(f"Calcolo di CIDEr per {model_name}")
    cider = calculate_cider(references, hypotheses)
    
    # Calcola statistiche sulle didascalie
    logger.info(f"Calcolo delle statistiche sulle didascalie per {model_name}")
    caption_stats = calculate_caption_stats(hypotheses)
    
    # Statistiche sul tempo di inferenza
    inference_time_stats = {
        "inference_time_mean": np.mean(inference_times),
        "inference_time_std": np.std(inference_times),
        "inference_time_min": np.min(inference_times),
        "inference_time_max": np.max(inference_times),
        "inference_time_total": np.sum(inference_times)
    }
    
    # Combina tutte le metriche
    metrics = {
        **bleu_metrics,
        "meteor": meteor,
        "cider": cider,
        **caption_stats,
        **inference_time_stats,
        "num_samples": len(examples)
    }
    
    # Salva le metriche
    metrics_file = os.path.join(output_dir, f"{model_name}_zero_shot_metrics.json")
    with open(metrics_file, "w") as f:
        json.dump(metrics, f, indent=2)
    
    # Log delle metriche su wandb
    wandb.log(metrics)
    
    # Chiudi wandb
    wandb.finish()
    
    logger.info(f"Metriche calcolate e salvate in: {metrics_file}")
    
    return metrics

def main():
    args = parse_args()
    
    # Calcola le metriche dai file di esempi esistenti
    metrics = calculate_metrics_from_examples(
        examples_file=args.examples_file,
        output_dir=args.output_dir,
        wandb_entity=args.wandb_entity,
        wandb_project=args.wandb_project
    )
    
    # Stampa le metriche principali
    print("\nRisultati del calcolo delle metriche:")
    print(f"BLEU-1: {metrics['bleu1']:.4f}")
    print(f"BLEU-4: {metrics['bleu4']:.4f}")
    print(f"METEOR: {metrics['meteor']:.4f}")
    print(f"CIDEr: {metrics['cider']:.4f}")
    print(f"Tempo medio di inferenza: {metrics['inference_time_mean']:.4f}s")

if __name__ == "__main__":
    main()
