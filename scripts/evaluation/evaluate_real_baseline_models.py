#!/usr/bin/env python3
"""
Script per valutare modelli baseline reali su dataset SVG.
Implementa: IDEFICS 3, Florence 2 Base, BLIP-2
"""

import argparse
import json
import os
from pathlib import Path
from tqdm import tqdm
import torch
from PIL import Image
import cairosvg
import io
import numpy as np
from nltk.translate.bleu_score import corpus_bleu
from nltk.translate.meteor_score import meteor_score
from rouge import Rouge
import wandb
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def convert_rgba_to_rgb(image):
    """
    Converte un'immagine da RGBA a RGB con sfondo bianco
    """
    if image.mode == 'RGBA':
        # Crea sfondo bianco
        bg = Image.new('RGB', image.size, (255, 255, 255))
        # Applica l'immagine RGBA sullo sfondo bianco
        bg.paste(image, mask=image.split()[3])  # Usa il canale alpha come mask
        return bg
    elif image.mode == 'P':
        # Converte palette in RGBA poi in RGB
        image = image.convert('RGBA')
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3] if len(image.split()) == 4 else None)
        return bg
    else:
        # Assicura che sia RGB
        return image.convert('RGB')

def rasterize_svg(svg_data, dpi=150):
    """Converte SVG in immagine PNG con sfondo bianco."""
    try:
        png_data = cairosvg.svg2png(bytestring=svg_data.encode('utf-8'), dpi=dpi)
        image = Image.open(io.BytesIO(png_data))
        # Converte sempre a RGB con sfondo bianco
        return convert_rgba_to_rgb(image)
    except Exception as e:
        logger.error(f"Errore durante la rasterizzazione SVG: {e}")
        return None

def load_model_and_processor(model_name):
    """Carica il modello e il processor specificato."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    if model_name == "blip2":
        from transformers import Blip2Processor, Blip2ForConditionalGeneration
        processor = Blip2Processor.from_pretrained("Salesforce/blip2-opt-2.7b")
        model = Blip2ForConditionalGeneration.from_pretrained("Salesforce/blip2-opt-2.7b", torch_dtype=torch.float16)
        model.to(device)
        return model, processor, device

    elif model_name == "idefics3":
        from transformers import AutoProcessor, Idefics2ForConditionalGeneration
        try:
            # Corretto per usare IDEFICS 2 con la classe e il dtype corretti
            processor = AutoProcessor.from_pretrained("HuggingFaceM4/idefics2-8b-chatty", trust_remote_code=True)
            model = Idefics2ForConditionalGeneration.from_pretrained(
                "HuggingFaceM4/idefics2-8b-chatty",
                torch_dtype=torch.float16,
                trust_remote_code=True
            ).to(device)
            return model, processor, device
        except Exception as e:
            logger.error(f"Impossibile caricare IDEFICS 2: {e}")
            return None, None, device

    elif model_name == "florence2":
        from transformers import AutoProcessor, Florence2ForConditionalGeneration
        try:
            # Corretto per usare Florence 2 Base con la classe e il dtype corretti
            processor = AutoProcessor.from_pretrained("microsoft/Florence-2-base", trust_remote_code=True)
            model = Florence2ForConditionalGeneration.from_pretrained(
                "microsoft/Florence-2-base",
                torch_dtype=torch.float16,
                trust_remote_code=True
            ).to(device)
            return model, processor, device
        except Exception as e:
            logger.error(f"Impossibile caricare Florence 2: {e}")
            return None, None, device

    else:
        raise ValueError(f"Modello non supportato: {model_name}")

def generate_caption(model, processor, image, model_name, device):
    """Genera caption per un'immagine usando il modello specificato."""
    try:
        if model_name == "blip2":
            inputs = processor(image, return_tensors="pt").to(device)
            generated_ids = model.generate(**inputs, max_length=50, num_beams=5)
            caption = processor.batch_decode(generated_ids, skip_special_tokens=True)[0].strip()

        elif model_name == "idefics3":
            # Logica di generazione corretta per IDEFICS-2
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image"},
                        {"type": "text", "text": "Describe this image in detail."},
                    ]
                }
            ]
            prompt = processor.apply_chat_template(messages, add_generation_prompt=True)
            inputs = processor(text=prompt, images=[image], return_tensors="pt").to(device)
            
            generated_ids = model.generate(**inputs, max_new_tokens=150)
            generated_texts = processor.batch_decode(generated_ids, skip_special_tokens=True)
            
            # Estrae la risposta del modello dal template
            raw_caption = generated_texts[0]
            if "assistant\n" in raw_caption:
                caption = raw_caption.split("assistant\n", 1)[1]
            else:
                caption = raw_caption # Fallback se il template non matcha

        elif model_name == "florence2":
            # Logica di generazione corretta per Florence-2
            prompt = "<OD>" # Task di object detection / captioning
            inputs = processor(text=prompt, images=image, return_tensors="pt")
            inputs = {k: v.to(device) for k, v in inputs.items()}

            generated_ids = model.generate(
                input_ids=inputs["input_ids"],
                pixel_values=inputs["pixel_values"],
                max_new_tokens=150,
                num_beams=3
            )
            generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
            
            # Post-processing specifico del modello
            parsed_answer = processor.post_process_generation(generated_text, task=prompt, image_size=image.size)
            caption = parsed_answer.get(prompt, "Failed to parse caption")

        else:
            raise ValueError(f"Modello non supportato: {model_name}")

        return caption.strip()

    except Exception as e:
        logger.error(f"Errore nella generazione caption con {model_name}: {e}")
        return ""

def evaluate_model(model_name, test_data, output_dir, batch_size=8):
    """Valuta un modello baseline sul dataset di test."""
    logger.info(f"Valutazione modello {model_name}...")

    # Carica modello e processor
    model, processor, device = load_model_and_processor(model_name)
    if model is None:
        logger.error(f"Impossibile caricare il modello {model_name}. Salto la valutazione.")
        return
        
    model.eval()

    # Prepara le metriche
    references = []
    hypotheses = []
    results_data = []

    # Processa i dati
    for i, item in enumerate(tqdm(test_data, desc=f"Evaluating {model_name}")):
        # Rasterizza SVG
        if 'xml' in item:
            svg_data = item['xml']
        elif 'svg_xml' in item:
            svg_data = item['svg_xml']
        else:
            logger.warning(f"Nessun campo SVG trovato nell'esempio {i}")
            continue

        image = rasterize_svg(svg_data)
        if image is None:
            continue

        # Genera caption
        with torch.no_grad():
            generated_caption = generate_caption(model, processor, image, model_name, device)

        # Salva risultati
        reference_caption = item.get('caption', item.get('reference_caption', ''))
        references.append(reference_caption)
        hypotheses.append(generated_caption)

        results_data.append({
            'id': item.get('id', item.get('example_id', f'example_{i}')),
            'reference': reference_caption,
            'generated': generated_caption
        })

        # Log ogni 50 esempi
        if (i + 1) % 50 == 0:
            logger.info(f"Processati {i + 1}/{len(test_data)} esempi")

    # Calcola metriche
    logger.info("Calcolo metriche...")

    # BLEU
    bleu = corpus_bleu([[ref.split()] for ref in references], [hyp.split() for hyp in hypotheses])

    # METEOR
    meteor = np.mean([meteor_score([ref], hyp) for ref, hyp in zip(references, hypotheses)])

    # ROUGE
    rouge = Rouge()
    rouge_scores = rouge.get_scores(hypotheses, references, avg=True)

    # Risultati finali
    results = {
        'model': model_name,
        'metrics': {
            'bleu': bleu,
            'meteor': meteor,
            'rouge-1': rouge_scores['rouge-1']['f'],
            'rouge-2': rouge_scores['rouge-2']['f'],
            'rouge-l': rouge_scores['rouge-l']['f']
        },
        'predictions': results_data
    }

    # Salva risultati
    output_path = Path(output_dir) / f"baseline_{model_name}"
    output_path.mkdir(parents=True, exist_ok=True)

    with open(output_path / 'metrics.json', 'w') as f:
        json.dump(results['metrics'], f, indent=2)

    with open(output_path / 'predictions.jsonl', 'w') as f:
        for pred in results['predictions']:
            f.write(json.dumps(pred) + '\n')

    logger.info(f"Risultati salvati in {output_path}")
    logger.info(f"Metriche {model_name}: BLEU={bleu:.4f}, METEOR={meteor:.4f}, ROUGE-L={rouge_scores['rouge-l']['f']:.4f}")

    return results

def main():
    """Funzione principale per eseguire lo script."""
    parser = argparse.ArgumentParser(description="Valuta modelli baseline reali.")
    parser.add_argument("--models", nargs='+', required=True,
                        choices=["idefics3", "florence2", "blip2"],
                        help="Nomi dei modelli da valutare.")
    parser.add_argument("--test_file", type=str, required=True, 
                        help="Path del file JSONL di test.")
    parser.add_argument("--output_dir", type=str, required=True,
                        help="Directory dove salvare i risultati.")
    parser.add_argument("--batch_size", type=int, default=8,
                        help="Dimensione del batch per la valutazione.")
    parser.add_argument("--wandb_project", type=str, default="svg_captioning_baselines",
                        help="Nome del progetto Wandb.")
    parser.add_argument("--wandb_run_name", type=str, default=None,
                        help="Nome specifico per il run Wandb (opzionale).")

    args = parser.parse_args()

    # Carica il dataset di test
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)

    # Configura Wandb se specificato
    if args.wandb_project:
        run_name = args.wandb_run_name if args.wandb_run_name else f"eval_{'_'.join(args.models)}_{Path(args.test_file).stem}"
        wandb.init(project=args.wandb_project, name=run_name, config=args)

    # Valuta ogni modello specificato
    for model_name in args.models:
        evaluate_model(model_name, test_data, args.output_dir, args.batch_size)
        torch.cuda.empty_cache() # Pulisce la cache GPU tra un modello e l'altro

    if args.wandb_project:
        wandb.finish()

if __name__ == "__main__":
    main()
