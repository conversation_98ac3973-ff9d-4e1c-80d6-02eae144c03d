#!/usr/bin/env python3
"""
Script per ricalcolare le baseline con il dataset TEST_5 ottimizzato
"""

import json
import os
import sys
from pathlib import Path
import argparse

def main():
    print("📊 RICALCOLO BASELINE CON DATASET TEST_5")
    print("=" * 50)
    
    # Setup paths
    base_dir = Path("/work/tesi_ediluzio")
    os.chdir(base_dir)
    
    # Verifica dataset TEST_5
    test_dataset = "data/processed/xml_format_optimized/test_set_test5_3k.json"
    if not os.path.exists(test_dataset):
        print(f"❌ Dataset test non trovato: {test_dataset}")
        sys.exit(1)
    
    # Carica dataset
    with open(test_dataset, 'r') as f:
        test_data = json.load(f)
    
    print(f"✅ Dataset TEST_5 caricato: {len(test_data)} esempi")
    
    # Modelli baseline da valutare
    baseline_models = [
        {
            "name": "BLIP",
            "model_id": "Salesforce/blip-image-captioning-base",
            "type": "blip"
        },
        {
            "name": "BLIP2_2.7B", 
            "model_id": "Salesforce/blip2-opt-2.7b",
            "type": "blip2"
        },
        {
            "name": "ViT_GPT2",
            "model_id": "nlpconnect/vit-gpt2-image-captioning", 
            "type": "vit_gpt2"
        },
        {
            "name": "GIT_base",
            "model_id": "microsoft/git-base",
            "type": "git"
        },
        {
            "name": "Ide_Fix_3",
            "model_id": "custom_ide_fix_3",
            "type": "custom"
        },
        {
            "name": "Flores_2_base",
            "model_id": "custom_flores_2",
            "type": "custom"
        }
    ]
    
    print(f"\\n📋 Modelli baseline da valutare: {len(baseline_models)}")
    for model in baseline_models:
        print(f"   - {model['name']}: {model['model_id']}")
    
    # Crea script di valutazione
    evaluation_script = f'''#!/bin/bash
#SBATCH --job-name=baseline_test5
#SBATCH --output=logs/baseline_test5_%j.out
#SBATCH --error=logs/baseline_test5_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=4
#SBATCH --mem=32G
#SBATCH --time=12:00:00

echo "📊 BASELINE EVALUATION TEST_5 DATASET"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"

# Crea environment separato per baseline
if [ ! -d "baseline_env" ]; then
    python -m venv baseline_env
    source baseline_env/bin/activate
    pip install --upgrade pip
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    pip install transformers datasets accelerate
    pip install pillow requests beautifulsoup4
    pip install nltk rouge-score sacrebleu
    pip install clip-by-openai
else
    source baseline_env/bin/activate
fi

cd /work/tesi_ediluzio

# Configurazione
export CUDA_VISIBLE_DEVICES=0
export HF_TOKEN="*************************************"

echo "🔍 Valutazione baseline su dataset TEST_5..."
echo "Dataset: {test_dataset}"
echo "Esempi: {len(test_data)}"

# Esegui valutazione per ogni modello baseline
python scripts/evaluation/evaluate_baseline_models.py \\
    --test_dataset {test_dataset} \\
    --output_dir results/baseline_test5_evaluation \\
    --models_config baseline_models_config.json \\
    --batch_size 16 \\
    --max_samples 1000

echo "✅ Baseline evaluation completata!"
'''
    
    # Salva script
    script_path = "scripts/slurm/baseline_test5_evaluation.slurm"
    with open(script_path, 'w') as f:
        f.write(evaluation_script)
    
    os.chmod(script_path, 0o755)
    print(f"✅ Script baseline creato: {script_path}")
    
    # Crea configurazione modelli
    models_config = {
        "models": baseline_models,
        "dataset": test_dataset,
        "metrics": ["BLEU-1", "BLEU-2", "BLEU-3", "BLEU-4", "METEOR", "ROUGE-L", "CIDEr", "CLIP"],
        "output_format": "json",
        "save_predictions": True
    }
    
    config_path = "baseline_models_config.json"
    with open(config_path, 'w') as f:
        json.dump(models_config, f, indent=2)
    
    print(f"✅ Configurazione modelli creata: {config_path}")
    
    print(f"\\n🎯 PROSSIMI PASSI:")
    print(f"1. Lanciare valutazione baseline:")
    print(f"   sbatch {script_path}")
    print(f"2. Attendere completamento training TEST_5")
    print(f"3. Valutare modelli TEST_5 finali")
    print(f"4. Confrontare risultati")
    
    print(f"\\n📊 METRICHE DA CALCOLARE:")
    for metric in models_config["metrics"]:
        print(f"   - {metric}")
    
    return models_config

if __name__ == "__main__":
    config = main()
