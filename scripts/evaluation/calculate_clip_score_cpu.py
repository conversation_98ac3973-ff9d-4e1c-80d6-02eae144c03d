import os
import json
import argparse
from pathlib import Path
import torch
from PIL import Image
import cairosvg
import io
from tqdm import tqdm
import wandb
from transformers import CLIPProcessor, CLIPModel

def rasterize_svg(svg_data, dpi=150):
    """Converte SVG in immagine PNG."""
    png_data = cairosvg.svg2png(bytestring=svg_data.encode('utf-8'), dpi=dpi)
    return Image.open(io.BytesIO(png_data))

def calculate_clip_score(model, processor, image, text, device="cpu"):
    """Calcola il CLIP score tra un'immagine e un testo."""
    # Prepara l'input
    inputs = processor(
        images=image,
        text=text,
        return_tensors="pt",
        padding=True
    ).to(device)
    
    # Calcola gli embedding
    with torch.no_grad():
        image_features = model.get_image_features(**inputs)
        text_features = model.get_text_features(**inputs)
    
    # Normalizza gli embedding
    image_features = image_features / image_features.norm(dim=-1, keepdim=True)
    text_features = text_features / text_features.norm(dim=-1, keepdim=True)
    
    # Calcola la similarità coseno
    similarity = (100.0 * image_features @ text_features.T).mean()
    
    return similarity.item()

def process_batch(model, processor, batch, device="cpu"):
    """Processa un batch di esempi."""
    results = []
    for example in batch:
        try:
            # Rasterizza l'SVG
            image = rasterize_svg(example['svg_xml'])
            
            # Calcola il CLIP score
            score = calculate_clip_score(
                model,
                processor,
                image,
                example['reference_caption'],
                device
            )
            
            results.append({
                'id': example['example_id'],
                'clip_score': score
            })
        except Exception as e:
            print(f"Errore nell'elaborazione dell'esempio {example['example_id']}: {str(e)}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='Calcola CLIP score su CPU')
    parser.add_argument('--test_file', required=True, help='Percorso del file di test')
    parser.add_argument('--output_dir', required=True, help='Directory di output')
    parser.add_argument('--batch_size', type=int, default=8,
                      help='Dimensione del batch per CPU')
    parser.add_argument('--num_workers', type=int, default=4,
                      help='Numero di worker per il dataloader')
    parser.add_argument('--use_wandb', action='store_true',
                      help='Usa Weights & Biases per il logging')
    parser.add_argument('--wandb_entity', default='337543-unimore',
                      help='Entity di Weights & Biases')
    parser.add_argument('--wandb_project', default='captioner',
                      help='Project di Weights & Biases')
    parser.add_argument('--wandb_run_name', default='clip_score_cpu',
                      help='Nome della run su Weights & Biases')
    
    args = parser.parse_args()
    
    # Crea la directory di output
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Carica il test set
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)
    
    # Inizializza il modello CLIP
    model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
    processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")
    model = model.to("cpu")
    model.eval()
    
    # Inizializza wandb se richiesto
    if args.use_wandb:
        wandb.init(
            project=args.wandb_project,
            entity=args.wandb_entity,
            name=args.wandb_run_name
        )
    
    # Processa i dati in batch
    all_results = []
    for i in tqdm(range(0, len(test_data), args.batch_size)):
        batch = test_data[i:i + args.batch_size]
        results = process_batch(model, processor, batch, "cpu")
        all_results.extend(results)
        
        # Log su wandb
        if args.use_wandb:
            wandb.log({
                'processed_examples': len(all_results),
                'mean_clip_score': sum(r['clip_score'] for r in results) / len(results)
            })
    
    # Calcola statistiche finali
    clip_scores = [r['clip_score'] for r in all_results]
    mean_score = sum(clip_scores) / len(clip_scores)
    max_score = max(clip_scores)
    min_score = min(clip_scores)
    
    # Salva i risultati
    output_file = output_dir / 'clip_scores.json'
    with open(output_file, 'w') as f:
        json.dump({
            'results': all_results,
            'statistics': {
                'mean_score': mean_score,
                'max_score': max_score,
                'min_score': min_score,
                'num_examples': len(all_results)
            }
        }, f, indent=2)
    
    # Log finale su wandb
    if args.use_wandb:
        wandb.log({
            'final_mean_clip_score': mean_score,
            'final_max_clip_score': max_score,
            'final_min_clip_score': min_score,
            'total_examples': len(all_results)
        })
        wandb.finish()
    
    print(f"\nRisultati CLIP Score:")
    print(f"Media: {mean_score:.2f}")
    print(f"Massimo: {max_score:.2f}")
    print(f"Minimo: {min_score:.2f}")
    print(f"Numero esempi: {len(all_results)}")

if __name__ == '__main__':
    main() 