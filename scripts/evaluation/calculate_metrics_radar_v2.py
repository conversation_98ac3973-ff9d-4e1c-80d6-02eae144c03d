import json
import os
import numpy as np
import matplotlib.pyplot as plt
from nltk.translate.bleu_score import corpus_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
import nltk
import argparse
from pycocoevalcap.cider.cider import Cider
import pandas as pd
from matplotlib.patches import Circle, RegularPolygon
from matplotlib.path import Path
from matplotlib.projections.polar import PolarAxes
from matplotlib.projections import register_projection
from matplotlib.spines import Spine
from matplotlib.transforms import Affine2D

# Ensure NLTK data is downloaded
nltk.download('wordnet')

def radar_factory(num_vars, frame='circle'):
    """Create a radar chart with `num_vars` axes.

    This function creates a RadarAxes projection and registers it.

    Parameters
    ----------
    num_vars : int
        Number of variables for radar chart.
    frame : {'circle' | 'polygon'}
        Shape of frame surrounding axes.

    """
    # calculate evenly-spaced axis angles
    theta = np.linspace(0, 2*np.pi, num_vars, endpoint=False)

    class RadarAxes(PolarAxes):

        name = 'radar'

        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            # rotate plot such that the first axis is at the top
            self.set_theta_zero_location('N')

        def fill(self, *args, closed=True, **kwargs):
            """Override fill so that line is closed by default"""
            return super().fill(closed=closed, *args, **kwargs)

        def plot(self, *args, **kwargs):
            """Override plot so that line is closed by default"""
            lines = super().plot(*args, **kwargs)
            for line in lines:
                self._close_line(line)

        def _close_line(self, line):
            x, y = line.get_data()
            # FIXME: markers at x[0], y[0] get doubled-up
            if x[0] != x[-1]:
                x = np.concatenate((x, [x[0]]))
                y = np.concatenate((y, [y[0]]))
                line.set_data(x, y)

        def set_varlabels(self, labels):
            self.set_thetagrids(np.degrees(theta), labels)

        def _gen_axes_patch(self):
            if frame == 'circle':
                return Circle((0.5, 0.5), 0.5)
            elif frame == 'polygon':
                return RegularPolygon((0.5, 0.5), num_vars,
                                      radius=.5, edgecolor="k")
            else:
                raise ValueError("unknown value for 'frame': %s" % frame)

        def draw(self, renderer):
            """ Draw. If frame is polygon, make gridlines polygon-shaped """
            if frame == 'polygon':
                gridlines = self.yaxis.get_gridlines()
                for gl in gridlines:
                    gl.get_path()._interpolation_steps = num_vars
            super().draw(renderer)


        def _gen_axes_spines(self):
            if frame == 'circle':
                return super()._gen_axes_spines()
            elif frame == 'polygon':
                # spine_type must be 'left'/'right'/'top'/'bottom'/'circle'.
                spine = Spine(axes=self,
                              spine_type='circle',
                              path=Path.unit_regular_polygon(num_vars))
                # unit_regular_polygon gives a polygon of radius 1 centered at
                # (0, 0) but we want a polygon of radius 0.5 centered at (0.5,
                # 0.5) in axes coordinates.
                spine.set_transform(Affine2D().scale(.5).translate(.5, .5)
                                    + self.transAxes)


                return {'polar': spine}
            else:
                raise ValueError("unknown value for 'frame': %s" % frame)

    register_projection(RadarAxes)
    return theta

def load_jsonl(file_path):
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def calculate_bleu_n(references, hypotheses, n):
    # Tokenize references and hypotheses
    tokenized_refs = [[ref.split()] for ref in references]
    tokenized_hyps = [hyp.split() for hyp in hypotheses]
    
    # Calculate BLEU-n score
    weights = tuple([0] * (n-1) + [1])  # e.g., for n=4: (0, 0, 0, 1)
    smoothing = SmoothingFunction().method1
    return corpus_bleu(tokenized_refs, tokenized_hyps, weights=weights, smoothing_function=smoothing)

def calculate_meteor(references, hypotheses):
    scores = []
    for ref, hyp in zip(references, hypotheses):
        score = meteor_score([ref.split()], hyp.split())
        scores.append(score)
    return np.mean(scores)

def calculate_cider(references, hypotheses):
    # Format data for CIDEr
    refs = {i: [references[i]] for i in range(len(references))}
    hyps = {i: [hypotheses[i]] for i in range(len(hypotheses))}
    
    # Calculate CIDEr score
    cider_scorer = Cider()
    score, _ = cider_scorer.compute_score(refs, hyps)
    return score

def create_radar_chart(metrics_llama, metrics_gemma, output_path):
    # Define the metrics to display in the order we want
    metrics_order = ['bleu1', 'bleu2', 'bleu3', 'bleu4', 'meteor', 'cider']
    labels = ['bleu1', 'bleu2', 'bleu3', 'bleu4', 'meteor', 'cider']
    
    # Extract values in the correct order
    llama_values = [metrics_llama[metric] for metric in metrics_order]
    gemma_values = [metrics_gemma[metric] if metrics_gemma else 0 for metric in metrics_order]
    
    # Set up the radar chart
    N = len(metrics_order)
    theta = radar_factory(N, frame='circle')
    
    fig = plt.figure(figsize=(10, 10))
    ax = fig.add_subplot(111, projection='radar')
    
    # Plot data for Llama
    ax.plot(theta, llama_values, 'o-', linewidth=2, color='blue', label='Llama 3.1 8B')
    ax.fill(theta, llama_values, alpha=0.1, color='blue')
    
    # Plot data for Gemma if available
    if metrics_gemma:
        ax.plot(theta, gemma_values, 'o-', linewidth=2, color='red', label='Gemma 2 9B IT')
        ax.fill(theta, gemma_values, alpha=0.1, color='red')
    
    # Set labels
    ax.set_varlabels(labels)
    
    # Set title
    ax.set_title('Metriche di Inferenza Zero-Shot (Ground Truth vs Generated)', size=15, y=1.05)
    
    # Set y-axis limits
    ax.set_ylim(0, 1.0)
    
    # Add concentric circles for reference
    ax.set_rgrids([0.2, 0.4, 0.6, 0.8, 1.0])
    
    # Add legend at the bottom
    ax.legend(loc='lower center', bbox_to_anchor=(0.5, -0.1), ncol=2)
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Calculate metrics for generated captions')
    parser.add_argument('--llama_input', type=str, required=True, help='Path to the Llama inference results JSONL file')
    parser.add_argument('--gemma_input', type=str, required=False, help='Path to the Gemma inference results JSONL file')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save metrics and charts')
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load Llama inference results
    llama_results = load_jsonl(args.llama_input)
    
    # Extract references and hypotheses for Llama
    llama_references = []
    llama_hypotheses = []
    
    for result in llama_results:
        # Clean up the generated caption (remove </s> tokens)
        generated_caption = result['generated_caption'].split('</s>')[0].strip()
        
        llama_references.append(result['true_caption'])
        llama_hypotheses.append(generated_caption)
    
    # Calculate metrics for Llama
    llama_bleu1 = calculate_bleu_n(llama_references, llama_hypotheses, 1)
    llama_bleu2 = calculate_bleu_n(llama_references, llama_hypotheses, 2)
    llama_bleu3 = calculate_bleu_n(llama_references, llama_hypotheses, 3)
    llama_bleu4 = calculate_bleu_n(llama_references, llama_hypotheses, 4)
    llama_meteor = calculate_meteor(llama_references, llama_hypotheses)
    llama_cider = calculate_cider(llama_references, llama_hypotheses) / 10.0  # Normalize CIDEr (typically 0-10)
    
    # Store Llama metrics
    llama_metrics = {
        'bleu1': llama_bleu1,
        'bleu2': llama_bleu2,
        'bleu3': llama_bleu3,
        'bleu4': llama_bleu4,
        'meteor': llama_meteor,
        'cider': llama_cider
    }
    
    # Save Llama metrics to JSON
    llama_metrics_path = os.path.join(args.output_dir, 'llama_metrics.json')
    with open(llama_metrics_path, 'w') as f:
        json.dump(llama_metrics, f, indent=2)
    
    # Process Gemma results if provided
    gemma_metrics = None
    if args.gemma_input:
        # Load Gemma inference results
        gemma_results = load_jsonl(args.gemma_input)
        
        # Extract references and hypotheses for Gemma
        gemma_references = []
        gemma_hypotheses = []
        
        for result in gemma_results:
            # Clean up the generated caption (remove </s> tokens)
            generated_caption = result['generated_caption'].split('</s>')[0].strip()
            
            gemma_references.append(result['true_caption'])
            gemma_hypotheses.append(generated_caption)
        
        # Calculate metrics for Gemma
        gemma_bleu1 = calculate_bleu_n(gemma_references, gemma_hypotheses, 1)
        gemma_bleu2 = calculate_bleu_n(gemma_references, gemma_hypotheses, 2)
        gemma_bleu3 = calculate_bleu_n(gemma_references, gemma_hypotheses, 3)
        gemma_bleu4 = calculate_bleu_n(gemma_references, gemma_hypotheses, 4)
        gemma_meteor = calculate_meteor(gemma_references, gemma_hypotheses)
        gemma_cider = calculate_cider(gemma_references, gemma_hypotheses) / 10.0  # Normalize CIDEr
        
        # Store Gemma metrics
        gemma_metrics = {
            'bleu1': gemma_bleu1,
            'bleu2': gemma_bleu2,
            'bleu3': gemma_bleu3,
            'bleu4': gemma_bleu4,
            'meteor': gemma_meteor,
            'cider': gemma_cider
        }
        
        # Save Gemma metrics to JSON
        gemma_metrics_path = os.path.join(args.output_dir, 'gemma_metrics.json')
        with open(gemma_metrics_path, 'w') as f:
            json.dump(gemma_metrics, f, indent=2)
    
    # Create radar chart
    chart_path = os.path.join(args.output_dir, 'metrics_radar.png')
    create_radar_chart(llama_metrics, gemma_metrics, chart_path)
    
    # Print metrics
    print("Llama 3.1 8B Metrics:")
    print(f"BLEU-1: {llama_bleu1:.4f}")
    print(f"BLEU-2: {llama_bleu2:.4f}")
    print(f"BLEU-3: {llama_bleu3:.4f}")
    print(f"BLEU-4: {llama_bleu4:.4f}")
    print(f"METEOR: {llama_meteor:.4f}")
    print(f"CIDEr: {llama_cider * 10:.4f}")  # Display unnormalized CIDEr
    
    if gemma_metrics:
        print("\nGemma 2 9B IT Metrics:")
        print(f"BLEU-1: {gemma_bleu1:.4f}")
        print(f"BLEU-2: {gemma_bleu2:.4f}")
        print(f"BLEU-3: {gemma_bleu3:.4f}")
        print(f"BLEU-4: {gemma_bleu4:.4f}")
        print(f"METEOR: {gemma_meteor:.4f}")
        print(f"CIDEr: {gemma_cider * 10:.4f}")  # Display unnormalized CIDEr
    
    print(f"\nMetrics saved to {args.output_dir}")
    print(f"Radar chart saved to {chart_path}")

if __name__ == '__main__':
    main()
