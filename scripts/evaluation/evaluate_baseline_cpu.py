#!/usr/bin/env python3
"""
Script baseline con CPU inference (stabile e robusto)
"""

import argparse
import json
import os
import signal
import time
from pathlib import Path
from tqdm import tqdm
import torch
from PIL import Image
import cairosvg
import io
import numpy as np
from transformers import pipeline
import logging
import time

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Timeout handler per evitare blocchi
class TimeoutError(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutError("Operazione timeout!")

def with_timeout(func, timeout_seconds=300):  # 5 minuti timeout
    """Esegue una funzione con timeout"""
    def wrapper(*args, **kwargs):
        # Imposta il timeout
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(timeout_seconds)

        try:
            result = func(*args, **kwargs)
            signal.alarm(0)  # Cancella il timeout
            return result
        except TimeoutError:
            logger.error(f"Timeout di {timeout_seconds}s raggiunto!")
            signal.alarm(0)
            return None
        except Exception as e:
            signal.alarm(0)
            logger.error(f"Errore durante esecuzione: {e}")
            return None

    return wrapper

def convert_rgba_to_rgb(image):
    """Converte un'immagine da RGBA a RGB con sfondo bianco"""
    if image.mode == 'RGBA':
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3])
        return bg
    elif image.mode == 'P':
        image = image.convert('RGBA')
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3] if len(image.split()) == 4 else None)
        return bg
    else:
        return image.convert('RGB')

def rasterize_svg(svg_data, dpi=150):
    """Converte SVG in immagine PNG con sfondo bianco."""
    try:
        png_data = cairosvg.svg2png(bytestring=svg_data.encode('utf-8'), dpi=dpi)
        image = Image.open(io.BytesIO(png_data))
        return convert_rgba_to_rgb(image)
    except Exception as e:
        logger.error(f"Errore durante la rasterizzazione SVG: {e}")
        return None

def create_cpu_pipeline(model_name):
    """Crea pipeline Hugging Face per CPU (stabile)."""
    
    try:
        logger.info(f"Creazione pipeline CPU per {model_name}...")
        
        # TUTTI I MODELLI SU CPU per stabilità
        if model_name == "idefix":
            # Ide Fix 3 - usando microsoft/git-base con image processor specifico
            from transformers import AutoTokenizer, AutoModelForCausalLM, AutoImageProcessor

            # Carica tutti i componenti separatamente
            tokenizer = AutoTokenizer.from_pretrained("microsoft/git-base",
                                                    trust_remote_code=True,
                                                    use_fast=False)

            # IMPORTANTE: Carica l'image processor specifico per git-base
            image_processor = AutoImageProcessor.from_pretrained("microsoft/git-base",
                                                               trust_remote_code=True)

            model = AutoModelForCausalLM.from_pretrained("microsoft/git-base",
                                                       torch_dtype=torch.float32,
                                                       device_map="cpu",
                                                       trust_remote_code=True,
                                                       low_cpu_mem_usage=True)

            # Crea pipeline con tutti i componenti specificati (SENZA device per accelerate)
            # NOTA: Non specificare device quando il modello è già caricato con device_map
            return pipeline("image-to-text",
                          model=model,
                          tokenizer=tokenizer,
                          image_processor=image_processor,
                          torch_dtype=torch.float32)
        
        elif model_name == "flores2":
            # Flores 2 base - usando nlpconnect/vit-gpt2 (molto leggero)
            return pipeline("image-to-text",
                          model="nlpconnect/vit-gpt2-image-captioning",
                          device=-1,  # CPU
                          torch_dtype=torch.float32)
        
        elif model_name == "blip2":
            # BLIP 2.7B - usando BLIP base (più leggero)
            return pipeline("image-to-text",
                          model="Salesforce/blip-image-captioning-base",
                          device=-1,  # CPU
                          torch_dtype=torch.float32)
        
        else:
            logger.error(f"Modello non supportato: {model_name}")
            return None
            
    except Exception as e:
        logger.error(f"Errore creazione pipeline {model_name}: {e}")
        return None

def evaluate_model_cpu(model_name, test_data, output_dir):
    """Valuta un modello usando CPU inference."""
    logger.info(f"Valutazione modello {model_name} con CPU inference...")
    
    start_time = time.time()
    
    # Crea pipeline CPU con timeout
    logger.info(f"Creazione pipeline {model_name} con timeout 5 minuti...")

    @with_timeout
    def create_pipeline_safe():
        return create_cpu_pipeline(model_name)

    captioner = create_pipeline_safe()
    if captioner is None:
        logger.error(f"Fallimento creazione pipeline {model_name} (timeout o errore)")
        return None
    
    logger.info(f"Pipeline {model_name} creata in {time.time() - start_time:.1f}s")
    
    results_data = []
    
    # Processa i dati con progress bar
    for i, item in enumerate(tqdm(test_data, desc=f"CPU Eval {model_name}")):
        try:
            # Rasterizza SVG
            if 'xml' in item:
                svg_data = item['xml']
            elif 'svg_xml' in item:
                svg_data = item['svg_xml']
            else:
                logger.warning(f"Nessun campo SVG trovato nell'esempio {i}")
                continue

            image = rasterize_svg(svg_data)
            if image is None:
                continue

            # Ridimensiona immagine per CPU (più piccola = più veloce)
            if image.size[0] > 384 or image.size[1] > 384:
                image.thumbnail((384, 384), Image.Resampling.LANCZOS)

            # Genera caption con CPU (parametri ottimizzati per velocità)
            try:
                start_inference = time.time()
                
                result = captioner(image,
                                 max_new_tokens=15,  # Molto ridotto per velocità
                                 do_sample=False,    # Deterministic
                                 num_beams=1,        # Veloce
                                 temperature=1.0)    # Default temperature
                
                generated_caption = result[0]['generated_text'] if result else ""
                
                inference_time = time.time() - start_inference
                
                if i < 5:  # Log primi 5 per debug
                    logger.info(f"Esempio {i}: {inference_time:.2f}s - '{generated_caption[:50]}...'")
                
            except Exception as e:
                logger.warning(f"Errore generazione caption esempio {i}: {e}")
                generated_caption = "[GENERATION_ERROR]"

            # Salva risultati
            reference_caption = item.get('caption', item.get('reference_caption', ''))
            
            results_data.append({
                'id': item.get('id', item.get('example_id', f'example_{i}')),
                'reference': reference_caption,
                'generated': generated_caption
            })

            # Log progresso ogni 50 esempi
            if (i + 1) % 50 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / (i + 1)
                eta = avg_time * (len(test_data) - i - 1)
                logger.info(f"Processati {i + 1}/{len(test_data)} esempi - ETA: {eta/60:.1f}min")
                
        except Exception as e:
            logger.error(f"Errore processando esempio {i}: {e}")
            continue

    # Salva risultati
    output_path = Path(output_dir) / f"baseline_{model_name}_cpu"
    output_path.mkdir(parents=True, exist_ok=True)

    with open(output_path / 'predictions.jsonl', 'w') as f:
        for pred in results_data:
            f.write(json.dumps(pred) + '\n')

    total_time = time.time() - start_time
    logger.info(f"Risultati salvati in {output_path}")
    logger.info(f"Processati {len(results_data)} esempi per {model_name} in {total_time/60:.1f}min")
    
    # Libera memoria
    del captioner
    
    return results_data

def main():
    parser = argparse.ArgumentParser(description='Valuta modelli baseline con CPU inference')
    parser.add_argument('--test_file', required=True, help='Percorso del file di test')
    parser.add_argument('--output_dir', required=True, help='Directory di output')
    parser.add_argument('--models', nargs='+',
                       choices=['idefix', 'flores2', 'blip2'],
                       default=['idefix'],  # Inizia con Ide Fix 3
                       help='Lista dei modelli da valutare (CPU inference)')

    args = parser.parse_args()

    # Carica il dataset di test
    logger.info(f"Caricamento dataset da {args.test_file}")
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)

    logger.info(f"Dataset caricato: {len(test_data)} esempi")
    logger.info("🖥️  Modalità CPU inference attivata per stabilità")

    # Valuta ogni modello
    for model_name in args.models:
        logger.info(f"Inizio valutazione {model_name} (CPU)")
        
        try:
            results = evaluate_model_cpu(
                model_name,
                test_data,
                args.output_dir
            )
            
            if results:
                logger.info(f"✅ {model_name} completato: {len(results)} esempi")
            else:
                logger.error(f"❌ {model_name} fallito")
                
        except Exception as e:
            logger.error(f"Errore nella valutazione di {model_name}: {e}")
            continue

    logger.info("🎉 Valutazione CPU completata!")

if __name__ == '__main__':
    main()
