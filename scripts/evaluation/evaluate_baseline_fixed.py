#!/usr/bin/env python3
"""
Baseline evaluation con Transformers 4.42.4 (FUNZIONANTE)
"""

import argparse
import json
import os
from pathlib import Path
from tqdm import tqdm
import torch
from PIL import Image
import cairosvg
import io
import logging
import time

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def convert_rgba_to_rgb(image):
    """Converte un'immagine da RGBA a RGB con sfondo bianco"""
    if image.mode == 'RGBA':
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3])
        return bg
    elif image.mode == 'P':
        image = image.convert('RGBA')
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3] if len(image.split()) == 4 else None)
        return bg
    else:
        return image.convert('RGB')

def rasterize_svg(svg_data, dpi=150):
    """Converte SVG in immagine PNG con sfondo bianco."""
    try:
        png_data = cairosvg.svg2png(bytestring=svg_data.encode('utf-8'), dpi=dpi)
        image = Image.open(io.BytesIO(png_data))
        return convert_rgba_to_rgb(image)
    except Exception as e:
        logger.error(f"Errore durante la rasterizzazione SVG: {e}")
        return None

def create_pipeline(model_name):
    """Crea pipeline con Transformers 4.42.4"""
    
    try:
        from transformers import pipeline
        logger.info(f"Creazione pipeline {model_name}...")
        
        if model_name == "idefix":
            # Ide Fix 3 - microsoft/git-base (leggero)
            return pipeline("image-to-text",
                          model="microsoft/git-base",
                          device=-1)  # CPU per stabilità
        
        elif model_name == "flores2":
            # Flores 2 - ViT-GPT2 (molto leggero)
            return pipeline("image-to-text",
                          model="nlpconnect/vit-gpt2-image-captioning",
                          device=-1)
        
        elif model_name == "blip2":
            # BLIP 2.7B - BLIP base (più leggero del 2.7B)
            return pipeline("image-to-text",
                          model="Salesforce/blip-image-captioning-base",
                          device=-1)
        
        else:
            logger.error(f"Modello non supportato: {model_name}")
            return None
            
    except Exception as e:
        logger.error(f"Errore creazione pipeline {model_name}: {e}")
        return None

def evaluate_model(model_name, test_data, output_dir):
    """Valuta un modello baseline"""
    logger.info(f"Valutazione modello {model_name}...")
    
    start_time = time.time()
    
    # Crea pipeline
    captioner = create_pipeline(model_name)
    if captioner is None:
        return None
    
    pipeline_time = time.time() - start_time
    logger.info(f"Pipeline {model_name} creata in {pipeline_time:.1f}s")
    
    results_data = []
    
    # Processa i dati
    for i, item in enumerate(tqdm(test_data, desc=f"Eval {model_name}")):
        try:
            # Rasterizza SVG
            if 'xml' in item:
                svg_data = item['xml']
            elif 'svg_xml' in item:
                svg_data = item['svg_xml']
            else:
                logger.warning(f"Nessun campo SVG trovato nell'esempio {i}")
                continue

            image = rasterize_svg(svg_data)
            if image is None:
                continue

            # Ridimensiona per velocità
            if image.size[0] > 384 or image.size[1] > 384:
                image.thumbnail((384, 384), Image.Resampling.LANCZOS)

            # Genera caption (parametri di default)
            try:
                result = captioner(image)
                generated_caption = result[0]['generated_text'] if result else ""
                
            except Exception as e:
                logger.warning(f"Errore generazione caption esempio {i}: {e}")
                generated_caption = "[GENERATION_ERROR]"

            # Salva risultati
            reference_caption = item.get('caption', item.get('reference_caption', ''))
            
            results_data.append({
                'id': item.get('id', item.get('example_id', f'example_{i}')),
                'reference': reference_caption,
                'generated': generated_caption
            })

            # Log progresso ogni 50 esempi
            if (i + 1) % 50 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / (i + 1)
                eta = avg_time * (len(test_data) - i - 1)
                logger.info(f"Processati {i + 1}/{len(test_data)} esempi - ETA: {eta/60:.1f}min")
                
        except Exception as e:
            logger.error(f"Errore processando esempio {i}: {e}")
            continue

    # Salva risultati
    output_path = Path(output_dir) / f"baseline_{model_name}_fixed"
    output_path.mkdir(parents=True, exist_ok=True)

    with open(output_path / 'predictions.jsonl', 'w') as f:
        for pred in results_data:
            f.write(json.dumps(pred) + '\n')

    total_time = time.time() - start_time
    logger.info(f"Risultati salvati in {output_path}")
    logger.info(f"Processati {len(results_data)} esempi per {model_name} in {total_time/60:.1f}min")
    
    # Libera memoria
    del captioner
    
    return results_data

def main():
    parser = argparse.ArgumentParser(description='Baseline evaluation con Transformers 4.42.4')
    parser.add_argument('--test_file', required=True, help='Percorso del file di test')
    parser.add_argument('--output_dir', required=True, help='Directory di output')
    parser.add_argument('--models', nargs='+',
                       choices=['idefix', 'flores2', 'blip2'],
                       default=['idefix'],
                       help='Lista dei modelli da valutare')

    args = parser.parse_args()

    # Verifica versione Transformers
    import transformers
    logger.info(f"🚀 Transformers version: {transformers.__version__}")

    # Carica il dataset di test
    logger.info(f"Caricamento dataset da {args.test_file}")
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)

    logger.info(f"Dataset caricato: {len(test_data)} esempi")

    # Valuta ogni modello
    for model_name in args.models:
        logger.info(f"Inizio valutazione {model_name}")
        
        try:
            results = evaluate_model(
                model_name,
                test_data,
                args.output_dir
            )
            
            if results:
                logger.info(f"✅ {model_name} completato: {len(results)} esempi")
            else:
                logger.error(f"❌ {model_name} fallito")
                
        except Exception as e:
            logger.error(f"Errore nella valutazione di {model_name}: {e}")
            continue

    logger.info("🎉 Baseline evaluation completata!")

if __name__ == '__main__':
    main()
