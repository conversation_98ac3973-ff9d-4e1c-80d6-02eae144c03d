import json
import os
import numpy as np
import matplotlib.pyplot as plt
from nltk.translate.bleu_score import corpus_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
import nltk
import argparse
from pycocoevalcap.cider.cider import Cider
import pandas as pd
from matplotlib.patches import Circle, RegularPolygon
from matplotlib.path import Path
from matplotlib.projections.polar import PolarAxes
from matplotlib.projections import register_projection
from matplotlib.spines import Spine
from matplotlib.transforms import Affine2D

# Ensure NLTK data is downloaded
nltk.download('wordnet')

def radar_factory(num_vars, frame='circle'):
    """Create a radar chart with `num_vars` axes.

    This function creates a RadarAxes projection and registers it.

    Parameters
    ----------
    num_vars : int
        Number of variables for radar chart.
    frame : {'circle' | 'polygon'}
        Shape of frame surrounding axes.

    """
    # calculate evenly-spaced axis angles
    theta = np.linspace(0, 2*np.pi, num_vars, endpoint=False)

    class RadarAxes(PolarAxes):

        name = 'radar'

        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            # rotate plot such that the first axis is at the top
            self.set_theta_zero_location('N')

        def fill(self, *args, closed=True, **kwargs):
            """Override fill so that line is closed by default"""
            return super().fill(closed=closed, *args, **kwargs)

        def plot(self, *args, **kwargs):
            """Override plot so that line is closed by default"""
            lines = super().plot(*args, **kwargs)
            for line in lines:
                self._close_line(line)

        def _close_line(self, line):
            x, y = line.get_data()
            # FIXME: markers at x[0], y[0] get doubled-up
            if x[0] != x[-1]:
                x = np.concatenate((x, [x[0]]))
                y = np.concatenate((y, [y[0]]))
                line.set_data(x, y)

        def set_varlabels(self, labels):
            self.set_thetagrids(np.degrees(theta), labels)

        def _gen_axes_patch(self):
            if frame == 'circle':
                return Circle((0.5, 0.5), 0.5)
            elif frame == 'polygon':
                return RegularPolygon((0.5, 0.5), num_vars,
                                      radius=.5, edgecolor="k")
            else:
                raise ValueError("unknown value for 'frame': %s" % frame)

        def draw(self, renderer):
            """ Draw. If frame is polygon, make gridlines polygon-shaped """
            if frame == 'polygon':
                gridlines = self.yaxis.get_gridlines()
                for gl in gridlines:
                    gl.get_path()._interpolation_steps = num_vars
            super().draw(renderer)


        def _gen_axes_spines(self):
            if frame == 'circle':
                return super()._gen_axes_spines()
            elif frame == 'polygon':
                # spine_type must be 'left'/'right'/'top'/'bottom'/'circle'.
                spine = Spine(axes=self,
                              spine_type='circle',
                              path=Path.unit_regular_polygon(num_vars))
                # unit_regular_polygon gives a polygon of radius 1 centered at
                # (0, 0) but we want a polygon of radius 0.5 centered at (0.5,
                # 0.5) in axes coordinates.
                spine.set_transform(Affine2D().scale(.5).translate(.5, .5)
                                    + self.transAxes)


                return {'polar': spine}
            else:
                raise ValueError("unknown value for 'frame': %s" % frame)

    register_projection(RadarAxes)
    return theta

def load_jsonl(file_path):
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def calculate_bleu(references, hypotheses):
    # Tokenize references and hypotheses
    tokenized_refs = [[ref.split()] for ref in references]
    tokenized_hyps = [hyp.split() for hyp in hypotheses]
    
    # Calculate BLEU score
    smoothing = SmoothingFunction().method1
    return corpus_bleu(tokenized_refs, tokenized_hyps, smoothing_function=smoothing)

def calculate_meteor(references, hypotheses):
    scores = []
    for ref, hyp in zip(references, hypotheses):
        score = meteor_score([ref.split()], hyp.split())
        scores.append(score)
    return np.mean(scores)

def calculate_cider(references, hypotheses):
    # Format data for CIDEr
    refs = {i: [references[i]] for i in range(len(references))}
    hyps = {i: [hypotheses[i]] for i in range(len(hypotheses))}
    
    # Calculate CIDEr score
    cider_scorer = Cider()
    score, _ = cider_scorer.compute_score(refs, hyps)
    return score

def create_radar_chart(metrics, output_path):
    N = len(metrics)
    theta = radar_factory(N, frame='polygon')

    fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='radar'))
    fig.subplots_adjust(top=0.85, bottom=0.05)

    # Plot data
    ax.plot(theta, list(metrics.values()), 'o-', linewidth=2)
    ax.fill(theta, list(metrics.values()), alpha=0.25)
    
    # Set labels and title
    ax.set_varlabels(list(metrics.keys()))
    ax.set_title('Model Evaluation Metrics', size=15, y=1.1)
    
    # Add legend and grid
    plt.grid(True)
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Calculate metrics for generated captions')
    parser.add_argument('--input_file', type=str, required=True, help='Path to the inference results JSONL file')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save metrics and charts')
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load inference results
    results = load_jsonl(args.input_file)
    
    # Extract references and hypotheses
    references = []
    hypotheses = []
    
    for result in results:
        # Clean up the generated caption (remove </s> tokens)
        generated_caption = result['generated_caption'].split('</s>')[0].strip()
        
        references.append(result['true_caption'])
        hypotheses.append(generated_caption)
    
    # Calculate metrics
    bleu_score = calculate_bleu(references, hypotheses)
    meteor_score_val = calculate_meteor(references, hypotheses)
    cider_score = calculate_cider(references, hypotheses)
    
    # Normalize scores for radar chart
    metrics = {
        'BLEU': bleu_score,
        'METEOR': meteor_score_val,
        'CIDEr': cider_score / 10.0,  # Normalize CIDEr (typically 0-10)
    }
    
    # Save metrics to JSON
    metrics_path = os.path.join(args.output_dir, 'metrics.json')
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # Create radar chart
    chart_path = os.path.join(args.output_dir, 'metrics_radar.png')
    create_radar_chart(metrics, chart_path)
    
    # Print metrics
    print(f"BLEU: {bleu_score:.4f}")
    print(f"METEOR: {meteor_score_val:.4f}")
    print(f"CIDEr: {cider_score:.4f}")
    
    print(f"Metrics saved to {metrics_path}")
    print(f"Radar chart saved to {chart_path}")

if __name__ == '__main__':
    main()
