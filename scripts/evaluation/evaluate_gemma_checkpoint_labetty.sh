#!/bin/bash

#SBATCH --job-name=eval_gemma_ckpt_labetty
#SBATCH --output=/work/tesi_ediluzio/logs/eval_gemma_ckpt_labetty_%j.out
#SBATCH --error=/work/tesi_ediluzio/logs/eval_gemma_ckpt_labetty_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=normal
#SBATCH --nodelist=labetty
#SBATCH --gpus=1
#SBATCH --time=02:00:00
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=32G
#SBATCH --mail-type=FAIL,END
#SBATCH --mail-user=<EMAIL>

################################################################################
# SEZIONE SETUP AMBIENTE E ESECUZIONE
################################################################################

PROJECT_ROOT="/work/tesi_ediluzio"
PYTHON_EXEC="${PROJECT_ROOT}/svg_captioning_env/bin/python"
SCRIPT_PATH="${PROJECT_ROOT}/experiments/xml_direct_input/evaluate_checkpoints.py"

# --- Verifica Ambiente ---
if [ ! -f "$PYTHON_EXEC" ]; then echo "Errore: Python non trovato: ${PYTHON_EXEC}" ; exit 1; fi
if [ ! -f "$SCRIPT_PATH" ]; then echo "Errore: Script Python non trovato: ${SCRIPT_PATH}" ; exit 1; fi
echo "Using Python executable: ${PYTHON_EXEC}"

# Imposta PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="${PROJECT_ROOT}:${PYTHONPATH}"
echo "PYTHONPATH: ${PYTHONPATH}"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Imposta le variabili di ambiente per Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_ENTITY="337543-unimore"
export WANDB_PROJECT="captioner"
echo "Variabili di ambiente WANDB impostate."

# Imposta le variabili
MODEL_NAME_OR_PATH="google/gemma-2-9b-it"
CHECKPOINT_DIR="${PROJECT_ROOT}/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence"
TEST_FILE="${PROJECT_ROOT}/experiments/xml_direct_input/dataset_analysis/splits/test.json"
OUTPUT_DIR="${PROJECT_ROOT}/experiments/xml_direct_input/evaluation_results/gemma2_9b_it_test1_convergence"
CHECKPOINT_PATTERN="checkpoint-2300"  # Valuta solo il checkpoint 2300

echo "Valutazione del checkpoint ottimale per Gemma 2 9B IT"
echo "Modello: ${MODEL_NAME_OR_PATH}"
echo "Checkpoint: ${CHECKPOINT_DIR}/${CHECKPOINT_PATTERN}"
echo "Dataset di test: ${TEST_FILE}"
echo "Output: ${OUTPUT_DIR}"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU Allocata (da SLURM): $SLURM_GPUS_ON_NODE"

# --- Comando di Esecuzione ---
echo "Eseguo lo script Python: ${SCRIPT_PATH}"

# Esegui passando gli argomenti allo script Python
${PYTHON_EXEC} ${SCRIPT_PATH} \
    --model_name_or_path "${MODEL_NAME_OR_PATH}" \
    --checkpoint_dir "${CHECKPOINT_DIR}" \
    --test_file "${TEST_FILE}" \
    --output_dir "${OUTPUT_DIR}" \
    --load_in_4bit \
    --num_samples 100 \
    --use_clip \
    --checkpoint_pattern "${CHECKPOINT_PATTERN}"

EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
  echo "Valutazione completata con successo"
else
  echo "Valutazione fallita con codice di errore: $EXIT_CODE"
fi
exit $EXIT_CODE
