#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per calcolare CLIP scores VERI per i modelli baseline:
- Renderizza SVG come immagini
- Usa modello CLIP reale (ViT-B/32)
- Calcola similarità immagine-testo
"""

import os
import sys
import json
import torch
import numpy as np
import argparse
from pathlib import Path
import logging
from tqdm import tqdm
from PIL import Image
import io
import cairosvg

# Importa CLIP
import clip

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_baseline_predictions(predictions_file):
    """Carica le predizioni baseline da file JSONL."""
    predictions = []
    try:
        with open(predictions_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    predictions.append(json.loads(line))
        logger.info(f"Caricate {len(predictions)} predizioni da {predictions_file}")
        return predictions
    except Exception as e:
        logger.error(f"Errore nel caricamento {predictions_file}: {e}")
        return []

def load_test_data(test_file):
    """Carica i dati di test con SVG."""
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        logger.info(f"Caricati {len(test_data)} esempi di test")
        return test_data
    except Exception as e:
        logger.error(f"Errore nel caricamento test data: {e}")
        return []

def render_svg_to_image(svg_content, size=224):
    """Renderizza SVG come immagine PIL."""
    try:
        # Pulisci SVG
        svg_clean = svg_content.strip()
        if not svg_clean.startswith('<?xml'):
            svg_clean = '<?xml version="1.0" encoding="utf-8"?>\n' + svg_clean
        
        # Correggi problemi comuni negli SVG
        svg_clean = svg_clean.replace('d="d=', 'd="')
        
        # Renderizza SVG come PNG
        png_data = cairosvg.svg2png(
            bytestring=svg_clean.encode('utf-8'),
            output_width=size,
            output_height=size,
            background_color='white'  # Sfondo bianco
        )
        
        # Converti in PIL Image
        image = Image.open(io.BytesIO(png_data)).convert('RGB')
        return image
        
    except Exception as e:
        logger.warning(f"Errore rendering SVG: {e}")
        # Crea immagine bianca di fallback
        return Image.new('RGB', (size, size), 'white')

def calculate_clip_score(image, caption, model, preprocess, device):
    """Calcola il CLIP score VERO tra immagine e caption."""
    try:
        # Preprocessa immagine
        image_input = preprocess(image).unsqueeze(0).to(device)
        
        # Tokenizza caption
        text_input = clip.tokenize([caption], truncate=True).to(device)
        
        # Calcola features
        with torch.no_grad():
            image_features = model.encode_image(image_input)
            text_features = model.encode_text(text_input)
            
            # Normalizza features
            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            # Calcola similarità coseno (CLIP score)
            similarity = (100.0 * image_features @ text_features.T).item()
            
        return similarity
        
    except Exception as e:
        logger.warning(f"Errore calcolo CLIP score: {e}")
        return 0.0

def calculate_baseline_real_clip_scores(model_name, predictions_file, test_data, output_dir, max_samples=50):
    """Calcola CLIP scores VERI per un modello baseline."""
    
    logger.info(f"🎯 Calcolo CLIP scores VERI per {model_name}...")
    
    # Setup device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Utilizzo device: {device}")
    
    # Carica modello CLIP
    logger.info("Caricamento modello CLIP ViT-B/32...")
    model, preprocess = clip.load("ViT-B/32", device=device)
    logger.info("✅ Modello CLIP caricato")
    
    # Carica predizioni
    predictions = load_baseline_predictions(predictions_file)
    if not predictions:
        logger.error(f"Nessuna predizione trovata per {model_name}")
        return None
    
    # Limita campioni se richiesto
    if max_samples and len(predictions) > max_samples:
        predictions = predictions[:max_samples]
        logger.info(f"Limitato a {max_samples} campioni")
    
    # Crea mappa ID -> SVG dai dati di test
    svg_map = {}
    for item in test_data:
        item_id = item.get('id', item.get('example_id'))
        svg_data = item.get('xml', item.get('svg', item.get('svg_data')))
        true_caption = item.get('caption', item.get('reference_caption', ''))
        
        if item_id and svg_data:
            svg_map[str(item_id)] = {
                'svg': svg_data,
                'true_caption': true_caption
            }
    
    logger.info(f"Mappa SVG creata con {len(svg_map)} elementi")
    
    # Calcola CLIP scores VERI
    results = []
    clip_scores = []
    
    for i, pred in enumerate(tqdm(predictions, desc=f"CLIP VERO {model_name}")):
        try:
            pred_id = str(pred.get('id', f'unknown_{i}'))
            generated_caption = pred.get('generated', '')
            reference_caption = pred.get('reference', '')
            
            # Trova SVG corrispondente
            svg_info = svg_map.get(pred_id)
            if not svg_info:
                logger.warning(f"SVG non trovato per ID {pred_id}")
                continue
            
            svg_data = svg_info['svg']
            true_caption = svg_info['true_caption'] or reference_caption
            
            # Renderizza SVG come immagine
            image = render_svg_to_image(svg_data, size=224)
            
            # Calcola CLIP score VERO per caption generata
            generated_clip_score = calculate_clip_score(image, generated_caption, model, preprocess, device)
            
            # Calcola CLIP score VERO per caption di riferimento (per confronto)
            true_clip_score = calculate_clip_score(image, true_caption, model, preprocess, device)
            
            # Salva risultato
            result = {
                'id': pred_id,
                'generated_caption': generated_caption,
                'true_caption': true_caption,
                'generated_clip_score': generated_clip_score,
                'true_clip_score': true_clip_score,
                'clip_score_ratio': generated_clip_score / true_clip_score if true_clip_score > 0 else 0.0
            }
            
            results.append(result)
            clip_scores.append(generated_clip_score)
            
        except Exception as e:
            logger.warning(f"Errore elaborazione predizione {i}: {e}")
            continue
    
    # Calcola statistiche
    if clip_scores:
        stats = {
            'mean_clip_score': float(np.mean(clip_scores)),
            'median_clip_score': float(np.median(clip_scores)),
            'std_clip_score': float(np.std(clip_scores)),
            'min_clip_score': float(np.min(clip_scores)),
            'max_clip_score': float(np.max(clip_scores)),
            'num_samples': len(clip_scores)
        }
    else:
        stats = {
            'mean_clip_score': 0.0,
            'median_clip_score': 0.0,
            'std_clip_score': 0.0,
            'min_clip_score': 0.0,
            'max_clip_score': 0.0,
            'num_samples': 0
        }
    
    # Crea risultato finale
    final_result = {
        'model_name': model_name,
        'clip_model': "ViT-B/32",
        'method': "real_clip_score_image_text",
        'aggregated': stats,
        'samples': results
    }
    
    # Salva risultati
    output_file = Path(output_dir) / f"{model_name}_real_clip_scores.json"
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(final_result, f, indent=2)
    
    logger.info(f"✅ CLIP scores VERI salvati in {output_file}")
    logger.info(f"📊 Media CLIP score VERO: {stats['mean_clip_score']:.4f}")
    logger.info(f"📊 Campioni elaborati: {stats['num_samples']}")
    
    return final_result

def main():
    parser = argparse.ArgumentParser(description='Calcola CLIP scores VERI per modelli baseline')
    parser.add_argument('--test_file', default='data/processed/xml_format/test_set_final_xml_reduced_rgb.json',
                       help='File dati di test con SVG')
    parser.add_argument('--baseline_dir', default='experiments/baseline_svg_fixed_final',
                       help='Directory con predizioni baseline')
    parser.add_argument('--output_dir', default='experiments/baseline_real_clip_scores',
                       help='Directory output')
    parser.add_argument('--models', nargs='+',
                       choices=['idefix', 'flores2', 'blip2'],
                       default=['idefix', 'flores2', 'blip2'],
                       help='Modelli da valutare')
    parser.add_argument('--max_samples', type=int, default=50,
                       help='Numero massimo campioni per modello')
    
    args = parser.parse_args()
    
    # Carica dati di test
    test_data = load_test_data(args.test_file)
    if not test_data:
        logger.error("Impossibile caricare dati di test")
        return
    
    # Mappa nomi modelli
    model_mapping = {
        'idefix': 'baseline_idefix_svg_fixed',
        'flores2': 'baseline_flores2_svg_fixed',
        'blip2': 'baseline_blip2_svg_fixed'
    }
    
    model_names = {
        'idefix': 'Ide Fix 3',
        'flores2': 'Flores 2 base',
        'blip2': 'BLIP 2.7B'
    }
    
    # Calcola CLIP scores VERI per ogni modello
    all_results = {}
    
    for model in args.models:
        model_dir = model_mapping[model]
        predictions_file = Path(args.baseline_dir) / model_dir / 'predictions.jsonl'
        
        if not predictions_file.exists():
            logger.warning(f"File predizioni non trovato: {predictions_file}")
            continue
        
        result = calculate_baseline_real_clip_scores(
            model_names[model], predictions_file, test_data, args.output_dir, args.max_samples
        )
        
        if result:
            all_results[model_names[model]] = result
    
    # Salva riassunto
    summary_file = Path(args.output_dir) / 'baseline_real_clip_summary.json'
    with open(summary_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    logger.info(f"✅ Riassunto salvato in {summary_file}")
    
    # Stampa risultati
    print("\n" + "="*80)
    print("🎯 CLIP SCORES VERI BASELINE - RISULTATI REALI")
    print("="*80)
    print("📊 Metodo: SVG renderizzati + CLIP ViT-B/32")
    print("🖼️  Immagini: 224x224 RGB con sfondo bianco")
    print("="*80)
    
    for model_name, result in all_results.items():
        stats = result['aggregated']
        print(f"\n🎯 {model_name}:")
        print(f"   📈 Media CLIP Score VERO: {stats['mean_clip_score']:.4f}")
        print(f"   📊 Mediana: {stats['median_clip_score']:.4f}")
        print(f"   📏 Std Dev: {stats['std_clip_score']:.4f}")
        print(f"   🔥 Max Score: {stats['max_clip_score']:.4f}")
        print(f"   ❄️  Min Score: {stats['min_clip_score']:.4f}")
        print(f"   📋 Campioni: {stats['num_samples']}")
    
    print("\n" + "="*80)
    print("✅ CLIP scores VERI calcolati con successo!")
    print("🎯 Ora hai i VERI CLIP scores per i modelli baseline!")

if __name__ == "__main__":
    main()
