#!/usr/bin/env python3
"""
Baseline evaluation ROBUSTO con timeout SVG
"""

import argparse
import json
import os
from pathlib import Path
from tqdm import tqdm
import torch
from PIL import Image
import cairosvg
import io
import logging
import time
import signal
from contextlib import contextmanager

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@contextmanager
def timeout_context(seconds):
    """Context manager per timeout"""
    def timeout_handler(signum, frame):
        raise TimeoutError(f"Operazione timeout dopo {seconds}s")
    
    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(seconds)
    try:
        yield
    finally:
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)

def convert_rgba_to_rgb(image):
    """Converte un'immagine da RGBA a RGB con sfondo bianco"""
    if image.mode == 'RGBA':
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3])
        return bg
    elif image.mode == 'P':
        image = image.convert('RGBA')
        bg = Image.new('RGB', image.size, (255, 255, 255))
        bg.paste(image, mask=image.split()[3] if len(image.split()) == 4 else None)
        return bg
    else:
        return image.convert('RGB')

def rasterize_svg_safe(svg_data, dpi=150, timeout_sec=10):
    """Converte SVG in immagine con timeout e gestione errori"""
    try:
        with timeout_context(timeout_sec):
            # Limita dimensione SVG per sicurezza
            if len(svg_data) > 100000:  # 100KB max
                logger.warning("SVG troppo grande, saltato")
                return None
                
            png_data = cairosvg.svg2png(bytestring=svg_data.encode('utf-8'), dpi=dpi)
            image = Image.open(io.BytesIO(png_data))
            return convert_rgba_to_rgb(image)
            
    except TimeoutError:
        logger.warning("SVG rasterizzazione timeout")
        return None
    except Exception as e:
        logger.warning(f"Errore SVG: {str(e)[:100]}")
        return None

def create_pipeline_safe(model_name):
    """Crea pipeline con gestione errori"""
    try:
        from transformers import pipeline
        logger.info(f"Creazione pipeline {model_name}...")
        
        if model_name == "idefix":
            return pipeline("image-to-text",
                          model="microsoft/git-base",
                          device=-1)
        elif model_name == "flores2":
            return pipeline("image-to-text",
                          model="nlpconnect/vit-gpt2-image-captioning",
                          device=-1)
        elif model_name == "blip2":
            return pipeline("image-to-text",
                          model="Salesforce/blip-image-captioning-base",
                          device=-1)
        else:
            return None
            
    except Exception as e:
        logger.error(f"Errore pipeline {model_name}: {e}")
        return None

def safe_inference(captioner, image, timeout_sec=30):
    """Inferenza con timeout"""
    try:
        with timeout_context(timeout_sec):
            result = captioner(image)
            return result[0]['generated_text'] if result else ""
    except TimeoutError:
        logger.warning("Inferenza timeout")
        return "[TIMEOUT_ERROR]"
    except Exception as e:
        logger.warning(f"Errore inferenza: {str(e)[:100]}")
        return "[INFERENCE_ERROR]"

def evaluate_model_robust(model_name, test_data, output_dir, max_examples=None):
    """Valuta modello con gestione robusta errori"""
    logger.info(f"Valutazione ROBUSTA modello {model_name}...")
    
    start_time = time.time()
    
    # Crea pipeline
    captioner = create_pipeline_safe(model_name)
    if captioner is None:
        return None
    
    pipeline_time = time.time() - start_time
    logger.info(f"Pipeline {model_name} creata in {pipeline_time:.1f}s")
    
    results_data = []
    errors = {'svg_error': 0, 'inference_error': 0, 'timeout': 0}
    
    # Limita esempi per test
    if max_examples:
        test_data = test_data[:max_examples]
        logger.info(f"Test limitato a {max_examples} esempi")
    
    # Processa i dati
    for i, item in enumerate(tqdm(test_data, desc=f"Robust {model_name}")):
        try:
            # Estrai SVG
            if 'xml' in item:
                svg_data = item['xml']
            elif 'svg_xml' in item:
                svg_data = item['svg_xml']
            else:
                logger.warning(f"Nessun campo SVG nell'esempio {i}")
                continue

            # Rasterizza con timeout
            image = rasterize_svg_safe(svg_data, timeout_sec=5)
            if image is None:
                errors['svg_error'] += 1
                generated_caption = "[SVG_ERROR]"
            else:
                # Ridimensiona
                if image.size[0] > 384 or image.size[1] > 384:
                    image.thumbnail((384, 384), Image.Resampling.LANCZOS)

                # Inferenza con timeout
                generated_caption = safe_inference(captioner, image, timeout_sec=15)
                
                if "[TIMEOUT_ERROR]" in generated_caption:
                    errors['timeout'] += 1
                elif "[INFERENCE_ERROR]" in generated_caption:
                    errors['inference_error'] += 1

            # Salva risultati
            reference_caption = item.get('caption', item.get('reference_caption', ''))
            
            results_data.append({
                'id': item.get('id', item.get('example_id', f'example_{i}')),
                'reference': reference_caption,
                'generated': generated_caption
            })

            # Log progresso ogni 25 esempi
            if (i + 1) % 25 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / (i + 1)
                eta = avg_time * (len(test_data) - i - 1)
                logger.info(f"Processati {i + 1}/{len(test_data)} esempi - ETA: {eta/60:.1f}min")
                logger.info(f"Errori: SVG={errors['svg_error']}, Inference={errors['inference_error']}, Timeout={errors['timeout']}")
                
        except Exception as e:
            logger.error(f"Errore critico esempio {i}: {e}")
            continue

    # Salva risultati
    output_path = Path(output_dir) / f"baseline_{model_name}_robust"
    output_path.mkdir(parents=True, exist_ok=True)

    with open(output_path / 'predictions.jsonl', 'w') as f:
        for pred in results_data:
            f.write(json.dumps(pred) + '\n')
    
    # Salva statistiche errori
    with open(output_path / 'error_stats.json', 'w') as f:
        json.dump(errors, f, indent=2)

    total_time = time.time() - start_time
    success_rate = (len(results_data) - sum(errors.values())) / len(results_data) * 100
    
    logger.info(f"Risultati salvati in {output_path}")
    logger.info(f"Processati {len(results_data)} esempi in {total_time/60:.1f}min")
    logger.info(f"Success rate: {success_rate:.1f}%")
    logger.info(f"Errori finali: {errors}")
    
    return results_data

def main():
    parser = argparse.ArgumentParser(description='Baseline ROBUSTO con timeout')
    parser.add_argument('--test_file', required=True)
    parser.add_argument('--output_dir', required=True)
    parser.add_argument('--models', nargs='+', choices=['idefix', 'flores2', 'blip2'], default=['idefix'])
    parser.add_argument('--max_examples', type=int, help='Limita numero esempi per test')

    args = parser.parse_args()

    # Verifica versione
    import transformers
    logger.info(f"🚀 Transformers: {transformers.__version__}")

    # Carica dataset
    with open(args.test_file, 'r') as f:
        test_data = json.load(f)
    logger.info(f"Dataset: {len(test_data)} esempi")

    # Valuta modelli
    for model_name in args.models:
        logger.info(f"Inizio valutazione ROBUSTA {model_name}")
        
        try:
            results = evaluate_model_robust(model_name, test_data, args.output_dir, args.max_examples)
            if results:
                logger.info(f"✅ {model_name} completato: {len(results)} esempi")
            else:
                logger.error(f"❌ {model_name} fallito")
        except Exception as e:
            logger.error(f"Errore valutazione {model_name}: {e}")

    logger.info("🎉 Baseline ROBUSTO completato!")

if __name__ == '__main__':
    main()
