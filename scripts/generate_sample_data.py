#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import random
import argparse
from typing import List, Dict, Any

# Modelli da simulare
MODELS = [
    "meta-llama/Llama-3.1-8B-Instruct",
    "google/gemma-2-9b-it",
    "mistralai/Mistral-7B-Instruct-v0.2",
    "microsoft/phi-3-mini-4k-instruct",
    "Qwen/Qwen2-7B-Instruct"
]

# Esempi di SVG per il test
SVG_EXAMPLES = [
    '<svg width="100" height="100"><path d="M10,30 Q25,5 40,30 T70,30" fill="none" stroke="black" stroke-width="2"/></svg>',
    '<svg width="100" height="100"><circle cx="50" cy="50" r="40" stroke="black" stroke-width="2" fill="red"/></svg>',
    '<svg width="100" height="100"><rect x="10" y="10" width="80" height="80" fill="blue" stroke="black" stroke-width="2"/></svg>',
    '<svg width="100" height="100"><line x1="10" y1="10" x2="90" y2="90" stroke="black" stroke-width="2"/></svg>',
    '<svg width="100" height="100"><polygon points="50,10 90,90 10,90" fill="green" stroke="black" stroke-width="2"/></svg>'
]

# Esempi di didascalie tecniche
TECHNICAL_CAPTIONS = [
    "Questa immagine SVG contiene un elemento path con coordinate M10,30 che definisce una curva Bezier quadratica.",
    "L'SVG mostra un elemento circle con raggio 40px posizionato al centro del canvas di 100x100px.",
    "Questo codice SVG definisce un rettangolo di dimensioni 80x80px con bordo nero e riempimento blu.",
    "L'immagine SVG contiene una linea che va dal punto (10,10) al punto (90,90) con spessore del tratto di 2px.",
    "Questo SVG rappresenta un poligono triangolare definito dai punti (50,10), (90,90) e (10,90) con riempimento verde."
]

# Esempi di didascalie visive
VISUAL_CAPTIONS = [
    "L'immagine mostra una linea ondulata che forma una curva elegante, simile a un'onda.",
    "L'immagine rappresenta un cerchio rosso con bordo nero al centro di uno sfondo bianco.",
    "L'immagine mostra un quadrato blu con bordo nero su sfondo bianco.",
    "L'immagine contiene una linea diagonale che attraversa lo spazio da sinistra in alto a destra in basso.",
    "L'immagine mostra un triangolo verde con bordo nero, con la punta rivolta verso l'alto."
]

# Esempi di didascalie miste
MIXED_CAPTIONS = [
    "Una linea curva a forma di onda creata con un elemento path SVG e comandi di curva Bezier.",
    "Un cerchio rosso con bordo nero di 2px, posizionato al centro di un canvas SVG di 100x100px.",
    "Un rettangolo blu con bordo nero definito dalle coordinate x=10, y=10 e dimensioni 80x80px.",
    "Una linea diagonale che attraversa il canvas SVG dal punto (10,10) al punto (90,90).",
    "Un triangolo verde con bordo nero definito da tre punti in un elemento polygon SVG."
]

def generate_sample_result(svg_index: int, model_index: int, caption_type: str) -> Dict[str, Any]:
    """
    Genera un risultato di esempio per l'inferenza zero-shot.
    
    Args:
        svg_index: Indice dell'esempio SVG
        model_index: Indice del modello
        caption_type: Tipo di didascalia ('technical', 'visual', 'mixed')
        
    Returns:
        Dizionario con il risultato generato
    """
    svg = SVG_EXAMPLES[svg_index % len(SVG_EXAMPLES)]
    model = MODELS[model_index % len(MODELS)]
    
    if caption_type == 'technical':
        caption = TECHNICAL_CAPTIONS[svg_index % len(TECHNICAL_CAPTIONS)]
    elif caption_type == 'visual':
        caption = VISUAL_CAPTIONS[svg_index % len(VISUAL_CAPTIONS)]
    else:  # mixed
        caption = MIXED_CAPTIONS[svg_index % len(MIXED_CAPTIONS)]
    
    # Aggiungi un po' di variabilità alla lunghezza
    if random.random() < 0.3:
        caption = caption + " " + random.choice([
            "Il contrasto tra i colori crea un effetto visivo interessante.",
            "Questo tipo di grafica vettoriale è ideale per interfacce responsive.",
            "L'elemento è scalabile senza perdita di qualità, tipico dei formati vettoriali.",
            "La semplicità del design comunica efficacemente il concetto visivo."
        ])
    
    return {
        "svg": svg,
        "model": model,
        "generated_caption": caption,
        "prompt": f"Genera una didascalia per il seguente codice SVG:\n\n{svg}",
        "timestamp": "2025-04-15T12:34:56Z"
    }

def generate_sample_data(num_samples: int = 100, output_path: str = "sample_zero_shot_results.jsonl") -> None:
    """
    Genera un set di dati di esempio per l'analisi zero-shot.
    
    Args:
        num_samples: Numero di campioni da generare
        output_path: Percorso dove salvare i dati generati
    """
    results = []
    
    # Distribuzione dei tipi di didascalie per modello
    model_distributions = {
        "meta-llama/Llama-3.1-8B-Instruct": {'technical': 0.6, 'visual': 0.2, 'mixed': 0.2},
        "google/gemma-2-9b-it": {'technical': 0.4, 'visual': 0.4, 'mixed': 0.2},
        "mistralai/Mistral-7B-Instruct-v0.2": {'technical': 0.5, 'visual': 0.3, 'mixed': 0.2},
        "microsoft/phi-3-mini-4k-instruct": {'technical': 0.3, 'visual': 0.5, 'mixed': 0.2},
        "Qwen/Qwen2-7B-Instruct": {'technical': 0.4, 'visual': 0.3, 'mixed': 0.3}
    }
    
    for i in range(num_samples):
        # Seleziona un modello e un esempio SVG
        model_index = i % len(MODELS)
        model = MODELS[model_index]
        svg_index = random.randint(0, len(SVG_EXAMPLES) - 1)
        
        # Seleziona il tipo di didascalia in base alla distribuzione del modello
        distribution = model_distributions[model]
        r = random.random()
        
        if r < distribution['technical']:
            caption_type = 'technical'
        elif r < distribution['technical'] + distribution['visual']:
            caption_type = 'visual'
        else:
            caption_type = 'mixed'
        
        # Genera il risultato
        result = generate_sample_result(svg_index, model_index, caption_type)
        results.append(result)
    
    # Salva i risultati in formato JSONL
    with open(output_path, 'w', encoding='utf-8') as f:
        for result in results:
            f.write(json.dumps(result) + '\n')
    
    print(f"Generati {len(results)} campioni di esempio in {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Genera dati di esempio per l'analisi zero-shot")
    parser.add_argument("--num_samples", type=int, default=100, help="Numero di campioni da generare")
    parser.add_argument("--output_path", type=str, default="data/sample_zero_shot_results.jsonl", help="Percorso dove salvare i dati generati")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    
    # Genera i dati
    generate_sample_data(args.num_samples, args.output_path)

if __name__ == "__main__":
    main()
