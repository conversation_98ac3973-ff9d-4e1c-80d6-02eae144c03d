#!/bin/bash

# Script per generare il report HTML e i grafici radar

# Verifica se il job è già in esecuzione
if squeue -u ediluzio | grep -q "gen_report"; then
    echo "Un job di generazione del report è già in esecuzione."
    exit 1
fi

# Invia il job SLURM
echo "Invio del job per la generazione del report..."
sbatch /work/tesi_ediluzio/scripts/slurm/generate_training_report.slurm

echo "Job inviato. Controlla lo stato con 'squeue -u ediluzio'."
echo "I log saranno disponibili in /work/tesi_ediluzio/logs/gen_report_*.out"
