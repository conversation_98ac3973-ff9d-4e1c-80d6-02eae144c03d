#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel, PeftConfig
import wandb
import torch

# Configurazione matplotlib
plt.style.use('ggplot')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

def setup_wandb(project_name="captioner", run_name="lora_parameters_analysis", entity="337543-unimore"):
    """Inizializza Weights & Biands per il tracking."""
    try:
        wandb.init(
            project=project_name,
            name=run_name,
            entity=entity,
            config={
                "analysis_type": "lora_parameters"
            }
        )
        print(f"W&B inizializzato: {wandb.run.name} (ID: {wandb.run.id})")
    except Exception as e:
        print(f"Errore nell'inizializzazione di W&B: {e}")
        print("Continuazione senza tracking W&B...")

def load_model_info(model_paths):
    """Carica informazioni sui modelli LoRA."""
    models_info = []
    
    for model_path in model_paths:
        try:
            # Carica la configurazione PEFT
            config = PeftConfig.from_pretrained(model_path)
            
            # Estrai informazioni
            model_info = {
                'model_path': model_path,
                'model_name': os.path.basename(model_path),
                'base_model': config.base_model_name_or_path,
                'task_type': config.task_type,
                'lora_r': config.r,
                'lora_alpha': config.lora_alpha,
                'lora_dropout': config.lora_dropout,
                'target_modules': config.target_modules,
                'bias': config.bias
            }
            
            # Carica il modello base per calcolare i parametri
            try:
                base_model = AutoModelForCausalLM.from_pretrained(
                    config.base_model_name_or_path,
                    torch_dtype=torch.float16,  # Usa float16 per risparmiare memoria
                    device_map="auto"
                )
                
                # Calcola il numero totale di parametri
                total_params = sum(p.numel() for p in base_model.parameters())
                
                # Calcola il numero di parametri addestrabili con LoRA
                trainable_params = 0
                for module_name in config.target_modules:
                    for name, param in base_model.named_parameters():
                        if module_name in name:
                            # Per ogni modulo target, calcola i parametri LoRA
                            # LoRA aggiunge 2 matrici di dimensione (r x dim_in) e (dim_out x r)
                            if 'weight' in name:
                                shape = param.shape
                                if len(shape) == 2:  # Solo per matrici 2D
                                    dim_out, dim_in = shape
                                    # Parametri LoRA: A (r x dim_in) + B (dim_out x r)
                                    lora_params = config.r * dim_in + dim_out * config.r
                                    trainable_params += lora_params
                
                model_info['total_params'] = total_params
                model_info['trainable_params'] = trainable_params
                model_info['trainable_percentage'] = (trainable_params / total_params) * 100
                
                # Libera memoria
                del base_model
                torch.cuda.empty_cache()
                
            except Exception as e:
                print(f"Errore nel calcolo dei parametri per {model_path}: {e}")
                model_info['total_params'] = 0
                model_info['trainable_params'] = 0
                model_info['trainable_percentage'] = 0
            
            models_info.append(model_info)
            print(f"Informazioni caricate per {model_path}")
            
        except Exception as e:
            print(f"Errore nel caricamento delle informazioni per {model_path}: {e}")
    
    return models_info

def create_models_comparison_df(models_info):
    """Crea un DataFrame per confrontare i modelli."""
    df = pd.DataFrame(models_info)
    return df

def plot_trainable_parameters(df, output_dir):
    """Crea un grafico dei parametri addestrabili per modello."""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Ordina per numero di parametri addestrabili
    df_sorted = df.sort_values('trainable_params', ascending=False)
    
    # Crea il grafico a barre
    sns.barplot(data=df_sorted, x='model_name', y='trainable_params', ax=ax)
    
    ax.set_title('Parametri Addestrabili per Modello', fontsize=16)
    ax.set_xlabel('Modello', fontsize=14)
    ax.set_ylabel('Numero di Parametri Addestrabili', fontsize=14)
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')
    
    # Formatta i numeri sull'asse y
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, loc: f"{x/1e6:.1f}M"))
    
    # Aggiungi etichette con i valori
    for i, p in enumerate(ax.patches):
        height = p.get_height()
        ax.text(p.get_x() + p.get_width()/2.,
                height + 0.1 * df_sorted['trainable_params'].max(),
                f"{height/1e6:.2f}M",
                ha="center", fontsize=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'trainable_parameters.png'), dpi=300, bbox_inches='tight')
    
    return fig

def plot_trainable_percentage(df, output_dir):
    """Crea un grafico della percentuale di parametri addestrabili per modello."""
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Ordina per percentuale di parametri addestrabili
    df_sorted = df.sort_values('trainable_percentage', ascending=False)
    
    # Crea il grafico a barre
    sns.barplot(data=df_sorted, x='model_name', y='trainable_percentage', ax=ax)
    
    ax.set_title('Percentuale di Parametri Addestrabili per Modello', fontsize=16)
    ax.set_xlabel('Modello', fontsize=14)
    ax.set_ylabel('Percentuale di Parametri Addestrabili (%)', fontsize=14)
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')
    
    # Aggiungi etichette con i valori
    for i, p in enumerate(ax.patches):
        height = p.get_height()
        ax.text(p.get_x() + p.get_width()/2.,
                height + 0.01,
                f"{height:.4f}%",
                ha="center", fontsize=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'trainable_percentage.png'), dpi=300, bbox_inches='tight')
    
    return fig

def plot_lora_hyperparameters(df, output_dir):
    """Crea un grafico degli iperparametri LoRA per modello."""
    # Crea un grafico per r e alpha
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Prepara i dati
    x = np.arange(len(df))
    width = 0.35
    
    # Crea le barre
    rects1 = ax.bar(x - width/2, df['lora_r'], width, label='LoRA r', color='skyblue')
    rects2 = ax.bar(x + width/2, df['lora_alpha'], width, label='LoRA alpha', color='coral')
    
    ax.set_title('Iperparametri LoRA per Modello', fontsize=16)
    ax.set_xlabel('Modello', fontsize=14)
    ax.set_ylabel('Valore', fontsize=14)
    ax.set_xticks(x)
    ax.set_xticklabels(df['model_name'], rotation=45, ha='right')
    ax.legend()
    
    # Aggiungi etichette con i valori
    def autolabel(rects):
        for rect in rects:
            height = rect.get_height()
            ax.annotate(f'{height}',
                        xy=(rect.get_x() + rect.get_width()/2, height),
                        xytext=(0, 3),
                        textcoords="offset points",
                        ha='center', va='bottom')
    
    autolabel(rects1)
    autolabel(rects2)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'lora_hyperparameters.png'), dpi=300, bbox_inches='tight')
    
    return fig

def plot_target_modules_comparison(df, output_dir):
    """Crea un grafico di confronto dei moduli target per modello."""
    # Estrai i moduli target unici
    all_modules = set()
    for modules in df['target_modules']:
        if isinstance(modules, list):
            all_modules.update(modules)
    
    # Crea una matrice di presenza dei moduli
    module_presence = {}
    for module in all_modules:
        module_presence[module] = []
        for modules in df['target_modules']:
            if isinstance(modules, list):
                module_presence[module].append(1 if module in modules else 0)
            else:
                module_presence[module].append(0)
    
    # Crea un DataFrame
    module_df = pd.DataFrame(module_presence, index=df['model_name'])
    
    # Crea un heatmap
    fig, ax = plt.subplots(figsize=(14, 10))
    
    sns.heatmap(module_df, cmap='YlGnBu', cbar=False, ax=ax)
    
    ax.set_title('Moduli Target per Modello', fontsize=16)
    ax.set_xlabel('Modulo', fontsize=14)
    ax.set_ylabel('Modello', fontsize=14)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'target_modules_comparison.png'), dpi=300, bbox_inches='tight')
    
    return fig

def main():
    parser = argparse.ArgumentParser(description="Analisi dei parametri LoRA")
    parser.add_argument("--model_paths", type=str, nargs='+', required=True,
                        help="Path ai modelli LoRA da analizzare")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/analysis/lora_parameters",
                        help="Directory per salvare i risultati dell'analisi")
    parser.add_argument("--use_wandb", action="store_true",
                        help="Usa Weights & Biands per il tracking")
    
    args = parser.parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Inizializza W&B
    if args.use_wandb:
        setup_wandb()
    
    # Carica le informazioni sui modelli
    models_info = load_model_info(args.model_paths)
    
    if not models_info:
        print("Nessuna informazione sui modelli caricata. Uscita.")
        return
    
    # Crea il DataFrame di confronto
    df = create_models_comparison_df(models_info)
    
    # Salva il DataFrame
    df.to_csv(os.path.join(args.output_dir, 'models_comparison.csv'), index=False)
    
    # Crea i grafici
    trainable_params_fig = plot_trainable_parameters(df, args.output_dir)
    trainable_percentage_fig = plot_trainable_percentage(df, args.output_dir)
    lora_hyperparams_fig = plot_lora_hyperparameters(df, args.output_dir)
    target_modules_fig = plot_target_modules_comparison(df, args.output_dir)
    
    # Carica i grafici su W&B
    if args.use_wandb:
        try:
            wandb.log({
                "trainable_parameters": wandb.Image(trainable_params_fig),
                "trainable_percentage": wandb.Image(trainable_percentage_fig),
                "lora_hyperparameters": wandb.Image(lora_hyperparams_fig),
                "target_modules_comparison": wandb.Image(target_modules_fig)
            })
            
            # Carica anche il DataFrame
            wandb.log({"models_comparison": wandb.Table(dataframe=df)})
            
            print("Grafici e dati caricati su W&B")
        except Exception as e:
            print(f"Errore nel caricamento su W&B: {e}")
    
    # Chiudi W&B
    if args.use_wandb and wandb.run is not None:
        wandb.finish()
    
    print(f"Analisi completata. Risultati salvati in {args.output_dir}")

if __name__ == "__main__":
    main()
