#!/bin/bash
#SBATCH --job-name=llama_t6_resume_FIXED
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=16
#SBATCH --gres=gpu:2
#SBATCH --mem=128G
#SBATCH --time=24:00:00
#SBATCH --output=logs/llama_t6_resume_FIXED_%j.out
#SBATCH --error=logs/llama_t6_resume_FIXED_%j.err

echo "=== LLAMA T6 RESUME FIXED ==="
echo "Job ID: $SLURM_JOB_ID"
echo "Data inizio: $(date)"
echo "🔧 TRAINING SCRIPT FIXATO - RIMOSSO EARLY STOPPING PERSONALIZZATO"
echo "✅ CHECKPOINT SICURO - SALVATO PRIMA DEL BUG (8 Jun 10:55)"

# Attivazione ambiente
source svg_env/bin/activate

# Pulizia memoria GPU
echo "🧹 Pulizia memoria GPU..."
nvidia-smi --gpu-reset || true
sleep 5

# Verifica GPU
echo "🔍 Verifica GPU disponibili:"
nvidia-smi --query-gpu=index,name,memory.total,memory.free --format=csv,noheader,nounits

# Attesa per stabilizzazione
echo "⏳ Attesa 15 secondi per stabilizzazione memoria..."
sleep 15

# VERIFICA CHECKPOINT ESISTENTE
echo "📊 Verifica checkpoint esistente..."
if [ -d "experiments/xml_direct_input/outputs/llama_t6_24h/checkpoint-12500" ]; then
    echo "✅ Checkpoint 12500 trovato - RESUME ABILITATO"
    echo "📈 Progresso precedente: Step 12500/50000 (25%), Loss: 1.1348 → 0.6882 (-39%)"
    
    # BACKUP del checkpoint corrotto
    echo "💾 Backup del checkpoint corrotto..."
    cp -r experiments/xml_direct_input/outputs/llama_t6_24h/checkpoint-12500 \
          experiments/xml_direct_input/outputs/llama_t6_24h/checkpoint-12500_BACKUP_$(date +%Y%m%d_%H%M%S)
else
    echo "❌ ERRORE: Checkpoint 12500 non trovato!"
    exit 1
fi

# Configurazione memoria
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=0

# TRAINING LLAMA T6 CON SCRIPT FIXATO
echo "🚀 Avvio training Llama T6 con script FIXATO..."
echo "🔧 NOTA: Early stopping personalizzato RIMOSSO - usando solo quello integrato"

torchrun --nproc_per_node=2 --master_port=29506 scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t6_24h.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t6_24h \
    --wandb_project svg_captioning_t6 \
    --wandb_entity 337543-unimore \
    --wandb_run_name llama_t6_24h_resume_FIXED \
    --early_stopping \
    --patience 10 \
    --min_delta 0.001

echo "✅ Training Llama T6 FIXED completato!"
echo "Data fine: $(date)"

echo "--- SCRIPT SLURM COMPLETATO ---"
