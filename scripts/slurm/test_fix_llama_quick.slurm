#!/bin/bash
#SBATCH --job-name=test_fix_llama
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:2
#SBATCH --mem=48G
#SBATCH --time=00:10:00
#SBATCH --output=logs/test_fix_llama_%j.out
#SBATCH --error=logs/test_fix_llama_%j.err

echo "🚀 TEST RAPIDO FIX LLAMA ROPE_SCALING"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva environment (best practice conda)
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Variabili ambiente per distributed training
export CUDA_VISIBLE_DEVICES=0,1
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128

# Test Llama con fix rope_scaling
echo "🔧 Test Llama con fix rope_scaling..."
torchrun --nproc_per_node=2 --master_port=29502 scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_test5_32k.json \
    --config_path experiments/xml_direct_input/configs/test_fix_llama_quick.json \
    --output_dir experiments/xml_direct_input/outputs/test_fix_quick_llama \
    --wandb_project svg_captioning_test_fix \
    --wandb_entity 337543-unimore \
    --wandb_run_name test_fix_quick_llama \
    --early_stopping \
    --patience 2 \
    --min_delta 0.001

echo "✅ Test Llama completato!"
