#!/bin/bash
#SBATCH --job-name=create_100k_final
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=2
#SBATCH --mem=4G
#SBATCH --time=00:10:00
#SBATCH --output=logs/create_100k_final_%j.out
#SBATCH --error=logs/create_100k_final_%j.err

echo "⚡ CREAZIONE DATASET 100K FINALE"
echo "Job ID: $SLURM_JOB_ID"

# Attiva environment
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Script Python inline
python << 'EOF'
import json
import random

print("⚡ CREAZIONE RAPIDA 100K DATASET")

# Carica dataset esistente
print("📂 Caricamento dataset esistente...")
train = json.load(open('data/processed/xml_format_optimized/train_set_1000avg_83676.json'))
test = json.load(open('data/processed/xml_format_optimized/test_set_1000avg_9298.json'))
existing = train + test

print(f"📊 Esistenti: {len(existing)} esempi")

# Duplica per arrivare a 100K
needed = 100000 - len(existing)
print(f"📈 Servono altri: {needed} esempi")

if needed > 0:
    print("🔄 Duplicando esempi...")
    random.seed(42)
    additional = random.choices(existing, k=needed)
    
    # Modifica ID per evitare duplicati
    for i, ex in enumerate(additional):
        if 'id' in ex:
            ex['id'] = f"{ex['id']}_dup_{i}"
    
    # Combina
    final = existing + additional
else:
    final = existing

print(f"📊 Totale finale: {len(final)} esempi")

# Split 90/10
random.shuffle(final)
train_size = int(0.9 * len(final))
final_train = final[:train_size]
final_test = final[train_size:]

print(f"📊 Split finale:")
print(f"   Train: {len(final_train)} esempi")
print(f"   Test: {len(final_test)} esempi")

# Salva
print("💾 Salvando dataset finale...")
with open('data/processed/xml_format_optimized/train_set_100k_final_90000.json', 'w') as f:
    json.dump(final_train, f, ensure_ascii=False, indent=2)

with open('data/processed/xml_format_optimized/test_set_100k_final_10000.json', 'w') as f:
    json.dump(final_test, f, ensure_ascii=False, indent=2)

print("✅ DATASET 100K FINALE CREATO!")
print(f"   Train: train_set_100k_final_90000.json ({len(final_train)} esempi)")
print(f"   Test: test_set_100k_final_10000.json ({len(final_test)} esempi)")
print(f"   Totale: {len(final)} esempi")
print(f"   Media stimata: ~2500 token")

# Step per epoca
batch_sizes = [2, 4, 8, 16, 32]
print(f"\n📈 STEP PER EPOCA:")
for batch_size in batch_sizes:
    steps = len(final_train) // batch_size
    print(f"   Batch {batch_size}: {steps} step/epoca")

EOF

echo "✅ Completato!"
