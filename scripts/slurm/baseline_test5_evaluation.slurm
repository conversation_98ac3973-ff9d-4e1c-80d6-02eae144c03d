#!/bin/bash
#SBATCH --job-name=baseline_test5
#SBATCH --output=logs/baseline_test5_%j.out
#SBATCH --error=logs/baseline_test5_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=4
#SBATCH --mem=32G
#SBATCH --time=12:00:00

echo "📊 BASELINE EVALUATION TEST_5 DATASET"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"

# Crea environment separato per baseline
if [ ! -d "baseline_env" ]; then
    python -m venv baseline_env
    source baseline_env/bin/activate
    pip install --upgrade pip
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    pip install transformers datasets accelerate
    pip install pillow requests beautifulsoup4
    pip install nltk rouge-score sacrebleu
    pip install clip-by-openai
else
    source baseline_env/bin/activate
fi

cd /work/tesi_ediluzio

# Configurazione
export CUDA_VISIBLE_DEVICES=0
export HF_TOKEN="*************************************"

echo "🔍 Valutazione baseline su dataset TEST_5..."
echo "Dataset: data/processed/xml_format_optimized/test_set_test5_3k.json"
echo "Esempi: 3556"

# Esegui valutazione per ogni modello baseline CORRETTO (IDEFICS 3, Florence 2, BLIP-2)
python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file data/processed/xml_format_optimized/test_set_test5_3k.json \
    --output_dir results/baseline_test5_evaluation \
    --models idefics florence2 blip-2 \
    --batch_size 8 \
    --wandb_project svg_captioning_baselines_test5

echo "✅ Baseline evaluation completata!"
