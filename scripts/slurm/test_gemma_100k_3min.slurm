#!/bin/bash
#SBATCH --job-name=test_gemma_100k
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:2
#SBATCH --mem=48G
#SBATCH --time=00:03:00
#SBATCH --output=logs/test_gemma_100k_%j.out
#SBATCH --error=logs/test_gemma_100k_%j.err

echo "🧪 TEST GEMMA 100K DATASET - 3 MINUTI"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# ATTIVA ENVIRONMENT (best practice conda)
echo "🔧 Setup environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# VARIABILI AMBIENTE - FIX MEMORIA GPU AGGRESSIVO
export CUDA_VISIBLE_DEVICES=0,1
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:False,max_split_size_mb:32,garbage_collection_threshold:0.6
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export TORCH_USE_CUDA_DSA=1

# VERIFICA DATASET
echo "📊 Verifica dataset..."
python -c "
import json
train_file = 'data/processed/xml_format_optimized/train_set_100k_final_90000.json'
with open(train_file, 'r') as f:
    data = json.load(f)
print(f'✅ Dataset caricato: {len(data)} esempi')
print(f'✅ Primo esempio ID: {data[0].get(\"id\", \"N/A\")}')
"

# TRAINING TEST 3 MINUTI
echo "🚀 Avvio training test Gemma..."
torchrun --nproc_per_node=2 --master_port=29501 scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/test_5_gemma_final_24h.json \
    --output_dir experiments/xml_direct_input/outputs/test_gemma_100k_3min \
    --wandb_project svg_captioning_test_100k \
    --wandb_entity 337543-unimore \
    --wandb_run_name test_gemma_100k_3min \
    --early_stopping \
    --patience 5 \
    --min_delta 0.001

echo "✅ Test Gemma completato!"
