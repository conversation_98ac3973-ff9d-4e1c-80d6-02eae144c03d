#!/bin/bash
#SBATCH --job-name=create_1000avg
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=02:00:00
#SBATCH --output=logs/create_1000avg_%j.out
#SBATCH --error=logs/create_1000avg_%j.err

echo "🎯 CREAZIONE DATASET 1000 TOKEN MEDI"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Attiva environment (best practice conda)
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Prima verifica dimensione dataset completo
echo "🔍 Analisi dataset completo..."
python -c "
import json
print('Caricamento dataset completo...')
with open('data/processed/filtered_svg_all_sources_FULL.json', 'r') as f:
    data = json.load(f)
print(f'Dataset completo: {len(data)} esempi')

# Campiona alcuni esempi per vedere la struttura
import random
sample = random.sample(data, min(5, len(data)))
for i, ex in enumerate(sample):
    print(f'Esempio {i+1}: {list(ex.keys())}')
    if 'xml' in ex:
        print(f'  XML length: {len(ex[\"xml\"])} chars')
    if 'caption' in ex:
        print(f'  Caption: {ex[\"caption\"][:100]}...')
"

# Crea dataset con Gemma tokenizer (più token per SVG)
echo "🔧 Creazione dataset con Gemma tokenizer..."
python scripts/data_processing/create_1000_avg_dataset.py \
    --data_file data/processed/filtered_svg_all_sources_FULL.json \
    --tokenizer google/gemma-2-9b-it \
    --target_avg 1000 \
    --max_examples 100000 \
    --output_dir data/processed/xml_format_optimized

# Crea dataset con Llama tokenizer
echo "🔧 Creazione dataset con Llama tokenizer..."
python scripts/data_processing/create_1000_avg_dataset.py \
    --data_file data/processed/filtered_svg_all_sources_FULL.json \
    --tokenizer meta-llama/Llama-3.1-8B-Instruct \
    --target_avg 1000 \
    --max_examples 100000 \
    --output_dir data/processed/xml_format_optimized

echo "✅ Creazione dataset 1000 token medi completata!"
