#!/bin/bash
#SBATCH --job-name=test_5_llama_final_24h
#SBATCH --output=logs/test_5_llama_final_24h_%j.out
#SBATCH --error=logs/test_5_llama_final_24h_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00
#SBATCH --priority=HIGH
#SBATCH --constraint=gpu_A40_48G

echo "🚀 TEST_5_LLAMA FINAL - TRAINING 24 ORE CON 2 GPU 🚀"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Crea directory logs se non esiste
mkdir -p logs

# VERIFICA ALLOCAZIONE GPU
echo "🔍 Verifica allocazione 2 GPU..."
echo "SLURM_GPUS_ON_NODE: $SLURM_GPUS_ON_NODE"
echo "SLURM_GPUS_PER_NODE: $SLURM_GPUS_PER_NODE"
echo "SLURM_GPUS: $SLURM_GPUS"
echo "SLURM_NTASKS: $SLURM_NTASKS"
echo "SLURM_NNODES: $SLURM_NNODES"
nvidia-smi --query-gpu=index,name,memory.total --format=csv

# CONTA GPU DISPONIBILI
gpu_count=$(nvidia-smi --list-gpus | wc -l)
echo "🔢 GPU rilevate da nvidia-smi: $gpu_count"

# 🚨 FIX CRITICO: Carica moduli CUDA per evitare driver errors (simile al fix Anaconda)
echo "🔧 Caricamento moduli CUDA..."
module load cuda/12.6.3 || module load cuda/12.1 || module load cuda/11.8 || module load cuda || echo "⚠️ Nessun modulo CUDA trovato"
module load anaconda3 || echo "⚠️ Modulo anaconda3 non trovato"

# Verifica CUDA dopo caricamento moduli
echo "🔍 Verifica CUDA post-moduli:"
nvidia-smi || echo "⚠️ nvidia-smi non disponibile"
nvcc --version || echo "⚠️ nvcc non disponibile"

# ATTIVA ENVIRONMENT (best practice conda)
echo "🔧 Setup environment TEST_5..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# VERIFICA AMBIENTE DISTRIBUITO
echo "🔍 Verifica environment per 2 GPU..."
python -c "
import torch
import torch.distributed as dist
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')
print(f'GPU count: {torch.cuda.device_count()}')
print(f'Torch distributed available: {dist.is_available()}')
print(f'NCCL available: {dist.is_nccl_available()}')

if torch.cuda.is_available():
    print('🔍 Lista GPU disponibili:')
    for i in range(min(torch.cuda.device_count(), 2)):
        print(f'GPU {i}: {torch.cuda.get_device_name(i)} - {torch.cuda.get_device_properties(i).total_memory / 1024**3:.1f} GB')
"

# CONFIGURAZIONE CUDA E AMBIENTE PER 2 GPU - OTTIMIZZATA PER LLAMA
export CUDA_VISIBLE_DEVICES=0,1
export TOKENIZERS_PARALLELISM=false
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:128
export HF_TOKEN="*************************************"
export WANDB_PROJECT="svg_captioning_final_24h"
export WANDB_ENTITY="337543-unimore"
export WANDB_RUN_NAME="test_5_llama_final_24h"

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

echo ""
echo "🎯 TEST_5_LLAMA FINAL - TRAINING COMPLETO 24 ORE..."
echo "📊 Dataset: train_set_test5_32k.json (32,000 esempi)"
echo "📊 Test: test_set_test5_3k.json (3,556 esempi)"
echo "🔧 Context window: 2048 token"
echo "📈 Max steps: 25,000 con early stopping"
echo "⚡ Batch size: 8 per GPU = 16 effective"
echo "🖥️ GPU: 2x boost_usr_prod"
echo "📊 Step per epoca: 2000"
echo "📈 Evaluation: Ogni 500 step"
echo "💾 Save: Ogni 500 step su best loss"
echo "🌐 Wandb: svg_captioning_final_24h"
echo "⏰ Durata: 24 ore"
echo ""

# VERIFICA DATASET
echo "📂 Verifica dataset..."
if [ ! -f "data/processed/xml_format_optimized/train_set_test5_32k.json" ]; then
    echo "❌ Dataset train non trovato!"
    exit 1
fi

if [ ! -f "data/processed/xml_format_optimized/test_set_test5_3k.json" ]; then
    echo "❌ Dataset test non trovato!"
    exit 1
fi

train_size=$(python -c "import json; data=json.load(open('data/processed/xml_format_optimized/train_set_test5_32k.json')); print(len(data))")
test_size=$(python -c "import json; data=json.load(open('data/processed/xml_format_optimized/test_set_test5_3k.json')); print(len(data))")

echo "✅ Dataset verificato:"
echo "   Train: $train_size esempi"
echo "   Test: $test_size esempi"

# CALCOLA STEP PER EPOCA CON NUOVO BATCH SIZE
effective_batch_size=$((2 * 8 * 1))  # 2 GPU * 8 batch_size * 1 grad_accum
steps_per_epoch=$((train_size / effective_batch_size))
echo "   Step per epoca: $steps_per_epoch"

# CREA DIRECTORY OUTPUT
mkdir -p experiments/xml_direct_input/outputs/test_5_llama_final_24h

# VERIFICA CHECKPOINT PER RESUME
echo "🔍 Verifica checkpoint esistenti per resume..."
latest_checkpoint=$(find experiments/xml_direct_input/outputs/test_5_llama_final_24h -name "checkpoint-*" -type d | sort -V | tail -1)
if [ -n "$latest_checkpoint" ]; then
    echo "✅ Checkpoint trovato per resume: $(basename $latest_checkpoint)"
    resume_arg="--best_checkpoint_dir $latest_checkpoint"
else
    echo "ℹ️ Nessun checkpoint trovato - training da zero"
    resume_arg=""
fi

echo ""
echo "🚀 INIZIO TRAINING TEST_5_LLAMA FINAL - 24 ORE (RESUME)..."

# PULIZIA MEMORIA PRIMA DEL TRAINING
echo "🧹 Pulizia memoria GPU..."
python -c "
import torch
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print('✅ Cache GPU pulita')
"

# Genera porta unica basata su Job ID per evitare conflitti
MASTER_PORT=$((29500 + ($SLURM_JOB_ID % 1000)))
echo "🔧 Porta master: $MASTER_PORT"

# TRAINING 2 GPU CON TORCHRUN - CONFIGURAZIONE CHE FUNZIONAVA 2 GIORNI FA
torchrun --nproc_per_node=2 --master_port=29500 scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/test_5_llama_final_24h.json \
    --output_dir experiments/xml_direct_input/outputs/llama_100k_3000ctx \
    $resume_arg \
    --use_wandb \
    --wandb_project svg_captioning_final_24h \
    --wandb_entity 337543-unimore \
    --wandb_run_name test_5_llama_final_24h \
    --early_stopping \
    --patience 10 \
    --min_delta 0.001

echo ""
echo "✅ Training TEST_5_LLAMA FINAL completato!"
echo "Timestamp fine: $(date)"

# VERIFICA OUTPUT FINALE
echo "📊 Verifica output finale..."
ls -la experiments/xml_direct_input/outputs/test_5_llama_final_24h/

# VERIFICA CHECKPOINT FINALI
if [ -d "experiments/xml_direct_input/outputs/test_5_llama_final_24h" ]; then
    checkpoints=$(find experiments/xml_direct_input/outputs/test_5_llama_final_24h -name "checkpoint-*" -type d | wc -l)
    echo "📁 Checkpoint salvati: $checkpoints"

    if [ $checkpoints -gt 0 ]; then
        latest_checkpoint=$(find experiments/xml_direct_input/outputs/test_5_llama_final_24h -name "checkpoint-*" -type d | sort -V | tail -1)
        echo "📊 Ultimo checkpoint: $(basename $latest_checkpoint)"
        
        # Verifica dimensione checkpoint
        checkpoint_size=$(du -sh "$latest_checkpoint" | cut -f1)
        echo "💾 Dimensione checkpoint: $checkpoint_size"
    fi
fi

echo ""
echo "🎉 TEST_5_LLAMA FINAL - TRAINING 24 ORE COMPLETATO!"
echo "🌐 Risultati: https://wandb.ai/337543-unimore/svg_captioning_final_24h"
echo "📊 Checkpoint: experiments/xml_direct_input/outputs/test_5_llama_final_24h/"
