#!/bin/bash
#SBATCH --job-name=clip_eva
#SBATCH --output=logs/clip_evaluation_fixed_%j.log
#SBATCH --error=logs/clip_evaluation_fixed_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=12:00:00

echo "🔍 CLIP EVALUATION CON AMBIENTE RIPARATO 🔍"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Crea directory logs se non esiste
mkdir -p logs

# ATTIVA AMBIENTE RIPARATO
echo "📦 Attivazione ambiente riparato..."
source /work/tesi_ediluzio/svg_env/bin/activate

# INSTALLA CLIP (compatibile con PyTorch 2.1.0)
echo "📦 Installazione CLIP..."
pip install git+https://github.com/openai/CLIP.git

# VERIFICA AMBIENTE
echo "🔍 Verifica ambiente..."
python -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')

import clip
print('CLIP installato correttamente')

import numpy as np
print(f'NumPy: {np.__version__}')
"

# LOGIN HUGGING FACE
echo "🔑 Login Hugging Face..."
export HUGGING_FACE_HUB_TOKEN="*************************************"
huggingface-cli login --token *************************************

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

echo "🎯 Avvio valutazione CLIP..."

# ESECUZIONE SCRIPT DI VALUTAZIONE CLIP
python experiments/xml_direct_input/evaluate_clip_score.py \
    --llama_results experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/inference_results.jsonl \
    --gemma_results experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/inference_results.jsonl \
    --reference_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_file experiments/xml_direct_input/outputs/clip_scores_both_models_fixed.json

echo "✅ Valutazione CLIP completata!"
echo "Timestamp fine: $(date)"
