#!/bin/bash
#SBATCH --job-name=eval_gemma
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --time=02:00:00
#SBATCH --output=logs/eval_gemma_%j.out
#SBATCH --error=logs/eval_gemma_%j.err

echo "📊 EVALUATION GEMMA CHECKPOINTS"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva environment (best practice conda)
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Checkpoints da valutare
CHECKPOINTS=(
    "experiments/xml_direct_input/outputs/test_5_gemma_final_24h/checkpoint-8500"
    "experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/checkpoint-38800"
)

TEST_FILE="data/processed/xml_format_optimized/test_set_test5_3k.json"
OUTPUT_DIR="evaluation/gemma_checkpoints_eval"

mkdir -p $OUTPUT_DIR

for checkpoint in "${CHECKPOINTS[@]}"; do
    if [ -d "$checkpoint" ]; then
        echo "🔧 Evaluating checkpoint: $checkpoint"
        
        checkpoint_name=$(basename $(dirname $checkpoint))_$(basename $checkpoint)
        
        # Inferenza
        python scripts/inference/run_inference.py \
            --model_path google/gemma-2-9b-it \
            --adapter_path $checkpoint \
            --data_file $TEST_FILE \
            --output_file $OUTPUT_DIR/${checkpoint_name}_results.jsonl \
            --batch_size 4 \
            --max_length 512 \
            --device cuda
        
        # Calcolo metriche
        python scripts/evaluation/calculate_metrics.py \
            --predictions_file $OUTPUT_DIR/${checkpoint_name}_results.jsonl \
            --output_file $OUTPUT_DIR/${checkpoint_name}_metrics.json
        
        echo "✅ Checkpoint $checkpoint completato"
    else
        echo "❌ Checkpoint non trovato: $checkpoint"
    fi
done

echo "📊 Generazione report comparativo..."
python scripts/evaluation/compare_checkpoints.py \
    --eval_dir $OUTPUT_DIR \
    --output_file $OUTPUT_DIR/gemma_checkpoints_comparison.json

echo "✅ Evaluation Gemma completata!"
