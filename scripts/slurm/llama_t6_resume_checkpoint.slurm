#!/bin/bash
#SBATCH --job-name=llama_t6_resume
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:2
#SBATCH --mem=48G
#SBATCH --time=24:00:00
#SBATCH --output=logs/llama_t6_resume_%j.out
#SBATCH --error=logs/llama_t6_resume_%j.err

echo "🦙 LLAMA T6 - RESUME DA CHECKPOINT 12500 - 2 GPU PARALLELO"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"
echo "Data inizio: $(date)"

# ATTIVA ENVIRONMENT
echo "🔧 Setup environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# VARIABILI AMBIENTE - 2 GPU PARALLELO - MEMORIA SEMPLICE
export CUDA_VISIBLE_DEVICES=0,1
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:32
export TOKENIZERS_PARALLELISM=false
export OMP_NUM_THREADS=1
export CUDA_LAUNCH_BLOCKING=0

# PULIZIA AGGRESSIVA MEMORIA GPU E PROCESSI
echo "🧹 Pulizia aggressiva memoria GPU e processi..."

# Kill tutti i processi Python dell'utente che potrebbero occupare GPU
echo "🔪 Kill processi Python precedenti..."
pkill -f "python.*train_lora" || true
pkill -f "torchrun" || true
sleep 5

# Pulizia memoria GPU con Python
python -c "
import torch
import gc
import os

# Pulizia cache PyTorch
if torch.cuda.is_available():
    # Reset completo di tutte le GPU
    for i in range(torch.cuda.device_count()):
        torch.cuda.set_device(i)
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        torch.cuda.reset_peak_memory_stats(i)

    # Garbage collection Python
    gc.collect()

    print(f'✅ Cache GPU pulita. GPU disponibili: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        torch.cuda.set_device(i)
        total_mem = torch.cuda.get_device_properties(i).total_memory / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        free = total_mem - reserved
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
        print(f'  Memoria totale: {total_mem:.1f} GB')
        print(f'  Memoria libera: {free:.1f} GB')
        print(f'  Memoria allocata: {allocated:.1f} GB')
        print(f'  Memoria riservata: {reserved:.1f} GB')
"

# Attesa per stabilizzazione
echo "⏳ Attesa 15 secondi per stabilizzazione memoria..."
sleep 15

# VERIFICA CHECKPOINT ESISTENTE
echo "📊 Verifica checkpoint esistente..."
if [ -d "experiments/xml_direct_input/outputs/llama_t6_24h/checkpoint-12500" ]; then
    echo "✅ Checkpoint 12500 trovato - RESUME ABILITATO"
    echo "📈 Progresso precedente: Step 12500/50000 (25%), Loss: 1.1348 → 0.6882 (-39%)"
else
    echo "❌ ERRORE: Checkpoint 12500 non trovato!"
    exit 1
fi

# VERIFICA DATASET
echo "📊 Verifica dataset..."
python -c "
import json
train_file = 'data/processed/xml_format_optimized/train_set_100k_final_90000.json'
with open(train_file, 'r') as f:
    data = json.load(f)
print(f'✅ Dataset caricato: {len(data)} esempi')
print(f'✅ Primo esempio ID: {data[0].get(\"id\", \"N/A\")}')
"

# TRAINING 2 GPU PARALLELO - 24 ORE - RESUME DA CHECKPOINT 12500
echo "🚀 Avvio training Llama T6 - 2 GPU - RESUME DA CHECKPOINT 12500..."
echo "🎯 Target: 50000 step totali (37500 step rimanenti)"
echo "📊 Loss attuale: 0.6882 (riduzione 39% da 1.1348)"

torchrun --nproc_per_node=2 --master_port=29507 scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t6_24h.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t6_24h \
    --wandb_project svg_captioning_t6 \
    --wandb_entity 337543-unimore \
    --wandb_run_name llama_t6_24h_resume \

echo "✅ Training Llama T6 completato!"
echo "Data fine: $(date)"

echo "--- SCRIPT SLURM COMPLETATO ---"
