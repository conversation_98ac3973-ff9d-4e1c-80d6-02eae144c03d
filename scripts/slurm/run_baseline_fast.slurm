#!/bin/bash
#SBATCH --job-name=baseline_fast
#SBATCH --output=logs/baseline_fast_%j.out
#SBATCH --error=logs/baseline_fast_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=03:00:00

echo "🚀 BASELINE EVALUATION VELOCE - DATASET RIDOTTO 🚀"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $(hostname)"

# Crea directory
mkdir -p logs
mkdir -p evaluation_results/baseline_fast

echo ""
echo "🔧 ATTIVAZIONE AMBIENTE:"
cd /work/tesi_ediluzio
source svg_env/bin/activate

echo ""
echo "🔍 VERIFICA AMBIENTE:"
echo "Python: $(which python)"
echo "PyTorch: $(python -c 'import torch; print(torch.__version__)')"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

# Verifica memoria GPU
python -c "
import torch
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        print(f'GPU {i}: {props.name} - {props.total_memory/1024**3:.1f} GB')
"

echo ""
echo "📊 CREAZIONE DATASET RIDOTTO:"
TEST_FILE_ORIG="data/processed/xml_format_optimized/baseline_eval_subset_400.json"
TEST_FILE="data/processed/xml_format_optimized/baseline_eval_subset_50.json"
OUTPUT_DIR="evaluation_results/baseline_fast"

# Crea dataset ridotto (primi 50 esempi)
python -c "
import json
with open('$TEST_FILE_ORIG', 'r') as f:
    data = json.load(f)
# Prendi primi 50 esempi
small_data = data[:50]
with open('$TEST_FILE', 'w') as f:
    json.dump(small_data, f, indent=2)
print(f'✅ Dataset ridotto creato: {len(small_data)} esempi')
"

if [ ! -f "$TEST_FILE" ]; then
    echo "❌ ERRORE: File di test non creato: $TEST_FILE"
    exit 1
fi

echo "✅ Dataset ridotto: $TEST_FILE"

echo ""
echo "🎯 MODELLI DA VALUTARE (50 esempi, batch_size=8):"
echo "1. 🖼️ ViT-GPT2 (nlpconnect/vit-gpt2-image-captioning)"

echo ""
echo "🚀 EVALUATION BASELINE VELOCE"
echo "============================="

# Test solo ViT-GPT2 con dataset ridotto
echo "🖼️ Valutazione ViT-GPT2 (50 esempi)..."
python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models vit-gpt2 \
    --batch_size 8 \
    --wandb_project svg_captioning_baseline_fast

if [ $? -eq 0 ]; then
    echo "✅ ViT-GPT2 completato con successo!"
    
    # Se funziona, prova GIT-base
    echo "🔧 Valutazione GIT-base (50 esempi)..."
    python scripts/evaluation/evaluate_real_baseline_models.py \
        --test_file "$TEST_FILE" \
        --output_dir "$OUTPUT_DIR" \
        --models git-base \
        --batch_size 8 \
        --wandb_project svg_captioning_baseline_fast
    
    if [ $? -eq 0 ]; then
        echo "✅ GIT-base completato con successo!"
        
        # Se funziona, prova BLIP-base
        echo "👁️ Valutazione BLIP-base (50 esempi)..."
        python scripts/evaluation/evaluate_real_baseline_models.py \
            --test_file "$TEST_FILE" \
            --output_dir "$OUTPUT_DIR" \
            --models blip-base \
            --batch_size 8 \
            --wandb_project svg_captioning_baseline_fast
    fi
else
    echo "❌ ViT-GPT2 fallito!"
fi

echo ""
echo "📊 RIEPILOGO RISULTATI:"
for model in vit-gpt2 git-base blip-base; do
    echo ""
    echo "🔹 $model:"
    
    METRICS_FILE="$OUTPUT_DIR/baseline_$model/metrics.json"
    
    if [ -f "$METRICS_FILE" ]; then
        echo "  ✅ Completato - Metriche:"
        python -c "
import json
try:
    with open('$METRICS_FILE', 'r') as f:
        metrics = json.load(f)
    print(f'    BLEU: {metrics.get(\"bleu\", 0):.4f}')
    print(f'    METEOR: {metrics.get(\"meteor\", 0):.4f}')
    print(f'    ROUGE-L: {metrics.get(\"rouge-l\", 0):.4f}')
except Exception as e:
    print(f'    ❌ Errore lettura metriche: {e}')
"
    else
        echo "  ❌ Non completato"
    fi
done

echo ""
echo "📁 FILE GENERATI:"
echo "  📊 Metriche: $OUTPUT_DIR/baseline_*/metrics.json"
echo "  📈 Predictions: $OUTPUT_DIR/baseline_*/predictions.jsonl"

echo ""
echo "🎉 EVALUATION BASELINE VELOCE COMPLETATA!"
echo "Timestamp fine: $(date)"
echo ""
echo "🔗 Wandb project: https://wandb.ai/337543-unimore/svg_captioning_baseline_fast"
