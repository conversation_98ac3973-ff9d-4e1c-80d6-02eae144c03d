#!/bin/bash
#SBATCH --job-name=clip_cpu_test
#SBATCH --output=logs/clip_cpu_test_%j.log
#SBATCH --error=logs/clip_cpu_test_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --cpus-per-task=8
#SBATCH --mem=16G
#SBATCH --time=2:00:00

echo "🔍 CLIP CPU TEST CON DATASET DIRETTO 🔍"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Crea directory logs se non esiste
mkdir -p logs

# ATTIVA AMBIENTE
echo "📦 Attivazione ambiente..."
source /work/tesi_ediluzio/test_5_env/bin/activate

# VERIFICA AMBIENTE
echo "🔍 Verifica ambiente..."
python -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')

try:
    from transformers import CLIPProcessor, CLIPModel
    print('CLIP Transformers OK')
except ImportError as e:
    print(f'CLIP Transformers Error: {e}')

import cairosvg
print('CairoSVG OK')
"

# LOGIN HUGGING FACE
echo "🔑 Login Hugging Face..."
export HUGGING_FACE_HUB_TOKEN="*************************************"
huggingface-cli login --token *************************************

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

echo "🎯 CLIP CPU con dataset test diretto..."

# CLIP CPU CON DATASET DIRETTO
python scripts/evaluation/calculate_clip_score_cpu.py \
    --test_file data/processed/xml_format_optimized/test_set_test5_3k.json \
    --output_dir experiments/clip_cpu_test \
    --batch_size 4 \
    --use_wandb \
    --wandb_project svg_captioning_clip_test \
    --wandb_entity 337543-unimore \
    --wandb_run_name clip_cpu_test_direct

echo "✅ CLIP CPU test completato!"
echo "Timestamp fine: $(date)"
