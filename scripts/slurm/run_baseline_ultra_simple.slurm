#!/bin/bash
#SBATCH --job-name=baseline_ultra
#SBATCH --output=logs/baseline_ultra_%j.out
#SBATCH --error=logs/baseline_ultra_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=01:00:00

echo "🚀 BASELINE EVALUATION ULTRA SEMPLICE 🚀"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $(hostname)"

# Crea directory
mkdir -p logs
mkdir -p evaluation_results/baseline_ultra

echo ""
echo "🔧 ATTIVAZIONE AMBIENTE:"
cd /work/tesi_ediluzio
source svg_env/bin/activate

echo ""
echo "🔍 VERIFICA AMBIENTE:"
echo "Python: $(which python)"
echo "PyTorch: $(python -c 'import torch; print(torch.__version__)')"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

# Verifica memoria GPU
python -c "
import torch
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        print(f'GPU {i}: {props.name} - {props.total_memory/1024**3:.1f} GB')
"

echo ""
echo "📊 DATASET DI TEST:"
TEST_FILE="data/processed/xml_format_optimized/baseline_eval_subset_400.json"
OUTPUT_DIR="evaluation_results/baseline_ultra"

if [ ! -f "$TEST_FILE" ]; then
    echo "❌ ERRORE: File di test non trovato: $TEST_FILE"
    exit 1
fi

echo "✅ Dataset trovato: $TEST_FILE"

echo ""
echo "🎯 MODELLI DA VALUTARE (ultra compatibili):"
echo "1. 👁️ BLIP-base (Salesforce/blip-image-captioning-base)"
echo "2. 🔧 GIT-base (microsoft/git-base)"  
echo "3. 🖼️ ViT-GPT2 (nlpconnect/vit-gpt2-image-captioning)"

echo ""
echo "🚀 EVALUATION BASELINE MODELS"
echo "=============================="

# Test solo ViT-GPT2 prima (più semplice)
echo "🖼️ Test ViT-GPT2..."
python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models vit-gpt2 \
    --batch_size 4 \
    --wandb_project svg_captioning_baseline_ultra

if [ $? -eq 0 ]; then
    echo "✅ ViT-GPT2 completato con successo!"
    
    # Se ViT-GPT2 funziona, prova gli altri
    echo "🔧 Test GIT-base..."
    python scripts/evaluation/evaluate_real_baseline_models.py \
        --test_file "$TEST_FILE" \
        --output_dir "$OUTPUT_DIR" \
        --models git-base \
        --batch_size 4 \
        --wandb_project svg_captioning_baseline_ultra
    
    echo "👁️ Test BLIP-base..."
    python scripts/evaluation/evaluate_real_baseline_models.py \
        --test_file "$TEST_FILE" \
        --output_dir "$OUTPUT_DIR" \
        --models blip-base \
        --batch_size 4 \
        --wandb_project svg_captioning_baseline_ultra
else
    echo "❌ ViT-GPT2 fallito!"
fi

echo ""
echo "📊 RIEPILOGO RISULTATI:"
for model in vit-gpt2 git-base blip-base; do
    echo ""
    echo "🔹 $model:"
    
    METRICS_FILE="$OUTPUT_DIR/baseline_$model/metrics.json"
    
    if [ -f "$METRICS_FILE" ]; then
        echo "  ✅ Completato - Metriche:"
        python -c "
import json
try:
    with open('$METRICS_FILE', 'r') as f:
        metrics = json.load(f)
    print(f'    BLEU: {metrics.get(\"bleu\", 0):.4f}')
    print(f'    METEOR: {metrics.get(\"meteor\", 0):.4f}')
    print(f'    ROUGE-L: {metrics.get(\"rouge-l\", 0):.4f}')
except:
    print('    ❌ Errore lettura metriche')
"
    else
        echo "  ❌ Non completato"
    fi
done

echo ""
echo "🎉 EVALUATION BASELINE ULTRA COMPLETATA!"
echo "Timestamp fine: $(date)"
