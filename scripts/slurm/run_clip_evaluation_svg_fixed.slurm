#!/bin/bash
#SBATCH --job-name=clip_svg_fixed
#SBATCH --output=logs/clip_svg_fixed_%j.log
#SBATCH --error=logs/clip_svg_fixed_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --mem=16G
#SBATCH --cpus-per-task=4
#SBATCH --time=2:00:00

echo "🔍 CLIP EVALUATION CON SVG RIPARATI 🔍"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Crea directory logs se non esiste
mkdir -p logs

# ATTIVA AMBIENTE AGGIORNATO
echo "📦 Attivazione ambiente aggiornato..."
source /work/tesi_ediluzio/svg_env/bin/activate

# CLIP è già installato e funzionante
echo "📦 CLIP già installato e riparato..."

# VERIFICA AMBIENTE
echo "🔍 Verifica ambiente..."
python -c "
import torch
import clip
import transformers
import cairosvg
print(f'✅ PyTorch: {torch.__version__}')
print(f'✅ CUDA: {torch.cuda.is_available()}')
print(f'✅ Transformers: {transformers.__version__}')
print(f'✅ CairoSVG: {cairosvg.__version__}')
print('✅ CLIP: OK')
"

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

echo "🔍 Verifica file inference..."

# PERCORSI FILE INFERENCE
LLAMA_FILE="experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/inference_results.jsonl"
GEMMA_FILE="experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/inference_results.jsonl"
REFERENCE_FILE="data/processed/xml_format/test_set_final_xml_reduced_rgb.json"
OUTPUT_FILE="experiments/clip_evaluation_svg_fixed/clip_scores_$(date +%Y%m%d_%H%M%S).json"

# Crea directory output
mkdir -p experiments/clip_evaluation_svg_fixed

# Verifica esistenza file
LLAMA_EXISTS=false
GEMMA_EXISTS=false

if [ -f "$LLAMA_FILE" ]; then
    echo "✅ File Llama trovato: $LLAMA_FILE"
    LLAMA_EXISTS=true
    ls -la "$LLAMA_FILE"
else
    echo "❌ File Llama NON trovato: $LLAMA_FILE"
fi

if [ -f "$GEMMA_FILE" ]; then
    echo "✅ File Gemma trovato: $GEMMA_FILE"
    GEMMA_EXISTS=true
    ls -la "$GEMMA_FILE"
else
    echo "❌ File Gemma NON trovato: $GEMMA_FILE"
fi

if [ "$LLAMA_EXISTS" = false ] && [ "$GEMMA_EXISTS" = false ]; then
    echo "❌ ERRORE: Nessun file inference trovato!"
    echo "I training devono completare prima della valutazione CLIP"
    exit 1
fi

echo "🎯 Avvio CLIP evaluation con SVG riparati..."

# COSTRUISCI COMANDO
CMD="python scripts/evaluation/evaluate_clip_svg_fixed.py"
CMD="$CMD --reference_file $REFERENCE_FILE"
CMD="$CMD --output_file $OUTPUT_FILE"
CMD="$CMD --device auto"

if [ "$LLAMA_EXISTS" = true ]; then
    CMD="$CMD --llama_results $LLAMA_FILE"
fi

if [ "$GEMMA_EXISTS" = true ]; then
    CMD="$CMD --gemma_results $GEMMA_FILE"
fi

echo "Comando: $CMD"

# ESECUZIONE
$CMD

echo "✅ CLIP evaluation completata!"
echo "Output salvato in: $OUTPUT_FILE"
echo "Timestamp fine: $(date)"
