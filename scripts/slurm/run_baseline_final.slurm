#!/bin/bash
#SBATCH --job-name=baseline_final
#SBATCH --output=logs/baseline_final_%j.log
#SBATCH --error=logs/baseline_final_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=2:00:00

echo "🎉 BASELINE FINALE CON TRANSFORMERS 4.42.4 🎉"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Crea directory logs se non esiste
mkdir -p logs

# ATTIVA AMBIENTE AGGIORNATO
echo "📦 Attivazione ambiente aggiornato..."
source /work/tesi_ediluzio/svg_env/bin/activate

# VERIFICA VERSIONI
echo "🔍 Verifica versioni aggiornate:"
python -c "
import transformers, torch, accelerate, numpy, PIL
print(f'✅ Transformers: {transformers.__version__}')
print(f'✅ PyTorch: {torch.__version__}')
print(f'✅ Accelerate: {accelerate.__version__}')
print(f'✅ NumPy: {numpy.__version__}')
print(f'✅ Pillow: {PIL.__version__}')
"

# CONFIGURAZIONE CPU OTTIMIZZATA
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
export TOKENIZERS_PARALLELISM=false

# LOGIN HUGGING FACE
echo "🔑 Login Hugging Face..."
export HUGGING_FACE_HUB_TOKEN="*************************************"
huggingface-cli login --token *************************************

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

echo "🎯 BASELINE FINALE: Ide Fix 3, Flores 2, BLIP 2.7B..."

# BASELINE CON TRANSFORMERS 4.42.4
python scripts/evaluation/evaluate_baseline_fixed.py \
    --test_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/baseline_transformers_442 \
    --models idefix flores2 blip2

echo "✅ Baseline finale completato!"
echo "Timestamp fine: $(date)"
