#!/bin/bash
#SBATCH --job-name=baseline_svg_fixed
#SBATCH --output=logs/baseline_svg_fixed_%j.log
#SBATCH --error=logs/baseline_svg_fixed_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=3:00:00

echo "🔧 BASELINE CON SVG RIPARATI (FUNZIONANTE!) 🔧"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Crea directory logs se non esiste
mkdir -p logs

# ATTIVA AMBIENTE AGGIORNATO
echo "📦 Attivazione ambiente aggiornato..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# VERIFICA VERSIONI
echo "🔍 Verifica versioni:"
python -c "
import transformers, torch, cairosvg
print(f'✅ Transformers: {transformers.__version__}')
print(f'✅ PyTorch: {torch.__version__}')
print(f'✅ CairoSVG: {cairosvg.__version__}')
"

# CONFIGURAZIONE CPU OTTIMIZZATA
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
export TOKENIZERS_PARALLELISM=false

# LOGIN HUGGING FACE
echo "🔑 Login Hugging Face..."
export HUGGING_FACE_HUB_TOKEN="*************************************"
huggingface-cli login --token *************************************

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

# VERIFICA DATASET
echo "📊 Verifica dataset..."
if [ ! -f "data/processed/xml_format/test_set_final_xml_reduced_rgb.json" ]; then
    echo "❌ Dataset non trovato!"
    exit 1
fi

python -c "
import json
with open('data/processed/xml_format/test_set_final_xml_reduced_rgb.json', 'r') as f:
    data = json.load(f)
print(f'✅ Dataset caricato: {len(data)} esempi')
print(f'✅ Primo esempio ID: {data[0].get(\"id\", \"N/A\")}')
"

# TEST PRELIMINARE CON 1 ESEMPIO
echo "🧪 Test preliminare con 1 esempio..."
python scripts/evaluation/evaluate_baseline_svg_fixed.py \
    --test_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/baseline_svg_fixed_test_single \
    --models idefix \
    --max_examples 1

if [ $? -ne 0 ]; then
    echo "❌ Test preliminare fallito! Uscita."
    exit 1
fi

echo "✅ Test preliminare riuscito! Procedo con baseline completo..."
echo "🎯 BASELINE SVG-FIXED: Ide Fix 3, Flores 2, BLIP 2.7B..."

# BASELINE CON SVG RIPARATI
python scripts/evaluation/evaluate_baseline_svg_fixed.py \
    --test_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/baseline_svg_fixed_final \
    --models idefix flores2 blip2

echo "✅ Baseline SVG-FIXED completato!"
echo "Timestamp fine: $(date)"
