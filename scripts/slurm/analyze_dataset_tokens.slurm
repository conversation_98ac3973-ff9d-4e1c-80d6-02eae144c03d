#!/bin/bash
#SBATCH --job-name=analyze_tokens
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=01:00:00
#SBATCH --output=logs/analyze_tokens_%j.out
#SBATCH --error=logs/analyze_tokens_%j.err

echo "🔍 ANALISI DISTRIBUZIONE TOKEN DATASET"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Attiva environment (best practice conda)
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Analisi con Gemma tokenizer (256K vocab)
echo "🔧 Analisi con Gemma tokenizer..."
python scripts/data_processing/analyze_token_distribution.py \
    --data_file data/processed/xml_format_optimized/train_set_test5_32k.json \
    --tokenizer google/gemma-2-9b-it \
    --max_length 1000 \
    --output_dir data/processed/xml_format_optimized

# Analisi con Llama tokenizer (32K vocab)
echo "🔧 Analisi con Llama tokenizer..."
python scripts/data_processing/analyze_token_distribution.py \
    --data_file data/processed/xml_format_optimized/train_set_test5_32k.json \
    --tokenizer meta-llama/Llama-3.1-8B-Instruct \
    --max_length 1000 \
    --output_dir data/processed/xml_format_optimized

echo "✅ Analisi completata!"
