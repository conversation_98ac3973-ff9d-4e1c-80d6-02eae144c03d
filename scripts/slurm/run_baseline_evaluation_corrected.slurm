#!/bin/bash
#SBATCH --job-name=baseline_eval_cpu
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/baseline_eval_cpu_%j.log
#SBATCH --error=logs/baseline_eval_cpu_%j.err
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=48G
#SBATCH --time=08:00:00

# Vai alla directory del progetto
cd /work/tesi_ediluzio

# Attiva l'ambiente virtuale
source svg_env/bin/activate

echo "🚀 Inizio valutazione baseline corretta 🚀"

# Esegui lo script Python
python3 scripts/evaluation/evaluate_real_baseline_models.py \
    --models idefics3 florence2 blip2 \
    --test_file data/processed/xml_format/test_set_final_xml_reduced.json \
    --output_dir results/baseline_evaluation_corrected \
    --batch_size 4 \
    --wandb_project svg_captioning_baselines_corrected

echo "✅ Valutazione completata con successo!" 