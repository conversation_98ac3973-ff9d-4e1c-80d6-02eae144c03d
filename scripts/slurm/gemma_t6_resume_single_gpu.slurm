#!/bin/bash
#SBATCH --job-name=gemma_t6_single
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=48G
#SBATCH --time=02:00:00
#SBATCH --output=logs/gemma_t6_single_%j.out
#SBATCH --error=logs/gemma_t6_single_%j.err

echo "🚀 GEMMA T6 - RESUME SINGLE GPU - DEBUG"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"
echo "Data inizio: $(date)"

# ATTIVA ENVIRONMENT
echo "🔧 Setup environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# VARIABILI AMBIENTE - 1 GPU
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:64
export TOKENIZERS_PARALLELISM=false
export OMP_NUM_THREADS=1

# PULIZIA MEMORIA GPU
echo "🧹 Pulizia memoria GPU..."
python -c "
import torch
import gc
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    gc.collect()
    print(f'✅ GPU disponibili: {torch.cuda.device_count()}')
"

# VERIFICA CHECKPOINT
echo "📊 Verifica checkpoint..."
if [ -d "experiments/xml_direct_input/outputs/gemma_t6_24h/checkpoint-4000" ]; then
    echo "✅ Checkpoint 4000 trovato"
else
    echo "❌ Checkpoint non trovato!"
    exit 1
fi

# TRAINING SINGLE GPU - DEBUG
echo "🚀 Avvio training Gemma T6 - SINGLE GPU - DEBUG..."

python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_ultra_memory_optimized.json \
    --output_dir experiments/xml_direct_input/outputs/gemma_t6_24h \
    --wandb_project svg_captioning_t6 \
    --wandb_entity 337543-unimore \
    --wandb_run_name gemma_t6_single_debug \

echo "✅ Training completato!"
echo "Data fine: $(date)"
