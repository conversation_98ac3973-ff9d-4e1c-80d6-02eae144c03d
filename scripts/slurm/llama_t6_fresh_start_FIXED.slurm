#!/bin/bash
#SBATCH --job-name=llama_t6_fresh_FIXED
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=16
#SBATCH --gres=gpu:2
#SBATCH --mem=128G
#SBATCH --time=24:00:00
#SBATCH --output=logs/llama_t6_fresh_FIXED_%j.out
#SBATCH --error=logs/llama_t6_fresh_FIXED_%j.err

echo "=== LLAMA T6 FRESH START FIXED ==="
echo "Job ID: $SLURM_JOB_ID"
echo "Data inizio: $(date)"
echo "🔧 TRAINING SCRIPT FIXATO - FRESH START"
echo "⚠️ CHECKPOINT PRECEDENTE POTENZIALMENTE CORROTTO - RIPARTENDO DA ZERO"

# Attivazione ambiente
source svg_env/bin/activate

# Pulizia memoria GPU
echo "🧹 Pulizia memoria GPU..."
nvidia-smi --gpu-reset || true
sleep 5

# Verifica GPU
echo "🔍 Verifica GPU disponibili:"
nvidia-smi --query-gpu=index,name,memory.total,memory.free --format=csv,noheader,nounits

# Attesa per stabilizzazione
echo "⏳ Attesa 15 secondi per stabilizzazione memoria..."
sleep 15

# RIMUOVI CHECKPOINT CORROTTI
echo "🗑️ Rimozione checkpoint potenzialmente corrotti..."
if [ -d "experiments/xml_direct_input/outputs/llama_t6_24h" ]; then
    echo "💾 Backup directory esistente..."
    mv experiments/xml_direct_input/outputs/llama_t6_24h \
       experiments/xml_direct_input/outputs/llama_t6_24h_CORRUPTED_BACKUP_$(date +%Y%m%d_%H%M%S)
    echo "✅ Directory rinominata per sicurezza"
fi

# Crea directory pulita
mkdir -p experiments/xml_direct_input/outputs/llama_t6_24h
echo "✅ Directory pulita creata"

# Configurazione memoria
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=0

# TRAINING LLAMA T6 FRESH START CON SCRIPT FIXATO
echo "🚀 Avvio training Llama T6 FRESH START con script FIXATO..."
echo "🔧 NOTA: Early stopping personalizzato RIMOSSO - usando solo quello integrato"
echo "🆕 FRESH START - nessun resume, training da zero"

# Prova prima con single GPU per evitare problemi rope_scaling
echo "🔧 TENTATIVO 1: Single GPU per evitare problemi distributed + rope_scaling"

python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/llama_t6_24h.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t6_24h \
    --wandb_project svg_captioning_t6 \
    --wandb_entity 337543-unimore \
    --wandb_run_name llama_t6_24h_fresh_FIXED \
    --early_stopping \
    --patience 10 \
    --min_delta 0.001

# Se fallisce, prova con multi-GPU
if [ $? -ne 0 ]; then
    echo "❌ Single GPU fallito, provo multi-GPU..."
    
    torchrun --nproc_per_node=2 --master_port=29508 scripts/training/train_lora_multi_gpu_simple.py \
        --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
        --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
        --config_path experiments/xml_direct_input/configs/llama_t6_24h.json \
        --output_dir experiments/xml_direct_input/outputs/llama_t6_24h \
        --wandb_project svg_captioning_t6 \
        --wandb_entity 337543-unimore \
        --wandb_run_name llama_t6_24h_fresh_FIXED_multi \
        --early_stopping \
        --patience 10 \
        --min_delta 0.001
fi

echo "✅ Training Llama T6 FRESH START completato!"
echo "Data fine: $(date)"

echo "--- SCRIPT SLURM COMPLETATO ---"
