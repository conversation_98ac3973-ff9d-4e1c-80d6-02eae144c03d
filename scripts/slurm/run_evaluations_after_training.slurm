#!/bin/bash
#SBATCH --job-name=eval_post_train
#SBATCH --output=logs/eval_post_train_%j.log
#SBATCH --error=logs/eval_post_train_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=6:00:00

echo "📊 VALUTAZIONI POST-TRAINING 📊"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Crea directory logs se non esiste
mkdir -p logs

# ATTIVA AMBIENTE RIPARATO
echo "📦 Attivazione ambiente riparato..."
source /work/tesi_ediluzio/svg_env/bin/activate

# VERIFICA AMBIENTE
echo "🔍 Verifica ambiente..."
python -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')

import rouge
print('Rouge installato correttamente')

import clip
print('CLIP installato correttamente')
"

# LOGIN HUGGING FACE
echo "🔑 Login Hugging Face..."
export HUGGING_FACE_HUB_TOKEN="*************************************"
huggingface-cli login --token *************************************

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

# VERIFICA CHE I FILE DI RISULTATI ESISTANO
echo "🔍 Verifica file di risultati training..."
LLAMA_RESULTS="experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/inference_results.jsonl"
GEMMA_RESULTS="experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/inference_results.jsonl"

if [ ! -f "$LLAMA_RESULTS" ]; then
    echo "❌ File risultati Llama non trovato: $LLAMA_RESULTS"
    echo "⏳ Training Llama probabilmente non completato"
fi

if [ ! -f "$GEMMA_RESULTS" ]; then
    echo "❌ File risultati Gemma non trovato: $GEMMA_RESULTS"
    echo "⏳ Training Gemma probabilmente non completato"
fi

# BASELINE EVALUATION (sempre possibile)
echo "📊 Avvio valutazione baseline models..."
python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/baseline_evaluation_post_training \
    --models blip vit-gpt2 blip2 git-base \
    --batch_size 16 \
    --wandb_project svg_captioning_baselines_post_training

echo "✅ Baseline evaluation completata!"

# CLIP EVALUATION (solo se esistono i risultati)
if [ -f "$LLAMA_RESULTS" ] && [ -f "$GEMMA_RESULTS" ]; then
    echo "📊 Avvio valutazione CLIP..."
    python experiments/xml_direct_input/evaluate_clip_score.py \
        --llama_results "$LLAMA_RESULTS" \
        --gemma_results "$GEMMA_RESULTS" \
        --reference_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
        --output_file experiments/xml_direct_input/outputs/clip_scores_post_training.json
    echo "✅ CLIP evaluation completata!"
else
    echo "⚠️  CLIP evaluation saltata: file di risultati training mancanti"
    echo "   Rilanciare questo job dopo il completamento dei training"
fi

echo "✅ Valutazioni post-training completate!"
echo "Timestamp fine: $(date)"
