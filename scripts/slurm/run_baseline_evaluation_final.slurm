#!/bin/bash
#SBATCH --job-name=baseline_eval_final
#SBATCH --output=logs/baseline_eval_final_%j.out
#SBATCH --error=logs/baseline_eval_final_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=02:00:00
#SBATCH --priority=HIGH

echo "🚀 BASELINE EVALUATION FINALE - 3 MODELLI RICHIESTI 🚀"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $(hostname)"

# Crea directory logs e output
mkdir -p logs
mkdir -p evaluation_results/baseline_final

echo ""
echo "🔧 ATTIVAZIONE AMBIENTE:"
cd /work/tesi_ediluzio
source svg_env/bin/activate

echo ""
echo "🔍 VERIFICA AMBIENTE:"
echo "Python: $(which python)"
echo "PyTorch: $(python -c 'import torch; print(torch.__version__)')"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

echo ""
echo "📊 DATASET DI TEST:"
TEST_FILE="data/processed/xml_format_optimized/baseline_eval_subset_400.json"
OUTPUT_DIR="evaluation_results/baseline_final"

if [ ! -f "$TEST_FILE" ]; then
    echo "❌ ERRORE: File di test non trovato: $TEST_FILE"
    exit 1
fi

echo "✅ Dataset trovato: $TEST_FILE"
echo "📈 Esempi nel dataset: $(python -c "import json; print(len(json.load(open('$TEST_FILE'))))")"

echo ""
echo "🎯 MODELLI DA VALUTARE:"
echo "1. 🔧 Ide Fix 3 (microsoft/git-large come proxy)"
echo "2. 🌍 Flores 2 base (microsoft/git-base come proxy)"  
echo "3. 👁️ BLIP 2.7B (Salesforce/blip2-opt-2.7b)"

echo ""
echo "🚀 FASE 1: EVALUATION BASELINE MODELS"
echo "========================================"

# Esegui evaluation dei 3 modelli richiesti
python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models idefics3 florence2 blip2 \
    --batch_size 4 \
    --wandb_project svg_captioning_baseline_final

echo ""
echo "✅ FASE 1 COMPLETATA - Baseline models evaluation"

echo ""
echo "🚀 FASE 2: CALCOLO CLIP SCORE (CPU)"
echo "===================================="

# Calcola CLIPScore per ogni modello
for model in idefics3 florence2 blip2; do
    echo "🔄 Calcolo CLIPScore per $model..."
    
    # Crea file predictions per CLIPScore
    PRED_FILE="$OUTPUT_DIR/baseline_$model/predictions.jsonl"
    
    if [ -f "$PRED_FILE" ]; then
        # Converte predictions in formato per CLIPScore
        python -c "
import json
import sys

# Leggi predictions
predictions = []
with open('$PRED_FILE', 'r') as f:
    for line in f:
        predictions.append(json.loads(line))

# Leggi test data originale per SVG
with open('$TEST_FILE', 'r') as f:
    test_data = json.load(f)

# Crea mapping per ID
test_map = {item.get('id', item.get('example_id', f'example_{i}')): item for i, item in enumerate(test_data)}

# Crea formato per CLIPScore
clip_data = []
for pred in predictions:
    test_item = test_map.get(pred['id'])
    if test_item:
        clip_data.append({
            'example_id': pred['id'],
            'svg_xml': test_item.get('xml', test_item.get('svg_xml', '')),
            'reference_caption': pred['reference'],
            'generated_caption': pred['generated']
        })

# Salva per CLIPScore
with open('$OUTPUT_DIR/baseline_$model/clip_input.json', 'w') as f:
    json.dump(clip_data, f, indent=2)
"
        
        # Calcola CLIPScore
        python scripts/evaluation/calculate_clip_score_cpu.py \
            --test_file "$OUTPUT_DIR/baseline_$model/clip_input.json" \
            --output_dir "$OUTPUT_DIR/baseline_$model" \
            --batch_size 8 \
            --use_wandb \
            --wandb_project svg_captioning_baseline_final \
            --wandb_run_name "clip_score_$model"
        
        echo "✅ CLIPScore completato per $model"
    else
        echo "❌ File predictions non trovato per $model: $PRED_FILE"
    fi
done

echo ""
echo "✅ FASE 2 COMPLETATA - CLIPScore calculation"

echo ""
echo "🚀 FASE 3: GENERAZIONE GRAFICO RADAR"
echo "====================================="

# Genera grafico radar comparativo
python scripts/visualization/create_comparison_radar.py \
    --baseline_dir "$OUTPUT_DIR" \
    --output_dir "$OUTPUT_DIR/visualizations" \
    --models idefics3 florence2 blip2 \
    --title "Baseline Models Comparison - Final Evaluation"

echo ""
echo "✅ FASE 3 COMPLETATA - Radar chart generation"

echo ""
echo "🚀 FASE 4: RIEPILOGO RISULTATI"
echo "==============================="

echo "📊 RISULTATI FINALI:"
for model in idefics3 florence2 blip2; do
    echo ""
    echo "🔹 $model:"
    
    METRICS_FILE="$OUTPUT_DIR/baseline_$model/metrics.json"
    CLIP_FILE="$OUTPUT_DIR/baseline_$model/clip_scores.json"
    
    if [ -f "$METRICS_FILE" ]; then
        echo "  📈 Metriche base:"
        python -c "
import json
with open('$METRICS_FILE', 'r') as f:
    metrics = json.load(f)
print(f'    BLEU: {metrics.get(\"bleu\", 0):.4f}')
print(f'    METEOR: {metrics.get(\"meteor\", 0):.4f}')
print(f'    ROUGE-L: {metrics.get(\"rouge-l\", 0):.4f}')
"
    fi
    
    if [ -f "$CLIP_FILE" ]; then
        echo "  🎯 CLIPScore:"
        python -c "
import json
with open('$CLIP_FILE', 'r') as f:
    clip_data = json.load(f)
stats = clip_data.get('statistics', {})
print(f'    Media: {stats.get(\"mean_score\", 0):.2f}')
print(f'    Max: {stats.get(\"max_score\", 0):.2f}')
print(f'    Min: {stats.get(\"min_score\", 0):.2f}')
"
    fi
done

echo ""
echo "📁 FILE GENERATI:"
echo "  📊 Metriche: $OUTPUT_DIR/baseline_*/metrics.json"
echo "  🎯 CLIPScore: $OUTPUT_DIR/baseline_*/clip_scores.json"
echo "  📈 Predictions: $OUTPUT_DIR/baseline_*/predictions.jsonl"
echo "  📊 Grafico radar: $OUTPUT_DIR/visualizations/"

echo ""
echo "🎉 EVALUATION BASELINE COMPLETATA CON SUCCESSO!"
echo "Timestamp fine: $(date)"
echo ""
echo "🔗 Wandb project: https://wandb.ai/337543-unimore/svg_captioning_baseline_final"
