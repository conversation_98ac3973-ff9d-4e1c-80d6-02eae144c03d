#!/bin/bash
#SBATCH --job-name=gen_report
#SBATCH --output=/work/tesi_ediluzio/logs/gen_report_%j.out
#SBATCH --error=/work/tesi_ediluzio/logs/gen_report_%j.err
#SBATCH --partition=cpu
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=1:00:00

# Attiva l'ambiente conda
source /work/tesi_ediluzio/miniconda3/bin/activate
conda activate svg_captioning

# Imposta le variabili di ambiente
export PYTHONPATH=/work/tesi_ediluzio:$PYTHONPATH

# Definisci le directory dei modelli
LLAMA_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test2_multi_gpu"
GEMMA_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test2_multi_gpu"
BLIP_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/external_captioners_prerendered/blip"
OUTPUT_DIR="/work/tesi_ediluzio/reports/html"

# Crea la directory di output se non esiste
mkdir -p $OUTPUT_DIR

# Esegui lo script per generare il report
echo "Generazione del report di training..."
python /work/tesi_ediluzio/scripts/visualization/generate_training_report.py \
    --model_dirs $LLAMA_DIR $GEMMA_DIR $BLIP_DIR \
    --output_dir $OUTPUT_DIR \
    --title "Valutazione dei Modelli di Generazione Didascalie SVG - Training Report" \
    --num_examples 5

# Aggiorna il file di log delle run di Weights & Biands
echo "Aggiornamento del file di log delle run di Weights & Biands..."
python -c "
import os
import datetime

# Percorso del file di log
log_file = '/work/tesi_ediluzio/wandb_runs_log.md'

# Leggi il contenuto attuale
with open(log_file, 'r') as f:
    content = f.read()

# Aggiungi una nota sul report generato
now = datetime.datetime.now().strftime('%d/%m/%Y %H:%M:%S')
note = f'\n\n## Report Generato\n\nUn report HTML con i risultati della valutazione è stato generato il {now}.\n'
note += f'Il report è disponibile in: {os.path.abspath(\"$OUTPUT_DIR/training_evaluation_report.html\")}\n'
note += f'Il grafico radar è disponibile in: {os.path.abspath(\"$OUTPUT_DIR/radar_chart.png\")}\n'

# Aggiungi la nota al contenuto
content += note

# Scrivi il contenuto aggiornato
with open(log_file, 'w') as f:
    f.write(content)

print('File di log aggiornato con successo.')
"

echo "Generazione del report completata."
