#!/bin/bash
#SBATCH --job-name=test_1gpu_accum
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=48G
#SBATCH --time=00:30:00
#SBATCH --output=logs/test_1gpu_accum_%j.out
#SBATCH --error=logs/test_1gpu_accum_%j.err

echo "🧪 TEST SINGLE GPU + GRADIENT ACCUMULATION"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# Attiva environment (best practice conda)
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Test con batch size alto e gradient accumulation
echo "🔧 Test Gemma single GPU con grad accumulation..."

# Crea config temporanea per test
cat > /tmp/test_1gpu_config.json << EOF
{
    "model_name_or_path": "google/gemma-2-9b-it",
    "output_dir": "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/test_1gpu_accum",
    "data_file": "/work/tesi_ediluzio/data/processed/xml_format_optimized/train_set_test5_32k.json",
    "per_device_train_batch_size": 8,
    "gradient_accumulation_steps": 2,
    "learning_rate": 1e-5,
    "weight_decay": 0.01,
    "max_steps": 100,
    "lr_scheduler_type": "cosine",
    "warmup_ratio": 0.05,
    "logging_steps": 5,
    "lora_r": 16,
    "lora_alpha": 32,
    "lora_dropout": 0.05,
    "lora_target_modules": ["q_proj", "v_proj", "k_proj", "o_proj"],
    "max_length": 2048,
    "bf16": true,
    "fp16": false,
    "tf32": true,
    "load_in_8bit": false,
    "load_in_4bit": false,
    "seed": 42,
    "gradient_checkpointing": true,
    "report_to": "wandb",
    "dataloader_num_workers": 2,
    "optim": "adamw_torch",
    "max_grad_norm": 1.0,
    "group_by_length": false,
    "save_strategy": "steps",
    "save_steps": 50,
    "save_total_limit": 2,
    "run_name": "test_1gpu_accum",
    "project_name": "svg_captioning_test"
}
EOF

# Training single GPU (senza torchrun)
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_test5_32k.json \
    --config_path /tmp/test_1gpu_config.json \
    --output_dir experiments/xml_direct_input/outputs/test_1gpu_accum \
    --wandb_project svg_captioning_test \
    --wandb_entity 337543-unimore \
    --wandb_run_name test_1gpu_accum \
    --early_stopping \
    --patience 5 \
    --min_delta 0.001

echo "✅ Test single GPU completato!"
