#!/bin/bash
#SBATCH --job-name=gemma_t6_resume
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:2
#SBATCH --mem=48G
#SBATCH --time=24:00:00
#SBATCH --output=logs/gemma_t6_resume_%j.out
#SBATCH --error=logs/gemma_t6_resume_%j.err

echo "🚀 GEMMA T6 - RESUME DA CHECKPOINT 4000 - 2 GPU PARALLELO"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"
echo "Data inizio: $(date)"

# ATTIVA ENVIRONMENT
echo "🔧 Setup environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# VARIABILI AMBIENTE - 2 GPU PARALLELO - MEMORIA AGGRESSIVA
export CUDA_VISIBLE_DEVICES=0,1
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:64,garbage_collection_threshold:0.8,roundup_power2_divisions:16
export TOKENIZERS_PARALLELISM=false
export OMP_NUM_THREADS=1
export CUDA_LAUNCH_BLOCKING=0
export TORCH_CUDA_ARCH_LIST="8.0;8.6;8.9"

# PULIZIA AGGRESSIVA MEMORIA GPU E PROCESSI
echo "🧹 Pulizia aggressiva memoria GPU e processi..."

# Kill tutti i processi Python dell'utente che potrebbero occupare GPU
echo "🔪 Kill processi Python precedenti..."
pkill -f "python.*train_lora" || true
pkill -f "torchrun" || true
sleep 5

# Pulizia memoria GPU con Python
python -c "
import torch
import gc
import os

# Pulizia cache PyTorch
if torch.cuda.is_available():
    # Reset completo di tutte le GPU
    for i in range(torch.cuda.device_count()):
        torch.cuda.set_device(i)
        torch.cuda.empty_cache()
        torch.cuda.ipc_collect()
        torch.cuda.reset_peak_memory_stats(i)

    # Garbage collection Python
    gc.collect()

    print(f'✅ Cache GPU pulita. GPU disponibili: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        torch.cuda.set_device(i)
        total_mem = torch.cuda.get_device_properties(i).total_memory / 1024**3
        reserved = torch.cuda.memory_reserved(i) / 1024**3
        allocated = torch.cuda.memory_allocated(i) / 1024**3
        free = total_mem - reserved
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
        print(f'  Memoria totale: {total_mem:.1f} GB')
        print(f'  Memoria libera: {free:.1f} GB')
        print(f'  Memoria allocata: {allocated:.1f} GB')
        print(f'  Memoria riservata: {reserved:.1f} GB')
"

# Attesa per stabilizzazione
echo "⏳ Attesa 15 secondi per stabilizzazione memoria..."
sleep 15

# VERIFICA CHECKPOINT ESISTENTE
echo "📊 Verifica checkpoint esistente..."
if [ -d "experiments/xml_direct_input/outputs/gemma_t6_24h/checkpoint-6000" ]; then
    echo "✅ Checkpoint 6000 trovato - RESUME ABILITATO"
    echo "📈 Progresso precedente: Step 6000/50000 (12%), Loss finale: 0.4646"
else
    echo "❌ ERRORE: Checkpoint 6000 non trovato!"
    exit 1
fi

# VERIFICA DATASET
echo "📊 Verifica dataset..."
python -c "
import json
train_file = 'data/processed/xml_format_optimized/train_set_100k_final_90000.json'
with open(train_file, 'r') as f:
    data = json.load(f)
print(f'✅ Dataset caricato: {len(data)} esempi')
print(f'✅ Primo esempio ID: {data[0].get(\"id\", \"N/A\")}')
"

# TRAINING 2 GPU PARALLELO - 24 ORE - RESUME DA CHECKPOINT 4000
echo "🚀 Avvio training Gemma T6 - 2 GPU - RESUME DA CHECKPOINT 6000..."
echo "🎯 Target: 50000 step totali (44000 step rimanenti)"
echo "📊 Loss attuale: 0.4646 (training completato precedentemente)"

torchrun --nproc_per_node=2 --master_port=29505 scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_ultra_memory_optimized.json \
    --output_dir experiments/xml_direct_input/outputs/gemma_t6_24h \
    --wandb_project svg_captioning_t6 \
    --wandb_entity 337543-unimore \
    --wandb_run_name gemma_t6_24h_resume_6000 \
    --early_stopping \
    --patience 100 \
    --min_delta 0.001 \

echo "✅ Training Gemma T6 completato!"
echo "Data fine: $(date)"
