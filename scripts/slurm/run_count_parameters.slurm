#!/bin/bash
#SBATCH --job-name=count_params
#SBATCH --output=logs/count_params_%j.out
#SBATCH --error=logs/count_params_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --qos=all_qos_dbg
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --constraint=gpu_L40S_48G|gpu_A40_48G|gpu_RTX5000_16G|gpu_RTXA5000_24G|gpu_RTX6000_24G|gpu_2080Ti_11G
#SBATCH --gres=gpu:1
#SBATCH --mem=16G
#SBATCH --time=00:30:00

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Imposta la variabile di ambiente per il token di Hugging Face
echo "Configurazione token Hugging Face..."
export HF_TOKEN="*************************************"
echo "Variabile di ambiente HF_TOKEN impostata."

# Crea la directory di output
OUTPUT_DIR="/work/tesi_ediluzio/experiments/parameter_counts"
mkdir -p "$OUTPUT_DIR"

# Funzione per contare i parametri di un modello
count_parameters() {
    local model_name=$1
    local lora_r=$2
    local output_file="$OUTPUT_DIR/${model_name//\//_}_r${lora_r}_params.json"
    
    echo "Conteggio parametri per $model_name con LoRA rank $lora_r..."
    
    $PYTHON_ENV /work/tesi_ediluzio/scripts/utils/count_parameters.py \
        --model_name_or_path "$model_name" \
        --lora_r "$lora_r" \
        --load_in_4bit \
        --output_file "$output_file"
    
    echo "Risultati salvati in $output_file"
}

# Conta i parametri per Gemma 2 9B IT
count_parameters "google/gemma-2-9b-it" 128

# Conta i parametri per Llama 3.1 8B
count_parameters "meta-llama/Llama-3.1-8B-Instruct" 128

# Conta i parametri per Gemma 2 9B IT con diversi ranghi di LoRA
for rank in 8 16 32 64 128 256; do
    count_parameters "google/gemma-2-9b-it" "$rank"
done

# Conta i parametri per Llama 3.1 8B con diversi ranghi di LoRA
for rank in 8 16 32 64 128 256; do
    count_parameters "meta-llama/Llama-3.1-8B-Instruct" "$rank"
done

echo "Conteggio parametri completato!"
