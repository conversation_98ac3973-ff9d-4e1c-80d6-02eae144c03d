#!/bin/bash
#SBATCH --job-name=create_100k
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=01:00:00
#SBATCH --output=logs/create_100k_%j.out
#SBATCH --error=logs/create_100k_%j.err

echo "🎯 CREAZIONE DATASET 100K ESEMPI - 1000 TOKEN MEDI"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Attiva environment (best practice conda)
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Crea UN SOLO dataset di 100K esempi con ~1000 token medi
python scripts/data_processing/create_100k_1000avg_dataset.py \
    --data_file data/processed/filtered_svg_all_sources_FULL.json \
    --output_dir data/processed/xml_format_optimized

echo "✅ Dataset 100K esempi creato!"
