#!/bin/bash
#SBATCH --job-name=qual_eva
#SBATCH --output=logs/qualitative_evaluation_%j.log
#SBATCH --error=logs/qualitative_evaluation_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --mem=48G
#SBATCH --cpus-per-task=8
#SBATCH --time=24:00:00
#SBATCH --mail-type=FAIL
#SBATCH --mail-user=<EMAIL>

# Crea directory logs se non esiste
mkdir -p logs

# Setup ambiente
source /work/tesi_ediluzio/svg_env/bin/activate

# Installa dipendenze mancanti
pip install pandas scipy matplotlib seaborn
pip install --upgrade accelerate
pip install --upgrade peft
pip install --upgrade bitsandbytes
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# Login a Hugging Face con token
export HUGGING_FACE_HUB_TOKEN="*************************************"
huggingface-cli login --token *************************************

# Verifica dipendenze
python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
python -c "import scipy; print('Scipy installed successfully')"
python -c "import matplotlib; print('Matplotlib installed successfully')"

# Esecuzione script di inferenza per Llama
python scripts/inference/run_inference.py \
    --model_path meta-llama/Meta-Llama-3.1-8B-Instruct \
    --adapter_path experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/checkpoint-23900 \
    --data_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_file experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/inference_results.jsonl \
    --batch_size 4 \
    --max_length 512 \
    --device cuda

# Esecuzione script di inferenza per Gemma
python scripts/inference/run_inference.py \
    --model_path google/gemma-2-9b-it \
    --adapter_path experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/checkpoint-36200 \
    --data_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_file experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/inference_results.jsonl \
    --batch_size 4 \
    --max_length 512 \
    --device cuda

# Generazione report qualitativo
python experiments/xml_direct_input/generate_qualitative_report.py \
    --llama_results experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/inference_results.jsonl \
    --gemma_results experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/inference_results.jsonl \
    --reference_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/xml_direct_input/outputs/qualitative_report \
    --num_examples 50