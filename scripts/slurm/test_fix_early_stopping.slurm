#!/bin/bash
#SBATCH --job-name=test_fix_early_stopping
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --time=00:10:00
#SBATCH --output=logs/test_fix_early_stopping_%j.out
#SBATCH --error=logs/test_fix_early_stopping_%j.err

echo "=== TEST FIX EARLY STOPPING ==="
echo "Job ID: $SLURM_JOB_ID"
echo "Data inizio: $(date)"

# Attivazione ambiente
source svg_env/bin/activate

# Configurazione test
cat > /tmp/test_fix_config.json << 'EOF'
{
    "per_device_train_batch_size": 1,
    "per_device_eval_batch_size": 1,
    "gradient_accumulation_steps": 2,
    "learning_rate": 2e-5,
    "weight_decay": 0.01,
    "max_steps": 50,
    "lr_scheduler_type": "cosine",
    "warmup_ratio": 0.1,
    "logging_steps": 5,
    "lora_r": 8,
    "lora_alpha": 16,
    "lora_dropout": 0.05,
    "lora_target_modules": ["q_proj", "v_proj"],
    "max_length": 512,
    "bf16": false,
    "fp16": true,
    "gradient_checkpointing": true,
    "dataloader_num_workers": 0,
    "optim": "adamw_torch",
    "val_file": "data/processed/xml_format_optimized/test_set_100k_final_10000.json",
    "eval_strategy": "steps",
    "eval_steps": 10,
    "save_strategy": "steps", 
    "save_steps": 20,
    "save_total_limit": 2,
    "load_best_model_at_end": true,
    "metric_for_best_model": "eval_loss",
    "greater_is_better": false,
    "early_stopping_patience": 3,
    "early_stopping_threshold": 0.001
}
EOF

echo "🧪 Test training script con early stopping FIXATO..."

# Test con Gemma (più veloce)
torchrun --nproc_per_node=2 --master_port=29510 scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_test5_32k.json \
    --config_path /tmp/test_fix_config.json \
    --output_dir experiments/xml_direct_input/outputs/test_fix_early_stopping \
    --wandb_project svg_captioning_test_fix \
    --wandb_entity 337543-unimore \
    --wandb_run_name test_fix_early_stopping \
    --early_stopping \
    --patience 3 \
    --min_delta 0.001

echo "✅ Test completato!"
echo "Data fine: $(date)"
