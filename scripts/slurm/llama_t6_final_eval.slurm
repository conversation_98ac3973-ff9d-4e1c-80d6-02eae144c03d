#!/bin/bash
#SBATCH --job-name=llama_t6_final
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:2
#SBATCH --mem=48G
#SBATCH --time=24:00:00
#SBATCH --output=logs/llama_t6_final_%j.out
#SBATCH --error=logs/llama_t6_final_%j.err

echo "🦙 LLAMA T6 FINAL - TRAINING COMPLETO CON EVALUATION - 2 GPU"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"
echo "Data inizio: $(date)"

# Setup environment
echo "🔧 Setup environment..."
source /work/tesi_ediluzio/svg_env/bin/activate

# Pulizia cache GPU
echo "🧹 Pulizia cache GPU..."
python -c "import torch; torch.cuda.empty_cache(); print('✅ Cache GPU pulita. GPU disponibili:', torch.cuda.device_count())"
for i in $(seq 0 $(($(nvidia-smi -L | wc -l) - 1))); do
    echo "GPU $i: $(nvidia-smi -i $i --query-gpu=name --format=csv,noheader,nounits)"
    echo "Memoria: $(nvidia-smi -i $i --query-gpu=memory.total --format=csv,noheader,nounits)"
done

# Verifica dataset
echo "📊 Verifica dataset..."
TRAIN_FILE="/work/tesi_ediluzio/data/processed/xml_format_optimized/train_set_100k_final_90000.json"
VAL_FILE="/work/tesi_ediluzio/data/processed/xml_format_optimized/test_set_100k_final_10000.json"

if [ -f "$TRAIN_FILE" ]; then
    TRAIN_COUNT=$(jq length "$TRAIN_FILE")
    echo "✅ Dataset training caricato: $TRAIN_COUNT esempi"
else
    echo "❌ Dataset training non trovato: $TRAIN_FILE"
    exit 1
fi

if [ -f "$VAL_FILE" ]; then
    VAL_COUNT=$(jq length "$VAL_FILE")
    echo "✅ Dataset validation caricato: $VAL_COUNT esempi"
else
    echo "❌ Dataset validation non trovato: $VAL_FILE"
    exit 1
fi

# Trova ultimo checkpoint dal job precedente (llama_t6_eval)
CHECKPOINT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama_t6_eval"
LATEST_CHECKPOINT=""

if [ -d "$CHECKPOINT_DIR" ]; then
    # Trova l'ultimo checkpoint numericamente
    LATEST_CHECKPOINT=$(find "$CHECKPOINT_DIR" -name "checkpoint-*" -type d | sort -V | tail -1)
    if [ -n "$LATEST_CHECKPOINT" ]; then
        echo "✅ Checkpoint trovato: $LATEST_CHECKPOINT"
        CHECKPOINT_STEP=$(basename "$LATEST_CHECKPOINT" | sed 's/checkpoint-//')
        echo "✅ Ripartenza dallo step: $CHECKPOINT_STEP"
    else
        echo "⚠️ Nessun checkpoint trovato in llama_t6_eval, cerco in llama_t6_24h"
        CHECKPOINT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama_t6_24h"
        LATEST_CHECKPOINT=$(find "$CHECKPOINT_DIR" -name "checkpoint-*" -type d | sort -V | tail -1)
        if [ -n "$LATEST_CHECKPOINT" ]; then
            echo "✅ Checkpoint backup trovato: $LATEST_CHECKPOINT"
        else
            echo "⚠️ Nessun checkpoint trovato, training da zero"
        fi
    fi
else
    echo "⚠️ Directory llama_t6_eval non trovata, cerco backup"
    CHECKPOINT_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama_t6_24h"
    if [ -d "$CHECKPOINT_DIR" ]; then
        LATEST_CHECKPOINT=$(find "$CHECKPOINT_DIR" -name "checkpoint-*" -type d | sort -V | tail -1)
        if [ -n "$LATEST_CHECKPOINT" ]; then
            echo "✅ Checkpoint backup trovato: $LATEST_CHECKPOINT"
        fi
    fi
fi

# Configurazione CUDA per memoria ottimizzata
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:32
export CUDA_LAUNCH_BLOCKING=0

echo "🦙 Avvio training Llama T6 FINAL con evaluation..."

# Parametri training
RESUME_ARG=""
if [ -n "$LATEST_CHECKPOINT" ]; then
    RESUME_ARG="--resume_from_checkpoint $LATEST_CHECKPOINT"
fi

# Avvio training con distributed
torchrun --nproc_per_node=2 \
    --master_port=29503 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file /work/tesi_ediluzio/data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path /work/tesi_ediluzio/experiments/xml_direct_input/configs/llama_t6_24h.json \
    --output_dir /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama_t6_final \
    --wandb_entity 337543-unimore \
    --wandb_project svg_captioning_t6_final \
    $RESUME_ARG

echo "✅ Training FINAL completato alle: $(date)"
echo "📁 Output salvato in: /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama_t6_final"
