#!/bin/bash
#SBATCH --job-name=baseline
#SBATCH --output=logs/baseline_evaluation_fixed_%j.log
#SBATCH --error=logs/baseline_evaluation_fixed_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=12:00:00

echo "📊 BASELINE EVALUATION CON AMBIENTE RIPARATO 📊"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Crea directory logs se non esiste
mkdir -p logs

# ATTIVA AMBIENTE RIPARATO
echo "📦 Attivazione ambiente riparato..."
source /work/tesi_ediluzio/svg_env/bin/activate

# VERIFICA AMBIENTE
echo "🔍 Verifica ambiente..."
python -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')

import transformers
print(f'Transformers: {transformers.__version__}')

import numpy as np
print(f'NumPy: {np.__version__}')

import cairosvg
print('CairoSVG OK')
"

# DOWNLOAD NLTK DATA
echo "📦 Download NLTK data..."
python -c "import nltk; nltk.download('punkt'); nltk.download('wordnet')"

# LOGIN HUGGING FACE
echo "🔑 Login Hugging Face..."
export HUGGING_FACE_HUB_TOKEN="*************************************"
huggingface-cli login --token *************************************

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

echo "🎯 Avvio valutazione baseline models..."

# VALUTA IDEFICS 3
echo "📊 Valutazione IDEFICS 3..."
python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/baseline_evaluation_fixed \
    --models idefics3 \
    --batch_size 8 \
    --wandb_project svg_captioning_baselines_fixed

# VALUTA FLORENCE 2
echo "📊 Valutazione Florence 2..."
python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/baseline_evaluation_fixed \
    --models florence2 \
    --batch_size 8 \
    --wandb_project svg_captioning_baselines_fixed

# VALUTA BLIP2 2.7B
echo "📊 Valutazione BLIP2 2.7B..."
python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/baseline_evaluation_fixed \
    --models blip2 \
    --batch_size 8 \
    --wandb_project svg_captioning_baselines_fixed

echo "✅ Valutazione baseline completata!"
echo "Timestamp fine: $(date)"
