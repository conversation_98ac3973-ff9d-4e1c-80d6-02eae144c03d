#!/bin/bash
#SBATCH --job-name=test_llama_100k
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:2
#SBATCH --mem=48G
#SBATCH --time=00:03:00
#SBATCH --output=logs/test_llama_100k_%j.out
#SBATCH --error=logs/test_llama_100k_%j.err

echo "🧪 TEST LLAMA 100K DATASET - 3 MINUTI"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# ATTIVA ENVIRONMENT (best practice conda)
echo "🔧 Setup environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# VARIABILI AMBIENTE
export CUDA_VISIBLE_DEVICES=0,1
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:False,max_split_size_mb:64
export TOKENIZERS_PARALLELISM=false

# VERIFICA DATASET
echo "📊 Verifica dataset..."
python -c "
import json
train_file = 'data/processed/xml_format_optimized/train_set_100k_final_90000.json'
with open(train_file, 'r') as f:
    data = json.load(f)
print(f'✅ Dataset caricato: {len(data)} esempi')
print(f'✅ Primo esempio ID: {data[0].get(\"id\", \"N/A\")}')
"

# TRAINING TEST 3 MINUTI
echo "🚀 Avvio training test Llama..."
torchrun --nproc_per_node=2 --master_port=29502 scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/test_5_llama_final_24h.json \
    --output_dir experiments/xml_direct_input/outputs/test_llama_100k_3min \
    --wandb_project svg_captioning_test_100k \
    --wandb_entity 337543-unimore \
    --wandb_run_name test_llama_100k_3min \
    --early_stopping \
    --patience 5 \
    --min_delta 0.001

echo "✅ Test Llama completato!"
