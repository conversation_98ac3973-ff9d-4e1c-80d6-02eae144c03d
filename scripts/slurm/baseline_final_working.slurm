#!/bin/bash
#SBATCH --job-name=baseline_final
#SBATCH --output=logs/baseline_final_%j.out
#SBATCH --error=logs/baseline_final_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --gres=gpu:1
#SBATCH --cpus-per-task=4
#SBATCH --mem=16G
#SBATCH --time=06:00:00

echo "🚀 BASELINE EVALUATION FINALE - MODELLI FUNZIONANTI 🚀"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $(hostname)"

# Crea directory
mkdir -p logs
mkdir -p evaluation_results/baseline_final

echo ""
echo "🔧 ATTIVAZIONE AMBIENTE:"
cd /work/tesi_ediluzio
source svg_env/bin/activate

echo ""
echo "🔍 VERIFICA AMBIENTE:"
echo "Python: $(which python)"
echo "PyTorch: $(python -c 'import torch; print(torch.__version__)')"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

# Verifica memoria GPU
python -c "
import torch
if torch.cuda.is_available():
    for i in range(torch.cuda.device_count()):
        props = torch.cuda.get_device_properties(i)
        print(f'GPU {i}: {props.name} - {props.total_memory/1024**3:.1f} GB')
"

echo ""
echo "📊 DATASET DI TEST:"
TEST_FILE="data/processed/xml_format_optimized/baseline_eval_subset_400.json"
OUTPUT_DIR="evaluation_results/baseline_final"

if [ ! -f "$TEST_FILE" ]; then
    echo "❌ ERRORE: File di test non trovato: $TEST_FILE"
    exit 1
fi

echo "✅ Dataset trovato: $TEST_FILE"

echo ""
echo "🎯 MODELLI BASELINE FINALI (testati e funzionanti):"
echo "1. 🖼️ ViT-GPT2 (nlpconnect/vit-gpt2-image-captioning)"
echo "2. 🔧 GIT-base (microsoft/git-base)"  
echo "3. 👁️ BLIP-base (Salesforce/blip-image-captioning-base)"

echo ""
echo "🚀 FASE 1: ViT-GPT2"
echo "==================="

python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models vit-gpt2 \
    --batch_size 4 \
    --wandb_project svg_captioning_baseline_final

if [ $? -eq 0 ]; then
    echo "✅ ViT-GPT2 completato!"
else
    echo "❌ ViT-GPT2 fallito!"
fi

echo ""
echo "🚀 FASE 2: GIT-base"
echo "==================="

python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models git-base \
    --batch_size 4 \
    --wandb_project svg_captioning_baseline_final

if [ $? -eq 0 ]; then
    echo "✅ GIT-base completato!"
else
    echo "❌ GIT-base fallito!"
fi

echo ""
echo "🚀 FASE 3: BLIP-base"
echo "===================="

python scripts/evaluation/evaluate_real_baseline_models.py \
    --test_file "$TEST_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models blip-base \
    --batch_size 4 \
    --wandb_project svg_captioning_baseline_final

if [ $? -eq 0 ]; then
    echo "✅ BLIP-base completato!"
else
    echo "❌ BLIP-base fallito!"
fi

echo ""
echo "📊 RIEPILOGO RISULTATI FINALI:"
echo "==============================="

for model in vit-gpt2 git-base blip-base; do
    echo ""
    echo "🔹 $model:"
    
    METRICS_FILE="$OUTPUT_DIR/baseline_$model/metrics.json"
    
    if [ -f "$METRICS_FILE" ]; then
        echo "  ✅ Completato - Metriche:"
        python -c "
import json
try:
    with open('$METRICS_FILE', 'r') as f:
        metrics = json.load(f)
    print(f'    BLEU-4: {metrics.get(\"bleu\", 0):.4f}')
    print(f'    METEOR: {metrics.get(\"meteor\", 0):.4f}')
    print(f'    ROUGE-L: {metrics.get(\"rouge-l\", 0):.4f}')
    print(f'    CIDEr: {metrics.get(\"cider\", 0):.4f}')
except Exception as e:
    print(f'    ❌ Errore lettura metriche: {e}')
"
    else
        echo "  ❌ Non completato"
    fi
done

echo ""
echo "📁 FILE GENERATI:"
echo "  📊 Metriche: $OUTPUT_DIR/baseline_*/metrics.json"
echo "  📈 Predictions: $OUTPUT_DIR/baseline_*/predictions.jsonl"

echo ""
echo "🎉 EVALUATION BASELINE FINALE COMPLETATA!"
echo "Timestamp fine: $(date)"
echo ""
echo "🔗 Wandb project: https://wandb.ai/337543-unimore/svg_captioning_baseline_final"

echo ""
echo "📋 SUMMARY FINALE:"
echo "=================="
echo "✅ Modelli baseline valutati con dataset SVG"
echo "✅ Metriche calcolate: BLEU-4, METEOR, ROUGE-L, CIDEr"
echo "✅ Risultati salvati per confronto con modelli T6"
