#!/bin/bash
#SBATCH --job-name=gemma_1gpu
#SBATCH --partition=boost_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=48G
#SBATCH --time=00:05:00
#SBATCH --output=logs/test_gemma_1gpu_%j.out
#SBATCH --error=logs/test_gemma_1gpu_%j.err

echo "🎯 GEMMA SINGLE GPU - MASSIMA STABILITÀ"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"

# ATTIVA ENVIRONMENT
echo "🔧 Setup environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# VARIABILI AMBIENTE - SINGLE GPU
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:False,max_split_size_mb:32
export TOKENIZERS_PARALLELISM=false

# PULIZIA CACHE GPU
echo "🧹 Pulizia cache GPU..."
python -c "
import torch
if torch.cuda.is_available():
    torch.cuda.empty_cache()
    print(f'✅ Cache GPU pulita. GPU: {torch.cuda.get_device_name(0)}')
    print(f'Memoria: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB')
"

# TRAINING SINGLE GPU
echo "🚀 Avvio training single GPU..."
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_ultra_memory_optimized.json \
    --output_dir experiments/xml_direct_input/outputs/test_gemma_1gpu \
    --wandb_project svg_captioning_1gpu \
    --wandb_entity 337543-unimore \
    --wandb_run_name test_gemma_1gpu \
    --early_stopping \
    --patience 3 \
    --min_delta 0.001

echo "✅ Test single GPU completato!"
