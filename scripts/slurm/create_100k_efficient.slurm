#!/bin/bash
#SBATCH --job-name=create_100k_eff
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=4
#SBATCH --mem=8G
#SBATCH --time=00:30:00
#SBATCH --output=logs/create_100k_eff_%j.out
#SBATCH --error=logs/create_100k_eff_%j.err

echo "🚀 CREAZIONE EFFICIENTE DATASET 100K - 1000 TOKEN MEDI"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Attiva environment (best practice conda)
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env

# Versione efficiente: streaming + campionamento intelligente
python scripts/data_processing/create_100k_efficient.py \
    --data_file data/processed/filtered_svg_all_sources_FULL.json \
    --output_dir data/processed/xml_format_optimized

echo "✅ Dataset 100K efficiente creato!"
