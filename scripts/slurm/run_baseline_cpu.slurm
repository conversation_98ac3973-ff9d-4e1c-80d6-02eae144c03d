#!/bin/bash
#SBATCH --job-name=baseline_cpu
#SBATCH --output=logs/baseline_cpu_%j.log
#SBATCH --error=logs/baseline_cpu_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --account=tesi_ediluzio
#SBATCH --cpus-per-task=8
#SBATCH --mem=32G
#SBATCH --time=3:00:00
# NO GPU - Solo CPU per stabilità

echo "🖥️  BASELINE CPU INFERENCE (STABILE) 🖥️"
echo "Data: $(date)"
echo "Job ID: $SLURM_JOB_ID"
echo "Nodo: $SLURM_NODELIST"

# Crea directory logs se non esiste
mkdir -p logs

# ATTIVA AMBIENTE RIPARATO
echo "📦 Attivazione ambiente riparato..."
source /work/tesi_ediluzio/svg_env/bin/activate

# VERIFICA AMBIENTE
echo "🔍 Verifica ambiente CPU..."
python -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CPU cores: {torch.get_num_threads()}')

from transformers import pipeline
print('Pipeline Transformers OK')

import cairosvg
print('CairoSVG OK')
"

# CONFIGURAZIONE CPU OTTIMIZZATA
export OMP_NUM_THREADS=8
export MKL_NUM_THREADS=8
export TOKENIZERS_PARALLELISM=false

# LOGIN HUGGING FACE
echo "🔑 Login Hugging Face..."
export HUGGING_FACE_HUB_TOKEN="*************************************"
huggingface-cli login --token *************************************

# VAI NELLA DIRECTORY DEL PROGETTO
cd /work/tesi_ediluzio

echo "🎯 Baseline CPU: Ide Fix 3, Flores 2, BLIP 2.7B (versioni leggere)..."

# BASELINE CON CPU INFERENCE
python scripts/evaluation/evaluate_baseline_cpu.py \
    --test_file data/processed/xml_format/test_set_final_xml_reduced_rgb.json \
    --output_dir experiments/baseline_cpu_stable \
    --models idefix flores2 blip2

echo "✅ Baseline CPU completato!"
echo "Timestamp fine: $(date)"
