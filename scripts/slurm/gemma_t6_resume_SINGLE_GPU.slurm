#!/bin/bash
#SBATCH --job-name=gemma_t6_single
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=64G
#SBATCH --time=24:00:00
#SBATCH --output=logs/gemma_t6_single_%j.out
#SBATCH --error=logs/gemma_t6_single_%j.err

echo "=== GEMMA T6 SINGLE GPU RESUME ==="
echo "Job ID: $SLURM_JOB_ID"
echo "Data inizio: $(date)"
echo "🔧 SINGLE GPU - RISOLVE PROBLEMI MEMORIA"
echo "✅ CHECKPOINT SICURO - checkpoint-11000 (22% progresso)"

# Attivazione ambiente
source svg_env/bin/activate

# Pulizia memoria GPU
echo "🧹 Pulizia memoria GPU..."
nvidia-smi --gpu-reset || true
sleep 5

# Verifica GPU
echo "🔍 Verifica GPU disponibili:"
nvidia-smi --query-gpu=index,name,memory.total,memory.free --format=csv,noheader,nounits

# Attesa per stabilizzazione
echo "⏳ Attesa 15 secondi per stabilizzazione memoria..."
sleep 15

# VERIFICA CHECKPOINT ESISTENTE
echo "📊 Verifica checkpoint esistente..."
if [ -d "experiments/xml_direct_input/outputs/gemma_t6_24h/checkpoint-11000" ]; then
    echo "✅ Checkpoint 11000 trovato - RESUME ABILITATO"
    echo "📈 Progresso precedente: Step 11000/50000 (22%), Loss: ~0.45"
    echo "🔧 CHECKPOINT SICURO - salvato prima del bug early stopping"
else
    echo "❌ ERRORE: Checkpoint 11000 non trovato!"
    exit 1
fi

# Configurazione memoria ottimizzata
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:256
export CUDA_LAUNCH_BLOCKING=0

# TRAINING GEMMA T6 SINGLE GPU
echo "🚀 Avvio training Gemma T6 SINGLE GPU..."
echo "🔧 NOTA: Single GPU per evitare problemi memoria"

python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_100k_final_90000.json \
    --config_path experiments/xml_direct_input/configs/gemma_ultra_memory_optimized.json \
    --output_dir experiments/xml_direct_input/outputs/gemma_t6_24h \
    --wandb_project svg_captioning_t6 \
    --wandb_entity 337543-unimore \
    --wandb_run_name gemma_t6_24h_single_resume \
    --early_stopping \
    --patience 10 \
    --min_delta 0.001

echo "✅ Training Gemma T6 SINGLE GPU completato!"
echo "Data fine: $(date)"

echo "--- SCRIPT SLURM COMPLETATO ---"
