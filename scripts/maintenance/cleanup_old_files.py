#!/usr/bin/env python3
"""
Script per pulire file vecchi e liberare spazio su disco.
"""

import os
import shutil
import glob
from pathlib import Path
from datetime import datetime, timedelta
import argparse

def get_size_mb(path):
    """Ottiene la dimensione di un file o directory in MB."""
    if os.path.isfile(path):
        return os.path.getsize(path) / (1024 * 1024)
    elif os.path.isdir(path):
        total = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total += os.path.getsize(filepath)
                except:
                    pass
        return total / (1024 * 1024)
    return 0

def cleanup_logs(logs_dir, days_old=7, dry_run=True):
    """Pulisce log vecchi."""
    print(f"\n🗂️ PULIZIA LOGS (più vecchi di {days_old} giorni)")
    print("=" * 50)
    
    cutoff_date = datetime.now() - timedelta(days=days_old)
    total_freed = 0
    files_removed = 0
    
    for log_file in glob.glob(f"{logs_dir}/*.log") + glob.glob(f"{logs_dir}/*.err") + glob.glob(f"{logs_dir}/*.out"):
        try:
            file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
            if file_time < cutoff_date:
                size_mb = get_size_mb(log_file)
                print(f"  📄 {os.path.basename(log_file)}: {size_mb:.1f} MB")
                
                if not dry_run:
                    os.remove(log_file)
                    print(f"    ✅ Rimosso")
                else:
                    print(f"    🔍 Da rimuovere (dry run)")
                
                total_freed += size_mb
                files_removed += 1
        except Exception as e:
            print(f"  ❌ Errore con {log_file}: {e}")
    
    print(f"\n📊 Log cleanup: {files_removed} file, {total_freed:.1f} MB liberati")
    return total_freed

def cleanup_cache(cache_dir, dry_run=True):
    """Pulisce cache Hugging Face."""
    print(f"\n🗄️ PULIZIA CACHE HUGGING FACE")
    print("=" * 50)
    
    total_freed = 0
    
    if os.path.exists(cache_dir):
        size_mb = get_size_mb(cache_dir)
        print(f"  📁 Cache directory: {size_mb:.1f} MB")
        
        if not dry_run:
            shutil.rmtree(cache_dir)
            print(f"    ✅ Cache rimossa")
            total_freed = size_mb
        else:
            print(f"    🔍 Da rimuovere (dry run)")
            total_freed = size_mb
    else:
        print("  ℹ️ Nessuna cache trovata")
    
    print(f"\n📊 Cache cleanup: {total_freed:.1f} MB liberati")
    return total_freed

def cleanup_wandb_cache(wandb_dir, dry_run=True):
    """Pulisce cache Wandb."""
    print(f"\n📊 PULIZIA CACHE WANDB")
    print("=" * 50)
    
    total_freed = 0
    
    # Pulisce run vecchi
    runs_dir = os.path.join(wandb_dir, "run-*")
    old_runs = glob.glob(runs_dir)
    
    for run_dir in old_runs:
        try:
            # Mantieni solo gli ultimi 5 run
            if len(old_runs) > 5:
                size_mb = get_size_mb(run_dir)
                print(f"  📁 {os.path.basename(run_dir)}: {size_mb:.1f} MB")
                
                if not dry_run:
                    shutil.rmtree(run_dir)
                    print(f"    ✅ Rimosso")
                else:
                    print(f"    🔍 Da rimuovere (dry run)")
                
                total_freed += size_mb
                old_runs.remove(run_dir)
        except Exception as e:
            print(f"  ❌ Errore con {run_dir}: {e}")
    
    print(f"\n📊 Wandb cleanup: {total_freed:.1f} MB liberati")
    return total_freed

def cleanup_temp_files(base_dir, dry_run=True):
    """Pulisce file temporanei."""
    print(f"\n🗑️ PULIZIA FILE TEMPORANEI")
    print("=" * 50)
    
    total_freed = 0
    patterns = [
        "**/*.tmp",
        "**/*.temp", 
        "**/__pycache__/**",
        "**/.pytest_cache/**",
        "**/core.*"
    ]
    
    for pattern in patterns:
        files = glob.glob(os.path.join(base_dir, pattern), recursive=True)
        for file_path in files:
            try:
                size_mb = get_size_mb(file_path)
                print(f"  🗄️ {os.path.relpath(file_path, base_dir)}: {size_mb:.1f} MB")
                
                if not dry_run:
                    if os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                    else:
                        os.remove(file_path)
                    print(f"    ✅ Rimosso")
                else:
                    print(f"    🔍 Da rimuovere (dry run)")
                
                total_freed += size_mb
            except Exception as e:
                print(f"  ❌ Errore con {file_path}: {e}")
    
    print(f"\n📊 Temp files cleanup: {total_freed:.1f} MB liberati")
    return total_freed

def show_disk_usage(base_dir):
    """Mostra utilizzo disco."""
    print(f"\n💾 UTILIZZO DISCO")
    print("=" * 50)
    
    # Spazio totale
    statvfs = os.statvfs(base_dir)
    total_space = statvfs.f_frsize * statvfs.f_blocks / (1024**3)  # GB
    free_space = statvfs.f_frsize * statvfs.f_bavail / (1024**3)   # GB
    used_space = total_space - free_space
    
    print(f"  💽 Spazio totale: {total_space:.1f} GB")
    print(f"  📊 Spazio usato: {used_space:.1f} GB ({used_space/total_space*100:.1f}%)")
    print(f"  🆓 Spazio libero: {free_space:.1f} GB")
    
    # Directory più grandi
    print(f"\n📁 DIRECTORY PIÙ GRANDI:")
    dirs = []
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)
        if os.path.isdir(item_path):
            size_mb = get_size_mb(item_path)
            dirs.append((item, size_mb))
    
    dirs.sort(key=lambda x: x[1], reverse=True)
    for name, size_mb in dirs[:10]:
        print(f"  📂 {name}: {size_mb:.1f} MB")

def main():
    parser = argparse.ArgumentParser(description='Pulisce file vecchi e cache')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Mostra cosa verrebbe rimosso senza rimuovere')
    parser.add_argument('--logs-days', type=int, default=7,
                       help='Rimuovi log più vecchi di N giorni (default: 7)')
    parser.add_argument('--base-dir', default='/work/tesi_ediluzio',
                       help='Directory base del progetto')
    
    args = parser.parse_args()
    
    print("🧹 PULIZIA FILE VECCHI E CACHE")
    print("=" * 60)
    print(f"📁 Directory base: {args.base_dir}")
    print(f"🔍 Modalità: {'DRY RUN' if args.dry_run else 'ESECUZIONE REALE'}")
    
    # Mostra utilizzo disco iniziale
    show_disk_usage(args.base_dir)
    
    total_freed = 0
    
    # Pulisci logs
    logs_dir = os.path.join(args.base_dir, "logs")
    if os.path.exists(logs_dir):
        total_freed += cleanup_logs(logs_dir, args.logs_days, args.dry_run)
    
    # Pulisci cache Hugging Face
    cache_dir = os.path.join(args.base_dir, "cache")
    total_freed += cleanup_cache(cache_dir, args.dry_run)
    
    # Pulisci cache Wandb
    wandb_dir = os.path.join(args.base_dir, "wandb")
    if os.path.exists(wandb_dir):
        total_freed += cleanup_wandb_cache(wandb_dir, args.dry_run)
    
    # Pulisci file temporanei
    total_freed += cleanup_temp_files(args.base_dir, args.dry_run)
    
    print(f"\n🎉 RIEPILOGO FINALE")
    print("=" * 60)
    print(f"💾 Spazio totale liberato: {total_freed:.1f} MB ({total_freed/1024:.2f} GB)")
    
    if args.dry_run:
        print("🔍 Questo era un DRY RUN - nessun file è stato rimosso")
        print("🚀 Esegui senza --dry-run per rimuovere effettivamente i file")
    else:
        print("✅ Pulizia completata!")
    
    # Mostra utilizzo disco finale
    if not args.dry_run:
        show_disk_usage(args.base_dir)

if __name__ == '__main__':
    main()
