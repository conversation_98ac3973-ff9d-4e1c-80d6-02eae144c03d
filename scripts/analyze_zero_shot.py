#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import wandb
from typing import List, Dict, Any, Optional, Tuple

# Configurazione matplotlib per stile e dimensioni
plt.style.use('ggplot')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

def setup_wandb(project_name: str = "captioner", 
                run_name: str = "zero_shot_analysis",
                entity: str = "337543-unimore") -> None:
    """
    Inizializza Weights & Biands per il tracking.
    
    Args:
        project_name: Nome del progetto W&B
        run_name: Nome della run
        entity: Entity W&B (username o team)
    """
    try:
        wandb.init(
            project=project_name,
            name=run_name,
            entity=entity,
            config={
                "analysis_type": "zero_shot",
                "models": ["llama31_8b", "gemma2_9b_it", "mistral_7b", "phi3_3.8b", "qwen2_7b"],
                "dataset": "svg_xml_test"
            }
        )
        print(f"W&B inizializzato: {wandb.run.name} (ID: {wandb.run.id})")
    except Exception as e:
        print(f"Errore nell'inizializzazione di W&B: {e}")
        print("Continuazione senza tracking W&B...")

def load_results(results_path: str) -> List[Dict[str, Any]]:
    """
    Carica i risultati delle inferenze zero-shot da un file JSONL.
    
    Args:
        results_path: Percorso al file JSONL con i risultati
        
    Returns:
        Lista di dizionari con i risultati
    """
    results = []
    try:
        with open(results_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    results.append(json.loads(line))
        print(f"Caricati {len(results)} risultati da {results_path}")
    except Exception as e:
        print(f"Errore nel caricamento dei risultati: {e}")
        results = []
    return results

def analyze_caption_length(results: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    Analizza la lunghezza delle didascalie generate.
    
    Args:
        results: Lista di risultati
        
    Returns:
        DataFrame con statistiche sulla lunghezza
    """
    lengths = []
    models = []
    
    for result in results:
        if 'generated_caption' in result and 'model' in result:
            caption = result['generated_caption']
            model = result['model'].split('/')[-1]  # Estrai solo il nome del modello
            
            lengths.append(len(caption))
            models.append(model)
    
    df = pd.DataFrame({
        'model': models,
        'caption_length': lengths
    })
    
    return df

def analyze_caption_content(results: List[Dict[str, Any]]) -> pd.DataFrame:
    """
    Analizza il contenuto delle didascalie per classificarle.
    
    Args:
        results: Lista di risultati
        
    Returns:
        DataFrame con classificazione del contenuto
    """
    # Parole chiave per identificare i tipi di descrizione
    technical_keywords = ['svg', 'path', 'stroke', 'width', 'fill', 'coordinate', 'pixel', 'element', 'code']
    visual_keywords = ['shape', 'curve', 'line', 'circle', 'rectangle', 'image', 'looks like', 'appears', 'visual']
    
    data = []
    
    for result in results:
        if 'generated_caption' in result and 'model' in result:
            caption = result['generated_caption'].lower()
            model = result['model'].split('/')[-1]
            
            # Conta le occorrenze di parole chiave
            tech_count = sum(1 for kw in technical_keywords if kw in caption)
            visual_count = sum(1 for kw in visual_keywords if kw in caption)
            
            # Classifica la didascalia
            if tech_count > visual_count * 2:
                caption_type = 'Tecnica'
            elif visual_count > tech_count * 2:
                caption_type = 'Visiva'
            elif tech_count > 0 and visual_count > 0:
                caption_type = 'Mista'
            else:
                caption_type = 'Generica'
            
            data.append({
                'model': model,
                'caption_type': caption_type,
                'technical_score': tech_count,
                'visual_score': visual_count
            })
    
    return pd.DataFrame(data)

def plot_caption_length_distribution(df: pd.DataFrame, save_path: Optional[str] = None) -> plt.Figure:
    """
    Crea un grafico della distribuzione delle lunghezze delle didascalie.
    
    Args:
        df: DataFrame con i dati
        save_path: Percorso dove salvare il grafico (opzionale)
        
    Returns:
        Figura matplotlib
    """
    fig, ax = plt.subplots(figsize=(12, 8))
    
    sns.histplot(data=df, x='caption_length', hue='model', bins=20, alpha=0.7, ax=ax)
    
    ax.set_title('Distribuzione della Lunghezza delle Didascalie per Modello', fontsize=16)
    ax.set_xlabel('Lunghezza (caratteri)', fontsize=14)
    ax.set_ylabel('Frequenza', fontsize=14)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Grafico salvato in {save_path}")
    
    return fig

def plot_caption_type_distribution(df: pd.DataFrame, save_path: Optional[str] = None) -> plt.Figure:
    """
    Crea un grafico della distribuzione dei tipi di didascalie.
    
    Args:
        df: DataFrame con i dati
        save_path: Percorso dove salvare il grafico (opzionale)
        
    Returns:
        Figura matplotlib
    """
    # Conta i tipi di didascalie per modello
    counts = df.groupby(['model', 'caption_type']).size().reset_index(name='count')
    
    # Calcola le percentuali
    total_by_model = counts.groupby('model')['count'].transform('sum')
    counts['percentage'] = counts['count'] / total_by_model * 100
    
    fig, ax = plt.subplots(figsize=(14, 10))
    
    sns.barplot(data=counts, x='model', y='percentage', hue='caption_type', ax=ax)
    
    ax.set_title('Distribuzione dei Tipi di Didascalie per Modello', fontsize=16)
    ax.set_xlabel('Modello', fontsize=14)
    ax.set_ylabel('Percentuale (%)', fontsize=14)
    ax.set_ylim(0, 100)
    
    # Aggiungi etichette con le percentuali
    for p in ax.patches:
        height = p.get_height()
        if height > 5:  # Mostra solo percentuali > 5% per leggibilità
            ax.text(p.get_x() + p.get_width()/2.,
                    height + 1,
                    f'{height:.1f}%',
                    ha="center", fontsize=10)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Grafico salvato in {save_path}")
    
    return fig

def plot_technical_vs_visual_scores(df: pd.DataFrame, save_path: Optional[str] = None) -> plt.Figure:
    """
    Crea un grafico di confronto tra punteggi tecnici e visivi.
    
    Args:
        df: DataFrame con i dati
        save_path: Percorso dove salvare il grafico (opzionale)
        
    Returns:
        Figura matplotlib
    """
    # Calcola i punteggi medi per modello
    scores = df.groupby('model')[['technical_score', 'visual_score']].mean().reset_index()
    
    # Prepara i dati per il grafico
    models = scores['model'].tolist()
    tech_scores = scores['technical_score'].tolist()
    visual_scores = scores['visual_score'].tolist()
    
    x = np.arange(len(models))
    width = 0.35
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    rects1 = ax.bar(x - width/2, tech_scores, width, label='Punteggio Tecnico', color='skyblue')
    rects2 = ax.bar(x + width/2, visual_scores, width, label='Punteggio Visivo', color='lightcoral')
    
    ax.set_title('Confronto tra Punteggi Tecnici e Visivi per Modello', fontsize=16)
    ax.set_xlabel('Modello', fontsize=14)
    ax.set_ylabel('Punteggio Medio', fontsize=14)
    ax.set_xticks(x)
    ax.set_xticklabels(models, rotation=45)
    ax.legend()
    
    # Aggiungi etichette con i valori
    def autolabel(rects):
        for rect in rects:
            height = rect.get_height()
            ax.annotate(f'{height:.2f}',
                        xy=(rect.get_x() + rect.get_width()/2, height),
                        xytext=(0, 3),
                        textcoords="offset points",
                        ha='center', va='bottom')
    
    autolabel(rects1)
    autolabel(rects2)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Grafico salvato in {save_path}")
    
    return fig

def plot_model_comparison_radar(df: pd.DataFrame, save_path: Optional[str] = None) -> plt.Figure:
    """
    Crea un grafico radar per confrontare le performance dei modelli.
    
    Args:
        df: DataFrame con i dati
        save_path: Percorso dove salvare il grafico (opzionale)
        
    Returns:
        Figura matplotlib
    """
    # Calcola metriche per ogni modello
    model_metrics = {}
    
    for model in df['model'].unique():
        model_df = df[df['model'] == model]
        
        # Calcola metriche
        tech_score = model_df['technical_score'].mean()
        visual_score = model_df['visual_score'].mean()
        
        # Calcola la diversità delle didascalie (usando la lunghezza come proxy)
        diversity = model_df['technical_score'].std() + model_df['visual_score'].std()
        
        # Calcola il bilanciamento tra tecnico e visivo
        balance = 5 - abs(tech_score - visual_score)  # 5 è il massimo punteggio
        
        model_metrics[model] = {
            'Precisione Tecnica': tech_score,
            'Orientamento Visivo': visual_score,
            'Diversità': diversity,
            'Bilanciamento': balance
        }
    
    # Prepara i dati per il grafico radar
    categories = ['Precisione Tecnica', 'Orientamento Visivo', 'Diversità', 'Bilanciamento']
    
    # Normalizza i valori per ogni categoria
    normalized_metrics = {}
    for category in categories:
        max_val = max(metrics[category] for metrics in model_metrics.values())
        min_val = min(metrics[category] for metrics in model_metrics.values())
        range_val = max_val - min_val if max_val > min_val else 1
        
        for model, metrics in model_metrics.items():
            if model not in normalized_metrics:
                normalized_metrics[model] = {}
            
            # Normalizza a un valore tra 1 e 5
            normalized_metrics[model][category] = 1 + 4 * (metrics[category] - min_val) / range_val
    
    # Crea il grafico radar
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(polar=True))
    
    # Numero di categorie
    N = len(categories)
    
    # Angoli per ogni asse
    angles = [n / float(N) * 2 * np.pi for n in range(N)]
    angles += angles[:1]  # Chiudi il cerchio
    
    # Colori per ogni modello
    colors = plt.cm.tab10(np.linspace(0, 1, len(normalized_metrics)))
    
    # Disegna il grafico per ogni modello
    for i, (model, metrics) in enumerate(normalized_metrics.items()):
        values = [metrics[cat] for cat in categories]
        values += values[:1]  # Chiudi il cerchio
        
        ax.plot(angles, values, linewidth=2, linestyle='solid', label=model, color=colors[i])
        ax.fill(angles, values, alpha=0.1, color=colors[i])
    
    # Imposta le etichette degli assi
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    
    # Imposta i limiti dell'asse y
    ax.set_ylim(0, 5)
    
    # Aggiungi titolo e legenda
    plt.title('Confronto delle Performance dei Modelli', size=16, y=1.1)
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Grafico salvato in {save_path}")
    
    return fig

def main():
    parser = argparse.ArgumentParser(description="Analisi delle inferenze zero-shot per SVG captioning")
    parser.add_argument("--results_path", type=str, required=True, help="Percorso al file JSONL con i risultati")
    parser.add_argument("--output_dir", type=str, default="./analysis_results", help="Directory per salvare i grafici")
    parser.add_argument("--use_wandb", action="store_true", help="Usa Weights & Biands per il tracking")
    parser.add_argument("--wandb_project", type=str, default="captioner", help="Nome del progetto W&B")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore", help="Entity W&B")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Inizializza W&B se richiesto
    if args.use_wandb:
        setup_wandb(project_name=args.wandb_project, entity=args.wandb_entity)
    
    # Carica i risultati
    results = load_results(args.results_path)
    
    if not results:
        print("Nessun risultato da analizzare. Uscita.")
        return
    
    # Analizza i dati
    length_df = analyze_caption_length(results)
    content_df = analyze_caption_content(results)
    
    # Crea e salva i grafici
    length_plot_path = os.path.join(args.output_dir, "caption_length_distribution.png")
    length_fig = plot_caption_length_distribution(length_df, length_plot_path)
    
    type_plot_path = os.path.join(args.output_dir, "caption_type_distribution.png")
    type_fig = plot_caption_type_distribution(content_df, type_plot_path)
    
    scores_plot_path = os.path.join(args.output_dir, "technical_vs_visual_scores.png")
    scores_fig = plot_technical_vs_visual_scores(content_df, scores_plot_path)
    
    radar_plot_path = os.path.join(args.output_dir, "model_comparison_radar.png")
    radar_fig = plot_model_comparison_radar(content_df, radar_plot_path)
    
    # Carica i grafici su W&B se richiesto
    if args.use_wandb:
        try:
            wandb.log({
                "caption_length_distribution": wandb.Image(length_fig),
                "caption_type_distribution": wandb.Image(type_fig),
                "technical_vs_visual_scores": wandb.Image(scores_fig),
                "model_comparison_radar": wandb.Image(radar_fig)
            })
            print("Grafici caricati su W&B")
        except Exception as e:
            print(f"Errore nel caricamento dei grafici su W&B: {e}")
    
    # Chiudi le figure
    plt.close('all')
    
    # Chiudi W&B se inizializzato
    if args.use_wandb and wandb.run is not None:
        wandb.finish()
    
    print("Analisi completata!")

if __name__ == "__main__":
    main()
