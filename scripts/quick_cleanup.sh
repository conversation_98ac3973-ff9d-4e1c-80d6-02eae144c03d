#!/bin/bash

echo "⚡ PULIZIA RAPIDA CACHE E FILE TEMPORANEI ⚡"
echo "Data: $(date)"

cd /work/tesi_ediluzio

echo "🧹 Pulizia cache Python..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null
find . -name "*.pyc" -delete 2>/dev/null
find . -name "*.pyo" -delete 2>/dev/null
echo "  ✅ Cache Python pulita"

echo "🧹 Pulizia file temporanei..."
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.temp" -delete 2>/dev/null
find . -name "*~" -delete 2>/dev/null
find . -name ".DS_Store" -delete 2>/dev/null
echo "  ✅ File temporanei rimossi"

echo "🧹 Pulizia log vecchi (>7 giorni)..."
find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null
find logs/ -name "*.out" -mtime +7 -delete 2>/dev/null
find logs/ -name "*.err" -mtime +7 -delete 2>/dev/null
echo "  ✅ Log vecchi rimossi"

echo "🧹 Pulizia cache W&B offline..."
find . -type d -name "wandb" -exec find {} -name "offline-*" -type d -mtime +3 -exec rm -rf {} + \; 2>/dev/null
echo "  ✅ Cache W&B pulita"

echo ""
echo "✅ PULIZIA RAPIDA COMPLETATA!"
echo "📏 Spazio progetto: $(du -sh . | cut -f1)"
