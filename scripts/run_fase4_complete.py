#!/usr/bin/env python3
"""
Script completo per FASE 4 - Baseline Models e Gestione RGBA → RGB
"""

import argparse
import subprocess
import json
from pathlib import Path
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """Esegue un comando e gestisce errori."""
    logger.info(f"Eseguendo: {description}")
    logger.info(f"Comando: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completato con successo")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Errore in {description}")
        logger.error(f"Stdout: {e.stdout}")
        logger.error(f"Stderr: {e.stderr}")
        return False

def test_rgba_conversion(test_file, output_dir):
    """Testa la conversione RGBA → RGB."""
    logger.info("=== FASE 4.1: Test Conversione RGBA → RGB ===")
    
    command = f"""
    python scripts/evaluation/test_rgba_conversion.py \
        --input_file {test_file} \
        --output_dir {output_dir}/rgba_conversion_test \
        --num_samples 100
    """
    
    return run_command(command, "Test conversione RGBA → RGB")

def convert_dataset_rgba_to_rgb(input_file, output_file):
    """Converte il dataset da RGBA a RGB."""
    logger.info("=== FASE 4.2: Conversione Dataset RGBA → RGB ===")
    
    command = f"""
    python scripts/data_processing/convert_rgba_to_rgb.py \
        --input_file {input_file} \
        --output_file {output_file}
    """
    
    return run_command(command, "Conversione dataset RGBA → RGB")

def submit_baseline_evaluation():
    """Sottomette il job SLURM per valutazione baseline."""
    logger.info("=== FASE 4.3: Valutazione Baseline Models ===")
    
    command = "sbatch scripts/slurm/run_baseline_evaluation.slurm"
    
    return run_command(command, "Sottomissione job baseline evaluation")

def check_baseline_requirements():
    """Controlla che tutti i requisiti per i baseline siano soddisfatti."""
    logger.info("=== FASE 4.0: Controllo Requisiti ===")
    
    # Controlla file necessari
    required_files = [
        "scripts/evaluation/evaluate_real_baseline_models.py",
        "scripts/slurm/run_baseline_evaluation.slurm",
        "scripts/data_processing/convert_rgba_to_rgb.py",
        "scripts/evaluation/test_rgba_conversion.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"File mancanti: {missing_files}")
        return False
    
    # Controlla dataset
    test_file = "data/processed/xml_format/test_set_final_xml_reduced_rgb.json"
    if not Path(test_file).exists():
        logger.warning(f"File test non trovato: {test_file}")
        logger.info("Verrà usato il file originale per la conversione")
    
    logger.info("✅ Tutti i requisiti soddisfatti")
    return True

def generate_fase4_report(output_dir):
    """Genera un report completo della FASE 4."""
    logger.info("=== FASE 4.4: Generazione Report ===")
    
    report_content = """
# FASE 4 - REPORT COMPLETO

## Obiettivi FASE 4
1. ✅ Implementazione baseline models (BLIP, BLIP2 2.7B, ViT-GPT2, GIT-base)
2. ✅ Gestione conversione RGBA → RGB con sfondo bianco
3. ✅ Test rasterizzazione SVG con sfondo bianco
4. ✅ Valutazione automatizzata baseline models

## Modelli Baseline Implementati

### BLIP (Salesforce/blip-image-captioning-base)
- Modello: BlipForConditionalGeneration
- Processor: BlipProcessor
- Memoria GPU: ~2GB
- Batch size: 16

### BLIP2 2.7B (Salesforce/blip2-opt-2.7b)
- Modello: Blip2ForConditionalGeneration
- Processor: Blip2Processor
- Memoria GPU: ~8GB (float16)
- Batch size: 8

### ViT-GPT2 (nlpconnect/vit-gpt2-image-captioning)
- Modello: VisionEncoderDecoderModel
- Feature Extractor: ViTImageProcessor
- Tokenizer: AutoTokenizer
- Memoria GPU: ~1GB
- Batch size: 16

### GIT-base (microsoft/git-base)
- Modello: AutoModelForCausalLM
- Processor: AutoProcessor
- Memoria GPU: ~2GB
- Batch size: 16

## Gestione RGBA → RGB

### Conversione Colori SVG
- Conversione automatica rgba() → rgb()
- Alpha blending con sfondo bianco (255,255,255)
- Rimozione attributi opacity problematici
- Aggiunta sfondo bianco se mancante

### Rasterizzazione Immagini
- Conversione RGBA → RGB con sfondo bianco
- Gestione palette colors (P mode)
- Verifica 3 canali RGB finali
- Controllo sfondo bianco negli angoli

## Metriche di Valutazione
- BLEU score (corpus-level)
- METEOR score
- ROUGE-1, ROUGE-2, ROUGE-L
- Logging automatico su Weights & Biases

## File Generati
"""
    
    # Controlla risultati esistenti
    baseline_dir = Path(output_dir) / "baseline_evaluation"
    if baseline_dir.exists():
        report_content += f"\n### Risultati Baseline\n"
        for model_dir in baseline_dir.glob("baseline_*"):
            model_name = model_dir.name.replace("baseline_", "")
            metrics_file = model_dir / "metrics.json"
            if metrics_file.exists():
                with open(metrics_file) as f:
                    metrics = json.load(f)
                report_content += f"- {model_name.upper()}: BLEU={metrics.get('bleu', 'N/A'):.4f}, METEOR={metrics.get('meteor', 'N/A'):.4f}\n"
    
    rgba_test_dir = Path(output_dir) / "rgba_conversion_test"
    if rgba_test_dir.exists():
        results_file = rgba_test_dir / "conversion_test_results.json"
        if results_file.exists():
            with open(results_file) as f:
                results = json.load(f)
            report_content += f"\n### Test Conversione RGBA\n"
            report_content += f"- Campioni testati: {results['total_samples']}\n"
            report_content += f"- Conversioni riuscite: {results['successful_conversions']}\n"
            report_content += f"- SVG con trasparenza: {results['transparency_found']}\n"
    
    # Salva report
    report_file = Path(output_dir) / "fase4_report.md"
    with open(report_file, 'w') as f:
        f.write(report_content)
    
    logger.info(f"Report salvato in: {report_file}")
    print(report_content)

def main():
    parser = argparse.ArgumentParser(description='Esegue FASE 4 completa - Baseline e RGBA conversion')
    parser.add_argument('--test_file', 
                       default='data/processed/xml_format/test_set_final_xml_reduced.json',
                       help='File di test SVG')
    parser.add_argument('--output_dir', 
                       default='experiments/fase4_results',
                       help='Directory di output')
    parser.add_argument('--skip_conversion', action='store_true',
                       help='Salta conversione RGBA se già fatta')
    parser.add_argument('--skip_baseline', action='store_true',
                       help='Salta valutazione baseline')
    parser.add_argument('--only_test', action='store_true',
                       help='Esegue solo test conversione RGBA')
    
    args = parser.parse_args()
    
    # Crea directory di output
    output_path = Path(args.output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    logger.info("🚀 Inizio FASE 4 - Baseline Models e RGBA Conversion")
    
    # Controllo requisiti
    if not check_baseline_requirements():
        logger.error("❌ Requisiti non soddisfatti. Uscita.")
        return
    
    success_count = 0
    total_steps = 4
    
    # Step 1: Test conversione RGBA
    if test_rgba_conversion(args.test_file, args.output_dir):
        success_count += 1
    
    if args.only_test:
        logger.info("✅ Solo test eseguito. Uscita.")
        return
    
    # Step 2: Conversione dataset
    if not args.skip_conversion:
        rgb_file = args.test_file.replace('.json', '_rgb.json')
        if convert_dataset_rgba_to_rgb(args.test_file, rgb_file):
            success_count += 1
            args.test_file = rgb_file  # Usa il file convertito
    else:
        success_count += 1
        logger.info("⏭️  Conversione dataset saltata")
    
    # Step 3: Valutazione baseline
    if not args.skip_baseline:
        if submit_baseline_evaluation():
            success_count += 1
            logger.info("📊 Job baseline sottomesso. Controlla con 'squeue -u $USER'")
    else:
        success_count += 1
        logger.info("⏭️  Valutazione baseline saltata")
    
    # Step 4: Report finale
    generate_fase4_report(args.output_dir)
    success_count += 1
    
    # Riassunto finale
    logger.info(f"\n🎯 FASE 4 COMPLETATA: {success_count}/{total_steps} step riusciti")
    
    if success_count == total_steps:
        logger.info("✅ Tutti gli step completati con successo!")
        logger.info("📋 Controlla i risultati in:")
        logger.info(f"   - Report: {args.output_dir}/fase4_report.md")
        logger.info(f"   - Test RGBA: {args.output_dir}/rgba_conversion_test/")
        logger.info(f"   - Baseline: {args.output_dir}/baseline_evaluation/")
    else:
        logger.warning("⚠️  Alcuni step sono falliti. Controlla i log.")

if __name__ == '__main__':
    main()
