#!/bin/bash

# Script per eseguire tutte le analisi del progetto

# Imposta l'ambiente Python
PYTHON_ENV="/work/tesi_ediluzio/svg_captioning_env/bin/python"
echo "Using Python executable: $PYTHON_ENV"

# Configura PYTHONPATH
echo "Configurazione PYTHONPATH..."
export PYTHONPATH="/work/tesi_ediluzio:$PYTHONPATH"
echo "PYTHONPATH: $PYTHONPATH"

# Configura Weights & Biands
echo "Configurazione Weights & Biands..."
export WANDB_API_KEY="****************************************"
export WANDB_DIR="/work/tesi_ediluzio/wandb"
export WANDB_CACHE_DIR="/work/tesi_ediluzio/wandb/cache"
export WANDB_CONFIG_DIR="/work/tesi_ediluzio/wandb/config"
export WANDB_DATA_DIR="/work/tesi_ediluzio/wandb/data"
mkdir -p "$WANDB_DIR" "$WANDB_CACHE_DIR" "$WANDB_CONFIG_DIR" "$WANDB_DATA_DIR"
echo "Variabili di ambiente WANDB impostate."

# Parametri
ANALYSIS_DIR="/work/tesi_ediluzio/analysis"
DATA_DIR="/work/tesi_ediluzio/data/processed/xml_format"
OUTPUTS_DIR="/work/tesi_ediluzio/experiments/xml_direct_input/outputs"
WANDB_PROJECT="captioner"
WANDB_ENTITY="337543-unimore"

# Crea le directory necessarie
mkdir -p "$ANALYSIS_DIR/zero_shot"
mkdir -p "$ANALYSIS_DIR/tokenizer"
mkdir -p "$ANALYSIS_DIR/lora_parameters"
mkdir -p "$ANALYSIS_DIR/checkpoint_cleanup"

# 1. Analisi Zero-Shot
echo "Esecuzione dell'analisi zero-shot..."
$PYTHON_ENV scripts/generate_sample_data.py --num_samples 200 --output_path "$ANALYSIS_DIR/zero_shot/sample_zero_shot_results.jsonl"
$PYTHON_ENV scripts/analyze_zero_shot.py \
    --results_path "$ANALYSIS_DIR/zero_shot/sample_zero_shot_results.jsonl" \
    --output_dir "$ANALYSIS_DIR/zero_shot" \
    --use_wandb \
    --wandb_project "$WANDB_PROJECT" \
    --wandb_entity "$WANDB_ENTITY"

# 2. Analisi del Tokenizer Personalizzato
echo "Esecuzione dell'analisi del tokenizer personalizzato..."
$PYTHON_ENV scripts/analyze_custom_tokenizer.py \
    --model_path "meta-llama/Llama-3.1-8B-Instruct" \
    --data_path "$DATA_DIR/train_set_final_xml.json" \
    --output_dir "$ANALYSIS_DIR/tokenizer/llama31_8b_standard" \
    --max_examples 50 \
    --use_wandb

$PYTHON_ENV scripts/analyze_custom_tokenizer.py \
    --model_path "meta-llama/Llama-3.1-8B-Instruct" \
    --data_path "$DATA_DIR/train_set_final_xml.json" \
    --output_dir "$ANALYSIS_DIR/tokenizer/llama31_8b_custom" \
    --use_custom_tokenizer \
    --max_examples 50 \
    --use_wandb

# 3. Analisi dei Parametri LoRA
echo "Esecuzione dell'analisi dei parametri LoRA..."
$PYTHON_ENV scripts/analyze_lora_parameters.py \
    --model_paths \
    "$OUTPUTS_DIR/llama31_8b_lora_xml_no_token_convergence" \
    "$OUTPUTS_DIR/gemma2_9b_it_lora_xml_no_token_convergence" \
    "$OUTPUTS_DIR/llama31_8b_lora_xml_custom_token" \
    "$OUTPUTS_DIR/gemma2_9b_it_lora_xml_custom_token" \
    --output_dir "$ANALYSIS_DIR/lora_parameters" \
    --use_wandb

# 4. Analisi della Pulizia Automatica dei Checkpoint
echo "Esecuzione dell'analisi della pulizia automatica dei checkpoint..."
$PYTHON_ENV scripts/analyze_checkpoint_cleanup.py \
    --output_dirs \
    "$OUTPUTS_DIR/llama31_8b_lora_xml_no_token_convergence" \
    "$OUTPUTS_DIR/gemma2_9b_it_lora_xml_no_token_convergence" \
    "$OUTPUTS_DIR/llama31_8b_lora_xml_custom_token" \
    "$OUTPUTS_DIR/gemma2_9b_it_lora_xml_custom_token" \
    --analysis_dir "$ANALYSIS_DIR/checkpoint_cleanup" \
    --use_wandb

echo "Tutte le analisi sono state completate!"
echo "I risultati sono stati salvati in: $ANALYSIS_DIR"
echo "I grafici sono stati caricati su Weights & Biands: $WANDB_PROJECT"
