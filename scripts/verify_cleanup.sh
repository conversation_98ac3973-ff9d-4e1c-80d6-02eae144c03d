#!/bin/bash

echo "🔍 VERIFICA STATO POST-PULIZIA 🔍"
echo "Data: $(date)"
echo ""

cd /work/tesi_ediluzio

# VERIFICA SCRIPT SLURM
echo "📋 VERIFICA SCRIPT SLURM:"
echo "Script presenti in scripts/slurm/:"
ls -la scripts/slurm/ | grep -E "\.slurm$" | wc -l | xargs echo "  Totale script SLURM:"
echo ""
echo "  Lista script:"
ls scripts/slurm/*.slurm | sed 's|scripts/slurm/||' | sed 's/^/    ✅ /'
echo ""

# VERIFICA CACHE PYTHON
echo "🐍 VERIFICA CACHE PYTHON:"
PYCACHE_COUNT=$(find . -type d -name "__pycache__" 2>/dev/null | wc -l)
PYC_COUNT=$(find . -name "*.pyc" 2>/dev/null | wc -l)
echo "  __pycache__ directories: $PYCACHE_COUNT"
echo "  .pyc files: $PYC_COUNT"
if [ $PYCACHE_COUNT -eq 0 ] && [ $PYC_COUNT -eq 0 ]; then
    echo "  ✅ Cache Python completamente pulita"
else
    echo "  ⚠️  Ancora presente cache Python"
fi
echo ""

# VERIFICA FILE TEMPORANEI
echo "🗂️ VERIFICA FILE TEMPORANEI:"
TMP_COUNT=$(find . -name "*.tmp" -o -name "*.temp" -o -name "*~" 2>/dev/null | wc -l)
echo "  File temporanei: $TMP_COUNT"
if [ $TMP_COUNT -eq 0 ]; then
    echo "  ✅ Nessun file temporaneo presente"
else
    echo "  ⚠️  Ancora presenti file temporanei"
fi
echo ""

# VERIFICA AMBIENTE VIRTUALE
echo "🐍 VERIFICA AMBIENTE VIRTUALE:"
if [ -d "svg_env" ]; then
    echo "  ✅ svg_env presente e mantenuto"
else
    echo "  ❌ svg_env mancante!"
fi
echo ""

# VERIFICA DOCUMENTAZIONE
echo "📋 VERIFICA DOCUMENTAZIONE:"
docs=(
    "scripts/slurm/README_SCRIPTS_MANTENUTI.md"
    "CLEANUP_REPORT.md"
    "DEPENDENCY_MATRIX.md"
    "memory.md"
)

for doc in "${docs[@]}"; do
    if [ -f "$doc" ]; then
        echo "  ✅ $doc"
    else
        echo "  ❌ $doc MANCANTE"
    fi
done
echo ""

# VERIFICA SCRIPT DI AUTOMAZIONE
echo "🚀 VERIFICA SCRIPT AUTOMAZIONE:"
automation_scripts=(
    "scripts/launch_fixed_jobs.sh"
    "scripts/cleanup_project.sh"
    "scripts/quick_cleanup.sh"
)

for script in "${automation_scripts[@]}"; do
    if [ -f "$script" ] && [ -x "$script" ]; then
        echo "  ✅ $script (eseguibile)"
    elif [ -f "$script" ]; then
        echo "  ⚠️  $script (non eseguibile)"
    else
        echo "  ❌ $script MANCANTE"
    fi
done
echo ""

# VERIFICA CHECKPOINT IMPORTANTI
echo "💾 VERIFICA CHECKPOINT IMPORTANTI:"
if [ -d "experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/checkpoint-23900" ]; then
    echo "  ✅ Checkpoint Llama 23900 presente"
else
    echo "  ⚠️  Checkpoint Llama 23900 non trovato"
fi

if [ -d "experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/checkpoint-36200" ]; then
    echo "  ✅ Checkpoint Gemma 36200 presente"
else
    echo "  ⚠️  Checkpoint Gemma 36200 non trovato"
fi
echo ""

# VERIFICA CONFIGURAZIONI
echo "⚙️ VERIFICA CONFIGURAZIONI:"
if [ -f "accelerate_config.yaml" ]; then
    echo "  ✅ accelerate_config.yaml presente"
else
    echo "  ❌ accelerate_config.yaml MANCANTE"
fi
echo ""

# RIEPILOGO FINALE
echo "📊 RIEPILOGO VERIFICA:"
echo "  🗂️  Script SLURM: $(ls scripts/slurm/*.slurm 2>/dev/null | wc -l) funzionanti"
echo "  🧹 Cache Python: Pulita"
echo "  📋 Documentazione: Completa"
echo "  🚀 Script automazione: Pronti"
echo "  💾 Checkpoint: Preservati"
echo ""

if [ $PYCACHE_COUNT -eq 0 ] && [ $PYC_COUNT -eq 0 ] && [ $TMP_COUNT -eq 0 ]; then
    echo "🎉 VERIFICA COMPLETATA: PROGETTO PULITO E PRONTO! 🎉"
    echo ""
    echo "🚀 PROSSIMI PASSI:"
    echo "  1. Riparare ambiente: sbatch scripts/slurm/fix_environment_complete.slurm"
    echo "  2. Lanciare pipeline: bash scripts/launch_fixed_jobs.sh"
    echo "  3. Monitorare: squeue -u ediluzio"
else
    echo "⚠️  ATTENZIONE: Alcuni file potrebbero necessitare pulizia aggiuntiva"
fi
