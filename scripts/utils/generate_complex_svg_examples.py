import os
import json
import argparse
import random
from datetime import datetime

# Definizione di SVG complessi con token difficili
COMPLEX_SVGS = [
    # SVG con linearGradient
    """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
    </linearGradient>
</defs>
<path style="fill:url(#grad1);stroke:rgb(0,0,0);stroke-width:2" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
</svg>""",

    # SVG con radialGradient
    """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <radialGradient id="grad2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
        <stop offset="0%" style="stop-color:rgb(255,255,0);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgb(0,128,0);stop-opacity:1" />
    </radialGradient>
</defs>
<circle cx="256" cy="256" r="128" style="fill:url(#grad2);stroke:rgb(0,0,0);stroke-width:2" />
</svg>""",

    # SVG con filter e feGaussianBlur
    """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <filter id="blur1" x="0" y="0">
        <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
    </filter>
</defs>
<rect x="128" y="128" width="256" height="256" style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2;filter:url(#blur1)" />
</svg>""",

    # SVG con pattern
    """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <pattern id="pattern1" patternUnits="userSpaceOnUse" width="20" height="20">
        <circle cx="10" cy="10" r="5" style="fill:rgb(0,0,255);stroke:none" />
    </pattern>
</defs>
<rect x="128" y="128" width="256" height="256" style="fill:url(#pattern1);stroke:rgb(0,0,0);stroke-width:2" />
</svg>""",

    # SVG con clipPath
    """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <clipPath id="clip1">
        <circle cx="256" cy="256" r="128" />
    </clipPath>
</defs>
<rect x="128" y="128" width="256" height="256" style="fill:rgb(255,0,0);clip-path:url(#clip1);stroke:rgb(0,0,0);stroke-width:2" />
</svg>"""
]

def generate_examples(num_examples, output_file, models):
    """Genera esempi SVG complessi con token difficili"""
    examples = []
    
    for i in range(num_examples):
        # Seleziona un SVG complesso casuale
        svg = random.choice(COMPLEX_SVGS)
        
        # Crea un esempio per ogni modello
        for model in models:
            example = {
                "id": i,
                "complexity": "complex",
                "svg": svg,
                "true_caption": f"Esempio complesso {i+1} con token difficili",
                "generated_caption": "",  # Sarà riempito dopo l'inferenza
                "inference_time": 0.0,    # Sarà riempito dopo l'inferenza
                "model": model
            }
            examples.append(example)
    
    # Salva gli esempi in un file JSON
    with open(output_file, "w") as f:
        json.dump(examples, f, indent=2)
    
    print(f"Generati {len(examples)} esempi SVG complessi in {output_file}")
    return examples

def main():
    parser = argparse.ArgumentParser(description="Genera esempi SVG complessi con token difficili")
    parser.add_argument("--num_examples", type=int, default=5, help="Numero di esempi da generare per ogni modello")
    parser.add_argument("--output_file", type=str, default="evaluation/complex_svg_examples.json", help="File di output JSON")
    parser.add_argument("--models", type=str, nargs="+", default=["Llama-3.1-8B-Instruct", "gemma-2-9b-it"], help="Modelli da utilizzare")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Genera gli esempi
    generate_examples(args.num_examples, args.output_file, args.models)

if __name__ == "__main__":
    main()
