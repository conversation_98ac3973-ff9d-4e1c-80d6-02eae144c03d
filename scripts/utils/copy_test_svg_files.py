import os
import shutil
import argparse

def copy_test_svg_files(source_dir, output_dir):
    """
    Copia tutti i file SVG dalla directory di origine alla directory di output.
    """
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Trova tutti i file SVG nella directory di origine
    svg_files = []
    for root, dirs, files in os.walk(source_dir):
        for file in files:
            if file.endswith('.svg'):
                svg_files.append(os.path.join(root, file))
    
    # Copia i file SVG nella directory di output
    copied_files = []
    for source_path in svg_files:
        filename = os.path.basename(source_path)
        dest_path = os.path.join(output_dir, filename)
        
        # Copia il file
        shutil.copy2(source_path, dest_path)
        copied_files.append((filename, source_path, dest_path))
    
    # Stampa un riepilogo
    print(f"Trovati {len(svg_files)} file SVG nella directory {source_dir}.")
    print(f"Copiati {len(copied_files)} file SVG nella directory {output_dir}.")
    
    # Stampa i dettagli dei file copiati
    for filename, source, dest in copied_files:
        print(f"Copiato: {filename}")
    
    return copied_files

def main():
    parser = argparse.ArgumentParser(description="Copia tutti i file SVG dalla directory di origine alla directory di output")
    parser.add_argument("--source_dir", type=str, required=True, help="Directory di origine dei file SVG")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i file SVG")
    
    args = parser.parse_args()
    
    # Copia i file SVG
    copy_test_svg_files(args.source_dir, args.output_dir)

if __name__ == "__main__":
    main()
