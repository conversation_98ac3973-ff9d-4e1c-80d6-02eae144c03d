#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per confrontare i risultati dei diversi modelli.
"""

import os
import json
import argparse
from typing import Dict, List, Any

def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Carica un file JSONL."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def main():
    parser = argparse.ArgumentParser(description="Confronta i risultati dei diversi modelli.")
    parser.add_argument("--results_dir", type=str, default="/work/tesi_ediluzio/results/lora_xml", help="Directory contenente i file di risultati.")
    args = parser.parse_args()
    
    # Trova tutti i file JSONL nella directory
    result_files = [f for f in os.listdir(args.results_dir) if f.endswith(".jsonl")]
    print(f"Trovati {len(result_files)} file di risultati:")
    for f in result_files:
        print(f"- {f}")
    
    # Carica i risultati
    results = {}
    for file_name in result_files:
        file_path = os.path.join(args.results_dir, file_name)
        model_name = file_name.replace("_eval_test.jsonl", "").replace("_test.jsonl", "")
        
        try:
            data = load_jsonl(file_path)
            results[model_name] = data
            print(f"Caricati {len(data)} risultati per il modello {model_name}")
            
            # Stampa un esempio di risultato
            if data:
                print(f"Esempio di risultato per {model_name}:")
                example = data[0]
                if "generated_caption" in example:
                    print(f"  Didascalia generata: {example['generated_caption']}")
                elif "generated" in example:
                    print(f"  Didascalia generata: {example['generated']}")
        except Exception as e:
            print(f"Errore nel caricamento del file {file_name}: {e}")
    
    print("\nRiepilogo dei modelli:")
    for model_name, data in results.items():
        print(f"- {model_name}: {len(data)} risultati")

if __name__ == "__main__":
    main()
