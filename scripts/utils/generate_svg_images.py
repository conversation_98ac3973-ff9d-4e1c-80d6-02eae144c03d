import json
import os
import sys

def generate_svg_images(input_file, output_dir):
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Leggi il file JSON
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    # Genera le immagini SVG
    for i, item in enumerate(data):
        svg_data = item['svg']
        model_name = item['model'].lower().replace('-', '_').replace('.', '_')
        complexity = item['complexity']
        svg_file = f"{output_dir}/{model_name}_{complexity}_{i}.svg"
        
        with open(svg_file, 'w') as f:
            f.write(svg_data)
        
        print(f"Generata immagine SVG: {svg_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Uso: python generate_svg_images.py input_file.json output_dir")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_dir = sys.argv[2]
    generate_svg_images(input_file, output_dir)
