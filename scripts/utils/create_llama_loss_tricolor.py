import matplotlib.pyplot as plt
import numpy as np
import os
from datetime import datetime

# Crea la directory di output se non esiste
output_dir = "/work/tesi_ediluzio/evaluation/charts"
os.makedirs(output_dir, exist_ok=True)

# Dati estratti dai log di training di Llama
# [step, loss]
llama_data = [
    [0, 1.4493],
    [140, 0.9477],
    [280, 0.7426],
    [420, 0.7304],
    [560, 0.6855],
    [700, 0.6589],
    [840, 0.6281],
    [980, 0.5776],
    [1120, 0.5978],
    [1260, 0.5777],
    [1400, 0.5739],
    [1540, 0.606],
    [1680, 0.5827],
    [1820, 0.6246],
    [1960, 0.5477],
    [2100, 0.5183],
    [2240, 0.5453],
    [2380, 0.4975],
    [2520, 0.5161],
    [2660, 0.5097],
    [2800, 0.5269],
    [2940, 0.4696],
    [3080, 0.4342],
    [3220, 0.476],
    [3360, 0.5065],
    [3500, 0.4545],
    [3640, 0.4466],
    [3780, 0.459],
    [3920, 0.3796],
    [4060, 0.4069],
    [4200, 0.3914],
    [4340, 0.3923],
    [4480, 0.399],
    [4620, 0.4139],
    [4760, 0.407],
    [4900, 0.3234],
    [5040, 0.3437],
    [5180, 0.3191],
    [5320, 0.3138],
    [5460, 0.305],
    [5600, 0.3195],
    [5740, 0.3237],
    [5880, 0.2162],
    [6020, 0.2198],
    [6160, 0.2471],
    [6300, 0.2389],
    [6440, 0.2302],
    [6580, 0.2223],
    [6720, 0.2494],
    [6860, 0.1459],
    [7000, 0.1508],
    [7140, 0.1495],
    [7280, 0.1482],
    [7420, 0.1492],
    [7560, 0.1476],
    [7700, 0.1683],
    [7840, 0.0938],
    [7980, 0.091],
    [8120, 0.0951],
    [8260, 0.1001],
    [8400, 0.0956],
    [8540, 0.0953],
    [8680, 0.0994],
    [8820, 0.0639],
    [8960, 0.0689],
    [9100, 0.0648],
    [9240, 0.0684],
    [9380, 0.0663],
    [9520, 0.0646],
    [9660, 0.067],
    [9800, 0.0509],
    [9940, 0.0506],
    [10080, 0.0486],
    [10220, 0.0503],
    [10360, 0.0539],
    [10500, 0.0529],
    [10640, 0.052],
    [10780, 0.0433],
    [10920, 0.0441],
    [11060, 0.0441],
    [11200, 0.0436],
    [11340, 0.043],
    [11480, 0.0443],
    [11620, 0.0445],
    [11760, 0.0377],
    [11900, 0.0393],
    [12040, 0.0398],
    [12180, 0.0394],
    [12320, 0.0397],
    [12460, 0.0407],
    [12600, 0.0404],
    [12740, 0.0371],
    [12880, 0.0362],
    [13020, 0.0363],
    [13160, 0.0377],
    [13300, 0.0384],
    [13440, 0.0375],
    [13580, 0.0372],
    [13720, 0.0362],
    [13860, 0.0357],
    [14000, 0.036],
    [14140, 0.0366],
    [14280, 0.0362],
    [14420, 0.0352],
    [14560, 0.0367]
]

# Estrai i dati
steps = [point[0] for point in llama_data]
loss = [point[1] for point in llama_data]

# Dividi i dati in tre segmenti per i tre colori
# Segmento 1: dall'inizio fino a step 900 (fase iniziale di training)
# Segmento 2: da step 900 a step 1600 (fase intermedia)
# Segmento 3: da step 1600 in poi (fase finale)
segment1_end_idx = next(i for i, step in enumerate(steps) if step >= 900)
segment2_end_idx = next(i for i, step in enumerate(steps) if step >= 1600)

segment1_steps = steps[:segment1_end_idx]
segment1_loss = loss[:segment1_end_idx]

segment2_steps = steps[segment1_end_idx:segment2_end_idx]
segment2_loss = loss[segment1_end_idx:segment2_end_idx]

segment3_steps = steps[segment2_end_idx:]
segment3_loss = loss[segment2_end_idx:]

# Crea il grafico nello stile di Weights & Biands
plt.figure(figsize=(8, 5))

# Plotta i tre segmenti con colori diversi
plt.plot(segment1_steps, segment1_loss, color='#ff7f0e', linewidth=2)  # Arancione
plt.plot(segment2_steps, segment2_loss, color='#9467bd', linewidth=2)  # Viola
plt.plot(segment3_steps, segment3_loss, color='#e377c2', linewidth=2)  # Rosa

# Aggiungi punti alla fine di ogni segmento per evidenziare la transizione
plt.scatter([segment1_steps[-1]], [segment1_loss[-1]], color='#ff7f0e', s=30)
plt.scatter([segment2_steps[-1]], [segment2_loss[-1]], color='#9467bd', s=30)
plt.scatter([segment3_steps[-1]], [segment3_loss[-1]], color='#e377c2', s=30)

# Personalizza il grafico
plt.title('loss', fontsize=14, pad=15)
plt.xlabel('Step', fontsize=12)
plt.ylabel('', fontsize=12)  # Nessuna etichetta sull'asse y, come nell'immagine di riferimento
plt.grid(True, linestyle='-', alpha=0.1)

# Imposta i limiti degli assi
plt.xlim(0, 2600)  # Limita a 2600 step come nell'immagine di riferimento
plt.ylim(0, 1.5)

# Imposta i tick dell'asse x
plt.xticks([0, 500, 1000, 1500, 2000, 2500], ['', '500', '1k', '1.5k', '2k', '2.5k'])

# Imposta i tick dell'asse y
plt.yticks([0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2, 1.4])

# Aggiungi la label "Step" nell'angolo in basso a destra
plt.text(2500, 0.05, 'Step', fontsize=10, color='gray')

# Rimuovi i bordi del grafico
plt.gca().spines['top'].set_visible(False)
plt.gca().spines['right'].set_visible(False)
plt.gca().spines['bottom'].set_visible(False)
plt.gca().spines['left'].set_visible(False)

# Aggiungi un po' di padding
plt.tight_layout()

# Salva il grafico
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_path = f"{output_dir}/llama_loss_tricolor_wandb_style_{timestamp}.png"
plt.savefig(output_path, dpi=300, bbox_inches='tight', transparent=True)
print(f"Grafico salvato in: {output_path}")

# Crea una versione con sfondo bianco (non trasparente)
plt.savefig(output_path.replace('.png', '_white_bg.png'), dpi=300, bbox_inches='tight', transparent=False)
print(f"Grafico con sfondo bianco salvato in: {output_path.replace('.png', '_white_bg.png')}")

print("Completato!")
