import os
import re
import json
import shutil
import argparse

def extract_svg_filenames(html_file):
    """
    Estrae i nomi dei file SVG dal file HTML.
    """
    svg_filenames = []
    
    with open(html_file, 'r') as f:
        html_content = f.read()
    
    # Cerca i placeholder per gli SVG
    pattern = r'SVG Placeholder: ([^<]+)'
    matches = re.findall(pattern, html_content)
    
    for match in matches:
        svg_filenames.append(match.strip())
    
    return svg_filenames

def copy_svg_files(html_file, output_dir):
    """
    Copia i file SVG menzionati nel file HTML nella directory di output.
    """
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Estrai i nomi dei file SVG
    svg_filenames = extract_svg_filenames(html_file)
    
    # Cerca i file SVG nella directory dei dati
    data_dir = '/work/tesi_ediluzio/data'
    copied_files = []
    
    for filename in svg_filenames:
        # Cerca il file SVG in tutte le sottodirectory
        for root, dirs, files in os.walk(data_dir):
            if filename in files:
                source_path = os.path.join(root, filename)
                dest_path = os.path.join(output_dir, filename)
                
                # Copia il file
                shutil.copy2(source_path, dest_path)
                copied_files.append((filename, source_path, dest_path))
                break
    
    # Stampa un riepilogo
    print(f"Trovati {len(svg_filenames)} file SVG nel file HTML.")
    print(f"Copiati {len(copied_files)} file SVG nella directory {output_dir}.")
    
    if len(copied_files) < len(svg_filenames):
        print(f"Attenzione: {len(svg_filenames) - len(copied_files)} file SVG non sono stati trovati.")
    
    # Stampa i dettagli dei file copiati
    for filename, source, dest in copied_files:
        print(f"Copiato: {filename}")
        print(f"  Da: {source}")
        print(f"  A: {dest}")
    
    return copied_files

def main():
    parser = argparse.ArgumentParser(description="Copia i file SVG menzionati nel file HTML nella directory di output")
    parser.add_argument("--html_file", type=str, required=True, help="File HTML con i placeholder per gli SVG")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per i file SVG")
    
    args = parser.parse_args()
    
    # Copia i file SVG
    copy_svg_files(args.html_file, args.output_dir)

if __name__ == "__main__":
    main()
