#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per ottenere il prossimo numero di run per un determinato job.
Questo script legge un file di conteggio e incrementa il numero per il job specificato.
"""

import os
import json
import argparse
import logging

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# File di conteggio
COUNTER_FILE = "/work/tesi_ediluzio/run_counters.json"

def parse_args():
    parser = argparse.ArgumentParser(description="Ottieni il prossimo numero di run per un job")
    parser.add_argument("--job_name", type=str, required=True, help="Nome del job (es. llama, gemma)")
    return parser.parse_args()

def get_next_run_number(job_name):
    """
    Ottiene il prossimo numero di run per il job specificato.
    
    Args:
        job_name: Nome del job
        
    Returns:
        Prossimo numero di run
    """
    # Inizializza il contatore se il file non esiste
    if not os.path.exists(COUNTER_FILE):
        counters = {}
    else:
        try:
            with open(COUNTER_FILE, 'r') as f:
                counters = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            logger.warning(f"Errore nella lettura del file {COUNTER_FILE}, inizializzazione di un nuovo file")
            counters = {}
    
    # Ottieni il contatore corrente per il job
    current_count = counters.get(job_name, 0)
    
    # Incrementa il contatore
    next_count = current_count + 1
    counters[job_name] = next_count
    
    # Salva il contatore aggiornato
    with open(COUNTER_FILE, 'w') as f:
        json.dump(counters, f, indent=2)
    
    logger.info(f"Prossimo numero di run per {job_name}: {next_count}")
    return next_count

def main():
    args = parse_args()
    next_run_number = get_next_run_number(args.job_name)
    
    # Stampa il risultato (per l'uso in script bash)
    print(next_run_number)

if __name__ == "__main__":
    main()
