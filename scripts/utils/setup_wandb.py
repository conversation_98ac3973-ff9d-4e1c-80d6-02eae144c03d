#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging
import wandb

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Configurazione di Weights & Biands")
    parser.add_argument("--api_key", type=str, default="6006c12f16afe29f1402ea7340dadad0cf62b347", help="API key di Weights & Biands")
    parser.add_argument("--entity", type=str, default="337543", help="Entity di Weights & Biands (username o team)")
    parser.add_argument("--project", type=str, default="svg_captioning", help="Nome del progetto Weights & Biands")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # Imposta la variabile di ambiente per l'API key
    os.environ["WANDB_API_KEY"] = args.api_key
    
    # Imposta la variabile di ambiente per l'entity
    os.environ["WANDB_ENTITY"] = args.entity
    
    # Imposta la variabile di ambiente per il progetto
    os.environ["WANDB_PROJECT"] = args.project
    
    # Salva le variabili di ambiente in un file di configurazione
    wandb_dir = os.path.expanduser("~/.wandb")
    os.makedirs(wandb_dir, exist_ok=True)
    
    # Crea il file di configurazione
    config_path = os.path.join(wandb_dir, "settings")
    with open(config_path, "w") as f:
        f.write(f"[default]\n")
        f.write(f"api_key = {args.api_key}\n")
        f.write(f"entity = {args.entity}\n")
        f.write(f"project = {args.project}\n")
    
    logger.info(f"Configurazione di Weights & Biands salvata in {config_path}")
    
    # Verifica la connessione a W&B
    try:
        logger.info("Verifica della connessione a Weights & Biands...")
        api = wandb.Api()
        me = api.viewer()
        logger.info(f"Connessione riuscita! Utente: {me.username}")
        
        # Crea un run di test
        logger.info("Creazione di un run di test...")
        run = wandb.init(
            entity=args.entity,
            project=args.project,
            name="test_connection",
            config={
                "test": True,
                "message": "Connessione a Weights & Biands riuscita!"
            }
        )
        
        # Log di una metrica di test
        wandb.log({"test_metric": 1.0})
        
        # Chiudi il run
        wandb.finish()
        
        logger.info("Run di test completato con successo!")
        logger.info(f"Visita https://wandb.ai/{args.entity}/{args.project} per vedere i tuoi run")
    except Exception as e:
        logger.error(f"Errore nella connessione a Weights & Biands: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
