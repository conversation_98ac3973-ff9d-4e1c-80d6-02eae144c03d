import wandb
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description="Elenca le run di Weights & Biands")
    parser.add_argument("--entity", type=str, default="337543-unimore", help="Entity di Weights & Biands")
    parser.add_argument("--project", type=str, default="captioner", help="Nome del progetto Weights & Biands")
    parser.add_argument("--filter", type=str, default="llama", help="Filtra le run per nome (case insensitive)")
    parser.add_argument("--api_key", type=str, default="6006c12f16afe29f1402ea7340dadad0cf62b347", help="API key di Weights & Biands")
    parser.add_argument("--limit", type=int, default=10, help="Numero massimo di run da mostrare")
    return parser.parse_args()

def main():
    args = parse_args()

    # Configura l'API key
    wandb.login(key=args.api_key)

    # Inizializza l'API
    api = wandb.Api()

    # Ottieni le run
    runs = api.runs(f"{args.entity}/{args.project}", filters={"display_name": {"$regex": f"(?i){args.filter}"}})

    print(f"Run trovate nel progetto {args.project} (filtrate per '{args.filter}'):")
    print("-" * 80)
    print(f"{'ID':<30} {'Nome':<40} {'Stato':<10}")
    print("-" * 80)

    count = 0
    for run in runs:
        print(f"{run.id:<30} {run.name:<40} {run.state:<10}")
        count += 1
        if count >= args.limit:
            break

    print("-" * 80)
    print(f"Mostrate {count} run su {len(list(runs))} totali.")
    print("\nPer riempire i gap in una run, usa il comando:")
    print("python fill_wandb_gaps.py --run_id RUN_ID --start_step 1200 --end_step 1650 --start_value 0.23 --end_value 0.14")

if __name__ == "__main__":
    main()
