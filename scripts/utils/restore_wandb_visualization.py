#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import wandb
import glob
import re
from tqdm import tqdm

def parse_args():
    parser = argparse.ArgumentParser(description="Ripristina la visualizzazione di un training su Weights & Biands")
    parser.add_argument("--checkpoint_dir", type=str, required=True,
                        help="Directory del checkpoint (es. /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence)")
    parser.add_argument("--wandb_entity", type=str, default="337543-unimore",
                        help="Entity di Weights & Biands")
    parser.add_argument("--wandb_project", type=str, default="captioner",
                        help="Progetto di Weights & Biands")
    parser.add_argument("--wandb_run_name", type=str, required=True,
                        help="Nome del run su Weights & Biands")
    parser.add_argument("--model_name", type=str, required=True,
                        help="Nome del modello (es. Llama 3.1 8B o Gemma 2 9B IT)")
    return parser.parse_args()

def extract_training_data(checkpoint_dir):
    """Estrae i dati di training dai checkpoint"""
    print(f"Estrazione dei dati di training da {checkpoint_dir}")

    # Trova tutti i checkpoint
    checkpoint_pattern = os.path.join(checkpoint_dir, "checkpoint-*")
    print(f"Cercando checkpoint con pattern: {checkpoint_pattern}")
    checkpoint_dirs = glob.glob(checkpoint_pattern)
    print(f"Checkpoint trovati (non ordinati): {checkpoint_dirs}")

    if not checkpoint_dirs:
        print(f"Nessun checkpoint trovato in {checkpoint_dir}")
        return None

    # Ordina i checkpoint
    try:
        checkpoint_dirs = sorted(checkpoint_dirs,
                                key=lambda x: int(re.search(r"checkpoint-(\d+)", x).group(1)))
        print(f"Checkpoint ordinati: {checkpoint_dirs}")
    except Exception as e:
        print(f"Errore nell'ordinamento dei checkpoint: {e}")
        return None

    print(f"Trovati {len(checkpoint_dirs)} checkpoint")

    # Estrai i dati di training
    training_data = []

    for checkpoint_dir in tqdm(checkpoint_dirs, desc="Elaborazione checkpoint"):
        # Carica trainer_state.json
        trainer_state_path = os.path.join(checkpoint_dir, "trainer_state.json")
        if not os.path.exists(trainer_state_path):
            print(f"File trainer_state.json non trovato in {checkpoint_dir}")
            continue

        with open(trainer_state_path, "r") as f:
            trainer_state = json.load(f)

        # Estrai i dati di training
        if "log_history" in trainer_state:
            for log_entry in trainer_state["log_history"]:
                if "loss" in log_entry:
                    # Aggiungi step se non presente
                    if "step" not in log_entry and "global_step" in log_entry:
                        log_entry["step"] = log_entry["global_step"]

                    training_data.append(log_entry)

    # Ordina i dati per step
    training_data.sort(key=lambda x: x.get("step", 0))

    return training_data

def upload_to_wandb(training_data, args):
    """Carica i dati di training su Weights & Biands"""
    print(f"Caricamento dei dati su Weights & Biands (entity={args.wandb_entity}, project={args.wandb_project}, run_name={args.wandb_run_name})")

    # Configura Weights & Biands
    os.environ["WANDB_API_KEY"] = "****************************************"
    os.environ["WANDB_DIR"] = "/work/tesi_ediluzio/wandb"
    os.environ["WANDB_CACHE_DIR"] = "/work/tesi_ediluzio/wandb/cache"
    os.environ["WANDB_CONFIG_DIR"] = "/work/tesi_ediluzio/wandb/config"
    os.environ["WANDB_DATA_DIR"] = "/work/tesi_ediluzio/wandb/data"

    # Crea le directory se non esistono
    for dir_path in [os.environ["WANDB_DIR"], os.environ["WANDB_CACHE_DIR"],
                    os.environ["WANDB_CONFIG_DIR"], os.environ["WANDB_DATA_DIR"]]:
        os.makedirs(dir_path, exist_ok=True)

    # Inizializza un nuovo run
    run = wandb.init(
        entity=args.wandb_entity,
        project=args.wandb_project,
        name=args.wandb_run_name,
        config={
            "model_name": args.model_name,
            "checkpoint_dir": args.checkpoint_dir,
            "restored": True
        }
    )

    # Carica i dati di training
    print(f"Caricamento di {len(training_data)} punti dati")

    for entry in tqdm(training_data, desc="Caricamento dati"):
        # Estrai step e dati
        step = entry.get("step", 0)

        # Crea un dizionario con i dati da caricare
        log_dict = {}

        # Aggiungi tutte le metriche
        for key, value in entry.items():
            if key not in ["step", "global_step", "epoch"]:
                log_dict[key] = value

        # Aggiungi epoch se presente
        if "epoch" in entry:
            log_dict["epoch"] = entry["epoch"]

        # Carica i dati
        wandb.log(log_dict, step=step)

    # Chiudi il run
    wandb.finish()

    print(f"Caricamento completato. Run disponibile su: {run.get_url()}")

def main():
    args = parse_args()

    # Estrai i dati di training
    training_data = extract_training_data(args.checkpoint_dir)

    if not training_data:
        print("Nessun dato di training trovato. Uscita.")
        return

    print(f"Estratti {len(training_data)} punti dati di training")

    # Carica i dati su Weights & Biands
    upload_to_wandb(training_data, args)

    print("Ripristino della visualizzazione completato con successo.")

if __name__ == "__main__":
    main()
