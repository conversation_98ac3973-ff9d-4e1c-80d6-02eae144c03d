#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per calcolare la similarità tra caption generate e caption di riferimento.
"""

import os
import sys
import json
import numpy as np
from collections import Counter
import re
import nltk
from nltk.util import ngrams
from nltk.tokenize import word_tokenize
from tqdm import tqdm

# Configurazione
PROJECT_ROOT = "/work/tesi_ediluzio"
RESULTS_FILE = f"{PROJECT_ROOT}/experiments/xml_direct_input/evaluation_results/llama31_8b_test1_convergence/checkpoint-2300/inference_results.jsonl"
OUTPUT_FILE = f"{PROJECT_ROOT}/experiments/xml_direct_input/evaluation_results/similarity_scores/llama31_8b_similarity_scores.json"
MODEL_NAME = "llama31_8b_finetuned"
NUM_SAMPLES = 50  # Limitiamo a 50 campioni per velocizzare

# Crea la directory di output se non esiste
os.makedirs(os.path.dirname(OUTPUT_FILE), exist_ok=True)

# Assicurati che NLTK abbia i dati necessari
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

def preprocess_text(text):
    """Preprocessa il testo per la similarità."""
    # Rimuovi i token speciali e la punteggiatura
    text = re.sub(r'</s>|<s>|<pad>|<unk>', '', text)
    # Converti in minuscolo
    text = text.lower()
    # Tokenizza
    tokens = word_tokenize(text)
    # Rimuovi la punteggiatura
    tokens = [token for token in tokens if token.isalnum()]
    return tokens

def calculate_jaccard_similarity(text1, text2):
    """Calcola la similarità di Jaccard tra due testi."""
    tokens1 = set(preprocess_text(text1))
    tokens2 = set(preprocess_text(text2))
    
    intersection = tokens1.intersection(tokens2)
    union = tokens1.union(tokens2)
    
    return len(intersection) / len(union) if union else 0

def calculate_cosine_similarity(text1, text2):
    """Calcola la similarità del coseno tra due testi."""
    tokens1 = preprocess_text(text1)
    tokens2 = preprocess_text(text2)
    
    # Crea i vettori di frequenza
    counter1 = Counter(tokens1)
    counter2 = Counter(tokens2)
    
    # Unisci i vocabolari
    all_tokens = set(counter1.keys()).union(counter2.keys())
    
    # Crea i vettori
    vector1 = [counter1.get(token, 0) for token in all_tokens]
    vector2 = [counter2.get(token, 0) for token in all_tokens]
    
    # Calcola il prodotto scalare
    dot_product = sum(a * b for a, b in zip(vector1, vector2))
    
    # Calcola le norme
    norm1 = np.sqrt(sum(a * a for a in vector1))
    norm2 = np.sqrt(sum(b * b for b in vector2))
    
    # Calcola la similarità del coseno
    return dot_product / (norm1 * norm2) if norm1 * norm2 > 0 else 0

def calculate_bleu_score(reference, candidate, n=4):
    """Calcola una versione semplificata del BLEU score."""
    reference_tokens = preprocess_text(reference)
    candidate_tokens = preprocess_text(candidate)
    
    # Calcola la precisione per diversi n-grammi
    precisions = []
    for i in range(1, n + 1):
        # Genera n-grammi
        reference_ngrams = list(ngrams(reference_tokens, i))
        candidate_ngrams = list(ngrams(candidate_tokens, i))
        
        if not candidate_ngrams:
            precisions.append(0)
            continue
        
        # Conta le corrispondenze
        reference_counts = Counter(reference_ngrams)
        candidate_counts = Counter(candidate_ngrams)
        
        # Calcola la precisione
        matches = sum(min(reference_counts[ngram], candidate_counts[ngram]) for ngram in candidate_counts)
        precision = matches / len(candidate_ngrams) if candidate_ngrams else 0
        precisions.append(precision)
    
    # Calcola la media geometrica delle precisioni
    if all(p > 0 for p in precisions):
        bleu = np.exp(np.mean([np.log(p) for p in precisions]))
    else:
        bleu = 0
    
    # Applica la penalità di brevità
    brevity_penalty = min(1, len(candidate_tokens) / len(reference_tokens)) if reference_tokens else 0
    
    return bleu * brevity_penalty

def load_results(results_file):
    """Carica i risultati dell'inferenza da un file JSON o JSONL."""
    results = []
    
    try:
        # Determina se il file è JSON o JSONL
        with open(results_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            
            # Riavvolgi il file
            f.seek(0)
            
            if first_line.startswith('[') or first_line.endswith(']'):
                # File JSON
                results = json.load(f)
            else:
                # File JSONL
                for line in f:
                    results.append(json.loads(line))
                    
        print(f"Caricati {len(results)} risultati da {results_file}")
        return results
    except Exception as e:
        print(f"Errore nel caricamento dei risultati: {e}")
        return []

def main():
    # Carica i risultati dell'inferenza
    results = load_results(RESULTS_FILE)
    
    # Limita il numero di campioni
    if NUM_SAMPLES > 0 and NUM_SAMPLES < len(results):
        results = results[:NUM_SAMPLES]
        print(f"Limitato a {NUM_SAMPLES} campioni")
    
    # Calcola le metriche di similarità per ogni risultato
    similarity_scores = []
    
    for i, result in enumerate(tqdm(results, desc="Calcolo similarità")):
        item_id = result.get('id')
        generated_caption = result.get('generated_caption', '')
        true_caption = result.get('true_caption', '')
        
        # Calcola le metriche di similarità
        jaccard = calculate_jaccard_similarity(generated_caption, true_caption)
        cosine = calculate_cosine_similarity(generated_caption, true_caption)
        bleu = calculate_bleu_score(true_caption, generated_caption)
        
        # Aggiungi le metriche al risultato
        result['jaccard_similarity'] = jaccard
        result['cosine_similarity'] = cosine
        result['bleu_score'] = bleu
        
        similarity_scores.append({
            'id': item_id,
            'jaccard_similarity': jaccard,
            'cosine_similarity': cosine,
            'bleu_score': bleu
        })
        
        if (i + 1) % 10 == 0 or i == len(results) - 1:
            print(f"Elaborati {i + 1}/{len(results)} esempi")
    
    # Calcola statistiche aggregate
    jaccard_scores = [r['jaccard_similarity'] for r in results if 'jaccard_similarity' in r]
    cosine_scores = [r['cosine_similarity'] for r in results if 'cosine_similarity' in r]
    bleu_scores = [r['bleu_score'] for r in results if 'bleu_score' in r]
    
    if jaccard_scores:
        aggregated_metrics = {
            'mean_jaccard_similarity': np.mean(jaccard_scores),
            'mean_cosine_similarity': np.mean(cosine_scores),
            'mean_bleu_score': np.mean(bleu_scores),
            'median_jaccard_similarity': np.median(jaccard_scores),
            'median_cosine_similarity': np.median(cosine_scores),
            'median_bleu_score': np.median(bleu_scores),
            'min_jaccard_similarity': np.min(jaccard_scores),
            'max_jaccard_similarity': np.max(jaccard_scores),
            'min_cosine_similarity': np.min(cosine_scores),
            'max_cosine_similarity': np.max(cosine_scores),
            'min_bleu_score': np.min(bleu_scores),
            'max_bleu_score': np.max(bleu_scores),
            'num_samples': len(jaccard_scores)
        }
        
        print(f"Media similarità Jaccard: {aggregated_metrics['mean_jaccard_similarity']:.4f}")
        print(f"Media similarità coseno: {aggregated_metrics['mean_cosine_similarity']:.4f}")
        print(f"Media BLEU score: {aggregated_metrics['mean_bleu_score']:.4f}")
    else:
        aggregated_metrics = {}
        print("Nessun dato disponibile per calcolare le metriche aggregate.")
    
    # Crea il risultato finale
    final_result = {
        'model_name': MODEL_NAME,
        'aggregated': aggregated_metrics,
        'samples': similarity_scores
    }
    
    # Salva i risultati
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(final_result, f, indent=2)
    
    print(f"Risultati salvati in {OUTPUT_FILE}")
    print("Valutazione completata!")

if __name__ == "__main__":
    main()
