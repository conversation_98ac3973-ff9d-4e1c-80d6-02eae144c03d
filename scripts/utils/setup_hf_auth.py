#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per configurare l'autenticazione Hugging Face.
"""

import os
import argparse
from huggingface_hub import login
from dotenv import load_dotenv

def setup_auth(token=None):
    """
    Configura l'autenticazione Hugging Face.
    
    Args:
        token: Token di accesso Hugging Face (opzionale)
    """
    # Se il token non è specificato, carica da .env
    if token is None:
        load_dotenv()
        token = os.getenv("HF_TOKEN")
    
    if not token:
        raise ValueError("Token di accesso Hugging Face non trovato. Specificare il token come argomento o nel file .env.")
    
    # Login su Hugging Face
    login(token=token, write_permission=False)
    print("Autenticazione Hugging Face configurata con successo.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Configura l'autenticazione Hugging Face.")
    parser.add_argument("--token", type=str, help="Token di accesso Hugging Face (opzionale, altrimenti carica da .env)")
    
    args = parser.parse_args()
    setup_auth(args.token)
