import os
import json
import time
import argparse
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# SVG di esempio semplice
SIMPLE_SVG = """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
</svg>"""

def main():
    parser = argparse.ArgumentParser(description="Esegue l'inferenza su un modello piccolo")
    parser.add_argument("--output_file", type=str, required=True, help="File JSON di output con i risultati dell'inferenza")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Pulisci la memoria
    torch.cuda.empty_cache()
    
    # Usa un modello piccolo
    model_name = "gpt2"
    
    try:
        print(f"Caricamento del modello {model_name}...")
        
        # Carica il tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print("Tokenizer caricato con successo")
        
        # Carica il modello
        model = AutoModelForCausalLM.from_pretrained(model_name)
        print("Modello caricato con successo")
        
        # Crea il prompt
        prompt = f"Descrivi questa immagine SVG:\n{SIMPLE_SVG}\n\n"
        print("Prompt creato")
        
        # Tokenizza il prompt
        inputs = tokenizer(prompt, return_tensors="pt")
        print("Prompt tokenizzato")
        
        # Genera la didascalia
        print("Generazione della didascalia...")
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=128,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
            )
        end_time = time.time()
        print("Didascalia generata")
        
        # Decodifica la risposta
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Rimuovi il prompt dalla risposta
        generated_caption = generated_text[len(prompt):].strip()
        
        # Calcola il tempo di inferenza
        inference_time = end_time - start_time
        
        # Crea l'esempio
        example = {
            "id": 0,
            "complexity": "simple",
            "svg": SIMPLE_SVG,
            "true_caption": "Esempio semplice",
            "generated_caption": generated_caption,
            "inference_time": inference_time,
            "model": model_name
        }
        
        # Salva i risultati
        with open(args.output_file, "w") as f:
            json.dump([example], f, indent=2)
        
        print(f"Inferenza completata in {inference_time:.2f}s")
        print(f"Risultati salvati in {args.output_file}")
        
    except Exception as e:
        print(f"Errore: {e}")
        
        # Salva un risultato vuoto
        example = {
            "id": 0,
            "complexity": "simple",
            "svg": SIMPLE_SVG,
            "true_caption": "Esempio semplice",
            "generated_caption": f"Errore: {str(e)}",
            "inference_time": 0.0,
            "model": model_name
        }
        
        with open(args.output_file, "w") as f:
            json.dump([example], f, indent=2)

if __name__ == "__main__":
    main()
