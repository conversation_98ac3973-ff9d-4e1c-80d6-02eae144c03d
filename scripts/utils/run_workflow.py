#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per l'automazione del workflow completo di SVG captioning.
Esegue tutte le fasi del workflow in sequenza.
"""

import os
import argparse
import logging
import subprocess
import time
from typing import Dict, List, Any, Optional
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command: str, description: str = None) -> int:
    """
    Esegue un comando shell.

    Args:
        command: Comando da eseguire
        description: Descrizione del comando (opzionale)

    Returns:
        Codice di uscita del comando
    """
    if description:
        logger.info(f"{description}...")

    logger.info(f"Esecuzione del comando: {command}")
    process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()

    if process.returncode != 0:
        logger.error(f"Errore nell'esecuzione del comando: {command}")
        logger.error(f"Stderr: {stderr.decode('utf-8')}")
    else:
        logger.info(f"Comando eseguito con successo: {command}")

    return process.returncode

def wait_for_job(job_id: str, check_interval: int = 60, max_wait_time: int = 3600) -> bool:
    """
    Attende il completamento di un job SLURM.

    Args:
        job_id: ID del job SLURM
        check_interval: Intervallo di controllo in secondi
        max_wait_time: Tempo massimo di attesa in secondi

    Returns:
        True se il job è completato con successo, False altrimenti
    """
    logger.info(f"In attesa del completamento del job {job_id}...")

    start_time = time.time()
    while time.time() - start_time < max_wait_time:
        # Controlla lo stato del job
        process = subprocess.Popen(f"squeue -j {job_id} -h", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()

        # Se il job non è più nella coda, è completato
        if not stdout.strip():
            # Controlla se il job è completato con successo
            process = subprocess.Popen(f"sacct -j {job_id} -n -P -o State", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate()

            state = stdout.decode('utf-8').strip()
            if "COMPLETED" in state:
                logger.info(f"Job {job_id} completato con successo.")
                return True
            else:
                logger.error(f"Job {job_id} terminato con stato: {state}")
                return False

        # Attendi prima del prossimo controllo
        time.sleep(check_interval)

    logger.error(f"Timeout nell'attesa del job {job_id}.")
    return False

def extract_job_id(output: str) -> Optional[str]:
    """
    Estrae l'ID del job SLURM dall'output del comando sbatch.

    Args:
        output: Output del comando sbatch

    Returns:
        ID del job SLURM o None se non trovato
    """
    import re
    match = re.search(r"Submitted batch job (\d+)", output)
    if match:
        return match.group(1)
    return None

def run_zero_shot_inference(
    models: List[str],
    output_dir: str,
    wait_for_completion: bool = True
) -> Dict[str, str]:
    """
    Esegue l'inferenza zero-shot per i modelli specificati.

    Args:
        models: Lista di modelli
        output_dir: Directory di output
        wait_for_completion: Se True, attende il completamento dei job

    Returns:
        Dizionario con gli ID dei job
    """
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)

    # Esegui l'inferenza per ogni modello
    job_ids = {}
    for model in models:
        # Estrai il nome breve del modello
        model_name = model.split("/")[-1].lower().replace("-", "_")

        # Definisci il percorso di output
        output_file = os.path.join(output_dir, f"{model_name}_results.jsonl")

        # Esegui il comando sbatch
        command = f"sbatch experiments/xml_direct_input/run_inference_unified.slurm {model} {output_file}"
        process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()

        # Estrai l'ID del job
        job_id = extract_job_id(stdout.decode('utf-8'))
        if job_id:
            job_ids[model] = job_id
            logger.info(f"Job {job_id} avviato per il modello {model}.")
        else:
            logger.error(f"Errore nell'avvio del job per il modello {model}.")

    # Attendi il completamento dei job se richiesto
    if wait_for_completion and job_ids:
        for model, job_id in job_ids.items():
            success = wait_for_job(job_id)
            if not success:
                logger.warning(f"Job {job_id} per il modello {model} non completato con successo.")

    return job_ids

def evaluate_results(
    results_dir: str,
    metrics_dir: str,
    wait_for_completion: bool = True
) -> None:
    """
    Valuta i risultati dell'inferenza.

    Args:
        results_dir: Directory con i risultati dell'inferenza
        metrics_dir: Directory di output per le metriche
        wait_for_completion: Se True, attende il completamento dei job
    """
    # Crea la directory di output se non esiste
    os.makedirs(metrics_dir, exist_ok=True)

    # Trova tutti i file di risultati
    results_files = []
    for file_name in os.listdir(results_dir):
        if file_name.endswith("_results.jsonl"):
            results_files.append(os.path.join(results_dir, file_name))

    logger.info(f"Trovati {len(results_files)} file di risultati.")

    # Valuta ogni file di risultati
    for results_file in results_files:
        # Estrai il nome del modello
        model_name = os.path.basename(results_file).replace("_results.jsonl", "")

        # Definisci il percorso di output
        output_file = os.path.join(metrics_dir, f"{model_name}_metrics.json")

        # Esegui la valutazione
        command = f"python evaluation/evaluate_captions.py --results_file {results_file} --output_file {output_file}"
        run_command(command, f"Valutazione dei risultati per il modello {model_name}")

def optimize_lora_configs(
    results_dir: str,
    metrics_dir: str,
    output_dir: str,
    train_file: str,
    top_k: int = 3
) -> None:
    """
    Ottimizza le configurazioni LoRA.

    Args:
        results_dir: Directory con i risultati dell'inferenza
        metrics_dir: Directory con le metriche di valutazione
        output_dir: Directory di output per le configurazioni LoRA
        train_file: Percorso al file di training
        top_k: Numero di modelli da selezionare
    """
    command = f"python experiments/xml_direct_input/optimize_lora_config.py --results_dir {results_dir} --metrics_dir {metrics_dir} --output_dir {output_dir} --train_file {train_file} --top_k {top_k}"
    run_command(command, "Ottimizzazione delle configurazioni LoRA")

def run_lora_finetuning(
    config_files: List[str],
    wait_for_completion: bool = True
) -> Dict[str, str]:
    """
    Esegue il fine-tuning LoRA per le configurazioni specificate.

    Args:
        config_files: Lista di file di configurazione
        wait_for_completion: Se True, attende il completamento dei job

    Returns:
        Dizionario con gli ID dei job
    """
    # Esegui il fine-tuning per ogni configurazione
    job_ids = {}
    for config_file in config_files:
        # Esegui il comando sbatch
        command = f"sbatch experiments/xml_direct_input/train_lora_8bit.slurm {config_file}"
        process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()

        # Estrai l'ID del job
        job_id = extract_job_id(stdout.decode('utf-8'))
        if job_id:
            job_ids[config_file] = job_id
            logger.info(f"Job {job_id} avviato per la configurazione {config_file}.")
        else:
            logger.error(f"Errore nell'avvio del job per la configurazione {config_file}.")

    # Attendi il completamento dei job se richiesto
    if wait_for_completion and job_ids:
        for config_file, job_id in job_ids.items():
            success = wait_for_job(job_id)
            if not success:
                logger.warning(f"Job {job_id} per la configurazione {config_file} non completato con successo.")

    return job_ids

def run_lora_inference(
    models: List[str],
    lora_dirs: List[str],
    output_dir: str,
    wait_for_completion: bool = True
) -> Dict[str, str]:
    """
    Esegue l'inferenza con i modelli fine-tuned con LoRA.

    Args:
        models: Lista di modelli
        lora_dirs: Lista di directory con gli adattatori LoRA
        output_dir: Directory di output
        wait_for_completion: Se True, attende il completamento dei job

    Returns:
        Dizionario con gli ID dei job
    """
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)

    # Esegui l'inferenza per ogni modello e adattatore LoRA
    job_ids = {}
    for model, lora_dir in zip(models, lora_dirs):
        # Estrai il nome breve del modello e dell'adattatore
        model_name = model.split("/")[-1].lower().replace("-", "_")
        lora_name = os.path.basename(lora_dir)

        # Definisci il percorso di output
        output_file = os.path.join(output_dir, f"{model_name}_{lora_name}_results.jsonl")

        # Esegui il comando sbatch
        command = f"sbatch experiments/xml_direct_input/run_inference_unified.slurm {model} {output_file} {lora_dir}"
        process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()

        # Estrai l'ID del job
        job_id = extract_job_id(stdout.decode('utf-8'))
        if job_id:
            job_ids[f"{model}_{lora_name}"] = job_id
            logger.info(f"Job {job_id} avviato per il modello {model} con adattatore {lora_name}.")
        else:
            logger.error(f"Errore nell'avvio del job per il modello {model} con adattatore {lora_name}.")

    # Attendi il completamento dei job se richiesto
    if wait_for_completion and job_ids:
        for model_lora, job_id in job_ids.items():
            success = wait_for_job(job_id)
            if not success:
                logger.warning(f"Job {job_id} per {model_lora} non completato con successo.")

    return job_ids

def compare_models(
    metrics_dir: str,
    output_dir: str
) -> None:
    """
    Confronta i modelli.

    Args:
        metrics_dir: Directory con le metriche di valutazione
        output_dir: Directory di output per i confronti
    """
    command = f"python evaluation/compare_models.py --metrics_dir {metrics_dir} --output_dir {output_dir}"
    run_command(command, "Confronto dei modelli")

def run_workflow(args: argparse.Namespace) -> None:
    """
    Esegue il workflow completo.

    Args:
        args: Argomenti della linea di comando
    """
    # Fase 1: Inferenza zero-shot
    if args.run_zero_shot:
        logger.info("=== Fase 1: Inferenza Zero-Shot ===")
        job_ids = run_zero_shot_inference(
            models=args.models,
            output_dir=args.zero_shot_dir,
            wait_for_completion=True
        )

    # Fase 2: Valutazione dei risultati
    if args.run_evaluation:
        logger.info("=== Fase 2: Valutazione dei Risultati ===")
        evaluate_results(
            results_dir=args.zero_shot_dir,
            metrics_dir=args.metrics_dir,
            wait_for_completion=True
        )

    # Fase 3: Ottimizzazione delle configurazioni LoRA
    if args.run_optimization:
        logger.info("=== Fase 3: Ottimizzazione delle Configurazioni LoRA ===")
        optimize_lora_configs(
            results_dir=args.zero_shot_dir,
            metrics_dir=args.metrics_dir,
            output_dir=args.configs_dir,
            train_file=args.train_file,
            top_k=args.top_k
        )

    # Fase 4: Fine-tuning LoRA
    if args.run_finetuning:
        logger.info("=== Fase 4: Fine-tuning LoRA ===")
        # Trova i file di configurazione
        config_files = []
        for file_name in os.listdir(args.configs_dir):
            if file_name.endswith("_lora_config.json"):
                config_files.append(os.path.join(args.configs_dir, file_name))

        logger.info(f"Trovati {len(config_files)} file di configurazione.")

        # Esegui il fine-tuning
        job_ids = run_lora_finetuning(
            config_files=config_files,
            wait_for_completion=True
        )

    # Fase 5: Inferenza con modelli fine-tuned
    if args.run_lora_inference:
        logger.info("=== Fase 5: Inferenza con Modelli Fine-tuned ===")
        # Trova le directory con gli adattatori LoRA
        lora_dirs = []
        models = []

        # Carica le configurazioni
        for file_name in os.listdir(args.configs_dir):
            if file_name.endswith("_lora_config.json"):
                config_path = os.path.join(args.configs_dir, file_name)
                with open(config_path, 'r') as f:
                    config = json.load(f)

                models.append(config.get("model_name_or_path"))
                lora_dirs.append(config.get("output_dir"))

        logger.info(f"Trovati {len(lora_dirs)} adattatori LoRA.")

        # Esegui l'inferenza
        job_ids = run_lora_inference(
            models=models,
            lora_dirs=lora_dirs,
            output_dir=args.lora_results_dir,
            wait_for_completion=True
        )

    # Fase 6: Valutazione dei risultati LoRA
    if args.run_lora_evaluation:
        logger.info("=== Fase 6: Valutazione dei Risultati LoRA ===")
        evaluate_results(
            results_dir=args.lora_results_dir,
            metrics_dir=args.lora_metrics_dir,
            wait_for_completion=True
        )

    # Fase 7: Confronto dei modelli
    if args.run_comparison:
        logger.info("=== Fase 7: Confronto dei Modelli ===")
        # Confronto dei modelli zero-shot
        compare_models(
            metrics_dir=args.metrics_dir,
            output_dir=os.path.join(args.comparisons_dir, "zero_shot")
        )

        # Confronto dei modelli fine-tuned
        compare_models(
            metrics_dir=args.lora_metrics_dir,
            output_dir=os.path.join(args.comparisons_dir, "lora")
        )

        # Confronto tra modelli zero-shot e fine-tuned
        # Crea una directory temporanea con tutte le metriche
        temp_metrics_dir = os.path.join(args.comparisons_dir, "temp_metrics")
        os.makedirs(temp_metrics_dir, exist_ok=True)

        # Copia le metriche
        for file_name in os.listdir(args.metrics_dir):
            if file_name.endswith("_metrics.json"):
                src = os.path.join(args.metrics_dir, file_name)
                dst = os.path.join(temp_metrics_dir, f"zs_{file_name}")
                run_command(f"cp {src} {dst}")

        for file_name in os.listdir(args.lora_metrics_dir):
            if file_name.endswith("_metrics.json"):
                src = os.path.join(args.lora_metrics_dir, file_name)
                dst = os.path.join(temp_metrics_dir, f"lora_{file_name}")
                run_command(f"cp {src} {dst}")

        # Confronto complessivo
        compare_models(
            metrics_dir=temp_metrics_dir,
            output_dir=os.path.join(args.comparisons_dir, "combined")
        )

        # Rimuovi la directory temporanea
        run_command(f"rm -rf {temp_metrics_dir}")

    logger.info("=== Workflow Completato ===")

def main():
    parser = argparse.ArgumentParser(description="Esegue il workflow completo di SVG captioning.")

    # Parametri generali
    parser.add_argument("--models", type=str, nargs="+", default=[
        "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
        "mistralai/Mistral-7B-Instruct-v0.3",
        "meta-llama/Llama-3.1-8B-Instruct",
        "meta-llama/Llama-3.2-3B-Instruct",
        "google/gemma-2-9b-it"
    ], help="Lista di modelli da utilizzare.")

    parser.add_argument("--train_file", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/train_set_final_xml.json", help="Percorso al file di training.")
    parser.add_argument("--test_file", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json", help="Percorso al file di test.")

    # Directory di output
    parser.add_argument("--zero_shot_dir", type=str, default="/work/tesi_ediluzio/results/zero_shot", help="Directory di output per i risultati dell'inferenza zero-shot.")
    parser.add_argument("--metrics_dir", type=str, default="/work/tesi_ediluzio/evaluation/metrics", help="Directory di output per le metriche di valutazione.")
    parser.add_argument("--configs_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/configs", help="Directory di output per le configurazioni LoRA.")
    parser.add_argument("--lora_results_dir", type=str, default="/work/tesi_ediluzio/results/lora", help="Directory di output per i risultati dell'inferenza con modelli fine-tuned.")
    parser.add_argument("--lora_metrics_dir", type=str, default="/work/tesi_ediluzio/evaluation/metrics/lora", help="Directory di output per le metriche di valutazione dei modelli fine-tuned.")
    parser.add_argument("--comparisons_dir", type=str, default="/work/tesi_ediluzio/evaluation/comparisons", help="Directory di output per i confronti.")

    # Parametri di ottimizzazione
    parser.add_argument("--top_k", type=int, default=3, help="Numero di modelli da selezionare per il fine-tuning.")

    # Fasi del workflow
    parser.add_argument("--run_zero_shot", action="store_true", help="Esegui l'inferenza zero-shot.")
    parser.add_argument("--run_evaluation", action="store_true", help="Esegui la valutazione dei risultati.")
    parser.add_argument("--run_optimization", action="store_true", help="Esegui l'ottimizzazione delle configurazioni LoRA.")
    parser.add_argument("--run_finetuning", action="store_true", help="Esegui il fine-tuning LoRA.")
    parser.add_argument("--run_lora_inference", action="store_true", help="Esegui l'inferenza con modelli fine-tuned.")
    parser.add_argument("--run_lora_evaluation", action="store_true", help="Esegui la valutazione dei risultati dei modelli fine-tuned.")
    parser.add_argument("--run_comparison", action="store_true", help="Esegui il confronto dei modelli.")
    parser.add_argument("--run_all", action="store_true", help="Esegui tutte le fasi del workflow.")

    args = parser.parse_args()

    # Se run_all è specificato, esegui tutte le fasi
    if args.run_all:
        args.run_zero_shot = True
        args.run_evaluation = True
        args.run_optimization = True
        args.run_finetuning = True
        args.run_lora_inference = True
        args.run_lora_evaluation = True
        args.run_comparison = True

    # Crea le directory di output se non esistono
    os.makedirs(args.zero_shot_dir, exist_ok=True)
    os.makedirs(args.metrics_dir, exist_ok=True)
    os.makedirs(args.configs_dir, exist_ok=True)
    os.makedirs(args.lora_results_dir, exist_ok=True)
    os.makedirs(args.lora_metrics_dir, exist_ok=True)
    os.makedirs(args.comparisons_dir, exist_ok=True)

    # Esegui il workflow
    run_workflow(args)

if __name__ == "__main__":
    main()
