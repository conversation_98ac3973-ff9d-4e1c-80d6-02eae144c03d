#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import datetime
import subprocess
from pathlib import Path

def parse_args():
    parser = argparse.ArgumentParser(description="Documenta la struttura del filesystem del progetto")
    parser.add_argument("--root_dir", type=str, default="/work/tesi_ediluzio", 
                        help="Directory radice del progetto")
    parser.add_argument("--output_file", type=str, default="/work/tesi_ediluzio/filesystem_structure.md", 
                        help="File di output per la documentazione")
    parser.add_argument("--max_depth", type=int, default=3, 
                        help="Profondità massima della struttura")
    parser.add_argument("--exclude_dirs", type=str, nargs="+", 
                        default=["svg_captioning_env", "__pycache__", ".git", "chunks", "wandb"],
                        help="Directory da escludere")
    parser.add_argument("--exclude_files", type=str, nargs="+", 
                        default=[".gitignore", ".DS_Store"],
                        help="File da escludere")
    parser.add_argument("--include_sizes", action="store_true", 
                        help="Includi le dimensioni dei file e delle directory")
    return parser.parse_args()

def get_dir_size(path):
    """Calcola la dimensione di una directory"""
    try:
        result = subprocess.run(["du", "-sh", path], capture_output=True, text=True, check=True)
        size = result.stdout.strip().split()[0]
        return size
    except:
        return "N/A"

def get_file_size(path):
    """Calcola la dimensione di un file"""
    try:
        size_bytes = os.path.getsize(path)
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    except:
        return "N/A"

def document_directory(path, output_file, max_depth, current_depth=0, exclude_dirs=None, exclude_files=None, include_sizes=False):
    """Documenta la struttura di una directory"""
    if exclude_dirs is None:
        exclude_dirs = []
    
    if exclude_files is None:
        exclude_files = []
    
    # Ottieni il nome della directory
    dir_name = os.path.basename(path)
    
    # Calcola l'indentazione
    indent = "  " * current_depth
    
    # Scrivi il nome della directory
    if current_depth == 0:
        output_file.write(f"# Struttura del Filesystem del Progetto\n\n")
        output_file.write(f"Data: {datetime.datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n\n")
        output_file.write(f"Directory radice: `{path}`\n\n")
        
        if include_sizes:
            dir_size = get_dir_size(path)
            output_file.write(f"Dimensione totale: {dir_size}\n\n")
        
        output_file.write("## Struttura\n\n")
    else:
        if include_sizes:
            dir_size = get_dir_size(path)
            output_file.write(f"{indent}- **{dir_name}/** ({dir_size})\n")
        else:
            output_file.write(f"{indent}- **{dir_name}/**\n")
    
    # Se abbiamo raggiunto la profondità massima, non continuare
    if current_depth >= max_depth:
        return
    
    # Ottieni la lista dei file e delle directory
    try:
        items = sorted(os.listdir(path))
    except:
        output_file.write(f"{indent}  - *Impossibile accedere alla directory*\n")
        return
    
    # Separa file e directory
    dirs = []
    files = []
    
    for item in items:
        item_path = os.path.join(path, item)
        
        if os.path.isdir(item_path):
            if item not in exclude_dirs:
                dirs.append(item)
        else:
            if item not in exclude_files:
                files.append(item)
    
    # Documenta le directory
    for dir_item in dirs:
        dir_path = os.path.join(path, dir_item)
        document_directory(dir_path, output_file, max_depth, current_depth + 1, exclude_dirs, exclude_files, include_sizes)
    
    # Documenta i file
    for file_item in files:
        file_path = os.path.join(path, file_item)
        
        if include_sizes:
            file_size = get_file_size(file_path)
            output_file.write(f"{indent}  - {file_item} ({file_size})\n")
        else:
            output_file.write(f"{indent}  - {file_item}\n")

def main():
    args = parse_args()
    
    # Verifica che la directory radice esista
    if not os.path.exists(args.root_dir):
        print(f"La directory {args.root_dir} non esiste.")
        return
    
    # Crea la directory di output se non esiste
    output_dir = os.path.dirname(args.output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Apri il file di output
    with open(args.output_file, "w") as f:
        # Documenta la struttura del filesystem
        document_directory(args.root_dir, f, args.max_depth, exclude_dirs=args.exclude_dirs, exclude_files=args.exclude_files, include_sizes=args.include_sizes)
    
    print(f"Documentazione della struttura del filesystem completata: {args.output_file}")

if __name__ == "__main__":
    main()
