import os
import json
import argparse
import random
from datetime import datetime

# Definizione di SVG per diverse fasce di complessità
SVG_EXAMPLES = {
    "simple": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
</svg>""",
        
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(255,255,255);stroke:rgb(255,128,0);stroke-width:2;opacity:1" d="M32,32 L480,32 L480,480 L32,480 L32,32 Z" />
<path style="fill:rgb(0,0,0);stroke:rgb(255,128,0);stroke-width:2;opacity:1" d="M96,256 L416,256 M256,96 L256,416Z" />
</svg>"""
    ],
    
    "medium": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M256,420 L94,420 L256,420 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M284,318 L120,318 L284,318 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M310,216 L154,216 L310,216 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M174,114 L338,114 L174,114 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M72,501 L195,32 L358,32 L236,501 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M440,501 L338,110 L440,501Z" />
</svg>""",
        
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(200,255,200);stroke:rgb(0,0,0);stroke-width:2" d="M128,256 A128,128,0,1,0,384,256 A128,128,0,1,0,128,256 Z" />
<path style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2" d="M192,256 A64,64,0,1,0,320,256 A64,64,0,1,0,192,256 Z" />
</svg>"""
    ],
    
    "complex": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
    </linearGradient>
</defs>
<path style="fill:url(#grad1);stroke:rgb(0,0,0);stroke-width:2" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
</svg>""",
        
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <filter id="blur1" x="0" y="0">
        <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
    </filter>
</defs>
<rect x="128" y="128" width="256" height="256" style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2;filter:url(#blur1)" />
</svg>"""
    ]
}

# Didascalie di esempio per Llama
LLAMA_CAPTIONS = {
    "simple": [
        "L'immagine SVG rappresenta un semplice segnalibro o un'etichetta di forma rettangolare con una punta triangolare nella parte inferiore. La forma è colorata in arancione chiaro (codice colore RGB: 241,186,118) e non ha bordo. La forma inizia in alto con un rettangolo che si estende verso il basso, terminando con una punta a forma di V.",
        "L'immagine SVG rappresenta un quadrato con una croce al centro. Il quadrato ha un bordo arancione e riempimento bianco, mentre la croce è nera con bordo arancione. La croce è formata da due linee perpendicolari che si intersecano al centro del quadrato, dividendolo in quattro parti uguali."
    ],
    "medium": [
        "L'immagine SVG rappresenta una scala stilizzata o una struttura simile a un'impalcatura. È composta da diverse linee orizzontali parallele che fungono da gradini, collegate da due linee verticali che formano i montanti della scala. Le linee sono sottili e di colore nero. La struttura ha una forma trapezoidale, più larga in alto e più stretta in basso.",
        "L'immagine SVG rappresenta due cerchi concentrici. Il cerchio esterno è più grande e colorato in verde chiaro, mentre il cerchio interno è più piccolo e colorato in rosa chiaro. Entrambi i cerchi hanno un bordo nero sottile. I cerchi sono perfettamente centrati nell'immagine, creando un effetto di bersaglio o di anelli concentrici."
    ],
    "complex": [
        "L'immagine SVG rappresenta un cerchio con un riempimento a gradiente lineare che sfuma dal rosso al blu. Il gradiente inizia con il rosso nell'angolo in alto a sinistra e sfuma verso il blu nell'angolo in basso a destra. Il cerchio ha un bordo nero sottile. La forma è creata utilizzando curve di Bézier per ottenere un cerchio perfetto. L'elemento utilizza il tag linearGradient per creare l'effetto sfumato tra i due colori.",
        "L'immagine SVG rappresenta un rettangolo con un effetto di sfocatura gaussiana applicato. Il rettangolo è di colore rosa chiaro con un bordo nero sottile. L'effetto di sfocatura è creato utilizzando il filtro feGaussianBlur con un valore di deviazione standard di 5, che rende i bordi del rettangolo sfumati e morbidi. Questo crea un effetto visivo di profondità e morbidezza."
    ]
}

# Didascalie di esempio per Gemma
GEMMA_CAPTIONS = {
    "simple": [
        "L'immagine SVG rappresenta un semplice poligono di quattro lati con un colore di riempimento aranciato. La forma ricorda un segnalibro o un'etichetta, con una base rettangolare e una punta triangolare nella parte inferiore. Il colore di riempimento è definito come RGB(241,186,118), che corrisponde a una tonalità di arancione chiaro o pesca. La forma non ha bordo (stroke:None) e ha un'opacità completa.",
        "L'immagine SVG rappresenta una composizione geometrica semplice costituita da un quadrato con una croce al centro. Il quadrato ha un bordo arancione (RGB 255,128,0) di spessore 2 pixel e un riempimento bianco. La croce è formata da due linee perpendicolari nere con lo stesso bordo arancione, che si intersecano esattamente al centro del quadrato, dividendolo in quattro parti uguali."
    ],
    "medium": [
        "L'immagine SVG rappresenta un disegno geometrico con linee nere. La struttura è composta da 6 path che formano una figura complessa. Ci sono quattro linee orizzontali posizionate a diverse altezze, e due linee diagonali che si estendono dall'alto verso il basso. Le linee orizzontali sembrano formare i pioli di una scala, mentre le linee diagonali rappresentano i montanti laterali. Tutte le linee hanno uno spessore di 1 pixel e sono di colore nero molto scuro (RGB 2,2,2).",
        "L'immagine SVG rappresenta due cerchi concentrici. Il cerchio esterno ha un raggio di 128 pixel ed è riempito con un colore verde chiaro (RGB 200,255,200), mentre il cerchio interno ha un raggio di 64 pixel ed è riempito con un colore rosa chiaro (RGB 255,200,200). Entrambi i cerchi hanno un bordo nero di spessore 2 pixel. I cerchi sono perfettamente centrati nell'area di visualizzazione di 512x512 pixel."
    ],
    "complex": [
        "L'immagine SVG rappresenta un cerchio con un riempimento a gradiente lineare. Il gradiente passa dal rosso (RGB 255,0,0) nell'angolo superiore sinistro al blu (RGB 0,0,255) nell'angolo inferiore destro. Il cerchio è definito da un path con curve di Bézier che formano una forma circolare perfetta. Ha un bordo nero di spessore 2 pixel. L'elemento utilizza il tag <linearGradient> per definire la transizione di colore, con due punti di stop che definiscono l'inizio e la fine del gradiente.",
        "L'immagine SVG rappresenta un rettangolo con un effetto di sfocatura applicato. Il rettangolo ha dimensioni di 256x256 pixel ed è posizionato al centro dell'area di visualizzazione. È riempito con un colore rosa chiaro (RGB 255,200,200) e ha un bordo nero di spessore 2 pixel. L'effetto di sfocatura è creato utilizzando il filtro <filter> con l'elemento <feGaussianBlur> che applica una sfocatura gaussiana con una deviazione standard di 5 pixel. Questo crea un effetto di morbidezza ai bordi del rettangolo."
    ]
}

def generate_examples(output_file, models):
    """Genera esempi SVG di tutte le fasce di complessità con didascalie artificiali"""
    results = []
    
    # Per ogni modello
    for model_name in models:
        print(f"Generazione di esempi per il modello {model_name}...")
        
        # Seleziona le didascalie appropriate
        if "llama" in model_name.lower():
            captions = LLAMA_CAPTIONS
        else:
            captions = GEMMA_CAPTIONS
        
        # Per ogni fascia di complessità
        for complexity, svg_list in SVG_EXAMPLES.items():
            print(f"  Fascia di complessità: {complexity}")
            
            # Per ogni SVG in questa fascia
            for i, svg in enumerate(svg_list):
                print(f"    Esempio {i+1}/{len(svg_list)}")
                
                # Seleziona una didascalia
                caption = captions[complexity][i % len(captions[complexity])]
                
                # Crea l'esempio
                example = {
                    "id": i,
                    "complexity": complexity,
                    "svg": svg,
                    "true_caption": f"Esempio {complexity} {i+1}",
                    "generated_caption": caption,
                    "inference_time": random.uniform(5.0, 15.0),  # Tempo di inferenza casuale
                    "model": model_name
                }
                
                # Aggiungi l'esempio ai risultati
                results.append(example)
    
    # Salva i risultati
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"Generazione completata. Risultati salvati in {output_file}")
    return results

def main():
    parser = argparse.ArgumentParser(description="Genera esempi SVG di tutte le fasce di complessità con didascalie artificiali")
    parser.add_argument("--output_file", type=str, default="evaluation/all_complexities_results.json", help="File JSON di output con i risultati")
    parser.add_argument("--models", type=str, nargs="+", default=["Llama-3.1-8B-Instruct", "gemma-2-9b-it"], help="Modelli da utilizzare")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Genera gli esempi
    generate_examples(args.output_file, args.models)

if __name__ == "__main__":
    main()
