#!/usr/bin/env python3
import os
import subprocess
import time
import argparse
import datetime

def check_job_status(job_id):
    """
    Controlla lo stato di un job SLURM.
    Restituisce True se il job è in esecuzione, False altrimenti.
    """
    try:
        result = subprocess.run(
            ["squeue", "-j", str(job_id), "-h"],
            capture_output=True,
            text=True,
            check=False
        )
        return bool(result.stdout.strip())
    except Exception as e:
        print(f"Errore nel controllo dello stato del job {job_id}: {e}")
        return False

def submit_job(slurm_script):
    """
    Invia un job SLURM.
    Restituisce l'ID del job se l'invio ha successo, None altrimenti.
    """
    try:
        result = subprocess.run(
            ["sbatch", slurm_script],
            capture_output=True,
            text=True,
            check=True
        )
        # Estrai l'ID del job dalla risposta
        job_id = result.stdout.strip().split()[-1]
        return job_id
    except Exception as e:
        print(f"Errore nell'invio del job {slurm_script}: {e}")
        return None

def monitor_and_restart_jobs(job_configs, check_interval=300):
    """
    Monitora i job specificati e li riavvia se necessario.
    
    Args:
        job_configs: Lista di dizionari con le configurazioni dei job
        check_interval: Intervallo di controllo in secondi
    """
    while True:
        print(f"\n=== Controllo job: {datetime.datetime.now()} ===")
        
        for config in job_configs:
            job_id = config["job_id"]
            job_name = config["job_name"]
            restart_script = config["restart_script"]
            
            # Controlla se il job è in esecuzione
            is_running = check_job_status(job_id)
            
            if is_running:
                print(f"Job {job_name} (ID: {job_id}) è in esecuzione.")
            else:
                print(f"Job {job_name} (ID: {job_id}) non è in esecuzione. Riavvio...")
                new_job_id = submit_job(restart_script)
                
                if new_job_id:
                    print(f"Job {job_name} riavviato con ID: {new_job_id}")
                    # Aggiorna l'ID del job nella configurazione
                    config["job_id"] = new_job_id
                else:
                    print(f"Impossibile riavviare il job {job_name}.")
        
        # Attendi prima del prossimo controllo
        print(f"Prossimo controllo tra {check_interval} secondi.")
        time.sleep(check_interval)

def main():
    parser = argparse.ArgumentParser(description="Monitora e riavvia i job SLURM")
    parser.add_argument("--llama_job_id", type=str, required=True, help="ID del job Llama")
    parser.add_argument("--gemma_job_id", type=str, required=True, help="ID del job Gemma")
    parser.add_argument("--check_interval", type=int, default=300, help="Intervallo di controllo in secondi")
    
    args = parser.parse_args()
    
    # Configura i job da monitorare
    job_configs = [
        {
            "job_id": args.llama_job_id,
            "job_name": "llama_convergence",
            "restart_script": "/work/tesi_ediluzio/restart_llama_convergence.slurm"
        },
        {
            "job_id": args.gemma_job_id,
            "job_name": "gemma_convergence",
            "restart_script": "/work/tesi_ediluzio/restart_gemma_convergence.slurm"
        }
    ]
    
    # Avvia il monitoraggio
    try:
        monitor_and_restart_jobs(job_configs, args.check_interval)
    except KeyboardInterrupt:
        print("\nMonitoraggio interrotto dall'utente.")
    except Exception as e:
        print(f"\nErrore durante il monitoraggio: {e}")

if __name__ == "__main__":
    main()
