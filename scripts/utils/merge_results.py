import json
import argparse

def merge_results(input_files, output_file):
    """Unisce i risultati di più file JSON in un unico file"""
    all_results = []
    
    # Carica i risultati da ogni file
    for file_path in input_files:
        try:
            with open(file_path, "r") as f:
                results = json.load(f)
                all_results.extend(results)
        except FileNotFoundError:
            print(f"File non trovato: {file_path}")
        except json.JSONDecodeError:
            print(f"Errore nella decodifica del file JSON: {file_path}")
    
    # Salva i risultati uniti
    with open(output_file, "w") as f:
        json.dump(all_results, f, indent=2)
    
    print(f"Risultati uniti salvati in {output_file}")
    return all_results

def main():
    parser = argparse.ArgumentParser(description="Unisce i risultati di più file JSON in un unico file")
    parser.add_argument("--input_files", type=str, nargs="+", required=True, help="File JSON di input con i risultati")
    parser.add_argument("--output_file", type=str, required=True, help="File JSON di output con i risultati uniti")
    
    args = parser.parse_args()
    
    # Unisci i risultati
    merge_results(args.input_files, args.output_file)

if __name__ == "__main__":
    main()
