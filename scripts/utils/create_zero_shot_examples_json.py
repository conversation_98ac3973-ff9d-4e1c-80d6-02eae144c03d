#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per creare un file JSON combinato degli esempi zero-shot.
"""

import os
import json

def create_combined_examples(output_dir):
    """Crea un file combinato degli esempi zero-shot."""
    try:
        # Cerca tutti i file di esempi
        examples_files = [f for f in os.listdir(output_dir) if f.endswith('_zero_shot_examples.json') and not f == 'zero_shot_examples.json']
        
        # Carica gli esempi
        all_examples = []
        for file in examples_files:
            model_key = file.split('_zero_shot')[0]
            file_path = os.path.join(output_dir, file)
            
            with open(file_path, 'r') as f:
                examples = json.load(f)
                
                # Aggiungi il nome del modello a ogni esempio
                for example in examples:
                    example['model'] = model_key
                
                # Aggiungi gli esempi alla lista completa
                all_examples.extend(examples)
        
        # Salva il file combinato
        combined_file = os.path.join(output_dir, 'zero_shot_examples.json')
        with open(combined_file, 'w') as f:
            json.dump(all_examples, f, indent=2)
        
        print(f"File combinato {combined_file} creato con successo.")
    except Exception as e:
        print(f"Errore nella creazione del file combinato: {e}")

if __name__ == "__main__":
    output_dir = "/work/tesi_ediluzio/evaluation/zero_shot"
    
    # Crea il file combinato
    create_combined_examples(output_dir)
