#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json

# Stampa informazioni di sistema
print(f"Python version: {sys.version}")
print(f"Working directory: {os.getcwd()}")

# Stampa variabili d'ambiente
print(f"HF_HOME: {os.environ.get('HF_HOME', 'Not set')}")
print(f"HF_TOKEN: {os.environ.get('HF_TOKEN', 'Not set')[:5]}...")
print(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")

# Verifica che possiamo scrivere nella directory di output
output_file = "/work/tesi_ediluzio/results/lora_xml/test_env.jsonl"
print(f"Tentativo di scrittura in: {output_file}")

try:
    # Crea la directory se non esiste
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Dati di esempio
    data = [
        {"id": 1, "text": "Test 1"},
        {"id": 2, "text": "Test 2"}
    ]
    
    # Scrivi i dati nel file
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item) + '\n')
    
    print(f"Dati scritti con successo in: {output_file}")
    
    # Verifica che il file esista
    if os.path.exists(output_file):
        print(f"Il file {output_file} esiste.")
        # Leggi il contenuto del file
        with open(output_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"Contenuto del file: {content}")
    else:
        print(f"Il file {output_file} non esiste.")
except Exception as e:
    print(f"Errore: {e}")
    import traceback
    traceback.print_exc()
