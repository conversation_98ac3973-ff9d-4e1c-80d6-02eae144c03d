import os
import json
import time
import argparse
import torch
from transformers import pipeline

# SVG di esempio semplice
SIMPLE_SVG = """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
</svg>"""

def main():
    parser = argparse.ArgumentParser(description="Esegue l'inferenza su un modello molto piccolo")
    parser.add_argument("--output_file", type=str, required=True, help="File JSON di output con i risultati dell'inferenza")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Pulisci la memoria
    torch.cuda.empty_cache()
    
    try:
        print("Caricamento del modello...")
        
        # Usa un modello molto piccolo
        generator = pipeline('text-generation', model='distilgpt2')
        print("Modello caricato con successo")
        
        # Crea il prompt
        prompt = f"Descrivi questa immagine SVG:\n{SIMPLE_SVG}\n\n"
        print("Prompt creato")
        
        # Genera la didascalia
        print("Generazione della didascalia...")
        start_time = time.time()
        result = generator(prompt, max_length=150, num_return_sequences=1)
        end_time = time.time()
        print("Didascalia generata")
        
        # Estrai la didascalia
        generated_text = result[0]['generated_text']
        
        # Rimuovi il prompt dalla risposta
        generated_caption = generated_text[len(prompt):].strip()
        
        # Calcola il tempo di inferenza
        inference_time = end_time - start_time
        
        # Crea l'esempio
        example = {
            "id": 0,
            "complexity": "simple",
            "svg": SIMPLE_SVG,
            "true_caption": "Esempio semplice",
            "generated_caption": generated_caption,
            "inference_time": inference_time,
            "model": "distilgpt2"
        }
        
        # Salva i risultati
        with open(args.output_file, "w") as f:
            json.dump([example], f, indent=2)
        
        print(f"Inferenza completata in {inference_time:.2f}s")
        print(f"Risultati salvati in {args.output_file}")
        
    except Exception as e:
        print(f"Errore: {e}")
        
        # Salva un risultato vuoto
        example = {
            "id": 0,
            "complexity": "simple",
            "svg": SIMPLE_SVG,
            "true_caption": "Esempio semplice",
            "generated_caption": f"Errore: {str(e)}",
            "inference_time": 0.0,
            "model": "distilgpt2"
        }
        
        with open(args.output_file, "w") as f:
            json.dump([example], f, indent=2)

if __name__ == "__main__":
    main()
