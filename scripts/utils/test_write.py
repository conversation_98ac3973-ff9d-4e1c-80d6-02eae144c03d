#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json

# Percorso del file di output
output_file = "/work/tesi_ediluzio/results/lora_xml/test_output.jsonl"

# Dati di esempio
data = [
    {"id": 1, "text": "Test 1"},
    {"id": 2, "text": "Test 2"}
]

# Crea la directory se non esiste
os.makedirs(os.path.dirname(output_file), exist_ok=True)

# Scrivi i dati nel file
print(f"Scrittura dati in: {output_file}")
try:
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item) + '\n')
    print(f"Dati scritti con successo in: {output_file}")
except Exception as e:
    print(f"Errore nella scrittura dei dati: {e}")

# Verifica che il file esista
if os.path.exists(output_file):
    print(f"Il file {output_file} esiste.")
    # Leggi il contenuto del file
    with open(output_file, 'r', encoding='utf-8') as f:
        content = f.read()
    print(f"Contenuto del file: {content}")
else:
    print(f"Il file {output_file} non esiste.")
