import json
import sys
from pathlib import Path

def find_max_svg_length(file_path):
    max_length = 0
    max_svg_id = None
    
    print(f"Analisi del file: {file_path}")
    
    with open(file_path, 'r') as f:
        try:
            data = json.load(f)
            for item in data:
                svg_length = len(item.get('xml', ''))
                if svg_length > max_length:
                    max_length = svg_length
                    max_svg_id = item.get('id', 'unknown')
        except json.JSONDecodeError as e:
            print(f"Errore nel parsing del file: {e}")
            return 0, None
    
    return max_length, max_svg_id

def main():
    # Percorsi dei file da analizzare
    files = [
        'data/processed/train_set_final.json',
        'data/processed/test_set_final_2k.json',
        'data/processed/filtered_bw_len512_ALL.json'
    ]
    
    overall_max = 0
    overall_max_id = None
    overall_max_file = None
    
    for file_path in files:
        if Path(file_path).exists():
            max_len, max_id = find_max_svg_length(file_path)
            print(f"\nFile: {file_path}")
            print(f"Lunghezza massima SVG: {max_len} caratteri")
            print(f"ID dell'SVG più lungo: {max_id}")
            
            if max_len > overall_max:
                overall_max = max_len
                overall_max_id = max_id
                overall_max_file = file_path
    
    print("\nRiepilogo:")
    print(f"Lunghezza massima assoluta: {overall_max} caratteri")
    print(f"ID dell'SVG più lungo: {overall_max_id}")
    print(f"File contenente l'SVG più lungo: {overall_max_file}")

if __name__ == "__main__":
    main() 