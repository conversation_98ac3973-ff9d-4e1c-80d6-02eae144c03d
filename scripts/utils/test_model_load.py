#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import PeftModel

# Parametri
model_name = "meta-llama/Llama-3.1-8B-Instruct"
lora_path = "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_quick_fixed3"
load_in_8bit = True

# Configura il logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Stampa informazioni di sistema
print(f"Python version: {sys.version}")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA version: {torch.version.cuda}")
    print(f"GPU count: {torch.cuda.device_count()}")
    print(f"GPU name: {torch.cuda.get_device_name(0)}")

# Stampa variabili d'ambiente
print(f"HF_HOME: {os.environ.get('HF_HOME', 'Not set')}")
print(f"HF_TOKEN: {os.environ.get('HF_TOKEN', 'Not set')[:5]}...")
print(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")

try:
    # Configurazione per la quantizzazione
    print("Configurazione quantizzazione...")
    quantization_config = None
    if load_in_8bit:
        quantization_config = BitsAndBytesConfig(
            load_in_8bit=True,
            bnb_4bit_compute_dtype=torch.float16
        )
    
    # Carica il tokenizer
    print(f"Caricamento tokenizer: {model_name}")
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True
    )
    print(f"Tokenizer caricato. Vocabolario: {len(tokenizer)} token")
    
    # Carica il modello base
    print(f"Caricamento modello base: {model_name}")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=quantization_config,
        device_map="auto",
        trust_remote_code=True
    )
    print("Modello base caricato con successo.")
    
    # Carica gli adattatori LoRA
    if lora_path:
        print(f"Caricamento adattatori LoRA: {lora_path}")
        model = PeftModel.from_pretrained(model, lora_path)
        print("Adattatori LoRA caricati con successo.")
    
    # Test di generazione
    print("Test di generazione...")
    input_text = "Ciao, come stai?"
    input_ids = tokenizer(input_text, return_tensors="pt").input_ids.to(model.device)
    
    with torch.no_grad():
        output = model.generate(input_ids, max_new_tokens=20)
    
    output_text = tokenizer.decode(output[0], skip_special_tokens=True)
    print(f"Input: {input_text}")
    print(f"Output: {output_text}")
    
    print("Test completato con successo!")
except Exception as e:
    print(f"Errore: {e}")
    import traceback
    traceback.print_exc()
