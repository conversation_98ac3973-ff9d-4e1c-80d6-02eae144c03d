#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per contare i parametri trainabili e totali di un modello LoRA.
Questo script consente di calcolare e visualizzare il numero di parametri trainabili,
il numero totale di parametri e la percentuale di parametri trainabili.
"""

import os
import sys
import argparse
import logging
import torch
from typing import Dict, Any, Tuple

from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Conta i parametri trainabili di un modello LoRA")
    parser.add_argument("--model_name_or_path", type=str, required=True, help="Path o nome del modello pre-addestrato")
    parser.add_argument("--lora_r", type=int, default=16, help="Rango LoRA")
    parser.add_argument("--lora_alpha", type=int, default=32, help="Alpha LoRA")
    parser.add_argument("--lora_target_modules", type=str, nargs="+", default=["q_proj", "v_proj", "k_proj", "o_proj"], 
                        help="Moduli target per LoRA")
    parser.add_argument("--load_in_4bit", action="store_true", help="Carica il modello in 4-bit")
    parser.add_argument("--load_in_8bit", action="store_true", help="Carica il modello in 8-bit")
    parser.add_argument("--output_file", type=str, default=None, help="File in cui salvare i risultati (opzionale)")
    return parser.parse_args()

def count_parameters(model) -> Dict[str, Any]:
    """
    Conta i parametri trainabili e totali di un modello.
    
    Args:
        model: Il modello da analizzare
        
    Returns:
        Un dizionario con informazioni sui parametri
    """
    trainable_params = 0
    all_param = 0
    
    for _, param in model.named_parameters():
        all_param += param.numel()
        if param.requires_grad:
            trainable_params += param.numel()
            
    trainable_percentage = 100 * trainable_params / all_param if all_param > 0 else 0
    
    # Formatta i numeri per una migliore leggibilità
    trainable_params_readable = f"{trainable_params:,}".replace(",", ".")
    all_params_readable = f"{all_param:,}".replace(",", ".")
    
    return {
        "trainable_params": trainable_params,
        "trainable_params_readable": trainable_params_readable,
        "all_params": all_param,
        "all_params_readable": all_params_readable,
        "trainable_percentage": trainable_percentage
    }

def load_model_and_count_parameters(args) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """
    Carica un modello, applica LoRA e conta i parametri.
    
    Args:
        args: Argomenti da linea di comando
        
    Returns:
        Una tupla contenente i dizionari di parametri prima e dopo l'applicazione di LoRA
    """
    # Imposta il token HF
    os.environ["HF_TOKEN"] = "*************************************"
    
    # Configura la quantizzazione
    quantization_config = None
    if args.load_in_4bit:
        logger.info("Caricamento del modello in 4-bit...")
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.bfloat16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
    elif args.load_in_8bit:
        logger.info("Caricamento del modello in 8-bit...")
        quantization_config = BitsAndBytesConfig(
            load_in_8bit=True
        )
    
    # Carica il modello
    logger.info(f"Caricamento del modello: {args.model_name_or_path}")
    device_map = "auto"
    
    model = AutoModelForCausalLM.from_pretrained(
        args.model_name_or_path,
        quantization_config=quantization_config,
        device_map=device_map,
        token=os.environ.get("HF_TOKEN"),
        trust_remote_code=True
    )
    
    # Conta i parametri prima di applicare LoRA
    logger.info("Conteggio dei parametri del modello base...")
    base_params = count_parameters(model)
    
    # Prepara il modello per il training se è quantizzato
    if args.load_in_4bit or args.load_in_8bit:
        logger.info("Preparazione del modello per il training con quantizzazione...")
        model = prepare_model_for_kbit_training(model)
        
        # Configura LoRA
    logger.info(f"Applicazione di LoRA con r={args.lora_r}, alpha={args.lora_alpha}, target_modules={args.lora_target_modules}")
        lora_config = LoraConfig(
            r=args.lora_r,
            lora_alpha=args.lora_alpha,
            target_modules=args.lora_target_modules,
        lora_dropout=0.05,
            bias="none",
        task_type="CAUSAL_LM"
        )
        
    # Applica LoRA
    model = get_peft_model(model, lora_config)
        
    # Conta i parametri dopo l'applicazione di LoRA
    logger.info("Conteggio dei parametri del modello con LoRA...")
    lora_params = count_parameters(model)
    
    return base_params, lora_params

def main():
    args = parse_args()
    
    try:
        # Carica il modello e conta i parametri
        base_params, lora_params = load_model_and_count_parameters(args)
        
        # Stampa i risultati
        print("\n" + "="*80)
        print(f"ANALISI PARAMETRI DEL MODELLO: {args.model_name_or_path}")
        print("="*80)
        print("\nPARAMETRI DEL MODELLO BASE:")
        print(f"Parametri totali: {base_params['all_params_readable']} ({base_params['all_params']})")
        print(f"Parametri trainabili: {base_params['trainable_params_readable']} ({base_params['trainable_params']})")
        print(f"Percentuale trainabile: {base_params['trainable_percentage']:.6f}%")
        
        print("\nPARAMETRI DEL MODELLO CON LORA:")
        print(f"Parametri totali: {lora_params['all_params_readable']} ({lora_params['all_params']})")
        print(f"Parametri trainabili: {lora_params['trainable_params_readable']} ({lora_params['trainable_params']})")
        print(f"Percentuale trainabile: {lora_params['trainable_percentage']:.6f}%")
    
        # Calcola la riduzione percentuale dei parametri trainabili rispetto al totale
        reduction = 100 * (1 - (lora_params['trainable_params'] / base_params['all_params']))
        print(f"\nRiduzione percentuale dei parametri trainabili: {reduction:.2f}%")
        print("="*80 + "\n")
        
        # Salva i risultati in un file se specificato
    if args.output_file:
        import json
            with open(args.output_file, 'w') as f:
                json.dump({
                    "model_name": args.model_name_or_path,
                    "lora_config": {
                "r": args.lora_r,
                "alpha": args.lora_alpha,
                        "target_modules": args.lora_target_modules
                    },
                    "base_params": base_params,
                    "lora_params": lora_params,
                    "reduction_percentage": reduction
                }, f, indent=2)
            logger.info(f"Risultati salvati in {args.output_file}")
        
    except Exception as e:
        logger.error(f"Errore: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()
