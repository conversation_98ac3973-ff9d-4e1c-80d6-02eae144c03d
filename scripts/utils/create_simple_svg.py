import os

# Directory di output
output_dir = "/work/tesi_ediluzio/evaluation/reports/svg_images_fixed"
os.makedirs(output_dir, exist_ok=True)

# Esempi selezionati
examples = {
    "simple_example_1": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <circle cx="256" cy="256" r="154" fill="#6281C0" />
    </svg>
    """,
    
    "simple_example_2": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <rect x="462" y="0" width="50" height="512" fill="#008000" />
    </svg>
    """,
    
    "simple_example_3": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <polygon points="256,0 5,250 151,250 151,510 359,510 359,250 505,250" fill="#000000" />
    </svg>
    """,
    
    "medium_example_1": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <path d="M268,181 C265,177,261,176,256,176 C252,176,247,177,244,181 L101,324 C95,330,95,341,101,347 C107,353,118,353,124,347 L256,214 L389,347 C395,353,405,353,411,347 C418,341,418,330,411,324 L268,181Z" fill="#000000" />
    </svg>
    """,
    
    "medium_example_2": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <path d="M440,202 C438,198,434,196,430,196 L256,196 L256,11 C256,6,252,2,248,0 C243,-1,238,1,235,6 L72,311 C70,314,70,318,72,321 C74,325,78,327,82,327 L234,327 L234,501 C234,506,237,510,242,511 C243,512,244,512,245,512 C249,512,252,510,254,507 L439,213 C441,209,442,205,440,202 M256,463 L256,316 C256,310,251,305,245,305 L100,305 L234,54 L234,207 C234,213,239,218,245,218 L410,218 L256,463Z" fill="#000000" />
    </svg>
    """,
    
    "medium_example_3": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <rect x="0" y="0" width="512" height="512" fill="#FFDA44" />
        <rect x="0" y="0" width="147" height="512" fill="#0052B4" />
        <path d="M256,412 L223,337 L256,262 L289,337 L256,412 M298,312 L264,237 L298,162 L331,237 L298,312 M214,312 L181,237 L214,162 L248,237 L214,312 M365,0 L512,0 L512,512 L365,512 L365,0Z" fill="#6DA544" />
    </svg>
    """,
    
    "complex_example_1": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <path d="M128,256 C128,185,184,128,253,128 C299,128,340,154,362,192 L320,192 L320,224 L416,224 L416,128 L384,128 L384,167 C356,124,308,96,253,96 C166,96,96,168,96,256 L128,256 Z" fill="#080341" />
        <path d="M384,256 C384,327,328,384,259,384 C213,384,172,358,150,320 L192,320 L192,288 L96,288 L96,384 L128,384 L128,345 C156,388,204,416,259,416 C346,416,416,344,416,256 L384,256Z" fill="#080341" />
    </svg>
    """,
    
    "complex_example_2": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <circle cx="256" cy="256" r="240" fill="#FFFFFF" />
        <path d="M256,16 C123,16,16,123,16,256 C16,389,123,496,256,496 C389,496,496,389,496,256 C496,123,389,16,256,16 M368,372 L323,372 L323,215 C323,211,323,204,323,196 C323,188,323,182,323,177 L279,372 L232,372 L189,177 C189,182,189,188,189,196 C189,204,189,211,189,215 L189,372 L144,372 L144,140 L214,140 L257,322 L298,140 L368,140 L368,372Z" fill="#8E24AA" />
    </svg>
    """,
    
    "complex_example_3": """
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
        <path d="M256,256 L256,512" stroke="#7FDFFF" stroke-width="100" fill="none" />
        <path d="M128,512 C128,384,51,384,51,256 A205,205,0,0,1,461,256 C461,384,384,384,384,512" stroke="#80A080" stroke-width="50" fill="none" />
        <path d="M512,128 L256,128 A128,128,0,0,0,256,384 L512,384" stroke="#AAAAAA" stroke-width="50" fill="#FFFFFF" />
    </svg>
    """
}

# Salva gli SVG corretti
for name, svg_content in examples.items():
    file_path = os.path.join(output_dir, f"{name}.svg")
    with open(file_path, "w") as f:
        f.write(svg_content.strip())
    print(f"Creato: {file_path}")

# Aggiorna il report principale per utilizzare i nuovi SVG
report_path = "/work/tesi_ediluzio/evaluation/reports/llama_fine_tuned_report.html"
with open(report_path, "r") as f:
    report_content = f.read()

# Sostituisci i percorsi degli SVG
for name in examples.keys():
    old_path = f'src="/work/tesi_ediluzio/evaluation/reports/svg_images/{name}.svg"'
    new_path = f'src="/work/tesi_ediluzio/evaluation/reports/svg_images_fixed/{name}.svg"'
    report_content = report_content.replace(old_path, new_path)

# Salva il report aggiornato
with open(report_path, "w") as f:
    f.write(report_content)
print(f"Aggiornato report: {report_path}")
