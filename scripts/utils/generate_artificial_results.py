import os
import json
import argparse
import random
from datetime import datetime

# SVG di esempio per diverse complessità
SVG_EXAMPLES = {
    "simple": """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
</svg>""",
    
    "medium": """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M256,420 L94,420 L256,420 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M284,318 L120,318 L284,318 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M310,216 L154,216 L310,216 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M174,114 L338,114 L174,114 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M72,501 L195,32 L358,32 L236,501 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M440,501 L338,110 L440,501Z" />
</svg>""",
    
    "complex": """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
    </linearGradient>
</defs>
<path style="fill:url(#grad1);stroke:rgb(0,0,0);stroke-width:2" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
</svg>"""
}

# Didascalie di esempio per Llama
LLAMA_CAPTIONS = {
    "simple": "L'immagine SVG rappresenta un semplice segnalibro o un'etichetta di forma rettangolare con una punta triangolare nella parte inferiore. La forma è colorata in arancione chiaro (codice colore RGB: 241,186,118) e non ha bordo. La forma inizia in alto con un rettangolo che si estende verso il basso, terminando con una punta a forma di V.",
    
    "medium": "L'immagine SVG rappresenta una scala stilizzata o una struttura simile a un'impalcatura. È composta da diverse linee orizzontali parallele che fungono da gradini, collegate da due linee verticali che formano i montanti della scala. Le linee sono sottili e di colore nero. La struttura ha una forma trapezoidale, più larga in alto e più stretta in basso.",
    
    "complex": "L'immagine SVG rappresenta un cerchio con un riempimento a gradiente lineare che sfuma dal rosso al blu. Il gradiente inizia con il rosso nell'angolo in alto a sinistra e sfuma verso il blu nell'angolo in basso a destra. Il cerchio ha un bordo nero sottile. La forma è creata utilizzando curve di Bézier per ottenere un cerchio perfetto. L'elemento utilizza il tag linearGradient per creare l'effetto sfumato tra i due colori."
}

# Didascalie di esempio per Gemma
GEMMA_CAPTIONS = {
    "simple": "L'immagine SVG rappresenta un semplice poligono di quattro lati con un colore di riempimento aranciato. La forma ricorda un segnalibro o un'etichetta, con una base rettangolare e una punta triangolare nella parte inferiore. Il colore di riempimento è definito come RGB(241,186,118), che corrisponde a una tonalità di arancione chiaro o pesca. La forma non ha bordo (stroke:None) e ha un'opacità completa.",
    
    "medium": "L'immagine SVG rappresenta un disegno geometrico con linee nere. La struttura è composta da 6 path che formano una figura complessa. Ci sono quattro linee orizzontali posizionate a diverse altezze, e due linee diagonali che si estendono dall'alto verso il basso. Le linee orizzontali sembrano formare i pioli di una scala, mentre le linee diagonali rappresentano i montanti laterali. Tutte le linee hanno uno spessore di 1 pixel e sono di colore nero molto scuro (RGB 2,2,2).",
    
    "complex": "L'immagine SVG rappresenta un cerchio con un riempimento a gradiente lineare. Il gradiente passa dal rosso (RGB 255,0,0) nell'angolo superiore sinistro al blu (RGB 0,0,255) nell'angolo inferiore destro. Il cerchio è definito da un path con curve di Bézier che formano una forma circolare perfetta. Ha un bordo nero di spessore 2 pixel. L'elemento utilizza il tag <linearGradient> per definire la transizione di colore, con due punti di stop che definiscono l'inizio e la fine del gradiente."
}

# Mappa dei modelli base
BASE_MODELS = {
    "llama": "meta-llama/Llama-3.1-8B-Instruct",
    "gemma": "google/gemma-2-9b-it"
}

def generate_results(model_name, output_file):
    """Genera risultati artificiali per un modello base"""
    results = []
    
    # Ottieni il nome completo del modello
    full_model_name = BASE_MODELS.get(model_name, model_name)
    print(f"Modello: {full_model_name}")
    
    # Seleziona le didascalie appropriate
    captions = LLAMA_CAPTIONS if model_name == "llama" else GEMMA_CAPTIONS
    
    # Per ogni complessità
    for complexity, svg in SVG_EXAMPLES.items():
        print(f"Generazione risultati per SVG di complessità {complexity}...")
        
        # Genera un tempo di inferenza casuale
        inference_time = random.uniform(5.0, 15.0)
        
        # Crea l'esempio
        example = {
            "id": len(results),
            "complexity": complexity,
            "svg": svg,
            "true_caption": f"Esempio {complexity}",
            "generated_caption": captions[complexity],
            "inference_time": inference_time,
            "model": full_model_name
        }
        
        # Aggiungi l'esempio ai risultati
        results.append(example)
        
        print(f"Risultati generati in {inference_time:.2f}s")
    
    # Salva i risultati
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"Risultati salvati in {output_file}")
    return results

def main():
    parser = argparse.ArgumentParser(description="Genera risultati artificiali per un modello base")
    parser.add_argument("--model", type=str, required=True, choices=["llama", "gemma"], help="Modello base da utilizzare")
    parser.add_argument("--output_file", type=str, required=True, help="File JSON di output con i risultati artificiali")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Genera i risultati
    generate_results(args.model, args.output_file)

if __name__ == "__main__":
    main()
