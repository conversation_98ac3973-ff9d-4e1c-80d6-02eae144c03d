#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per analizzare la distribuzione delle lunghezze degli SVG nel dataset.
"""

import os
import sys
import json
import argparse
import logging
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Analisi delle lunghezze degli SVG nel dataset")
    parser.add_argument("--data_file", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/train_set_final_xml.json",
                        help="Path al file di dati in formato JSON")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/analysis",
                        help="Directory di output per i risultati")
    parser.add_argument("--llama_tokenizer", type=str, default="meta-llama/Llama-3.1-8B-Instruct",
                        help="Nome o path del tokenizer Llama")
    parser.add_argument("--gemma_tokenizer", type=str, default="google/gemma-2-9b-it",
                        help="Nome o path del tokenizer Gemma")
    parser.add_argument("--llama_max_length", type=int, default=1024,
                        help="Lunghezza massima del contesto per Llama")
    parser.add_argument("--gemma_max_length", type=int, default=768,
                        help="Lunghezza massima del contesto per Gemma")
    return parser.parse_args()

def load_data(data_file):
    """Carica i dati dal file JSON."""
    logger.info(f"Caricamento dei dati da {data_file}")
    
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"Caricati {len(data)} esempi")
    return data

def analyze_svg_lengths(data, llama_tokenizer, gemma_tokenizer, llama_max_length, gemma_max_length):
    """Analizza la distribuzione delle lunghezze degli SVG nel dataset."""
    logger.info("Analisi delle lunghezze degli SVG...")
    
    # Inizializza le liste per le lunghezze
    char_lengths = []
    llama_token_lengths = []
    gemma_token_lengths = []
    
    # Conta gli SVG che superano la lunghezza massima
    llama_over_max = 0
    gemma_over_max = 0
    
    # Analizza ogni esempio
    for i, item in enumerate(data):
        # Ottieni i dati SVG
        svg = item.get('xml', '')
        
        if not svg:
            logger.warning(f"Dati SVG non trovati per l'esempio {i}")
            continue
        
        # Calcola la lunghezza in caratteri
        char_length = len(svg)
        char_lengths.append(char_length)
        
        # Calcola la lunghezza in token per Llama
        llama_tokens = llama_tokenizer.encode(svg)
        llama_token_length = len(llama_tokens)
        llama_token_lengths.append(llama_token_length)
        
        # Calcola la lunghezza in token per Gemma
        gemma_tokens = gemma_tokenizer.encode(svg)
        gemma_token_length = len(gemma_tokens)
        gemma_token_lengths.append(gemma_token_length)
        
        # Verifica se supera la lunghezza massima
        if llama_token_length > llama_max_length:
            llama_over_max += 1
        
        if gemma_token_length > gemma_max_length:
            gemma_over_max += 1
        
        # Log ogni 100 esempi
        if (i + 1) % 100 == 0:
            logger.info(f"Analizzati {i + 1}/{len(data)} esempi")
    
    # Calcola le statistiche
    stats = {
        'char': {
            'min': min(char_lengths),
            'max': max(char_lengths),
            'mean': np.mean(char_lengths),
            'median': np.median(char_lengths),
            'p90': np.percentile(char_lengths, 90),
            'p95': np.percentile(char_lengths, 95),
            'p99': np.percentile(char_lengths, 99)
        },
        'llama_tokens': {
            'min': min(llama_token_lengths),
            'max': max(llama_token_lengths),
            'mean': np.mean(llama_token_lengths),
            'median': np.median(llama_token_lengths),
            'p90': np.percentile(llama_token_lengths, 90),
            'p95': np.percentile(llama_token_lengths, 95),
            'p99': np.percentile(llama_token_lengths, 99),
            'over_max': llama_over_max,
            'over_max_percent': 100 * llama_over_max / len(data)
        },
        'gemma_tokens': {
            'min': min(gemma_token_lengths),
            'max': max(gemma_token_lengths),
            'mean': np.mean(gemma_token_lengths),
            'median': np.median(gemma_token_lengths),
            'p90': np.percentile(gemma_token_lengths, 90),
            'p95': np.percentile(gemma_token_lengths, 95),
            'p99': np.percentile(gemma_token_lengths, 99),
            'over_max': gemma_over_max,
            'over_max_percent': 100 * gemma_over_max / len(data)
        }
    }
    
    return {
        'stats': stats,
        'char_lengths': char_lengths,
        'llama_token_lengths': llama_token_lengths,
        'gemma_token_lengths': gemma_token_lengths
    }

def plot_distributions(results, output_dir):
    """Crea grafici per le distribuzioni delle lunghezze."""
    logger.info("Creazione dei grafici...")
    
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Crea il grafico per la distribuzione delle lunghezze in caratteri
    plt.figure(figsize=(10, 6))
    plt.hist(results['char_lengths'], bins=50, alpha=0.7)
    plt.axvline(results['stats']['char']['mean'], color='r', linestyle='--', label=f"Media: {results['stats']['char']['mean']:.2f}")
    plt.axvline(results['stats']['char']['median'], color='g', linestyle='--', label=f"Mediana: {results['stats']['char']['median']:.2f}")
    plt.axvline(results['stats']['char']['p95'], color='b', linestyle='--', label=f"95° percentile: {results['stats']['char']['p95']:.2f}")
    plt.title("Distribuzione delle lunghezze degli SVG in caratteri")
    plt.xlabel("Lunghezza (caratteri)")
    plt.ylabel("Numero di esempi")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(output_dir, "svg_char_lengths.png"), dpi=300, bbox_inches='tight')
    
    # Crea il grafico per la distribuzione delle lunghezze in token per Llama
    plt.figure(figsize=(10, 6))
    plt.hist(results['llama_token_lengths'], bins=50, alpha=0.7)
    plt.axvline(results['stats']['llama_tokens']['mean'], color='r', linestyle='--', label=f"Media: {results['stats']['llama_tokens']['mean']:.2f}")
    plt.axvline(results['stats']['llama_tokens']['median'], color='g', linestyle='--', label=f"Mediana: {results['stats']['llama_tokens']['median']:.2f}")
    plt.axvline(results['stats']['llama_tokens']['p95'], color='b', linestyle='--', label=f"95° percentile: {results['stats']['llama_tokens']['p95']:.2f}")
    plt.axvline(args.llama_max_length, color='k', linestyle='-', label=f"Max length: {args.llama_max_length}")
    plt.title("Distribuzione delle lunghezze degli SVG in token (Llama)")
    plt.xlabel("Lunghezza (token)")
    plt.ylabel("Numero di esempi")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(output_dir, "svg_llama_token_lengths.png"), dpi=300, bbox_inches='tight')
    
    # Crea il grafico per la distribuzione delle lunghezze in token per Gemma
    plt.figure(figsize=(10, 6))
    plt.hist(results['gemma_token_lengths'], bins=50, alpha=0.7)
    plt.axvline(results['stats']['gemma_tokens']['mean'], color='r', linestyle='--', label=f"Media: {results['stats']['gemma_tokens']['mean']:.2f}")
    plt.axvline(results['stats']['gemma_tokens']['median'], color='g', linestyle='--', label=f"Mediana: {results['stats']['gemma_tokens']['median']:.2f}")
    plt.axvline(results['stats']['gemma_tokens']['p95'], color='b', linestyle='--', label=f"95° percentile: {results['stats']['gemma_tokens']['p95']:.2f}")
    plt.axvline(args.gemma_max_length, color='k', linestyle='-', label=f"Max length: {args.gemma_max_length}")
    plt.title("Distribuzione delle lunghezze degli SVG in token (Gemma)")
    plt.xlabel("Lunghezza (token)")
    plt.ylabel("Numero di esempi")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(output_dir, "svg_gemma_token_lengths.png"), dpi=300, bbox_inches='tight')
    
    # Crea il grafico di confronto tra Llama e Gemma
    plt.figure(figsize=(10, 6))
    plt.hist(results['llama_token_lengths'], bins=50, alpha=0.5, label="Llama")
    plt.hist(results['gemma_token_lengths'], bins=50, alpha=0.5, label="Gemma")
    plt.axvline(args.llama_max_length, color='r', linestyle='-', label=f"Llama max: {args.llama_max_length}")
    plt.axvline(args.gemma_max_length, color='g', linestyle='-', label=f"Gemma max: {args.gemma_max_length}")
    plt.title("Confronto delle lunghezze degli SVG in token (Llama vs Gemma)")
    plt.xlabel("Lunghezza (token)")
    plt.ylabel("Numero di esempi")
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(output_dir, "svg_token_lengths_comparison.png"), dpi=300, bbox_inches='tight')
    
    logger.info(f"Grafici salvati in {output_dir}")

def main():
    global args
    args = parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica i dati
    data = load_data(args.data_file)
    
    # Carica i tokenizer
    logger.info(f"Caricamento del tokenizer Llama da {args.llama_tokenizer}")
    llama_tokenizer = AutoTokenizer.from_pretrained(args.llama_tokenizer)
    
    logger.info(f"Caricamento del tokenizer Gemma da {args.gemma_tokenizer}")
    gemma_tokenizer = AutoTokenizer.from_pretrained(args.gemma_tokenizer)
    
    # Analizza le lunghezze degli SVG
    results = analyze_svg_lengths(data, llama_tokenizer, gemma_tokenizer, args.llama_max_length, args.gemma_max_length)
    
    # Stampa le statistiche
    logger.info("Statistiche delle lunghezze degli SVG:")
    logger.info(f"Caratteri: min={results['stats']['char']['min']}, max={results['stats']['char']['max']}, media={results['stats']['char']['mean']:.2f}, mediana={results['stats']['char']['median']:.2f}")
    logger.info(f"Token Llama: min={results['stats']['llama_tokens']['min']}, max={results['stats']['llama_tokens']['max']}, media={results['stats']['llama_tokens']['mean']:.2f}, mediana={results['stats']['llama_tokens']['median']:.2f}")
    logger.info(f"Token Gemma: min={results['stats']['gemma_tokens']['min']}, max={results['stats']['gemma_tokens']['max']}, media={results['stats']['gemma_tokens']['mean']:.2f}, mediana={results['stats']['gemma_tokens']['median']:.2f}")
    
    logger.info(f"SVG che superano la lunghezza massima per Llama ({args.llama_max_length}): {results['stats']['llama_tokens']['over_max']} ({results['stats']['llama_tokens']['over_max_percent']:.2f}%)")
    logger.info(f"SVG che superano la lunghezza massima per Gemma ({args.gemma_max_length}): {results['stats']['gemma_tokens']['over_max']} ({results['stats']['gemma_tokens']['over_max_percent']:.2f}%)")
    
    # Crea i grafici
    plot_distributions(results, args.output_dir)
    
    # Salva le statistiche in un file JSON
    stats_file = os.path.join(args.output_dir, "svg_length_stats.json")
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(results['stats'], f, indent=2)
    
    logger.info(f"Statistiche salvate in {stats_file}")
    logger.info("Analisi completata!")

if __name__ == "__main__":
    main()
