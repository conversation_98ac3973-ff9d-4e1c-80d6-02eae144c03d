#!/usr/bin/env python3
"""
Analisi risultati baseline SVG-Fixed
"""

import json
import os
from pathlib import Path
import pandas as pd
from collections import Counter
import re

def load_predictions(file_path):
    """Carica predizioni da file JSONL"""
    predictions = []
    with open(file_path, 'r') as f:
        for line in f:
            predictions.append(json.loads(line))
    return predictions

def analyze_model_results(model_name, predictions):
    """Analizza risultati di un modello"""
    print(f"\n🔍 ANALISI {model_name.upper()}:")
    print(f"📊 Esempi totali: {len(predictions)}")
    
    # Lunghezza caption
    lengths = [len(pred['generated'].split()) for pred in predictions]
    print(f"📏 Lunghezza media caption: {sum(lengths)/len(lengths):.1f} parole")
    print(f"📏 Range lunghezza: {min(lengths)}-{max(lengths)} parole")
    
    # Parole più comuni
    all_words = []
    for pred in predictions:
        words = re.findall(r'\b\w+\b', pred['generated'].lower())
        all_words.extend(words)
    
    common_words = Counter(all_words).most_common(10)
    print(f"🔤 Parole più comuni: {', '.join([f'{w}({c})' for w, c in common_words[:5]])}")
    
    # Esempi interessanti
    print(f"\n📝 ESEMPI {model_name.upper()}:")
    for i, pred in enumerate(predictions[:3]):
        print(f"  {i+1}. ID: {pred['id']}")
        print(f"     Reference: {pred['reference'][:60]}...")
        print(f"     Generated: {pred['generated']}")
        print()

def compare_models(idefix_preds, flores2_preds, blip2_preds):
    """Confronta i modelli"""
    print("\n🔄 CONFRONTO MODELLI:")
    
    # Stesso esempio per tutti i modelli
    example_id = idefix_preds[0]['id']
    
    print(f"📋 Esempio ID: {example_id}")
    print(f"📖 Reference: {idefix_preds[0]['reference']}")
    print(f"🤖 Ide Fix 3: {idefix_preds[0]['generated']}")
    print(f"🌸 Flores 2:  {flores2_preds[0]['generated']}")
    print(f"🔵 BLIP 2.7B: {blip2_preds[0]['generated']}")
    
    # Lunghezze medie
    idefix_avg = sum(len(p['generated'].split()) for p in idefix_preds) / len(idefix_preds)
    flores2_avg = sum(len(p['generated'].split()) for p in flores2_preds) / len(flores2_preds)
    blip2_avg = sum(len(p['generated'].split()) for p in blip2_preds) / len(blip2_preds)
    
    print(f"\n📊 LUNGHEZZE MEDIE:")
    print(f"🤖 Ide Fix 3: {idefix_avg:.1f} parole")
    print(f"🌸 Flores 2:  {flores2_avg:.1f} parole")
    print(f"🔵 BLIP 2.7B: {blip2_avg:.1f} parole")

def find_interesting_examples(predictions, model_name):
    """Trova esempi interessanti"""
    print(f"\n🎯 ESEMPI INTERESSANTI {model_name.upper()}:")
    
    # Caption più lunghe
    long_captions = sorted(predictions, key=lambda x: len(x['generated'].split()), reverse=True)[:2]
    print("📏 Caption più lunghe:")
    for i, pred in enumerate(long_captions):
        print(f"  {i+1}. {pred['generated']} ({len(pred['generated'].split())} parole)")
    
    # Caption più corte
    short_captions = sorted(predictions, key=lambda x: len(x['generated'].split()))[:2]
    print("📏 Caption più corte:")
    for i, pred in enumerate(short_captions):
        print(f"  {i+1}. {pred['generated']} ({len(pred['generated'].split())} parole)")

def main():
    base_dir = Path("experiments/baseline_svg_fixed_final")
    
    print("🎉 ANALISI RISULTATI BASELINE SVG-FIXED")
    print("=" * 50)
    
    # Carica risultati
    idefix_file = base_dir / "baseline_idefix_svg_fixed" / "predictions.jsonl"
    flores2_file = base_dir / "baseline_flores2_svg_fixed" / "predictions.jsonl"
    blip2_file = base_dir / "baseline_blip2_svg_fixed" / "predictions.jsonl"
    
    idefix_preds = load_predictions(idefix_file)
    flores2_preds = load_predictions(flores2_file)
    blip2_preds = load_predictions(blip2_file)
    
    # Analizza ogni modello
    analyze_model_results("Ide Fix 3", idefix_preds)
    analyze_model_results("Flores 2", flores2_preds)
    analyze_model_results("BLIP 2.7B", blip2_preds)
    
    # Confronta modelli
    compare_models(idefix_preds, flores2_preds, blip2_preds)
    
    # Esempi interessanti
    find_interesting_examples(idefix_preds, "Ide Fix 3")
    find_interesting_examples(flores2_preds, "Flores 2")
    find_interesting_examples(blip2_preds, "BLIP 2.7B")
    
    print("\n🎉 ANALISI COMPLETATA!")
    print(f"✅ Tutti e 3 i modelli hanno processato {len(idefix_preds)} esempi con successo")
    print("✅ SVG riparati funzionano perfettamente")
    print("✅ Baseline pronto per confronto con training models")

if __name__ == "__main__":
    main()
