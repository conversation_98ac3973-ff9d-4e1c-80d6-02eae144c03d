#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per pre-renderizzare gli SVG in PNG.
Questo script prende un file JSON contenente SVG e li renderizza in immagini PNG.
"""

import os
import sys
import json
import argparse
import logging
import time
import subprocess
import shutil
import tempfile
from tqdm import tqdm
from PIL import Image, ImageDraw, ImageFont
import io
import cairosvg
import traceback
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import re

# Importa svglib come alternativa a cairosvg
try:
    from svglib.svglib import svg2rlg
    from reportlab.graphics import renderPM
    SVGLIB_AVAILABLE = True
except ImportError:
    SVGLIB_AVAILABLE = False

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Pre-renderizza SVG in PNG")
    parser.add_argument("--data_file", type=str, required=True,
                        help="File JSON contenente gli SVG")
    parser.add_argument("--output_dir", type=str, required=True,
                        help="Directory di output per le immagini PNG")
    parser.add_argument("--size", type=int, default=224,
                        help="Dimensione delle immagini PNG (default: 224)")
    parser.add_argument("--num_workers", type=int, default=1,
                        help="Numero di worker per il multiprocessing (default: 1)")
    parser.add_argument("--timeout", type=int, default=10,
                        help="Timeout in secondi per il rendering di ogni SVG (default: 10)")
    parser.add_argument("--max_samples", type=int, default=-1,
                        help="Numero massimo di campioni da elaborare (-1 per tutti)")
    return parser.parse_args()

def is_inkscape_available():
    """
    Verifica se Inkscape è disponibile nel sistema.

    Returns:
        bool: True se Inkscape è disponibile, False altrimenti
    """
    try:
        result = subprocess.run(['which', 'inkscape'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE)
        return result.returncode == 0
    except Exception:
        return False

def render_with_inkscape(svg_string, output_path, size, timeout):
    """
    Renderizza un SVG in PNG usando Inkscape.

    Args:
        svg_string: Stringa contenente l'SVG
        output_path: Percorso di output per il PNG
        size: Dimensione dell'immagine
        timeout: Timeout in secondi

    Returns:
        bool: True se il rendering è riuscito, False altrimenti
    """
    try:
        # Crea un file temporaneo per l'SVG
        with tempfile.NamedTemporaryFile(suffix='.svg', delete=False) as temp_svg:
            temp_svg.write(svg_string.encode('utf-8'))
            temp_svg_path = temp_svg.name

        # Comando Inkscape per convertire SVG in PNG
        cmd = [
            'inkscape',
            '--export-filename=' + output_path,
            f'--export-width={size}',
            f'--export-height={size}',
            temp_svg_path
        ]

        # Esegui il comando con timeout
        process = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=timeout
        )

        # Rimuovi il file temporaneo
        os.unlink(temp_svg_path)

        # Verifica se il comando è riuscito
        if process.returncode == 0 and os.path.exists(output_path):
            return True
        else:
            logger.warning(f"Inkscape ha restituito un errore: {process.stderr.decode('utf-8', errors='ignore')}")
            return False

    except subprocess.TimeoutExpired:
        logger.warning(f"Timeout durante l'esecuzione di Inkscape per {output_path}")
        # Rimuovi il file temporaneo se esiste ancora
        if 'temp_svg_path' in locals() and os.path.exists(temp_svg_path):
            os.unlink(temp_svg_path)
        return False
    except Exception as e:
        logger.error(f"Errore durante l'utilizzo di Inkscape: {e}")
        # Rimuovi il file temporaneo se esiste ancora
        if 'temp_svg_path' in locals() and os.path.exists(temp_svg_path):
            os.unlink(temp_svg_path)
        return False

def render_svg_to_png(args):
    """
    Renderizza un SVG in PNG.

    Args:
        args: Tupla contenente (svg_string, output_path, size, timeout)

    Returns:
        Tupla (success, output_path, error_message)
    """
    svg_string, output_path, size, timeout = args
    item_id = os.path.basename(output_path).replace('.png', '')

    try:
        # Imposta un timeout per il rendering
        start_time = time.time()

        # Verifica se l'SVG è valido
        if not svg_string or len(svg_string) < 10 or not svg_string.strip().startswith('<svg'):
            logger.error(f"SVG non valido o vuoto per {item_id}")
            return (False, output_path, "SVG non valido o vuoto")

        # Pulisci l'SVG da eventuali caratteri problematici
        svg_string = svg_string.replace('\x00', '')

        # Correggi problemi comuni negli SVG
        # Aggiungi viewBox se manca
        if 'viewBox' not in svg_string and 'width' in svg_string and 'height' in svg_string:
            svg_string = svg_string.replace('<svg ', f'<svg viewBox="0 0 {size} {size}" ', 1)

        # Correggi i path con sintassi errata (d=M... invece di d="M...")
        svg_string = svg_string.replace('d=M', 'd="M')
        if 'd="M' in svg_string and not 'Z"' in svg_string and not 'z"' in svg_string:
            svg_string = svg_string.replace('" />', ' Z" />')

        # Strategia 1: Prova prima con cairosvg (veloce ma meno compatibile)
        try:
            # Renderizza SVG in PNG con cairosvg
            png_data = cairosvg.svg2png(
                bytestring=svg_string.encode('utf-8'),
                output_width=size,
                output_height=size,
                parent_width=size,
                parent_height=size,
                scale=1.0,
                unsafe=True  # Permette di renderizzare SVG potenzialmente non validi
            )

            # Salva l'immagine PNG
            with open(output_path, 'wb') as f:
                f.write(png_data)

            logger.info(f"Rendering riuscito con cairosvg per {item_id}")
            return (True, output_path, "cairosvg_success")

        except Exception as cairo_error:
            # Registra l'errore specifico di cairosvg
            logger.warning(f"cairosvg fallito per {item_id}: {cairo_error}")

            # Strategia 2: Prova con svglib se disponibile
            if SVGLIB_AVAILABLE:
                try:
                    # Salva l'SVG in un file temporaneo
                    with tempfile.NamedTemporaryFile(suffix='.svg', delete=False) as temp_svg:
                        temp_svg.write(svg_string.encode('utf-8'))
                        temp_svg_path = temp_svg.name

                    # Converti SVG in un oggetto ReportLab
                    drawing = svg2rlg(temp_svg_path)

                    # Renderizza in un'immagine PIL
                    img_data = renderPM.drawToString(drawing, fmt="PNG")
                    img = Image.open(io.BytesIO(img_data))

                    # Ridimensiona l'immagine se necessario
                    if img.size != (size, size):
                        img = img.resize((size, size), Image.LANCZOS)

                    # Salva l'immagine
                    img.save(output_path, format='PNG')

                    # Rimuovi il file temporaneo
                    os.unlink(temp_svg_path)

                    logger.info(f"Rendering riuscito con svglib per {item_id}")
                    return (True, output_path, "svglib_success")

                except Exception as svglib_error:
                    logger.warning(f"svglib fallito per {item_id}: {svglib_error}")
                    # Rimuovi il file temporaneo se esiste ancora
                    if 'temp_svg_path' in locals() and os.path.exists(temp_svg_path):
                        os.unlink(temp_svg_path)

            # Strategia 3: Prova con una versione semplificata dell'SVG
            try:
                # Estrai i path dall'SVG originale
                paths = re.findall(r'<path[^>]*>', svg_string)

                # Crea un nuovo SVG semplificato
                simplified_svg = f'''<svg width="{size}" height="{size}" viewBox="0 0 {size} {size}" xmlns="http://www.w3.org/2000/svg">
                <rect width="{size}" height="{size}" fill="white"/>
                {' '.join(paths)}
                </svg>'''

                # Renderizza l'SVG semplificato
                png_data = cairosvg.svg2png(
                    bytestring=simplified_svg.encode('utf-8'),
                    output_width=size,
                    output_height=size,
                    unsafe=True
                )

                # Salva l'immagine PNG
                with open(output_path, 'wb') as f:
                    f.write(png_data)

                logger.info(f"Rendering riuscito con SVG semplificato per {item_id}")
                return (True, output_path, "simplified_svg_success")

            except Exception as simplified_error:
                logger.warning(f"SVG semplificato fallito per {item_id}: {simplified_error}")

            # Strategia 4: Prova con Inkscape se disponibile
            if is_inkscape_available():
                if render_with_inkscape(svg_string, output_path, size, timeout):
                    logger.info(f"Rendering riuscito con Inkscape per {item_id}")
                    return (True, output_path, "inkscape_success")
                else:
                    logger.warning(f"Anche Inkscape è fallito per {item_id}")
            else:
                logger.warning("Inkscape non disponibile nel sistema")

            # Strategia 4: Crea un'immagine informativa come fallback
            try:
                # Crea un'immagine con testo che indica l'ID dell'SVG
                from PIL import Image, ImageDraw, ImageFont
                img = Image.new('RGB', (size, size), color='white')
                draw = ImageDraw.Draw(img)

                # Aggiungi un bordo per distinguere l'immagine
                draw.rectangle([(0, 0), (size-1, size-1)], outline='black', width=2)

                # Aggiungi l'ID dell'SVG come testo
                try:
                    font = ImageFont.truetype("DejaVuSans.ttf", 12)
                except:
                    font = ImageFont.load_default()

                draw.text((10, 10), f"SVG ID: {item_id}", fill='black', font=font)
                draw.text((10, 30), "Rendering fallito", fill='red', font=font)

                # Aggiungi un estratto dell'SVG come testo
                svg_preview = svg_string[:100] + "..." if len(svg_string) > 100 else svg_string
                lines = [svg_preview[i:i+40] for i in range(0, len(svg_preview), 40)]
                for i, line in enumerate(lines[:5]):
                    draw.text((10, 50 + i*15), line, fill='blue', font=font)

                img.save(output_path, format='PNG')
                logger.warning(f"Creata immagine informativa fallback per {item_id}")
                return (True, output_path, "informative_fallback")

            except Exception as fallback_error:
                logger.error(f"Errore nella creazione dell'immagine fallback informativa: {fallback_error}")

                # Strategia 5: Fallback con SVG semplificato standard
                try:
                    fallback_svg = f'<svg width="{size}" height="{size}" xmlns="http://www.w3.org/2000/svg"><rect width="{size}" height="{size}" fill="white" stroke="black" stroke-width="1"/><text x="10" y="20" font-family="sans-serif" font-size="12" fill="black">SVG ID: {item_id}</text></svg>'
                    png_data = cairosvg.svg2png(
                        bytestring=fallback_svg.encode('utf-8'),
                        output_width=size,
                        output_height=size
                    )

                    # Salva l'immagine PNG
                    with open(output_path, 'wb') as f:
                        f.write(png_data)

                    logger.warning(f"Utilizzato SVG fallback standard per {item_id}")
                    return (True, output_path, "standard_fallback")
                except Exception as standard_fallback_error:
                    logger.error(f"Errore anche nel fallback standard: {standard_fallback_error}")

        # Verifica se il rendering ha superato il timeout
        if time.time() - start_time > timeout:
            logger.warning(f"Timeout durante il rendering SVG per {item_id}. Utilizzando immagine fallback.")
            # Crea un'immagine vuota come fallback
            img = Image.new('RGB', (size, size), color='white')
            draw = ImageDraw.Draw(img)
            draw.rectangle([(0, 0), (size-1, size-1)], outline='black', width=2)
            draw.text((10, 10), f"SVG ID: {item_id}", fill='black')
            draw.text((10, 30), "Timeout durante il rendering", fill='red')
            img.save(output_path, format='PNG')
            return (True, output_path, "timeout_fallback")

    except Exception as e:
        error_message = f"Errore nel rendering SVG: {str(e)}"
        logger.error(f"{error_message}\n{traceback.format_exc()}")

        # Crea un'immagine vuota come fallback
        try:
            img = Image.new('RGB', (size, size), color='white')
            draw = ImageDraw.Draw(img)
            draw.rectangle([(0, 0), (size-1, size-1)], outline='black', width=2)
            draw.text((10, 10), f"SVG ID: {item_id}", fill='black')
            draw.text((10, 30), "Errore generico", fill='red')
            img.save(output_path, format='PNG')
            return (True, output_path, f"error_fallback: {str(e)}")
        except Exception as fallback_error:
            return (False, output_path, f"Errore anche nel fallback: {str(fallback_error)}")

def main():
    args = parse_args()

    # Crea la directory di output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)

    # Carica i dati
    logger.info(f"Caricamento dei dati da {args.data_file}")
    try:
        with open(args.data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        logger.error(f"Errore nel caricamento dei dati: {e}")
        sys.exit(1)

    # Limita il numero di campioni se specificato
    if args.max_samples > 0 and args.max_samples < len(data):
        logger.info(f"Limitazione a {args.max_samples} campioni")
        data = data[:args.max_samples]

    logger.info(f"Elaborazione di {len(data)} SVG")

    # Prepara gli argomenti per il multiprocessing
    render_args = []
    for i, item in enumerate(data):
        item_id = item.get('id', f"item_{i}")

        # Ottieni l'SVG dal campo corretto
        svg = None
        if 'xml' in item:
            svg = item.get('xml', '')
        elif 'svg' in item:
            svg = item.get('svg', '')
        elif 'caption' in item and isinstance(item['caption'], dict) and 'xml' in item['caption']:
            svg = item['caption'].get('xml', '')

        if not svg:
            logger.warning(f"SVG non trovato per l'ID {item_id}")
            # Crea un'immagine vuota come fallback
            output_path = os.path.join(args.output_dir, f"{item_id}.png")
            try:
                from PIL import Image
                img = Image.new('RGB', (args.size, args.size), color='white')
                img.save(output_path, format='PNG')
                logger.warning(f"Creata immagine fallback vuota per {item_id}")
            except Exception as fallback_error:
                logger.error(f"Impossibile creare immagine fallback per {item_id}: {fallback_error}")
            continue

        output_path = os.path.join(args.output_dir, f"{item_id}.png")
        render_args.append((svg, output_path, args.size, args.timeout))

    # Verifica se Inkscape è disponibile
    inkscape_available = is_inkscape_available()
    if inkscape_available:
        logger.info("Inkscape è disponibile nel sistema e verrà utilizzato come fallback")
    else:
        logger.warning("Inkscape non è disponibile nel sistema. Verranno utilizzati altri metodi di rendering")

    # Verifica se svglib è disponibile
    if SVGLIB_AVAILABLE:
        logger.info("svglib è disponibile e verrà utilizzato come alternativa a cairosvg")
    else:
        logger.warning("svglib non è disponibile. Installalo con 'pip install svglib'")

    # Renderizza gli SVG in modo sequenziale (più affidabile)
    success_count = 0
    error_count = 0
    cairosvg_success = 0
    svglib_success = 0
    inkscape_success = 0
    simplified_svg_success = 0
    fallback_count = 0

    for arg in tqdm(render_args, desc="Rendering SVG"):
        try:
            result = render_svg_to_png(arg)
            success, output_path, message = result

            if success:
                success_count += 1
                if message == "cairosvg_success":
                    cairosvg_success += 1
                elif message == "svglib_success":
                    svglib_success += 1
                elif message == "inkscape_success":
                    inkscape_success += 1
                elif message == "simplified_svg_success":
                    simplified_svg_success += 1
                else:
                    # Successo con fallback
                    fallback_count += 1
            else:
                error_count += 1
                logger.error(f"Errore per {output_path}: {message}")

        except Exception as e:
            error_count += 1
            logger.error(f"Errore generale: {e}")

    # Crea un file di riepilogo
    summary = {
        'total': len(render_args),
        'success': success_count,
        'error': error_count,
        'cairosvg_success': cairosvg_success,
        'svglib_success': svglib_success,
        'inkscape_success': inkscape_success,
        'simplified_svg_success': simplified_svg_success,
        'fallback_count': fallback_count,
        'inkscape_available': inkscape_available,
        'svglib_available': SVGLIB_AVAILABLE,
        'size': args.size,
        'data_file': args.data_file,
        'output_dir': args.output_dir
    }

    summary_path = os.path.join(args.output_dir, "rendering_summary.json")
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)

    logger.info(f"Rendering completato: {success_count} successi, {error_count} errori")
    logger.info(f"Riepilogo salvato in {summary_path}")

if __name__ == "__main__":
    main()
