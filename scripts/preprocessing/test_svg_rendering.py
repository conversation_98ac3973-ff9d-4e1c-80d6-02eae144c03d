#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per testare il rendering SVG con diverse librerie.
Questo script prende un file JSON contenente SVG e testa diverse librerie di rendering.
"""

import os
import sys
import json
import argparse
import logging
import time
from tqdm import tqdm
from PIL import Image, ImageDraw, ImageFont
import io
import traceback
import tempfile

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Test di rendering SVG con diverse librerie")
    parser.add_argument("--data_file", type=str, required=True,
                        help="File JSON contenente gli SVG")
    parser.add_argument("--output_dir", type=str, required=True,
                        help="Directory di output per le immagini PNG")
    parser.add_argument("--size", type=int, default=224,
                        help="Dimensione delle immagini PNG (default: 224)")
    parser.add_argument("--num_samples", type=int, default=5,
                        help="Numero di campioni da testare (default: 5)")
    return parser.parse_args()

def test_cairosvg(svg_string, output_path, size):
    """Testa il rendering SVG con cairosvg."""
    try:
        import cairosvg
        import time

        # Registra il tempo di inizio
        start_time = time.time()

        # Pulisci l'SVG da eventuali caratteri problematici
        svg_string = svg_string.replace('\x00', '')

        # Aggiungi informazioni di debug
        logger.info(f"Rendering con cairosvg per {os.path.basename(output_path)}")
        logger.info(f"Dimensione SVG: {len(svg_string)} caratteri")

        # Prova a renderizzare l'SVG
        png_data = cairosvg.svg2png(
            bytestring=svg_string.encode('utf-8'),
            output_width=size,
            output_height=size,
            parent_width=size,
            parent_height=size,
            scale=1.0,
            unsafe=True
        )

        # Salva l'immagine PNG
        with open(output_path, 'wb') as f:
            f.write(png_data)

        # Registra il tempo di fine
        end_time = time.time()
        logger.info(f"Rendering completato in {end_time - start_time:.2f} secondi")

        return True, None

    except Exception as e:
        logger.error(f"Errore nel rendering con cairosvg: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False, str(e)

def test_svglib(svg_string, output_path, size):
    """Testa il rendering SVG con svglib."""
    try:
        from svglib.svglib import svg2rlg
        from reportlab.graphics import renderPM
        import time

        # Registra il tempo di inizio
        start_time = time.time()

        # Aggiungi informazioni di debug
        logger.info(f"Rendering con svglib per {os.path.basename(output_path)}")
        logger.info(f"Dimensione SVG: {len(svg_string)} caratteri")

        # Salva l'SVG in un file temporaneo
        with tempfile.NamedTemporaryFile(suffix='.svg', delete=False) as temp_svg:
            temp_svg.write(svg_string.encode('utf-8'))
            temp_svg_path = temp_svg.name
            logger.info(f"SVG salvato in file temporaneo: {temp_svg_path}")

        # Converti SVG in un oggetto ReportLab
        logger.info("Conversione SVG in oggetto ReportLab...")
        drawing = svg2rlg(temp_svg_path)

        # Renderizza in un'immagine PIL
        logger.info("Renderizzazione in immagine PIL...")
        img_data = renderPM.drawToString(drawing, fmt="PNG")
        img = Image.open(io.BytesIO(img_data))

        # Ridimensiona l'immagine se necessario
        if img.size != (size, size):
            logger.info(f"Ridimensionamento immagine da {img.size} a ({size}, {size})...")
            img = img.resize((size, size), Image.LANCZOS)

        # Salva l'immagine
        logger.info(f"Salvataggio immagine in {output_path}...")
        img.save(output_path, format='PNG')

        # Rimuovi il file temporaneo
        os.unlink(temp_svg_path)

        # Registra il tempo di fine
        end_time = time.time()
        logger.info(f"Rendering completato in {end_time - start_time:.2f} secondi")

        return True, None

    except Exception as e:
        logger.error(f"Errore nel rendering con svglib: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Rimuovi il file temporaneo se esiste ancora
        if 'temp_svg_path' in locals() and os.path.exists(temp_svg_path):
            os.unlink(temp_svg_path)
        return False, str(e)

def test_inkscape(svg_string, output_path, size):
    """Testa il rendering SVG con Inkscape."""
    try:
        import time

        # Registra il tempo di inizio
        start_time = time.time()

        # Aggiungi informazioni di debug
        logger.info(f"Rendering con Inkscape per {os.path.basename(output_path)}")
        logger.info(f"Dimensione SVG: {len(svg_string)} caratteri")

        # Verifica se Inkscape è disponibile
        import subprocess
        logger.info("Verifica disponibilità di Inkscape...")
        result = subprocess.run(['which', 'inkscape'],
                               stdout=subprocess.PIPE,
                               stderr=subprocess.PIPE)
        if result.returncode != 0:
            logger.warning("Inkscape non è installato")
            return False, "Inkscape non è installato"

        # Crea un file temporaneo per l'SVG
        logger.info("Creazione file temporaneo per SVG...")
        with tempfile.NamedTemporaryFile(suffix='.svg', delete=False) as temp_svg:
            temp_svg.write(svg_string.encode('utf-8'))
            temp_svg_path = temp_svg.name
            logger.info(f"SVG salvato in file temporaneo: {temp_svg_path}")

        # Comando Inkscape per convertire SVG in PNG
        cmd = [
            'inkscape',
            '--export-filename=' + output_path,
            f'--export-width={size}',
            f'--export-height={size}',
            temp_svg_path
        ]
        logger.info(f"Comando Inkscape: {' '.join(cmd)}")

        # Esegui il comando con timeout
        logger.info("Esecuzione comando Inkscape...")
        process = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            timeout=30  # Aumentato il timeout a 30 secondi
        )

        # Rimuovi il file temporaneo
        os.unlink(temp_svg_path)

        # Verifica se il comando è riuscito
        if process.returncode == 0 and os.path.exists(output_path):
            # Registra il tempo di fine
            end_time = time.time()
            logger.info(f"Rendering completato in {end_time - start_time:.2f} secondi")
            return True, None
        else:
            error_msg = process.stderr.decode('utf-8', errors='ignore')
            logger.error(f"Errore nel rendering con Inkscape: {error_msg}")
            return False, error_msg

    except Exception as e:
        logger.error(f"Errore nel rendering con Inkscape: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Rimuovi il file temporaneo se esiste ancora
        if 'temp_svg_path' in locals() and os.path.exists(temp_svg_path):
            os.unlink(temp_svg_path)
        return False, str(e)

def main():
    args = parse_args()

    # Crea la directory di output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)

    # Configura il logging per scrivere anche su file
    log_file = os.path.join(args.output_dir, "test_svg_rendering.log")
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
    logger.addHandler(file_handler)

    logger.info("=" * 80)
    logger.info("Avvio test di rendering SVG")
    logger.info(f"File di test: {args.data_file}")
    logger.info(f"Directory di output: {args.output_dir}")
    logger.info(f"Dimensione immagini: {args.size}x{args.size}")
    logger.info(f"Numero di campioni: {args.num_samples}")
    logger.info("=" * 80)

    # Carica i dati
    logger.info(f"Caricamento dei dati da {args.data_file}")
    try:
        with open(args.data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        logger.error(f"Errore nel caricamento dei dati: {e}")
        sys.exit(1)

    # Limita il numero di campioni
    if args.num_samples > 0 and args.num_samples < len(data):
        logger.info(f"Limitazione a {args.num_samples} campioni")
        data = data[:args.num_samples]

    logger.info(f"Elaborazione di {len(data)} SVG")

    # Risultati
    results = []

    # Testa il rendering per ogni SVG
    for i, item in enumerate(tqdm(data, desc="Testing SVG rendering")):
        item_id = item.get('id', f"item_{i}")

        # Ottieni l'SVG dal campo corretto
        svg = None
        if 'xml' in item:
            svg = item.get('xml', '')
        elif 'svg' in item:
            svg = item.get('svg', '')
        elif 'caption' in item and isinstance(item['caption'], dict) and 'xml' in item['caption']:
            svg = item['caption'].get('xml', '')

        if not svg:
            logger.warning(f"SVG non trovato per l'ID {item_id}")
            continue

        # Crea le directory per i risultati
        cairosvg_dir = os.path.join(args.output_dir, "cairosvg")
        svglib_dir = os.path.join(args.output_dir, "svglib")
        inkscape_dir = os.path.join(args.output_dir, "inkscape")

        os.makedirs(cairosvg_dir, exist_ok=True)
        os.makedirs(svglib_dir, exist_ok=True)
        os.makedirs(inkscape_dir, exist_ok=True)

        # Percorsi di output
        cairosvg_path = os.path.join(cairosvg_dir, f"{item_id}.png")
        svglib_path = os.path.join(svglib_dir, f"{item_id}.png")
        inkscape_path = os.path.join(inkscape_dir, f"{item_id}.png")

        # Testa cairosvg
        cairosvg_success, cairosvg_error = test_cairosvg(svg, cairosvg_path, args.size)

        # Testa svglib
        svglib_success, svglib_error = test_svglib(svg, svglib_path, args.size)

        # Testa Inkscape
        inkscape_success, inkscape_error = test_inkscape(svg, inkscape_path, args.size)

        # Registra i risultati
        result = {
            'id': item_id,
            'cairosvg': {
                'success': cairosvg_success,
                'error': cairosvg_error,
                'path': cairosvg_path if cairosvg_success else None
            },
            'svglib': {
                'success': svglib_success,
                'error': svglib_error,
                'path': svglib_path if svglib_success else None
            },
            'inkscape': {
                'success': inkscape_success,
                'error': inkscape_error,
                'path': inkscape_path if inkscape_success else None
            }
        }

        results.append(result)

    # Salva i risultati
    results_path = os.path.join(args.output_dir, "rendering_test_results.json")
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)

    # Crea un riepilogo
    summary = {
        'total': len(results),
        'cairosvg_success': sum(1 for r in results if r['cairosvg']['success']),
        'svglib_success': sum(1 for r in results if r['svglib']['success']),
        'inkscape_success': sum(1 for r in results if r['inkscape']['success']),
        'all_success': sum(1 for r in results if r['cairosvg']['success'] and r['svglib']['success'] and r['inkscape']['success']),
        'all_fail': sum(1 for r in results if not r['cairosvg']['success'] and not r['svglib']['success'] and not r['inkscape']['success'])
    }

    # Salva il riepilogo
    summary_path = os.path.join(args.output_dir, "rendering_test_summary.json")
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)

    # Stampa il riepilogo
    logger.info("Riepilogo dei test di rendering:")
    logger.info(f"Totale SVG testati: {summary['total']}")
    logger.info(f"Successi con cairosvg: {summary['cairosvg_success']} ({summary['cairosvg_success']/summary['total']*100:.1f}%)")
    logger.info(f"Successi con svglib: {summary['svglib_success']} ({summary['svglib_success']/summary['total']*100:.1f}%)")
    logger.info(f"Successi con Inkscape: {summary['inkscape_success']} ({summary['inkscape_success']/summary['total']*100:.1f}%)")
    logger.info(f"SVG con successo in tutti i metodi: {summary['all_success']} ({summary['all_success']/summary['total']*100:.1f}%)")
    logger.info(f"SVG falliti con tutti i metodi: {summary['all_fail']} ({summary['all_fail']/summary['total']*100:.1f}%)")

    logger.info(f"Risultati salvati in {results_path}")
    logger.info(f"Riepilogo salvato in {summary_path}")

if __name__ == "__main__":
    main()
