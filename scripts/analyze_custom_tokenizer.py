#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from transformers import AutoTokenizer
import wandb
from collections import Counter
import re

# Configurazione matplotlib
plt.style.use('ggplot')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

def setup_wandb(project_name="captioner", run_name="tokenizer_analysis", entity="337543-unimore"):
    """Inizializza Weights & Biands per il tracking."""
    try:
        wandb.init(
            project=project_name,
            name=run_name,
            entity=entity,
            config={
                "analysis_type": "custom_tokenizer",
                "svg_tokens": 17
            }
        )
        print(f"W&B inizializzato: {wandb.run.name} (ID: {wandb.run.id})")
    except Exception as e:
        print(f"Errore nell'inizializzazione di W&B: {e}")
        print("Continuazione senza tracking W&B...")

def load_tokenizer(model_path, use_custom_tokenizer=False):
    """Carica il tokenizer, opzionalmente con token SVG personalizzati."""
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    if use_custom_tokenizer:
        # Lista di token SVG da aggiungere
        svg_tokens = [
            '<|Z|>', '<|SEP|>', '<|H|>', '<|stroke|>', '<|begin_of_style|>',
            '<|stroke-width|>', '<|end_of_svg|>', '<|currentColor|>', '<|C|>',
            '<|S|>', '<|begin_of_svg|>', '<|end_of_path|>', '<|Q|>', '<|A|>',
            '<|none|>', '<|T|>', '<|M|>', '<|end_of_style|>', '<|opacity|>',
            '<|V|>', '<|L|>', '<|begin_of_path|><|M|>', '<|color|>'
        ]
        
        # Aggiungi i token al tokenizer
        num_added = tokenizer.add_tokens(svg_tokens, special_tokens=True)
        print(f"Aggiunti {num_added} nuovi token speciali.")
    
    return tokenizer

def analyze_svg_tokenization(tokenizer, svg_examples, output_dir):
    """Analizza come il tokenizer tokenizza gli esempi SVG."""
    results = []
    
    for i, svg in enumerate(svg_examples):
        # Tokenizza l'SVG
        tokens = tokenizer.tokenize(svg)
        token_ids = tokenizer.encode(svg)
        
        # Calcola statistiche
        num_tokens = len(tokens)
        unique_tokens = len(set(tokens))
        
        # Identifica token speciali SVG
        svg_special_tokens = [t for t in tokens if t.startswith('<|') and t.endswith('|>')]
        num_svg_special_tokens = len(svg_special_tokens)
        
        results.append({
            'example_id': i,
            'svg': svg,
            'num_tokens': num_tokens,
            'unique_tokens': unique_tokens,
            'num_svg_special_tokens': num_svg_special_tokens,
            'tokens': tokens,
            'token_ids': token_ids
        })
    
    # Crea un DataFrame
    df = pd.DataFrame(results)
    
    # Salva i risultati
    df.to_csv(os.path.join(output_dir, 'svg_tokenization_analysis.csv'), index=False)
    
    return df

def plot_token_distribution(df, output_dir):
    """Crea un grafico della distribuzione del numero di token per esempio SVG."""
    fig, ax = plt.subplots(figsize=(12, 6))
    
    sns.histplot(data=df, x='num_tokens', bins=20, kde=True, ax=ax)
    
    ax.set_title('Distribuzione del Numero di Token per Esempio SVG', fontsize=16)
    ax.set_xlabel('Numero di Token', fontsize=14)
    ax.set_ylabel('Frequenza', fontsize=14)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'token_distribution.png'), dpi=300, bbox_inches='tight')
    
    return fig

def plot_special_tokens_usage(df, output_dir):
    """Crea un grafico dell'uso dei token speciali SVG."""
    fig, ax = plt.subplots(figsize=(12, 6))
    
    sns.scatterplot(data=df, x='num_tokens', y='num_svg_special_tokens', ax=ax)
    
    ax.set_title('Uso dei Token Speciali SVG', fontsize=16)
    ax.set_xlabel('Numero Totale di Token', fontsize=14)
    ax.set_ylabel('Numero di Token Speciali SVG', fontsize=14)
    
    # Aggiungi linea di regressione
    sns.regplot(data=df, x='num_tokens', y='num_svg_special_tokens', 
                scatter=False, ax=ax, color='red')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'special_tokens_usage.png'), dpi=300, bbox_inches='tight')
    
    return fig

def analyze_token_frequency(df, output_dir):
    """Analizza la frequenza dei token nei dati SVG."""
    # Estrai tutti i token da tutti gli esempi
    all_tokens = []
    for tokens in df['tokens']:
        all_tokens.extend(tokens)
    
    # Conta la frequenza dei token
    token_counts = Counter(all_tokens)
    
    # Crea un DataFrame con le frequenze
    token_freq_df = pd.DataFrame({
        'token': list(token_counts.keys()),
        'frequency': list(token_counts.values())
    })
    
    # Ordina per frequenza decrescente
    token_freq_df = token_freq_df.sort_values('frequency', ascending=False)
    
    # Identifica i token speciali SVG
    token_freq_df['is_svg_special'] = token_freq_df['token'].apply(
        lambda t: 1 if (t.startswith('<|') and t.endswith('|>')) else 0
    )
    
    # Salva i risultati
    token_freq_df.to_csv(os.path.join(output_dir, 'token_frequency.csv'), index=False)
    
    return token_freq_df

def plot_token_frequency(token_freq_df, output_dir, top_n=30):
    """Crea un grafico della frequenza dei token più comuni."""
    # Prendi i top N token
    top_tokens = token_freq_df.head(top_n)
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Crea un grafico a barre con colori diversi per token speciali SVG
    bars = sns.barplot(data=top_tokens, x='token', y='frequency', 
                       hue='is_svg_special', palette=['skyblue', 'coral'], ax=ax)
    
    ax.set_title(f'Frequenza dei {top_n} Token Più Comuni', fontsize=16)
    ax.set_xlabel('Token', fontsize=14)
    ax.set_ylabel('Frequenza', fontsize=14)
    ax.set_xticklabels(ax.get_xticklabels(), rotation=90)
    
    # Aggiungi legenda
    ax.legend(['Token Standard', 'Token Speciali SVG'])
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'token_frequency.png'), dpi=300, bbox_inches='tight')
    
    return fig

def plot_svg_special_tokens_frequency(token_freq_df, output_dir):
    """Crea un grafico della frequenza dei token speciali SVG."""
    # Filtra solo i token speciali SVG
    svg_tokens = token_freq_df[token_freq_df['is_svg_special'] == 1]
    
    if len(svg_tokens) == 0:
        print("Nessun token speciale SVG trovato nei dati.")
        return None
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    sns.barplot(data=svg_tokens, x='token', y='frequency', color='coral', ax=ax)
    
    ax.set_title('Frequenza dei Token Speciali SVG', fontsize=16)
    ax.set_xlabel('Token Speciale', fontsize=14)
    ax.set_ylabel('Frequenza', fontsize=14)
    ax.set_xticklabels(ax.get_xticklabels(), rotation=90)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'svg_special_tokens_frequency.png'), dpi=300, bbox_inches='tight')
    
    return fig

def load_svg_examples(data_path):
    """Carica esempi SVG da un file JSON o JSONL."""
    examples = []
    
    try:
        # Determina il formato del file
        if data_path.endswith('.json'):
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                # Estrai gli SVG dal JSON
                if isinstance(data, list):
                    for item in data:
                        svg = item.get('xml_data', item.get('xml', item.get('data', '')))
                        if svg:
                            examples.append(svg)
                elif isinstance(data, dict):
                    for key, item in data.items():
                        if isinstance(item, dict):
                            svg = item.get('xml_data', item.get('xml', item.get('data', '')))
                            if svg:
                                examples.append(svg)
        
        elif data_path.endswith('.jsonl'):
            with open(data_path, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        item = json.loads(line)
                        svg = item.get('xml_data', item.get('xml', item.get('data', '')))
                        if svg:
                            examples.append(svg)
        
        print(f"Caricati {len(examples)} esempi SVG da {data_path}")
    
    except Exception as e:
        print(f"Errore nel caricamento degli esempi SVG: {e}")
        # Genera alcuni esempi di fallback
        examples = [
            '<svg width="100" height="100"><path d="M10,30 Q25,5 40,30 T70,30" fill="none" stroke="black" stroke-width="2"/></svg>',
            '<svg width="100" height="100"><circle cx="50" cy="50" r="40" stroke="black" stroke-width="2" fill="red"/></svg>',
            '<svg width="100" height="100"><rect x="10" y="10" width="80" height="80" fill="blue" stroke="black" stroke-width="2"/></svg>',
            '<svg width="100" height="100"><line x1="10" y1="10" x2="90" y2="90" stroke="black" stroke-width="2"/></svg>',
            '<svg width="100" height="100"><polygon points="50,10 90,90 10,90" fill="green" stroke="black" stroke-width="2"/></svg>'
        ]
        print(f"Utilizzando {len(examples)} esempi SVG di fallback")
    
    return examples

def main():
    parser = argparse.ArgumentParser(description="Analisi del tokenizer personalizzato per SVG")
    parser.add_argument("--model_path", type=str, default="meta-llama/Llama-3.1-8B-Instruct", 
                        help="Path al modello o nome del modello su Hugging Face")
    parser.add_argument("--data_path", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/train_set_final_xml.json", 
                        help="Path al file JSON o JSONL con esempi SVG")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/analysis/tokenizer", 
                        help="Directory per salvare i risultati dell'analisi")
    parser.add_argument("--use_custom_tokenizer", action="store_true", 
                        help="Usa il tokenizer personalizzato con token SVG")
    parser.add_argument("--use_wandb", action="store_true", 
                        help="Usa Weights & Biands per il tracking")
    parser.add_argument("--max_examples", type=int, default=100, 
                        help="Numero massimo di esempi SVG da analizzare")
    
    args = parser.parse_args()
    
    # Crea la directory di output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Inizializza W&B
    if args.use_wandb:
        setup_wandb(run_name=f"tokenizer_analysis_{'custom' if args.use_custom_tokenizer else 'standard'}")
    
    # Carica il tokenizer
    tokenizer = load_tokenizer(args.model_path, args.use_custom_tokenizer)
    
    # Carica gli esempi SVG
    svg_examples = load_svg_examples(args.data_path)
    
    # Limita il numero di esempi
    if args.max_examples > 0 and len(svg_examples) > args.max_examples:
        svg_examples = svg_examples[:args.max_examples]
    
    # Analizza la tokenizzazione
    df = analyze_svg_tokenization(tokenizer, svg_examples, args.output_dir)
    
    # Crea i grafici
    token_dist_fig = plot_token_distribution(df, args.output_dir)
    special_tokens_fig = plot_special_tokens_usage(df, args.output_dir)
    
    # Analizza la frequenza dei token
    token_freq_df = analyze_token_frequency(df, args.output_dir)
    token_freq_fig = plot_token_frequency(token_freq_df, args.output_dir)
    svg_tokens_freq_fig = plot_svg_special_tokens_frequency(token_freq_df, args.output_dir)
    
    # Carica i grafici su W&B
    if args.use_wandb:
        try:
            wandb.log({
                "token_distribution": wandb.Image(token_dist_fig),
                "special_tokens_usage": wandb.Image(special_tokens_fig),
                "token_frequency": wandb.Image(token_freq_fig)
            })
            
            if svg_tokens_freq_fig is not None:
                wandb.log({
                    "svg_special_tokens_frequency": wandb.Image(svg_tokens_freq_fig)
                })
            
            # Carica anche alcune statistiche
            wandb.log({
                "avg_tokens_per_svg": df['num_tokens'].mean(),
                "avg_unique_tokens_per_svg": df['unique_tokens'].mean(),
                "avg_svg_special_tokens_per_svg": df['num_svg_special_tokens'].mean(),
                "total_unique_tokens": len(token_freq_df),
                "total_unique_svg_special_tokens": token_freq_df['is_svg_special'].sum()
            })
            
            print("Grafici e statistiche caricati su W&B")
        except Exception as e:
            print(f"Errore nel caricamento su W&B: {e}")
    
    # Chiudi W&B
    if args.use_wandb and wandb.run is not None:
        wandb.finish()
    
    print(f"Analisi completata. Risultati salvati in {args.output_dir}")

if __name__ == "__main__":
    main()
