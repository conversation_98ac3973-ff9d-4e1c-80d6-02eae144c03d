#!/usr/bin/env python3
"""
Script di monitoraggio automatico per i training jobs Gemma e Llama.
Monitora progresso, loss, memoria GPU e stato dei job.
"""

import os
import sys
import json
import time
import subprocess
import logging
from datetime import datetime
from typing import Dict, List, Optional

# Configura il logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

class TrainingMonitor:
    def __init__(self):
        self.base_dir = "/work/tesi_ediluzio"
        self.models = {
            "gemma": {
                "output_dir": "experiments/xml_direct_input/outputs/gemma_t6_24h",
                "job_pattern": "gemma_t6",
                "target_steps": 50000,
                "initial_step": 4000
            },
            "llama": {
                "output_dir": "experiments/xml_direct_input/outputs/llama_t6_24h", 
                "job_pattern": "llama_t6",
                "target_steps": 50000,
                "initial_step": 12500
            }
        }
    
    def get_active_jobs(self) -> Dict[str, Optional[str]]:
        """Ottiene i job attivi per ogni modello"""
        try:
            result = subprocess.run(
                ["squeue", "-u", os.environ.get("USER", "ediluzio"), "--format=%i,%j,%t,%N"],
                capture_output=True, text=True, cwd=self.base_dir
            )
            
            jobs = {}
            for model in self.models:
                jobs[model] = None
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        parts = line.split(',')
                        if len(parts) >= 4:
                            job_id, job_name, status, node = parts
                            for model, config in self.models.items():
                                if config["job_pattern"] in job_name:
                                    jobs[model] = {
                                        "job_id": job_id,
                                        "status": status,
                                        "node": node
                                    }
            
            return jobs
        except Exception as e:
            logger.error(f"Errore nel recupero dei job: {e}")
            return {model: None for model in self.models}
    
    def get_latest_checkpoint(self, model: str) -> Optional[Dict]:
        """Ottiene l'ultimo checkpoint per un modello"""
        output_dir = os.path.join(self.base_dir, self.models[model]["output_dir"])
        
        if not os.path.exists(output_dir):
            return None
        
        checkpoints = [d for d in os.listdir(output_dir) if d.startswith("checkpoint-")]
        if not checkpoints:
            return None
        
        # Trova l'ultimo checkpoint
        latest = max(checkpoints, key=lambda x: int(x.split("-")[1]))
        checkpoint_path = os.path.join(output_dir, latest)
        
        # Leggi trainer_state.json
        trainer_state_path = os.path.join(checkpoint_path, "trainer_state.json")
        if os.path.exists(trainer_state_path):
            try:
                with open(trainer_state_path, 'r') as f:
                    trainer_state = json.load(f)
                
                return {
                    "checkpoint": latest,
                    "global_step": trainer_state.get("global_step", 0),
                    "train_loss": trainer_state.get("log_history", [{}])[-1].get("train_loss", "N/A"),
                    "eval_loss": trainer_state.get("log_history", [{}])[-1].get("eval_loss", "N/A"),
                    "epoch": trainer_state.get("epoch", 0)
                }
            except Exception as e:
                logger.error(f"Errore lettura trainer_state per {model}: {e}")
        
        return {"checkpoint": latest, "global_step": int(latest.split("-")[1])}
    
    def get_recent_logs(self, model: str, job_info: Dict, lines: int = 10) -> str:
        """Ottiene le ultime righe dei log"""
        if not job_info:
            return "Job non attivo"
        
        job_id = job_info["job_id"]
        log_file = os.path.join(self.base_dir, f"logs/{self.models[model]['job_pattern']}_resume_{job_id}.out")
        
        if os.path.exists(log_file):
            try:
                result = subprocess.run(
                    ["tail", f"-{lines}", log_file],
                    capture_output=True, text=True
                )
                return result.stdout if result.returncode == 0 else "Errore lettura log"
            except Exception as e:
                return f"Errore: {e}"
        
        return "Log non trovato"
    
    def print_status_report(self):
        """Stampa un report completo dello stato"""
        print("=" * 80)
        print(f"📊 TRAINING PROGRESS REPORT - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        jobs = self.get_active_jobs()
        
        for model, config in self.models.items():
            print(f"\n🤖 {model.upper()} MODEL")
            print("-" * 40)
            
            job_info = jobs.get(model)
            if job_info:
                print(f"📋 Job ID: {job_info['job_id']}")
                print(f"📊 Status: {job_info['status']}")
                print(f"🖥️  Node: {job_info['node']}")
            else:
                print("❌ No active job")
            
            # Checkpoint info
            checkpoint_info = self.get_latest_checkpoint(model)
            if checkpoint_info:
                current_step = checkpoint_info["global_step"]
                target_step = config["target_steps"]
                progress = (current_step / target_step) * 100
                
                print(f"📈 Progress: {current_step}/{target_step} ({progress:.1f}%)")
                print(f"📊 Epoch: {checkpoint_info.get('epoch', 'N/A')}")
                print(f"📉 Train Loss: {checkpoint_info.get('train_loss', 'N/A')}")
                print(f"📉 Eval Loss: {checkpoint_info.get('eval_loss', 'N/A')}")
                print(f"💾 Latest Checkpoint: {checkpoint_info['checkpoint']}")
                
                # Stima tempo rimanente
                remaining_steps = target_step - current_step
                if remaining_steps > 0 and job_info and job_info['status'] == 'R':
                    print(f"⏳ Remaining Steps: {remaining_steps}")
            else:
                print("❌ No checkpoint found")
            
            # Recent logs
            if job_info:
                print(f"\n📝 Recent Logs:")
                recent_logs = self.get_recent_logs(model, job_info, 3)
                for line in recent_logs.split('\n')[-3:]:
                    if line.strip():
                        print(f"   {line}")
        
        print("\n" + "=" * 80)
    
    def monitor_loop(self, interval: int = 300):
        """Loop di monitoraggio continuo"""
        logger.info(f"🚀 Avvio monitoraggio continuo (intervallo: {interval}s)")
        
        try:
            while True:
                self.print_status_report()
                
                # Controlla se tutti i job sono completati
                jobs = self.get_active_jobs()
                active_jobs = [job for job in jobs.values() if job is not None]
                
                if not active_jobs:
                    logger.info("🏁 Tutti i job sono completati. Terminazione monitoraggio.")
                    break
                
                logger.info(f"⏳ Prossimo aggiornamento in {interval} secondi...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            logger.info("🛑 Monitoraggio interrotto dall'utente")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Monitor training progress")
    parser.add_argument("--interval", type=int, default=300, help="Monitoring interval in seconds")
    parser.add_argument("--once", action="store_true", help="Run once and exit")
    
    args = parser.parse_args()
    
    monitor = TrainingMonitor()
    
    if args.once:
        monitor.print_status_report()
    else:
        monitor.monitor_loop(args.interval)

if __name__ == "__main__":
    main()
