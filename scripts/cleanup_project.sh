#!/bin/bash

echo "🧹 PULIZIA COMPLETA PROGETTO TESI_EDILUZIO 🧹"
echo "Data: $(date)"
echo "⚠️  ATTENZIONE: Questa operazione rimuoverà file vecchi e cache!"
echo ""

# Vai nella directory del progetto
cd /work/tesi_ediluzio

# Conferma dall'utente
read -p "Continuare con la pulizia? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Pulizia annullata dall'utente"
    exit 1
fi

echo "🚀 Inizio pulizia..."

# STEP 1: RIMOZIONE SCRIPT SLURM VECCHI E DATATI
echo ""
echo "📂 STEP 1: Rimozione script SLURM vecchi..."

# Lista script da mantenere (solo quelli funzionanti e aggiornati)
KEEP_SCRIPTS=(
    "fix_environment_complete.slurm"
    "run_llama_l4_fixed.slurm"
    "run_gemma_l4_fixed.slurm"
    "run_clip_evaluation_fixed.slurm"
    "run_baseline_evaluation_fixed.slurm"
    "generate_training_report.slurm"
    "run_count_parameters.slurm"
    "run_qualitative_evaluation.slurm"
)

# Crea backup degli script da mantenere
echo "💾 Backup script da mantenere..."
mkdir -p /tmp/slurm_backup
for script in "${KEEP_SCRIPTS[@]}"; do
    if [ -f "scripts/slurm/$script" ]; then
        cp "scripts/slurm/$script" "/tmp/slurm_backup/"
        echo "  ✅ Backup: $script"
    fi
done

# Rimuovi tutti gli script SLURM vecchi
echo "🗑️  Rimozione script vecchi..."
rm -rf scripts/slurm/*

# Ripristina script da mantenere
echo "📥 Ripristino script aggiornati..."
for script in "${KEEP_SCRIPTS[@]}"; do
    if [ -f "/tmp/slurm_backup/$script" ]; then
        cp "/tmp/slurm_backup/$script" "scripts/slurm/"
        echo "  ✅ Ripristinato: $script"
    fi
done

# Pulisci backup temporaneo
rm -rf /tmp/slurm_backup

echo "✅ Script SLURM puliti: mantenuti solo ${#KEEP_SCRIPTS[@]} script funzionanti"

# STEP 2: PULIZIA CACHE PYTHON
echo ""
echo "🐍 STEP 2: Pulizia cache Python..."

# Rimuovi cache __pycache__
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null
echo "  ✅ Cache __pycache__ rimossa"

# Rimuovi file .pyc
find . -name "*.pyc" -delete 2>/dev/null
echo "  ✅ File .pyc rimossi"

# Rimuovi file .pyo
find . -name "*.pyo" -delete 2>/dev/null
echo "  ✅ File .pyo rimossi"

# STEP 3: PULIZIA CACHE HUGGING FACE
echo ""
echo "🤗 STEP 3: Pulizia cache Hugging Face..."

# Cache modelli Hugging Face
if [ -d "$HOME/.cache/huggingface" ]; then
    # Mantieni solo i token, rimuovi cache modelli
    find "$HOME/.cache/huggingface" -name "*.bin" -delete 2>/dev/null
    find "$HOME/.cache/huggingface" -name "*.safetensors" -delete 2>/dev/null
    find "$HOME/.cache/huggingface" -type d -name "models--*" -exec rm -rf {} + 2>/dev/null
    echo "  ✅ Cache modelli Hugging Face pulita"
else
    echo "  ℹ️  Cache Hugging Face non trovata"
fi

# STEP 4: PULIZIA CACHE WANDB
echo ""
echo "📊 STEP 4: Pulizia cache Weights & Biases..."

# Rimuovi run offline vecchie
find . -type d -name "wandb" -exec find {} -name "offline-*" -type d -mtime +7 -exec rm -rf {} + \; 2>/dev/null
echo "  ✅ Run offline W&B vecchie rimosse"

# Rimuovi file temporanei wandb
find . -name ".wandb" -type d -exec rm -rf {} + 2>/dev/null
echo "  ✅ File temporanei W&B rimossi"

# STEP 5: PULIZIA LOG VECCHI
echo ""
echo "📋 STEP 5: Pulizia log vecchi..."

# Rimuovi log più vecchi di 14 giorni
find logs/ -name "*.log" -mtime +14 -delete 2>/dev/null
find logs/ -name "*.out" -mtime +14 -delete 2>/dev/null
find logs/ -name "*.err" -mtime +14 -delete 2>/dev/null
echo "  ✅ Log più vecchi di 14 giorni rimossi"

# STEP 6: PULIZIA FILE TEMPORANEI
echo ""
echo "🗂️  STEP 6: Pulizia file temporanei..."

# Rimuovi file temporanei vari
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.temp" -delete 2>/dev/null
find . -name "*~" -delete 2>/dev/null
find . -name ".DS_Store" -delete 2>/dev/null
echo "  ✅ File temporanei rimossi"

# STEP 7: PULIZIA CHECKPOINT VECCHI
echo ""
echo "💾 STEP 7: Pulizia checkpoint vecchi..."

# Mantieni solo gli ultimi 3 checkpoint per modello
for model_dir in experiments/xml_direct_input/outputs/*/; do
    if [ -d "$model_dir" ]; then
        model_name=$(basename "$model_dir")
        echo "  🔍 Controllo checkpoint per: $model_name"

        # Lista checkpoint ordinati per data (più recenti prima)
        checkpoints=($(find "$model_dir" -maxdepth 1 -type d -name "checkpoint-*" | sort -V | tail -n +4))

        # Rimuovi checkpoint vecchi (mantieni solo gli ultimi 3)
        for old_checkpoint in "${checkpoints[@]}"; do
            if [ -d "$old_checkpoint" ]; then
                rm -rf "$old_checkpoint"
                echo "    🗑️  Rimosso: $(basename "$old_checkpoint")"
            fi
        done
    fi
done

echo "  ✅ Checkpoint vecchi puliti (mantenuti ultimi 3 per modello)"

# STEP 8: PULIZIA CACHE PYTORCH
echo ""
echo "🔥 STEP 8: Pulizia cache PyTorch..."

# Cache PyTorch
if [ -d "$HOME/.cache/torch" ]; then
    rm -rf "$HOME/.cache/torch"
    echo "  ✅ Cache PyTorch rimossa"
fi

# STEP 9: PULIZIA AMBIENTI VIRTUALI VECCHI
echo ""
echo "🐍 STEP 9: Pulizia ambienti virtuali vecchi..."

# Lista ambienti da mantenere
KEEP_ENVS=("svg_env")

# Rimuovi ambienti vecchi
for env_dir in */; do
    if [[ "$env_dir" == *"env"* ]] || [[ "$env_dir" == *"venv"* ]]; then
        env_name=$(basename "$env_dir" "/")
        if [[ ! " ${KEEP_ENVS[@]} " =~ " ${env_name} " ]]; then
            if [ -d "$env_dir" ]; then
                echo "  🗑️  Rimozione ambiente vecchio: $env_name"
                rm -rf "$env_dir"
            fi
        fi
    fi
done

echo "  ✅ Ambienti virtuali vecchi rimossi"

# STEP 10: RIEPILOGO SPAZIO LIBERATO
echo ""
echo "📊 STEP 10: Calcolo spazio liberato..."

# Calcola spazio totale utilizzato
TOTAL_SIZE=$(du -sh . | cut -f1)
echo "  📏 Spazio totale progetto: $TOTAL_SIZE"

# STEP 11: CREAZIONE LISTA SCRIPT MANTENUTI
echo ""
echo "📋 STEP 11: Creazione documentazione script mantenuti..."

cat > scripts/slurm/README_SCRIPTS_MANTENUTI.md << 'EOF'
# 📋 SCRIPT SLURM MANTENUTI DOPO PULIZIA

## ✅ Script Funzionanti (Post-Pulizia)

### 🔧 Riparazione Ambiente
- `fix_environment_complete.slurm` - Riparazione completa ambiente con dipendenze compatibili

### 🚀 Training Models
- `run_llama_l4_fixed.slurm` - Training Llama 3.1 8B con ambiente riparato
- `run_gemma_l4_fixed.slurm` - Training Gemma 2 9B con ambiente riparato

### 📊 Valutazione
- `run_baseline_evaluation_fixed.slurm` - Valutazione baseline models (BLIP, ViT-GPT2, etc.)
- `run_clip_evaluation_fixed.slurm` - CLIP evaluation compatibile
- `run_qualitative_evaluation.slurm` - Valutazione qualitativa con report HTML

### 📈 Analisi e Report
- `generate_training_report.slurm` - Generazione report training
- `run_count_parameters.slurm` - Conteggio parametri modelli

## 🗑️ Script Rimossi (Obsoleti/Problematici)

Tutti gli script con problemi di dipendenze, versioni obsolete, o configurazioni non funzionanti sono stati rimossi per mantenere solo quelli testati e funzionanti.

## 🚀 Utilizzo

Per lanciare la pipeline completa:
```bash
bash scripts/launch_fixed_jobs.sh
```

Per script individuali:
```bash
sbatch scripts/slurm/<nome_script>.slurm
```
EOF

echo "  ✅ Documentazione script creata: scripts/slurm/README_SCRIPTS_MANTENUTI.md"

echo ""
echo "🎉 PULIZIA COMPLETATA! 🎉"
echo ""
echo "📊 RIEPILOGO:"
echo "  ✅ Script SLURM: mantenuti ${#KEEP_SCRIPTS[@]} script funzionanti"
echo "  ✅ Cache Python: pulita (__pycache__, .pyc, .pyo)"
echo "  ✅ Cache Hugging Face: modelli rimossi, token mantenuti"
echo "  ✅ Cache W&B: run offline vecchie rimosse"
echo "  ✅ Log: rimossi quelli più vecchi di 14 giorni"
echo "  ✅ File temporanei: rimossi"
echo "  ✅ Checkpoint: mantenuti ultimi 3 per modello"
echo "  ✅ Cache PyTorch: pulita"
echo "  ✅ Ambienti virtuali vecchi: rimossi"
echo ""
echo "🚀 Il progetto è ora pulito e ottimizzato!"
echo "📋 Vedi: scripts/slurm/README_SCRIPTS_MANTENUTI.md per la lista script mantenuti"
