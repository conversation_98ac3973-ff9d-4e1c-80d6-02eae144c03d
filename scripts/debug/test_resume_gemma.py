#!/usr/bin/env python3
"""
Script di debug per testare il resume del training Gemma
"""

import os
import sys
import torch
import json
import logging
from transformers import <PERSON>Tokenizer, AutoModelForCausalLM, TrainingArguments, Trainer
from peft import LoraConfig, get_peft_model
from datasets import Dataset

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    logger.info("🔍 DEBUG: Test resume Gemma T6")
    
    # Verifica ambiente
    logger.info(f"Python: {sys.version}")
    logger.info(f"PyTorch: {torch.__version__}")
    logger.info(f"CUDA disponibile: {torch.cuda.is_available()}")
    logger.info(f"GPU count: {torch.cuda.device_count()}")
    
    # Verifica checkpoint
    checkpoint_dir = "experiments/xml_direct_input/outputs/gemma_t6_24h/checkpoint-4000"
    if not os.path.exists(checkpoint_dir):
        logger.error(f"❌ Checkpoint non trovato: {checkpoint_dir}")
        return
    
    logger.info(f"✅ Checkpoint trovato: {checkpoint_dir}")
    
    # Lista file nel checkpoint
    files = os.listdir(checkpoint_dir)
    logger.info(f"File nel checkpoint: {files}")
    
    # Verifica trainer_state.json
    trainer_state_path = os.path.join(checkpoint_dir, "trainer_state.json")
    if os.path.exists(trainer_state_path):
        with open(trainer_state_path, 'r') as f:
            state = json.load(f)
        logger.info(f"Global step: {state.get('global_step')}")
        logger.info(f"Epoch: {state.get('epoch')}")
        logger.info(f"Max steps: {state.get('max_steps')}")
    
    # Prova a caricare il modello base
    try:
        logger.info("🔄 Caricamento modello base...")
        model = AutoModelForCausalLM.from_pretrained(
            "google/gemma-2-9b-it",
            torch_dtype=torch.bfloat16,
            device_map="auto",
            trust_remote_code=True
        )
        logger.info("✅ Modello base caricato")
        
        # Applica LoRA
        logger.info("🔄 Applicazione LoRA...")
        lora_config = LoraConfig(
            r=8,
            lora_alpha=16,
            target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
            lora_dropout=0.05,
            bias="none",
            task_type="CAUSAL_LM"
        )
        model = get_peft_model(model, lora_config)
        logger.info("✅ LoRA applicato")
        
        # Prova a caricare il checkpoint
        logger.info("🔄 Test caricamento checkpoint...")
        
        # Verifica se possiamo caricare i file del checkpoint
        adapter_path = os.path.join(checkpoint_dir, "adapter_model.safetensors")
        if os.path.exists(adapter_path):
            logger.info(f"✅ Adapter model trovato: {adapter_path}")
        else:
            logger.warning(f"⚠️ Adapter model non trovato: {adapter_path}")
            
        optimizer_path = os.path.join(checkpoint_dir, "optimizer.pt")
        if os.path.exists(optimizer_path):
            logger.info(f"✅ Optimizer state trovato: {optimizer_path}")
        else:
            logger.warning(f"⚠️ Optimizer state non trovato: {optimizer_path}")
            
        logger.info("✅ Test completato con successo")
        
    except Exception as e:
        logger.error(f"❌ Errore durante il test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
