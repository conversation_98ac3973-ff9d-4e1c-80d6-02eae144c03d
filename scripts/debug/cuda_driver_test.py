#!/usr/bin/env python3
"""
Script di test diagnostico per identificare il problema CUDA driver
"""

import os
import sys
import traceback
import subprocess

def test_basic_imports():
    """Test import base"""
    print("=" * 60)
    print("🔍 TEST 1: IMPORT BASE")
    print("=" * 60)
    
    try:
        import torch
        print(f"✅ PyTorch importato: {torch.__version__}")
        
        print(f"✅ CUDA available: {torch.cuda.is_available()}")
        print(f"✅ CUDA device count: {torch.cuda.device_count()}")
        
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                print(f"✅ GPU {i}: {torch.cuda.get_device_name(i)}")
                print(f"   Memory: {torch.cuda.get_device_properties(i).total_memory / 1e9:.1f} GB")
        
        return True
    except Exception as e:
        print(f"❌ Errore import base: {e}")
        traceback.print_exc()
        return False

def test_cuda_context():
    """Test creazione contesto CUDA"""
    print("\n" + "=" * 60)
    print("🔍 TEST 2: CONTESTO CUDA")
    print("=" * 60)
    
    try:
        import torch
        
        # Test creazione tensore su CPU
        x_cpu = torch.randn(10, 10)
        print(f"✅ Tensore CPU creato: {x_cpu.shape}")
        
        if torch.cuda.is_available():
            # Test spostamento su GPU
            device = torch.device('cuda:0')
            print(f"✅ Device CUDA: {device}")
            
            # Questo è dove spesso fallisce
            x_gpu = x_cpu.to(device)
            print(f"✅ Tensore spostato su GPU: {x_gpu.device}")
            
            # Test operazione su GPU
            y_gpu = x_gpu * 2
            print(f"✅ Operazione su GPU completata: {y_gpu.shape}")
            
            # Test spostamento back to CPU
            y_cpu = y_gpu.cpu()
            print(f"✅ Tensore riportato su CPU: {y_cpu.shape}")
            
        return True
    except Exception as e:
        print(f"❌ Errore contesto CUDA: {e}")
        traceback.print_exc()
        return False

def test_model_loading():
    """Test caricamento modello"""
    print("\n" + "=" * 60)
    print("🔍 TEST 3: CARICAMENTO MODELLO")
    print("=" * 60)
    
    try:
        import torch
        import torch.nn as nn
        
        # Test modello semplice
        model = nn.Linear(10, 5)
        print(f"✅ Modello creato: {model}")
        
        if torch.cuda.is_available():
            # Test spostamento modello su GPU
            device = torch.device('cuda:0')
            model = model.to(device)
            print(f"✅ Modello spostato su GPU: {next(model.parameters()).device}")
            
            # Test forward pass
            x = torch.randn(32, 10).to(device)
            y = model(x)
            print(f"✅ Forward pass completato: {y.shape}")
            
        return True
    except Exception as e:
        print(f"❌ Errore caricamento modello: {e}")
        traceback.print_exc()
        return False

def test_transformers_model():
    """Test caricamento modello Transformers"""
    print("\n" + "=" * 60)
    print("🔍 TEST 4: MODELLO TRANSFORMERS")
    print("=" * 60)
    
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        # Test con modello piccolo
        model_name = "gpt2"
        print(f"🔄 Caricamento {model_name}...")
        
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        print(f"✅ Tokenizer caricato")
        
        # Carica modello su CPU prima
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="cpu"
        )
        print(f"✅ Modello caricato su CPU")
        
        if torch.cuda.is_available():
            # Test spostamento su GPU
            device = torch.device('cuda:0')
            model = model.to(device)
            print(f"✅ Modello spostato su GPU")
            
            # Test tokenizzazione e inference
            text = "Hello world"
            inputs = tokenizer(text, return_tensors="pt").to(device)
            print(f"✅ Input tokenizzato e spostato su GPU")
            
            with torch.no_grad():
                outputs = model(**inputs)
            print(f"✅ Inference completata: {outputs.logits.shape}")
            
        return True
    except Exception as e:
        print(f"❌ Errore modello Transformers: {e}")
        traceback.print_exc()
        return False

def test_distributed_setup():
    """Test setup distribuito"""
    print("\n" + "=" * 60)
    print("🔍 TEST 5: SETUP DISTRIBUITO")
    print("=" * 60)
    
    try:
        import torch
        import torch.distributed as dist
        
        print(f"✅ Torch distributed available: {dist.is_available()}")
        print(f"✅ NCCL available: {dist.is_nccl_available()}")
        
        # Test environment variables
        rank = os.environ.get('RANK', '0')
        world_size = os.environ.get('WORLD_SIZE', '1')
        local_rank = os.environ.get('LOCAL_RANK', '0')
        
        print(f"✅ RANK: {rank}")
        print(f"✅ WORLD_SIZE: {world_size}")
        print(f"✅ LOCAL_RANK: {local_rank}")
        
        return True
    except Exception as e:
        print(f"❌ Errore setup distribuito: {e}")
        traceback.print_exc()
        return False

def test_system_info():
    """Test informazioni sistema"""
    print("\n" + "=" * 60)
    print("🔍 TEST 6: INFORMAZIONI SISTEMA")
    print("=" * 60)
    
    try:
        # Informazioni nodo
        hostname = subprocess.check_output(['hostname']).decode().strip()
        print(f"✅ Hostname: {hostname}")
        
        # Informazioni GPU
        try:
            nvidia_smi = subprocess.check_output(['nvidia-smi', '--query-gpu=name,driver_version,cuda_version', '--format=csv,noheader']).decode().strip()
            print(f"✅ GPU Info:\n{nvidia_smi}")
        except:
            print("❌ nvidia-smi non disponibile")
        
        # Informazioni CUDA
        try:
            nvcc_version = subprocess.check_output(['nvcc', '--version']).decode().strip()
            print(f"✅ NVCC disponibile")
        except:
            print("❌ nvcc non disponibile")
        
        # Informazioni Python/PyTorch
        import torch
        print(f"✅ Python: {sys.version}")
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA compiled version: {torch.version.cuda}")
        
        return True
    except Exception as e:
        print(f"❌ Errore informazioni sistema: {e}")
        traceback.print_exc()
        return False

def main():
    """Esegue tutti i test diagnostici"""
    print("🚀 DIAGNOSTICA CUDA DRIVER - ANALISI COMPLETA")
    print("🎯 Obiettivo: Identificare la causa reale del problema")
    
    results = []
    
    # Esegui tutti i test
    results.append(("Import Base", test_basic_imports()))
    results.append(("Contesto CUDA", test_cuda_context()))
    results.append(("Caricamento Modello", test_model_loading()))
    results.append(("Modello Transformers", test_transformers_model()))
    results.append(("Setup Distribuito", test_distributed_setup()))
    results.append(("Informazioni Sistema", test_system_info()))
    
    # Riepilogo risultati
    print("\n" + "=" * 60)
    print("📊 RIEPILOGO RISULTATI")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
    
    # Analisi
    failed_tests = [name for name, success in results if not success]
    
    if not failed_tests:
        print("\n🎉 TUTTI I TEST PASSATI!")
        print("Il problema potrebbe essere specifico del training distribuito o del modello particolare.")
    else:
        print(f"\n❌ TEST FALLITI: {', '.join(failed_tests)}")
        print("Il problema è identificato nei test sopra.")
    
    print("\n🔍 PROSSIMI PASSI:")
    if not failed_tests:
        print("- Il problema è probabilmente nel training specifico")
        print("- Testare con modello più piccolo")
        print("- Testare senza distributed training")
    else:
        print("- Risolvere i problemi identificati nei test falliti")
        print("- Contattare supporto HPC se necessario")

if __name__ == "__main__":
    main()
