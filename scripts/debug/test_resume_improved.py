#!/usr/bin/env python3
"""
Test script per verificare che il resume dai checkpoint funzioni correttamente
dopo la rimozione dei file rng_state_*.pth problematici.
"""

import os
import sys
import json
import logging

# Aggiungi la directory principale al path
sys.path.append("/work/tesi_ediluzio")

# Configura il logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_checkpoint_validity(checkpoint_dir):
    """Testa la validità di un checkpoint"""
    logger.info(f"🔍 Testing checkpoint: {checkpoint_dir}")
    
    if not os.path.exists(checkpoint_dir):
        logger.error(f"❌ Checkpoint directory non esiste: {checkpoint_dir}")
        return False
    
    # File essenziali che devono essere presenti
    required_files = [
        "adapter_model.safetensors",
        "adapter_config.json", 
        "trainer_state.json",
        "training_args.bin"
    ]
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(checkpoint_dir, file)
        if not os.path.exists(file_path):
            missing_files.append(file)
    
    if missing_files:
        logger.error(f"❌ File mancanti: {missing_files}")
        return False
    
    # Controlla se ci sono file rng_state problematici
    rng_files = [f for f in os.listdir(checkpoint_dir) if f.startswith("rng_state_") and f.endswith(".pth")]
    if rng_files:
        logger.warning(f"⚠️ File rng_state trovati (potrebbero causare problemi): {rng_files}")
    else:
        logger.info("✅ Nessun file rng_state problematico trovato")
    
    # Verifica che trainer_state.json sia valido
    try:
        with open(os.path.join(checkpoint_dir, "trainer_state.json"), "r") as f:
            trainer_state = json.load(f)
        logger.info(f"✅ trainer_state.json valido - Global step: {trainer_state.get('global_step', 'N/A')}")
    except Exception as e:
        logger.error(f"❌ Errore lettura trainer_state.json: {e}")
        return False
    
    logger.info(f"✅ Checkpoint {checkpoint_dir} è valido")
    return True

def main():
    """Test principale"""
    logger.info("🚀 Avvio test checkpoint validity...")
    
    # Test checkpoint Gemma
    gemma_checkpoint = "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma_t6_24h/checkpoint-4000"
    logger.info("\n" + "="*60)
    logger.info("TESTING GEMMA CHECKPOINT")
    logger.info("="*60)
    gemma_valid = test_checkpoint_validity(gemma_checkpoint)
    
    # Test checkpoint Llama
    llama_checkpoint = "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama_t6_24h/checkpoint-12500"
    logger.info("\n" + "="*60)
    logger.info("TESTING LLAMA CHECKPOINT")
    logger.info("="*60)
    llama_valid = test_checkpoint_validity(llama_checkpoint)
    
    # Riepilogo
    logger.info("\n" + "="*60)
    logger.info("RIEPILOGO TEST")
    logger.info("="*60)
    logger.info(f"Gemma checkpoint: {'✅ VALIDO' if gemma_valid else '❌ NON VALIDO'}")
    logger.info(f"Llama checkpoint: {'✅ VALIDO' if llama_valid else '❌ NON VALIDO'}")
    
    if gemma_valid and llama_valid:
        logger.info("🎉 Tutti i checkpoint sono validi! Pronti per il resume.")
        return True
    else:
        logger.error("❌ Alcuni checkpoint hanno problemi.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
