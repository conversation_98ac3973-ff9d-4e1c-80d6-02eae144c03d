#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import argparse
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import wandb
import glob
import re
from datetime import datetime

# Configurazione matplotlib
plt.style.use('ggplot')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

def setup_wandb(project_name="captioner", run_name="checkpoint_cleanup_analysis", entity="337543-unimore"):
    """Inizializza Weights & Biands per il tracking."""
    try:
        wandb.init(
            project=project_name,
            name=run_name,
            entity=entity,
            config={
                "analysis_type": "checkpoint_cleanup"
            }
        )
        print(f"W&B inizializzato: {wandb.run.name} (ID: {wandb.run.id})")
    except Exception as e:
        print(f"Errore nell'inizializzazione di W&B: {e}")
        print("Continuazione senza tracking W&B...")

def get_directory_size(path):
    """Calcola la dimensione di una directory in MB."""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            if os.path.exists(fp):
                total_size += os.path.getsize(fp)
    return total_size / (1024 * 1024)  # Converti in MB

def analyze_checkpoints(output_dirs):
    """Analizza i checkpoint nelle directory di output."""
    results = []
    
    for output_dir in output_dirs:
        if not os.path.isdir(output_dir):
            print(f"Directory non trovata: {output_dir}")
            continue
        
        # Trova tutti i checkpoint
        checkpoint_dirs = glob.glob(os.path.join(output_dir, "checkpoint-*"))
        
        if not checkpoint_dirs:
            print(f"Nessun checkpoint trovato in {output_dir}")
            continue
        
        # Estrai il numero di step da ogni checkpoint
        checkpoint_steps = []
        for cp_dir in checkpoint_dirs:
            match = re.search(r'checkpoint-(\d+)', cp_dir)
            if match:
                step = int(match.group(1))
                checkpoint_steps.append((cp_dir, step))
        
        # Ordina i checkpoint per step
        checkpoint_steps.sort(key=lambda x: x[1])
        
        # Calcola la dimensione di ogni checkpoint
        for cp_dir, step in checkpoint_steps:
            size_mb = get_directory_size(cp_dir)
            
            # Ottieni la data di modifica
            mod_time = os.path.getmtime(cp_dir)
            mod_date = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d %H:%M:%S')
            
            results.append({
                'output_dir': output_dir,
                'model_name': os.path.basename(output_dir),
                'checkpoint': os.path.basename(cp_dir),
                'step': step,
                'size_mb': size_mb,
                'mod_date': mod_date
            })
    
    # Crea un DataFrame
    if results:
        df = pd.DataFrame(results)
        return df
    else:
        print("Nessun risultato trovato.")
        return None

def plot_checkpoint_sizes(df, output_dir):
    """Crea un grafico delle dimensioni dei checkpoint per modello."""
    if df is None:
        return None
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Crea il grafico a barre
    sns.barplot(data=df, x='checkpoint', y='size_mb', hue='model_name', ax=ax)
    
    ax.set_title('Dimensione dei Checkpoint per Modello', fontsize=16)
    ax.set_xlabel('Checkpoint', fontsize=14)
    ax.set_ylabel('Dimensione (MB)', fontsize=14)
    ax.set_xticklabels(ax.get_xticklabels(), rotation=90)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'checkpoint_sizes.png'), dpi=300, bbox_inches='tight')
    
    return fig

def plot_checkpoint_timeline(df, output_dir):
    """Crea un grafico della timeline dei checkpoint."""
    if df is None:
        return None
    
    # Converti la data di modifica in datetime
    df['mod_datetime'] = pd.to_datetime(df['mod_date'])
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Crea un grafico a dispersione
    for model in df['model_name'].unique():
        model_df = df[df['model_name'] == model]
        ax.scatter(model_df['mod_datetime'], model_df['step'], label=model, s=100, alpha=0.7)
        
        # Collega i punti con una linea
        ax.plot(model_df['mod_datetime'], model_df['step'], alpha=0.5)
    
    ax.set_title('Timeline dei Checkpoint', fontsize=16)
    ax.set_xlabel('Data di Modifica', fontsize=14)
    ax.set_ylabel('Step', fontsize=14)
    ax.legend()
    
    # Formatta l'asse x
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'checkpoint_timeline.png'), dpi=300, bbox_inches='tight')
    
    return fig

def plot_disk_usage_reduction(df, output_dir):
    """Crea un grafico della riduzione dell'uso del disco."""
    if df is None:
        return None
    
    # Calcola l'uso totale del disco per modello
    disk_usage = df.groupby('model_name')['size_mb'].sum().reset_index()
    
    # Calcola l'uso del disco se tutti i checkpoint fossero stati mantenuti
    all_checkpoints = df.groupby(['model_name', 'step'])['size_mb'].mean().reset_index()
    all_steps = all_checkpoints['step'].unique()
    
    # Simula l'uso del disco senza pulizia
    simulated_usage = []
    for model in df['model_name'].unique():
        model_df = all_checkpoints[all_checkpoints['model_name'] == model]
        avg_size = model_df['size_mb'].mean()
        
        # Simula l'uso del disco se tutti gli step fossero stati mantenuti
        simulated_size = avg_size * len(all_steps)
        
        simulated_usage.append({
            'model_name': model,
            'simulated_size_mb': simulated_size
        })
    
    simulated_df = pd.DataFrame(simulated_usage)
    
    # Unisci i DataFrame
    merged_df = pd.merge(disk_usage, simulated_df, on='model_name')
    merged_df['saved_mb'] = merged_df['simulated_size_mb'] - merged_df['size_mb']
    merged_df['saved_percentage'] = (merged_df['saved_mb'] / merged_df['simulated_size_mb']) * 100
    
    # Crea il grafico
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Prepara i dati
    x = np.arange(len(merged_df))
    width = 0.35
    
    # Crea le barre
    rects1 = ax.bar(x - width/2, merged_df['size_mb'], width, label='Uso Attuale', color='skyblue')
    rects2 = ax.bar(x + width/2, merged_df['simulated_size_mb'], width, label='Uso Simulato (Senza Pulizia)', color='coral')
    
    ax.set_title('Riduzione dell\'Uso del Disco con Pulizia Automatica', fontsize=16)
    ax.set_xlabel('Modello', fontsize=14)
    ax.set_ylabel('Dimensione (MB)', fontsize=14)
    ax.set_xticks(x)
    ax.set_xticklabels(merged_df['model_name'], rotation=45, ha='right')
    ax.legend()
    
    # Aggiungi etichette con i valori
    def autolabel(rects):
        for rect in rects:
            height = rect.get_height()
            ax.annotate(f'{height:.1f} MB',
                        xy=(rect.get_x() + rect.get_width()/2, height),
                        xytext=(0, 3),
                        textcoords="offset points",
                        ha='center', va='bottom')
    
    autolabel(rects1)
    autolabel(rects2)
    
    # Aggiungi etichette con la percentuale di risparmio
    for i, row in enumerate(merged_df.itertuples()):
        ax.annotate(f'Risparmio: {row.saved_percentage:.1f}%',
                    xy=(i, row.size_mb + 10),
                    ha='center', va='bottom',
                    color='green', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'disk_usage_reduction.png'), dpi=300, bbox_inches='tight')
    
    return fig

def main():
    parser = argparse.ArgumentParser(description="Analisi della pulizia automatica dei checkpoint")
    parser.add_argument("--output_dirs", type=str, nargs='+', required=True,
                        help="Directory di output dei modelli da analizzare")
    parser.add_argument("--analysis_dir", type=str, default="/work/tesi_ediluzio/analysis/checkpoint_cleanup",
                        help="Directory per salvare i risultati dell'analisi")
    parser.add_argument("--use_wandb", action="store_true",
                        help="Usa Weights & Biands per il tracking")
    
    args = parser.parse_args()
    
    # Crea la directory di analisi
    os.makedirs(args.analysis_dir, exist_ok=True)
    
    # Inizializza W&B
    if args.use_wandb:
        setup_wandb()
    
    # Analizza i checkpoint
    df = analyze_checkpoints(args.output_dirs)
    
    if df is not None:
        # Salva il DataFrame
        df.to_csv(os.path.join(args.analysis_dir, 'checkpoint_analysis.csv'), index=False)
        
        # Crea i grafici
        sizes_fig = plot_checkpoint_sizes(df, args.analysis_dir)
        timeline_fig = plot_checkpoint_timeline(df, args.analysis_dir)
        disk_usage_fig = plot_disk_usage_reduction(df, args.analysis_dir)
        
        # Carica i grafici su W&B
        if args.use_wandb:
            try:
                wandb.log({
                    "checkpoint_sizes": wandb.Image(sizes_fig),
                    "checkpoint_timeline": wandb.Image(timeline_fig),
                    "disk_usage_reduction": wandb.Image(disk_usage_fig)
                })
                
                # Carica anche il DataFrame
                wandb.log({"checkpoint_analysis": wandb.Table(dataframe=df)})
                
                print("Grafici e dati caricati su W&B")
            except Exception as e:
                print(f"Errore nel caricamento su W&B: {e}")
        
        # Chiudi W&B
        if args.use_wandb and wandb.run is not None:
            wandb.finish()
        
        print(f"Analisi completata. Risultati salvati in {args.analysis_dir}")
    else:
        print("Analisi non completata a causa di errori.")

if __name__ == "__main__":
    main()
