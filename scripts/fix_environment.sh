#!/bin/bash

echo "=== FIXING ENVIRONMENT DEPENDENCIES ==="

# Attiva l'ambiente
source /work/tesi_ediluzio/svg_env/bin/activate

echo "Ambiente attivato: $VIRTUAL_ENV"

# Disinstalla pacchetti problematici
echo "Disinstallazione pacchetti problematici..."
pip uninstall -y torch torchvision torchaudio sympy transformers peft accelerate

# Reinstalla torch con versioni compatibili
echo "Reinstallazione PyTorch..."
pip install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cu118

# Reinstalla transformers e dipendenze
echo "Reinstallazione Transformers..."
pip install transformers==4.36.0

# Reinstalla altre dipendenze
echo "Reinstallazione altre dipendenze..."
pip install peft==0.7.1
pip install accelerate==0.25.0
pip install sympy==1.12

# Reinstalla dipendenze per evaluation
echo "Reinstallazione dipendenze evaluation..."
pip install nltk rouge-score py-rouge wandb cairosvg pillow

# Verifica installazione
echo "=== VERIFICA INSTALLAZIONE ==="
python -c "
import torch
import torchvision
import transformers
import peft
import accelerate
import sympy
print('✅ Torch:', torch.__version__)
print('✅ Torchvision:', torchvision.__version__)
print('✅ Transformers:', transformers.__version__)
print('✅ PEFT:', peft.__version__)
print('✅ Accelerate:', accelerate.__version__)
print('✅ Sympy:', sympy.__version__)
print('✅ CUDA available:', torch.cuda.is_available())
if torch.cuda.is_available():
    print('✅ GPU count:', torch.cuda.device_count())
    for i in range(torch.cuda.device_count()):
        print(f'✅ GPU {i}:', torch.cuda.get_device_name(i))
"

echo "=== ENVIRONMENT FIXED ==="
