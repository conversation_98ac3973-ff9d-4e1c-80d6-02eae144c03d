#!/bin/bash

echo "=== FIXING NUMPY COMPATIBILITY ==="

# Attiva l'ambiente
source /work/tesi_ediluzio/svg_env/bin/activate

echo "Ambiente attivato: $VIRTUAL_ENV"

# Downgrade NumPy a versione 1.x compatibile
echo "Downgrade NumPy a versione compatibile..."
pip install "numpy<2.0" --force-reinstall

# Verifica installazione
echo "=== VERIFICA NUMPY ==="
python -c "
import numpy as np
print('✅ NumPy version:', np.__version__)
print('✅ NumPy compatible with PyTorch')

import torch
print('✅ PyTorch version:', torch.__version__)
print('✅ CUDA available:', torch.cuda.is_available())

import accelerate
print('✅ Accelerate version:', accelerate.__version__)
"

echo "=== NUMPY FIXED ==="
