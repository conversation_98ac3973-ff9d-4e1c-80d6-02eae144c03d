#!/bin/bash

# Script per eseguire l'inferenza sul modello Gemma per SVG complex

# Imposta le variabili d'ambiente
export HF_HOME="/work/tesi_ediluzio/.cache/huggingface"
export HF_TOKEN="*************************************"
export PYTHONPATH="/work/tesi_ediluzio:${PYTHONPATH}"

# Imposta le variabili
DATA_FILE="evaluation/complex_svg_gemma.json"
OUTPUT_FILE="evaluation/zero_shot/gemma-2-9b-it_complex_zero_shot_examples.json"
MODEL="google/gemma-2-9b-it"

# Crea le directory necessarie
mkdir -p evaluation/zero_shot

# Esegui l'inferenza
echo "Esecuzione dell'inferenza sul modello $MODEL per SVG complex..."
python experiments/xml_direct_input/run_zero_shot_inference.py \
    --model_name_or_path $MODEL \
    --data_file $DATA_FILE \
    --output_file $OUTPUT_FILE \
    --do_sample \
    --temperature 0.6 \
    --top_p 0.9 \
    --max_new_tokens 150

echo "Inferenza completata. Risultati salvati in $OUTPUT_FILE"
