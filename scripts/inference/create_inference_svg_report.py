import os
import json
import argparse
from datetime import datetime

def create_inference_svg_report(llama_results_file, gemma_results_file, output_file):
    """
    Crea un report HTML con SVG diretti per i risultati dell'inferenza.
    """
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Organizza i risultati per complessità
    results_by_complexity = {
        "simple": {"llama": [], "gemma": []},
        "medium": {"llama": [], "gemma": []},
        "complex": {"llama": [], "gemma": []}
    }
    
    for result in llama_results:
        complexity = result.get("complexity", "unknown")
        if complexity in results_by_complexity:
            results_by_complexity[complexity]["llama"].append(result)
    
    for result in gemma_results:
        complexity = result.get("complexity", "unknown")
        if complexity in results_by_complexity:
            results_by_complexity[complexity]["gemma"].append(result)
    
    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Inferenza dei Modelli Base su SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 300px;
            height: 300px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .svg-container svg {
            max-width: 100%;
            max-height: 100%;
        }
        .caption {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .model-section {
            margin-top: 50px;
            margin-bottom: 30px;
        }
        .complexity-section {
            margin-bottom: 40px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        .file-name {
            font-style: italic;
            color: #666;
            margin-bottom: 10px;
        }
        .metrics {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Inferenza dei Modelli Base su SVG</h1>
    <p style="text-align: center;">Report generato il """ + datetime.now().strftime('%d/%m/%Y %H:%M:%S') + """</p>
"""
    
    # Aggiungi le sezioni per ogni complessità
    for complexity in ["simple", "medium", "complex"]:
        if not results_by_complexity[complexity]["llama"] and not results_by_complexity[complexity]["gemma"]:
            continue
            
        html += f"""
    <div class="complexity-section">
        <h2>Complessità: {complexity.capitalize()}</h2>
"""
        
        # Aggiungi la sezione per Llama
        html += """
        <div class="model-section">
            <h3>Modello: Llama 3.1 8B Instruct</h3>
"""
        
        # Aggiungi gli esempi per Llama
        for i, result in enumerate(results_by_complexity[complexity]["llama"]):
            svg_content = result.get("svg", "")
            file_name = result.get("file", "")
            generated_caption = result.get("generated_caption", "")
            inference_time = result.get("inference_time", 0)
            
            # Pulisci l'SVG per l'inclusione diretta
            if svg_content.startswith('<?xml'):
                svg_content = svg_content[svg_content.find('<svg'):]
            
            html += f"""
            <div class="example">
                <div class="file-name">{os.path.basename(file_name) if file_name else f"Esempio {complexity} {i+1}"}</div>
                <div class="svg-container">
                    {svg_content}
                </div>
                <div class="caption">
                    <strong>Didascalia generata:</strong>
                    <p>{generated_caption}</p>
                </div>
                <div class="metrics">
                    Tempo di inferenza: {inference_time:.2f}s
                </div>
            </div>
"""
        
        html += """
        </div>
"""
        
        # Aggiungi la sezione per Gemma
        html += """
        <div class="model-section">
            <h3>Modello: Gemma 2 9B IT</h3>
"""
        
        # Aggiungi gli esempi per Gemma
        for i, result in enumerate(results_by_complexity[complexity]["gemma"]):
            svg_content = result.get("svg", "")
            file_name = result.get("file", "")
            generated_caption = result.get("generated_caption", "")
            inference_time = result.get("inference_time", 0)
            
            # Pulisci l'SVG per l'inclusione diretta
            if svg_content.startswith('<?xml'):
                svg_content = svg_content[svg_content.find('<svg'):]
            
            html += f"""
            <div class="example">
                <div class="file-name">{os.path.basename(file_name) if file_name else f"Esempio {complexity} {i+1}"}</div>
                <div class="svg-container">
                    {svg_content}
                </div>
                <div class="caption">
                    <strong>Didascalia generata:</strong>
                    <p>{generated_caption}</p>
                </div>
                <div class="metrics">
                    Tempo di inferenza: {inference_time:.2f}s
                </div>
            </div>
"""
        
        html += """
        </div>
    </div>
"""
    
    # Chiudi l'HTML
    html += """
    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per l'inferenza dei modelli base su SVG.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Genera un report HTML con SVG diretti per i risultati dell'inferenza")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report HTML
    create_inference_svg_report(args.llama_results, args.gemma_results, args.output_file)
