import os
import json
import time
import argparse
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# SVG di esempio per diverse complessità
SVG_EXAMPLES = {
    "simple": """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
</svg>""",
    
    "medium": """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M256,420 L94,420 L256,420 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M284,318 L120,318 L284,318 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M310,216 L154,216 L310,216 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M174,114 L338,114 L174,114 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M72,501 L195,32 L358,32 L236,501 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M440,501 L338,110 L440,501Z" />
</svg>""",
    
    "complex": """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
    </linearGradient>
</defs>
<path style="fill:url(#grad1);stroke:rgb(0,0,0);stroke-width:2" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
</svg>"""
}

# Mappa dei modelli base
BASE_MODELS = {
    "llama": "/homes/ediluzio/.cache/huggingface/hub/models--meta-llama--Llama-3.1-8B-Instruct",
    "gemma": "/homes/ediluzio/.cache/huggingface/hub/models--google--gemma-2-9b-it"
}

def run_inference(model_name, output_file):
    """Esegue l'inferenza su un modello base dalla cache"""
    results = []
    
    # Ottieni il percorso del modello dalla cache
    model_path = BASE_MODELS.get(model_name)
    print(f"Percorso del modello: {model_path}")
    
    try:
        # Carica il tokenizer
        print("Caricamento del tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path, local_files_only=True)
        print("Tokenizer caricato con successo")
        
        # Carica il modello
        print("Caricamento del modello...")
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            local_files_only=True
        )
        print("Modello caricato con successo")
        
        # Per ogni complessità
        for complexity, svg in SVG_EXAMPLES.items():
            print(f"Inferenza su SVG di complessità {complexity}...")
            
            # Crea il prompt
            prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg}\n\n"
            
            # Tokenizza il prompt
            inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
            
            # Genera la didascalia
            start_time = time.time()
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=256,
                    do_sample=True,
                    temperature=0.7,
                    top_p=0.9,
                )
            end_time = time.time()
            
            # Decodifica la risposta
            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Rimuovi il prompt dalla risposta
            generated_caption = generated_text[len(prompt):].strip()
            
            # Calcola il tempo di inferenza
            inference_time = end_time - start_time
            
            # Crea l'esempio
            example = {
                "id": len(results),
                "complexity": complexity,
                "svg": svg,
                "true_caption": f"Esempio {complexity}",
                "generated_caption": generated_caption,
                "inference_time": inference_time,
                "model": model_name
            }
            
            # Aggiungi l'esempio ai risultati
            results.append(example)
            
            print(f"Inferenza completata in {inference_time:.2f}s")
            
            # Salva i risultati parziali
            with open(output_file, "w") as f:
                json.dump(results, f, indent=2)
        
        # Libera la memoria
        del model
        del tokenizer
        torch.cuda.empty_cache()
        
    except Exception as e:
        print(f"Errore: {e}")
        
        # Salva un risultato di errore
        example = {
            "id": len(results),
            "complexity": "error",
            "svg": "",
            "true_caption": "Errore",
            "generated_caption": f"Errore: {str(e)}",
            "inference_time": 0.0,
            "model": model_name
        }
        
        results.append(example)
        
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2)
    
    print(f"Risultati salvati in {output_file}")
    return results

def main():
    parser = argparse.ArgumentParser(description="Esegue l'inferenza su un modello base dalla cache")
    parser.add_argument("--model", type=str, required=True, choices=["llama", "gemma"], help="Modello base da utilizzare")
    parser.add_argument("--output_file", type=str, required=True, help="File JSON di output con i risultati dell'inferenza")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Pulisci la memoria
    torch.cuda.empty_cache()
    
    # Esegui l'inferenza
    run_inference(args.model, args.output_file)

if __name__ == "__main__":
    main()
