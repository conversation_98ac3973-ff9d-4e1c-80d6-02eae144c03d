#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import PeftModel

# Aggiungi la directory di lavoro al PYTHONPATH
sys.path.append('/work/tesi_ediluzio')

# Parametri
model_name = "meta-llama/Llama-3.1-8B-Instruct"
lora_path = "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token"
test_file = "/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
output_file = "/work/tesi_ediluzio/results/lora_xml/llama31_8b_lora_xml_custom_token_eval_test_manual.jsonl"
load_in_8bit = True
num_samples = 5
use_custom_tokenizer = True

# Template per il prompt zero-shot
ZERO_SHOT_PROMPT_TEMPLATE = """Genera una didascalia concisa e descrittiva per il seguente codice SVG:

{svg_data}

Didascalia:"""

print("Caricamento del dataset di test...")
try:
    with open(test_file, 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    print(f"Caricati {len(test_data)} esempi di test.")
except Exception as e:
    print(f"Errore nel caricamento del dataset: {e}")
    sys.exit(1)

# Limita il numero di campioni
if num_samples > 0:
    print(f"Limitazione a {num_samples} campioni")
    test_data = test_data[:num_samples]

print(f"Caricamento del modello: {model_name}")
try:
    # Configurazione per la quantizzazione
    quantization_config = None
    if load_in_8bit:
        quantization_config = BitsAndBytesConfig(
            load_in_8bit=True,
            bnb_4bit_compute_dtype=torch.float16
        )

    # Carica il modello base
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=quantization_config,
        device_map="auto",
        trust_remote_code=True
    )

    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        trust_remote_code=True
    )

    # Carica gli adattatori LoRA
    if lora_path:
        print(f"Caricamento degli adattatori LoRA: {lora_path}")
        model = PeftModel.from_pretrained(model, lora_path)

    print("Modello caricato con successo.")
except Exception as e:
    print(f"Errore nel caricamento del modello: {e}")
    sys.exit(1)

# Genera didascalie
results = []
for i, example in enumerate(test_data):
    svg_data = example.get("data", "")
    reference = example.get("caption", "")

    print(f"Generazione didascalia per esempio {i+1}/{len(test_data)}...")

    # Prepara il prompt
    prompt = ZERO_SHOT_PROMPT_TEMPLATE.format(svg_data=svg_data)

    # Tokenizza il prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

    # Genera la didascalia
    with torch.no_grad():
        output = model.generate(
            **inputs,
            max_new_tokens=100,
            temperature=0.7,
            top_p=0.9,
            top_k=50,
            repetition_penalty=1.1,
            pad_token_id=tokenizer.eos_token_id
        )

    # Decodifica l'output
    generated_text = tokenizer.decode(output[0], skip_special_tokens=True)

    # Estrai la didascalia dal testo generato
    generated = generated_text.split("Didascalia:")[-1].strip()

    # Salva il risultato
    results.append({
        "id": i,
        "svg": svg_data,
        "true_caption": reference,
        "generated_caption": generated
    })

    print(f"Didascalia generata: {generated}")

# Salva i risultati
print(f"Salvataggio dei risultati in: {output_file}")
try:
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in results:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    print(f"Risultati salvati con successo in: {output_file}")
except Exception as e:
    print(f"Errore nel salvataggio dei risultati: {e}")
    sys.exit(1)
