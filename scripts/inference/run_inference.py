#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per eseguire l'inferenza con modelli fine-tuned.
"""

import os
import sys
import json
import torch
import argparse
import logging
from tqdm import tqdm
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import PeftModel

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(description="Inferenza con modelli fine-tuned")
    parser.add_argument("--model_path", type=str, required=True, help="Path al modello base")
    parser.add_argument("--adapter_path", type=str, required=True, help="Path all'adapter LoRA")
    parser.add_argument("--data_file", type=str, required=True, help="Path al file di test in formato JSON")
    parser.add_argument("--output_file", type=str, required=True, help="Path al file di output per i risultati")
    parser.add_argument("--num_samples", type=int, default=-1, help="Numero di campioni da testare (-1 per tutti)")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Dispositivo da utilizzare")
    parser.add_argument("--max_length", type=int, default=1024, help="Lunghezza massima della sequenza di input (prompt + generazione). Usare con cautela, preferire max_new_tokens.")
    parser.add_argument("--max_new_tokens", type=int, default=256, help="Lunghezza massima delle nuove tokens da generare.")
    parser.add_argument("--temperature", type=float, default=0.7, help="Temperatura per la generazione")
    parser.add_argument("--top_p", type=float, default=0.9, help="Top-p per la generazione")
    parser.add_argument("--top_k", type=int, default=50, help="Top-k per la generazione")
    parser.add_argument("--batch_size", type=int, default=4, help="Batch size per l'inferenza")
    return parser.parse_args()

def load_model(model_path, adapter_path, device):
    """Carica il modello base e l'adapter LoRA."""
    logger.info(f"Caricamento del modello base da {model_path}")
    logger.info(f"Caricamento dell'adapter LoRA da {adapter_path}")

    # Configurazione per quantizzazione a 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.bfloat16,
        bnb_4bit_use_double_quant=True
    )

    # Carica il modello base
    base_model = AutoModelForCausalLM.from_pretrained(
        model_path,
        quantization_config=bnb_config,
        device_map="auto",
        trust_remote_code=True
    )

    # Carica l'adapter LoRA
    model = PeftModel.from_pretrained(base_model, adapter_path)

    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path)

    return model, tokenizer

def load_data(data_file, num_samples=-1):
    """Carica i dati di test."""
    logger.info(f"Caricamento dei dati da {data_file}")

    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    logger.info(f"Caricati {len(data)} esempi")

    if num_samples > 0 and num_samples < len(data):
        data = data[:num_samples]
        logger.info(f"Limitato a {num_samples} esempi")

    return data

def generate_caption(model, tokenizer, svg_data, max_length=512, temperature=0.7, top_p=0.9, top_k=50):
    """
    Genera una caption per un'immagine SVG.

    Args:
        model: Il modello di linguaggio
        tokenizer: Il tokenizer
        svg_data: I dati SVG in formato stringa
        max_length: Lunghezza massima della caption
        temperature: Temperatura per la generazione
        top_p: Probabilità cumulativa per il sampling
        top_k: Numero di token da considerare per il sampling

    Returns:
        str: La caption generata
    """
    # Prompt modificato per forzare l'output in inglese
    prompt = f"<svg>{svg_data}</svg>\n\nDescribe this SVG image in English:"

    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

    # Genera la caption
    outputs = model.generate(
        **inputs,
        max_new_tokens=max_length,
        temperature=temperature,
        top_p=top_p,
        top_k=top_k,
        do_sample=True,
        pad_token_id=tokenizer.eos_token_id
    )

    # Decodifica la caption
    caption = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # Rimuovi il prompt dalla caption
    caption = caption.replace(prompt, "").strip()

    return caption

def main():
    args = parse_args()

    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Carica il modello e il tokenizer
    model, tokenizer = load_model(args.model_path, args.adapter_path, args.device)

    # Carica i dati di test
    data = load_data(args.data_file, args.num_samples)

    # Genera le caption
    results = []

    for i, item in enumerate(tqdm(data, desc="Generazione caption")):
        item_id = item.get('id', f"item_{i}")
        true_caption = item.get('caption', '')

        # Ottieni i dati SVG
        svg = item.get('xml', '')

        if not svg:
            logger.warning(f"Dati SVG non trovati per l'ID {item_id}")
            continue

        # Genera la caption
        generated_caption = generate_caption(
            model, tokenizer, svg,
            max_length=args.max_length,
            temperature=args.temperature,
            top_p=args.top_p,
            top_k=args.top_k
        )

        # Crea il risultato
        result = {
            'id': item_id,
            'svg': svg,
            'true_caption': true_caption,
            'generated_caption': generated_caption
        }

        # Aggiungi il risultato alla lista
        results.append(result)

        # Log ogni 10 esempi
        if (i + 1) % 10 == 0:
            logger.info(f"Elaborati {i + 1}/{len(data)} esempi")
            logger.info(f"Esempio {item_id}:")
            logger.info(f"  Caption vera: {true_caption}")
            logger.info(f"  Caption generata: {generated_caption}")

    # Salva i risultati
    logger.info(f"Salvataggio dei risultati in {args.output_file}")

    with open(args.output_file, 'w', encoding='utf-8') as f:
        for result in results:
            f.write(json.dumps(result) + '\n')

    logger.info(f"Inferenza completata. Risultati salvati in {args.output_file}")

if __name__ == "__main__":
    main()
