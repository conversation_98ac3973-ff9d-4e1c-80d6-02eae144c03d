import os
import json
import time
import argparse
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

def load_model_and_tokenizer(model_name):
    """Carica il modello e il tokenizer"""
    print(f"Caricamento del modello {model_name}...")

    # Carica il modello e il tokenizer direttamente dal nome
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        device_map="auto"
    )

    return model, tokenizer

def generate_caption(model, tokenizer, svg_content, max_length=512):
    """Genera una didascalia per un SVG"""
    # Crea il prompt
    prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg_content}\n\n"

    # Tokenizza il prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

    # Genera la didascalia
    start_time = time.time()
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
        )
    end_time = time.time()

    # Decodifica la risposta
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # Rimuovi il prompt dalla risposta
    generated_caption = generated_text[len(prompt):].strip()

    # Calcola il tempo di inferenza
    inference_time = end_time - start_time

    return generated_caption, inference_time

def run_inference(examples_file, output_file):
    """Esegue l'inferenza sui modelli per gli esempi SVG complessi"""
    # Carica gli esempi
    with open(examples_file, "r") as f:
        examples = json.load(f)

    # Raggruppa gli esempi per modello
    examples_by_model = {}
    for example in examples:
        model = example.get("model")
        if model not in examples_by_model:
            examples_by_model[model] = []
        examples_by_model[model].append(example)

    # Esegui l'inferenza per ogni modello
    for model_name, model_examples in examples_by_model.items():
        print(f"Esecuzione dell'inferenza per il modello {model_name}...")

        # Carica il modello e il tokenizer
        model, tokenizer = load_model_and_tokenizer(model_name)

        # Esegui l'inferenza per ogni esempio
        for example in model_examples:
            svg_content = example.get("svg")

            # Genera la didascalia
            generated_caption, inference_time = generate_caption(model, tokenizer, svg_content)

            # Aggiorna l'esempio
            example["generated_caption"] = generated_caption
            example["inference_time"] = inference_time

            print(f"Esempio {example.get('id')}: inferenza completata in {inference_time:.2f}s")

    # Salva gli esempi aggiornati
    with open(output_file, "w") as f:
        json.dump(examples, f, indent=2)

    print(f"Inferenza completata. Risultati salvati in {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Esegue l'inferenza sui modelli per gli esempi SVG complessi")
    parser.add_argument("--examples_file", type=str, default="evaluation/complex_svg_examples.json", help="File JSON con gli esempi SVG complessi")
    parser.add_argument("--output_file", type=str, default="evaluation/complex_svg_results.json", help="File JSON di output con i risultati dell'inferenza")

    args = parser.parse_args()

    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Esegui l'inferenza
    run_inference(args.examples_file, args.output_file)

if __name__ == "__main__":
    main()
