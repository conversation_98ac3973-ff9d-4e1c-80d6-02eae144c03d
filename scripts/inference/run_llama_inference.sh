#!/bin/bash

# Configurazione
MODEL_PATH="meta-llama/Meta-Llama-3.1-8B-Instruct"
ADAPTER_PATH="experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/checkpoint-23900"
DATA_FILE="data/test.json"
OUTPUT_FILE="experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/inference_results.jsonl"

# Esegui l'inferenza
python scripts/inference/run_inference_optimized.py \
    --model_path "$MODEL_PATH" \
    --adapter_path "$ADAPTER_PATH" \
    --data_file "$DATA_FILE" \
    --output_file "$OUTPUT_FILE" \
    --batch_size 4 \
    --max_length 512 \
    --temperature 0.7 \
    --top_p 0.9 \
    --top_k 50 \
    --load_in_4bit
