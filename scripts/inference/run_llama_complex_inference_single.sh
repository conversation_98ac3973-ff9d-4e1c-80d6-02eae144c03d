#!/bin/bash

# Script per eseguire l'inferenza sul modello Llama per SVG complex, un esempio alla volta

# Imposta le variabili d'ambiente
export HF_HOME="/work/tesi_ediluzio/.cache/huggingface"
export HF_TOKEN="*************************************"
export PYTHONPATH="/work/tesi_ediluzio:${PYTHONPATH}"

# Imposta le variabili
DATA_DIR="evaluation/complex_svg_single"
OUTPUT_DIR="evaluation/zero_shot"
MODEL="meta-llama/Llama-3.1-8B-Instruct"
FINAL_OUTPUT="evaluation/zero_shot/Llama-3.1-8B-Instruct_complex_zero_shot_examples.json"

# Crea le directory necessarie
mkdir -p $DATA_DIR
mkdir -p $OUTPUT_DIR

# Estrai i singoli esempi dal file JSON originale
python -c "
import json
import os

# Carica il file JSON originale
with open('evaluation/complex_svg_llama.json', 'r') as f:
    data = json.load(f)

# Crea un file JSON per ogni esempio
for i, item in enumerate(data):
    with open(f'$DATA_DIR/example_{i}.json', 'w') as f:
        json.dump([item], f, indent=2)
"

# Esegui l'inferenza per ogni esempio
echo "Esecuzione dell'inferenza sul modello $MODEL per SVG complex..."
results=[]

for file in $DATA_DIR/example_*.json; do
    echo "Elaborazione di $file..."
    output_file="${file%.json}_output.json"
    
    # Esegui l'inferenza
    python experiments/xml_direct_input/run_zero_shot_inference.py \
        --model_name_or_path $MODEL \
        --data_file $file \
        --output_file $output_file \
        --do_sample \
        --temperature 0.6 \
        --top_p 0.9 \
        --max_new_tokens 150
    
    # Aggiungi i risultati all'array
    if [ -f "$output_file" ]; then
        results+=("$output_file")
    fi
done

# Combina i risultati in un unico file
python -c "
import json
import os

# Carica i risultati
results = []
for file in $results:
    if os.path.exists(file):
        with open(file, 'r') as f:
            try:
                data = json.load(f)
                results.extend(data)
            except json.JSONDecodeError:
                print(f'Errore nel decodificare {file}')

# Salva i risultati combinati
with open('$FINAL_OUTPUT', 'w') as f:
    json.dump(results, f, indent=2)
"

echo "Inferenza completata. Risultati salvati in $FINAL_OUTPUT"
