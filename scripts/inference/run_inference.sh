#!/bin/bash

cd /work/tesi_ediluzio
source svg_captioning_env/bin/activate
export HF_HOME="/work/tesi_ediluzio/.cache/huggingface"
export HF_TOKEN="*************************************"
export PYTHONPATH="${PWD}:${PYTHONPATH}"

echo "Esecuzione script di inferenza..."
python -u experiments/xml_direct_input/run_inference_unified.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --lora_path /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_quick_fixed3 \
    --test_file /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    --output_file /work/tesi_ediluzio/results/lora_xml/llama31_8b_lora_xml_eval_test.jsonl \
    --load_in_8bit \
    --num_samples 2

echo "Script completato."
echo "Verifica file di output:"
ls -la /work/tesi_ediluzio/results/lora_xml/
