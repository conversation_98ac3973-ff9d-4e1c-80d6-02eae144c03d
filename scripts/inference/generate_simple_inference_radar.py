import os
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
import re
from collections import Counter

def tokenize(text):
    """Tokenizza il testo in parole."""
    # Rimuovi punteggiatura e converti in minuscolo
    text = re.sub(r'[^\w\s]', '', text.lower())
    return text.split()

def calculate_bleu_simple(reference, hypothesis, n=1):
    """
    Calcola una versione semplificata di BLEU-n.
    """
    if not hypothesis:
        return 0.0
    
    # Tokenizza
    reference_tokens = tokenize(reference)
    hypothesis_tokens = tokenize(hypothesis)
    
    if not hypothesis_tokens:
        return 0.0
    
    # Crea n-grammi
    ref_ngrams = Counter()
    for i in range(len(reference_tokens) - n + 1):
        ref_ngrams[tuple(reference_tokens[i:i+n])] += 1
    
    hyp_ngrams = Counter()
    for i in range(len(hypothesis_tokens) - n + 1):
        hyp_ngrams[tuple(hypothesis_tokens[i:i+n])] += 1
    
    # Calcola la precisione
    matches = 0
    for ngram, count in hyp_ngrams.items():
        matches += min(count, ref_ngrams[ngram])
    
    if sum(hyp_ngrams.values()) == 0:
        return 0.0
    
    return matches / sum(hyp_ngrams.values())

def calculate_meteor_simple(reference, hypothesis):
    """
    Calcola una versione semplificata di METEOR.
    """
    if not hypothesis:
        return 0.0
    
    # Tokenizza
    reference_tokens = set(tokenize(reference))
    hypothesis_tokens = set(tokenize(hypothesis))
    
    if not hypothesis_tokens:
        return 0.0
    
    # Calcola la sovrapposizione
    overlap = len(reference_tokens.intersection(hypothesis_tokens))
    
    # Calcola la precisione e il richiamo
    precision = overlap / len(hypothesis_tokens) if hypothesis_tokens else 0
    recall = overlap / len(reference_tokens) if reference_tokens else 0
    
    # Calcola F1
    if precision + recall == 0:
        return 0.0
    
    return 2 * precision * recall / (precision + recall)

def calculate_cider_simple(references, hypotheses):
    """
    Calcola una versione semplificata di CIDEr.
    """
    scores = []
    
    for ref, hyp in zip(references, hypotheses):
        ref_tokens = tokenize(ref)
        hyp_tokens = tokenize(hyp)
        
        if not hyp_tokens:
            scores.append(0.0)
            continue
        
        # Crea n-grammi (1-4)
        ref_ngrams = set()
        hyp_ngrams = set()
        
        for n in range(1, 5):
            for i in range(len(ref_tokens) - n + 1):
                ref_ngrams.add(tuple(ref_tokens[i:i+n]))
            for i in range(len(hyp_tokens) - n + 1):
                hyp_ngrams.add(tuple(hyp_tokens[i:i+n]))
        
        # Calcola la sovrapposizione
        overlap = len(ref_ngrams.intersection(hyp_ngrams))
        union = len(ref_ngrams.union(hyp_ngrams))
        
        if union == 0:
            scores.append(0.0)
        else:
            scores.append(overlap / union)
    
    # Restituisci la media
    if not scores:
        return 0.0
    return sum(scores) / len(scores)

def evaluate_inference_results(results_file):
    """
    Valuta i risultati dell'inferenza calcolando le metriche.
    """
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    # Estrai ground truth e didascalie generate
    references = []
    hypotheses = []
    
    for result in results:
        true_caption = result.get('true_caption', '')
        generated_caption = result.get('generated_caption', '')
        
        references.append(true_caption)
        hypotheses.append(generated_caption)
    
    # Calcola le metriche
    bleu1_scores = []
    bleu2_scores = []
    bleu3_scores = []
    bleu4_scores = []
    meteor_scores = []
    
    for ref, hyp in zip(references, hypotheses):
        bleu1 = calculate_bleu_simple(ref, hyp, 1)
        bleu2 = calculate_bleu_simple(ref, hyp, 2)
        bleu3 = calculate_bleu_simple(ref, hyp, 3)
        bleu4 = calculate_bleu_simple(ref, hyp, 4)
        meteor = calculate_meteor_simple(ref, hyp)
        
        bleu1_scores.append(bleu1)
        bleu2_scores.append(bleu2)
        bleu3_scores.append(bleu3)
        bleu4_scores.append(bleu4)
        meteor_scores.append(meteor)
    
    # Calcola CIDEr
    cider_score = calculate_cider_simple(references, hypotheses)
    
    # Calcola le medie
    metrics = {
        'bleu1': sum(bleu1_scores) / len(bleu1_scores) if bleu1_scores else 0,
        'bleu2': sum(bleu2_scores) / len(bleu2_scores) if bleu2_scores else 0,
        'bleu3': sum(bleu3_scores) / len(bleu3_scores) if bleu3_scores else 0,
        'bleu4': sum(bleu4_scores) / len(bleu4_scores) if bleu4_scores else 0,
        'meteor': sum(meteor_scores) / len(meteor_scores) if meteor_scores else 0,
        'cider': cider_score
    }
    
    return metrics

def create_inference_radar_chart(llama_results_file, gemma_results_file, output_file, metrics_to_plot=None):
    """
    Crea un grafico radar per le metriche di inferenza.
    """
    # Valuta i risultati
    llama_metrics = evaluate_inference_results(llama_results_file)
    gemma_metrics = evaluate_inference_results(gemma_results_file)
    
    # Crea un dizionario con i risultati
    metrics = {
        'Llama 3.1 8B': llama_metrics,
        'Gemma 2 9B IT': gemma_metrics
    }
    
    # Definisci quali metriche plottare
    if metrics_to_plot is None:
        metrics_to_plot = ['bleu1', 'bleu2', 'bleu3', 'bleu4', 'meteor', 'cider']
    
    # Crea il grafico radar
    N = len(metrics_to_plot)
    angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il loop
    
    # Crea la figura
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(polar=True))
    
    # Aggiungi le etichette delle metriche
    plt.xticks(angles[:-1], metrics_to_plot, size=12)
    
    # Imposta i limiti dell'asse y
    ax.set_ylim(0, 1)
    
    # Plotta i dati per ogni modello
    colors = ['b', 'r']
    for i, model in enumerate(metrics.keys()):
        # Ottieni i valori per questo modello
        values = [metrics[model].get(metric, 0) for metric in metrics_to_plot]
        
        # Chiudi il loop
        values += values[:1]
        
        # Plotta i valori
        color = colors[i % len(colors)]
        ax.plot(angles, values, color=color, linewidth=2, label=model)
        ax.fill(angles, values, color=color, alpha=0.25)
        
        # Aggiungi i valori effettivi come testo
        for j, metric in enumerate(metrics_to_plot):
            value = metrics[model].get(metric, 0)
            angle = angles[j]
            radius = values[j]
            
            # Calcola la posizione del testo
            x = (radius * 1.1) * np.cos(angle)
            y = (radius * 1.1) * np.sin(angle)
            
            # Aggiungi il testo
            plt.text(angle, radius * 1.1, f"{value:.4f}", 
                     horizontalalignment='center',
                     verticalalignment='center',
                     size=8,
                     color=color)
    
    # Aggiungi la legenda
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    # Aggiungi il titolo
    plt.title('Metriche di Inferenza Zero-Shot', size=15)
    
    # Salva la figura
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Grafico radar salvato in {output_file}")
    
    # Salva anche le metriche in un file JSON
    metrics_file = os.path.splitext(output_file)[0] + "_metrics.json"
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    print(f"Metriche salvate in {metrics_file}")

def main():
    parser = argparse.ArgumentParser(description="Genera un grafico radar per le metriche di inferenza")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File immagine di output")
    parser.add_argument("--metrics", type=str, nargs="+", help="Metriche da plottare")
    
    args = parser.parse_args()
    
    # Crea il grafico radar
    create_inference_radar_chart(args.llama_results, args.gemma_results, args.output_file, args.metrics)

if __name__ == "__main__":
    main()
