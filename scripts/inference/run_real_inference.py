import os
import json
import time
import argparse
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# Definizione di SVG per diverse fasce di complessità
SVG_EXAMPLES = {
    "simple": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
</svg>""",

        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(255,255,255);stroke:rgb(255,128,0);stroke-width:2;opacity:1" d="M32,32 L480,32 L480,480 L32,480 L32,32 Z" />
<path style="fill:rgb(0,0,0);stroke:rgb(255,128,0);stroke-width:2;opacity:1" d="M96,256 L416,256 M256,96 L256,416Z" />
</svg>"""
    ],

    "medium": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M256,420 L94,420 L256,420 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M284,318 L120,318 L284,318 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M310,216 L154,216 L310,216 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M174,114 L338,114 L174,114 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M72,501 L195,32 L358,32 L236,501 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M440,501 L338,110 L440,501Z" />
</svg>""",

        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(200,255,200);stroke:rgb(0,0,0);stroke-width:2" d="M128,256 A128,128,0,1,0,384,256 A128,128,0,1,0,128,256 Z" />
<path style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2" d="M192,256 A64,64,0,1,0,320,256 A64,64,0,1,0,192,256 Z" />
</svg>"""
    ],

    "complex": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
    </linearGradient>
</defs>
<path style="fill:url(#grad1);stroke:rgb(0,0,0);stroke-width:2" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
</svg>""",

        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <filter id="blur1" x="0" y="0">
        <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
    </filter>
</defs>
<rect x="128" y="128" width="256" height="256" style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2;filter:url(#blur1)" />
</svg>"""
    ]
}

def load_model_and_tokenizer(model_name):
    """Carica il modello e il tokenizer"""
    print(f"Caricamento del modello {model_name}...")

    # Carica il modello e il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        # Rimuovi device_map per evitare la necessità di Accelerate
        low_cpu_mem_usage=False
    )

    return model, tokenizer

def generate_caption(model, tokenizer, svg_content, max_length=512):
    """Genera una didascalia per un SVG"""
    # Crea il prompt
    prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg_content}\n\n"

    # Tokenizza il prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

    # Genera la didascalia
    start_time = time.time()
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
        )
    end_time = time.time()

    # Decodifica la risposta
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # Rimuovi il prompt dalla risposta
    generated_caption = generated_text[len(prompt):].strip()

    # Calcola il tempo di inferenza
    inference_time = end_time - start_time

    return generated_caption, inference_time

def run_inference_on_all_complexities(output_file, models):
    """Esegue l'inferenza su SVG di tutte le fasce di complessità"""
    results = []

    # Per ogni modello
    for model_name in models:
        print(f"Esecuzione dell'inferenza per il modello {model_name}...")

        # Carica il modello e il tokenizer
        model, tokenizer = load_model_and_tokenizer(model_name)

        # Per ogni fascia di complessità
        for complexity, svg_list in SVG_EXAMPLES.items():
            print(f"  Fascia di complessità: {complexity}")

            # Per ogni SVG in questa fascia
            for i, svg in enumerate(svg_list):
                print(f"    Esempio {i+1}/{len(svg_list)}")

                # Genera la didascalia
                generated_caption, inference_time = generate_caption(model, tokenizer, svg)

                # Crea l'esempio
                example = {
                    "id": i,
                    "complexity": complexity,
                    "svg": svg,
                    "true_caption": f"Esempio {complexity} {i+1}",
                    "generated_caption": generated_caption,
                    "inference_time": inference_time,
                    "model": model_name
                }

                # Aggiungi l'esempio ai risultati
                results.append(example)

                print(f"      Inferenza completata in {inference_time:.2f}s")

    # Salva i risultati
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)

    print(f"Inferenza completata. Risultati salvati in {output_file}")
    return results

def main():
    parser = argparse.ArgumentParser(description="Esegue l'inferenza su SVG di tutte le fasce di complessità")
    parser.add_argument("--output_file", type=str, default="evaluation/all_complexities_results.json", help="File JSON di output con i risultati dell'inferenza")
    parser.add_argument("--models", type=str, nargs="+", default=["meta-llama/Llama-3.1-8B-Instruct", "google/gemma-2-9b-it"], help="Modelli da utilizzare")

    args = parser.parse_args()

    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Esegui l'inferenza
    run_inference_on_all_complexities(args.output_file, args.models)

if __name__ == "__main__":
    main()
