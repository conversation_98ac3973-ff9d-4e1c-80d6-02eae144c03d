#!/usr/bin/env python3
import os
import json
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
from peft import PeftModel, PeftConfig

# Configurazione
model_name = "meta-llama/Llama-3.1-8B-Instruct"
lora_path = "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token/checkpoint-600"
test_file = "/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json"
output_file = "/work/tesi_ediluzio/results/lora_xml/llama31_8b_lora_xml_custom_token_eval_test_manual.jsonl"
load_in_8bit = True
num_samples = 5

# Imposta la variabile di ambiente HF_TOKEN
os.environ["HF_TOKEN"] = "*************************************"

print(f"Loading model: {model_name}")
print(f"LoRA path: {lora_path}")

# Carica il tokenizer
tokenizer = AutoTokenizer.from_pretrained(model_name, token=os.environ.get("HF_TOKEN"))

# Aggiungi token speciali per SVG
print("Adding special tokens for SVG...")
special_tokens = [
    "<|A|>", "<|C|>", "<|L|>", "<|M|>", "<|SEP|>", "<|begin_of_path|><|M|>",
    "<|begin_of_style|>", "<|begin_of_svg|>", "<|color|>", "<|currentColor|>",
    "<|end_of_path|>", "<|end_of_style|>", "<|end_of_svg|>", "<|none|>",
    "<|opacity|>", "<|stroke-width|>", "<|stroke|>"
]
tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})
print(f"Added {len(special_tokens)} special tokens.")

# Configura il modello
print("Loading model...")
if load_in_8bit:
    print("Using 8-bit quantization...")
    quantization_config = BitsAndBytesConfig(
        load_in_8bit=True,
        llm_int8_threshold=6.0,
        llm_int8_has_fp16_weight=False
    )
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        quantization_config=quantization_config,
        device_map="auto",
        token=os.environ.get("HF_TOKEN")
    )
else:
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        device_map="auto",
        token=os.environ.get("HF_TOKEN")
    )

# Resize il modello
print("Resizing token embeddings...")
model.resize_token_embeddings(len(tokenizer))

# Carica gli adattatori LoRA
print(f"Loading LoRA adapters from {lora_path}...")
model = PeftModel.from_pretrained(model, lora_path)

# Carica i dati di test
print(f"Loading test data from {test_file}...")
with open(test_file, "r") as f:
    test_data = json.load(f)

# Limita il numero di campioni
test_data = test_data[:num_samples]
print(f"Using {len(test_data)} samples for evaluation.")

# Definisci il prompt template
def create_prompt(svg_data):
    return f"""<s>[INST] Descrivi questa immagine SVG:
{svg_data} [/INST]"""

# Esegui l'inferenza
print("Running inference...")
results = []
for i, item in enumerate(test_data):
    print(f"Processing sample {i+1}/{len(test_data)}...")
    
    # Ottieni i dati SVG
    svg_data = item.get("xml", item.get("data", ""))
    true_caption = item.get("caption", "")
    
    # Crea il prompt
    prompt = create_prompt(svg_data)
    
    # Tokenizza l'input
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
    
    # Genera la risposta
    with torch.no_grad():
        outputs = model.generate(
            input_ids=inputs.input_ids,
            attention_mask=inputs.attention_mask,
            max_new_tokens=256,
            temperature=0.7,
            top_p=0.9,
            do_sample=True
        )
    
    # Decodifica la risposta
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Estrai la risposta dal testo generato
    response = generated_text.split("[/INST]")[-1].strip()
    
    # Salva i risultati
    result = {
        "id": i,
        "svg": svg_data[:100] + "...",  # Tronca l'SVG per brevità
        "true_caption": true_caption,
        "generated_caption": response
    }
    results.append(result)
    
    print(f"Generated caption: {response}")

# Salva i risultati
print(f"Saving results to {output_file}...")
with open(output_file, "w") as f:
    for result in results:
        f.write(json.dumps(result) + "\n")

print("Done!")
