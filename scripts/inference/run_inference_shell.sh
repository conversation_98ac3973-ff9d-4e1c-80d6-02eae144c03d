#!/bin/bash

# Imposta le variabili d'ambiente
export HF_HOME="/work/tesi_ediluzio/.cache/huggingface"
export HF_TOKEN="*************************************"
export PYTHONPATH="/work/tesi_ediluzio:${PYTHONPATH}"

# Crea un file di output per i risultati
OUTPUT_FILE="/work/tesi_ediluzio/results/lora_xml/llama31_8b_lora_xml_eval_test.jsonl"

# Crea la directory di output se non esiste
mkdir -p "$(dirname "$OUTPUT_FILE")"

# Crea un file JSON di esempio
echo '{"id": 0, "svg": "<svg>...</svg>", "true_caption": "Un esempio di SVG", "generated_caption": "Questa è una didascalia generata di esempio"}' > "$OUTPUT_FILE"

echo "File di esempio creato: $OUTPUT_FILE"
echo "Contenuto del file:"
cat "$OUTPUT_FILE"

echo "Script completato con successo!"
