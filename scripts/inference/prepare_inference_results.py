#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per preparare i risultati dell'inferenza per il calcolo del CLIP Score.
"""

import os
import json
import sys

# Configurazione
PROJECT_ROOT = "/work/tesi_ediluzio"
RESULTS_FILE = f"{PROJECT_ROOT}/experiments/xml_direct_input/evaluation_results/llama31_8b_test1_convergence/checkpoint-2300/inference_results.jsonl"
OUTPUT_FILE = f"{PROJECT_ROOT}/inference_results_for_clip.json"
NUM_SAMPLES = 20  # Limitiamo a 20 campioni per velocizzare

def load_results(results_file):
    """Carica i risultati dell'inferenza da un file JSON o JSONL."""
    results = []
    
    try:
        # Determina se il file è JSON o JSONL
        with open(results_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            
            # Riavvolgi il file
            f.seek(0)
            
            if first_line.startswith('[') or first_line.endswith(']'):
                # File JSON
                results = json.load(f)
            else:
                # File JSONL
                for line in f:
                    results.append(json.loads(line))
                    
        print(f"Caricati {len(results)} risultati da {results_file}")
        return results
    except Exception as e:
        print(f"Errore nel caricamento dei risultati: {e}")
        return []

def main():
    # Carica i risultati dell'inferenza
    results = load_results(RESULTS_FILE)
    
    # Limita il numero di campioni
    if NUM_SAMPLES > 0 and NUM_SAMPLES < len(results):
        results = results[:NUM_SAMPLES]
        print(f"Limitato a {NUM_SAMPLES} campioni")
    
    # Salva i risultati in formato JSON
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    print(f"Risultati salvati in {OUTPUT_FILE}")

if __name__ == "__main__":
    main()
