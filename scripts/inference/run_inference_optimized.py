#!/usr/bin/env python3
import argparse
import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

import torch
from datasets import load_dataset
from peft import PeftModel
from tqdm import tqdm
from transformers import (
    AutoModelForCausalLM,
    AutoTokenizer,
    BitsAndBytesConfig,
    GenerationConfig,
)

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def setup_model_and_tokenizer(
    model_path: str,
    adapter_path: Optional[str] = None,
    device: str = "cuda",
    load_in_4bit: bool = True
) -> tuple:
    """
    Configura il modello e il tokenizer con le impostazioni ottimizzate.
    
    Args:
        model_path: Path del modello base
        adapter_path: Path dell'adapter LoRA (opzionale)
        device: Device su cui caricare il modello
        load_in_4bit: Se True, carica il modello in 4-bit
    
    Returns:
        tuple: (model, tokenizer)
    """
    logger.info(f"Caricamento modello base da: {model_path}")
    
    # Configurazione quantizzazione 4-bit
    if load_in_4bit:
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.bfloat16,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_use_double_quant=True
        )
    else:
        quantization_config = None

    # Carica il modello base
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        quantization_config=quantization_config,
        device_map="auto",
        trust_remote_code=True
    )

    # Carica l'adapter LoRA se specificato
    if adapter_path:
        logger.info(f"Caricamento adapter LoRA da: {adapter_path}")
        model = PeftModel.from_pretrained(model, adapter_path)
        model = model.merge_and_unload()

    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        model_path,
        trust_remote_code=True
    )
    
    # Configura il tokenizer
    tokenizer.pad_token = tokenizer.eos_token
    tokenizer.padding_side = "left"

    return model, tokenizer

def generate_caption(
    model: AutoModelForCausalLM,
    tokenizer: AutoTokenizer,
    svg_data: str,
    max_length: int = 512,
    temperature: float = 0.7,
    top_p: float = 0.9,
    top_k: int = 50
) -> str:
    """
    Genera una caption per un'immagine SVG.
    
    Args:
        model: Il modello di linguaggio
        tokenizer: Il tokenizer
        svg_data: I dati SVG in formato stringa
        max_length: Lunghezza massima della caption
        temperature: Temperatura per la generazione
        top_p: Probabilità cumulativa per il sampling
        top_k: Numero di token da considerare per il sampling
    
    Returns:
        str: La caption generata
    """
    # Prompt modificato per forzare l'output in inglese
    prompt = f"<svg>{svg_data}</svg>\n\nDescribe this SVG image in English:"
    
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

    # Configura la generazione
    generation_config = GenerationConfig(
        max_length=max_length,
        temperature=temperature,
        top_p=top_p,
        top_k=top_k,
        do_sample=True,
        pad_token_id=tokenizer.eos_token_id
    )

    # Genera la caption
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            generation_config=generation_config
        )

    # Decodifica l'output
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Estrai solo la caption (rimuovi il prompt)
    caption = generated_text.split("Describe this SVG image in English:")[-1].strip()
    
    return caption

def process_dataset(
    model: AutoModelForCausalLM,
    tokenizer: AutoTokenizer,
    data_file: str,
    output_file: str,
    batch_size: int = 4,
    max_length: int = 512,
    temperature: float = 0.7,
    top_p: float = 0.9,
    top_k: int = 50
) -> None:
    """
    Processa il dataset e genera le caption.
    
    Args:
        model: Modello per la generazione
        tokenizer: Tokenizer per il modello
        data_file: Path del file JSON con i dati
        output_file: Path del file di output
        batch_size: Dimensione del batch
        max_length: Lunghezza massima della generazione
        temperature: Temperatura per la generazione
        top_p: Probabilità cumulativa per nucleus sampling
        top_k: Numero di token da considerare per top-k sampling
    """
    logger.info(f"Caricamento dataset da: {data_file}")
    dataset = load_dataset("json", data_files=data_file)["train"]
    
    results = []
    total_examples = len(dataset)
    
    logger.info(f"Generazione caption per {total_examples} esempi")
    
    for i in tqdm(range(0, total_examples, batch_size)):
        batch = dataset[i:i + batch_size]
        
        for example in batch:
            try:
                # Genera la caption
                generated_caption = generate_caption(
                    model=model,
                    tokenizer=tokenizer,
                    svg_data=example["svg_xml"],
                    max_length=max_length,
                    temperature=temperature,
                    top_p=top_p,
                    top_k=top_k
                )
                
                # Salva il risultato
                result = {
                    "id": example["example_id"],
                    "svg": example["svg_xml"],
                    "true_caption": example["reference_caption"],
                    "generated_caption": generated_caption
                }
                results.append(result)
                
            except Exception as e:
                logger.error(f"Errore nell'elaborazione dell'esempio {example['example_id']}: {str(e)}")
                continue
    
    # Salva i risultati
    logger.info(f"Salvataggio risultati in: {output_file}")
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, "w", encoding="utf-8") as f:
        for result in results:
            f.write(json.dumps(result, ensure_ascii=False) + "\n")
    
    logger.info(f"Elaborazione completata. Generati {len(results)} risultati.")

def main():
    parser = argparse.ArgumentParser(description="Script per l'inferenza dei modelli Llama e Gemma")
    
    # Argomenti obbligatori
    parser.add_argument("--model_path", type=str, required=True,
                      help="Path del modello base (es. meta-llama/Meta-Llama-3.1-8B-Instruct)")
    parser.add_argument("--data_file", type=str, required=True,
                      help="Path del file JSON con i dati di test")
    parser.add_argument("--output_file", type=str, required=True,
                      help="Path del file di output per i risultati")
    
    # Argomenti opzionali
    parser.add_argument("--adapter_path", type=str, default=None,
                      help="Path dell'adapter LoRA (opzionale)")
    parser.add_argument("--batch_size", type=int, default=4,
                      help="Dimensione del batch (default: 4)")
    parser.add_argument("--max_length", type=int, default=512,
                      help="Lunghezza massima della generazione (default: 512)")
    parser.add_argument("--temperature", type=float, default=0.7,
                      help="Temperatura per la generazione (default: 0.7)")
    parser.add_argument("--top_p", type=float, default=0.9,
                      help="Probabilità cumulativa per nucleus sampling (default: 0.9)")
    parser.add_argument("--top_k", type=int, default=50,
                      help="Numero di token da considerare per top-k sampling (default: 50)")
    parser.add_argument("--device", type=str, default="cuda",
                      help="Device su cui eseguire l'inferenza (default: cuda)")
    parser.add_argument("--load_in_4bit", action="store_true",
                      help="Carica il modello in 4-bit (default: True)")
    
    args = parser.parse_args()
    
    # Setup del modello e tokenizer
    model, tokenizer = setup_model_and_tokenizer(
        model_path=args.model_path,
        adapter_path=args.adapter_path,
        device=args.device,
        load_in_4bit=args.load_in_4bit
    )
    
    # Processa il dataset
    process_dataset(
        model=model,
        tokenizer=tokenizer,
        data_file=args.data_file,
        output_file=args.output_file,
        batch_size=args.batch_size,
        max_length=args.max_length,
        temperature=args.temperature,
        top_p=args.top_p,
        top_k=args.top_k
    )

if __name__ == "__main__":
    main() 