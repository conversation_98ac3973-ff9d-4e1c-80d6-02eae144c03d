import os
import json
import time
import argparse
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from glob import glob

# Mappa dei modelli base
BASE_MODELS = {
    "llama": "meta-llama/Llama-3.1-8B-Instruct",
    "gemma": "google/gemma-2-9b-it"
}

def load_svg_dataset(dataset_dir):
    """Carica tutti gli SVG dal dataset, organizzati per complessità"""
    svg_files = glob(os.path.join(dataset_dir, "**/*.svg"), recursive=True)
    
    # Organizza gli SVG per complessità
    svg_by_complexity = {
        "simple": [],
        "medium": [],
        "complex": []
    }
    
    # Determina la complessità in base alla dimensione del file
    for svg_file in svg_files:
        file_size = os.path.getsize(svg_file)
        
        # Leggi il contenuto dell'SVG
        with open(svg_file, "r") as f:
            svg_content = f.read()
        
        # Determina la complessità in base alla dimensione e al contenuto
        if file_size < 1000 and svg_content.count("<path") <= 2:
            complexity = "simple"
        elif file_size > 5000 or "linearGradient" in svg_content or "filter" in svg_content:
            complexity = "complex"
        else:
            complexity = "medium"
        
        # Aggiungi l'SVG alla categoria appropriata
        svg_by_complexity[complexity].append({
            "file": svg_file,
            "content": svg_content
        })
    
    # Limita il numero di SVG per categoria
    for complexity in svg_by_complexity:
        if len(svg_by_complexity[complexity]) > 5:
            svg_by_complexity[complexity] = svg_by_complexity[complexity][:5]
    
    return svg_by_complexity

def run_inference(model_name, dataset_dir, output_file, hf_token=None):
    """Esegue l'inferenza su un modello base di Hugging Face"""
    results = []
    
    # Ottieni il nome completo del modello
    full_model_name = BASE_MODELS.get(model_name, model_name)
    print(f"Modello: {full_model_name}")
    
    # Carica il dataset
    print("Caricamento del dataset...")
    svg_by_complexity = load_svg_dataset(dataset_dir)
    print(f"Dataset caricato: {sum(len(svgs) for svgs in svg_by_complexity.values())} SVG totali")
    
    try:
        # Carica il tokenizer
        print("Caricamento del tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(full_model_name, token=hf_token)
        print("Tokenizer caricato con successo")
        
        # Carica il modello
        print("Caricamento del modello...")
        model = AutoModelForCausalLM.from_pretrained(
            full_model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            token=hf_token
        )
        print("Modello caricato con successo")
        
        # Per ogni complessità
        for complexity, svg_list in svg_by_complexity.items():
            print(f"Inferenza su {len(svg_list)} SVG di complessità {complexity}...")
            
            # Per ogni SVG in questa complessità
            for i, svg_data in enumerate(svg_list):
                svg_file = svg_data["file"]
                svg_content = svg_data["content"]
                
                print(f"  Inferenza su {os.path.basename(svg_file)} ({i+1}/{len(svg_list)})...")
                
                # Crea il prompt
                prompt = f"Descrivi dettagliatamente questa immagine SVG:\n{svg_content}\n\n"
                
                # Tokenizza il prompt
                inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
                
                # Genera la didascalia
                start_time = time.time()
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=256,
                        do_sample=True,
                        temperature=0.7,
                        top_p=0.9,
                    )
                end_time = time.time()
                
                # Decodifica la risposta
                generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
                
                # Rimuovi il prompt dalla risposta
                generated_caption = generated_text[len(prompt):].strip()
                
                # Calcola il tempo di inferenza
                inference_time = end_time - start_time
                
                # Crea l'esempio
                example = {
                    "id": len(results),
                    "file": svg_file,
                    "complexity": complexity,
                    "svg": svg_content,
                    "true_caption": os.path.basename(svg_file),
                    "generated_caption": generated_caption,
                    "inference_time": inference_time,
                    "model": full_model_name
                }
                
                # Aggiungi l'esempio ai risultati
                results.append(example)
                
                print(f"    Inferenza completata in {inference_time:.2f}s")
                
                # Salva i risultati parziali
                with open(output_file, "w") as f:
                    json.dump(results, f, indent=2)
        
        # Libera la memoria
        del model
        del tokenizer
        torch.cuda.empty_cache()
        
    except Exception as e:
        print(f"Errore: {e}")
        
        # Salva un risultato di errore
        example = {
            "id": len(results),
            "file": "",
            "complexity": "error",
            "svg": "",
            "true_caption": "Errore",
            "generated_caption": f"Errore: {str(e)}",
            "inference_time": 0.0,
            "model": full_model_name
        }
        
        results.append(example)
        
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2)
    
    print(f"Inferenza completata. Risultati salvati in {output_file}")
    return results

def main():
    parser = argparse.ArgumentParser(description="Esegue l'inferenza su un modello base di Hugging Face")
    parser.add_argument("--model", type=str, required=True, choices=["llama", "gemma"], help="Modello base da utilizzare")
    parser.add_argument("--dataset_dir", type=str, required=True, help="Directory contenente il dataset SVG")
    parser.add_argument("--output_file", type=str, required=True, help="File JSON di output con i risultati dell'inferenza")
    parser.add_argument("--hf_token", type=str, help="Token di Hugging Face per accedere ai modelli")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Pulisci la memoria
    torch.cuda.empty_cache()
    
    # Esegui l'inferenza
    run_inference(args.model, args.dataset_dir, args.output_file, args.hf_token)

if __name__ == "__main__":
    main()
