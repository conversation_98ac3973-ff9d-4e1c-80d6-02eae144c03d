import os
import json
import time
import argparse
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# Definizione di SVG per diverse fasce di complessità
SVG_EXAMPLES = {
    "simple": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
</svg>"""
    ],
    
    "medium": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M256,420 L94,420 L256,420 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M284,318 L120,318 L284,318 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M310,216 L154,216 L310,216 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M174,114 L338,114 L174,114 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M72,501 L195,32 L358,32 L236,501 Z" />
<path style="fill:none;stroke:rgb(2,2,2);stroke-width:1;opacity:1" d="M440,501 L338,110 L440,501Z" />
</svg>"""
    ],
    
    "complex": [
        """<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
    </linearGradient>
</defs>
<path style="fill:url(#grad1);stroke:rgb(0,0,0);stroke-width:2" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
</svg>"""
    ]
}

def load_model_and_tokenizer(model_name):
    """Carica il modello e il tokenizer con impostazioni per ridurre l'uso di memoria"""
    print(f"Caricamento del modello {model_name}...")
    
    # Carica il tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    # Carica il modello con opzioni per ridurre l'uso di memoria
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,  # Usa precisione ridotta
        device_map="auto",
        low_cpu_mem_usage=True,
        max_memory={0: "8GiB"},  # Limita l'uso di memoria GPU
        offload_folder="offload",  # Offload su disco se necessario
    )
    
    return model, tokenizer

def generate_caption(model, tokenizer, svg_content, max_length=128):
    """Genera una didascalia per un SVG con impostazioni leggere"""
    # Crea il prompt
    prompt = f"Descrivi questa immagine SVG:\n{svg_content}\n\n"
    
    # Tokenizza il prompt
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
    
    # Genera la didascalia con impostazioni per ridurre l'uso di memoria
    start_time = time.time()
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,  # Limita la lunghezza dell'output
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            num_beams=1,  # Usa beam search più semplice
        )
    end_time = time.time()
    
    # Decodifica la risposta
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Rimuovi il prompt dalla risposta
    generated_caption = generated_text[len(prompt):].strip()
    
    # Calcola il tempo di inferenza
    inference_time = end_time - start_time
    
    return generated_caption, inference_time

def run_inference_on_model(model_name, output_file):
    """Esegue l'inferenza su un singolo modello"""
    results = []
    
    try:
        # Carica il modello e il tokenizer
        model, tokenizer = load_model_and_tokenizer(model_name)
        
        # Per ogni fascia di complessità
        for complexity, svg_list in SVG_EXAMPLES.items():
            print(f"  Fascia di complessità: {complexity}")
            
            # Per ogni SVG in questa fascia
            for i, svg in enumerate(svg_list):
                print(f"    Esempio {i+1}/{len(svg_list)}")
                
                try:
                    # Genera la didascalia
                    generated_caption, inference_time = generate_caption(model, tokenizer, svg)
                    
                    # Crea l'esempio
                    example = {
                        "id": i,
                        "complexity": complexity,
                        "svg": svg,
                        "true_caption": f"Esempio {complexity} {i+1}",
                        "generated_caption": generated_caption,
                        "inference_time": inference_time,
                        "model": model_name
                    }
                    
                    # Aggiungi l'esempio ai risultati
                    results.append(example)
                    
                    print(f"      Inferenza completata in {inference_time:.2f}s")
                    
                    # Salva i risultati parziali dopo ogni inferenza
                    with open(output_file, "w") as f:
                        json.dump(results, f, indent=2)
                except Exception as e:
                    print(f"      Errore durante l'inferenza: {e}")
        
        # Libera la memoria
        del model
        del tokenizer
        torch.cuda.empty_cache()
        
    except Exception as e:
        print(f"Errore durante il caricamento del modello: {e}")
    
    # Salva i risultati finali
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"Inferenza completata per il modello {model_name}. Risultati salvati in {output_file}")
    return results

def main():
    parser = argparse.ArgumentParser(description="Esegue l'inferenza su un singolo modello con impostazioni leggere")
    parser.add_argument("--model", type=str, required=True, help="Modello da utilizzare")
    parser.add_argument("--output_file", type=str, required=True, help="File JSON di output con i risultati dell'inferenza")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea la directory per l'offload se non esiste
    os.makedirs("offload", exist_ok=True)
    
    # Esegui l'inferenza
    run_inference_on_model(args.model, args.output_file)

if __name__ == "__main__":
    main()
