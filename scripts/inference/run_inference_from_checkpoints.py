#!/usr/bin/env python3
"""
Script per eseguire inferenza usando checkpoint LoRA locali senza scaricare modelli base.
Usa i modelli già salvati localmente.
"""

import argparse
import json
import logging
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
from tqdm import tqdm
import os

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_model_from_checkpoint(checkpoint_path, device="auto"):
    """Carica il modello direttamente dal checkpoint LoRA locale."""
    logger.info(f"Caricamento modello da checkpoint: {checkpoint_path}")
    
    # Carica il modello LoRA direttamente dal checkpoint
    # Il checkpoint contiene già il modello base + adapter
    try:
        model = AutoModelForCausalLM.from_pretrained(
            checkpoint_path,
            torch_dtype=torch.float16,
            device_map=device,
            trust_remote_code=True
        )
        
        tokenizer = AutoTokenizer.from_pretrained(checkpoint_path)
        
        # Assicurati che il tokenizer abbia un pad_token
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        logger.info(f"Modello caricato con successo da {checkpoint_path}")
        return model, tokenizer
        
    except Exception as e:
        logger.error(f"Errore nel caricamento da checkpoint: {e}")
        logger.info("Tentativo di caricamento come adapter LoRA...")
        
        # Fallback: prova a caricare come adapter
        # Determina il modello base dal path
        if "llama" in checkpoint_path.lower():
            base_model_name = "meta-llama/Llama-3.1-8B-Instruct"
        elif "gemma" in checkpoint_path.lower():
            base_model_name = "google/gemma-2-9b-it"
        else:
            raise ValueError(f"Impossibile determinare il modello base da {checkpoint_path}")
        
        # Carica modello base e adapter
        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.float16,
            device_map=device,
            trust_remote_code=True
        )
        
        model = PeftModel.from_pretrained(base_model, checkpoint_path)
        tokenizer = AutoTokenizer.from_pretrained(base_model_name)
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        logger.info(f"Modello caricato come adapter LoRA da {checkpoint_path}")
        return model, tokenizer

def generate_caption(model, tokenizer, svg_xml, max_length=150, temperature=0.7, top_p=0.9, top_k=50):
    """Genera una caption per un SVG."""
    
    # Crea il prompt
    prompt = f"""<svg_xml>
{svg_xml}
</svg_xml>

Describe this SVG image in detail:"""
    
    # Tokenizza
    inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
    inputs = {k: v.to(model.device) for k, v in inputs.items()}
    
    # Genera
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_length,
            temperature=temperature,
            top_p=top_p,
            top_k=top_k,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id
        )
    
    # Decodifica
    full_response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Rimuovi il prompt dalla risposta
    caption = full_response.replace(prompt, "").strip()
    
    return caption

def run_inference(checkpoint_path, test_file, output_file, device="auto"):
    """Esegue inferenza su un dataset di test."""
    
    # Carica il modello
    model, tokenizer = load_model_from_checkpoint(checkpoint_path, device)
    model.eval()
    
    # Carica i dati di test
    logger.info(f"Caricamento dati da {test_file}")
    with open(test_file, 'r') as f:
        test_data = json.load(f)
    
    logger.info(f"Caricati {len(test_data)} esempi")
    
    # Esegui inferenza
    results = []
    
    for i, example in enumerate(tqdm(test_data, desc="Generazione caption")):
        try:
            # Estrai SVG
            svg_xml = example.get('xml', example.get('svg_xml', ''))
            if not svg_xml:
                logger.warning(f"Nessun XML trovato nell'esempio {i}")
                continue
            
            # Genera caption
            generated_caption = generate_caption(model, tokenizer, svg_xml)
            
            # Salva risultato
            result = {
                'id': example.get('id', example.get('example_id', f'example_{i}')),
                'reference_caption': example.get('caption', example.get('reference_caption', '')),
                'generated_caption': generated_caption,
                'svg_xml': svg_xml
            }
            
            results.append(result)
            
            # Log ogni 10 esempi
            if (i + 1) % 10 == 0:
                logger.info(f"Elaborati {i + 1}/{len(test_data)} esempi")
                logger.info(f"Esempio {result['id']}:")
                logger.info(f"  Caption vera: {result['reference_caption'][:100]}...")
                logger.info(f"  Caption generata: {generated_caption[:100]}...")
            
        except Exception as e:
            logger.error(f"Errore nell'esempio {i}: {e}")
            continue
    
    # Salva risultati
    logger.info(f"Salvataggio risultati in {output_file}")
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w') as f:
        for result in results:
            f.write(json.dumps(result) + '\n')
    
    logger.info(f"Inferenza completata. {len(results)} risultati salvati in {output_file}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='Inferenza da checkpoint LoRA locali')
    parser.add_argument('--checkpoint_path', required=True, help='Path del checkpoint LoRA')
    parser.add_argument('--test_file', required=True, help='File di test JSON')
    parser.add_argument('--output_file', required=True, help='File di output JSONL')
    parser.add_argument('--device', default='auto', help='Device per il modello')
    parser.add_argument('--max_length', type=int, default=150, help='Lunghezza massima caption')
    parser.add_argument('--temperature', type=float, default=0.7, help='Temperature per generazione')
    parser.add_argument('--top_p', type=float, default=0.9, help='Top-p per generazione')
    parser.add_argument('--top_k', type=int, default=50, help='Top-k per generazione')
    
    args = parser.parse_args()
    
    logger.info(f"Inizio inferenza da checkpoint: {args.checkpoint_path}")
    
    results = run_inference(
        args.checkpoint_path,
        args.test_file,
        args.output_file,
        args.device
    )
    
    logger.info(f"Inferenza completata con successo! {len(results)} esempi processati.")

if __name__ == '__main__':
    main()
