import os
import re

# Directory contenente gli SVG
svg_dir = "/work/tesi_ediluzio/evaluation/reports/svg_images"

# Funzione per correggere il formato SVG
def fix_svg_file(file_path):
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Correggi il formato del path
    content = content.replace('d=""d=', 'd="')
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"Corretto: {file_path}")

# Correggi tutti i file SVG nella directory
for filename in os.listdir(svg_dir):
    if filename.endswith(".svg"):
        file_path = os.path.join(svg_dir, filename)
        fix_svg_file(file_path)
