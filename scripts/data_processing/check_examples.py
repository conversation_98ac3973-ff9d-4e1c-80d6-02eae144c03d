import json

# Carica gli esempi
with open('evaluation/zero_shot/zero_shot_examples.json', 'r') as f:
    examples = json.load(f)

# Conta le complessità
complexities = {}
for ex in examples:
    c = ex.get('complexity', 'unknown')
    complexities[c] = complexities.get(c, 0) + 1

print('Distribuzione complessità:', complexities)

# Controlla se ci sono SVG con token complessi
complex_tokens = ['linearGradient', 'radialGradient', 'filter', 'feGaussianBlur', 'pattern', 'clipPath']
examples_with_complex_tokens = 0

for ex in examples:
    svg = ex.get('svg', '')
    has_complex_token = any(token in svg for token in complex_tokens)
    if has_complex_token:
        examples_with_complex_tokens += 1

print(f'Esempi con token complessi: {examples_with_complex_tokens} su {len(examples)}')

# Mostra alcuni esempi per ogni complessità
for complexity in complexities:
    print(f"\nEsempi di complessità '{complexity}':")
    count = 0
    for ex in examples:
        if ex.get('complexity') == complexity and count < 2:
            print(f"  Modello: {ex.get('model')}")
            print(f"  ID: {ex.get('id')}")
            print(f"  Ground Truth: {ex.get('true_caption')}")
            print(f"  Generated: {ex.get('generated_caption')[:100]}...")
            print(f"  Inference Time: {ex.get('inference_time')}")
            print()
            count += 1
