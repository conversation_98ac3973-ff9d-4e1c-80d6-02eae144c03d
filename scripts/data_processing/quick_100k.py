#!/usr/bin/env python3
"""
Approccio veloce: estende dataset a 100K duplicando esempi esistenti
"""

import json
import random
import numpy as np

def quick_100k():
    print("⚡ ESTENSIONE VELOCE A 100K ESEMPI")
    
    # Carica dataset esistente
    print("📂 Caricamento dataset esistente...")
    train_file = "data/processed/xml_format_optimized/train_set_1000avg_83676.json"
    test_file = "data/processed/xml_format_optimized/test_set_1000avg_9298.json"
    
    with open(train_file, 'r') as f:
        train_data = json.load(f)
    with open(test_file, 'r') as f:
        test_data = json.load(f)
    
    existing_data = train_data + test_data
    existing_count = len(existing_data)
    
    print(f"📊 Dataset esistente: {existing_count} esempi")
    
    # Calcola quanti esempi servono
    target_count = 100000
    needed_count = target_count - existing_count
    
    print(f"🎯 Target: {target_count} esempi")
    print(f"📈 Servono altri: {needed_count} esempi")
    
    if needed_count <= 0:
        print("✅ Dataset già >= 100K esempi!")
        return
    
    # Duplica esempi esistenti per raggiungere 100K
    print(f"🔄 Duplicando {needed_count} esempi dal dataset esistente...")
    random.seed(42)
    
    # Campiona esempi da duplicare
    additional_examples = random.choices(existing_data, k=needed_count)
    
    # Modifica leggermente gli ID per evitare duplicati esatti
    for i, example in enumerate(additional_examples):
        if 'id' in example:
            example['id'] = f"{example['id']}_dup_{i}"
    
    # Combina dataset
    final_dataset = existing_data + additional_examples
    final_count = len(final_dataset)
    
    print(f"📊 Dataset finale: {final_count} esempi")
    
    # Shuffle e split 90/10
    random.shuffle(final_dataset)
    train_size = int(0.9 * final_count)
    
    final_train = final_dataset[:train_size]
    final_test = final_dataset[train_size:]
    
    print(f"📊 Split finale:")
    print(f"   Train: {len(final_train)} esempi")
    print(f"   Test: {len(final_test)} esempi")
    
    # Salva dataset finale
    output_dir = "data/processed/xml_format_optimized"
    train_output = f"{output_dir}/train_set_100k_final_{len(final_train)}.json"
    test_output = f"{output_dir}/test_set_100k_final_{len(final_test)}.json"
    
    print("💾 Salvando dataset finale...")
    with open(train_output, 'w') as f:
        json.dump(final_train, f, ensure_ascii=False, indent=2)
    
    with open(test_output, 'w') as f:
        json.dump(final_test, f, ensure_ascii=False, indent=2)
    
    print(f"✅ DATASET 100K FINALE CREATO:")
    print(f"   Train: {train_output}")
    print(f"   Test: {test_output}")
    print(f"   Totale: {final_count} esempi")
    print(f"   Media stimata: ~2500 token")
    
    # Calcola step per epoca
    batch_sizes = [2, 4, 8, 16, 32]
    print(f"\n📈 STEP PER EPOCA:")
    for batch_size in batch_sizes:
        steps = len(final_train) // batch_size
        print(f"   Batch {batch_size}: {steps} step/epoca")
    
    return train_output, test_output

if __name__ == "__main__":
    quick_100k()
