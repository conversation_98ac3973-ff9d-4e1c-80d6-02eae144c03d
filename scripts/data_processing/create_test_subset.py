import json
import random
import os
from pathlib import Path

def create_test_subset(input_file, output_file, num_samples=10):
    """
    Crea un subset di test dal dataset originale
    """
    # Carica il dataset
    with open(input_file, 'r') as f:
        dataset = json.load(f)
    
    # Seleziona casualmente num_samples esempi
    test_subset = random.sample(dataset, num_samples)
    
    # Salva il subset
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(test_subset, f, indent=2)
    
    print(f"Creato subset di test con {len(test_subset)} esempi in {output_file}")

if __name__ == "__main__":
    # Percorsi file
    input_file = "data/processed/xml_format/test_set_final_2k_xml.json"
    output_file = "data/processed/test_subset/test_subset.json"
    
    # Crea subset
    create_test_subset(input_file, output_file, num_samples=10) 