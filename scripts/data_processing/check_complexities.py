import json

# Carica gli esempi
with open('evaluation/zero_shot/zero_shot_examples.json', 'r') as f:
    examples = json.load(f)

# Trova tutte le complessità
complexities = set()
for ex in examples:
    complexities.add(ex.get('complexity', 'unknown'))

print('Complessità presenti:', complexities)

# Controlla se ci sono SVG con token complessi
complex_tokens = ['linearGradient', 'radialGradient', 'filter', 'feGaussianBlur', 'pattern', 'clipPath', 'stdDeviation']
examples_with_complex_tokens = []

for ex in examples:
    svg = ex.get('svg', '')
    has_complex_token = any(token in svg for token in complex_tokens)
    if has_complex_token:
        examples_with_complex_tokens.append(ex)

print(f'Esempi con token complessi: {len(examples_with_complex_tokens)} su {len(examples)}')

if examples_with_complex_tokens:
    print("\nEsempi con token complessi:")
    for ex in examples_with_complex_tokens[:3]:  # Mostra solo i primi 3
        print(f"  ID: {ex.get('id')}")
        print(f"  Complessità: {ex.get('complexity')}")
        print(f"  SVG: {ex.get('svg')[:100]}...")
        print()
