import json
import argparse
from pathlib import Path
import cairosvg
import io
from PIL import Image
from tqdm import tqdm
import os

def rasterize_svg(svg_data, dpi=150):
    """Converte SVG in immagine PNG."""
    png_data = cairosvg.svg2png(bytestring=svg_data.encode('utf-8'), dpi=dpi)
    return Image.open(io.BytesIO(png_data))

def process_file(input_file, output_dir, dpi=150):
    """Elabora il file JSON contenente gli SVG e li rasterizza."""
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Leggi il file JSON
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    # Processa ogni SVG
    for example in tqdm(data, desc="Rasterizzazione SVG"):
        if 'xml' in example:
            svg = example['xml']
            output_file = os.path.join(output_dir, f"{example['id']}.png")
            
            try:
                # Converti SVG in PNG
                cairosvg.svg2png(bytestring=svg.encode('utf-8'),
                               write_to=output_file,
                               dpi=dpi)
            except Exception as e:
                print(f"Errore nella conversione di {example['id']}: {str(e)}")
    
    print(f"Rasterizzazione completata. Immagini salvate in: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description='Rasterizza file SVG in PNG')
    parser.add_argument('--input_file', required=True, help='File JSON di input')
    parser.add_argument('--output_dir', required=True, help='Directory di output per i PNG')
    parser.add_argument('--dpi', type=int, default=150, help='DPI per la rasterizzazione')
    args = parser.parse_args()
    
    process_file(args.input_file, args.output_dir, args.dpi)

if __name__ == '__main__':
    main() 