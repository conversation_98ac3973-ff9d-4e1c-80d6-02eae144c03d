import json
import os
import re

# Crea la directory di output se non esiste
output_dir = "/work/tesi_ediluzio/evaluation/reports/svg_images"
os.makedirs(output_dir, exist_ok=True)

# Leggi il file di inferenza
inference_file = "/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/llama31_8b_test1_convergence/checkpoint-2300/inference_results.jsonl"

# Esempi selezionati per categoria
simple_examples = [45, 21, 2]  # Cerchio blu, Barra verticale verde, Triangolo nero
medium_examples = [18, 13, 20]  # Freccia, Fulmine, Bandiera
complex_examples = [40, 23, 17]  # <PERSON><PERSON><PERSON> di riciclo, Lettera M, Design astratto

# Funzione per estrarre e salvare l'SVG
def extract_and_save_svg(data, index, category, example_num):
    item = data[index]
    svg_content = item["svg"]
    
    # Pulisci l'SVG per renderlo visualizzabile
    svg_content = svg_content.replace("d=", "d=\"").replace(" Z\"", " Z\"")
    
    # Salva l'SVG
    filename = f"{category}_example_{example_num}.svg"
    filepath = os.path.join(output_dir, filename)
    
    with open(filepath, "w") as f:
        f.write(svg_content)
    
    print(f"Salvato {filepath}")
    
    return {
        "filename": filename,
        "true_caption": item["true_caption"],
        "generated_caption": item["generated_caption"].split("</s>")[0]
    }

# Leggi il file di inferenza
data = []
with open(inference_file, "r") as f:
    for line in f:
        data.append(json.loads(line))

# Estrai e salva gli SVG
examples = {}

# SVG semplici
examples["simple"] = []
for i, idx in enumerate(simple_examples):
    examples["simple"].append(extract_and_save_svg(data, idx, "simple", i+1))

# SVG di media complessità
examples["medium"] = []
for i, idx in enumerate(medium_examples):
    examples["medium"].append(extract_and_save_svg(data, idx, "medium", i+1))

# SVG complessi
examples["complex"] = []
for i, idx in enumerate(complex_examples):
    examples["complex"].append(extract_and_save_svg(data, idx, "complex", i+1))

# Salva le informazioni sugli esempi in un file JSON
examples_file = os.path.join(output_dir, "examples.json")
with open(examples_file, "w") as f:
    json.dump(examples, f, indent=2)

print(f"Informazioni sugli esempi salvate in {examples_file}")
