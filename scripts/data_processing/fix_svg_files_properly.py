import os
import re

# Directory contenente gli SVG
svg_dir = "/work/tesi_ediluzio/evaluation/reports/svg_images"

# Funzione per correggere il formato SVG
def fix_svg_file(file_path):
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Correggi il formato del path
    # Rimuovi i doppi apici prima di d=
    content = content.replace('d=""d=', 'd="')
    
    # Assicurati che i valori RGB siano corretti (aggiungi # e converti in hex)
    def rgb_to_hex(match):
        rgb_str = match.group(1)
        rgb_values = rgb_str.split(',')
        if len(rgb_values) == 3:
            r, g, b = map(int, rgb_values)
            return f'fill="#{r:02x}{g:02x}{b:02x}"'
        return match.group(0)
    
    content = re.sub(r'fill:(\d+,\d+,\d+)', rgb_to_hex, content)
    
    # Correggi il formato stroke
    def stroke_to_hex(match):
        rgb_str = match.group(1)
        if rgb_str.lower() == 'none':
            return 'stroke="none"'
        rgb_values = rgb_str.split(',')
        if len(rgb_values) == 3:
            r, g, b = map(int, rgb_values)
            return f'stroke="#{r:02x}{g:02x}{b:02x}"'
        return match.group(0)
    
    content = re.sub(r'stroke:([^;]+)', stroke_to_hex, content)
    
    # Converti opacity e stroke-width
    content = re.sub(r'opacity:(\d+(\.\d+)?)', r'opacity="\1"', content)
    content = re.sub(r'stroke-width:(\d+(\.\d+)?)', r'stroke-width="\1"', content)
    
    # Crea un nuovo SVG completamente corretto
    new_content = f"""<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
{content.split('<path')[1:][0]}
</svg>
"""
    
    with open(file_path, 'w') as f:
        f.write(new_content)
    
    print(f"Corretto: {file_path}")

# Correggi tutti i file SVG nella directory
for filename in os.listdir(svg_dir):
    if filename.endswith(".svg") and filename.startswith(("simple_example", "medium_example", "complex_example")):
        file_path = os.path.join(svg_dir, filename)
        fix_svg_file(file_path)
