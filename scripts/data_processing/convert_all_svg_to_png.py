import os
import argparse
import subprocess
from glob import glob

def convert_svg_to_png(svg_dir, png_dir, width=512, height=512):
    """
    Converte tutti i file SVG in una directory in file PNG.
    """
    # Crea la directory di output se non esiste
    os.makedirs(png_dir, exist_ok=True)
    
    # Trova tutti i file SVG nella directory
    svg_files = glob(os.path.join(svg_dir, "*.svg"))
    
    # Converti ogni file SVG in PNG
    for svg_file in svg_files:
        # Ottieni il nome del file senza estensione
        file_name = os.path.basename(svg_file)
        file_name_without_ext = os.path.splitext(file_name)[0]
        
        # Costruisci il percorso del file PNG
        png_file = os.path.join(png_dir, file_name_without_ext + ".png")
        
        # Converti SVG in PNG usando ImageMagick
        try:
            subprocess.run([
                'convert',
                '-background', 'none',
                '-size', f'{width}x{height}',
                svg_file,
                png_file
            ], check=True)
            print(f"Convertito {svg_file} in {png_file}")
        except subprocess.CalledProcessError as e:
            print(f"Errore nella conversione di {svg_file}: {e}")
    
    return len(svg_files)

def main():
    parser = argparse.ArgumentParser(description="Converti file SVG in PNG")
    parser.add_argument("--svg_dir", type=str, required=True, help="Directory contenente i file SVG")
    parser.add_argument("--png_dir", type=str, required=True, help="Directory di output per i file PNG")
    parser.add_argument("--width", type=int, default=512, help="Larghezza delle immagini PNG")
    parser.add_argument("--height", type=int, default=512, help="Altezza delle immagini PNG")
    
    args = parser.parse_args()
    
    # Converti i file SVG in PNG
    num_converted = convert_svg_to_png(args.svg_dir, args.png_dir, args.width, args.height)
    
    print(f"Convertiti {num_converted} file SVG in PNG")

if __name__ == "__main__":
    main()
