#!/usr/bin/env python3
"""
Script per creare un subset del dataset di testing per evaluation baseline.
Prende circa 1/10 del dataset originale (350-400 esempi).
"""

import json
import random
import argparse
from pathlib import Path

def create_baseline_subset(input_file, output_file, subset_size=400, seed=42):
    """
    Crea un subset del dataset per evaluation baseline.
    
    Args:
        input_file: File dataset originale
        output_file: File output subset
        subset_size: Numero di esempi nel subset
        seed: Seed per riproducibilità
    """
    
    # Imposta seed per riproducibilità
    random.seed(seed)
    
    # Carica dataset originale
    print(f"📂 Caricamento dataset da: {input_file}")
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    print(f"📊 Dataset originale: {len(data)} esempi")
    
    # Calcola dimensione subset (max 1/10 del dataset)
    max_subset = len(data) // 10
    actual_subset_size = min(subset_size, max_subset, len(data))
    
    print(f"🎯 Creazione subset di {actual_subset_size} esempi")
    
    # Seleziona esempi casuali
    subset_data = random.sample(data, actual_subset_size)
    
    # Verifica che abbiamo tutti i campi necessari
    required_fields = ['id', 'caption', 'xml']
    missing_fields = []
    
    for i, example in enumerate(subset_data[:5]):  # Controlla primi 5 esempi
        for field in required_fields:
            if field not in example:
                missing_fields.append(f"Esempio {i}: manca campo '{field}'")
    
    if missing_fields:
        print("⚠️ Campi mancanti trovati:")
        for msg in missing_fields:
            print(f"  {msg}")
    else:
        print("✅ Tutti i campi richiesti sono presenti")
    
    # Salva subset
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(subset_data, f, indent=2)
    
    print(f"💾 Subset salvato in: {output_file}")
    print(f"📈 Riduzione: {len(data)} → {actual_subset_size} esempi ({actual_subset_size/len(data)*100:.1f}%)")
    
    # Statistiche del subset
    print("\n📊 STATISTICHE SUBSET:")
    
    # Lunghezza caption
    caption_lengths = [len(ex.get('caption', '').split()) for ex in subset_data]
    avg_caption_len = sum(caption_lengths) / len(caption_lengths)
    print(f"  📝 Lunghezza media caption: {avg_caption_len:.1f} parole")
    
    # Lunghezza XML
    xml_lengths = [len(ex.get('xml', '')) for ex in subset_data]
    avg_xml_len = sum(xml_lengths) / len(xml_lengths)
    print(f"  🔧 Lunghezza media XML: {avg_xml_len:.0f} caratteri")
    
    # Esempi di ID
    sample_ids = [ex.get('id', f'example_{i}') for i, ex in enumerate(subset_data[:5])]
    print(f"  🆔 Esempi ID: {sample_ids}")
    
    return actual_subset_size

def main():
    parser = argparse.ArgumentParser(description='Crea subset dataset per evaluation baseline')
    parser.add_argument('--input_file', 
                       default='data/processed/xml_format_optimized/test_set_test5_3k.json',
                       help='File dataset originale')
    parser.add_argument('--output_file',
                       default='data/processed/xml_format_optimized/baseline_eval_subset_400.json', 
                       help='File output subset')
    parser.add_argument('--subset_size', type=int, default=400,
                       help='Dimensione del subset (default: 400)')
    parser.add_argument('--seed', type=int, default=42,
                       help='Seed per riproducibilità (default: 42)')
    
    args = parser.parse_args()
    
    print("🎯 CREAZIONE SUBSET DATASET PER BASELINE EVALUATION")
    print("=" * 60)
    
    # Verifica che il file input esista
    if not Path(args.input_file).exists():
        print(f"❌ ERRORE: File input non trovato: {args.input_file}")
        return 1
    
    try:
        subset_size = create_baseline_subset(
            args.input_file,
            args.output_file, 
            args.subset_size,
            args.seed
        )
        
        print(f"\n✅ SUBSET CREATO CON SUCCESSO!")
        print(f"📁 File: {args.output_file}")
        print(f"📊 Dimensione: {subset_size} esempi")
        print(f"⏰ Tempo stimato evaluation: ~{subset_size * 3 / 60:.0f} minuti per modello")
        
        return 0
        
    except Exception as e:
        print(f"❌ ERRORE durante la creazione del subset: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
