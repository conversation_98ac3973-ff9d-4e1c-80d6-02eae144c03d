import os
import json
import argparse
import subprocess
import tempfile

def convert_svg_to_png(svg_content, output_path, width=512, height=512):
    """
    Converte un contenuto SVG in un'immagine PNG.
    """
    # Crea un file temporaneo per l'SVG
    with tempfile.NamedTemporaryFile(suffix='.svg', delete=False) as temp_svg:
        temp_svg.write(svg_content.encode('utf-8'))
        temp_svg_path = temp_svg.name
    
    # Converti SVG in PNG usando Inkscape
    try:
        subprocess.run([
            'inkscape',
            '--export-filename=' + output_path,
            '--export-width=' + str(width),
            '--export-height=' + str(height),
            temp_svg_path
        ], check=True)
        print(f"Convertito SVG in PNG: {output_path}")
    except subprocess.CalledProcessError as e:
        print(f"Errore nella conversione SVG in PNG: {e}")
    except FileNotFoundError:
        print("Inkscape non trovato. Assicurati che sia installato.")
    
    # Rimuovi il file temporaneo
    os.unlink(temp_svg_path)

def process_results(results_file, output_dir, prefix):
    """
    Processa i risultati dell'inferenza e converte gli SVG in PNG.
    """
    # Carica i risultati
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Converti gli SVG in PNG
    for i, result in enumerate(results):
        svg_content = result.get('svg', '')
        if not svg_content:
            continue
        
        # Crea il percorso di output
        output_path = os.path.join(output_dir, f"{prefix}_{i+1}.png")
        
        # Converti SVG in PNG
        convert_svg_to_png(svg_content, output_path)
        
        # Aggiungi il percorso dell'immagine al risultato
        result['png_path'] = output_path
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Converti SVG in PNG")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per le immagini PNG")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Processa i risultati
    llama_results = process_results(args.llama_results, args.output_dir, "llama")
    gemma_results = process_results(args.gemma_results, args.output_dir, "gemma")
    
    # Salva i risultati aggiornati
    llama_output = os.path.join(args.output_dir, "llama_results_with_png.json")
    gemma_output = os.path.join(args.output_dir, "gemma_results_with_png.json")
    
    with open(llama_output, 'w') as f:
        json.dump(llama_results, f, indent=2)
    
    with open(gemma_output, 'w') as f:
        json.dump(gemma_results, f, indent=2)
    
    print(f"Risultati aggiornati salvati in {llama_output} e {gemma_output}")

if __name__ == "__main__":
    main()
