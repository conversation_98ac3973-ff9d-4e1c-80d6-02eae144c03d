import json
import os
import re

# Crea la directory di output se non esiste
output_dir = "/work/tesi_ediluzio/evaluation/reports/svg_images_original"
os.makedirs(output_dir, exist_ok=True)

# Leggi il file di inferenza
inference_file = "/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/llama31_8b_test1_convergence/checkpoint-2300/inference_results.jsonl"

# Esempi selezionati per categoria
simple_examples = [45, 21, 2]  # Cerchio blu, Barra verticale verde, Triangolo nero
medium_examples = [18, 13, 20]  # Freccia, Fulmine, Bandiera
complex_examples = [40, 23, 17]  # <PERSON><PERSON><PERSON> di riciclo, Lettera M, Design astratto

# Mappa degli indici agli ID dei file
index_to_filename = {
    45: "simple_example_1",
    21: "simple_example_2",
    2: "simple_example_3",
    18: "medium_example_1",
    13: "medium_example_2",
    20: "medium_example_3",
    40: "complex_example_1",
    23: "complex_example_2",
    17: "complex_example_3"
}

# Funzione per correggere il formato SVG
def fix_svg_content(svg_content):
    # Rimuovi gli escape di virgolette
    svg_content = svg_content.replace('\\"', '"')
    
    # Correggi i path
    svg_content = re.sub(r'd=(["\'])d=([^"\']+)(["\'])', r'd=\1\2\3', svg_content)
    
    # Correggi gli attributi di stile
    svg_content = re.sub(r'style="([^"]+)"', lambda m: fix_style_attribute(m.group(1)), svg_content)
    
    # Assicurati che l'SVG abbia un tag di chiusura corretto
    if not svg_content.strip().endswith("</svg>"):
        svg_content = svg_content.rstrip() + "\n</svg>"
    
    return svg_content

def fix_style_attribute(style_str):
    # Converti gli attributi di stile in attributi XML
    attributes = []
    for attr in style_str.split(';'):
        if not attr.strip():
            continue
        key, value = attr.split(':', 1)
        key = key.strip()
        value = value.strip()
        
        # Gestisci i casi speciali
        if key == 'fill' or key == 'stroke':
            if value.lower() == 'none':
                attributes.append(f'{key}="none"')
            elif ',' in value:
                # Converti RGB in hex
                try:
                    r, g, b = map(int, value.split(','))
                    attributes.append(f'{key}="#{r:02x}{g:02x}{b:02x}"')
                except:
                    attributes.append(f'{key}="{value}"')
            else:
                attributes.append(f'{key}="{value}"')
        else:
            attributes.append(f'{key}="{value}"')
    
    return " ".join(attributes)

# Funzione per estrarre e salvare l'SVG
def extract_and_save_svg(data, index, filename):
    item = data[index]
    svg_content = item["svg"]
    
    # Correggi l'SVG
    svg_content = fix_svg_content(svg_content)
    
    # Salva l'SVG
    filepath = os.path.join(output_dir, f"{filename}.svg")
    
    with open(filepath, "w") as f:
        f.write(svg_content)
    
    print(f"Salvato {filepath}")
    
    return {
        "filename": f"{filename}.svg",
        "true_caption": item["true_caption"],
        "generated_caption": item["generated_caption"].split("</s>")[0]
    }

# Leggi il file di inferenza
data = []
with open(inference_file, "r") as f:
    for line in f:
        data.append(json.loads(line))

# Estrai e salva gli SVG
examples = {}

# SVG semplici
examples["simple"] = []
for idx in simple_examples:
    filename = index_to_filename[idx]
    examples["simple"].append(extract_and_save_svg(data, idx, filename))

# SVG di media complessità
examples["medium"] = []
for idx in medium_examples:
    filename = index_to_filename[idx]
    examples["medium"].append(extract_and_save_svg(data, idx, filename))

# SVG complessi
examples["complex"] = []
for idx in complex_examples:
    filename = index_to_filename[idx]
    examples["complex"].append(extract_and_save_svg(data, idx, filename))

# Salva le informazioni sugli esempi in un file JSON
examples_file = os.path.join(output_dir, "examples.json")
with open(examples_file, "w") as f:
    json.dump(examples, f, indent=2)

print(f"Informazioni sugli esempi salvate in {examples_file}")

# Aggiorna il report principale per utilizzare i nuovi SVG
report_path = "/work/tesi_ediluzio/evaluation/reports/llama_fine_tuned_report.html"
with open(report_path, "r") as f:
    report_content = f.read()

# Sostituisci i percorsi degli SVG
for category in examples:
    for example in examples[category]:
        filename = example["filename"]
        old_path = f'src="/work/tesi_ediluzio/evaluation/reports/svg_images_fixed/{filename}"'
        new_path = f'src="/work/tesi_ediluzio/evaluation/reports/svg_images_original/{filename}"'
        report_content = report_content.replace(old_path, new_path)

# Salva il report aggiornato
with open(report_path, "w") as f:
    f.write(report_content)
print(f"Aggiornato report: {report_path}")
