#!/usr/bin/env python3
"""
Script per creare dataset ottimizzato secondo le nuove specifiche:
- 20,000 esempi più corti (sotto 800 token)
- 90% train (18,000) / 10% test (2,000)
- Context window ottimizzato
"""

import json
import os
import random
from transformers import AutoTokenizer
from pathlib import Path

def main():
    print("🔧 CREAZIONE DATASET OTTIMIZZATO")
    print("=" * 50)

    # Setup
    os.environ['HF_TOKEN'] = '*************************************'

    # Carica tokenizer
    print("📝 Caricamento tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained('meta-llama/Llama-3.1-8B-Instruct', token=os.environ['HF_TOKEN'])
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Carica dataset originale
    print("📂 Caricamento dataset originale...")
    with open('data/processed/xml_format/train_set_final_xml_augmented.json', 'r') as f:
        original_data = json.load(f)

    print(f"📊 Dataset originale: {len(original_data)} esempi")

    # Analizza e filtra per lunghezza token
    print("🔍 Analisi lunghezze token...")
    valid_examples = []

    for i, item in enumerate(original_data):
        if i % 10000 == 0:
            print(f"   Processati: {i}/{len(original_data)}")

        svg = item['xml']
        caption = item['caption']
        prompt = f'<s>[INST] Descrivi questa immagine SVG:\\n{svg} [/INST] {caption}</s>'

        tokens = tokenizer.encode(prompt)
        token_length = len(tokens)

        if token_length <= 800:  # Filtro sotto 800 token
            valid_examples.append({
                'item': item,
                'token_length': token_length,
                'char_length': len(svg) + len(caption)
            })

    print(f"✅ Esempi validi (≤800 token): {len(valid_examples)}")

    # Ordina per lunghezza token (più corti prima)
    valid_examples.sort(key=lambda x: x['token_length'])

    # Prendi i 35,555 più corti per ottenere 32K train (2000 step/epoca)
    target_total = 35555  # Per ottenere 32K train con split 90/10
    if len(valid_examples) >= target_total:
        selected_examples = valid_examples[:target_total]
        print(f"📊 Selezionati i {target_total:,} esempi più corti")
    else:
        selected_examples = valid_examples
        print(f"⚠️ Solo {len(selected_examples)} esempi disponibili (< {target_total:,})")

    # Analizza il dataset selezionato
    token_lengths = [ex['token_length'] for ex in selected_examples]
    max_tokens = max(token_lengths)
    avg_tokens = sum(token_lengths) / len(token_lengths)

    print(f"\\n📏 STATISTICHE DATASET SELEZIONATO:")
    print(f"Esempi: {len(selected_examples)}")
    print(f"Token min: {min(token_lengths)}")
    print(f"Token max: {max_tokens}")
    print(f"Token media: {avg_tokens:.1f}")
    print(f"Token 95°: {token_lengths[int(len(token_lengths)*0.95)]}")

    # Determina context window ottimale
    # Aggiungi margine significativo per arrivare a 2000 come richiesto
    optimal_context = max(2000, int(max_tokens * 1.5))
    # Arrotonda a multiplo di 64 per efficienza
    optimal_context = ((optimal_context + 63) // 64) * 64

    print(f"\\n🎯 CONTEXT WINDOW OTTIMALE: {optimal_context}")

    # Mescola e dividi 90/10
    random.seed(42)  # Per riproducibilità
    items_only = [ex['item'] for ex in selected_examples]
    random.shuffle(items_only)

    # Split 90/10
    split_point = int(len(items_only) * 0.9)
    train_data = items_only[:split_point]
    test_data = items_only[split_point:]

    print(f"\\n📊 SPLIT FINALE:")
    print(f"Train: {len(train_data)} esempi ({len(train_data)/len(items_only)*100:.1f}%)")
    print(f"Test: {len(test_data)} esempi ({len(test_data)/len(items_only)*100:.1f}%)")

    # Crea directory output
    output_dir = Path("data/processed/xml_format_optimized")
    output_dir.mkdir(exist_ok=True)

    # Salva dataset
    train_file = output_dir / "train_set_test5_32k.json"
    test_file = output_dir / "test_set_test5_3k.json"

    print(f"\\n💾 Salvataggio dataset...")
    with open(train_file, 'w') as f:
        json.dump(train_data, f, indent=2)

    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)

    # Salva metadati
    metadata = {
        "total_examples": len(items_only),
        "train_examples": len(train_data),
        "test_examples": len(test_data),
        "max_tokens": max_tokens,
        "avg_tokens": avg_tokens,
        "optimal_context_window": optimal_context,
        "token_filter": "≤800 tokens",
        "split_ratio": "90/10",
        "seed": 42
    }

    metadata_file = output_dir / "dataset_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)

    print(f"✅ Dataset salvato:")
    print(f"   📁 Train: {train_file}")
    print(f"   📁 Test: {test_file}")
    print(f"   📁 Metadata: {metadata_file}")

    # Calcola step per epoca
    batch_size_per_gpu = 8  # Nuovo batch size
    num_gpus = 2
    total_batch_size = batch_size_per_gpu * num_gpus
    steps_per_epoch = len(train_data) // total_batch_size

    print(f"\\n📈 CALCOLI TRAINING:")
    print(f"Batch size per GPU: {batch_size_per_gpu}")
    print(f"Numero GPU: {num_gpus}")
    print(f"Batch size totale: {total_batch_size}")
    print(f"Step per epoca: {steps_per_epoch}")
    print(f"Epoche per 25,000 step: {25000 / steps_per_epoch:.1f}")

    # Verifica target di 2000 step per epoca
    target_steps = 2000
    if abs(steps_per_epoch - target_steps) > 50:
        print(f"⚠️ ATTENZIONE: Step per epoca ({steps_per_epoch}) diverso dal target ({target_steps})")
    else:
        print(f"✅ Step per epoca vicino al target di {target_steps}")

    print(f"\\n🎯 RACCOMANDAZIONI:")
    print(f"- Context window: {optimal_context}")
    print(f"- Batch size per GPU: {batch_size_per_gpu}")
    print(f"- Evaluation ogni: {steps_per_epoch} step (fine epoca)")
    print(f"- Save ogni: 150 step (come richiesto)")
    print(f"- Early stopping patience: 10 eval (10 epoche)")
    print(f"- Early stopping a metà epoca: {steps_per_epoch // 2} step")

    return metadata

if __name__ == "__main__":
    metadata = main()
