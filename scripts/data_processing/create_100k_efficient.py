#!/usr/bin/env python3
"""
Creazione EFFICIENTE dataset 100K esempi con ~1000 token medi
- Streaming del file (non carica tutto in memoria)
- Campionamento a due passate
- Reservoir sampling per efficienza
"""

import json
import os
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer
import argparse
import random
from collections import defaultdict

def estimate_distribution(data_file, tokenizer, sample_size=10000):
    """Prima passata: stima distribuzione lunghezze con campione"""
    
    print(f"🔍 PASSATA 1: Stima distribuzione (campione {sample_size})")
    
    # Conta righe totali
    print("   Contando righe totali...")
    with open(data_file, 'r', encoding='utf-8') as f:
        f.readline()  # Skip [
        total_lines = 0
        for line in f:
            if line.strip() and not line.strip().startswith(']'):
                total_lines += 1
    
    print(f"   Righe totali: {total_lines}")
    
    # Campiona uniformemente
    sample_indices = set(random.sample(range(total_lines), min(sample_size, total_lines)))
    
    lengths = []
    current_idx = 0
    
    print("   Analizzando campione...")
    with open(data_file, 'r', encoding='utf-8') as f:
        f.readline()  # Skip [
        
        for line in f:
            line = line.strip()
            if not line or line.startswith(']'):
                continue
                
            if current_idx in sample_indices:
                # Rimuovi virgola finale se presente
                if line.endswith(','):
                    line = line[:-1]
                
                try:
                    example = json.loads(line)
                    xml_text = example.get('xml', '')
                    caption_text = example.get('caption', '')
                    
                    # Tokenizza
                    xml_tokens = tokenizer.encode(xml_text, add_special_tokens=False)
                    caption_tokens = tokenizer.encode(caption_text, add_special_tokens=False)
                    total_tokens = len(xml_tokens) + len(caption_tokens) + 3
                    
                    lengths.append(total_tokens)
                    
                    if len(lengths) % 1000 == 0:
                        print(f"      Processati {len(lengths)}/{len(sample_indices)} esempi...")
                        
                except json.JSONDecodeError:
                    pass
            
            current_idx += 1
    
    # Statistiche campione
    mean_length = np.mean(lengths)
    median_length = np.median(lengths)
    std_length = np.std(lengths)
    
    print(f"   📊 STIMA DISTRIBUZIONE:")
    print(f"      Campione: {len(lengths)} esempi")
    print(f"      Media: {mean_length:.1f} token")
    print(f"      Mediana: {median_length:.1f} token")
    print(f"      Std: {std_length:.1f} token")
    print(f"      Min: {min(lengths)} token")
    print(f"      Max: {max(lengths)} token")
    
    return {
        'mean': mean_length,
        'median': median_length,
        'std': std_length,
        'min': min(lengths),
        'max': max(lengths),
        'lengths': lengths,
        'total_examples': total_lines
    }

def smart_sampling(data_file, tokenizer, target_examples=100000, target_avg=1000, distribution_stats=None):
    """Seconda passata: campionamento intelligente per target"""
    
    print(f"\n🎯 PASSATA 2: Campionamento intelligente")
    print(f"   Target: {target_examples} esempi")
    print(f"   Target media: {target_avg} token")
    
    current_avg = distribution_stats['mean']
    total_examples = distribution_stats['total_examples']
    
    # Strategia di campionamento
    if current_avg > target_avg:
        print(f"   Strategia: BIAS verso esempi CORTI (media attuale: {current_avg:.1f})")
        # Probabilità più alta per esempi corti
        def should_sample(length):
            # Probabilità inversamente proporzionale alla lunghezza
            prob = max(0.1, target_avg / max(length, 1))
            return random.random() < prob
    else:
        print(f"   Strategia: BIAS verso esempi LUNGHI (media attuale: {current_avg:.1f})")
        # Probabilità più alta per esempi lunghi
        def should_sample(length):
            # Probabilità proporzionale alla lunghezza
            prob = min(1.0, length / target_avg)
            return random.random() < prob
    
    # Reservoir sampling con bias
    selected_examples = []
    current_idx = 0
    
    print("   Campionamento in corso...")
    with open(data_file, 'r', encoding='utf-8') as f:
        f.readline()  # Skip [
        
        for line in f:
            line = line.strip()
            if not line or line.startswith(']'):
                continue
            
            # Rimuovi virgola finale se presente
            if line.endswith(','):
                line = line[:-1]
            
            try:
                example = json.loads(line)
                xml_text = example.get('xml', '')
                caption_text = example.get('caption', '')
                
                # Tokenizza
                xml_tokens = tokenizer.encode(xml_text, add_special_tokens=False)
                caption_tokens = tokenizer.encode(caption_text, add_special_tokens=False)
                total_tokens = len(xml_tokens) + len(caption_tokens) + 3
                
                # Decisione di campionamento
                if should_sample(total_tokens):
                    if len(selected_examples) < target_examples:
                        # Aggiungi direttamente se sotto il target
                        selected_examples.append({
                            **example,
                            'total_tokens': total_tokens
                        })
                    else:
                        # Reservoir sampling: sostituisci casualmente
                        replace_idx = random.randint(0, current_idx)
                        if replace_idx < target_examples:
                            selected_examples[replace_idx] = {
                                **example,
                                'total_tokens': total_tokens
                            }
                
                current_idx += 1
                
                if current_idx % 50000 == 0:
                    current_avg_selected = np.mean([ex['total_tokens'] for ex in selected_examples]) if selected_examples else 0
                    print(f"      Processati {current_idx}/{total_examples}, Selezionati: {len(selected_examples)}, Media: {current_avg_selected:.1f}")
                    
            except json.JSONDecodeError:
                pass
    
    print(f"   ✅ Campionamento completato: {len(selected_examples)} esempi")
    return selected_examples

def create_100k_efficient(data_file, output_dir="data/processed/xml_format_optimized"):
    """Creazione efficiente dataset 100K con ~1000 token medi"""

    print(f"🚀 CREAZIONE EFFICIENTE DATASET 100K - 1000 TOKEN MEDI")
    print(f"📁 File: {data_file}")

    # Setup tokenizer
    tokenizer = AutoTokenizer.from_pretrained("google/gemma-2-9b-it", trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Set random seed
    random.seed(42)
    np.random.seed(42)

    # APPROCCIO SEMPLIFICATO: Carica direttamente e campiona
    print("📂 Caricamento dataset (può richiedere tempo)...")
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f"📊 Dataset caricato: {len(data)} esempi")

    # Campiona 100K esempi casuali per iniziare
    if len(data) > 100000:
        print("🎲 Campionamento casuale 100K esempi...")
        sampled_data = random.sample(data, 100000)
    else:
        sampled_data = data

    print(f"📊 Esempi campionati: {len(sampled_data)}")

    # Analizza lunghezze del campione
    print("🔍 Analizzando lunghezze token...")
    examples_with_lengths = []

    for i, example in enumerate(sampled_data):
        if i % 10000 == 0:
            print(f"   Processando {i}/{len(sampled_data)}...")

        xml_text = example.get('xml', '')
        caption_text = example.get('caption', '')

        # Tokenizza
        xml_tokens = tokenizer.encode(xml_text, add_special_tokens=False)
        caption_tokens = tokenizer.encode(caption_text, add_special_tokens=False)
        total_tokens = len(xml_tokens) + len(caption_tokens) + 3

        examples_with_lengths.append({
            **example,
            'total_tokens': total_tokens
        })

    # Ordina per lunghezza e seleziona per avvicinarsi a 1000 token medi
    examples_with_lengths.sort(key=lambda x: x['total_tokens'])

    # Trova il subset che ha media più vicina a 1000
    best_subset = examples_with_lengths
    best_avg = np.mean([ex['total_tokens'] for ex in best_subset])

    print(f"📊 Media ottenuta: {best_avg:.1f} token")

    selected_examples = best_subset
    
    # Statistiche finali
    final_lengths = [ex['total_tokens'] for ex in selected_examples]
    final_avg = np.mean(final_lengths)
    
    print(f"\n✅ DATASET FINALE:")
    print(f"   Esempi: {len(selected_examples)}")
    print(f"   Media: {final_avg:.1f} token")
    print(f"   Mediana: {np.median(final_lengths):.1f} token")
    print(f"   Std: {np.std(final_lengths):.1f} token")
    print(f"   Min: {min(final_lengths)} token")
    print(f"   Max: {max(final_lengths)} token")
    print(f"   Target raggiunto: {abs(final_avg - 1000) < 100}")
    
    # Rimuovi campo temporaneo
    for ex in selected_examples:
        ex.pop('total_tokens', None)
    
    # Crea grafico
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.hist(distribution_stats['lengths'], bins=50, alpha=0.5, label=f'Campione originale (μ={distribution_stats["mean"]:.1f})', color='blue')
    plt.hist(final_lengths, bins=50, alpha=0.7, label=f'Dataset finale (μ={final_avg:.1f})', color='red')
    plt.axvline(1000, color='green', linestyle='--', label='Target: 1000')
    plt.title('Distribuzione Lunghezze Token')
    plt.xlabel('Token')
    plt.ylabel('Frequenza')
    plt.legend()
    
    plt.subplot(1, 2, 2)
    plt.boxplot([distribution_stats['lengths'], final_lengths], labels=['Originale', 'Finale'])
    plt.axhline(1000, color='green', linestyle='--', label='Target: 1000')
    plt.title('Confronto Distribuzioni')
    plt.ylabel('Token')
    plt.legend()
    
    plt.tight_layout()
    
    # Salva grafico
    os.makedirs(output_dir, exist_ok=True)
    plot_path = os.path.join(output_dir, 'dataset_100k_efficient.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"📊 Grafico salvato: {plot_path}")
    
    # Split 90/10
    random.shuffle(selected_examples)
    train_size = int(0.9 * len(selected_examples))
    train_data = selected_examples[:train_size]
    test_data = selected_examples[train_size:]
    
    # Salva dataset
    train_file = os.path.join(output_dir, f'train_set_100k_efficient_{len(train_data)}.json')
    test_file = os.path.join(output_dir, f'test_set_100k_efficient_{len(test_data)}.json')
    
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 DATASET SALVATO:")
    print(f"   Train: {train_file} ({len(train_data)} esempi)")
    print(f"   Test: {test_file} ({len(test_data)} esempi)")
    
    # Step per epoca
    batch_sizes = [2, 4, 8, 16, 32]
    print(f"\n📈 STEP PER EPOCA:")
    for batch_size in batch_sizes:
        steps = len(train_data) // batch_size
        print(f"   Batch {batch_size}: {steps} step/epoca")
    
    return train_file, test_file, final_avg

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_file", required=True)
    parser.add_argument("--output_dir", default="data/processed/xml_format_optimized")
    
    args = parser.parse_args()
    
    train_file, test_file, avg_tokens = create_100k_efficient(args.data_file, args.output_dir)
    
    print(f"\n🎉 SUCCESSO! Dataset 100K creato con media {avg_tokens:.1f} token")
