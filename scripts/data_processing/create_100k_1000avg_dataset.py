#!/usr/bin/env python3
"""
Crea UN DATASET di 100K esempi con ~1000 token medi
- Campiona dal dataset originale
- Target: 100.000 esempi con media ~1000 token
- Un solo dataset per entrambi i modelli
"""

import json
import os
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer
import argparse
from collections import defaultdict

def create_100k_1000avg_dataset(data_file, output_dir="data/processed/xml_format_optimized"):
    """Crea dataset di 100K esempi con ~1000 token medi"""
    
    print(f"🎯 CREAZIONE DATASET 100K ESEMPI - 1000 TOKEN MEDI")
    print(f"📁 Dataset: {data_file}")
    print(f"📊 Target: 100.000 esempi")
    print(f"📏 Target media: ~1000 token")
    
    # Usa Gemma tokenizer come riferimento (più conservativo)
    tokenizer = AutoTokenizer.from_pretrained("google/gemma-2-9b-it", trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Carica dataset completo
    print("📂 Caricamento dataset completo...")
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 Dataset originale: {len(data)} esempi")
    
    # Analizza tutti gli esempi e calcola lunghezze
    print("🔍 Analizzando lunghezze token...")
    examples_with_lengths = []
    
    for i, example in enumerate(data):
        if i % 10000 == 0:
            print(f"   Processando {i}/{len(data)}...")
            
        xml_text = example.get('xml', '')
        caption_text = example.get('caption', '')
        
        # Tokenizza
        xml_tokens = tokenizer.encode(xml_text, add_special_tokens=False)
        caption_tokens = tokenizer.encode(caption_text, add_special_tokens=False)
        total_tokens = len(xml_tokens) + len(caption_tokens) + 3  # special tokens
        
        examples_with_lengths.append({
            **example,
            'total_tokens': total_tokens
        })
    
    # Statistiche dataset completo
    all_lengths = [ex['total_tokens'] for ex in examples_with_lengths]
    print(f"\n📊 STATISTICHE DATASET COMPLETO:")
    print(f"   Esempi: {len(examples_with_lengths)}")
    print(f"   Media: {np.mean(all_lengths):.1f} token")
    print(f"   Mediana: {np.median(all_lengths):.1f} token")
    print(f"   Min: {min(all_lengths)} token")
    print(f"   Max: {max(all_lengths)} token")
    print(f"   95° percentile: {np.percentile(all_lengths, 95):.1f} token")
    
    # Strategia per campionare 100K esempi con media ~1000 token
    target_examples = 100000
    target_avg = 1000
    current_avg = np.mean(all_lengths)
    
    print(f"\n🎯 STRATEGIA CAMPIONAMENTO:")
    print(f"   Media attuale: {current_avg:.1f} token")
    print(f"   Target media: {target_avg} token")
    
    if current_avg > target_avg:
        # Dataset ha media troppo alta, prendiamo esempi più corti
        print("   → Campionando verso esempi più CORTI")
        # Ordina per lunghezza crescente e prendi i primi 100K
        examples_with_lengths.sort(key=lambda x: x['total_tokens'])
        selected_examples = examples_with_lengths[:target_examples]
        
    elif current_avg < target_avg:
        # Dataset ha media troppo bassa, prendiamo esempi più lunghi
        print("   → Campionando verso esempi più LUNGHI")
        # Ordina per lunghezza decrescente e prendi i primi 100K
        examples_with_lengths.sort(key=lambda x: x['total_tokens'], reverse=True)
        selected_examples = examples_with_lengths[:target_examples]
        
    else:
        # Media già buona, campiona casualmente
        print("   → Campionamento CASUALE")
        np.random.shuffle(examples_with_lengths)
        selected_examples = examples_with_lengths[:target_examples]
    
    # Se non abbiamo abbastanza esempi, prendi tutti
    if len(examples_with_lengths) < target_examples:
        print(f"   ⚠️  Solo {len(examples_with_lengths)} esempi disponibili (< {target_examples})")
        selected_examples = examples_with_lengths
    
    # Calcola statistiche finali
    final_lengths = [ex['total_tokens'] for ex in selected_examples]
    final_avg = np.mean(final_lengths)
    
    print(f"\n✅ DATASET FINALE:")
    print(f"   Esempi: {len(selected_examples)}")
    print(f"   Media: {final_avg:.1f} token")
    print(f"   Mediana: {np.median(final_lengths):.1f} token")
    print(f"   Std: {np.std(final_lengths):.1f} token")
    print(f"   Min: {min(final_lengths)} token")
    print(f"   Max: {max(final_lengths)} token")
    print(f"   95° percentile: {np.percentile(final_lengths, 95):.1f} token")
    
    # Aggiustamento fine per avvicinarsi a 1000 token medi
    if abs(final_avg - target_avg) > 50:  # Se siamo lontani dal target
        print(f"\n🔧 AGGIUSTAMENTO FINE (differenza: {final_avg - target_avg:+.1f})")
        
        if final_avg > target_avg:
            # Sostituisci esempi lunghi con esempi corti
            print("   → Sostituendo esempi LUNGHI con esempi CORTI")
            remaining_examples = [ex for ex in examples_with_lengths if ex not in selected_examples]
            remaining_examples.sort(key=lambda x: x['total_tokens'])  # Più corti prima
            
            # Sostituisci i più lunghi del dataset selezionato
            selected_examples.sort(key=lambda x: x['total_tokens'], reverse=True)
            
            for i in range(min(len(remaining_examples), len(selected_examples) // 4)):
                if len(remaining_examples) > i:
                    selected_examples[i] = remaining_examples[i]
                    
        else:
            # Sostituisci esempi corti con esempi lunghi
            print("   → Sostituendo esempi CORTI con esempi LUNGHI")
            remaining_examples = [ex for ex in examples_with_lengths if ex not in selected_examples]
            remaining_examples.sort(key=lambda x: x['total_tokens'], reverse=True)  # Più lunghi prima
            
            # Sostituisci i più corti del dataset selezionato
            selected_examples.sort(key=lambda x: x['total_tokens'])
            
            for i in range(min(len(remaining_examples), len(selected_examples) // 4)):
                if len(remaining_examples) > i:
                    selected_examples[i] = remaining_examples[i]
        
        # Ricalcola statistiche dopo aggiustamento
        final_lengths = [ex['total_tokens'] for ex in selected_examples]
        final_avg = np.mean(final_lengths)
        
        print(f"   Nuova media: {final_avg:.1f} token")
    
    # Rimuovi il campo 'total_tokens' aggiunto per l'analisi
    for ex in selected_examples:
        ex.pop('total_tokens', None)
    
    # Crea grafici
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # Distribuzione originale vs finale
    axes[0].hist(all_lengths, bins=50, alpha=0.5, label=f'Originale (μ={np.mean(all_lengths):.1f})', color='blue')
    axes[0].hist(final_lengths, bins=50, alpha=0.7, label=f'Finale (μ={final_avg:.1f})', color='red')
    axes[0].axvline(target_avg, color='green', linestyle='--', label=f'Target: {target_avg}')
    axes[0].set_title('Distribuzione Lunghezze Token')
    axes[0].set_xlabel('Token')
    axes[0].set_ylabel('Frequenza')
    axes[0].legend()
    
    # Box plot comparativo
    axes[1].boxplot([all_lengths, final_lengths], labels=['Originale', 'Finale'])
    axes[1].axhline(target_avg, color='green', linestyle='--', label=f'Target: {target_avg}')
    axes[1].set_title('Confronto Distribuzioni')
    axes[1].set_ylabel('Token')
    axes[1].legend()
    
    plt.tight_layout()
    
    # Salva grafico
    os.makedirs(output_dir, exist_ok=True)
    plot_path = os.path.join(output_dir, f'dataset_100k_1000avg.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"📊 Grafico salvato: {plot_path}")
    
    # Split 90/10
    np.random.shuffle(selected_examples)
    train_size = int(0.9 * len(selected_examples))
    train_data = selected_examples[:train_size]
    test_data = selected_examples[train_size:]
    
    # Salva dataset
    train_file = os.path.join(output_dir, f'train_set_100k_{len(train_data)}.json')
    test_file = os.path.join(output_dir, f'test_set_100k_{len(test_data)}.json')
    
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 DATASET SALVATO:")
    print(f"   Train: {train_file} ({len(train_data)} esempi)")
    print(f"   Test: {test_file} ({len(test_data)} esempi)")
    
    # Calcola step per epoca
    batch_sizes = [2, 4, 8, 16, 32]
    print(f"\n📈 STEP PER EPOCA (dataset {len(train_data)} esempi):")
    for batch_size in batch_sizes:
        steps_per_epoch = len(train_data) // batch_size
        print(f"   Batch size {batch_size}: {steps_per_epoch} step/epoca")
    
    return train_file, test_file, {
        'total_examples': len(selected_examples),
        'train_examples': len(train_data),
        'test_examples': len(test_data),
        'avg_tokens': final_avg,
        'median_tokens': np.median(final_lengths),
        'std_tokens': np.std(final_lengths),
        'min_tokens': min(final_lengths),
        'max_tokens': max(final_lengths)
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_file", required=True, help="File dataset completo")
    parser.add_argument("--output_dir", default="data/processed/xml_format_optimized", help="Directory output")
    
    args = parser.parse_args()
    
    # Set random seed per riproducibilità
    np.random.seed(42)
    
    train_file, test_file, stats = create_100k_1000avg_dataset(
        args.data_file,
        args.output_dir
    )
    
    print(f"\n✅ Dataset 100K esempi con ~1000 token medi creato!")
    print(f"📊 Media finale: {stats['avg_tokens']:.1f} token")
    print(f"📁 Esempi: {stats['total_examples']}")
