import json
import sys

def add_svg_to_json(input_file, svg_file, output_file):
    # Leggi il file JSON formattato
    with open(input_file, 'r') as f:
        data = json.load(f)
    
    # Leggi il file JSON con i dati SVG
    with open(svg_file, 'r') as f:
        svg_data = json.load(f)
    
    # Aggiungi i dati SVG ai dati formattati
    for i, item in enumerate(data):
        if i < len(svg_data):
            item['svg'] = svg_data[i]['xml']
            item['complexity'] = 'complex'
            if 'llama' in input_file.lower():
                item['model'] = 'Llama-3.1-8B-Instruct'
            else:
                item['model'] = 'gemma-2-9b-it'
            item['inference_time'] = 0  # Non abbiamo i tempi di inferenza
    
    # Scrivi il file JSON
    with open(output_file, 'w') as f:
        json.dump(data, f, indent=2)
    
    print(f"Aggiunti dati SVG a {len(data)} elementi da {input_file} a {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Uso: python add_svg_to_json.py input_file.json svg_file.json output_file.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    svg_file = sys.argv[2]
    output_file = sys.argv[3]
    add_svg_to_json(input_file, svg_file, output_file)
