#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import random
import shutil
from tqdm import tqdm

def parse_args():
    parser = argparse.ArgumentParser(description="Prepara i dati di test per la valutazione finale")
    parser.add_argument("--input_file", type=str, default="/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json", 
                        help="File di input con i dati di test")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/data/test_samples", 
                        help="Directory di output per i campioni di test")
    parser.add_argument("--num_samples", type=int, default=100, 
                        help="Numero di campioni da estrarre")
    parser.add_argument("--seed", type=int, default=42, 
                        help="Seed per la generazione casuale")
    parser.add_argument("--save_svg", action="store_true", 
                        help="Salva anche i file SVG")
    parser.add_argument("--svg_dir", type=str, default="/work/tesi_ediluzio/data/test_samples/svg", 
                        help="Directory di output per i file SVG")
    return parser.parse_args()

def load_test_data(input_file):
    """Carica i dati di test dal file JSON"""
    if not os.path.exists(input_file):
        print(f"Il file {input_file} non esiste.")
        return None
    
    with open(input_file, "r") as f:
        data = json.load(f)
    
    return data

def extract_samples(data, num_samples, seed):
    """Estrae un sottoinsieme casuale di campioni"""
    if not data:
        return None
    
    # Imposta il seed per la riproducibilità
    random.seed(seed)
    
    # Estrai un sottoinsieme casuale
    if num_samples >= len(data):
        print(f"Il numero di campioni richiesto ({num_samples}) è maggiore o uguale al numero di campioni disponibili ({len(data)}). Verranno utilizzati tutti i campioni.")
        samples = data
    else:
        samples = random.sample(data, num_samples)
    
    return samples

def save_samples(samples, output_dir, save_svg=False, svg_dir=None):
    """Salva i campioni estratti"""
    if not samples:
        return
    
    # Crea la directory di output se non esiste
    os.makedirs(output_dir, exist_ok=True)
    
    # Crea la directory per i file SVG se richiesto
    if save_svg and svg_dir:
        os.makedirs(svg_dir, exist_ok=True)
    
    # Salva i campioni in un file JSON
    output_file = os.path.join(output_dir, "test_samples.json")
    with open(output_file, "w") as f:
        json.dump(samples, f, indent=2)
    
    print(f"Salvati {len(samples)} campioni in {output_file}")
    
    # Salva anche i singoli campioni
    for i, sample in enumerate(tqdm(samples, desc="Salvataggio campioni")):
        # Salva il campione in un file JSON
        sample_file = os.path.join(output_dir, f"sample_{i+1}.json")
        with open(sample_file, "w") as f:
            json.dump(sample, f, indent=2)
        
        # Salva il file SVG se richiesto
        if save_svg and svg_dir and "xml" in sample:
            svg_content = sample["xml"]
            svg_file = os.path.join(svg_dir, f"sample_{i+1}.svg")
            with open(svg_file, "w") as f:
                f.write(svg_content)
    
    # Crea un file di indice
    index_file = os.path.join(output_dir, "index.md")
    with open(index_file, "w") as f:
        f.write("# Campioni di Test per la Valutazione Finale\n\n")
        f.write(f"Numero di campioni: {len(samples)}\n\n")
        f.write("## Elenco dei Campioni\n\n")
        
        for i, sample in enumerate(samples):
            caption = sample.get("caption", "Nessuna didascalia")
            f.write(f"### Campione {i+1}\n\n")
            f.write(f"**Didascalia originale**: {caption}\n\n")
            
            if save_svg and svg_dir:
                f.write(f"**SVG**: [sample_{i+1}.svg](svg/sample_{i+1}.svg)\n\n")
            
            f.write(f"**JSON**: [sample_{i+1}.json](sample_{i+1}.json)\n\n")
    
    print(f"Creato file di indice: {index_file}")

def main():
    args = parse_args()
    
    # Carica i dati di test
    print(f"Caricamento dei dati di test da {args.input_file}...")
    data = load_test_data(args.input_file)
    
    if not data:
        print("Impossibile caricare i dati di test.")
        return
    
    print(f"Caricati {len(data)} campioni.")
    
    # Estrai un sottoinsieme casuale
    print(f"Estrazione di {args.num_samples} campioni casuali...")
    samples = extract_samples(data, args.num_samples, args.seed)
    
    if not samples:
        print("Impossibile estrarre i campioni.")
        return
    
    # Salva i campioni
    print(f"Salvataggio dei campioni in {args.output_dir}...")
    save_samples(samples, args.output_dir, args.save_svg, args.svg_dir)
    
    print("Preparazione dei dati di test completata.")

if __name__ == "__main__":
    main()
