#!/usr/bin/env python
import json
import xml.etree.ElementTree as ET

def check_xml_validity(file_path, num_samples=100):
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    total = len(data)
    if num_samples > total:
        num_samples = total
    
    valid = 0
    invalid = 0
    invalid_examples = []
    
    for i, example in enumerate(data[:num_samples]):
        try:
            xml_str = example.get('xml', '')
            if not xml_str:
                invalid += 1
                invalid_examples.append((i, "XML vuoto"))
                continue
                
            root = ET.fromstring(xml_str)
            valid += 1
        except Exception as e:
            invalid += 1
            invalid_examples.append((i, str(e)))
    
    print(f"File: {file_path}")
    print(f"Totale esempi nel dataset: {total}")
    print(f"Esempi controllati: {num_samples}")
    print(f"XML validi: {valid} ({valid/num_samples*100:.2f}%)")
    print(f"XML non validi: {invalid} ({invalid/num_samples*100:.2f}%)")
    
    if invalid > 0:
        print("\nEsempi non validi:")
        for i, error in invalid_examples[:10]:  # Mostra solo i primi 10 errori
            print(f"  Esempio {i}: {error}")
        if len(invalid_examples) > 10:
            print(f"  ... e altri {len(invalid_examples) - 10} esempi")

# Controlla i dataset
check_xml_validity('/work/tesi_ediluzio/data/processed/test_set_final_2k.json')
check_xml_validity('/work/tesi_ediluzio/data/processed/train_set_final.json', num_samples=1000)
