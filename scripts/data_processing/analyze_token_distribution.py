#!/usr/bin/env python3
"""
Analisi distribuzione token nel dataset SVG
- Analizza lunghezza SVG in token e caratteri
- Crea dataset filtrato per lunghezza massima
- Calcola step per epoca
"""

import json
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer
from collections import Counter
import argparse

def analyze_dataset_tokens(data_file, tokenizer_name, max_length=1000, output_dir="data/processed/xml_format_optimized"):
    """Analizza distribuzione token e crea dataset filtrato"""
    
    print(f"🔍 Analisi dataset: {data_file}")
    print(f"🔧 Tokenizer: {tokenizer_name}")
    print(f"📏 Max length target: {max_length} token")
    
    # Carica tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Carica dataset
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 Dataset originale: {len(data)} esempi")
    
    # Analizza ogni esempio
    svg_lengths_tokens = []
    svg_lengths_chars = []
    caption_lengths_tokens = []
    caption_lengths_chars = []
    total_lengths_tokens = []
    
    valid_examples = []
    
    for i, example in enumerate(data):
        if i % 1000 == 0:
            print(f"   Processando {i}/{len(data)}...")
            
        svg_text = example.get('xml', '')  # Fix: campo corretto è 'xml' non 'svg'
        caption_text = example.get('caption', '')
        
        # Tokenizza SVG
        svg_tokens = tokenizer.encode(svg_text, add_special_tokens=False)
        svg_len_tokens = len(svg_tokens)
        svg_len_chars = len(svg_text)
        
        # Tokenizza caption
        caption_tokens = tokenizer.encode(caption_text, add_special_tokens=False)
        caption_len_tokens = len(caption_tokens)
        caption_len_chars = len(caption_text)
        
        # Lunghezza totale (con special tokens)
        total_len_tokens = svg_len_tokens + caption_len_tokens + 3  # <bos>, <sep>, <eos>
        
        # Salva statistiche
        svg_lengths_tokens.append(svg_len_tokens)
        svg_lengths_chars.append(svg_len_chars)
        caption_lengths_tokens.append(caption_len_tokens)
        caption_lengths_chars.append(caption_len_chars)
        total_lengths_tokens.append(total_len_tokens)
        
        # Filtra per lunghezza massima
        if total_len_tokens <= max_length:
            valid_examples.append({
                **example,
                'svg_tokens': svg_len_tokens,
                'caption_tokens': caption_len_tokens,
                'total_tokens': total_len_tokens,
                'svg_chars': svg_len_chars,
                'caption_chars': caption_len_chars
            })
    
    # Statistiche
    print(f"\n📊 STATISTICHE TOKEN:")
    print(f"SVG Token - Media: {np.mean(svg_lengths_tokens):.1f}, Mediana: {np.median(svg_lengths_tokens):.1f}")
    print(f"SVG Token - Min: {np.min(svg_lengths_tokens)}, Max: {np.max(svg_lengths_tokens)}")
    print(f"SVG Token - 95°: {np.percentile(svg_lengths_tokens, 95):.1f}")
    
    print(f"\nCaption Token - Media: {np.mean(caption_lengths_tokens):.1f}, Mediana: {np.median(caption_lengths_tokens):.1f}")
    print(f"Caption Token - Min: {np.min(caption_lengths_tokens)}, Max: {np.max(caption_lengths_tokens)}")
    
    print(f"\nTotal Token - Media: {np.mean(total_lengths_tokens):.1f}, Mediana: {np.median(total_lengths_tokens):.1f}")
    print(f"Total Token - Min: {np.min(total_lengths_tokens)}, Max: {np.max(total_lengths_tokens)}")
    print(f"Total Token - 95°: {np.percentile(total_lengths_tokens, 95):.1f}")
    
    print(f"\n📊 STATISTICHE CARATTERI:")
    print(f"SVG Chars - Media: {np.mean(svg_lengths_chars):.1f}, Mediana: {np.median(svg_lengths_chars):.1f}")
    print(f"Caption Chars - Media: {np.mean(caption_lengths_chars):.1f}, Mediana: {np.median(caption_lengths_chars):.1f}")
    
    print(f"\n🎯 FILTRO LUNGHEZZA <= {max_length} token:")
    print(f"Esempi validi: {len(valid_examples)}/{len(data)} ({len(valid_examples)/len(data)*100:.1f}%)")
    
    # Crea grafici
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Distribuzione SVG token
    axes[0,0].hist(svg_lengths_tokens, bins=50, alpha=0.7, color='blue')
    axes[0,0].axvline(np.mean(svg_lengths_tokens), color='red', linestyle='--', label=f'Media: {np.mean(svg_lengths_tokens):.1f}')
    axes[0,0].set_title('Distribuzione SVG Token')
    axes[0,0].set_xlabel('Token')
    axes[0,0].set_ylabel('Frequenza')
    axes[0,0].legend()
    
    # Distribuzione Caption token
    axes[0,1].hist(caption_lengths_tokens, bins=50, alpha=0.7, color='green')
    axes[0,1].axvline(np.mean(caption_lengths_tokens), color='red', linestyle='--', label=f'Media: {np.mean(caption_lengths_tokens):.1f}')
    axes[0,1].set_title('Distribuzione Caption Token')
    axes[0,1].set_xlabel('Token')
    axes[0,1].set_ylabel('Frequenza')
    axes[0,1].legend()
    
    # Distribuzione Total token
    axes[1,0].hist(total_lengths_tokens, bins=50, alpha=0.7, color='purple')
    axes[1,0].axvline(max_length, color='red', linestyle='-', label=f'Max: {max_length}')
    axes[1,0].axvline(np.mean(total_lengths_tokens), color='orange', linestyle='--', label=f'Media: {np.mean(total_lengths_tokens):.1f}')
    axes[1,0].set_title('Distribuzione Total Token')
    axes[1,0].set_xlabel('Token')
    axes[1,0].set_ylabel('Frequenza')
    axes[1,0].legend()
    
    # Correlazione SVG chars vs token
    axes[1,1].scatter(svg_lengths_chars, svg_lengths_tokens, alpha=0.5, s=1)
    axes[1,1].set_title('SVG: Caratteri vs Token')
    axes[1,1].set_xlabel('Caratteri')
    axes[1,1].set_ylabel('Token')
    
    plt.tight_layout()
    
    # Salva grafico
    os.makedirs(output_dir, exist_ok=True)
    plot_path = os.path.join(output_dir, f'token_analysis_{tokenizer_name.replace("/", "_")}_max{max_length}.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"📊 Grafico salvato: {plot_path}")
    
    # Salva dataset filtrato
    if len(valid_examples) >= 10000:  # Almeno 10K esempi
        # Ordina per lunghezza totale (più corti prima)
        valid_examples.sort(key=lambda x: x['total_tokens'])
        
        # Prendi i primi 100K o tutti se meno
        max_examples = min(100000, len(valid_examples))
        filtered_dataset = valid_examples[:max_examples]
        
        # Split 90/10
        train_size = int(0.9 * len(filtered_dataset))
        train_data = filtered_dataset[:train_size]
        test_data = filtered_dataset[train_size:]
        
        # Salva file
        train_file = os.path.join(output_dir, f'train_set_max{max_length}_{len(train_data)}.json')
        test_file = os.path.join(output_dir, f'test_set_max{max_length}_{len(test_data)}.json')
        
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, ensure_ascii=False, indent=2)
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Dataset salvato:")
        print(f"   Train: {train_file} ({len(train_data)} esempi)")
        print(f"   Test: {test_file} ({len(test_data)} esempi)")
        
        # Calcola step per epoca
        batch_sizes = [2, 4, 8, 16, 32]
        print(f"\n📈 STEP PER EPOCA (dataset {len(train_data)} esempi):")
        for batch_size in batch_sizes:
            steps_per_epoch = len(train_data) // batch_size
            print(f"   Batch size {batch_size}: {steps_per_epoch} step/epoca")
        
        return train_file, test_file, {
            'total_examples': len(data),
            'valid_examples': len(valid_examples),
            'filtered_examples': len(filtered_dataset),
            'train_examples': len(train_data),
            'test_examples': len(test_data),
            'svg_tokens_mean': np.mean(svg_lengths_tokens),
            'caption_tokens_mean': np.mean(caption_lengths_tokens),
            'total_tokens_mean': np.mean(total_lengths_tokens),
            'svg_chars_mean': np.mean(svg_lengths_chars),
            'caption_chars_mean': np.mean(caption_lengths_chars)
        }
    else:
        print(f"❌ Troppo pochi esempi validi: {len(valid_examples)} < 10000")
        return None, None, None

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_file", required=True, help="File dataset da analizzare")
    parser.add_argument("--tokenizer", default="google/gemma-2-9b-it", help="Tokenizer da usare")
    parser.add_argument("--max_length", type=int, default=1000, help="Lunghezza massima in token")
    parser.add_argument("--output_dir", default="data/processed/xml_format_optimized", help="Directory output")
    
    args = parser.parse_args()
    
    train_file, test_file, stats = analyze_dataset_tokens(
        args.data_file, 
        args.tokenizer, 
        args.max_length, 
        args.output_dir
    )
    
    if stats:
        print(f"\n✅ Analisi completata!")
        print(f"📊 Statistiche salvate in: {args.output_dir}")
