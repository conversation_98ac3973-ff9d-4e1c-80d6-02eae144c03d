import json
import argparse
from pathlib import Path
import re

def convert_rgba_to_rgb(input_file, output_file):
    """Converte i colori RGBA in RGB nel file SVG con sfondo bianco."""
    # Carica il file di input
    with open(input_file, 'r') as f:
        data = json.load(f)

    # Funzione per convertire RGBA in RGB con alpha blending su sfondo bianco
    def rgba_to_rgb(match):
        rgba = match.group(1)
        values = [float(x.strip()) for x in rgba.split(',')]

        if len(values) == 4:
            r, g, b, a = values
            # Normalizza i valori se sono in range 0-255
            if r > 1 or g > 1 or b > 1:
                r, g, b = r/255, g/255, b/255
            if a > 1:
                a = a/255

            # Alpha blending con sfondo bianco (255,255,255)
            final_r = int((1 - a) * 255 + a * r * 255)
            final_g = int((1 - a) * 255 + a * g * 255)
            final_b = int((1 - a) * 255 + a * b * 255)

            return f'rgb({final_r},{final_g},{final_b})'
        else:
            # Se non è RGBA valido, mantieni originale
            return match.group(0)

    # Funzione per aggiungere sfondo bianco se mancante
    def add_white_background(svg):
        # Controlla se c'è già un background
        if 'style="background' not in svg and '<rect' not in svg[:200]:
            # Estrai dimensioni del viewBox o width/height
            viewbox_match = re.search(r'viewBox="([^"]*)"', svg)
            if viewbox_match:
                viewbox = viewbox_match.group(1).split()
                if len(viewbox) >= 4:
                    x, y, width, height = viewbox
                    # Aggiungi rettangolo bianco di sfondo
                    bg_rect = f'<rect x="{x}" y="{y}" width="{width}" height="{height}" fill="white"/>'
                    svg = svg.replace('<svg', f'<svg').replace('>', f'>{bg_rect}', 1)
        return svg

    converted_count = 0
    # Converti ogni SVG
    for example in data:
        if 'xml' in example:
            svg = example['xml']
            original_svg = svg

            # Sostituisci i colori RGBA con RGB
            svg = re.sub(r'rgba\(([^)]+)\)', rgba_to_rgb, svg)

            # Aggiungi sfondo bianco se necessario
            svg = add_white_background(svg)

            # Rimuovi attributi di trasparenza problematici
            svg = re.sub(r'fill-opacity="[^"]*"', '', svg)
            svg = re.sub(r'stroke-opacity="[^"]*"', '', svg)
            svg = re.sub(r'opacity="0"', 'opacity="1"', svg)

            example['xml'] = svg

            if svg != original_svg:
                converted_count += 1

    # Crea la directory di output se non esiste
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Salva il file convertito
    with open(output_file, 'w') as f:
        json.dump(data, f, indent=2)

    print(f"Conversione completata. {converted_count}/{len(data)} SVG modificati.")
    print(f"Risultati salvati in: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Converti colori RGBA a RGB in file SVG')
    parser.add_argument('--input_file', required=True, help='File JSON di input')
    parser.add_argument('--output_file', required=True, help='File JSON di output')
    args = parser.parse_args()

    convert_rgba_to_rgb(args.input_file, args.output_file)

if __name__ == '__main__':
    main()