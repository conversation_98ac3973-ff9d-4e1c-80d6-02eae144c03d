#!/usr/bin/env python3
"""
Crea dataset con lunghezza media 1000 token
- Campiona esempi da zero alla lunghezza massima
- Target: 100K esempi con media ~1000 token
- Distribuzione uniforme per complessità
"""

import json
import os
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer
import argparse
from collections import defaultdict

def create_1000_avg_dataset(data_file, tokenizer_name, target_avg=1000, max_examples=100000, output_dir="data/processed/xml_format_optimized"):
    """Crea dataset con lunghezza media target"""
    
    print(f"🎯 CREAZIONE DATASET 1000 TOKEN MEDI")
    print(f"📁 Dataset: {data_file}")
    print(f"🔧 Tokenizer: {tokenizer_name}")
    print(f"📏 Target media: {target_avg} token")
    print(f"📊 Max esempi: {max_examples}")
    
    # Carica tokenizer
    tokenizer = AutoTokenizer.from_pretrained(tokenizer_name, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Carica dataset completo
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"📊 Dataset originale: {len(data)} esempi")
    
    # Analizza tutti gli esempi e calcola lunghezze
    examples_by_length = defaultdict(list)
    all_lengths = []
    
    print("🔍 Analizzando lunghezze...")
    for i, example in enumerate(data):
        if i % 5000 == 0:
            print(f"   Processando {i}/{len(data)}...")
            
        xml_text = example.get('xml', '')
        caption_text = example.get('caption', '')
        
        # Tokenizza
        xml_tokens = tokenizer.encode(xml_text, add_special_tokens=False)
        caption_tokens = tokenizer.encode(caption_text, add_special_tokens=False)
        total_tokens = len(xml_tokens) + len(caption_tokens) + 3  # special tokens
        
        # Raggruppa per lunghezza (bins di 50 token)
        length_bin = (total_tokens // 50) * 50
        examples_by_length[length_bin].append({
            **example,
            'xml_tokens': len(xml_tokens),
            'caption_tokens': len(caption_tokens),
            'total_tokens': total_tokens
        })
        all_lengths.append(total_tokens)
    
    print(f"📊 Lunghezze trovate:")
    print(f"   Min: {min(all_lengths)} token")
    print(f"   Max: {max(all_lengths)} token")
    print(f"   Media attuale: {np.mean(all_lengths):.1f} token")
    print(f"   Bins disponibili: {len(examples_by_length)}")
    
    # Strategia per raggiungere media 1000:
    # Campiona più esempi dalle lunghezze maggiori
    
    # Calcola distribuzione target
    min_length = min(all_lengths)
    max_length = max(all_lengths)
    
    # Per media 1000, dobbiamo bilanciare verso lunghezze maggiori
    # Usiamo distribuzione pesata verso l'alto
    
    selected_examples = []
    length_bins = sorted(examples_by_length.keys())
    
    print(f"🎯 Campionamento per media {target_avg} token...")
    
    # Calcola pesi per ogni bin (più peso ai bin lunghi)
    weights = {}
    for length_bin in length_bins:
        # Peso proporzionale alla lunghezza per spingere la media verso l'alto
        weight = max(1, length_bin / 200)  # Più peso ai bin lunghi
        weights[length_bin] = weight
    
    # Normalizza pesi
    total_weight = sum(weights.values())
    for length_bin in weights:
        weights[length_bin] /= total_weight
    
    # Campiona esempi secondo i pesi
    remaining_examples = max_examples
    
    for length_bin in length_bins:
        available = len(examples_by_length[length_bin])
        if available == 0:
            continue
            
        # Calcola quanti esempi prendere da questo bin
        target_from_bin = int(weights[length_bin] * max_examples)
        actual_from_bin = min(target_from_bin, available, remaining_examples)
        
        if actual_from_bin > 0:
            # Campiona casualmente dal bin
            sampled = np.random.choice(
                len(examples_by_length[length_bin]), 
                size=actual_from_bin, 
                replace=False
            )
            
            for idx in sampled:
                selected_examples.append(examples_by_length[length_bin][idx])
            
            remaining_examples -= actual_from_bin
            print(f"   Bin {length_bin:3d}: {actual_from_bin:5d} esempi (disponibili: {available})")
        
        if remaining_examples <= 0:
            break
    
    # Verifica media ottenuta
    selected_lengths = [ex['total_tokens'] for ex in selected_examples]
    actual_avg = np.mean(selected_lengths)
    
    print(f"\n📊 RISULTATI CAMPIONAMENTO:")
    print(f"   Esempi selezionati: {len(selected_examples)}")
    print(f"   Media ottenuta: {actual_avg:.1f} token")
    print(f"   Target: {target_avg} token")
    print(f"   Differenza: {actual_avg - target_avg:+.1f} token")
    print(f"   Min: {min(selected_lengths)} token")
    print(f"   Max: {max(selected_lengths)} token")
    print(f"   95° percentile: {np.percentile(selected_lengths, 95):.1f} token")
    
    # Se la media è troppo bassa, aggiungi esempi lunghi
    if actual_avg < target_avg * 0.9:  # Se sotto il 90% del target
        print(f"🔧 Media troppo bassa, aggiungendo esempi lunghi...")
        
        # Trova esempi molto lunghi non ancora selezionati
        long_bins = [b for b in length_bins if b >= target_avg]
        for length_bin in sorted(long_bins, reverse=True):
            if actual_avg >= target_avg * 0.95:  # Stop al 95% del target
                break
                
            available = examples_by_length[length_bin]
            # Prendi esempi non ancora selezionati
            for example in available:
                if example not in selected_examples and len(selected_examples) < max_examples:
                    selected_examples.append(example)
                    selected_lengths.append(example['total_tokens'])
                    actual_avg = np.mean(selected_lengths)
                    
                    if actual_avg >= target_avg * 0.95:
                        break
    
    # Statistiche finali
    final_lengths = [ex['total_tokens'] for ex in selected_examples]
    final_avg = np.mean(final_lengths)
    
    print(f"\n✅ DATASET FINALE:")
    print(f"   Esempi: {len(selected_examples)}")
    print(f"   Media: {final_avg:.1f} token")
    print(f"   Mediana: {np.median(final_lengths):.1f} token")
    print(f"   Std: {np.std(final_lengths):.1f} token")
    print(f"   Range: {min(final_lengths)}-{max(final_lengths)} token")
    
    # Crea grafici
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # Distribuzione originale vs finale
    axes[0].hist(all_lengths, bins=50, alpha=0.5, label=f'Originale (μ={np.mean(all_lengths):.1f})', color='blue')
    axes[0].hist(final_lengths, bins=50, alpha=0.7, label=f'Finale (μ={final_avg:.1f})', color='red')
    axes[0].axvline(target_avg, color='green', linestyle='--', label=f'Target: {target_avg}')
    axes[0].set_title('Distribuzione Lunghezze Token')
    axes[0].set_xlabel('Token')
    axes[0].set_ylabel('Frequenza')
    axes[0].legend()
    
    # Distribuzione per bins
    bin_counts = defaultdict(int)
    for length in final_lengths:
        bin_counts[(length // 100) * 100] += 1
    
    bins = sorted(bin_counts.keys())
    counts = [bin_counts[b] for b in bins]
    
    axes[1].bar(bins, counts, width=80, alpha=0.7, color='orange')
    axes[1].set_title('Distribuzione per Bins (100 token)')
    axes[1].set_xlabel('Lunghezza Token (bin)')
    axes[1].set_ylabel('Numero Esempi')
    
    plt.tight_layout()
    
    # Salva grafico
    os.makedirs(output_dir, exist_ok=True)
    plot_path = os.path.join(output_dir, f'dataset_1000avg_{tokenizer_name.replace("/", "_")}.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"📊 Grafico salvato: {plot_path}")
    
    # Split 90/10
    np.random.shuffle(selected_examples)
    train_size = int(0.9 * len(selected_examples))
    train_data = selected_examples[:train_size]
    test_data = selected_examples[train_size:]
    
    # Salva dataset
    train_file = os.path.join(output_dir, f'train_set_1000avg_{len(train_data)}.json')
    test_file = os.path.join(output_dir, f'test_set_1000avg_{len(test_data)}.json')
    
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, ensure_ascii=False, indent=2)
    
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"💾 Dataset salvato:")
    print(f"   Train: {train_file} ({len(train_data)} esempi)")
    print(f"   Test: {test_file} ({len(test_data)} esempi)")
    
    # Calcola step per epoca
    batch_sizes = [2, 4, 8, 16, 32]
    print(f"\n📈 STEP PER EPOCA (dataset {len(train_data)} esempi):")
    for batch_size in batch_sizes:
        steps_per_epoch = len(train_data) // batch_size
        print(f"   Batch size {batch_size}: {steps_per_epoch} step/epoca")
    
    return train_file, test_file, {
        'total_examples': len(selected_examples),
        'train_examples': len(train_data),
        'test_examples': len(test_data),
        'avg_tokens': final_avg,
        'median_tokens': np.median(final_lengths),
        'std_tokens': np.std(final_lengths),
        'min_tokens': min(final_lengths),
        'max_tokens': max(final_lengths)
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_file", required=True, help="File dataset completo")
    parser.add_argument("--tokenizer", default="google/gemma-2-9b-it", help="Tokenizer da usare")
    parser.add_argument("--target_avg", type=int, default=1000, help="Lunghezza media target")
    parser.add_argument("--max_examples", type=int, default=100000, help="Numero massimo esempi")
    parser.add_argument("--output_dir", default="data/processed/xml_format_optimized", help="Directory output")
    
    args = parser.parse_args()
    
    # Set random seed per riproducibilità
    np.random.seed(42)
    
    train_file, test_file, stats = create_1000_avg_dataset(
        args.data_file,
        args.tokenizer, 
        args.target_avg,
        args.max_examples,
        args.output_dir
    )
    
    print(f"\n✅ Dataset 1000 token medi creato!")
    print(f"📊 Media finale: {stats['avg_tokens']:.1f} token")
