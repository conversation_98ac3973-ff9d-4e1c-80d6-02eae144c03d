import json
import sys

def convert_jsonl_to_json(input_file, output_file):
    # Leggi il file JSONL
    with open(input_file, 'r') as f:
        lines = f.readlines()
    
    # Converti ogni riga in un oggetto JSON
    data = []
    for line in lines:
        if line.strip():  # Ignora le righe vuote
            try:
                item = json.loads(line)
                data.append(item)
            except json.JSONDecodeError as e:
                print(f"Errore nel decodificare la riga: {line}")
                print(f"Errore: {e}")
    
    # Scrivi il file JSON
    with open(output_file, 'w') as f:
        json.dump(data, f, indent=2)
    
    print(f"Convertito {len(data)} elementi da {input_file} a {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Uso: python convert_jsonl_to_json.py input_file.jsonl output_file.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    convert_jsonl_to_json(input_file, output_file)
