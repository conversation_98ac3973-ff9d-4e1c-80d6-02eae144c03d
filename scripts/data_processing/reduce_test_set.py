import json
import argparse
import random
from pathlib import Path

def reduce_test_set(test_file, train_file, output_test_file, output_train_file, test_percentage=0.1, seed=42):
    """Riduce il test set e aumenta il training set."""
    # Imposta il seed per riproducibilità
    random.seed(seed)
    
    # Carica i dataset
    with open(test_file, 'r') as f:
        test_data = json.load(f)
    
    with open(train_file, 'r') as f:
        train_data = json.load(f)
    
    # Calcola il numero di esempi da mantenere nel test set
    num_test_examples = int(len(test_data) * test_percentage)
    
    # Seleziona casualmente gli esempi per il test set ridotto
    reduced_test_data = random.sample(test_data, num_test_examples)
    
    # Gli esempi rimanenti vanno nel training set
    remaining_test_data = [x for x in test_data if x not in reduced_test_data]
    augmented_train_data = train_data + remaining_test_data
    
    # Crea le directory di output se non esistono
    for output_file in [output_test_file, output_train_file]:
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Salva i dataset
    with open(output_test_file, 'w') as f:
        json.dump(reduced_test_data, f, indent=2)
    
    with open(output_train_file, 'w') as f:
        json.dump(augmented_train_data, f, indent=2)
    
    print(f"Test set ridotto da {len(test_data)} a {len(reduced_test_data)} esempi")
    print(f"Training set aumentato da {len(train_data)} a {len(augmented_train_data)} esempi")
    print(f"Risultati salvati in:")
    print(f"- Test set ridotto: {output_test_file}")
    print(f"- Training set aumentato: {output_train_file}")

def main():
    parser = argparse.ArgumentParser(description='Riduce il test set e aumenta il training set')
    parser.add_argument('--test_file', required=True, help='Percorso del file di test')
    parser.add_argument('--train_file', required=True, help='Percorso del file di training')
    parser.add_argument('--output_test_file', required=True, help='Percorso del file di test ridotto')
    parser.add_argument('--output_train_file', required=True, help='Percorso del file di training aumentato')
    parser.add_argument('--test_percentage', type=float, default=0.1,
                      help='Percentuale di esempi da mantenere nel test set (default: 0.1)')
    parser.add_argument('--seed', type=int, default=42,
                      help='Seed per riproducibilità (default: 42)')
    
    args = parser.parse_args()
    reduce_test_set(
        args.test_file,
        args.train_file,
        args.output_test_file,
        args.output_train_file,
        args.test_percentage,
        args.seed
    )

if __name__ == '__main__':
    main() 