#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare un report completo dei risultati.
"""

import os
import json
import argparse
from typing import Dict, List, Any

def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """Carica un file JSONL."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line))
    return data

def main():
    parser = argparse.ArgumentParser(description="Genera un report completo dei risultati.")
    parser.add_argument("--results_dir", type=str, default="/work/tesi_ediluzio/results/lora_xml", help="Directory contenente i file di risultati.")
    parser.add_argument("--output_file", type=str, default="/work/tesi_ediluzio/results/report.md", help="File di output per il report.")
    args = parser.parse_args()
    
    # Trova tutti i file JSONL nella directory
    result_files = [f for f in os.listdir(args.results_dir) if f.endswith(".jsonl") and not f.startswith("test_")]
    print(f"Trovati {len(result_files)} file di risultati:")
    for f in result_files:
        print(f"- {f}")
    
    # Carica i risultati
    results = {}
    for file_name in result_files:
        file_path = os.path.join(args.results_dir, file_name)
        model_name = file_name.replace("_eval_test.jsonl", "").replace("_test.jsonl", "")
        
        try:
            data = load_jsonl(file_path)
            results[model_name] = data
            print(f"Caricati {len(data)} risultati per il modello {model_name}")
        except Exception as e:
            print(f"Errore nel caricamento del file {file_name}: {e}")
    
    # Genera il report
    report = "# Report dei Risultati\n\n"
    
    # Aggiungi una sezione per ogni modello
    for model_name, data in results.items():
        report += f"## {model_name}\n\n"
        report += f"Numero di risultati: {len(data)}\n\n"
        
        if data:
            report += "### Esempi di Didascalie Generate\n\n"
            
            # Aggiungi alcuni esempi di didascalie generate
            for i, example in enumerate(data[:3]):  # Mostra solo i primi 3 esempi
                report += f"#### Esempio {i+1}\n\n"
                
                # Aggiungi la didascalia di riferimento
                if "true_caption" in example:
                    report += f"**Didascalia di riferimento:** {example['true_caption']}\n\n"
                elif "reference" in example:
                    report += f"**Didascalia di riferimento:** {example['reference']}\n\n"
                
                # Aggiungi la didascalia generata
                if "generated_caption" in example:
                    report += f"**Didascalia generata:** {example['generated_caption']}\n\n"
                elif "generated" in example:
                    report += f"**Didascalia generata:** {example['generated']}\n\n"
        
        report += "\n"
    
    # Aggiungi una sezione di confronto
    report += "## Confronto tra Modelli\n\n"
    report += "| Modello | Numero di Risultati |\n"
    report += "|---------|--------------------|\n"
    for model_name, data in results.items():
        report += f"| {model_name} | {len(data)} |\n"
    
    # Salva il report
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    with open(args.output_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"Report salvato in: {args.output_file}")

if __name__ == "__main__":
    main()
