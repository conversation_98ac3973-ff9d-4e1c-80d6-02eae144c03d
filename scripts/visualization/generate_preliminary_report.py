#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import re
import datetime
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from tabulate import tabulate

def parse_args():
    parser = argparse.ArgumentParser(description="Genera un report preliminare sui modelli addestrati")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs", 
                        help="Directory di output dei training")
    parser.add_argument("--log_dir", type=str, default="/work/tesi_ediluzio/logs", 
                        help="Directory dei log")
    parser.add_argument("--results_dir", type=str, default="/work/tesi_ediluzio/results", 
                        help="Directory dei risultati")
    parser.add_argument("--report_file", type=str, default="/work/tesi_ediluzio/results/preliminary_report.md", 
                        help="File di output del report")
    parser.add_argument("--plot", action="store_true", help="Genera grafici")
    parser.add_argument("--plot_dir", type=str, default="/work/tesi_ediluzio/plots", 
                        help="Directory dove salvare i grafici")
    return parser.parse_args()

def get_model_info(output_dir):
    """Ottiene informazioni sui modelli addestrati"""
    models = []
    
    for model_dir in os.listdir(output_dir):
        model_path = os.path.join(output_dir, model_dir)
        if not os.path.isdir(model_path):
            continue
        
        # Estrai informazioni dal nome del modello
        model_info = {
            "name": model_dir,
            "path": model_path,
            "size_mb": sum(os.path.getsize(os.path.join(model_path, f)) 
                          for f in os.listdir(model_path) 
                          if os.path.isfile(os.path.join(model_path, f))) / (1024 * 1024)
        }
        
        # Determina il tipo di modello
        if "llama31_8b" in model_dir:
            model_info["base_model"] = "Llama 3.1 8B"
        elif "gemma2_9b_it" in model_dir:
            model_info["base_model"] = "Gemma 2 9B IT"
        else:
            model_info["base_model"] = "Altro"
        
        # Determina se usa tokenizer personalizzato
        model_info["custom_tokenizer"] = "custom_token" in model_dir
        
        # Determina se è un modello di convergenza
        model_info["convergence"] = "convergence" in model_dir
        
        # Ottieni il numero di checkpoint
        checkpoints = [d for d in os.listdir(model_path) if d.startswith("checkpoint-") and os.path.isdir(os.path.join(model_path, d))]
        model_info["num_checkpoints"] = len(checkpoints)
        
        # Ottieni l'ultimo checkpoint
        if checkpoints:
            last_checkpoint = max([int(c.split("-")[1]) for c in checkpoints])
            model_info["last_checkpoint"] = last_checkpoint
        else:
            model_info["last_checkpoint"] = None
        
        # Verifica se il modello è completo (ha il file adapter_model.bin)
        model_info["is_complete"] = os.path.exists(os.path.join(model_path, "adapter_model.bin"))
        
        # Leggi il file di configurazione dell'adattatore se esiste
        adapter_config_path = os.path.join(model_path, "adapter_config.json")
        if os.path.exists(adapter_config_path):
            with open(adapter_config_path, "r") as f:
                adapter_config = json.load(f)
                model_info["adapter_config"] = adapter_config
                
                # Estrai informazioni rilevanti dalla configurazione
                model_info["lora_r"] = adapter_config.get("r", "N/A")
                model_info["lora_alpha"] = adapter_config.get("lora_alpha", "N/A")
                model_info["target_modules"] = adapter_config.get("target_modules", [])
        
        models.append(model_info)
    
    return models

def get_log_info(log_dir, model_name):
    """Ottiene informazioni dai file di log per un modello specifico"""
    log_info = {}
    
    # Cerca i file di log relativi al modello
    log_files = [f for f in os.listdir(log_dir) if model_name in f and f.endswith(".err")]
    
    if not log_files:
        return None
    
    # Ordina i file di log per data di modifica (dal più recente)
    log_files.sort(key=lambda f: os.path.getmtime(os.path.join(log_dir, f)), reverse=True)
    
    # Analizza il file di log più recente
    latest_log = os.path.join(log_dir, log_files[0])
    
    with open(latest_log, "r") as f:
        content = f.read()
    
    # Estrai informazioni rilevanti
    
    # Estrai gli step completati
    step_matches = re.findall(r"Step\s+(\d+)/(\d+)", content)
    if step_matches:
        log_info["current_step"] = step_matches[-1][0]
        log_info["total_steps"] = step_matches[-1][1]
    
    # Estrai la loss
    loss_matches = re.findall(r"loss\s*=\s*([\d\.]+)", content)
    if loss_matches:
        log_info["final_loss"] = loss_matches[-1]
        log_info["loss_history"] = loss_matches
        
        # Calcola la riduzione percentuale della loss
        if len(loss_matches) > 1:
            initial_loss = float(loss_matches[0])
            final_loss = float(loss_matches[-1])
            log_info["loss_reduction"] = (initial_loss - final_loss) / initial_loss * 100
    
    # Estrai il learning rate
    lr_matches = re.findall(r"learning_rate\s*=\s*([\d\.e\-]+)", content)
    if lr_matches:
        log_info["final_lr"] = lr_matches[-1]
    
    # Estrai il tempo per step
    time_matches = re.findall(r"(\d+\.\d+) s/it", content)
    if time_matches:
        times = [float(t) for t in time_matches]
        log_info["avg_time_per_step"] = sum(times) / len(times)
        log_info["total_training_time"] = sum(times)
    
    # Estrai i checkpoint salvati
    checkpoint_matches = re.findall(r"Saving model checkpoint to ([^\n]+)", content)
    if checkpoint_matches:
        log_info["checkpoints"] = checkpoint_matches
    
    # Verifica se il training è stato completato
    if "Training completed" in content:
        log_info["training_completed"] = True
    elif "Early stopping attivato" in content:
        log_info["early_stopping_triggered"] = True
    elif "DUE TO TIME LIMIT" in content:
        log_info["time_limit_reached"] = True
    
    return log_info

def plot_loss_comparison(models_data, output_dir=None):
    """Genera un grafico di confronto delle curve di loss"""
    plt.figure(figsize=(12, 8))
    
    for model in models_data:
        if "log_info" in model and model["log_info"] and "loss_history" in model["log_info"]:
            loss_history = [float(loss) for loss in model["log_info"]["loss_history"]]
            steps = list(range(1, len(loss_history) + 1))
            
            # Usa una versione abbreviata del nome del modello per la leggenda
            model_name = model["name"]
            if "llama31_8b" in model_name:
                display_name = "Llama 3.1 8B"
            elif "gemma2_9b_it" in model_name:
                display_name = "Gemma 2 9B IT"
            else:
                display_name = model_name
                
            if "custom_token" in model_name:
                display_name += " (Custom Token)"
            else:
                display_name += " (No Token)"
                
            plt.plot(steps, loss_history, label=display_name)
    
    plt.title("Confronto delle curve di apprendimento")
    plt.xlabel("Step")
    plt.ylabel("Loss")
    plt.grid(True)
    plt.legend()
    
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        plt.savefig(os.path.join(output_dir, "loss_comparison.png"))
        return os.path.join(output_dir, "loss_comparison.png")
    else:
        plt.show()
        return None

def generate_markdown_report(models_data, args, loss_comparison_path=None):
    """Genera un report in formato Markdown"""
    now = datetime.datetime.now()
    
    report = f"""# Report Preliminare dei Modelli Addestrati

Data: {now.strftime("%d/%m/%Y %H:%M:%S")}

## Panoramica dei Modelli

"""
    
    # Tabella dei modelli
    table_data = []
    headers = ["Modello", "Base", "Tokenizer", "Convergenza", "Completo", "Ultimo Checkpoint", "Dimensione (MB)"]
    
    for model in models_data:
        table_data.append([
            model["name"],
            model["base_model"],
            "Personalizzato" if model["custom_tokenizer"] else "Standard",
            "Sì" if model["convergence"] else "No",
            "Sì" if model["is_complete"] else "No",
            model["last_checkpoint"] if model["last_checkpoint"] else "N/A",
            f"{model['size_mb']:.2f}"
        ])
    
    report += tabulate(table_data, headers=headers, tablefmt="pipe") + "\n\n"
    
    # Dettagli dei modelli
    report += "## Dettagli dei Modelli\n\n"
    
    for model in models_data:
        report += f"### {model['name']}\n\n"
        
        report += f"- **Modello base**: {model['base_model']}\n"
        report += f"- **Tokenizer personalizzato**: {'Sì' if model['custom_tokenizer'] else 'No'}\n"
        report += f"- **Training fino a convergenza**: {'Sì' if model['convergence'] else 'No'}\n"
        report += f"- **Modello completo**: {'Sì' if model['is_complete'] else 'No'}\n"
        report += f"- **Numero di checkpoint**: {model['num_checkpoints']}\n"
        report += f"- **Ultimo checkpoint**: {model['last_checkpoint'] if model['last_checkpoint'] else 'N/A'}\n"
        report += f"- **Dimensione**: {model['size_mb']:.2f} MB\n"
        
        if "lora_r" in model:
            report += f"- **LoRA r**: {model['lora_r']}\n"
        
        if "lora_alpha" in model:
            report += f"- **LoRA alpha**: {model['lora_alpha']}\n"
        
        if "target_modules" in model and model["target_modules"]:
            report += f"- **Target modules**: {', '.join(model['target_modules'])}\n"
        
        if "log_info" in model and model["log_info"]:
            log_info = model["log_info"]
            report += "\n**Informazioni di training:**\n\n"
            
            if "current_step" in log_info and "total_steps" in log_info:
                report += f"- **Progresso**: Step {log_info['current_step']}/{log_info['total_steps']} "
                report += f"({float(log_info['current_step']) / float(log_info['total_steps']) * 100:.2f}%)\n"
            
            if "final_loss" in log_info:
                report += f"- **Loss finale**: {log_info['final_loss']}\n"
            
            if "loss_reduction" in log_info:
                report += f"- **Riduzione loss**: {log_info['loss_reduction']:.2f}%\n"
            
            if "avg_time_per_step" in log_info:
                report += f"- **Tempo medio per step**: {log_info['avg_time_per_step']:.2f} s\n"
            
            if "total_training_time" in log_info:
                hours = log_info['total_training_time'] / 3600
                report += f"- **Tempo totale di training**: {hours:.2f} ore\n"
            
            if "training_completed" in log_info and log_info["training_completed"]:
                report += "- **Stato**: Training completato\n"
            elif "early_stopping_triggered" in log_info and log_info["early_stopping_triggered"]:
                report += "- **Stato**: Early stopping attivato\n"
            elif "time_limit_reached" in log_info and log_info["time_limit_reached"]:
                report += "- **Stato**: Interrotto per limite di tempo\n"
            else:
                report += "- **Stato**: In corso o sconosciuto\n"
        
        report += "\n"
    
    # Confronto delle curve di apprendimento
    if loss_comparison_path:
        report += "## Confronto delle Curve di Apprendimento\n\n"
        report += f"![Confronto delle curve di apprendimento]({loss_comparison_path})\n\n"
    
    # Stato attuale e prossimi passi
    report += "## Stato Attuale e Prossimi Passi\n\n"
    
    # Verifica lo stato dei training
    llama_no_token_complete = any(m["name"].startswith("llama31_8b_lora_xml_no_token_convergence") and m["is_complete"] for m in models_data)
    gemma_no_token_complete = any(m["name"].startswith("gemma2_9b_it_lora_xml_no_token_convergence") and m["is_complete"] for m in models_data)
    llama_custom_token_complete = any(m["name"].startswith("llama31_8b_lora_xml_custom_token_convergence") and m["is_complete"] for m in models_data)
    gemma_custom_token_complete = any(m["name"].startswith("gemma2_9b_it_lora_xml_custom_token_convergence") and m["is_complete"] for m in models_data)
    
    report += "### Stato dei Training\n\n"
    report += f"- **Llama 3.1 8B senza tokenizer personalizzato**: {'Completato' if llama_no_token_complete else 'In corso'}\n"
    report += f"- **Gemma 2 9B IT senza tokenizer personalizzato**: {'Completato' if gemma_no_token_complete else 'In corso'}\n"
    report += f"- **Llama 3.1 8B con tokenizer personalizzato**: {'Completato' if llama_custom_token_complete else 'Non avviato' if not llama_no_token_complete else 'In attesa del completamento del modello base'}\n"
    report += f"- **Gemma 2 9B IT con tokenizer personalizzato**: {'Completato' if gemma_custom_token_complete else 'Non avviato' if not gemma_no_token_complete else 'In attesa del completamento del modello base'}\n\n"
    
    report += "### Prossimi Passi\n\n"
    
    if not llama_no_token_complete or not gemma_no_token_complete:
        report += "1. **Completare i training senza tokenizer personalizzato**\n"
        report += "   - Attendere il completamento dei training in corso\n"
        report += "   - Riavviare i training se interrotti per limite di tempo\n\n"
    
    if (llama_no_token_complete and not llama_custom_token_complete) or (gemma_no_token_complete and not gemma_custom_token_complete):
        report += "2. **Avviare i training con tokenizer personalizzato**\n"
        report += "   - Utilizzare i modelli convergenti come base\n"
        report += "   - Avviare i training con tokenizer personalizzato\n\n"
    
    if llama_no_token_complete or gemma_no_token_complete:
        report += "3. **Eseguire l'inferenza per i modelli completati**\n"
        report += "   - Avviare l'inferenza per i modelli senza tokenizer personalizzato completati\n"
        report += "   - Analizzare i risultati preliminari\n\n"
    
    if llama_custom_token_complete or gemma_custom_token_complete:
        report += "4. **Eseguire l'inferenza per i modelli con tokenizer personalizzato**\n"
        report += "   - Avviare l'inferenza per i modelli con tokenizer personalizzato completati\n"
        report += "   - Confrontare i risultati con quelli dei modelli senza tokenizer personalizzato\n\n"
    
    if (llama_no_token_complete and llama_custom_token_complete) or (gemma_no_token_complete and gemma_custom_token_complete):
        report += "5. **Eseguire la valutazione completa**\n"
        report += "   - Valutare le prestazioni di tutti i modelli completati\n"
        report += "   - Generare report comparativi\n\n"
    
    return report

def main():
    args = parse_args()
    
    # Ottieni informazioni sui modelli
    models = get_model_info(args.output_dir)
    
    if not models:
        print("Nessun modello trovato nella directory specificata.")
        return
    
    # Ottieni informazioni dai log per ogni modello
    for model in models:
        log_info = get_log_info(args.log_dir, model["name"])
        if log_info:
            model["log_info"] = log_info
    
    # Genera il grafico di confronto delle curve di apprendimento
    loss_comparison_path = None
    if args.plot:
        loss_comparison_path = plot_loss_comparison(models, args.plot_dir)
        if loss_comparison_path:
            # Converti il percorso assoluto in percorso relativo per il markdown
            loss_comparison_path = os.path.relpath(loss_comparison_path, os.path.dirname(args.report_file))
    
    # Genera il report
    report = generate_markdown_report(models, args, loss_comparison_path)
    
    # Salva il report
    os.makedirs(os.path.dirname(args.report_file), exist_ok=True)
    with open(args.report_file, "w") as f:
        f.write(report)
    
    print(f"Report generato con successo: {args.report_file}")

if __name__ == "__main__":
    main()
