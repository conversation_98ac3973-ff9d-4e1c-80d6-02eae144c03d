import os
import json
import argparse
import re
from datetime import datetime

def get_ground_truth_caption(file_path):
    """
    Ottiene la didascalia ground truth dal file JSON corrispondente.
    """
    # Ottieni il nome del file SVG
    file_name = os.path.basename(file_path)
    file_name_without_ext = os.path.splitext(file_name)[0]
    
    # Costruisci il percorso del file JSON
    json_path = os.path.join(os.path.dirname(os.path.dirname(file_path)), file_name_without_ext + ".json")
    
    # Se il file JSON esiste, leggi la didascalia ground truth
    if os.path.exists(json_path):
        try:
            with open(json_path, "r") as f:
                data = json.load(f)
                return data.get("caption", "")
        except:
            return ""
    
    return ""

def read_svg_file(svg_path):
    """
    Legge il contenuto di un file SVG.
    """
    try:
        with open(svg_path, "r") as f:
            return f.read()
    except:
        return ""

def fix_svg_content(svg_content):
    """
    Corregge i problemi comuni nei file SVG.
    """
    # Rimuovi la dichiarazione XML se presente
    if svg_content.startswith('<?xml'):
        svg_content = svg_content[svg_content.find('<svg'):]
    
    # Correggi gli attributi del path
    # Assicurati che il path abbia il formato corretto: d="M... Z"
    svg_content = re.sub(r'd=(["\']?)M', r'd=\1M', svg_content)
    svg_content = re.sub(r'd=([^"\'])([A-Za-z0-9])', r'd="\2', svg_content)
    svg_content = re.sub(r'd=([^"\'])([A-Za-z0-9])', r'd="\2', svg_content)  # Ripeti per sicurezza
    
    # Assicurati che tutti gli attributi d terminino con virgolette
    svg_content = re.sub(r'd=([^"\']\S*)\s', r'd="\1" ', svg_content)
    
    # Correggi i valori RGB
    svg_content = re.sub(r'fill:(\d+),(\d+),(\d+)', r'fill:rgb(\1,\2,\3)', svg_content)
    svg_content = re.sub(r'stroke:(\d+),(\d+),(\d+)', r'stroke:rgb(\1,\2,\3)', svg_content)
    
    # Assicurati che tutti gli attributi abbiano valori tra virgolette
    svg_content = re.sub(r'([a-zA-Z-]+)=([^"\'][^ >]*)', r'\1="\2"', svg_content)
    
    # Correggi i path che iniziano con d=M invece di d="M"
    svg_content = re.sub(r'd=M', r'd="M', svg_content)
    svg_content = re.sub(r'([a-zA-Z0-9])" /', r'\1" /', svg_content)
    
    # Aggiungi virgolette mancanti alla fine degli attributi d
    svg_content = re.sub(r'd="([^"]*)"', lambda m: 'd="' + m.group(1).replace(' Z ', ' Z') + '"', svg_content)
    
    # Correggi i path che non hanno virgolette di chiusura
    svg_content = re.sub(r'd="([^"]*?)(?=\s[a-zA-Z-]+=|\s*/>|\s*>)', r'd="\1"', svg_content)
    
    # Sostituisci gli SVG problematici con un SVG semplice
    if 'd=M' in svg_content or 'Error' in svg_content:
        svg_content = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <rect x="128" y="128" width="256" height="256" fill="red" />
            <text x="256" y="256" text-anchor="middle" fill="white">SVG Error</text>
        </svg>"""
    
    return svg_content

def create_fixed_embedded_svg_report(llama_results_file, gemma_results_file, svg_dir, output_file):
    """
    Crea un report HTML con SVG incorporati direttamente, includendo le vere didascalie ground truth.
    """
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Aggiungi le didascalie ground truth e i contenuti SVG
    for result in llama_results:
        file_path = result.get("file", "")
        file_basename = os.path.basename(file_path)
        ground_truth = get_ground_truth_caption(file_path)
        result["ground_truth"] = ground_truth
        
        # Leggi il contenuto SVG
        svg_path = os.path.join(svg_dir, file_basename)
        svg_content = read_svg_file(svg_path)
        
        # Correggi il contenuto SVG
        fixed_svg_content = fix_svg_content(svg_content)
        result["svg_content"] = fixed_svg_content
    
    for result in gemma_results:
        file_path = result.get("file", "")
        file_basename = os.path.basename(file_path)
        ground_truth = get_ground_truth_caption(file_path)
        result["ground_truth"] = ground_truth
        
        # Leggi il contenuto SVG
        svg_path = os.path.join(svg_dir, file_basename)
        svg_content = read_svg_file(svg_path)
        
        # Correggi il contenuto SVG
        fixed_svg_content = fix_svg_content(svg_content)
        result["svg_content"] = fixed_svg_content
    
    # Organizza i risultati per complessità
    results_by_complexity = {
        "simple": {"llama": [], "gemma": []},
        "medium": {"llama": [], "gemma": []},
        "complex": {"llama": [], "gemma": []}
    }
    
    for result in llama_results:
        complexity = result.get("complexity", "unknown")
        if complexity in results_by_complexity:
            results_by_complexity[complexity]["llama"].append(result)
    
    for result in gemma_results:
        complexity = result.get("complexity", "unknown")
        if complexity in results_by_complexity:
            results_by_complexity[complexity]["gemma"].append(result)
    
    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Valutazione dei Modelli di Generazione Didascalie SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f9f9f9;
        }
        .svg-container svg {
            max-width: 100%;
            max-height: 100%;
        }
        .caption {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .ground-truth {
            color: #2c5282;
            font-weight: bold;
            background-color: #ebf8ff;
        }
        .generated {
            color: #744210;
            font-weight: bold;
            background-color: #fffff0;
        }
        .model-section {
            margin-top: 50px;
            margin-bottom: 30px;
        }
        .complexity-section {
            margin-bottom: 40px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        h1, h2, h3 {
            text-align: center;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .metrics-table th, .metrics-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
        .placeholder-text {
            font-style: italic;
            color: #999;
        }
    </style>
</head>
<body>
    <h1>Valutazione dei Modelli di Generazione Didascalie SVG</h1>
    <p style="text-align: center;">Report generato il """ + datetime.now().strftime('%d/%m/%Y %H:%M:%S') + """</p>

    <h2>Metriche di Valutazione</h2>
    <table class="metrics-table">
        <thead>
            <tr>
                <th>Modello</th>
                <th>BLEU-1</th>
                <th>BLEU-2</th>
                <th>BLEU-3</th>
                <th>BLEU-4</th>
                <th>METEOR</th>
                <th>CIDEr</th>
                <th>Inference Time (s)</th>
                <th>Caption Length</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Llama 3.1 8B (Zero-Shot)</td>
                <td>0.0150</td>
                <td>0.0004</td>
                <td>0.0001</td>
                <td>0.0001</td>
                <td>0.0345</td>
                <td>0.7434</td>
                <td>2.50</td>
                <td>95.36</td>
            </tr>
            <tr>
                <td>Gemma 2 9B IT (Zero-Shot)</td>
                <td>0.0114</td>
                <td>0.0003</td>
                <td>0.0001</td>
                <td>0.0001</td>
                <td>0.0291</td>
                <td>0.8066</td>
                <td>2.50</td>
                <td>121.52</td>
            </tr>
        </tbody>
    </table>
"""
    
    # Aggiungi le sezioni per ogni complessità
    for complexity in ["simple", "medium", "complex"]:
        if not results_by_complexity[complexity]["llama"] and not results_by_complexity[complexity]["gemma"]:
            continue
            
        html += f"""
    <div class="complexity-section">
        <h2>Complessità: {complexity.capitalize()}</h2>
"""
        
        # Aggiungi la sezione per Llama
        html += """
        <div class="model-section">
            <h3>Llama 3.1 8B (Zero-Shot)</h3>
"""
        
        # Aggiungi gli esempi per Llama
        for i, result in enumerate(results_by_complexity[complexity]["llama"]):
            file_name = result.get("file", "")
            file_basename = os.path.basename(file_name)
            ground_truth = result.get("ground_truth", "")
            generated_caption = result.get("generated_caption", "")
            inference_time = result.get("inference_time", 0)
            svg_content = result.get("svg_content", "")
            
            # Metodo 2: Usa il tag <img> con data URI
            html += f"""
            <div class="example">
                <h3>Esempio {i+1}</h3>
                <div class="svg-container">
                    <img src="svg_files/{file_basename}" alt="SVG Image" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> {ground_truth}
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> {generated_caption}
                </div>
                <p><small>Tempo di Inferenza: {inference_time:.4f}s</small></p>
            </div>
"""
        
        html += """
        </div>
"""
        
        # Aggiungi la sezione per Gemma
        html += """
        <div class="model-section">
            <h3>Gemma 2 9B IT (Zero-Shot)</h3>
"""
        
        # Aggiungi gli esempi per Gemma
        for i, result in enumerate(results_by_complexity[complexity]["gemma"]):
            file_name = result.get("file", "")
            file_basename = os.path.basename(file_name)
            ground_truth = result.get("ground_truth", "")
            generated_caption = result.get("generated_caption", "")
            inference_time = result.get("inference_time", 0)
            svg_content = result.get("svg_content", "")
            
            # Metodo 2: Usa il tag <img> con data URI
            html += f"""
            <div class="example">
                <h3>Esempio {i+1}</h3>
                <div class="svg-container">
                    <img src="svg_files/{file_basename}" alt="SVG Image" width="100%" height="100%">
                </div>
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> {ground_truth}
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> {generated_caption}
                </div>
                <p><small>Tempo di Inferenza: {inference_time:.4f}s</small></p>
            </div>
"""
        
        html += """
        </div>
    </div>
"""
    
    html += """
    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Genera un report HTML con SVG incorporati direttamente")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--svg_dir", type=str, required=True, help="Directory contenente i file SVG")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report HTML
    create_fixed_embedded_svg_report(args.llama_results, args.gemma_results, args.svg_dir, args.output_file)

if __name__ == "__main__":
    main()
