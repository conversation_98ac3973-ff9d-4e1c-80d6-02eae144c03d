import os
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt

def create_radar_chart(metrics_file, output_file, metrics_to_plot=None):
    """
    Create a radar chart for model evaluation metrics using a simpler approach.
    """
    # Load metrics
    with open(metrics_file, 'r') as f:
        metrics = json.load(f)
    
    # Define which metrics to plot
    if metrics_to_plot is None:
        metrics_to_plot = ['bleu1', 'bleu2', 'bleu3', 'bleu4', 'meteor', 'cider']
    
    # Get model names
    model_names = list(metrics.keys())
    
    # Set up the radar chart
    N = len(metrics_to_plot)
    angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()
    angles += angles[:1]  # Close the loop
    
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(polar=True))
    
    # Add metric labels
    plt.xticks(angles[:-1], metrics_to_plot, size=12)
    
    # Set y-axis limits
    ax.set_ylim(0, 1)
    
    # Plot data for each model
    colors = ['b', 'r', 'g', 'c', 'm', 'y', 'k']
    for i, model in enumerate(model_names):
        # Get values for this model
        values = [metrics[model].get(metric, 0) for metric in metrics_to_plot]
        
        # Normalize values to 0-1 range
        max_values = {}
        for metric in metrics_to_plot:
            max_values[metric] = max([metrics[m].get(metric, 0) for m in model_names])
        
        normalized_values = []
        for j, metric in enumerate(metrics_to_plot):
            if max_values[metric] > 0:
                normalized_values.append(values[j] / max_values[metric])
            else:
                normalized_values.append(0)
        
        # Close the loop
        normalized_values += normalized_values[:1]
        
        # Plot values
        color = colors[i % len(colors)]
        ax.plot(angles, normalized_values, color=color, linewidth=2, label=model)
        ax.fill(angles, normalized_values, color=color, alpha=0.25)
        
        # Add actual values as text
        for j, metric in enumerate(metrics_to_plot):
            value = metrics[model].get(metric, 0)
            angle = angles[j]
            radius = normalized_values[j]
            
            # Calculate text position
            x = (radius * 1.1) * np.cos(angle)
            y = (radius * 1.1) * np.sin(angle)
            
            # Add text
            plt.text(angle, radius * 1.1, f"{value:.4f}", 
                     horizontalalignment='center',
                     verticalalignment='center',
                     size=8,
                     color=color)
    
    # Add legend
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    # Add title
    plt.title('Model Evaluation Metrics', size=15)
    
    # Save the figure
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Radar chart saved to {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Generate radar chart for model evaluation metrics")
    parser.add_argument("--metrics_file", type=str, required=True, help="JSON file with metrics")
    parser.add_argument("--output_file", type=str, required=True, help="Output image file")
    parser.add_argument("--metrics", type=str, nargs="+", help="Metrics to plot")
    
    args = parser.parse_args()
    
    # Create radar chart
    create_radar_chart(args.metrics_file, args.output_file, args.metrics)

if __name__ == "__main__":
    main()
