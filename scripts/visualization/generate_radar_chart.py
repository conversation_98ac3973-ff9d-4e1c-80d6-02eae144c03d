import os
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.path import Path
from matplotlib.spines import Spine
from matplotlib.transforms import Affine2D

def radar_factory(num_vars, frame='circle'):
    """
    Create a radar chart with `num_vars` axes.
    """
    # Calculate evenly-spaced axis angles
    theta = np.linspace(0, 2*np.pi, num_vars, endpoint=False)
    
    class RadarAxes(plt.PolarAxes):
        name = 'radar'
        
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.set_theta_zero_location('N')
            
        def fill(self, *args, closed=True, **kwargs):
            """Override fill so that line is closed by default"""
            return super().fill(closed=closed, *args, **kwargs)
            
        def plot(self, *args, **kwargs):
            """Override plot so that line is closed by default"""
            lines = super().plot(*args, **kwargs)
            for line in lines:
                self._close_line(line)
                
        def _close_line(self, line):
            x, y = line.get_data()
            # FIXME: markers at x[0], y[0] get doubled-up
            if x[0] != x[-1]:
                x = np.concatenate((x, [x[0]]))
                y = np.concatenate((y, [y[0]]))
                line.set_data(x, y)
                
        def set_varlabels(self, labels):
            self.set_thetagrids(np.degrees(theta), labels)
            
        def _gen_axes_patch(self):
            if frame == 'circle':
                return plt.Circle((0.5, 0.5), 0.5)
            elif frame == 'polygon':
                return plt.Polygon(self.unit_poly_verts, closed=True, edgecolor='k')
            else:
                raise ValueError("unknown value for 'frame': %s" % frame)
                
        def _gen_axes_spines(self):
            if frame == 'circle':
                return super()._gen_axes_spines()
            elif frame == 'polygon':
                # spine_type must be 'left'/'right'/'top'/'bottom'/'circle'.
                spine = Spine(axes=self,
                              spine_type='circle',
                              path=Path(self.unit_poly_verts))
                spine.set_transform(Affine2D().scale(.5).translate(.5, .5)
                                    + self.transAxes)
                return {'polar': spine}
            else:
                raise ValueError("unknown value for 'frame': %s" % frame)
                
        def unit_poly_verts(self, theta=theta):
            """Return vertices of polygon for subplot axes.
            This polygon is circumscribed by a unit circle centered at (0.5, 0.5)
            """
            x0, y0, r = [0.5] * 3
            verts = [(r*np.cos(t) + x0, r*np.sin(t) + y0) for t in theta]
            return verts
            
    register_projection(RadarAxes)
    return theta

def register_projection(proj_class):
    """Register a new projection."""
    name = proj_class.name
    plt.projections.register_projection(proj_class)

def create_radar_chart(metrics_file, output_file, metrics_to_plot=None):
    """
    Create a radar chart for model evaluation metrics.
    """
    # Load metrics
    with open(metrics_file, 'r') as f:
        metrics = json.load(f)
    
    # Define which metrics to plot
    if metrics_to_plot is None:
        metrics_to_plot = ['bleu1', 'bleu2', 'bleu3', 'bleu4', 'meteor', 'cider']
    
    # Get model names and normalize metrics
    model_names = list(metrics.keys())
    data = []
    
    # Find max values for normalization
    max_values = {}
    for metric in metrics_to_plot:
        max_values[metric] = max([metrics[model].get(metric, 0) for model in model_names])
    
    # Normalize data
    for model in model_names:
        model_data = []
        for metric in metrics_to_plot:
            value = metrics[model].get(metric, 0)
            # Normalize to 0-1 range
            if max_values[metric] > 0:
                normalized_value = value / max_values[metric]
            else:
                normalized_value = 0
            model_data.append(normalized_value)
        data.append(model_data)
    
    # Set up the radar chart
    N = len(metrics_to_plot)
    theta = radar_factory(N, frame='polygon')
    
    # Create the figure
    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='radar'))
    
    # Plot the data
    colors = ['b', 'r', 'g', 'c', 'm', 'y', 'k']
    for i, (d, model) in enumerate(zip(data, model_names)):
        color = colors[i % len(colors)]
        ax.plot(theta, d, color=color, label=model)
        ax.fill(theta, d, facecolor=color, alpha=0.25)
    
    # Set labels and title
    ax.set_varlabels(metrics_to_plot)
    ax.set_title('Model Evaluation Metrics', size=15, y=1.1)
    
    # Add legend
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    # Add actual values as text
    for i, model in enumerate(model_names):
        for j, metric in enumerate(metrics_to_plot):
            value = metrics[model].get(metric, 0)
            angle = theta[j]
            radius = data[i][j]
            
            # Calculate text position
            x = (radius * 1.1) * np.cos(angle)
            y = (radius * 1.1) * np.sin(angle)
            
            # Add text
            plt.text(x, y, f"{value:.4f}", 
                     horizontalalignment='center',
                     verticalalignment='center',
                     size=8,
                     color=colors[i % len(colors)])
    
    # Save the figure
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Radar chart saved to {output_file}")

def create_metrics_json(checkpoint_dirs, output_file):
    """
    Create a JSON file with metrics from multiple checkpoint directories.
    """
    metrics = {}
    
    for checkpoint_dir in checkpoint_dirs:
        # Get model name from directory
        model_name = os.path.basename(checkpoint_dir)
        
        # Look for evaluation results
        eval_file = os.path.join(checkpoint_dir, 'eval_results.json')
        if os.path.exists(eval_file):
            with open(eval_file, 'r') as f:
                model_metrics = json.load(f)
            metrics[model_name] = model_metrics
    
    # Save metrics to file
    with open(output_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    print(f"Metrics saved to {output_file}")
    return metrics

def main():
    parser = argparse.ArgumentParser(description="Generate radar chart for model evaluation metrics")
    parser.add_argument("--metrics_file", type=str, help="JSON file with metrics")
    parser.add_argument("--checkpoint_dirs", type=str, nargs="+", help="Directories with checkpoint evaluation results")
    parser.add_argument("--output_file", type=str, required=True, help="Output image file")
    parser.add_argument("--metrics", type=str, nargs="+", help="Metrics to plot")
    
    args = parser.parse_args()
    
    # Create metrics file if not provided
    if args.metrics_file is None and args.checkpoint_dirs is not None:
        metrics_file = os.path.splitext(args.output_file)[0] + "_metrics.json"
        create_metrics_json(args.checkpoint_dirs, metrics_file)
    else:
        metrics_file = args.metrics_file
    
    # Create radar chart
    create_radar_chart(metrics_file, args.output_file, args.metrics)

if __name__ == "__main__":
    main()
