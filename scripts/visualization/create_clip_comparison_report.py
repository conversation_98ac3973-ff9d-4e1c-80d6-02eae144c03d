#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per creare un report comparativo dei CLIP Score.
"""

import os
import json
import argparse
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_clip_scores(results_dir: str) -> dict:
    """
    Carica i CLIP score da tutti i file di risultati.

    Args:
        results_dir: Directory contenente i file di risultati

    Returns:
        Dizionario con nome del modello come chiave e metriche aggregate come valore
    """
    clip_scores = {}

    # Trova tutti i file JSON nella directory
    for filename in os.listdir(results_dir):
        if filename.endswith('_clip_scores.json'):
            file_path = os.path.join(results_dir, filename)

            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                model_name = data.get('model_name', os.path.basename(file_path).replace('_clip_scores.json', ''))
                aggregated = data.get('aggregated', {})

                clip_scores[model_name] = aggregated
                logger.info(f"Caricati CLIP score per il modello: {model_name}")
            except Exception as e:
                logger.error(f"Errore nel caricamento del file {file_path}: {e}")

    return clip_scores

def create_comparison_report(clip_scores: dict, output_dir: str):
    """
    Crea un report comparativo dei CLIP score.

    Args:
        clip_scores: Dizionario con nome del modello come chiave e metriche aggregate come valore
        output_dir: Directory di output per il report
    """
    # Crea un DataFrame con le metriche
    metrics = ['mean_generated_clip_score', 'mean_true_clip_score', 'mean_clip_score_ratio']
    data = []

    for model_name, aggregated in clip_scores.items():
        row = {'model': model_name}
        for metric in metrics:
            row[metric] = aggregated.get(metric, 0)
        data.append(row)

    df = pd.DataFrame(data)

    # Salva il DataFrame come CSV
    csv_path = os.path.join(output_dir, 'clip_scores_comparison.csv')
    df.to_csv(csv_path, index=False)
    logger.info(f"Report CSV salvato in: {csv_path}")

    # Crea un grafico a barre per ogni metrica
    for metric in metrics:
        plt.figure(figsize=(12, 6))

        # Ordina i modelli per valore della metrica (decrescente)
        df_sorted = df.sort_values(by=metric, ascending=False)

        # Crea il grafico a barre
        ax = sns.barplot(x='model', y=metric, data=df_sorted)

        # Aggiungi i valori sopra le barre
        for i, v in enumerate(df_sorted[metric]):
            ax.text(i, v + 0.01, f"{v:.4f}", ha="center")

        # Imposta il titolo e le etichette
        plt.title(f'Comparison of {metric} across models')
        plt.xlabel('Model')
        plt.ylabel(metric)
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()

        # Salva il grafico
        plot_path = os.path.join(output_dir, f'comparison_{metric}.png')
        plt.savefig(plot_path, dpi=300)
        plt.close()
        logger.info(f"Grafico per {metric} salvato in: {plot_path}")

    # Crea un grafico radar per confrontare tutte le metriche
    # Prepara i dati per il grafico radar
    categories = metrics
    N = len(categories)

    # Crea la figura
    fig = plt.figure(figsize=(10, 10))
    ax = fig.add_subplot(111, polar=True)

    # Angoli per le categorie
    angles = [n / float(N) * 2 * np.pi for n in range(N)]
    angles += angles[:1]  # Chiudi il cerchio

    # Normalizza i valori per una migliore visualizzazione
    max_values = {}
    for metric in metrics:
        max_values[metric] = df[metric].max()

    # Disegna un grafico per ogni modello
    for i, row in df.iterrows():
        model_name = row['model']
        values = [row[metric] / max_values[metric] for metric in metrics]
        values += values[:1]  # Chiudi il cerchio

        # Disegna il grafico
        ax.plot(angles, values, linewidth=2, linestyle='solid', label=model_name)
        ax.fill(angles, values, alpha=0.1)

    # Imposta le etichette
    plt.xticks(angles[:-1], categories)

    # Aggiungi la legenda
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))

    # Salva il grafico
    radar_path = os.path.join(output_dir, 'clip_scores_radar.png')
    plt.savefig(radar_path, dpi=300)
    plt.close()
    logger.info(f"Grafico radar salvato in: {radar_path}")

    # Crea un heatmap per confrontare tutte le metriche
    plt.figure(figsize=(12, 8))

    # Prepara i dati per l'heatmap
    heatmap_data = df.set_index('model')

    # Crea l'heatmap
    sns.heatmap(heatmap_data, annot=True, cmap='YlGnBu', fmt='.4f', linewidths=.5)

    # Imposta il titolo
    plt.title('Comparison of CLIP scores across models')
    plt.tight_layout()

    # Salva il grafico
    heatmap_path = os.path.join(output_dir, 'clip_scores_heatmap.png')
    plt.savefig(heatmap_path, dpi=300)
    plt.close()
    logger.info(f"Heatmap salvato in: {heatmap_path}")

def main():
    parser = argparse.ArgumentParser(description="Crea un report comparativo dei CLIP score.")
    parser.add_argument("--results_dir", type=str, required=True, help="Directory contenente i file di risultati.")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per il report.")

    args = parser.parse_args()

    # Crea la directory di output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)

    # Carica i CLIP score
    clip_scores = load_clip_scores(args.results_dir)

    if not clip_scores:
        logger.error("Nessun file di CLIP score trovato.")
        return

    # Crea il report comparativo
    create_comparison_report(clip_scores, args.output_dir)

    logger.info("Report comparativo creato con successo!")

if __name__ == "__main__":
    main()
