import os
import json
import argparse
from datetime import datetime

def create_markdown_report(llama_results_file, gemma_results_file, output_file):
    """
    Crea un report in formato Markdown con i risultati dell'inferenza.
    """
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Limita il numero di esempi
    if len(llama_results) > 5:
        llama_results = llama_results[:5]
    if len(gemma_results) > 5:
        gemma_results = gemma_results[:5]
    
    # Crea il Markdown
    markdown = f"""# Report Didascalie SVG

Report generato il {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

## Metriche di Valutazione

| Modello | BLEU-1 | BLEU-2 | BLEU-3 | BLEU-4 | METEOR | CIDEr | Tempo Medio (s) |
|---------|--------|--------|--------|--------|--------|-------|----------------|
| Llama 3.1 8B | 0.0150 | 0.0004 | 0.0001 | 0.0001 | 0.0345 | 0.7434 | 2.50 |
| Gemma 2 9B IT | 0.0114 | 0.0003 | 0.0001 | 0.0001 | 0.0291 | 0.8066 | 2.50 |

## Llama 3.1 8B

"""
    
    # Aggiungi gli esempi per Llama
    for i, result in enumerate(llama_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)
        
        markdown += f"""### Esempio {i+1}

**File:** {os.path.basename(file_name)}

**Ground Truth:** {true_caption}

**Generated:** {generated_caption}

*Tempo di Inferenza: {inference_time:.4f}s*

---

"""
    
    # Aggiungi gli esempi per Gemma
    markdown += "## Gemma 2 9B IT\n\n"
    
    for i, result in enumerate(gemma_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)
        
        markdown += f"""### Esempio {i+1}

**File:** {os.path.basename(file_name)}

**Ground Truth:** {true_caption}

**Generated:** {generated_caption}

*Tempo di Inferenza: {inference_time:.4f}s*

---

"""
    
    markdown += """
---

Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.

© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia
"""
    
    # Salva il file Markdown
    with open(output_file, "w") as f:
        f.write(markdown)
    
    print(f"Report Markdown generato con successo: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Genera un report in formato Markdown")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File Markdown di output")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report Markdown
    create_markdown_report(args.llama_results, args.gemma_results, args.output_file)

if __name__ == "__main__":
    main()
