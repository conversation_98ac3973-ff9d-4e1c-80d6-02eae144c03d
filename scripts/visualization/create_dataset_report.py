import os
import json
import argparse
import base64
from glob import glob

def create_html_report(llama_results_file, gemma_results_file, output_file):
    """Crea un report HTML completo con i risultati dell'inferenza"""
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Organizza i risultati per complessità
    results_by_complexity = {
        "simple": {"llama": [], "gemma": []},
        "medium": {"llama": [], "gemma": []},
        "complex": {"llama": [], "gemma": []}
    }
    
    for result in llama_results:
        complexity = result.get("complexity", "unknown")
        if complexity in results_by_complexity:
            results_by_complexity[complexity]["llama"].append(result)
    
    for result in gemma_results:
        complexity = result.get("complexity", "unknown")
        if complexity in results_by_complexity:
            results_by_complexity[complexity]["gemma"].append(result)
    
    # Crea il report HTML
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>SVG Inference Report</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            h1, h2, h3 {
                color: #333;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .complexity-section {
                margin-bottom: 40px;
                border-bottom: 1px solid #eee;
                padding-bottom: 20px;
            }
            .model-section {
                margin-bottom: 30px;
            }
            .example {
                margin-bottom: 30px;
                padding: 15px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
            }
            .svg-container {
                margin-bottom: 15px;
                padding: 10px;
                background-color: white;
                border: 1px solid #eee;
                border-radius: 5px;
                text-align: center;
            }
            .svg-container svg {
                max-width: 300px;
                max-height: 300px;
            }
            .caption {
                margin-top: 10px;
                padding: 10px;
                background-color: #f0f0f0;
                border-radius: 5px;
            }
            .metrics {
                margin-top: 10px;
                font-size: 0.9em;
                color: #666;
            }
            .model-name {
                font-weight: bold;
                color: #0066cc;
            }
            .file-name {
                font-style: italic;
                color: #666;
                margin-bottom: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>SVG Inference Report</h1>
    """
    
    # Aggiungi le sezioni per ogni complessità
    for complexity in ["simple", "medium", "complex"]:
        html += f"""
            <div class="complexity-section">
                <h2>Complessità: {complexity.capitalize()}</h2>
        """
        
        # Aggiungi la sezione per Llama
        html += """
                <div class="model-section">
                    <h3>Modello: Llama 3.1 8B Instruct</h3>
        """
        
        # Aggiungi gli esempi per Llama
        for result in results_by_complexity[complexity]["llama"]:
            svg_content = result.get("svg", "")
            file_name = result.get("file", "")
            generated_caption = result.get("generated_caption", "")
            inference_time = result.get("inference_time", 0)
            
            html += f"""
                    <div class="example">
                        <div class="file-name">{os.path.basename(file_name)}</div>
                        <div class="svg-container">
                            {svg_content}
                        </div>
                        <div class="caption">
                            <strong>Didascalia generata:</strong>
                            <p>{generated_caption}</p>
                        </div>
                        <div class="metrics">
                            Tempo di inferenza: {inference_time:.2f}s
                        </div>
                    </div>
            """
        
        html += """
                </div>
        """
        
        # Aggiungi la sezione per Gemma
        html += """
                <div class="model-section">
                    <h3>Modello: Gemma 2 9B IT</h3>
        """
        
        # Aggiungi gli esempi per Gemma
        for result in results_by_complexity[complexity]["gemma"]:
            svg_content = result.get("svg", "")
            file_name = result.get("file", "")
            generated_caption = result.get("generated_caption", "")
            inference_time = result.get("inference_time", 0)
            
            html += f"""
                    <div class="example">
                        <div class="file-name">{os.path.basename(file_name)}</div>
                        <div class="svg-container">
                            {svg_content}
                        </div>
                        <div class="caption">
                            <strong>Didascalia generata:</strong>
                            <p>{generated_caption}</p>
                        </div>
                        <div class="metrics">
                            Tempo di inferenza: {inference_time:.2f}s
                        </div>
                    </div>
            """
        
        html += """
                </div>
            </div>
        """
    
    html += """
        </div>
    </body>
    </html>
    """
    
    # Scrivi il report HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Crea un report HTML con i risultati dell'inferenza")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output con il report")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report HTML
    create_html_report(args.llama_results, args.gemma_results, args.output_file)

if __name__ == "__main__":
    main()
