import os
import json
import argparse
import re
from datetime import datetime

def count_path_points(path_d):
    """Conta il numero di punti in un path SVG."""
    # Rimuovi i comandi e conta i punti
    commands = re.findall(r'[A-Za-z]', path_d)
    points = re.findall(r'[-+]?\d*\.\d+|\d+', path_d)
    
    # Stima il numero di punti (approssimativo)
    return len(points) // 2

def estimate_svg_complexity(svg_string):
    """Stima la complessità di un SVG basandosi sul numero di path e punti."""
    # Conta il numero di path
    path_count = svg_string.count('<path')
    
    # Estrai tutti i path d
    path_ds = re.findall(r'd="([^"]*)"', svg_string)
    if not path_ds:
        path_ds = re.findall(r'd=([^"\s>]*)', svg_string)
    
    # Conta i punti totali
    total_points = sum(count_path_points(d) for d in path_ds)
    
    # Calcola la complessità
    complexity = {
        'path_count': path_count,
        'total_points': total_points,
        'avg_points_per_path': total_points / path_count if path_count > 0 else 0
    }
    
    return complexity

def get_token_info():
    """Restituisce informazioni sui token SVG complessi."""
    return """
    <div class="token-info" style="background-color: #fff8e8; padding: 10px; border-radius: 5px; margin-top: 10px; font-family: monospace; font-size: 14px;">
        <p><strong>Token Difficili:</strong></p>
        <ul>
            <li><code>linearGradient</code> - Definisce un gradiente lineare che cambia colore lungo una linea retta</li>
            <li><code>radialGradient</code> - Definisce un gradiente radiale che cambia colore dal centro verso l'esterno</li>
            <li><code>feGaussianBlur</code> - Applica un effetto di sfocatura gaussiana all'elemento</li>
            <li><code>filter</code> - Definisce un filtro SVG che può essere applicato agli elementi</li>
            <li><code>pattern</code> - Definisce un pattern ripetitivo che può essere usato come riempimento</li>
            <li><code>clipPath</code> - Definisce un'area di ritaglio per limitare la visualizzazione di un elemento</li>
            <li><code>stdDeviation</code> - Parametro che controlla l'intensità della sfocatura</li>
        </ul>
    </div>
    """

def create_complex_svg_report(results_file, output_file):
    """Crea un report HTML con i risultati dell'inferenza sui SVG complessi."""
    # Carica i risultati
    with open(results_file, "r") as f:
        examples = json.load(f)
    
    # Raggruppa gli esempi per modello
    examples_by_model = {}
    for example in examples:
        model = example.get("model", "unknown")
        if model not in examples_by_model:
            examples_by_model[model] = []
        examples_by_model[model].append(example)
    
    # Mappa i nomi dei modelli per la visualizzazione
    model_display_names = {
        "Llama-3.1-8B-Instruct": "Llama 3.1 8B (Zero-Shot)",
        "gemma-2-9b-it": "Gemma 2 9B IT (Zero-Shot)",
    }
    
    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Report SVG Complessi con Token Difficili</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            margin: 0 auto;
        }
        .caption {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .ground-truth {
            color: #2c5282;
            font-weight: bold;
            background-color: #ebf8ff;
        }
        .generated {
            color: #744210;
            font-weight: bold;
            background-color: #fffff0;
        }
        .model-section {
            margin-top: 50px;
            margin-bottom: 30px;
        }
        .svg-info {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 14px;
        }
        h1, h2, h3 {
            text-align: center;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .metrics-table th, .metrics-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Report SVG Complessi con Token Difficili</h1>
    <p style="text-align: center;">Report generato il """ + datetime.now().strftime("%d/%m/%Y %H:%M:%S") + """</p>
    
    <p style="text-align: center;">Questo report mostra i risultati dell'inferenza su SVG complessi con token difficili come <code>linearGradient</code>, <code>radialGradient</code>, <code>filter</code>, ecc.</p>
"""
    
    # Aggiungi una sezione per ogni modello
    for model, examples_list in examples_by_model.items():
        model_display = model_display_names.get(model, model)
        
        html += f"""
    <div class="model-section">
        <h2>{model_display}</h2>
"""
        
        # Aggiungi gli esempi per questo modello
        for i, example in enumerate(examples_list):
            # Estrai l'SVG
            svg_string = example.get("svg", "")
            
            # Rimuovi la dichiarazione XML se presente
            if svg_string.startswith('<?xml'):
                svg_string = svg_string[svg_string.find('<svg'):]
            
            # Calcola le informazioni sull'SVG
            svg_info = estimate_svg_complexity(svg_string)
            
            # Ottieni informazioni sui token difficili
            token_info = get_token_info()
            
            html += f"""
        <div class="example">
            <h3>Esempio {i+1}</h3>
            <div class="svg-container">
                {svg_string}
            </div>
            <div class="svg-info">
                <p><strong>Informazioni SVG:</strong></p>
                <ul>
                    <li>Numero di path: {svg_info['path_count']}</li>
                    <li>Punti totali: {svg_info['total_points']}</li>
                    <li>Media punti per path: {svg_info['avg_points_per_path']:.2f}</li>
                    <li>Complessità: {example.get('complexity', 'complex')}</li>
                    <li>Lunghezza SVG: {len(svg_string)} caratteri</li>
                </ul>
            </div>
            {token_info}
            <div class="caption ground-truth">
                <strong>Ground Truth:</strong> {example.get('true_caption', '')}
            </div>
            <div class="caption generated">
                <strong>Generated Caption:</strong> {example.get('generated_caption', '')}
            </div>
            <p><small>Tempo di Inferenza: {example.get('inference_time', 0):.4f}s</small></p>
        </div>
"""
        
        html += """    </div>
"""
    
    # Chiudi l'HTML
    html += """
    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per la valutazione dei modelli su SVG complessi con token difficili.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Crea un report HTML con i risultati dell'inferenza sui SVG complessi")
    parser.add_argument("--results_file", type=str, default="evaluation/complex_svg_results.json", help="File JSON con i risultati dell'inferenza")
    parser.add_argument("--output_file", type=str, default="evaluation/reports/complex_svg_report.html", help="File HTML di output")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report
    create_complex_svg_report(args.results_file, args.output_file)

if __name__ == "__main__":
    main()
