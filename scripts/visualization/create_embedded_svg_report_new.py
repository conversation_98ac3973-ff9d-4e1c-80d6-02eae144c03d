import json
import os

# Leggi il file JSON con le informazioni sugli esempi
examples_file = "/work/tesi_ediluzio/evaluation/reports/svg_images/examples.json"
with open(examples_file, "r") as f:
    examples = json.load(f)

# Funzione per leggere il contenuto di un file SVG
def read_svg_content(filename):
    filepath = os.path.join("/work/tesi_ediluzio/evaluation/reports/svg_images", filename)
    with open(filepath, "r") as f:
        return f.read()

# Crea l'HTML con gli SVG incorporati
html_content = """<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Llama 3.1 8B - Report di Captioning SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .metrics {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
        }
        .metrics-table th, .metrics-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
        .example {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            background-color: #fff;
        }
        .example-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
        }
        .svg-container {
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .svg-container svg {
            max-width: 100%;
            max-height: 200px;
        }
        .caption-container {
            display: flex;
            flex-direction: column;
        }
        .caption-box {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 5px;
        }
        .ground-truth {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
        }
        .generated {
            background-color: #f8f5e8;
            border-left: 4px solid #f39c12;
        }
        .complexity-section {
            margin-top: 40px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        .metrics-image {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Llama 3.1 8B - Report di Captioning SVG</h1>
        
        <div class="metrics">
            <h2>Metriche di Valutazione</h2>
            <p>Il modello Llama 3.1 8B fine-tuned ha ottenuto i seguenti risultati nella generazione di caption per immagini SVG:</p>
            
            <table class="metrics-table">
                <tr>
                    <th>Metrica</th>
                    <th>Valore</th>
                    <th>Descrizione</th>
                </tr>
                <tr>
                    <td>BLEU-1</td>
                    <td>0.4644</td>
                    <td>Precisione delle singole parole</td>
                </tr>
                <tr>
                    <td>BLEU-2</td>
                    <td>0.3307</td>
                    <td>Precisione delle coppie di parole consecutive</td>
                </tr>
                <tr>
                    <td>BLEU-3</td>
                    <td>0.2455</td>
                    <td>Precisione delle triple di parole consecutive</td>
                </tr>
                <tr>
                    <td>BLEU-4</td>
                    <td>0.1652</td>
                    <td>Precisione delle quadruple di parole consecutive</td>
                </tr>
                <tr>
                    <td>METEOR</td>
                    <td>0.4745</td>
                    <td>Valutazione che considera sinonimi e variazioni morfologiche</td>
                </tr>
                <tr>
                    <td>CIDEr</td>
                    <td>0.8078</td>
                    <td>Metrica specifica per la valutazione di caption di immagini</td>
                </tr>
            </table>
            
            <div style="text-align: center; margin-top: 20px;">
                <img src="/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/metrics_radar.png" alt="Radar Chart delle Metriche" class="metrics-image">
            </div>
        </div>
        
        <h2>Esempi di Caption Generate</h2>
        <p>Di seguito sono riportati alcuni esempi di caption generate dal modello Llama 3.1 8B, suddivisi per complessità dell'SVG.</p>
"""

# Aggiungi la sezione SVG Semplici
html_content += """
        <!-- Sezione SVG Semplici -->
        <div class="complexity-section">
            <h3>SVG Semplici</h3>
            <p>Esempi di SVG con forme geometriche di base e pochi elementi.</p>
        </div>
"""

# Aggiungi gli esempi semplici
for i, example in enumerate(examples["simple"]):
    svg_content = read_svg_content(example["filename"])
    html_content += f"""
        <!-- Esempio {i+1} -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    {svg_content}
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> {example["true_caption"]}
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> {example["generated_caption"]}
                    </div>
                </div>
            </div>
        </div>
"""

# Aggiungi la sezione SVG di Media Complessità
html_content += """
        <!-- Sezione SVG di Media Complessità -->
        <div class="complexity-section">
            <h3>SVG di Media Complessità</h3>
            <p>Esempi di SVG con forme più elaborate o combinazioni di elementi.</p>
        </div>
"""

# Aggiungi gli esempi di media complessità
for i, example in enumerate(examples["medium"]):
    svg_content = read_svg_content(example["filename"])
    html_content += f"""
        <!-- Esempio {i+4} -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    {svg_content}
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> {example["true_caption"]}
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> {example["generated_caption"]}
                    </div>
                </div>
            </div>
        </div>
"""

# Aggiungi la sezione SVG Complessi
html_content += """
        <!-- Sezione SVG Complessi -->
        <div class="complexity-section">
            <h3>SVG Complessi</h3>
            <p>Esempi di SVG con forme complesse, più elementi o design elaborati.</p>
        </div>
"""

# Aggiungi gli esempi complessi
for i, example in enumerate(examples["complex"]):
    svg_content = read_svg_content(example["filename"])
    html_content += f"""
        <!-- Esempio {i+7} -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    {svg_content}
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> {example["true_caption"]}
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> {example["generated_caption"]}
                    </div>
                </div>
            </div>
        </div>
"""

# Chiudi l'HTML
html_content += """
        <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 0.9em;">
            <p>Report generato il 5 maggio 2025</p>
        </div>
    </div>
</body>
</html>
"""

# Salva il file HTML
output_file = "/work/tesi_ediluzio/llama_caption_report_embedded.html"
with open(output_file, "w") as f:
    f.write(html_content)

print(f"Report HTML con SVG incorporati salvato in: {output_file}")
