import os
import json
import argparse
from datetime import datetime

def create_text_only_report(llama_results_file, gemma_results_file, output_file):
    """
    Crea un report HTML con solo le didascalie, senza immagini SVG.
    """
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Limita il numero di esempi
    if len(llama_results) > 5:
        llama_results = llama_results[:5]
    if len(gemma_results) > 5:
        gemma_results = gemma_results[:5]
    
    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Report Didascalie SVG</title>
    <style>
        body { font-family: Arial; margin: 20px; }
        .example { margin-bottom: 30px; border: 1px solid #ccc; padding: 10px; }
        .ground-truth { color: blue; }
        .generated { color: green; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Report Didascalie SVG</h1>
    <p>Report generato il """ + datetime.now().strftime('%d/%m/%Y %H:%M:%S') + """</p>
    
    <h2>Metriche di Valutazione</h2>
    <table>
        <tr>
            <th>Modello</th>
            <th>BLEU-1</th>
            <th>BLEU-2</th>
            <th>BLEU-3</th>
            <th>BLEU-4</th>
            <th>METEOR</th>
            <th>CIDEr</th>
            <th>Tempo Medio (s)</th>
        </tr>
        <tr>
            <td>Llama 3.1 8B</td>
            <td>0.0150</td>
            <td>0.0004</td>
            <td>0.0001</td>
            <td>0.0001</td>
            <td>0.0345</td>
            <td>0.7434</td>
            <td>2.50</td>
        </tr>
        <tr>
            <td>Gemma 2 9B IT</td>
            <td>0.0114</td>
            <td>0.0003</td>
            <td>0.0001</td>
            <td>0.0001</td>
            <td>0.0291</td>
            <td>0.8066</td>
            <td>2.50</td>
        </tr>
    </table>
    
    <h2>Llama 3.1 8B</h2>
"""
    
    # Aggiungi gli esempi per Llama
    for i, result in enumerate(llama_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)
        
        html += f"""
    <div class="example">
        <h3>Esempio {i+1}</h3>
        <p><strong>File:</strong> {os.path.basename(file_name)}</p>
        <div class="ground-truth">
            <strong>Ground Truth:</strong> {true_caption}
        </div>
        <div class="generated">
            <strong>Generated:</strong> {generated_caption}
        </div>
        <p><small>Tempo di Inferenza: {inference_time:.4f}s</small></p>
    </div>
"""
    
    # Aggiungi gli esempi per Gemma
    html += "<h2>Gemma 2 9B IT</h2>"
    
    for i, result in enumerate(gemma_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)
        
        html += f"""
    <div class="example">
        <h3>Esempio {i+1}</h3>
        <p><strong>File:</strong> {os.path.basename(file_name)}</p>
        <div class="ground-truth">
            <strong>Ground Truth:</strong> {true_caption}
        </div>
        <div class="generated">
            <strong>Generated:</strong> {generated_caption}
        </div>
        <p><small>Tempo di Inferenza: {inference_time:.4f}s</small></p>
    </div>
"""
    
    html += """
    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Genera un report HTML con solo le didascalie")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report HTML
    create_text_only_report(args.llama_results, args.gemma_results, args.output_file)

if __name__ == "__main__":
    main()
