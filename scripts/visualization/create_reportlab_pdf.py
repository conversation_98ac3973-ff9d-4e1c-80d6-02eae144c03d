import os
import json
import argparse
import tempfile
import base64
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.units import inch, cm
from reportlab.lib.enums import TA_CENTER, TA_LEFT

def create_pdf_report(llama_results_file, gemma_results_file, output_file):
    """
    Crea un report PDF con i risultati dell'inferenza utilizzando ReportLab.
    """
    # Carica i risultati
    llama_results = []
    gemma_results = []

    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)

    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)

    # <PERSON>ita il numero di esempi
    if len(llama_results) > 3:
        llama_results = llama_results[:3]
    if len(gemma_results) > 3:
        gemma_results = gemma_results[:3]

    # Crea il documento PDF
    doc = SimpleDocTemplate(
        output_file,
        pagesize=A4,
        rightMargin=72,
        leftMargin=72,
        topMargin=72,
        bottomMargin=72
    )

    # Stili
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        name='MyTitle',
        parent=styles['Heading1'],
        fontSize=18,
        alignment=TA_CENTER,
        spaceAfter=12
    )
    subtitle_style = ParagraphStyle(
        name='MySubtitle',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=6
    )
    normal_style = ParagraphStyle(
        name='MyNormal',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6
    )
    caption_style = ParagraphStyle(
        name='MyCaption',
        parent=styles['Normal'],
        fontSize=8,
        spaceAfter=6,
        textColor=colors.gray
    )

    # Elementi del documento
    elements = []

    # Titolo
    elements.append(Paragraph("Report Didascalie SVG", title_style))
    elements.append(Paragraph(f"Report generato il {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}", caption_style))
    elements.append(Spacer(1, 0.5*inch))

    # Metriche di valutazione
    elements.append(Paragraph("Metriche di Valutazione", subtitle_style))

    # Tabella delle metriche
    data = [
        ['Modello', 'BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'Tempo (s)'],
        ['Llama 3.1 8B', '0.0150', '0.0004', '0.0001', '0.0001', '0.0345', '0.7434', '2.50'],
        ['Gemma 2 9B IT', '0.0114', '0.0003', '0.0001', '0.0001', '0.0291', '0.8066', '2.50']
    ]

    table = Table(data)
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 10),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))

    elements.append(table)
    elements.append(Spacer(1, 0.5*inch))

    # Llama 3.1 8B
    elements.append(Paragraph("Llama 3.1 8B", subtitle_style))
    elements.append(Spacer(1, 0.25*inch))

    # Aggiungi gli esempi per Llama
    for i, result in enumerate(llama_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)

        elements.append(Paragraph(f"Esempio {i+1}", subtitle_style))
        elements.append(Paragraph(f"<b>File:</b> {os.path.basename(file_name)}", normal_style))
        elements.append(Paragraph(f"<b>Ground Truth:</b> {true_caption}", normal_style))
        elements.append(Paragraph(f"<b>Generated:</b> {generated_caption}", normal_style))
        elements.append(Paragraph(f"<i>Tempo di Inferenza: {inference_time:.4f}s</i>", caption_style))
        elements.append(Spacer(1, 0.25*inch))

    # Gemma 2 9B IT
    elements.append(Paragraph("Gemma 2 9B IT", subtitle_style))
    elements.append(Spacer(1, 0.25*inch))

    # Aggiungi gli esempi per Gemma
    for i, result in enumerate(gemma_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)

        elements.append(Paragraph(f"Esempio {i+1}", subtitle_style))
        elements.append(Paragraph(f"<b>File:</b> {os.path.basename(file_name)}", normal_style))
        elements.append(Paragraph(f"<b>Ground Truth:</b> {true_caption}", normal_style))
        elements.append(Paragraph(f"<b>Generated:</b> {generated_caption}", normal_style))
        elements.append(Paragraph(f"<i>Tempo di Inferenza: {inference_time:.4f}s</i>", caption_style))
        elements.append(Spacer(1, 0.25*inch))

    # Note finali
    elements.append(Spacer(1, 0.5*inch))
    elements.append(Paragraph("Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.", normal_style))
    elements.append(Spacer(1, 0.25*inch))
    elements.append(Paragraph(f"© {datetime.now().year} Università di Modena e Reggio Emilia", caption_style))

    # Costruisci il documento
    doc.build(elements)

    print(f"Report PDF generato con successo: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Genera un report PDF con i risultati dell'inferenza")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File PDF di output")

    args = parser.parse_args()

    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Crea il report PDF
    create_pdf_report(args.llama_results, args.gemma_results, args.output_file)

if __name__ == "__main__":
    main()
