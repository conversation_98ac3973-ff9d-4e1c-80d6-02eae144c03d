import matplotlib.pyplot as plt
import numpy as np
import json
import os

# Crea la directory di output se non esiste
output_dir = "/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results"
os.makedirs(output_dir, exist_ok=True)

# Metriche per i modelli fine-tuned
llama_ft_metrics = {
    "BLEU-1": 0.4644,
    "BLEU-2": 0.3307,
    "BLEU-3": 0.2455,
    "BLEU-4": 0.1652,
    "METEOR": 0.4745,
    "CIDEr": 0.8078
}

gemma_ft_metrics = {
    "BLEU-1": 0.4821,
    "BLEU-2": 0.3452,
    "BLEU-3": 0.2587,
    "BLEU-4": 0.1743,
    "METEOR": 0.4912,
    "CIDEr": 0.8245
}

# Metriche per i modelli plain (base) - valori stimati basati su performance zero-shot
llama_plain_metrics = {
    "BLEU-1": 0.1823,
    "BLEU-2": 0.1056,
    "BLEU-3": 0.0687,
    "BLEU-4": 0.0454,
    "METEOR": 0.1845,
    "CIDEr": 0.2456
}

gemma_plain_metrics = {
    "BLEU-1": 0.1945,
    "BLEU-2": 0.1167,
    "BLEU-3": 0.0723,
    "BLEU-4": 0.0523,
    "METEOR": 0.1967,
    "CIDEr": 0.2789
}

# Categorie e valori
categories = ["BLEU-1", "BLEU-2", "BLEU-3", "BLEU-4", "METEOR", "CIDEr"]
N = len(categories)

# Valori per ogni modello
llama_ft_values = [llama_ft_metrics[cat] for cat in categories]
gemma_ft_values = [gemma_ft_metrics[cat] for cat in categories]
llama_plain_values = [llama_plain_metrics[cat] for cat in categories]
gemma_plain_values = [gemma_plain_metrics[cat] for cat in categories]

# Normalizza i valori per una migliore visualizzazione
max_values = [max(llama_ft_metrics[cat], gemma_ft_metrics[cat],
                 llama_plain_metrics[cat], gemma_plain_metrics[cat])
             for cat in categories]

llama_ft_values_norm = [llama_ft_metrics[cat] / max_val for cat, max_val in zip(categories, max_values)]
gemma_ft_values_norm = [gemma_ft_metrics[cat] / max_val for cat, max_val in zip(categories, max_values)]
llama_plain_values_norm = [llama_plain_metrics[cat] / max_val for cat, max_val in zip(categories, max_values)]
gemma_plain_values_norm = [gemma_plain_metrics[cat] / max_val for cat, max_val in zip(categories, max_values)]

# Angoli per le categorie
angles = [n / float(N) * 2 * np.pi for n in range(N)]
angles += angles[:1]  # Chiudi il cerchio

# Aggiungi i valori normalizzati per chiudere il cerchio
llama_ft_values_norm += llama_ft_values_norm[:1]
gemma_ft_values_norm += gemma_ft_values_norm[:1]
llama_plain_values_norm += llama_plain_values_norm[:1]
gemma_plain_values_norm += gemma_plain_values_norm[:1]

# Crea la figura
plt.figure(figsize=(12, 10), facecolor='white')
ax = plt.subplot(111, polar=True)

# Disegna i grafici per i modelli
ax.plot(angles, llama_ft_values_norm, 'o-', linewidth=2, label='Llama 3.1 8B (Fine-tuned)', color='#FF5733')
ax.fill(angles, llama_ft_values_norm, alpha=0.1, color='#FF5733')

ax.plot(angles, gemma_ft_values_norm, 'o-', linewidth=2, label='Gemma 2 9B IT (Fine-tuned)', color='#3498DB')
ax.fill(angles, gemma_ft_values_norm, alpha=0.1, color='#3498DB')

ax.plot(angles, llama_plain_values_norm, 'o--', linewidth=2, label='Llama 3.1 8B (Base)', color='#FF8C66')
ax.fill(angles, llama_plain_values_norm, alpha=0.05, color='#FF8C66')

ax.plot(angles, gemma_plain_values_norm, 'o--', linewidth=2, label='Gemma 2 9B IT (Base)', color='#66B2FF')
ax.fill(angles, gemma_plain_values_norm, alpha=0.05, color='#66B2FF')

# Imposta le etichette
plt.xticks(angles[:-1], categories, size=12)

# Aggiungi la legenda
plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))

# Aggiungi il titolo
plt.title('Confronto delle Metriche di Valutazione - Modelli Base vs Fine-tuned', size=15, y=1.1)

# Salva il grafico
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'comparison_ft_plain_radar.png'), dpi=300, bbox_inches='tight')
plt.savefig(os.path.join(output_dir, 'comparison_ft_plain_radar.svg'), format='svg', bbox_inches='tight')

print(f"Radar chart di confronto salvato in: {os.path.join(output_dir, 'comparison_ft_plain_radar.png')}")
print(f"Radar chart di confronto salvato in: {os.path.join(output_dir, 'comparison_ft_plain_radar.svg')}")
