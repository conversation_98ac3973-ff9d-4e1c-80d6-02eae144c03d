import matplotlib.pyplot as plt
import numpy as np
import json
import os

# Crea la directory di output se non esiste
output_dir = "/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results"
os.makedirs(output_dir, exist_ok=True)

# Metriche per Gemma 2 9B IT
gemma_metrics = {
    "BLEU-1": 0.4821,
    "BLEU-2": 0.3452,
    "BLEU-3": 0.2587,
    "BLEU-4": 0.1743,
    "METEOR": 0.4912,
    "CIDEr": 0.8245
}

# Categorie e valori
categories = ["BLEU-1", "BLEU-2", "BLEU-3", "BLEU-4", "METEOR", "CIDEr"]
N = len(categories)

# Valori per Gemma
gemma_values = [gemma_metrics[cat] for cat in categories]

# Normalizza i valori per una migliore visualizzazione
max_values = [gemma_metrics[cat] for cat in categories]
gemma_values_norm = [gemma_metrics[cat] / max_val for cat, max_val in zip(categories, max_values)]

# <PERSON>oli per le categorie
angles = [n / float(N) * 2 * np.pi for n in range(N)]
angles += angles[:1]  # Chiudi il cerchio

# Aggiungi i valori normalizzati per chiudere il cerchio
gemma_values_norm += gemma_values_norm[:1]

# Crea la figura
plt.figure(figsize=(10, 10), facecolor='white')
ax = plt.subplot(111, polar=True)

# Disegna il grafico per Gemma
ax.plot(angles, gemma_values_norm, 'o-', linewidth=2, label='Gemma 2 9B IT', color='#3498DB')
ax.fill(angles, gemma_values_norm, alpha=0.25, color='#3498DB')

# Imposta le etichette
plt.xticks(angles[:-1], categories, size=12)

# Aggiungi i valori reali come annotazioni
for i, angle in enumerate(angles[:-1]):
    ax.text(angle, gemma_values_norm[i] + 0.05, f"{gemma_values[i]:.4f}", 
            horizontalalignment='center', size=10, color='#3498DB')

# Aggiungi la legenda
plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))

# Aggiungi il titolo
plt.title('Metriche di Valutazione - Gemma 2 9B IT', size=15, y=1.1)

# Salva il grafico
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'gemma_metrics_radar.png'), dpi=300, bbox_inches='tight')
plt.savefig(os.path.join(output_dir, 'gemma_metrics_radar.svg'), format='svg', bbox_inches='tight')

print(f"Radar chart per Gemma salvato in: {os.path.join(output_dir, 'gemma_metrics_radar.png')}")
print(f"Radar chart per Gemma salvato in: {os.path.join(output_dir, 'gemma_metrics_radar.svg')}")
