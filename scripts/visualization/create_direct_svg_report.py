import os
import json
from datetime import datetime

def create_direct_svg_report(metrics_file, examples_file, output_file):
    """
    Crea un report HTML con SVG diretti, basato sul formato che funziona correttamente.
    """
    # Carica le metriche
    with open(metrics_file, "r") as f:
        metrics = json.load(f)
    
    # Carica gli esempi
    with open(examples_file, "r") as f:
        examples = json.load(f)
    
    # Raggruppa gli esempi per modello
    examples_by_model = {}
    for example in examples:
        model = example.get('model', 'unknown')
        if model not in examples_by_model:
            examples_by_model[model] = []
        examples_by_model[model].append(example)
    
    # Limita il numero di esempi per modello (5 per modello)
    for model in examples_by_model:
        if len(examples_by_model[model]) > 5:
            examples_by_model[model] = examples_by_model[model][:5]
    
    # Mappa i nomi dei modelli per la visualizzazione
    model_display_names = {
        'Llama-3.1-8B-Instruct': 'Llama 3.1 8B (Zero-Shot)',
        'gemma-2-9b-it': 'Gemma 2 9B IT (Zero-Shot)',
    }
    
    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Valutazione dei Modelli di Generazione Didascalie SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            margin: 0 auto;
        }
        .caption {
            margin-bottom: 10px;
        }
        .ground-truth {
            color: #2c5282;
            font-weight: bold;
        }
        .generated {
            color: #744210;
            font-weight: bold;
        }
        .model-section {
            margin-top: 50px;
            margin-bottom: 30px;
        }
        h1, h2 {
            text-align: center;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .metrics-table th, .metrics-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Valutazione dei Modelli di Generazione Didascalie SVG</h1>
    <p style="text-align: center;">Report generato il """ + datetime.now().strftime('%d/%m/%Y %H:%M:%S') + """</p>

    <h2>Metriche di Valutazione</h2>
    <table class="metrics-table">
        <thead>
            <tr>
                <th>Modello</th>
                <th>BLEU-1</th>
                <th>BLEU-2</th>
                <th>BLEU-3</th>
                <th>BLEU-4</th>
                <th>METEOR</th>
                <th>CIDEr</th>
                <th>Inference Time (s)</th>
                <th>Caption Length</th>
            </tr>
        </thead>
        <tbody>
"""
    
    # Aggiungi le righe della tabella delle metriche
    for model, model_metrics in metrics.items():
        display_name = model_display_names.get(model, model)
        html += f"""            <tr>
                <td>{display_name}</td>
                <td>{model_metrics.get('bleu1', 0):.4f}</td>
                <td>{model_metrics.get('bleu2', 0):.4f}</td>
                <td>{model_metrics.get('bleu3', 0):.4f}</td>
                <td>{model_metrics.get('bleu4', 0):.4f}</td>
                <td>{model_metrics.get('meteor', 0):.4f}</td>
                <td>{model_metrics.get('cider', 0):.4f}</td>
                <td>{model_metrics.get('inference_time_mean', 0):.2f}</td>
                <td>{model_metrics.get('caption_length_mean', 0):.2f}</td>
            </tr>
"""
    
    html += """        </tbody>
    </table>
"""
    
    # Aggiungi le sezioni per ogni modello
    for model, examples_list in examples_by_model.items():
        display_name = model_display_names.get(model, model)
        html += f"""
    <div class="model-section">
        <h2>{display_name}</h2>
"""
        
        # Aggiungi gli esempi per questo modello
        for i, example in enumerate(examples_list):
            # Pulisci l'SVG per l'inclusione diretta
            svg_string = example.get("svg", "")
            if svg_string.startswith('<?xml'):
                svg_string = svg_string[svg_string.find('<svg'):]
            
            # Correggi il formato del path se necessario
            if 'd=M' in svg_string:
                svg_string = svg_string.replace('d=M', 'd="M')
                if ' Z"' not in svg_string and ' z"' not in svg_string:
                    svg_string = svg_string.replace('Z"', 'Z"').replace('z"', 'z"')
                    if '"' not in svg_string.split('d=')[1].split()[0]:
                        svg_string = svg_string.replace('Z', 'Z"')
            
            # Correggi gli attributi di stile
            if 'fill:' in svg_string and 'rgb' not in svg_string:
                import re
                # Trova tutti i pattern fill:r,g,b
                fill_patterns = re.findall(r'fill:(\d+),(\d+),(\d+)', svg_string)
                for r, g, b in fill_patterns:
                    svg_string = svg_string.replace(f'fill:{r},{g},{b}', f'fill:rgb({r},{g},{b})')
                
                # Trova tutti i pattern stroke:r,g,b
                stroke_patterns = re.findall(r'stroke:(\d+),(\d+),(\d+)', svg_string)
                for r, g, b in stroke_patterns:
                    svg_string = svg_string.replace(f'stroke:{r},{g},{b}', f'stroke:rgb({r},{g},{b})')
            
            html += f"""
        <div class="example">
            <h3>Esempio {i+1}</h3>
            <div class="svg-container">
                {svg_string}
            </div>
            <div class="caption ground-truth">
                <strong>Ground Truth:</strong> {example.get('true_caption', '')}
            </div>
            <div class="caption generated">
                <strong>Generated Caption:</strong> {example.get('generated_caption', '')}
            </div>
            <p><small>Tempo di Inferenza: {example.get('inference_time', 0):.4f}s</small></p>
        </div>
"""
        
        html += """    </div>
"""
    
    # Chiudi l'HTML
    html += """
    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Genera un report HTML con SVG diretti")
    parser.add_argument("--metrics_file", type=str, required=True, help="File JSON con le metriche")
    parser.add_argument("--examples_file", type=str, required=True, help="File JSON con gli esempi")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")
    
    args = parser.parse_args()
    
    create_direct_svg_report(args.metrics_file, args.examples_file, args.output_file)
