#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import argparse
import re
import datetime
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

def parse_args():
    parser = argparse.ArgumentParser(description="Visualizza le curve di apprendimento dai log di training")
    parser.add_argument("--log_dir", type=str, default="/work/tesi_ediluzio/logs", 
                        help="Directory dei log")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/plots", 
                        help="Directory di output per i grafici")
    parser.add_argument("--job_ids", type=str, nargs="+", help="ID dei job da analizzare")
    parser.add_argument("--compare", action="store_true", help="Confronta le curve di apprendimento")
    parser.add_argument("--smooth", type=int, default=20, help="Fattore di smoothing per la media mobile")
    parser.add_argument("--save", action="store_true", help="Salva i grafici invece di mostrarli")
    return parser.parse_args()

def extract_loss_from_log(log_file):
    """Estrae i valori di loss dal file di log"""
    if not os.path.exists(log_file):
        return None
    
    with open(log_file, "r") as f:
        content = f.read()
    
    # Estrai la loss
    loss_matches = re.findall(r"loss\s*=\s*([\d\.]+)", content)
    if not loss_matches:
        return None
    
    # Converti in float
    loss_values = [float(loss) for loss in loss_matches]
    
    # Estrai gli step
    step_matches = re.findall(r"Step\s+(\d+)/\d+", content)
    if not step_matches or len(step_matches) != len(loss_values):
        # Se non ci sono step o il numero non corrisponde, usa indici sequenziali
        steps = list(range(1, len(loss_values) + 1))
    else:
        steps = [int(step) for step in step_matches]
    
    return {"steps": steps, "loss": loss_values}

def get_job_info(log_dir, job_id):
    """Ottiene informazioni sul job dai file di log"""
    # Cerca i file di log relativi al job
    log_files = [f for f in os.listdir(log_dir) if f.endswith(f"_{job_id}.err")]
    
    if not log_files:
        return None
    
    # Prendi il primo file di log trovato
    log_file = os.path.join(log_dir, log_files[0])
    
    # Estrai il nome del job dal nome del file
    job_name = log_files[0].replace(f"_{job_id}.err", "")
    
    # Estrai i valori di loss
    loss_data = extract_loss_from_log(log_file)
    if not loss_data:
        return None
    
    return {
        "job_id": job_id,
        "job_name": job_name,
        "log_file": log_file,
        "loss_data": loss_data
    }

def plot_learning_curve(job_info, args):
    """Genera un grafico della curva di apprendimento"""
    if not job_info or "loss_data" not in job_info or not job_info["loss_data"]:
        print(f"Nessun dato di loss disponibile per il job {job_info['job_id']}")
        return
    
    loss_data = job_info["loss_data"]
    steps = loss_data["steps"]
    loss_values = loss_data["loss"]
    
    plt.figure(figsize=(12, 6))
    plt.plot(steps, loss_values, marker=".", linestyle="-", markersize=3, alpha=0.5, label="Loss")
    
    # Aggiungi una media mobile per visualizzare meglio il trend
    window_size = min(args.smooth, len(loss_values) // 5) if len(loss_values) > args.smooth else 1
    if window_size > 1:
        moving_avg = np.convolve(loss_values, np.ones(window_size)/window_size, mode="valid")
        plt.plot(steps[window_size-1:], moving_avg, "r-", linewidth=2, label=f"Media mobile ({window_size} step)")
    
    plt.title(f"Curva di apprendimento - {job_info['job_name']} (Job {job_info['job_id']})")
    plt.xlabel("Step")
    plt.ylabel("Loss")
    plt.grid(True)
    plt.legend()
    
    # Aggiungi annotazioni per i valori iniziali e finali
    if len(loss_values) > 0:
        plt.annotate(f"Iniziale: {loss_values[0]:.4f}", 
                    xy=(steps[0], loss_values[0]), 
                    xytext=(steps[0] + 10, loss_values[0] + 0.1),
                    arrowprops=dict(facecolor="black", shrink=0.05, width=1, headwidth=8))
        
        plt.annotate(f"Finale: {loss_values[-1]:.4f}", 
                    xy=(steps[-1], loss_values[-1]), 
                    xytext=(steps[-1] - 10, loss_values[-1] + 0.1),
                    arrowprops=dict(facecolor="black", shrink=0.05, width=1, headwidth=8))
    
    # Calcola e mostra la riduzione percentuale della loss
    if len(loss_values) > 1:
        initial_loss = loss_values[0]
        final_loss = loss_values[-1]
        reduction = (initial_loss - final_loss) / initial_loss * 100
        plt.figtext(0.5, 0.01, f"Riduzione loss: {reduction:.2f}%", ha="center", fontsize=12, bbox={"facecolor":"orange", "alpha":0.5, "pad":5})
    
    if args.save:
        os.makedirs(args.output_dir, exist_ok=True)
        output_file = os.path.join(args.output_dir, f"learning_curve_{job_info['job_id']}.png")
        plt.savefig(output_file, dpi=300, bbox_inches="tight")
        print(f"Grafico salvato in: {output_file}")
    else:
        plt.show()

def plot_comparison(job_infos, args):
    """Genera un grafico di confronto delle curve di apprendimento"""
    if not job_infos:
        print("Nessun dato disponibile per il confronto")
        return
    
    plt.figure(figsize=(14, 8))
    
    for job_info in job_infos:
        if "loss_data" not in job_info or not job_info["loss_data"]:
            continue
        
        loss_data = job_info["loss_data"]
        steps = loss_data["steps"]
        loss_values = loss_data["loss"]
        
        # Usa una versione abbreviata del nome del job per la leggenda
        if "llama31_8b" in job_info["job_name"]:
            display_name = "Llama 3.1 8B"
        elif "gemma2_9b_it" in job_info["job_name"]:
            display_name = "Gemma 2 9B IT"
        else:
            display_name = job_info["job_name"]
            
        if "custom_token" in job_info["job_name"]:
            display_name += " (Custom Token)"
        else:
            display_name += " (No Token)"
        
        # Aggiungi una media mobile per visualizzare meglio il trend
        window_size = min(args.smooth, len(loss_values) // 5) if len(loss_values) > args.smooth else 1
        if window_size > 1:
            moving_avg = np.convolve(loss_values, np.ones(window_size)/window_size, mode="valid")
            plt.plot(steps[window_size-1:], moving_avg, linewidth=2, label=display_name)
        else:
            plt.plot(steps, loss_values, linewidth=2, label=display_name)
    
    plt.title("Confronto delle curve di apprendimento")
    plt.xlabel("Step")
    plt.ylabel("Loss")
    plt.grid(True)
    plt.legend()
    
    if args.save:
        os.makedirs(args.output_dir, exist_ok=True)
        output_file = os.path.join(args.output_dir, "learning_curves_comparison.png")
        plt.savefig(output_file, dpi=300, bbox_inches="tight")
        print(f"Grafico di confronto salvato in: {output_file}")
    else:
        plt.show()

def main():
    args = parse_args()
    
    # Se non sono stati specificati job_ids, ottieni tutti i job dell'utente
    if not args.job_ids:
        # Cerca tutti i file di log con estensione .err
        log_files = [f for f in os.listdir(args.log_dir) if f.endswith(".err")]
        
        # Estrai gli ID dei job dai nomi dei file
        job_ids = []
        for log_file in log_files:
            job_id_match = re.search(r"_(\d+)\.err$", log_file)
            if job_id_match:
                job_ids.append(job_id_match.group(1))
        
        # Rimuovi i duplicati
        job_ids = list(set(job_ids))
    else:
        job_ids = args.job_ids
    
    if not job_ids:
        print("Nessun job trovato.")
        return
    
    # Raccogli informazioni per ogni job
    job_infos = []
    
    for job_id in job_ids:
        job_info = get_job_info(args.log_dir, job_id)
        if job_info:
            job_infos.append(job_info)
    
    if not job_infos:
        print("Nessun dato di loss trovato nei file di log.")
        return
    
    # Genera i grafici
    if args.compare:
        plot_comparison(job_infos, args)
    else:
        for job_info in job_infos:
            plot_learning_curve(job_info, args)

if __name__ == "__main__":
    main()
