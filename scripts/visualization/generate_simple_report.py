#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare un report HTML semplice per la valutazione zero-shot.
"""

import os
import json
from datetime import datetime

def create_metrics_table(metrics):
    """Crea una tabella con le metriche per ogni modello."""
    # Crea la tabella HTML
    html = "<table class='table table-striped table-bordered'>\n"
    html += "<thead>\n<tr>\n"
    
    # Intestazioni della tabella
    headers = ["Model", "BLEU-1", "BLEU-4", "METEOR", "CIDEr", "Inference Time (s)", "Caption Length"]
    for header in headers:
        html += f"<th>{header}</th>\n"
    
    html += "</tr>\n</thead>\n<tbody>\n"
    
    # R<PERSON><PERSON> della tabella
    for model, model_metrics in metrics.items():
        html += "<tr>\n"
        html += f"<td>{model}</td>\n"
        html += f"<td>{model_metrics.get('bleu1', 0):.4f}</td>\n"
        html += f"<td>{model_metrics.get('bleu4', 0):.4f}</td>\n"
        html += f"<td>{model_metrics.get('meteor', 0):.4f}</td>\n"
        html += f"<td>{model_metrics.get('cider', 0):.4f}</td>\n"
        html += f"<td>{model_metrics.get('inference_time_mean', 0):.4f}</td>\n"
        html += f"<td>{model_metrics.get('caption_length_mean', 0):.1f}</td>\n"
        html += "</tr>\n"
    
    html += "</tbody>\n</table>\n"
    
    return html

def create_examples_section(examples, num_examples=5):
    """Crea una sezione con esempi qualitativi per ogni modello."""
    # Raggruppa gli esempi per modello
    examples_by_model = {}
    for example in examples:
        model = example.get('model', 'unknown')
        if model not in examples_by_model:
            examples_by_model[model] = []
        examples_by_model[model].append(example)
    
    # Limita il numero di esempi per modello
    for model in examples_by_model:
        if len(examples_by_model[model]) > num_examples:
            examples_by_model[model] = examples_by_model[model][:num_examples]
    
    # Crea la sezione HTML
    html = ""
    
    for model, model_examples in examples_by_model.items():
        html += f"<h3>Esempi per il Modello {model}</h3>\n"
        html += "<div class='row'>\n"
        
        for example in model_examples:
            html += "<div class='col-md-6 mb-4'>\n"
            html += "<div class='card'>\n"
            html += "<div class='card-body'>\n"
            html += f"<h5 class='card-title'>Esempio {example.get('id', '')}</h5>\n"
            html += "<div class='card-text'>\n"
            html += "<p><strong>Ground Truth:</strong></p>\n"
            html += f"<p>{example.get('true_caption', '')}</p>\n"
            html += "<p><strong>Generated Caption:</strong></p>\n"
            html += f"<p>{example.get('generated_caption', '')}</p>\n"
            html += "</div>\n"
            html += "<div class='card-footer'>\n"
            html += f"<small class='text-muted'>Tempo di Inferenza: {example.get('inference_time', 0):.4f}s</small>\n"
            html += "</div>\n"
            html += "</div>\n"
            html += "</div>\n"
            html += "</div>\n"
        
        html += "</div>\n"
    
    return html

def generate_html_report(
    output_dir,
    metrics_file,
    examples_file,
    title="Valutazione Zero-Shot dei Modelli di Generazione Didascalie SVG",
    num_examples=5
):
    """Genera un report HTML per la valutazione zero-shot."""
    # Crea la directory di output
    os.makedirs(output_dir, exist_ok=True)
    
    # Carica le metriche
    with open(metrics_file, "r") as f:
        metrics = json.load(f)
    
    # Carica gli esempi
    with open(examples_file, "r") as f:
        examples = json.load(f)
    
    # Crea il contenuto HTML
    html = f"""
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{title}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{
                padding-top: 2rem;
                padding-bottom: 2rem;
            }}
            .header {{
                margin-bottom: 2rem;
                text-align: center;
            }}
            .section {{
                margin-bottom: 3rem;
            }}
            .card {{
                height: 100%;
            }}
            .metrics-table {{
                margin-top: 2rem;
                margin-bottom: 2rem;
            }}
            .footer {{
                margin-top: 3rem;
                padding-top: 1rem;
                border-top: 1px solid #dee2e6;
                text-align: center;
                font-size: 0.9rem;
                color: #6c757d;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>{title}</h1>
                <p class="lead">Report generato il {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Sommario</h2>
                <p>
                    Questo report presenta una valutazione zero-shot dei modelli base (non addestrati) per la generazione di didascalie
                    per immagini SVG. Include metriche di valutazione e esempi qualitativi per ogni modello.
                </p>
            </div>
            
            <div class="section">
                <h2>Metriche di Valutazione</h2>
                <div class="metrics-table">
                    {create_metrics_table(metrics)}
                </div>
                <p>
                    <strong>Note sulle Metriche:</strong>
                </p>
                <ul>
                    <li><strong>BLEU-1/4:</strong> Misura la precisione delle n-gramme tra la didascalia generata e quella di riferimento.</li>
                    <li><strong>METEOR:</strong> Misura la corrispondenza tra la didascalia generata e quella di riferimento, considerando sinonimi e variazioni morfologiche.</li>
                    <li><strong>CIDEr:</strong> Misura la similarità tra la didascalia generata e quella di riferimento, pesando i termini in base alla loro frequenza nel corpus.</li>
                    <li><strong>Inference Time:</strong> Tempo medio di inferenza per generare una didascalia.</li>
                    <li><strong>Caption Length:</strong> Lunghezza media delle didascalie generate (in token).</li>
                </ul>
            </div>
            
            <div class="section">
                <h2>Esempi Qualitativi</h2>
                {create_examples_section(examples, num_examples)}
            </div>
            
            <div class="footer">
                <p>Report generato automaticamente per la valutazione zero-shot dei modelli di generazione didascalie SVG.</p>
                <p>© {datetime.now().year} Università di Modena e Reggio Emilia</p>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """
    
    # Salva il report HTML
    output_file = os.path.join(output_dir, "zero_shot_evaluation_report.html")
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")
    
    return output_file

if __name__ == "__main__":
    # Parametri
    output_dir = "/work/tesi_ediluzio/evaluation/reports"
    metrics_file = "/work/tesi_ediluzio/evaluation/zero_shot/zero_shot_metrics.json"
    examples_file = "/work/tesi_ediluzio/evaluation/zero_shot/zero_shot_examples.json"
    title = "Valutazione Zero-Shot dei Modelli di Generazione Didascalie SVG"
    num_examples = 5
    
    # Genera il report HTML
    generate_html_report(
        output_dir=output_dir,
        metrics_file=metrics_file,
        examples_file=examples_file,
        title=title,
        num_examples=num_examples
    )
