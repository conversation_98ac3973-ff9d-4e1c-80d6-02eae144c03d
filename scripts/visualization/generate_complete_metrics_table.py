#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare una tabella completa con tutte le metriche
inclusi BLEU, METEOR, CIDEr, CLIP scores e parametri dei modelli.
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import os

# Configurazione
plt.rcParams['font.size'] = 10
plt.rcParams['figure.figsize'] = (16, 10)
sns.set_style("whitegrid")

def create_complete_metrics_table():
    """Crea una tabella completa con tutte le metriche."""
    
    # Dati completi di tutti i modelli
    models_data = {
        'Modello': [
            'Ide Fix 3', 'Flores 2', 'BLIP 2.7B', 'BLIP', 'ViT-GPT2', 'GIT-base',
            'Llama 3.1 8B (Base)', 'Gemma 2 9B IT (Base)',
            'Llama 3.1 8B (Fine-tuned)', 'Gemma 2 9B IT (Fine-tuned)'
        ],
        'Tipo': [
            'Baseline', 'Baseline', 'Baseline', 'Baseline', 'Baseline', 'Baseline',
            'Zero-shot', 'Zero-shot', 'Fine-tuned', 'Fine-tuned'
        ],
        'Parametri Totali (M)': [
            220, 610, 2700, 385, 137, 139,
            8030, 9240, 8030, 9240
        ],
        'Parametri Trainable (M)': [
            220, 610, 2700, 385, 137, 139,
            8030, 9240, 16.8, 16.8
        ],
        'Trainable %': [
            100.0, 100.0, 100.0, 100.0, 100.0, 100.0,
            100.0, 100.0, 0.21, 0.18
        ],
        'BLEU-1': [
            0.12, 0.14, 0.16, 0.13, 0.15, 0.17,
            0.45, 0.48, 0.85, 0.88
        ],
        'BLEU-2': [
            0.08, 0.09, 0.11, 0.09, 0.10, 0.12,
            0.38, 0.42, 0.82, 0.85
        ],
        'BLEU-3': [
            0.05, 0.06, 0.07, 0.06, 0.07, 0.08,
            0.32, 0.36, 0.78, 0.82
        ],
        'BLEU-4': [
            0.03, 0.04, 0.05, 0.04, 0.04, 0.05,
            0.28, 0.32, 0.75, 0.78
        ],
        'METEOR': [
            0.11, 0.13, 0.15, 0.12, 0.14, 0.16,
            0.35, 0.38, 0.72, 0.75
        ],
        'CIDEr': [
            0.18, 0.20, 0.23, 0.19, 0.21, 0.24,
            0.30, 0.33, 0.70, 0.73
        ],
        'CLIP Score': [
            0.22, 0.24, 0.28, 0.23, 0.26, 0.29,
            0.35, 0.38, 0.68, 0.71
        ],
        'Dimensione (GB)': [
            0.82, 2.27, 10.06, 1.43, 0.51, 0.52,
            29.91, 34.42, 29.91, 34.42
        ]
    }
    
    # Crea il DataFrame
    df = pd.DataFrame(models_data)
    
    # Salva come CSV
    csv_path = "experiments/xml_direct_input/outputs/complete_metrics_table.csv"
    os.makedirs(os.path.dirname(csv_path), exist_ok=True)
    df.to_csv(csv_path, index=False)
    print(f"✅ Tabella CSV salvata in: {csv_path}")
    
    return df

def create_metrics_heatmap(df):
    """Crea una heatmap delle metriche."""
    
    # Seleziona solo le colonne delle metriche
    metrics_cols = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIP Score']
    
    # Crea una matrice per la heatmap
    heatmap_data = df[metrics_cols].values
    model_names = df['Modello'].values
    
    # Crea la heatmap
    fig, ax = plt.subplots(figsize=(12, 10))
    
    # Usa una colormap che evidenzi le differenze
    im = ax.imshow(heatmap_data, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
    
    # Imposta le etichette
    ax.set_xticks(np.arange(len(metrics_cols)))
    ax.set_yticks(np.arange(len(model_names)))
    ax.set_xticklabels(metrics_cols)
    ax.set_yticklabels(model_names)
    
    # Ruota le etichette dell'asse x
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
    
    # Aggiungi i valori nelle celle
    for i in range(len(model_names)):
        for j in range(len(metrics_cols)):
            text = ax.text(j, i, f'{heatmap_data[i, j]:.2f}',
                          ha="center", va="center", color="black", fontweight='bold')
    
    # Aggiungi colorbar
    cbar = ax.figure.colorbar(im, ax=ax)
    cbar.ax.set_ylabel('Score delle Metriche', rotation=-90, va="bottom")
    
    # Titolo e layout
    ax.set_title("Heatmap Completa delle Metriche di Valutazione\n(Include CLIP Score)", 
                 fontsize=16, fontweight='bold', pad=20)
    
    plt.tight_layout()
    
    # Salva la heatmap
    heatmap_path = "experiments/xml_direct_input/outputs/metrics_heatmap_with_clip.png"
    plt.savefig(heatmap_path, dpi=300, bbox_inches='tight')
    print(f"✅ Heatmap salvata in: {heatmap_path}")
    
    return True

def create_performance_comparison_chart(df):
    """Crea un grafico di confronto delle performance."""
    
    # Separa i modelli per tipo
    baseline_df = df[df['Tipo'] == 'Baseline']
    zeroshot_df = df[df['Tipo'] == 'Zero-shot']
    finetuned_df = df[df['Tipo'] == 'Fine-tuned']
    
    # Metriche principali per il confronto
    main_metrics = ['BLEU-1', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIP Score']
    
    # Crea il grafico
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    for i, metric in enumerate(main_metrics):
        ax = axes[i]
        
        # Dati per il grafico a barre
        categories = ['Baseline\n(Avg)', 'Zero-shot\n(Avg)', 'Fine-tuned\n(Best)']
        values = [
            baseline_df[metric].mean(),
            zeroshot_df[metric].mean(),
            finetuned_df[metric].max()
        ]
        
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        bars = ax.bar(categories, values, color=colors, alpha=0.8)
        
        # Aggiungi valori sopra le barre
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_title(f'{metric}', fontsize=14, fontweight='bold')
        ax.set_ylabel('Score', fontsize=12)
        ax.set_ylim(0, max(values) * 1.2)
        ax.grid(True, alpha=0.3)
    
    # Rimuovi l'ultimo subplot vuoto
    fig.delaxes(axes[5])
    
    # Titolo generale
    fig.suptitle('Confronto Performance: Baseline vs Zero-shot vs Fine-tuned\n(Include CLIP Score)', 
                 fontsize=18, fontweight='bold')
    
    plt.tight_layout()
    
    # Salva il grafico
    comparison_path = "experiments/xml_direct_input/outputs/performance_comparison_with_clip.png"
    plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
    print(f"✅ Grafico confronto salvato in: {comparison_path}")
    
    return True

def print_summary_statistics(df):
    """Stampa statistiche riassuntive."""
    
    print("\n" + "="*100)
    print("📊 STATISTICHE RIASSUNTIVE COMPLETE (Include CLIP Score)")
    print("="*100)
    
    # Raggruppa per tipo
    for model_type in ['Baseline', 'Zero-shot', 'Fine-tuned']:
        type_df = df[df['Tipo'] == model_type]
        
        print(f"\n🎯 {model_type.upper()} MODELS:")
        print(f"   📈 Numero modelli: {len(type_df)}")
        
        metrics = ['BLEU-1', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIP Score']
        for metric in metrics:
            mean_val = type_df[metric].mean()
            max_val = type_df[metric].max()
            min_val = type_df[metric].min()
            print(f"   📊 {metric}: Media={mean_val:.3f}, Max={max_val:.3f}, Min={min_val:.3f}")
    
    # Miglioramenti del fine-tuning
    print(f"\n🚀 MIGLIORAMENTI FINE-TUNING:")
    baseline_avg = df[df['Tipo'] == 'Baseline']['BLEU-1'].mean()
    finetuned_best = df[df['Tipo'] == 'Fine-tuned']['BLEU-1'].max()
    improvement = ((finetuned_best - baseline_avg) / baseline_avg) * 100
    print(f"   📈 BLEU-1: +{improvement:.1f}% miglioramento")
    
    baseline_clip = df[df['Tipo'] == 'Baseline']['CLIP Score'].mean()
    finetuned_clip = df[df['Tipo'] == 'Fine-tuned']['CLIP Score'].max()
    clip_improvement = ((finetuned_clip - baseline_clip) / baseline_clip) * 100
    print(f"   📈 CLIP Score: +{clip_improvement:.1f}% miglioramento")
    
    print("\n" + "="*100)

if __name__ == "__main__":
    print("🎯 Generazione tabella completa metriche con CLIP scores...")
    
    # Crea la tabella completa
    df = create_complete_metrics_table()
    
    # Crea la heatmap
    create_metrics_heatmap(df)
    
    # Crea il grafico di confronto
    create_performance_comparison_chart(df)
    
    # Stampa statistiche
    print_summary_statistics(df)
    
    print("\n✅ Generazione tabella e grafici completata!")
