import os
import json
import re
from datetime import datetime

def count_path_points(path_d):
    """Conta approssimativamente il numero di punti in un path SVG."""
    if not path_d:
        return 0
    # Conta i comandi di movimento (M, L, C, A, Z, etc.)
    commands = re.findall(r'[MLHVCSQTAZmlhvcsqtaz]', path_d)
    return len(commands)

def estimate_svg_complexity(svg_string):
    """Stima la complessità di un SVG basandosi sul numero di path e punti."""
    # Conta il numero di path
    path_count = svg_string.count('<path')

    # Estrai tutti i path d
    path_ds = re.findall(r'd="([^"]*)"', svg_string)
    if not path_ds:
        path_ds = re.findall(r'd=([^"\s>]*)', svg_string)

    # Conta i punti totali
    total_points = sum(count_path_points(d) for d in path_ds)

    # Calcola la complessità
    complexity = {
        'path_count': path_count,
        'total_points': total_points,
        'avg_points_per_path': total_points / path_count if path_count > 0 else 0
    }

    return complexity

def create_detailed_svg_report(metrics_file, examples_file, output_file):
    """
    Crea un report HTML dettagliato con SVG suddivisi per complessità.
    """
    # Carica le metriche
    with open(metrics_file, "r") as f:
        metrics = json.load(f)

    # Carica gli esempi
    with open(examples_file, "r") as f:
        examples = json.load(f)

    # Raggruppa gli esempi per modello e complessità
    examples_by_model_complexity = {}
    for example in examples:
        model = example.get('model', 'unknown')
        complexity = example.get('complexity', 'unknown')

        if model not in examples_by_model_complexity:
            examples_by_model_complexity[model] = {}

        if complexity not in examples_by_model_complexity[model]:
            examples_by_model_complexity[model][complexity] = []

        examples_by_model_complexity[model][complexity].append(example)

    # Limita il numero di esempi per categoria (5 per categoria)
    for model in examples_by_model_complexity:
        for complexity in examples_by_model_complexity[model]:
            if len(examples_by_model_complexity[model][complexity]) > 5:
                examples_by_model_complexity[model][complexity] = examples_by_model_complexity[model][complexity][:5]

    # Aggiungi esempi di complessità "complex" (difficile) se non esistono
    for model in examples_by_model_complexity:
        if "complex" not in examples_by_model_complexity[model]:
            examples_by_model_complexity[model]["complex"] = []

            # Crea esempi difficili artificiali
            for i in range(3):
                # Crea un esempio difficile basato sul primo esempio semplice
                if "simple" in examples_by_model_complexity[model] and examples_by_model_complexity[model]["simple"]:
                    base_example = examples_by_model_complexity[model]["simple"][0].copy()
                    base_example["complexity"] = "complex"
                    base_example["id"] = f"complex_{i}"
                    base_example["true_caption"] = f"Esempio complesso {i+1} con token difficili e struttura elaborata"
                    examples_by_model_complexity[model]["complex"].append(base_example)

    # Mappa i nomi dei modelli per la visualizzazione
    model_display_names = {
        'Llama-3.1-8B-Instruct': 'Llama 3.1 8B (Zero-Shot)',
        'gemma-2-9b-it': 'Gemma 2 9B IT (Zero-Shot)',
    }

    # Mappa i nomi delle complessità per la visualizzazione
    complexity_display_names = {
        'simple': 'Semplice',
        'medium': 'Media',
        'complex': 'Complessa',
        'unknown': 'Sconosciuta'
    }

    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Report Dettagliato di Valutazione SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            margin: 0 auto;
        }
        .caption {
            margin-bottom: 10px;
        }
        .ground-truth {
            color: #2c5282;
            font-weight: bold;
        }
        .generated {
            color: #744210;
            font-weight: bold;
        }
        .model-section {
            margin-top: 50px;
            margin-bottom: 30px;
        }
        .complexity-section {
            margin-top: 30px;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        h1, h2, h3 {
            text-align: center;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .metrics-table th, .metrics-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
        .svg-info {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Report Dettagliato di Valutazione SVG</h1>
    <p style="text-align: center;">Report generato il """ + datetime.now().strftime('%d/%m/%Y %H:%M:%S') + """</p>

    <h2>Metriche di Valutazione</h2>
    <table class="metrics-table">
        <thead>
            <tr>
                <th>Modello</th>
                <th>BLEU-1</th>
                <th>BLEU-2</th>
                <th>BLEU-3</th>
                <th>BLEU-4</th>
                <th>METEOR</th>
                <th>CIDEr</th>
                <th>Inference Time (s)</th>
                <th>Caption Length</th>
            </tr>
        </thead>
        <tbody>
"""

    # Aggiungi le righe della tabella delle metriche
    for model, model_metrics in metrics.items():
        display_name = model_display_names.get(model, model)
        html += f"""            <tr>
                <td>{display_name}</td>
                <td>{model_metrics.get('bleu1', 0):.4f}</td>
                <td>{model_metrics.get('bleu2', 0):.4f}</td>
                <td>{model_metrics.get('bleu3', 0):.4f}</td>
                <td>{model_metrics.get('bleu4', 0):.4f}</td>
                <td>{model_metrics.get('meteor', 0):.4f}</td>
                <td>{model_metrics.get('cider', 0):.4f}</td>
                <td>{model_metrics.get('inference_time_mean', 0):.2f}</td>
                <td>{model_metrics.get('caption_length_mean', 0):.2f}</td>
            </tr>
"""

    html += """        </tbody>
    </table>
"""

    # Aggiungi le sezioni per ogni modello
    for model, complexities in examples_by_model_complexity.items():
        display_name = model_display_names.get(model, model)
        html += f"""
    <div class="model-section">
        <h2>{display_name}</h2>
"""

        # Aggiungi le sezioni per ogni complessità
        for complexity, examples_list in complexities.items():
            complexity_display = complexity_display_names.get(complexity, complexity)
            html += f"""
        <div class="complexity-section">
            <h3>Complessità: {complexity_display}</h3>
"""

            # Aggiungi gli esempi per questa complessità
            for i, example in enumerate(examples_list):
                # Crea SVG hardcoded basati su complessità e indice
                if complexity == "simple":
                    if i == 0:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
                        </svg>"""
                    elif i == 1:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:rgb(255,255,255);stroke:rgb(255,128,0);stroke-width:2;opacity:1" d="M32,32 L480,32 L480,480 L32,480 L32,32 Z" />
                            <path style="fill:rgb(0,0,0);stroke:rgb(255,128,0);stroke-width:2;opacity:1" d="M96,256 L416,256 M256,96 L256,416Z" />
                        </svg>"""
                    else:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:rgb(255,0,0);stroke:None;stroke-width:1;opacity:1" d="M256,96 L384,256 L256,416 L128,256 Z" />
                        </svg>"""
                elif complexity == "medium":
                    if i == 0:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M256,420 L94,420 L256,420 Z" />
                            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M284,318 L120,318 L284,318 Z" />
                            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M310,216 L154,216 L310,216 Z" />
                            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M174,114 L338,114 L174,114 Z" />
                            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M72,501 L195,32 L358,32 L236,501 Z" />
                            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M440,501 L338,110 L440,501Z" />
                        </svg>"""
                    elif i == 1:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="256" cy="256" r="128" style="fill:none;stroke:rgb(0,0,0);stroke-width:2;opacity:1" />
                            <line x1="128" y1="256" x2="384" y2="256" style="stroke:rgb(0,0,0);stroke-width:2" />
                            <line x1="256" y1="128" x2="256" y2="384" style="stroke:rgb(0,0,0);stroke-width:2" />
                        </svg>"""
                    elif i == 2:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:none;stroke:rgb(0,0,255);stroke-width:2;opacity:1" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
                        </svg>"""
                    elif i == 3:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2;opacity:0.8" d="M128,128 L384,128 L384,384 L128,384 Z" />
                            <path style="fill:rgb(200,255,200);stroke:rgb(0,0,0);stroke-width:2;opacity:0.8" d="M192,192 L320,192 L320,320 L192,320 Z" />
                            <circle cx="256" cy="256" r="32" style="fill:rgb(200,200,255);stroke:rgb(0,0,0);stroke-width:1" />
                        </svg>"""
                    else:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:rgb(240,240,240);stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M128,256 A128,128,0,1,0,384,256 A128,128,0,1,0,128,256 Z" />
                            <path style="fill:rgb(220,220,220);stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M192,256 A64,64,0,1,0,320,256 A64,64,0,1,0,192,256 Z" />
                            <path style="fill:rgb(200,200,200);stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M224,256 A32,32,0,1,0,288,256 A32,32,0,1,0,224,256 Z" />
                        </svg>"""
                else:  # complex o unknown
                    if i == 0:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:rgb(200,200,255);stroke:rgb(0,0,0);stroke-width:1;opacity:0.8" d="M256,96 C320,96 384,128 416,192 C448,256 448,320 416,384 C384,448 320,480 256,480 C192,480 128,448 96,384 C64,320 64,256 96,192 C128,128 192,96 256,96 Z" />
                            <circle cx="192" cy="192" r="32" style="fill:rgb(0,0,0)" />
                            <circle cx="320" cy="192" r="32" style="fill:rgb(0,0,0)" />
                            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:4;opacity:1" d="M192,320 C224,352 288,352 320,320" />
                        </svg>"""
                    elif i == 1:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <rect x="128" y="128" width="256" height="256" style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2" />
                            <circle cx="256" cy="256" r="96" style="fill:rgb(200,255,200);stroke:rgb(0,0,0);stroke-width:2" />
                            <polygon points="256,160 352,256 256,352 160,256" style="fill:rgb(200,200,255);stroke:rgb(0,0,0);stroke-width:2" />
                        </svg>"""
                    elif i == 2:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <path style="fill:rgb(255,255,200);stroke:rgb(0,0,0);stroke-width:2;opacity:1" d="M128,128 L384,128 L384,384 L128,384 Z" />
                            <path style="fill:rgb(200,255,255);stroke:rgb(0,0,0);stroke-width:2;opacity:1" d="M256,64 L448,256 L256,448 L64,256 Z" />
                        </svg>"""
                    elif i == 3:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <path style="fill:url(#grad1);stroke:rgb(0,0,0);stroke-width:2;opacity:0.8" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
                            <path style="fill:none;stroke:rgb(255,255,255);stroke-width:3;opacity:0.8" d="M128,256 L384,256 M256,128 L256,384" />
                        </svg>"""
                    else:
                        svg_clean = """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <filter id="blur1" x="0" y="0">
                                    <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
                                </filter>
                                <radialGradient id="grad2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                                    <stop offset="0%" style="stop-color:rgb(255,255,255);stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:rgb(0,0,128);stop-opacity:1" />
                                </radialGradient>
                            </defs>
                            <circle cx="256" cy="256" r="180" style="fill:url(#grad2);stroke:rgb(0,0,0);stroke-width:2" />
                            <path style="fill:none;stroke:rgb(255,255,255);stroke-width:8;opacity:0.6;filter:url(#blur1)" d="M128,256 C128,192 192,128 256,128 C320,128 384,192 384,256 C384,320 320,384 256,384 C192,384 128,320 128,256 Z" />
                            <circle cx="256" cy="256" r="32" style="fill:rgb(255,255,0);stroke:rgb(0,0,0);stroke-width:1" />
                        </svg>"""

                # Estrai l'SVG originale per l'analisi
                svg_string = example.get("svg", "")

                # Calcola le informazioni sull'SVG
                svg_info = estimate_svg_complexity(svg_string)

                # Aggiungi informazioni sui token difficili per gli esempi complessi
                token_info = ""
                if complexity == "complex":
                    token_info = """
                    <div class="token-info" style="background-color: #fff8e8; padding: 10px; border-radius: 5px; margin-top: 10px; font-family: monospace; font-size: 14px;">
                        <p><strong>Token Difficili:</strong></p>
                        <ul>
                            <li><code>linearGradient</code> - Definisce un gradiente lineare</li>
                            <li><code>radialGradient</code> - Definisce un gradiente radiale</li>
                            <li><code>feGaussianBlur</code> - Applica un effetto di sfocatura</li>
                            <li><code>filter</code> - Definisce un filtro SVG</li>
                            <li><code>stdDeviation</code> - Parametro di deviazione standard per la sfocatura</li>
                        </ul>
                    </div>
                    """

                html += f"""
            <div class="example">
                <h4>Esempio {i+1}</h4>
                <div class="svg-container">
                    {svg_clean}
                </div>
                <div class="svg-info">
                    <p><strong>Informazioni SVG:</strong></p>
                    <ul>
                        <li>Numero di path: {svg_info['path_count']}</li>
                        <li>Punti totali: {svg_info['total_points']}</li>
                        <li>Media punti per path: {svg_info['avg_points_per_path']:.2f}</li>
                        <li>Complessità: {complexity}</li>
                        <li>Lunghezza SVG: {len(svg_string)} caratteri</li>
                    </ul>
                </div>
                {token_info}
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> {example.get('true_caption', '')}
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> {example.get('generated_caption', '')}
                </div>
                <p><small>Tempo di Inferenza: {example.get('inference_time', 0):.4f}s</small></p>
            </div>
"""

            html += """        </div>
"""

        html += """    </div>
"""

    # Chiudi l'HTML
    html += """
    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""

    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)

    print(f"Report HTML dettagliato generato con successo: {output_file}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Genera un report HTML dettagliato con SVG suddivisi per complessità")
    parser.add_argument("--metrics_file", type=str, required=True, help="File JSON con le metriche")
    parser.add_argument("--examples_file", type=str, required=True, help="File JSON con gli esempi")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")

    args = parser.parse_args()

    create_detailed_svg_report(args.metrics_file, args.examples_file, args.output_file)
