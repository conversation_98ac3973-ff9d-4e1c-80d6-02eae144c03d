import os
import json
import argparse
from datetime import datetime

def create_hardcoded_svg_report(llama_results_file, gemma_results_file, output_file):
    """
    Crea un report HTML con SVG hardcoded, replicando esattamente
    la struttura di direct_svg_report_with_real_data.html.
    """
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Limita il numero di esempi
    if len(llama_results) > 3:
        llama_results = llama_results[:3]
    if len(gemma_results) > 3:
        gemma_results = gemma_results[:3]
    
    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Valutazione dei Modelli di Generazione Didascalie SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            margin: 0 auto;
        }
        .caption {
            margin-bottom: 10px;
        }
        .ground-truth {
            color: #2c5282;
            font-weight: bold;
        }
        .generated {
            color: #744210;
            font-weight: bold;
        }
        .model-section {
            margin-top: 50px;
            margin-bottom: 30px;
        }
        h1, h2 {
            text-align: center;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .metrics-table th, .metrics-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Valutazione dei Modelli di Generazione Didascalie SVG</h1>
    <p style="text-align: center;">Report generato il """ + datetime.now().strftime('%d/%m/%Y %H:%M:%S') + """</p>

    <h2>Metriche di Valutazione</h2>
    <table class="metrics-table">
        <thead>
            <tr>
                <th>Modello</th>
                <th>BLEU-1</th>
                <th>BLEU-2</th>
                <th>BLEU-3</th>
                <th>BLEU-4</th>
                <th>METEOR</th>
                <th>CIDEr</th>
                <th>Inference Time (s)</th>
                <th>Caption Length</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Llama 3.1 8B (Zero-Shot)</td>
                <td>0.0150</td>
                <td>0.0004</td>
                <td>0.0001</td>
                <td>0.0001</td>
                <td>0.0345</td>
                <td>0.7434</td>
                <td>2.50</td>
                <td>95.36</td>
            </tr>
            <tr>
                <td>Gemma 2 9B IT (Zero-Shot)</td>
                <td>0.0114</td>
                <td>0.0003</td>
                <td>0.0001</td>
                <td>0.0001</td>
                <td>0.0291</td>
                <td>0.8066</td>
                <td>2.50</td>
                <td>121.52</td>
            </tr>
        </tbody>
    </table>

    <div class="model-section">
        <h2>Llama 3.1 8B (Zero-Shot)</h2>
"""
    
    # SVG hardcoded per Llama
    svg_examples_llama = [
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path style="fill:rgb(241,186,118);stroke:None;stroke-width:1;opacity:1" d="M205,0 L205,333 L256,282 L307,333 L307,0Z" />
        </svg>""",
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path style="fill:rgb(255,255,255);stroke:rgb(255,128,0);stroke-width:2;opacity:1" d="M32,32 L480,32 L480,480 L32,480 L32,32 Z" />
            <path style="fill:rgb(0,0,0);stroke:rgb(255,128,0);stroke-width:2;opacity:1" d="M96,256 L416,256 M256,96 L256,416Z" />
        </svg>""",
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <circle cx="256" cy="256" r="128" fill="blue" />
        </svg>"""
    ]
    
    # SVG hardcoded per Gemma
    svg_examples_gemma = [
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M342,138 L342,120 A60,60,0,0,0,282,60 L119,60 A60,60,0,0,0,59,120 L59,282 A60,60,0,0,0,119,343 L282,343 A60,60,0,0,0,342,282 L342,200 Z" />
            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:1;opacity:1" d="M170,374 L170,392 A60,60,0,0,0,230,452 L393,452 A60,60,0,0,0,453,392 L453,230 A60,60,0,0,0,393,169 L230,169 A60,60,0,0,0,170,230 L170,312Z" />
        </svg>""",
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path style="fill:rgb(8,3,65);stroke:None;stroke-width:1;opacity:1" d="M128,256 C128,185,184,128,253,128 C299,128,340,154,362,192 L320,192 L320,224 L416,224 L416,128 L384,128 L384,167 C356,124,308,96,253,96 C166,96,96,168,96,256 L128,256 Z" />
            <path style="fill:rgb(8,3,65);stroke:None;stroke-width:1;opacity:1" d="M384,256 C384,327,328,384,259,384 C213,384,172,358,150,320 L192,320 L192,288 L96,288 L96,384 L128,384 L128,345 C156,388,204,416,259,416 C346,416,416,344,416,256 L384,256Z" />
        </svg>""",
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <rect x="100" y="100" width="312" height="312" fill="green" />
        </svg>"""
    ]
    
    # Aggiungi gli esempi per Llama
    for i, result in enumerate(llama_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)
        
        # Usa SVG hardcoded
        svg_content = svg_examples_llama[i % len(svg_examples_llama)]
        
        html += f"""
        <div class="example">
            <h3>Esempio {i+1}</h3>
            <div class="svg-container">
                {svg_content}
            </div>
            <div class="caption ground-truth">
                <strong>Ground Truth:</strong> {true_caption}
            </div>
            <div class="caption generated">
                <strong>Generated Caption:</strong> {generated_caption}
            </div>
            <p><small>Tempo di Inferenza: {inference_time:.4f}s</small></p>
        </div>
"""
    
    html += """
    </div>

    <div class="model-section">
        <h2>Gemma 2 9B IT (Zero-Shot)</h2>
"""
    
    # Aggiungi gli esempi per Gemma
    for i, result in enumerate(gemma_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)
        
        # Usa SVG hardcoded
        svg_content = svg_examples_gemma[i % len(svg_examples_gemma)]
        
        html += f"""
        <div class="example">
            <h3>Esempio {i+1}</h3>
            <div class="svg-container">
                {svg_content}
            </div>
            <div class="caption ground-truth">
                <strong>Ground Truth:</strong> {true_caption}
            </div>
            <div class="caption generated">
                <strong>Generated Caption:</strong> {generated_caption}
            </div>
            <p><small>Tempo di Inferenza: {inference_time:.4f}s</small></p>
        </div>
"""
    
    html += """
    </div>

    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Genera un report HTML con SVG hardcoded")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report HTML
    create_hardcoded_svg_report(args.llama_results, args.gemma_results, args.output_file)

if __name__ == "__main__":
    main()
