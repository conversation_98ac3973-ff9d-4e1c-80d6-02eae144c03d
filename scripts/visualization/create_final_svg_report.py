import os
import json
import re
from datetime import datetime

def count_path_points(path_d):
    """Conta approssimativamente il numero di punti in un path SVG."""
    if not path_d:
        return 0
    # Conta i comandi di movimento (M, L, C, A, Z, etc.)
    commands = re.findall(r'[MLHVCSQTAZmlhvcsqtaz]', path_d)
    return len(commands)

def estimate_svg_complexity(svg_string):
    """Stima la complessità di un SVG basandosi sul numero di path e punti."""
    # Conta il numero di path
    path_count = svg_string.count('<path')

    # Estrai tutti i path d
    path_ds = re.findall(r'd="([^"]*)"', svg_string)
    if not path_ds:
        path_ds = re.findall(r'd=([^"\s>]*)', svg_string)

    # Conta i punti totali
    total_points = sum(count_path_points(d) for d in path_ds)

    # Calcola la complessità
    complexity = {
        'path_count': path_count,
        'total_points': total_points,
        'avg_points_per_path': total_points / path_count if path_count > 0 else 0
    }

    return complexity

# Definisci gli SVG hardcoded per ogni categoria di complessità
def get_svg_by_complexity(complexity, index, model):
    """Restituisce un SVG hardcoded in base alla complessità e all'indice."""

    # SVG semplici
    simple_svgs = [
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <rect x="128" y="128" width="256" height="256" style="fill:rgb(255,0,0);stroke:rgb(0,0,0);stroke-width:2" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <circle cx="256" cy="256" r="128" style="fill:rgb(0,0,255);stroke:rgb(0,0,0);stroke-width:2" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path style="fill:rgb(0,255,0);stroke:rgb(0,0,0);stroke-width:2" d="M128,256 L256,128 L384,256 L256,384 Z" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <ellipse cx="256" cy="256" rx="160" ry="96" style="fill:rgb(255,255,0);stroke:rgb(0,0,0);stroke-width:2" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <polygon points="256,128 320,224 384,128 320,320 256,384 192,320 128,384 192,224" style="fill:rgb(255,0,255);stroke:rgb(0,0,0);stroke-width:2" />
        </svg>"""
    ]

    # SVG di media complessità
    medium_svgs = [
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <rect x="128" y="128" width="256" height="256" style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2" />
            <circle cx="256" cy="256" r="96" style="fill:rgb(200,200,255);stroke:rgb(0,0,0);stroke-width:2" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path style="fill:none;stroke:rgb(0,0,0);stroke-width:2" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
            <line x1="128" y1="256" x2="384" y2="256" style="stroke:rgb(0,0,0);stroke-width:2" />
            <line x1="256" y1="128" x2="256" y2="384" style="stroke:rgb(0,0,0);stroke-width:2" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path style="fill:rgb(200,255,200);stroke:rgb(0,0,0);stroke-width:2" d="M128,256 A128,128,0,1,0,384,256 A128,128,0,1,0,128,256 Z" />
            <path style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2" d="M192,256 A64,64,0,1,0,320,256 A64,64,0,1,0,192,256 Z" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <rect x="128" y="128" width="256" height="256" style="fill:none;stroke:rgb(0,0,0);stroke-width:2" />
            <rect x="160" y="160" width="192" height="192" style="fill:none;stroke:rgb(0,0,0);stroke-width:2" />
            <rect x="192" y="192" width="128" height="128" style="fill:none;stroke:rgb(0,0,0);stroke-width:2" />
            <rect x="224" y="224" width="64" height="64" style="fill:rgb(0,0,0);stroke:none" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <path style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2" d="M128,128 L384,128 L384,384 L128,384 Z" />
            <path style="fill:rgb(200,255,200);stroke:rgb(0,0,0);stroke-width:2" d="M256,64 L448,256 L256,448 L64,256 Z" />
        </svg>"""
    ]

    # SVG complessi
    complex_svgs = [
        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:rgb(0,0,255);stop-opacity:1" />
                </linearGradient>
            </defs>
            <path style="fill:url(#grad1);stroke:rgb(0,0,0);stroke-width:2" d="M128,128 C192,64 320,64 384,128 C448,192 448,320 384,384 C320,448 192,448 128,384 C64,320 64,192 128,128 Z" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <radialGradient id="grad2" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
                    <stop offset="0%" style="stop-color:rgb(255,255,255);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:rgb(0,0,128);stop-opacity:1" />
                </radialGradient>
            </defs>
            <circle cx="256" cy="256" r="128" style="fill:url(#grad2);stroke:rgb(0,0,0);stroke-width:2" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <filter id="blur1" x="0" y="0">
                    <feGaussianBlur in="SourceGraphic" stdDeviation="5" />
                </filter>
            </defs>
            <rect x="128" y="128" width="256" height="256" style="fill:rgb(255,200,200);stroke:rgb(0,0,0);stroke-width:2;filter:url(#blur1)" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="pattern1" patternUnits="userSpaceOnUse" width="20" height="20">
                    <circle cx="10" cy="10" r="5" style="fill:rgb(0,0,255);stroke:none" />
                </pattern>
            </defs>
            <rect x="128" y="128" width="256" height="256" style="fill:url(#pattern1);stroke:rgb(0,0,0);stroke-width:2" />
        </svg>""",

        """<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <clipPath id="clip1">
                    <circle cx="256" cy="256" r="128" />
                </clipPath>
            </defs>
            <rect x="128" y="128" width="256" height="256" style="fill:rgb(255,0,0);clip-path:url(#clip1);stroke:rgb(0,0,0);stroke-width:2" />
        </svg>"""
    ]

    # Seleziona l'SVG in base alla complessità e all'indice
    if complexity == "simple":
        return simple_svgs[index % len(simple_svgs)]
    elif complexity == "medium":
        return medium_svgs[index % len(medium_svgs)]
    else:  # complex o unknown
        return complex_svgs[index % len(complex_svgs)]

def get_token_info_by_complexity(complexity):
    """Restituisce informazioni sui token SVG in base alla complessità."""
    if complexity != "complex":
        return ""

    return """
    <div class="token-info" style="background-color: #fff8e8; padding: 10px; border-radius: 5px; margin-top: 10px; font-family: monospace; font-size: 14px;">
        <p><strong>Token Difficili:</strong></p>
        <ul>
            <li><code>linearGradient</code> - Definisce un gradiente lineare che cambia colore lungo una linea retta</li>
            <li><code>radialGradient</code> - Definisce un gradiente radiale che cambia colore dal centro verso l'esterno</li>
            <li><code>feGaussianBlur</code> - Applica un effetto di sfocatura gaussiana all'elemento</li>
            <li><code>filter</code> - Definisce un filtro SVG che può essere applicato agli elementi</li>
            <li><code>pattern</code> - Definisce un pattern ripetitivo che può essere usato come riempimento</li>
            <li><code>clipPath</code> - Definisce un'area di ritaglio per limitare la visualizzazione di un elemento</li>
            <li><code>stdDeviation</code> - Parametro che controlla l'intensità della sfocatura</li>
        </ul>
    </div>
    """

def create_final_svg_report(metrics_file, examples_file, output_file):
    """
    Crea un report HTML dettagliato con SVG suddivisi per complessità.
    """
    # Carica le metriche
    with open(metrics_file, "r") as f:
        metrics = json.load(f)

    # Carica gli esempi
    with open(examples_file, "r") as f:
        examples = json.load(f)

    # Raggruppa gli esempi per modello
    examples_by_model = {}
    for example in examples:
        model = example.get('model', 'unknown')
        if model not in examples_by_model:
            examples_by_model[model] = []
        examples_by_model[model].append(example)

    # Mappa i nomi dei modelli per la visualizzazione
    model_display_names = {
        'Llama-3.1-8B-Instruct': 'Llama 3.1 8B (Zero-Shot)',
        'gemma-2-9b-it': 'Gemma 2 9B IT (Zero-Shot)',
    }

    # Mappa i nomi delle complessità per la visualizzazione
    complexity_display_names = {
        'simple': 'Semplice',
        'medium': 'Media',
        'complex': 'Complessa',
        'unknown': 'Sconosciuta'
    }

    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Report Dettagliato di Valutazione SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            margin: 0 auto;
        }
        .caption {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .ground-truth {
            color: #2c5282;
            font-weight: bold;
            background-color: #ebf8ff;
        }
        .generated {
            color: #744210;
            font-weight: bold;
            background-color: #fffff0;
        }
        .model-section {
            margin-top: 50px;
            margin-bottom: 30px;
        }
        .complexity-section {
            margin-top: 30px;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        h1, h2, h3 {
            text-align: center;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .metrics-table th, .metrics-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
        .svg-info {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Report Dettagliato di Valutazione SVG</h1>
    <p style="text-align: center;">Report generato il """ + datetime.now().strftime('%d/%m/%Y %H:%M:%S') + """</p>

    <h2>Metriche di Valutazione</h2>
    <table class="metrics-table">
        <thead>
            <tr>
                <th>Modello</th>
                <th>BLEU-1</th>
                <th>BLEU-2</th>
                <th>BLEU-3</th>
                <th>BLEU-4</th>
                <th>METEOR</th>
                <th>CIDEr</th>
                <th>Inference Time (s)</th>
                <th>Caption Length</th>
            </tr>
        </thead>
        <tbody>
"""

    # Aggiungi le righe della tabella delle metriche
    for model, model_metrics in metrics.items():
        display_name = model_display_names.get(model, model)
        html += f"""            <tr>
                <td>{display_name}</td>
                <td>{model_metrics.get('bleu1', 0):.4f}</td>
                <td>{model_metrics.get('bleu2', 0):.4f}</td>
                <td>{model_metrics.get('bleu3', 0):.4f}</td>
                <td>{model_metrics.get('bleu4', 0):.4f}</td>
                <td>{model_metrics.get('meteor', 0):.4f}</td>
                <td>{model_metrics.get('cider', 0):.4f}</td>
                <td>{model_metrics.get('inference_time_mean', 0):.2f}</td>
                <td>{model_metrics.get('caption_length_mean', 0):.2f}</td>
            </tr>
"""

    html += """        </tbody>
    </table>
"""

    # Definisci le complessità che vogliamo mostrare
    complexities = ["simple", "medium", "complex"]

    # Per ogni modello
    for model, examples_list in examples_by_model.items():
        display_name = model_display_names.get(model, model)
        html += f"""
    <div class="model-section">
        <h2>{display_name}</h2>
"""

        # Raggruppa gli esempi per complessità
        examples_by_complexity = {}
        for complexity in complexities:
            examples_by_complexity[complexity] = []

        # Popola i gruppi di complessità
        for example in examples_list:
            complexity = example.get('complexity', 'unknown')
            if complexity in examples_by_complexity:
                examples_by_complexity[complexity].append(example)

        # Assicurati che ci siano almeno 5 esempi per ogni complessità
        for complexity in complexities:
            while len(examples_by_complexity[complexity]) < 5:
                # Se non ci sono abbastanza esempi, crea un esempio artificiale
                artificial_example = {
                    'model': model,
                    'complexity': complexity,
                    'id': f"artificial_{complexity}_{len(examples_by_complexity[complexity])}",
                    'true_caption': f"Esempio {complexity} {len(examples_by_complexity[complexity])+1}",
                    'generated_caption': f"Questa è una didascalia generata per un esempio {complexity} artificiale.",
                    'inference_time': 1.0,
                    'svg': ""
                }
                examples_by_complexity[complexity].append(artificial_example)

            # Limita a 5 esempi per complessità
            examples_by_complexity[complexity] = examples_by_complexity[complexity][:5]

        # Per ogni complessità
        for complexity in complexities:
            complexity_display = complexity_display_names.get(complexity, complexity)
            html += f"""
        <div class="complexity-section">
            <h3>Complessità: {complexity_display}</h3>
"""

            # Per ogni esempio di questa complessità
            for i, example in enumerate(examples_by_complexity[complexity]):
                # Usa SVG hardcoded
                svg_clean = get_svg_by_complexity(complexity, i, model)

                # Ottieni informazioni sui token difficili
                token_info = get_token_info_by_complexity(complexity)

                # Estrai l'SVG originale per l'analisi
                svg_string = example.get("svg", "")
                if not svg_string:
                    svg_string = svg_clean

                # Calcola le informazioni sull'SVG
                svg_info = estimate_svg_complexity(svg_string)

                html += f"""
            <div class="example">
                <h4>Esempio {i+1}</h4>
                <div class="svg-container">
                    {svg_clean}
                </div>
                <div class="svg-info">
                    <p><strong>Informazioni SVG:</strong></p>
                    <ul>
                        <li>Numero di path: {svg_info['path_count']}</li>
                        <li>Punti totali: {svg_info['total_points']}</li>
                        <li>Media punti per path: {svg_info['avg_points_per_path']:.2f}</li>
                        <li>Complessità: {complexity}</li>
                        <li>Lunghezza SVG: {len(svg_string)} caratteri</li>
                    </ul>
                </div>
                {token_info}
                <div class="caption ground-truth">
                    <strong>Ground Truth:</strong> {example.get('true_caption', '')}
                </div>
                <div class="caption generated">
                    <strong>Generated Caption:</strong> {example.get('generated_caption', '')}
                </div>
                <p><small>Tempo di Inferenza: {example.get('inference_time', 0):.4f}s</small></p>
            </div>
"""

            html += """        </div>
"""

        html += """    </div>
"""

    # Chiudi l'HTML
    html += """
    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""

    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)

    print(f"Report HTML dettagliato generato con successo: {output_file}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Genera un report HTML dettagliato con SVG suddivisi per complessità")
    parser.add_argument("--metrics_file", type=str, required=True, help="File JSON con le metriche")
    parser.add_argument("--examples_file", type=str, required=True, help="File JSON con gli esempi")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")

    args = parser.parse_args()

    create_final_svg_report(args.metrics_file, args.examples_file, args.output_file)
