#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare report HTML e grafici radar dopo il completamento dei job di training.
Questo script combina le funzionalità di generate_enhanced_report.py e generate_radar_chart.py.

Uso:
    python generate_training_report.py --model_dirs /path/to/model1 /path/to/model2 --output_dir /path/to/output
"""

import os
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import io
import base64
from datetime import datetime
import plotly.graph_objects as go
import pandas as pd
import re
from typing import Dict, List, Any, Tuple

# Configurazione dei modelli e dei colori
MODEL_COLORS = {
    'llama31_8b_base': '#1f77b4',  # blu
    'gemma2_9b_it_base': '#ff7f0e',  # arancione
    'llama31_8b_finetuned': '#2ca02c',  # verde
    'gemma2_9b_it_finetuned': '#d62728',  # rosso
    'blip': '#9467bd',  # viola
    'vit-gpt2': '#8c564b',  # marrone
    'cogvlm': '#e377c2',  # rosa
}

MODEL_DISPLAY_NAMES = {
    'llama31_8b_base': 'Llama 3.1 8B (Base)',
    'gemma2_9b_it_base': 'Gemma 2 9B IT (Base)',
    'llama31_8b_finetuned': 'Llama 3.1 8B (Fine-tuned)',
    'gemma2_9b_it_finetuned': 'Gemma 2 9B IT (Fine-tuned)',
    'blip': 'BLIP',
    'vit-gpt2': 'ViT-GPT2',
    'cogvlm': 'CogVLM',
}

def load_metrics(model_dirs: List[str]) -> Dict[str, Dict[str, float]]:
    """
    Carica le metriche di valutazione dai file eval_results.json nelle directory dei modelli.

    Args:
        model_dirs: Lista di directory contenenti i risultati dei modelli

    Returns:
        Dictionary con le metriche per ogni modello
    """
    metrics = {}

    for model_dir in model_dirs:
        model_name = os.path.basename(model_dir)
        eval_file = os.path.join(model_dir, 'eval_results.json')

        if os.path.exists(eval_file):
            try:
                with open(eval_file, 'r') as f:
                    model_metrics = json.load(f)
                metrics[model_name] = model_metrics
                print(f"Caricate metriche per il modello {model_name}")
            except Exception as e:
                print(f"Errore nel caricamento delle metriche per {model_name}: {e}")

    return metrics

def load_examples(model_dirs: List[str], num_examples: int = 5) -> List[Dict[str, Any]]:
    """
    Carica esempi di didascalie generate dai file di risultati.

    Args:
        model_dirs: Lista di directory contenenti i risultati dei modelli
        num_examples: Numero di esempi da caricare per ogni modello

    Returns:
        Lista di esempi con didascalie generate e di riferimento
    """
    examples = []

    for model_dir in model_dirs:
        model_name = os.path.basename(model_dir)
        results_file = os.path.join(model_dir, 'test_results.jsonl')

        if os.path.exists(results_file):
            try:
                # Carica i risultati dal file JSONL
                results = []
                with open(results_file, 'r') as f:
                    for line in f:
                        results.append(json.loads(line))

                # Seleziona un sottoinsieme casuale di esempi
                if len(results) > num_examples:
                    import random
                    random.shuffle(results)
                    results = results[:num_examples]

                # Aggiungi gli esempi alla lista
                for result in results:
                    example = {
                        'model': model_name,
                        'svg': result.get('svg', ''),
                        'true_caption': result.get('reference', result.get('true_caption', '')),
                        'generated_caption': result.get('generated', result.get('generated_caption', '')),
                        'inference_time': result.get('inference_time', 0)
                    }
                    examples.append(example)

                print(f"Caricati {len(results)} esempi per il modello {model_name}")
            except Exception as e:
                print(f"Errore nel caricamento degli esempi per {model_name}: {e}")

    return examples

def create_radar_chart(metrics: Dict[str, Dict[str, float]], output_file: str) -> str:
    """
    Crea un grafico radar per confrontare le metriche tra i modelli.

    Args:
        metrics: Dictionary con le metriche per ogni modello
        output_file: Percorso del file di output per il grafico

    Returns:
        HTML del grafico radar
    """
    # Definisci le categorie (assi) del grafico radar
    categories = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr']

    # Crea il grafico radar
    fig = go.Figure()

    # Aggiungi i dati per ogni modello
    for model_name, model_metrics in metrics.items():
        # Mappa i nomi delle metriche
        values = [
            model_metrics.get('bleu1', model_metrics.get('bleu-1', 0)),
            model_metrics.get('bleu2', model_metrics.get('bleu-2', 0)),
            model_metrics.get('bleu3', model_metrics.get('bleu-3', 0)),
            model_metrics.get('bleu4', model_metrics.get('bleu-4', 0)),
            model_metrics.get('meteor', 0),
            model_metrics.get('cider', 0)
        ]

        # Normalizza CIDEr se necessario
        if values[5] > 1:
            values[5] = values[5] / 10.0

        # Determina il colore e il nome visualizzato
        color = MODEL_COLORS.get(model_name, '#777777')
        display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)

        # Aggiungi la traccia al grafico
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name=display_name,
            line=dict(color=color),
            fillcolor=f'rgba({",".join(str(int(c * 255)) for c in plt.colors.to_rgb(color))},0.2)'
        ))

    # Configura il layout
    fig.update_layout(
        title="Confronto delle Metriche di Valutazione - Modelli Base vs Fine-tuned",
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )
        ),
        showlegend=True,
        legend=dict(
            yanchor="bottom",
            y=0,
            xanchor="right",
            x=1
        ),
        margin=dict(l=80, r=80, t=50, b=50)
    )

    # Salva il grafico come immagine
    fig.write_image(output_file)
    print(f"Grafico radar salvato in: {output_file}")

    # Restituisci l'HTML del grafico
    return fig.to_html(full_html=False, include_plotlyjs='cdn')

def create_metrics_table(metrics: Dict[str, Dict[str, float]]) -> str:
    """
    Crea una tabella HTML con le metriche per ogni modello.

    Args:
        metrics: Dictionary con le metriche per ogni modello

    Returns:
        HTML della tabella
    """
    # Crea la tabella HTML
    html = "<table class='table table-striped table-bordered'>\n"
    html += "<thead>\n<tr>\n"

    # Intestazioni della tabella
    headers = ["Model", "BLEU-1", "BLEU-2", "BLEU-3", "BLEU-4", "METEOR", "CIDEr", "Inference Time (s)"]
    for header in headers:
        html += f"<th>{header}</th>\n"

    html += "</tr>\n</thead>\n<tbody>\n"

    # Righe della tabella
    for model_name, model_metrics in metrics.items():
        display_name = MODEL_DISPLAY_NAMES.get(model_name, model_name)

        html += "<tr>\n"
        html += f"<td>{display_name}</td>\n"
        html += f"<td>{model_metrics.get('bleu1', model_metrics.get('bleu-1', 0)):.4f}</td>\n"
        html += f"<td>{model_metrics.get('bleu2', model_metrics.get('bleu-2', 0)):.4f}</td>\n"
        html += f"<td>{model_metrics.get('bleu3', model_metrics.get('bleu-3', 0)):.4f}</td>\n"
        html += f"<td>{model_metrics.get('bleu4', model_metrics.get('bleu-4', 0)):.4f}</td>\n"
        html += f"<td>{model_metrics.get('meteor', 0):.4f}</td>\n"
        html += f"<td>{model_metrics.get('cider', 0):.4f}</td>\n"
        html += f"<td>{model_metrics.get('inference_time_mean', 0):.4f}</td>\n"
        html += "</tr>\n"

    html += "</tbody>\n</table>\n"

    return html

def clean_svg_for_embed(svg_string: str) -> str:
    """
    Pulisce una stringa SVG per l'inclusione diretta in HTML.

    Args:
        svg_string: Stringa SVG da pulire

    Returns:
        Stringa SVG pulita
    """
    try:
        # Rimuovi la dichiarazione XML se presente
        if svg_string.startswith('<?xml'):
            svg_string = svg_string[svg_string.find('<svg'):]

        # Assicurati che l'SVG abbia un viewBox
        if 'viewBox' not in svg_string:
            svg_string = svg_string.replace('<svg', '<svg viewBox="0 0 512 512"')

        # Aggiungi width e height se non presenti
        if 'width' not in svg_string:
            svg_string = svg_string.replace('<svg', '<svg width="100%"')
        if 'height' not in svg_string:
            svg_string = svg_string.replace('<svg', '<svg height="100%"')

        return svg_string
    except Exception as e:
        print(f"Errore nella pulizia SVG: {e}")
        return "<svg width='100%' height='100%' viewBox='0 0 512 512'><text x='50%' y='50%' text-anchor='middle'>Errore SVG</text></svg>"

def create_examples_section(examples: List[Dict[str, Any]], num_examples: int = 5) -> str:
    """
    Crea una sezione HTML con esempi qualitativi per ogni modello.

    Args:
        examples: Lista di esempi con didascalie generate e di riferimento
        num_examples: Numero massimo di esempi da mostrare per ogni modello

    Returns:
        HTML della sezione con gli esempi
    """
    # Raggruppa gli esempi per modello
    examples_by_model = {}
    for example in examples:
        model = example.get('model', 'unknown')
        if model not in examples_by_model:
            examples_by_model[model] = []
        examples_by_model[model].append(example)

    # Limita il numero di esempi per modello
    for model in examples_by_model:
        if len(examples_by_model[model]) > num_examples:
            examples_by_model[model] = examples_by_model[model][:num_examples]

    # Crea la sezione HTML
    html = ""

    for model, model_examples in examples_by_model.items():
        display_name = MODEL_DISPLAY_NAMES.get(model, model)
        html += f"<h2 class='mt-5 mb-4'>Esempi per {display_name}</h2>\n"

        for i, example in enumerate(model_examples):
            # Pulisci l'SVG per l'inclusione diretta
            svg_string = clean_svg_for_embed(example.get("svg", ""))

            html += "<div class='example mb-5 p-4 border rounded'>\n"
            html += f"<h3 class='mb-3'>Esempio {i+1}</h3>\n"

            # Contenitore SVG grande e ben visibile
            html += "<div class='svg-container mb-4' style='width: 300px; height: 300px; border: 1px solid #ccc; margin: 0 auto;'>\n"
            html += svg_string + "\n"
            html += "</div>\n"

            # Ground Truth con stile distintivo
            html += "<div class='caption-container mb-3'>\n"
            html += "<p><strong>Ground Truth:</strong></p>\n"
            html += f"<p class='ground-truth' style='color: #2c5282; font-weight: bold; padding: 10px; background-color: #f8f9fa; border-radius: 5px;'>{example.get('true_caption', '')}</p>\n"
            html += "</div>\n"

            # Generated Caption con stile distintivo
            html += "<div class='caption-container mb-3'>\n"
            html += "<p><strong>Generated Caption:</strong></p>\n"
            html += f"<p class='generated-caption' style='color: #744210; font-weight: bold; padding: 10px; background-color: #f0fff4; border-radius: 5px;'>{example.get('generated_caption', '')}</p>\n"
            html += "</div>\n"

            # Tempo di inferenza
            html += f"<p class='text-muted'><small>Tempo di Inferenza: {example.get('inference_time', 0):.4f}s</small></p>\n"
            html += "</div>\n"

    return html

def generate_html_report(
    metrics: Dict[str, Dict[str, float]],
    examples: List[Dict[str, Any]],
    output_dir: str,
    title: str = "Valutazione dei Modelli di Generazione Didascalie SVG",
    num_examples: int = 5
) -> str:
    """
    Genera un report HTML completo con grafici radar e esempi qualitativi.

    Args:
        metrics: Dictionary con le metriche per ogni modello
        examples: Lista di esempi con didascalie generate e di riferimento
        output_dir: Directory di output per il report
        title: Titolo del report
        num_examples: Numero di esempi da mostrare per ogni modello

    Returns:
        Percorso del file HTML generato
    """
    # Crea la directory di output
    os.makedirs(output_dir, exist_ok=True)

    # Crea il grafico radar
    radar_chart_file = os.path.join(output_dir, "radar_chart.png")
    radar_chart_html = create_radar_chart(metrics, radar_chart_file)

    # Crea la sezione degli esempi
    examples_section = create_examples_section(examples, num_examples)

    # Crea il contenuto HTML
    html = f"""
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{title}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{
                padding-top: 2rem;
                padding-bottom: 2rem;
            }}
            .header {{
                margin-bottom: 2rem;
                text-align: center;
            }}
            .section {{
                margin-bottom: 3rem;
            }}
            .card {{
                height: 100%;
            }}
            .card-img-top {{
                height: 200px;
                object-fit: contain;
                background-color: #f8f9fa;
                padding: 1rem;
            }}
            .metrics-table {{
                margin-top: 2rem;
                margin-bottom: 2rem;
            }}
            .footer {{
                margin-top: 3rem;
                padding-top: 1rem;
                border-top: 1px solid #dee2e6;
                text-align: center;
                font-size: 0.9rem;
                color: #6c757d;
            }}
            .ground-truth {{
                background-color: #f8f9fa;
                padding: 0.5rem;
                border-radius: 0.25rem;
                font-style: italic;
            }}
            .generated-caption {{
                background-color: #e9f7ef;
                padding: 0.5rem;
                border-radius: 0.25rem;
            }}
            .radar-chart {{
                width: 100%;
                height: 500px;
                margin: 2rem 0;
            }}
            .svg-container {{
                width: 300px;
                height: 300px;
                border: 1px solid #ccc;
                margin: 0 auto;
            }}
            .svg-container svg {{
                width: 100%;
                height: 100%;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>{title}</h1>
                <p class="lead">Report generato il {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
            </div>

            <div class="section">
                <h2>Sommario</h2>
                <p>
                    Questo report presenta una valutazione dei modelli per la generazione di didascalie
                    per immagini SVG. Include grafici comparativi delle metriche, tabelle di valutazione
                    e esempi qualitativi per ogni modello.
                </p>
            </div>

            <div class="section">
                <h2>Confronto delle Metriche</h2>

                <div class="radar-chart">
                    {radar_chart_html}
                </div>

                <div class="metrics-table">
                    {create_metrics_table(metrics)}
                </div>

                <p>
                    <strong>Note sulle Metriche:</strong>
                </p>
                <ul>
                    <li><strong>BLEU-1/2/3/4:</strong> Misura la precisione delle n-gramme tra la didascalia generata e quella di riferimento.</li>
                    <li><strong>METEOR:</strong> Misura la corrispondenza tra la didascalia generata e quella di riferimento, considerando sinonimi e variazioni morfologiche.</li>
                    <li><strong>CIDEr:</strong> Misura la similarità tra la didascalia generata e quella di riferimento, pesando i termini in base alla loro frequenza nel corpus.</li>
                    <li><strong>Inference Time:</strong> Tempo medio di inferenza per generare una didascalia.</li>
                </ul>
            </div>

            <div class="section">
                <h2>Esempi Qualitativi</h2>
                {examples_section}
            </div>

            <div class="footer">
                <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
                <p>© {datetime.now().year} Università di Modena e Reggio Emilia</p>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """

    # Salva il report HTML
    output_file = os.path.join(output_dir, "training_evaluation_report.html")
    with open(output_file, "w") as f:
        f.write(html)

    print(f"Report HTML generato con successo: {output_file}")

    return output_file

def main():
    """Funzione principale per generare report e grafici."""
    parser = argparse.ArgumentParser(description="Genera report HTML e grafici radar dopo il completamento dei job di training")
    parser.add_argument("--model_dirs", type=str, nargs="+", required=True, help="Directory contenenti i risultati dei modelli")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/reports/html", help="Directory di output per il report")
    parser.add_argument("--title", type=str, default="Valutazione dei Modelli di Generazione Didascalie SVG", help="Titolo del report")
    parser.add_argument("--num_examples", type=int, default=5, help="Numero di esempi qualitativi per modello")

    args = parser.parse_args()

    # Carica le metriche e gli esempi
    metrics = load_metrics(args.model_dirs)
    examples = load_examples(args.model_dirs, args.num_examples)

    # Genera il report HTML
    output_file = generate_html_report(
        metrics=metrics,
        examples=examples,
        output_dir=args.output_dir,
        title=args.title,
        num_examples=args.num_examples
    )

    # Genera anche un grafico radar separato
    radar_chart_file = os.path.join(args.output_dir, "radar_chart.png")
    create_radar_chart(metrics, radar_chart_file)

    print(f"Generazione completata. Report HTML: {output_file}, Grafico radar: {radar_chart_file}")

if __name__ == "__main__":
    main()
