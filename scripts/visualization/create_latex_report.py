import os
import json
import argparse
import tempfile
import subprocess
from datetime import datetime

def save_svg_as_pdf(svg_content, output_path):
    """
    Salva un contenuto SVG come file PDF utilizzando Inkscape.
    """
    # Crea un file temporaneo per l'SVG
    with tempfile.NamedTemporaryFile(suffix='.svg', delete=False) as temp_svg:
        temp_svg.write(svg_content.encode('utf-8'))
        temp_svg_path = temp_svg.name
    
    # Converti SVG in PDF usando pdf2svg (inverso)
    try:
        # Prova con Inkscape
        subprocess.run([
            'inkscape',
            '--export-filename=' + output_path,
            '--export-type=pdf',
            temp_svg_path
        ], check=True)
        print(f"Convertito SVG in PDF: {output_path}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        try:
            # Prova con rsvg-convert
            subprocess.run([
                'rsvg-convert',
                '-f', 'pdf',
                '-o', output_path,
                temp_svg_path
            ], check=True)
            print(f"Convertito SVG in PDF con rsvg-convert: {output_path}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("Non è stato possibile convertire SVG in PDF. Assicurati che Inkscape o rsvg-convert siano installati.")
            # Crea un PDF vuoto
            with open(output_path, 'w') as f:
                f.write("%PDF-1.4\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[3 0 R]/Count 1>>\nendobj\n3 0 obj\n<</Type/Page/MediaBox[0 0 3 3]/Parent 2 0 R/Resources<<>>>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000052 00000 n \n0000000101 00000 n \n\ntrailer\n<</Size 4/Root 1 0 R>>\nstartxref\n178\n%%EOF\n")
    
    # Rimuovi il file temporaneo
    os.unlink(temp_svg_path)

def create_latex_report(llama_results_file, gemma_results_file, output_dir):
    """
    Crea un report LaTeX con i risultati dell'inferenza.
    """
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Limita il numero di esempi
    if len(llama_results) > 3:
        llama_results = llama_results[:3]
    if len(gemma_results) > 3:
        gemma_results = gemma_results[:3]
    
    # Crea la directory per le immagini
    images_dir = os.path.join(output_dir, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # Salva gli SVG come PDF
    for i, result in enumerate(llama_results):
        svg_content = result.get("svg", "")
        if svg_content:
            pdf_path = os.path.join(images_dir, f"llama_{i+1}.pdf")
            save_svg_as_pdf(svg_content, pdf_path)
            result["pdf_path"] = pdf_path
    
    for i, result in enumerate(gemma_results):
        svg_content = result.get("svg", "")
        if svg_content:
            pdf_path = os.path.join(images_dir, f"gemma_{i+1}.pdf")
            save_svg_as_pdf(svg_content, pdf_path)
            result["pdf_path"] = pdf_path
    
    # Crea il file LaTeX
    latex_content = r"""\documentclass[a4paper,12pt]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{longtable}
\usepackage{geometry}
\usepackage{xcolor}
\usepackage{hyperref}

\geometry{a4paper,margin=2.5cm}

\title{Report Didascalie SVG}
\author{Università di Modena e Reggio Emilia}
\date{\today}

\begin{document}

\maketitle

\section{Metriche di Valutazione}

\begin{table}[h]
\centering
\begin{tabular}{lcccccccc}
\toprule
Modello & BLEU-1 & BLEU-2 & BLEU-3 & BLEU-4 & METEOR & CIDEr & Tempo (s) & Lunghezza \\
\midrule
Llama 3.1 8B & 0.0150 & 0.0004 & 0.0001 & 0.0001 & 0.0345 & 0.7434 & 2.50 & 95.36 \\
Gemma 2 9B IT & 0.0114 & 0.0003 & 0.0001 & 0.0001 & 0.0291 & 0.8066 & 2.50 & 121.52 \\
\bottomrule
\end{tabular}
\caption{Metriche di valutazione per i modelli di generazione didascalie SVG.}
\end{table}

\section{Llama 3.1 8B}

"""
    
    # Aggiungi gli esempi per Llama
    for i, result in enumerate(llama_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)
        pdf_path = result.get("pdf_path", "")
        
        latex_content += f"""
\\subsection{{Esempio {i+1}}}

\\textbf{{File:}} {os.path.basename(file_name)}

\\textbf{{Ground Truth:}} {true_caption}

\\begin{{center}}
"""
        
        if pdf_path and os.path.exists(pdf_path):
            rel_path = os.path.relpath(pdf_path, output_dir)
            latex_content += f"\\includegraphics[width=0.5\\textwidth]{{{rel_path}}}\n"
        
        latex_content += f"""\\end{{center}}

\\textbf{{Generated:}} {generated_caption.replace('_', '\\_').replace('%', '\\%').replace('#', '\\#').replace('&', '\\&')}

\\textit{{Tempo di Inferenza: {inference_time:.4f}s}}

\\hrulefill

"""
    
    # Aggiungi gli esempi per Gemma
    latex_content += r"\section{Gemma 2 9B IT}" + "\n"
    
    for i, result in enumerate(gemma_results):
        file_name = result.get("file", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        inference_time = result.get("inference_time", 0)
        pdf_path = result.get("pdf_path", "")
        
        latex_content += f"""
\\subsection{{Esempio {i+1}}}

\\textbf{{File:}} {os.path.basename(file_name)}

\\textbf{{Ground Truth:}} {true_caption}

\\begin{{center}}
"""
        
        if pdf_path and os.path.exists(pdf_path):
            rel_path = os.path.relpath(pdf_path, output_dir)
            latex_content += f"\\includegraphics[width=0.5\\textwidth]{{{rel_path}}}\n"
        
        latex_content += f"""\\end{{center}}

\\textbf{{Generated:}} {generated_caption.replace('_', '\\_').replace('%', '\\%').replace('#', '\\#').replace('&', '\\&')}

\\textit{{Tempo di Inferenza: {inference_time:.4f}s}}

\\hrulefill

"""
    
    latex_content += r"""
\section*{Note}

Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.

\vspace{1cm}

\begin{center}
\copyright\ """ + str(datetime.now().year) + r""" Università di Modena e Reggio Emilia
\end{center}

\end{document}
"""
    
    # Salva il file LaTeX
    latex_file = os.path.join(output_dir, "inference_report.tex")
    with open(latex_file, "w") as f:
        f.write(latex_content)
    
    print(f"File LaTeX generato con successo: {latex_file}")
    
    # Compila il file LaTeX
    try:
        subprocess.run([
            'pdflatex',
            '-interaction=nonstopmode',
            '-output-directory=' + output_dir,
            latex_file
        ], check=True)
        print(f"PDF generato con successo: {os.path.join(output_dir, 'inference_report.pdf')}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Non è stato possibile compilare il file LaTeX. Assicurati che pdflatex sia installato.")

def main():
    parser = argparse.ArgumentParser(description="Genera un report LaTeX con i risultati dell'inferenza")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory di output per il report")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Crea il report LaTeX
    create_latex_report(args.llama_results, args.gemma_results, args.output_dir)

if __name__ == "__main__":
    main()
