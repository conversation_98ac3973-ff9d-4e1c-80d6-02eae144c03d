#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare un report HTML avanzato con logo, grafici radar e esempi qualitativi.
"""

import os
import json
import base64
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import io
from datetime import datetime
import plotly.graph_objects as go

def load_logo(logo_path):
    """Carica il logo e lo converte in base64 per l'inclusione nel HTML."""
    try:
        with open(logo_path, "rb") as f:
            logo_data = f.read()
        logo_base64 = base64.b64encode(logo_data).decode('utf-8')
        logo_ext = os.path.splitext(logo_path)[1][1:]  # Estensione senza il punto
        return f"data:image/{logo_ext};base64,{logo_base64}"
    except Exception as e:
        print(f"Errore nel caricamento del logo: {e}")
        return None

def clean_svg_for_embed(svg_string):
    """Pulisce una stringa SVG per l'inclusione diretta in HTML."""
    try:
        # Rimuovi la dichiarazione XML se presente
        if svg_string.startswith('<?xml'):
            svg_string = svg_string[svg_string.find('<svg'):]

        # Assicurati che l'SVG abbia un viewBox
        if 'viewBox' not in svg_string:
            svg_string = svg_string.replace('<svg', '<svg viewBox="0 0 512 512"')

        # Aggiungi width e height se non presenti
        if 'width' not in svg_string:
            svg_string = svg_string.replace('<svg', '<svg width="100%"')
        if 'height' not in svg_string:
            svg_string = svg_string.replace('<svg', '<svg height="100%"')

        return svg_string
    except Exception as e:
        print(f"Errore nella pulizia SVG: {e}")
        return "<svg width='100%' height='100%' viewBox='0 0 512 512'><text x='50%' y='50%' text-anchor='middle'>Errore SVG</text></svg>"

def create_radar_chart(metrics_dict, output_dir):
    """Crea un grafico radar per confrontare le metriche tra i modelli."""
    # Definisci le categorie (assi) del grafico radar
    categories = ['BLEU_1', 'BLEU_2', 'BLEU_3', 'BLEU_4', 'METEOR', 'CIDEr', 'CLIP_SCORE']

    # Normalizza i valori di CIDEr e CLIP_SCORE per adattarli alla scala
    for model, metrics in metrics_dict.items():
        if 'cider' in metrics:
            metrics['cider'] = metrics['cider'] / 10.0  # Normalizza CIDEr
        if 'clip_score' in metrics:
            metrics['clip_score'] = metrics['clip_score'] / 100.0  # Normalizza CLIP Score

    # Crea una figura con sfondo trasparente
    fig = plt.figure(figsize=(10, 10), facecolor='none')
    ax = fig.add_subplot(111, polar=True)

    # Colori per i diversi modelli
    colors = {
        'llama31_8b_zero_shot': '#1f77b4',  # blu
        'gemma2_9b_it_zero_shot': '#ff7f0e',  # arancione
        'llama31_8b_finetuned': '#2ca02c',  # verde
        'gemma2_9b_it_finetuned': '#d62728',  # rosso
        'llama31_8b_custom_tokenizer': '#9467bd',  # viola
        'gemma2_9b_it_custom_tokenizer': '#8c564b'  # marrone
    }

    # Mappa i nomi dei modelli per la legenda
    model_names = {
        'Llama-3.1-8B-Instruct': 'llama31_8b_zero_shot',
        'gemma-2-9b-it': 'gemma2_9b_it_zero_shot',
        # Aggiungi qui i nomi dei modelli fine-tuned quando saranno disponibili
    }

    # Numero di categorie
    N = len(categories)

    # Angoli per ogni asse
    angles = [n / float(N) * 2 * np.pi for n in range(N)]
    angles += angles[:1]  # Chiudi il cerchio

    # Imposta gli assi
    ax.set_theta_offset(np.pi / 2)  # Sposta l'inizio in alto
    ax.set_theta_direction(-1)  # Senso orario

    # Imposta le etichette degli assi
    plt.xticks(angles[:-1], categories, size=12)

    # Imposta i limiti del grafico
    ax.set_ylim(0, 1)

    # Disegna i cerchi concentrici per i livelli
    ax.set_rticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_rlabel_position(0)

    # Aggiungi i dati per ogni modello
    for model_original, model_key in model_names.items():
        if model_original in metrics_dict:
            metrics = metrics_dict[model_original]
            values = [
                metrics.get('bleu1', 0),
                metrics.get('bleu2', 0),
                metrics.get('bleu3', 0),
                metrics.get('bleu4', 0),
                metrics.get('meteor', 0),
                metrics.get('cider', 0),
                metrics.get('clip_score', 0) if 'clip_score' in metrics else 0
            ]
            values += values[:1]  # Chiudi il poligono

            # Disegna il poligono
            ax.plot(angles, values, linewidth=2, linestyle='solid', label=model_key, color=colors.get(model_key, 'gray'))
            ax.fill(angles, values, alpha=0.1, color=colors.get(model_key, 'gray'))

    # Aggiungi la legenda
    plt.legend(loc='lower right', bbox_to_anchor=(0.1, 0.1))

    # Salva il grafico come immagine
    chart_path = os.path.join(output_dir, 'radar_chart.png')
    plt.savefig(chart_path, format='png', bbox_inches='tight', transparent=True, dpi=300)
    plt.close()

    # Converti l'immagine in base64 per l'inclusione nel HTML
    with open(chart_path, "rb") as f:
        chart_data = f.read()
    chart_base64 = base64.b64encode(chart_data).decode('utf-8')

    return f"data:image/png;base64,{chart_base64}"

def create_plotly_radar_chart(metrics_dict):
    """Crea un grafico radar interattivo con Plotly per confrontare le metriche tra i modelli."""
    # Definisci le categorie (assi) del grafico radar
    categories = ['BLEU_1', 'BLEU_2', 'BLEU_3', 'BLEU_4', 'METEOR', 'CIDEr', 'CLIP_SCORE']

    # Normalizza i valori di CIDEr e CLIP_SCORE per adattarli alla scala
    metrics_dict_normalized = {}
    for model, metrics in metrics_dict.items():
        metrics_dict_normalized[model] = metrics.copy()
        if 'cider' in metrics:
            metrics_dict_normalized[model]['cider'] = metrics['cider'] / 10.0  # Normalizza CIDEr
        if 'clip_score' in metrics:
            metrics_dict_normalized[model]['clip_score'] = metrics['clip_score'] / 100.0  # Normalizza CLIP Score

    # Colori per i diversi modelli
    colors = {
        'Llama-3.1-8B-Instruct': '#1f77b4',  # blu
        'gemma-2-9b-it': '#ff7f0e',  # arancione
        'llama31_8b_finetuned': '#2ca02c',  # verde
        'gemma2_9b_it_finetuned': '#d62728',  # rosso
        'llama31_8b_custom_tokenizer': '#9467bd',  # viola
        'gemma2_9b_it_custom_tokenizer': '#8c564b'  # marrone
    }

    # Mappa i nomi dei modelli per la legenda
    model_display_names = {
        'Llama-3.1-8B-Instruct': 'Llama 3.1 8B (Zero-Shot)',
        'gemma-2-9b-it': 'Gemma 2 9B IT (Zero-Shot)',
        # Aggiungi qui i nomi dei modelli fine-tuned quando saranno disponibili
    }

    # Crea il grafico radar
    fig = go.Figure()

    # Aggiungi i dati per ogni modello
    for model, metrics in metrics_dict_normalized.items():
        values = [
            metrics.get('bleu1', 0),
            metrics.get('bleu2', 0),
            metrics.get('bleu3', 0),
            metrics.get('bleu4', 0),
            metrics.get('meteor', 0),
            metrics.get('cider', 0),
            metrics.get('clip_score', 0) if 'clip_score' in metrics else 0
        ]

        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name=model_display_names.get(model, model),
            line=dict(color=colors.get(model, 'gray')),
            fillcolor='rgba(31, 119, 180, 0.2)' if model == 'Llama-3.1-8B-Instruct' else 'rgba(255, 127, 14, 0.2)'
        ))

    # Configura il layout
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )
        ),
        showlegend=True,
        legend=dict(
            yanchor="bottom",
            y=0,
            xanchor="right",
            x=1
        ),
        margin=dict(l=80, r=80, t=20, b=20),
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)'
    )

    # Converti il grafico in HTML
    return fig.to_html(full_html=False, include_plotlyjs='cdn')

def create_metrics_table(metrics_dict):
    """Crea una tabella con le metriche per ogni modello."""
    # Crea la tabella HTML
    html = "<table class='table table-striped table-bordered'>\n"
    html += "<thead>\n<tr>\n"

    # Intestazioni della tabella
    headers = ["Model", "BLEU-1", "BLEU-2", "BLEU-3", "BLEU-4", "METEOR", "CIDEr", "CLIP Score", "Inference Time (s)", "Caption Length"]
    for header in headers:
        html += f"<th>{header}</th>\n"

    html += "</tr>\n</thead>\n<tbody>\n"

    # Mappa i nomi dei modelli per la visualizzazione
    model_display_names = {
        'Llama-3.1-8B-Instruct': 'Llama 3.1 8B (Zero-Shot)',
        'gemma-2-9b-it': 'Gemma 2 9B IT (Zero-Shot)',
        # Aggiungi qui i nomi dei modelli fine-tuned quando saranno disponibili
    }

    # Righe della tabella
    for model, metrics in metrics_dict.items():
        html += "<tr>\n"
        html += f"<td>{model_display_names.get(model, model)}</td>\n"
        html += f"<td>{metrics.get('bleu1', 0):.4f}</td>\n"
        html += f"<td>{metrics.get('bleu2', 0):.4f}</td>\n"
        html += f"<td>{metrics.get('bleu3', 0):.4f}</td>\n"
        html += f"<td>{metrics.get('bleu4', 0):.4f}</td>\n"
        html += f"<td>{metrics.get('meteor', 0):.4f}</td>\n"
        html += f"<td>{metrics.get('cider', 0):.4f}</td>\n"
        html += f"<td>{metrics.get('clip_score', 0):.4f}</td>\n"
        html += f"<td>{metrics.get('inference_time_mean', 0):.4f}</td>\n"
        html += f"<td>{metrics.get('caption_length_mean', 0):.1f}</td>\n"
        html += "</tr>\n"

    html += "</tbody>\n</table>\n"

    return html

def create_examples_section(examples, num_examples=5):
    """Crea una sezione con esempi qualitativi per ogni modello, includendo l'SVG direttamente."""
    # Raggruppa gli esempi per modello
    examples_by_model = {}
    for example in examples:
        model = example.get('model', 'unknown')
        if model not in examples_by_model:
            examples_by_model[model] = []
        examples_by_model[model].append(example)

    # Limita il numero di esempi per modello
    for model in examples_by_model:
        if len(examples_by_model[model]) > num_examples:
            examples_by_model[model] = examples_by_model[model][:num_examples]

    # Mappa i nomi dei modelli per la visualizzazione
    model_display_names = {
        'Llama-3.1-8B-Instruct': 'Llama 3.1 8B (Zero-Shot)',
        'gemma-2-9b-it': 'Gemma 2 9B IT (Zero-Shot)',
        # Aggiungi qui i nomi dei modelli fine-tuned quando saranno disponibili
    }

    # Crea la sezione HTML
    html = ""

    for model, model_examples in examples_by_model.items():
        html += f"<h2 class='mt-5 mb-4'>Esempi per {model_display_names.get(model, model)}</h2>\n"

        for i, example in enumerate(model_examples):
            # Pulisci l'SVG per l'inclusione diretta
            svg_string = clean_svg_for_embed(example.get("svg", ""))

            html += "<div class='example mb-5 p-4 border rounded'>\n"
            html += f"<h3 class='mb-3'>Esempio {i+1}</h3>\n"

            # Contenitore SVG grande e ben visibile
            html += "<div class='svg-container mb-4' style='width: 300px; height: 300px; border: 1px solid #ccc; margin: 0 auto;'>\n"
            html += svg_string + "\n"
            html += "</div>\n"

            # Ground Truth con stile distintivo
            html += "<div class='caption-container mb-3'>\n"
            html += "<p><strong>Ground Truth:</strong></p>\n"
            html += f"<p class='ground-truth' style='color: #2c5282; font-weight: bold; padding: 10px; background-color: #f8f9fa; border-radius: 5px;'>{example.get('true_caption', '')}</p>\n"
            html += "</div>\n"

            # Generated Caption con stile distintivo
            html += "<div class='caption-container mb-3'>\n"
            html += "<p><strong>Generated Caption:</strong></p>\n"
            html += f"<p class='generated-caption' style='color: #744210; font-weight: bold; padding: 10px; background-color: #f0fff4; border-radius: 5px;'>{example.get('generated_caption', '')}</p>\n"
            html += "</div>\n"

            # Tempo di inferenza
            html += f"<p class='text-muted'><small>Tempo di Inferenza: {example.get('inference_time', 0):.4f}s</small></p>\n"
            html += "</div>\n"

    return html

def generate_html_report(
    output_dir,
    metrics_file,
    examples_file,
    title="Valutazione dei Modelli di Generazione Didascalie SVG",
    num_examples=5
):
    """Genera un report HTML avanzato con grafici radar e esempi qualitativi con SVG diretti."""
    # Crea la directory di output
    os.makedirs(output_dir, exist_ok=True)

    # Carica le metriche
    with open(metrics_file, "r") as f:
        metrics = json.load(f)

    # Carica gli esempi
    with open(examples_file, "r") as f:
        examples = json.load(f)

    # Crea il grafico radar
    radar_chart_html = create_plotly_radar_chart(metrics)

    # Crea la sezione degli esempi
    examples_section = create_examples_section(examples, num_examples)

    # Crea il contenuto HTML
    html = f"""
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{title}</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body {{
                padding-top: 2rem;
                padding-bottom: 2rem;
            }}
            .header {{
                margin-bottom: 2rem;
                text-align: center;
            }}
            .section {{
                margin-bottom: 3rem;
            }}
            .card {{
                height: 100%;
            }}
            .card-img-top {{
                height: 200px;
                object-fit: contain;
                background-color: #f8f9fa;
                padding: 1rem;
            }}
            .metrics-table {{
                margin-top: 2rem;
                margin-bottom: 2rem;
            }}
            .footer {{
                margin-top: 3rem;
                padding-top: 1rem;
                border-top: 1px solid #dee2e6;
                text-align: center;
                font-size: 0.9rem;
                color: #6c757d;
            }}
            .ground-truth {{
                background-color: #f8f9fa;
                padding: 0.5rem;
                border-radius: 0.25rem;
                font-style: italic;
            }}
            .generated-caption {{
                background-color: #e9f7ef;
                padding: 0.5rem;
                border-radius: 0.25rem;
            }}
            .radar-chart {{
                width: 100%;
                height: 500px;
                margin: 2rem 0;
            }}
            .svg-container {{
                width: 300px;
                height: 300px;
                border: 1px solid #ccc;
                margin: 0 auto;
            }}
            .svg-container svg {{
                width: 100%;
                height: 100%;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>{title}</h1>
                <p class="lead">Report generato il {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
            </div>

            <div class="section">
                <h2>Sommario</h2>
                <p>
                    Questo report presenta una valutazione dei modelli per la generazione di didascalie
                    per immagini SVG. Include grafici comparativi delle metriche, tabelle di valutazione
                    e esempi qualitativi per ogni modello.
                </p>
            </div>

            <div class="section">
                <h2>Confronto delle Metriche</h2>

                <div class="radar-chart">
                    {radar_chart_html}
                </div>

                <div class="metrics-table">
                    {create_metrics_table(metrics)}
                </div>

                <p>
                    <strong>Note sulle Metriche:</strong>
                </p>
                <ul>
                    <li><strong>BLEU-1/2/3/4:</strong> Misura la precisione delle n-gramme tra la didascalia generata e quella di riferimento.</li>
                    <li><strong>METEOR:</strong> Misura la corrispondenza tra la didascalia generata e quella di riferimento, considerando sinonimi e variazioni morfologiche.</li>
                    <li><strong>CIDEr:</strong> Misura la similarità tra la didascalia generata e quella di riferimento, pesando i termini in base alla loro frequenza nel corpus.</li>
                    <li><strong>CLIP Score:</strong> Misura la similarità semantica tra l'immagine SVG e la didascalia generata utilizzando il modello CLIP.</li>
                    <li><strong>Inference Time:</strong> Tempo medio di inferenza per generare una didascalia.</li>
                    <li><strong>Caption Length:</strong> Lunghezza media delle didascalie generate (in token).</li>
                </ul>
            </div>

            <div class="section">
                <h2>Esempi Qualitativi</h2>
                {examples_section}
            </div>

            <div class="footer">
                <p>Report generato automaticamente per la valutazione dei modelli di generazione didascalie SVG.</p>
                <p>© {datetime.now().year} Università di Modena e Reggio Emilia</p>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """

    # Salva il report HTML
    output_file = os.path.join(output_dir, "enhanced_evaluation_report.html")
    with open(output_file, "w") as f:
        f.write(html)

    print(f"Report HTML avanzato generato con successo: {output_file}")

    return output_file

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Generazione di un report HTML avanzato")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/evaluation/reports", help="Directory di output per il report HTML")
    parser.add_argument("--metrics_file", type=str, default="/work/tesi_ediluzio/evaluation/zero_shot/zero_shot_metrics.json", help="File JSON con le metriche")
    parser.add_argument("--examples_file", type=str, default="/work/tesi_ediluzio/evaluation/zero_shot/zero_shot_examples.json", help="File JSON con gli esempi")
    parser.add_argument("--title", type=str, default="Valutazione dei Modelli di Generazione Didascalie SVG", help="Titolo del report")
    parser.add_argument("--num_examples", type=int, default=5, help="Numero di esempi qualitativi per modello")

    args = parser.parse_args()

    # Genera il report HTML
    generate_html_report(
        output_dir=args.output_dir,
        metrics_file=args.metrics_file,
        examples_file=args.examples_file,
        title=args.title,
        num_examples=args.num_examples
    )
