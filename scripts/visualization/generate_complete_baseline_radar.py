#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare un grafico radar completo con tutti i modelli baseline
inclusi i CLIP scores e parametri dei modelli.
"""

import matplotlib.pyplot as plt
import numpy as np
import json
import os
from pathlib import Path
import argparse
from transformers import AutoModel, AutoTokenizer
import torch

# Configurazione
plt.rcParams['font.size'] = 12
plt.rcParams['figure.figsize'] = (14, 10)

def get_model_parameters(model_name):
    """Calcola i parametri del modello."""
    try:
        model_lower = model_name.lower().replace(' ', '_').replace('-', '_')

        if 'ide_fix_3' in model_lower or model_lower == 'idefix':
            # Ide Fix 3 - basato su T5-base
            return {
                'total_params': 220_000_000,  # ~220M parametri
                'trainable_params': 220_000_000,
                'trainable_percentage': 100.0
            }
        elif 'flores_2' in model_lower or model_lower == 'flores2':
            # Flores 2 - basato su mBART
            return {
                'total_params': 610_000_000,  # ~610M parametri
                'trainable_params': 610_000_000,
                'trainable_percentage': 100.0
            }
        elif 'blip_2.7b' in model_lower or model_lower == 'blip2':
            # BLIP2 2.7B
            return {
                'total_params': 2_700_000_000,  # 2.7B parametri
                'trainable_params': 2_700_000_000,
                'trainable_percentage': 100.0
            }
        elif model_lower == 'blip':
            # BLIP base
            return {
                'total_params': 385_000_000,  # ~385M parametri
                'trainable_params': 385_000_000,
                'trainable_percentage': 100.0
            }
        elif 'vit_gpt2' in model_lower:
            # ViT-GPT2
            return {
                'total_params': 137_000_000,  # ~137M parametri
                'trainable_params': 137_000_000,
                'trainable_percentage': 100.0
            }
        elif 'git_base' in model_lower:
            # GIT-base
            return {
                'total_params': 139_000_000,  # ~139M parametri
                'trainable_params': 139_000_000,
                'trainable_percentage': 100.0
            }
        elif 'llama' in model_lower:
            # Llama 3.1 8B
            return {
                'total_params': 8_030_000_000,  # 8.03B parametri
                'trainable_params': 16_777_216 if 'fine_tuned' in model_lower else 8_030_000_000,  # LoRA: ~16.7M
                'trainable_percentage': 0.21 if 'fine_tuned' in model_lower else 100.0
            }
        elif 'gemma' in model_lower:
            # Gemma 2 9B IT
            return {
                'total_params': 9_240_000_000,  # 9.24B parametri
                'trainable_params': 16_777_216 if 'fine_tuned' in model_lower else 9_240_000_000,  # LoRA: ~16.7M
                'trainable_percentage': 0.18 if 'fine_tuned' in model_lower else 100.0
            }
        else:
            return {
                'total_params': 0,
                'trainable_params': 0,
                'trainable_percentage': 0.0
            }
    except Exception as e:
        print(f"Errore nel calcolo parametri per {model_name}: {e}")
        return {
            'total_params': 0,
            'trainable_params': 0,
            'trainable_percentage': 0.0
        }

def load_baseline_predictions(file_path):
    """Carica le predizioni baseline da file JSONL."""
    predictions = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    predictions.append(json.loads(line))
        return predictions
    except Exception as e:
        print(f"Errore nel caricamento {file_path}: {e}")
        return []

def calculate_baseline_metrics(predictions):
    """Calcola metriche baseline simulate basate sui dati reali."""
    if not predictions:
        return {}

    # Analizza la qualità delle predizioni
    total_samples = len(predictions)

    # Calcola lunghezze medie
    ref_lengths = [len(p.get('reference', '').split()) for p in predictions]
    gen_lengths = [len(p.get('generated', '').split()) for p in predictions]

    avg_ref_length = np.mean(ref_lengths) if ref_lengths else 0
    avg_gen_length = np.mean(gen_lengths) if gen_lengths else 0

    # Simula metriche basate sulla qualità osservata
    length_ratio = min(avg_gen_length / max(avg_ref_length, 1), 1.0)

    # Metriche simulate basate sull'analisi qualitativa
    base_quality = 0.15  # Qualità base osservata

    return {
        'BLEU-1': base_quality * length_ratio * 1.2,
        'BLEU-2': base_quality * length_ratio * 0.8,
        'BLEU-3': base_quality * length_ratio * 0.6,
        'BLEU-4': base_quality * length_ratio * 0.4,
        'METEOR': base_quality * length_ratio * 1.1,
        'CIDEr': base_quality * length_ratio * 1.5,
        'CLIP': base_quality * 1.8  # CLIP score simulato
    }

def create_complete_baseline_radar():
    """Crea il grafico radar completo con tutti i modelli baseline."""

    # DATI REALI DEI MODELLI BASELINE (da experiments/baseline_metrics_355_dataset/all_baseline_metrics_355_dataset.json)
    # VALUTATI SUL DATASET DI 355 ESEMPI + CLIP SCORES REALI
    baseline_models = {
        'Ide Fix 3': {
            'BLEU-1': 0.1090, 'BLEU-2': 0.0495, 'BLEU-3': 0.0238, 'BLEU-4': 0.0150,
            'METEOR': 0.0000, 'CIDEr': 0.2267, 'CLIPScore': 0.2732  # CLIPScore REALE: 27.32/100
        },
        'Flores 2 base': {
            'BLEU-1': 0.1736, 'BLEU-2': 0.0687, 'BLEU-3': 0.0334, 'BLEU-4': 0.0163,
            'METEOR': 0.0000, 'CIDEr': 0.1106, 'CLIPScore': 0.2253  # CLIPScore REALE: 22.53/100
        },
        'BLIP 2.7B': {
            'BLEU-1': 0.1298, 'BLEU-2': 0.0643, 'BLEU-3': 0.0337, 'BLEU-4': 0.0164,
            'METEOR': 0.0000, 'CIDEr': 0.2264, 'CLIPScore': 0.2802  # CLIPScore REALE: 28.02/100
        }
    }

    # RIMUOVO I DATI FINTI DEI MODELLI FINE-TUNED - CONCENTRARSI SOLO SUI BASELINE REALI
    finetuned_models = {}

    # Combina tutti i modelli
    all_models = {**baseline_models, **finetuned_models}

    # Metriche da plottare
    metrics = ['BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'METEOR', 'CIDEr', 'CLIPScore']

    # Setup del grafico radar
    N = len(metrics)
    angles = np.linspace(0, 2*np.pi, N, endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il loop

    # Crea la figura - SOLO MODELLI BASELINE REALI
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(polar=True))

    # Colori per i modelli baseline
    baseline_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

    # GRAFICO: Solo modelli baseline REALI
    ax.set_title('Modelli Baseline - Risultati REALI\n(BLEU, METEOR, CIDEr, CLIPScore)',
                  size=16, fontweight='bold', pad=30)

    for i, (model_name, model_metrics) in enumerate(baseline_models.items()):
        values = [model_metrics[metric] for metric in metrics]
        values += values[:1]  # Chiudi il loop

        color = baseline_colors[i % len(baseline_colors)]
        ax.plot(angles, values, 'o-', linewidth=3, label=model_name, color=color, markersize=8)
        ax.fill(angles, values, alpha=0.2, color=color)

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics, size=14, fontweight='bold')
    ax.set_ylim(0, 0.35)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12)
    ax.grid(True, alpha=0.3)

    plt.tight_layout()

    # Salva il grafico
    output_path = "experiments/xml_direct_input/outputs/baseline_radar_REAL_DATA.png"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"✅ Grafico radar BASELINE REALI salvato in: {output_path}")

    return all_models

def generate_parameters_report(all_models):
    """Genera il report dei parametri per tutti i modelli."""

    print("\n" + "="*80)
    print("📊 REPORT COMPLETO PARAMETRI MODELLI")
    print("="*80)

    # Calcola parametri per ogni modello
    for model_name in all_models.keys():
        params = get_model_parameters(model_name)

        print(f"\n🔹 {model_name.upper()}:")
        print(f"   📈 Parametri Totali: {params['total_params']:,}")
        print(f"   🎯 Parametri Trainable: {params['trainable_params']:,}")
        print(f"   📊 Percentuale Trainable: {params['trainable_percentage']:.2f}%")

        # Calcola dimensione in GB (approssimativa)
        size_gb = params['total_params'] * 4 / (1024**3)  # 4 bytes per parametro (float32)
        print(f"   💾 Dimensione Approssimativa: {size_gb:.2f} GB")

    print("\n" + "="*80)

    return True

def create_parameters_pie_chart(all_models):
    """Crea un grafico a torta per i parametri dei modelli."""

    # Prepara i dati per il grafico a torta
    model_names = []
    param_counts = []
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FF4757', '#3742FA']

    for model_name in all_models.keys():
        params = get_model_parameters(model_name)
        if params['total_params'] > 0:
            model_names.append(model_name)
            param_counts.append(params['total_params'])

    # Crea il grafico a torta
    fig, ax = plt.subplots(figsize=(12, 8))

    # Calcola le percentuali
    total_params = sum(param_counts)
    percentages = [count/total_params * 100 for count in param_counts]

    # Crea etichette con numero di parametri
    labels = []
    for name, count in zip(model_names, param_counts):
        if count >= 1_000_000_000:
            label = f"{name}\n{count/1_000_000_000:.1f}B params"
        else:
            label = f"{name}\n{count/1_000_000:.0f}M params"
        labels.append(label)

    # Plot del grafico a torta
    wedges, texts, autotexts = ax.pie(param_counts, labels=labels, autopct='%1.1f%%',
                                      colors=colors[:len(model_names)], startangle=90)

    # Migliora l'aspetto del testo
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(10)

    for text in texts:
        text.set_fontsize(9)

    ax.set_title('Distribuzione Parametri Modelli BASELINE\n(Solo Dati Reali)',
                 fontsize=16, fontweight='bold', pad=20)

    plt.tight_layout()

    # Salva il grafico
    output_path = "experiments/xml_direct_input/outputs/baseline_parameters_pie_REAL_DATA.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"✅ Grafico a torta parametri BASELINE REALI salvato in: {output_path}")

    return True

if __name__ == "__main__":
    print("🎯 Generazione grafico radar completo baseline con CLIP scores...")

    # Genera il grafico radar
    all_models = create_complete_baseline_radar()

    # Genera il grafico a torta dei parametri
    create_parameters_pie_chart(all_models)

    # Genera il report parametri
    generate_parameters_report(all_models)

    print("\n✅ Generazione completata!")
