#!/bin/bash

# Script per creare il report HTML con i risultati dell'inferenza

# Imposta le variabili
LLAMA_RESULTS="evaluation/llama_dataset_results.json"
GEMMA_RESULTS="evaluation/gemma_dataset_results.json"
REPORT_FILE="evaluation/reports/dataset_inference_report.html"

# Crea le directory necessarie
mkdir -p evaluation/reports

# Crea il report HTML
echo "Creazione del report HTML..."
python create_dataset_report.py \
    --llama_results $LLAMA_RESULTS \
    --gemma_results $GEMMA_RESULTS \
    --output_file $REPORT_FILE

echo "Report HTML generato con successo: $REPORT_FILE"
