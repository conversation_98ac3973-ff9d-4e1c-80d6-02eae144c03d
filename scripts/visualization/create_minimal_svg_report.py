import os
import json
import argparse
from datetime import datetime

def create_minimal_svg_report(llama_results_file, gemma_results_file, output_file):
    """
    Crea un report HTML minimalista con SVG incorporati direttamente.
    """
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Limita il numero di esempi
    if len(llama_results) > 3:
        llama_results = llama_results[:3]
    if len(gemma_results) > 3:
        gemma_results = gemma_results[:3]
    
    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>SVG Report</title>
    <style>
        body { font-family: Arial; margin: 20px; }
        .example { margin-bottom: 30px; border: 1px solid #ccc; padding: 10px; }
        .svg-container { width: 300px; height: 300px; border: 1px solid #000; margin: 10px auto; }
        .ground-truth { color: blue; }
        .generated { color: green; }
    </style>
</head>
<body>
    <h1>SVG Report</h1>
"""
    
    # Aggiungi gli esempi per Llama
    html += "<h2>Llama 3.1 8B</h2>"
    
    for i, result in enumerate(llama_results):
        svg_content = result.get("svg", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        
        # Estrai il contenuto SVG
        if svg_content.startswith('<?xml'):
            svg_content = svg_content[svg_content.find('<svg'):]
        
        # Crea un SVG hardcoded per test
        test_svg = """<svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
  <rect width="100" height="100" x="100" y="100" fill="blue" />
</svg>"""
        
        html += f"""
    <div class="example">
        <h3>Esempio {i+1}</h3>
        <div class="svg-container">
            {test_svg}
        </div>
        <div class="ground-truth">
            <strong>Ground Truth:</strong> {true_caption}
        </div>
        <div class="generated">
            <strong>Generated:</strong> {generated_caption}
        </div>
    </div>
"""
    
    # Aggiungi gli esempi per Gemma
    html += "<h2>Gemma 2 9B IT</h2>"
    
    for i, result in enumerate(gemma_results):
        svg_content = result.get("svg", "")
        true_caption = result.get("true_caption", "")
        generated_caption = result.get("generated_caption", "")
        
        # Estrai il contenuto SVG
        if svg_content.startswith('<?xml'):
            svg_content = svg_content[svg_content.find('<svg'):]
        
        # Crea un SVG hardcoded per test
        test_svg = """<svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
  <circle cx="150" cy="150" r="50" fill="red" />
</svg>"""
        
        html += f"""
    <div class="example">
        <h3>Esempio {i+1}</h3>
        <div class="svg-container">
            {test_svg}
        </div>
        <div class="ground-truth">
            <strong>Ground Truth:</strong> {true_caption}
        </div>
        <div class="generated">
            <strong>Generated:</strong> {generated_caption}
        </div>
    </div>
"""
    
    html += """
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Genera un report HTML minimalista")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report HTML
    create_minimal_svg_report(args.llama_results, args.gemma_results, args.output_file)

if __name__ == "__main__":
    main()
