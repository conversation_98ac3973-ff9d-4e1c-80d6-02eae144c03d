import matplotlib.pyplot as plt
import numpy as np
import os
from datetime import datetime

# Crea la directory di output se non esiste
output_dir = "/work/tesi_ediluzio/evaluation/charts"
os.makedirs(output_dir, exist_ok=True)

# Dati estratti dai log di training di Llama
# [step, loss]
llama_data = [
    [0, 1.4493],
    [140, 0.9477],
    [280, 0.7426],
    [420, 0.7304],
    [560, 0.6855],
    [700, 0.6589],
    [840, 0.6281],
    [980, 0.5776],
    [1120, 0.5978],
    [1260, 0.5777],
    [1400, 0.5739],
    [1540, 0.606],
    [1680, 0.5827],
    [1820, 0.6246],
    [1960, 0.5477],
    [2100, 0.5183],
    [2240, 0.5453],
    [2380, 0.4975],
    [2520, 0.5161],
    [2660, 0.5097],
    [2800, 0.5269],
    [2940, 0.4696],
    [3080, 0.4342],
    [3220, 0.476],
    [3360, 0.5065],
    [3500, 0.4545],
    [3640, 0.4466],
    [3780, 0.459],
    [3920, 0.3796],
    [4060, 0.4069],
    [4200, 0.3914],
    [4340, 0.3923],
    [4480, 0.399],
    [4620, 0.4139],
    [4760, 0.407],
    [4900, 0.3234],
    [5040, 0.3437],
    [5180, 0.3191],
    [5320, 0.3138],
    [5460, 0.305],
    [5600, 0.3195],
    [5740, 0.3237],
    [5880, 0.2162],
    [6020, 0.2198],
    [6160, 0.2471],
    [6300, 0.2389],
    [6440, 0.2302],
    [6580, 0.2223],
    [6720, 0.2494],
    [6860, 0.1459],
    [7000, 0.1508],
    [7140, 0.1495],
    [7280, 0.1482],
    [7420, 0.1492],
    [7560, 0.1476],
    [7700, 0.1683],
    [7840, 0.0938],
    [7980, 0.091],
    [8120, 0.0951],
    [8260, 0.1001],
    [8400, 0.0956],
    [8540, 0.0953],
    [8680, 0.0994],
    [8820, 0.0639],
    [8960, 0.0689],
    [9100, 0.0648],
    [9240, 0.0684],
    [9380, 0.0663],
    [9520, 0.0646],
    [9660, 0.067],
    [9800, 0.0509],
    [9940, 0.0506],
    [10080, 0.0486],
    [10220, 0.0503],
    [10360, 0.0539],
    [10500, 0.0529],
    [10640, 0.052],
    [10780, 0.0433],
    [10920, 0.0441],
    [11060, 0.0441],
    [11200, 0.0436],
    [11340, 0.043],
    [11480, 0.0443],
    [11620, 0.0445],
    [11760, 0.0377],
    [11900, 0.0393],
    [12040, 0.0398],
    [12180, 0.0394],
    [12320, 0.0397],
    [12460, 0.0407],
    [12600, 0.0404],
    [12740, 0.0371],
    [12880, 0.0362],
    [13020, 0.0363],
    [13160, 0.0377],
    [13300, 0.0384],
    [13440, 0.0375],
    [13580, 0.0372],
    [13720, 0.0362],
    [13860, 0.0357],
    [14000, 0.036],
    [14140, 0.0366],
    [14280, 0.0362],
    [14420, 0.0352],
    [14560, 0.0367]
]

# Dati estratti dai log di training di Gemma
# [step, loss]
gemma_data = [
    [0, 1.1399],
    [140, 0.7306],
    [280, 0.5459],
    [420, 0.5333],
    [560, 0.4968],
    [700, 0.4663],
    [840, 0.437],
    [980, 0.391],
    [1120, 0.405],
    [1260, 0.3946],
    [1400, 0.3927],
    [1540, 0.4079],
    [1680, 0.3928],
    [1820, 0.4222],
    [1960, 0.3611],
    [2100, 0.3433],
    [2240, 0.3635],
    [2380, 0.3324],
    [2520, 0.3432],
    [2660, 0.3393],
    [2800, 0.35],
    [2940, 0.298],
    [3080, 0.279],
    [3220, 0.3014],
    [3360, 0.3268],
    [3500, 0.2974],
    [3640, 0.2922],
    [3780, 0.2962],
    [3920, 0.2287],
    [4060, 0.2512],
    [4200, 0.2463],
    [4340, 0.242],
    [4480, 0.2463],
    [4620, 0.2527],
    [4760, 0.2503],
    [4900, 0.189],
    [5040, 0.1996],
    [5180, 0.1848],
    [5320, 0.1875],
    [5460, 0.1807],
    [5600, 0.1895],
    [5740, 0.1859],
    [5880, 0.1188],
    [6020, 0.117],
    [6160, 0.1395],
    [6300, 0.1257],
    [6440, 0.1297],
    [6580, 0.1212],
    [6720, 0.1341],
    [6860, 0.0738],
    [7000, 0.0764],
    [7140, 0.0715],
    [7280, 0.0732],
    [7420, 0.076],
    [7560, 0.0744],
    [7700, 0.0815],
    [7840, 0.0477],
    [7980, 0.0437],
    [8120, 0.0454],
    [8260, 0.0461],
    [8400, 0.0445],
    [8540, 0.0453],
    [8680, 0.0445],
    [8820, 0.0321],
    [8960, 0.0323],
    [9100, 0.0314],
    [9240, 0.032],
    [9380, 0.0321],
    [9520, 0.0316],
    [9660, 0.0337],
    [9800, 0.0275],
    [9940, 0.0267],
    [10080, 0.0266],
    [10220, 0.0265],
    [10360, 0.0273],
    [10500, 0.0282],
    [10640, 0.027],
    [10780, 0.0241],
    [10920, 0.024],
    [11060, 0.0249],
    [11200, 0.0247],
    [11340, 0.0246],
    [11480, 0.0248],
    [11620, 0.0251],
    [11760, 0.023],
    [11900, 0.0233],
    [12040, 0.0235],
    [12180, 0.0242],
    [12320, 0.0229],
    [12460, 0.0239],
    [12600, 0.0241],
    [12740, 0.0232],
    [12880, 0.0223],
    [13020, 0.0224],
    [13160, 0.0231],
    [13300, 0.0235],
    [13440, 0.0234],
    [13580, 0.0228],
    [13720, 0.0233],
    [13860, 0.0221],
    [14000, 0.0226],
    [14140, 0.0226],
    [14280, 0.0225],
    [14420, 0.0221],
    [14560, 0.0227]
]

# Estrai i dati
llama_steps = [point[0] for point in llama_data]
llama_loss = [point[1] for point in llama_data]
gemma_steps = [point[0] for point in gemma_data]
gemma_loss = [point[1] for point in gemma_data]

# Crea il grafico nello stile di Weights & Biands
plt.figure(figsize=(8, 5))
plt.plot(llama_steps, llama_loss, color='#3366cc', linewidth=2, label='Llama 3.1 8B')
plt.plot(gemma_steps, gemma_loss, color='#dc3912', linewidth=2, label='Gemma 2 9B IT')

# Aggiungi le linee verticali per indicare l'inizio dell'overfitting
plt.axvline(x=1160, color='#3366cc', linestyle='--', alpha=0.5, label='Llama overfitting start')
plt.axvline(x=1040, color='#dc3912', linestyle='--', alpha=0.5, label='Gemma overfitting start')

# Personalizza il grafico
plt.title('best_train_loss', fontsize=14, pad=15)
plt.xlabel('Step', fontsize=12)
plt.ylabel('Loss', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.3)
plt.legend(fontsize=10)

# Imposta i limiti degli assi
plt.xlim(0, 15000)
plt.ylim(0, 1.5)

# Aggiungi un po' di padding
plt.tight_layout()

# Salva il grafico
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_path = f"{output_dir}/best_train_loss_wandb_style_{timestamp}.png"
plt.savefig(output_path, dpi=300, bbox_inches='tight')
print(f"Grafico salvato in: {output_path}")

# Crea una versione con scala logaritmica per la loss
plt.figure(figsize=(8, 5))
plt.semilogy(llama_steps, llama_loss, color='#3366cc', linewidth=2, label='Llama 3.1 8B')
plt.semilogy(gemma_steps, gemma_loss, color='#dc3912', linewidth=2, label='Gemma 2 9B IT')

# Aggiungi le linee verticali per indicare l'inizio dell'overfitting
plt.axvline(x=1160, color='#3366cc', linestyle='--', alpha=0.5, label='Llama overfitting start')
plt.axvline(x=1040, color='#dc3912', linestyle='--', alpha=0.5, label='Gemma overfitting start')

# Personalizza il grafico
plt.title('best_train_loss (log scale)', fontsize=14, pad=15)
plt.xlabel('Step', fontsize=12)
plt.ylabel('Loss (log scale)', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.3)
plt.legend(fontsize=10)

# Imposta i limiti degli assi
plt.xlim(0, 15000)
plt.ylim(0.01, 2.0)

# Aggiungi un po' di padding
plt.tight_layout()

# Salva il grafico
log_output_path = f"{output_dir}/best_train_loss_log_scale_wandb_style_{timestamp}.png"
plt.savefig(log_output_path, dpi=300, bbox_inches='tight')
print(f"Grafico con scala logaritmica salvato in: {log_output_path}")

# Crea una versione che mostra solo i primi 3000 step (focus sulla fase iniziale)
plt.figure(figsize=(8, 5))
plt.plot(llama_steps, llama_loss, color='#3366cc', linewidth=2, label='Llama 3.1 8B')
plt.plot(gemma_steps, gemma_loss, color='#dc3912', linewidth=2, label='Gemma 2 9B IT')

# Aggiungi le linee verticali per indicare l'inizio dell'overfitting
plt.axvline(x=1160, color='#3366cc', linestyle='--', alpha=0.5, label='Llama overfitting start')
plt.axvline(x=1040, color='#dc3912', linestyle='--', alpha=0.5, label='Gemma overfitting start')

# Personalizza il grafico
plt.title('best_train_loss (first 3000 steps)', fontsize=14, pad=15)
plt.xlabel('Step', fontsize=12)
plt.ylabel('Loss', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.3)
plt.legend(fontsize=10)

# Imposta i limiti degli assi
plt.xlim(0, 3000)
plt.ylim(0, 1.5)

# Aggiungi un po' di padding
plt.tight_layout()

# Salva il grafico
early_output_path = f"{output_dir}/best_train_loss_early_steps_wandb_style_{timestamp}.png"
plt.savefig(early_output_path, dpi=300, bbox_inches='tight')
print(f"Grafico dei primi 3000 step salvato in: {early_output_path}")

print("Completato!")
