import os
import json
import argparse
from datetime import datetime

def create_svg_files_report(llama_results_file, gemma_results_file, output_file):
    """
    Crea un report HTML con SVG salvati come file separati.
    """
    # Crea la directory per gli SVG
    svg_dir = os.path.join(os.path.dirname(output_file), "svg_files")
    os.makedirs(svg_dir, exist_ok=True)
    
    # Carica i risultati
    llama_results = []
    gemma_results = []
    
    if os.path.exists(llama_results_file):
        with open(llama_results_file, "r") as f:
            llama_results = json.load(f)
    
    if os.path.exists(gemma_results_file):
        with open(gemma_results_file, "r") as f:
            gemma_results = json.load(f)
    
    # Organizza i risultati per complessità
    results_by_complexity = {
        "simple": {"llama": [], "gemma": []},
        "medium": {"llama": [], "gemma": []},
        "complex": {"llama": [], "gemma": []}
    }
    
    for result in llama_results:
        complexity = result.get("complexity", "unknown")
        if complexity in results_by_complexity:
            results_by_complexity[complexity]["llama"].append(result)
    
    for result in gemma_results:
        complexity = result.get("complexity", "unknown")
        if complexity in results_by_complexity:
            results_by_complexity[complexity]["gemma"].append(result)
    
    # Salva gli SVG come file separati
    svg_files = {}
    for complexity in results_by_complexity:
        for model in ["llama", "gemma"]:
            for i, result in enumerate(results_by_complexity[complexity][model]):
                svg_content = result.get("svg", "")
                svg_filename = f"{complexity}_{model}_{i+1}.svg"
                svg_path = os.path.join(svg_dir, svg_filename)
                
                with open(svg_path, "w") as f:
                    f.write(svg_content)
                
                # Salva il percorso del file SVG nel risultato
                result["svg_file"] = os.path.join("svg_files", svg_filename)
                svg_files[f"{complexity}_{model}_{i+1}"] = os.path.join("svg_files", svg_filename)
    
    # Crea l'HTML
    html = """<!DOCTYPE html>
<html>
<head>
    <title>Inferenza dei Modelli Base su SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .example {
            margin-bottom: 40px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .svg-container {
            width: 512px;
            height: 512px;
            border: 1px solid #ccc;
            margin-bottom: 10px;
            margin: 0 auto;
        }
        .svg-container img {
            max-width: 100%;
            max-height: 100%;
        }
        .caption {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .model-section {
            margin-top: 50px;
            margin-bottom: 30px;
        }
        .complexity-section {
            margin-bottom: 40px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        .file-name {
            font-style: italic;
            color: #666;
            margin-bottom: 10px;
        }
        .metrics {
            margin-top: 10px;
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Inferenza dei Modelli Base su SVG</h1>
    <p style="text-align: center;">Report generato il """ + datetime.now().strftime('%d/%m/%Y %H:%M:%S') + """</p>
"""
    
    # Aggiungi le sezioni per ogni complessità
    for complexity in ["simple", "medium", "complex"]:
        if not results_by_complexity[complexity]["llama"] and not results_by_complexity[complexity]["gemma"]:
            continue
            
        html += f"""
    <div class="complexity-section">
        <h2>Complessità: {complexity.capitalize()}</h2>
"""
        
        # Aggiungi la sezione per Llama
        html += """
        <div class="model-section">
            <h3>Modello: Llama 3.1 8B Instruct</h3>
"""
        
        # Aggiungi gli esempi per Llama
        for i, result in enumerate(results_by_complexity[complexity]["llama"]):
            file_name = result.get("file", "")
            generated_caption = result.get("generated_caption", "")
            inference_time = result.get("inference_time", 0)
            svg_file = result.get("svg_file", "")
            
            html += f"""
            <div class="example">
                <div class="file-name">{os.path.basename(file_name) if file_name else f"Esempio {complexity} {i+1}"}</div>
                <div class="svg-container">
                    <object type="image/svg+xml" data="{svg_file}" width="100%" height="100%">
                        Il tuo browser non supporta SVG
                    </object>
                </div>
                <div class="caption">
                    <strong>Didascalia generata:</strong>
                    <p>{generated_caption}</p>
                </div>
                <div class="metrics">
                    Tempo di inferenza: {inference_time:.2f}s
                </div>
            </div>
"""
        
        html += """
        </div>
"""
        
        # Aggiungi la sezione per Gemma
        html += """
        <div class="model-section">
            <h3>Modello: Gemma 2 9B IT</h3>
"""
        
        # Aggiungi gli esempi per Gemma
        for i, result in enumerate(results_by_complexity[complexity]["gemma"]):
            file_name = result.get("file", "")
            generated_caption = result.get("generated_caption", "")
            inference_time = result.get("inference_time", 0)
            svg_file = result.get("svg_file", "")
            
            html += f"""
            <div class="example">
                <div class="file-name">{os.path.basename(file_name) if file_name else f"Esempio {complexity} {i+1}"}</div>
                <div class="svg-container">
                    <object type="image/svg+xml" data="{svg_file}" width="100%" height="100%">
                        Il tuo browser non supporta SVG
                    </object>
                </div>
                <div class="caption">
                    <strong>Didascalia generata:</strong>
                    <p>{generated_caption}</p>
                </div>
                <div class="metrics">
                    Tempo di inferenza: {inference_time:.2f}s
                </div>
            </div>
"""
        
        html += """
        </div>
    </div>
"""
    
    # Chiudi l'HTML
    html += """
    <footer style="text-align: center; margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p>Report generato automaticamente per l'inferenza dei modelli base su SVG.</p>
        <p>© """ + str(datetime.now().year) + """ Università di Modena e Reggio Emilia</p>
    </footer>
</body>
</html>"""
    
    # Salva il file HTML
    with open(output_file, "w") as f:
        f.write(html)
    
    print(f"Report HTML generato con successo: {output_file}")
    print(f"SVG salvati nella directory: {svg_dir}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Genera un report HTML con SVG salvati come file separati")
    parser.add_argument("--llama_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Llama")
    parser.add_argument("--gemma_results", type=str, required=True, help="File JSON con i risultati dell'inferenza di Gemma")
    parser.add_argument("--output_file", type=str, required=True, help="File HTML di output")
    
    args = parser.parse_args()
    
    # Crea la directory di output se non esiste
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Crea il report HTML
    create_svg_files_report(args.llama_results, args.gemma_results, args.output_file)
