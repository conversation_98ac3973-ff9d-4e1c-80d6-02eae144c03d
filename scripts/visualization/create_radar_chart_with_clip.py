#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per generare un grafico radar che confronta metriche di valutazione
tra modelli base e fine-tuned, incluso CLIP score.
"""

import os
import sys
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl

# Imposta uno stile più moderno
plt.style.use('seaborn-v0_8')
mpl.rcParams['font.family'] = 'sans-serif'
mpl.rcParams['font.sans-serif'] = ['Arial']

def load_metrics(metrics_file):
    """
    Carica le metriche da un file JSON.
    """
    try:
        with open(metrics_file, 'r', encoding='utf-8') as f:
            metrics = json.load(f)
        return metrics
    except Exception as e:
        print(f"Errore nel caricamento del file {metrics_file}: {e}")
        return None

def load_clip_scores(clip_scores_file):
    """
    Carica i CLIP score da un file JSON.
    """
    try:
        with open(clip_scores_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if 'aggregated_metrics' in data:
                return data['aggregated_metrics']['mean_generated_clip_score']
            else:
                # Fallback: cerca di trovare i clip score in altra struttura
                scores = [item.get('generated_clip_score', 0) for item in data.get('results', [])]
                if scores:
                    return np.mean(scores)
                return 0
    except Exception as e:
        print(f"Errore nel caricamento del file CLIP {clip_scores_file}: {e}")
        return 0

def normalize_metrics(metrics_dict, max_values=None):
    """
    Normalizza le metriche in un intervallo [0, 1] per il grafico radar.
    
    Args:
        metrics_dict: Dizionario con le metriche
        max_values: Dizionario con i valori massimi per ogni metrica
    
    Returns:
        Dizionario con le metriche normalizzate
    """
    normalized = {}
    
    # Valori massimi tipici per ogni metrica
    if max_values is None:
        max_values = {
            'bleu_1': 1.0,
            'bleu_2': 1.0,
            'bleu_3': 1.0,
            'bleu_4': 1.0,
            'meteor': 1.0,
            'cider': 3.0,  # CIDEr può superare 1.0
            'clip_score': 35.0  # CLIP score tipicamente va da 0 a 35
        }
    
    for key, value in metrics_dict.items():
        if key.lower() in max_values:
            normalized[key] = value / max_values[key.lower()]
        else:
            normalized[key] = value  # Mantieni il valore originale se non c'è un max
    
    return normalized

def create_radar_chart(models_data, output_file=None, title="Confronto delle Metriche di Valutazione"):
    """
    Crea un grafico radar che confronta diverse metriche tra modelli.
    
    Args:
        models_data: Lista di dizionari con i dati dei modelli
        output_file: File di output per il grafico
        title: Titolo del grafico
    """
    # Estrai le categorie (metriche) da visualizzare
    categories = set()
    for model in models_data:
        categories.update(model['metrics'].keys())
    categories = sorted(list(categories))
    
    # Numero di categorie
    N = len(categories)
    
    # Angoli per il grafico radar
    angles = [n / float(N) * 2 * np.pi for n in range(N)]
    angles += angles[:1]  # Chiudi il cerchio
    
    # Crea la figura
    fig = plt.figure(figsize=(10, 10))
    ax = plt.subplot(111, polar=True)
    
    # Colori per i modelli
    colors = ['#FF5733', '#33A1FF', '#FF9033', '#33FFC7']
    
    # Stili di linea per base vs fine-tuned
    line_styles = ['-', '-', '--', '--']
    
    # Crea il grafico radar per ogni modello
    for i, model in enumerate(models_data):
        values = [model['metrics'].get(cat, 0) for cat in categories]
        values += values[:1]  # Chiudi il cerchio
        
        ax.plot(angles, values, 'o-', linewidth=2, markersize=6, 
                label=model['name'], color=colors[i], linestyle=line_styles[i])
        ax.fill(angles, values, alpha=0.1, color=colors[i])
    
    # Configura gli assi e le etichette
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories, size=12, fontweight='bold')
    
    # Aggiungi griglie
    ax.set_rlabel_position(0)
    plt.yticks([0.2, 0.4, 0.6, 0.8, 1.0], ["0.2", "0.4", "0.6", "0.8", "1.0"], 
               color="grey", size=10)
    plt.ylim(0, 1.1)
    
    # Aggiungi titolo e legenda
    plt.title(title, size=16, pad=20)
    plt.legend(loc='lower right', bbox_to_anchor=(0.1, 0.1), fontsize=10)
    
    # Salva o mostra il grafico
    if output_file:
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Grafico salvato: {output_file}")
    else:
        plt.tight_layout()
        plt.show()
    
    plt.close()

def main():
    parser = argparse.ArgumentParser(description="Genera un grafico radar con metriche di valutazione.")
    parser.add_argument("--llama_finetuned_metrics", type=str, help="File JSON con le metriche per Llama fine-tuned")
    parser.add_argument("--gemma_finetuned_metrics", type=str, help="File JSON con le metriche per Gemma fine-tuned")
    parser.add_argument("--llama_base_metrics", type=str, help="File JSON con le metriche per Llama base")
    parser.add_argument("--gemma_base_metrics", type=str, help="File JSON con le metriche per Gemma base")
    parser.add_argument("--llama_finetuned_clip", type=str, help="File JSON con i CLIP score per Llama fine-tuned")
    parser.add_argument("--gemma_finetuned_clip", type=str, help="File JSON con i CLIP score per Gemma fine-tuned")
    parser.add_argument("--llama_base_clip", type=str, help="File JSON con i CLIP score per Llama base")
    parser.add_argument("--gemma_base_clip", type=str, help="File JSON con i CLIP score per Gemma base")
    parser.add_argument("--output", type=str, default="radar_chart_with_clip.png", help="File di output per il grafico")
    
    args = parser.parse_args()
    
    # Crea un dizionario per i dati dei modelli
    models_data = []
    
    # Carica le metriche e i CLIP score per ogni modello
    if args.llama_finetuned_metrics:
        llama_ft_metrics = load_metrics(args.llama_finetuned_metrics)
        if llama_ft_metrics:
            metrics = {
                "BLEU-1": llama_ft_metrics.get("bleu_1", 0),
                "BLEU-2": llama_ft_metrics.get("bleu_2", 0),
                "BLEU-3": llama_ft_metrics.get("bleu_3", 0),
                "BLEU-4": llama_ft_metrics.get("bleu_4", 0),
                "METEOR": llama_ft_metrics.get("meteor", 0),
                "CIDEr": llama_ft_metrics.get("cider", 0),
            }
            
            # Aggiungi CLIP score se disponibile
            if args.llama_finetuned_clip:
                clip_score = load_clip_scores(args.llama_finetuned_clip)
                metrics["CLIP"] = clip_score
            
            # Normalizza le metriche
            metrics = normalize_metrics(metrics)
            
            models_data.append({
                "name": "Llama 3.1 8B (Fine-tuned)",
                "metrics": metrics
            })
    
    if args.gemma_finetuned_metrics:
        gemma_ft_metrics = load_metrics(args.gemma_finetuned_metrics)
        if gemma_ft_metrics:
            metrics = {
                "BLEU-1": gemma_ft_metrics.get("bleu_1", 0),
                "BLEU-2": gemma_ft_metrics.get("bleu_2", 0),
                "BLEU-3": gemma_ft_metrics.get("bleu_3", 0),
                "BLEU-4": gemma_ft_metrics.get("bleu_4", 0),
                "METEOR": gemma_ft_metrics.get("meteor", 0),
                "CIDEr": gemma_ft_metrics.get("cider", 0),
            }
            
            # Aggiungi CLIP score se disponibile
            if args.gemma_finetuned_clip:
                clip_score = load_clip_scores(args.gemma_finetuned_clip)
                metrics["CLIP"] = clip_score
            
            # Normalizza le metriche
            metrics = normalize_metrics(metrics)
            
            models_data.append({
                "name": "Gemma 2 9B IT (Fine-tuned)",
                "metrics": metrics
            })
    
    if args.llama_base_metrics:
        llama_base_metrics = load_metrics(args.llama_base_metrics)
        if llama_base_metrics:
            metrics = {
                "BLEU-1": llama_base_metrics.get("bleu_1", 0),
                "BLEU-2": llama_base_metrics.get("bleu_2", 0),
                "BLEU-3": llama_base_metrics.get("bleu_3", 0),
                "BLEU-4": llama_base_metrics.get("bleu_4", 0),
                "METEOR": llama_base_metrics.get("meteor", 0),
                "CIDEr": llama_base_metrics.get("cider", 0),
            }
            
            # Aggiungi CLIP score se disponibile
            if args.llama_base_clip:
                clip_score = load_clip_scores(args.llama_base_clip)
                metrics["CLIP"] = clip_score
            
            # Normalizza le metriche
            metrics = normalize_metrics(metrics)
            
            models_data.append({
                "name": "Llama 3.1 8B (Base)",
                "metrics": metrics
            })
    
    if args.gemma_base_metrics:
        gemma_base_metrics = load_metrics(args.gemma_base_metrics)
        if gemma_base_metrics:
            metrics = {
                "BLEU-1": gemma_base_metrics.get("bleu_1", 0),
                "BLEU-2": gemma_base_metrics.get("bleu_2", 0),
                "BLEU-3": gemma_base_metrics.get("bleu_3", 0),
                "BLEU-4": gemma_base_metrics.get("bleu_4", 0),
                "METEOR": gemma_base_metrics.get("meteor", 0),
                "CIDEr": gemma_base_metrics.get("cider", 0),
            }
            
            # Aggiungi CLIP score se disponibile
            if args.gemma_base_clip:
                clip_score = load_clip_scores(args.gemma_base_clip)
                metrics["CLIP"] = clip_score
            
            # Normalizza le metriche
            metrics = normalize_metrics(metrics)
            
            models_data.append({
                "name": "Gemma 2 9B IT (Base)",
                "metrics": metrics
            })
    
    # Crea il grafico radar
    if models_data:
        create_radar_chart(models_data, args.output, "Confronto delle Metriche di Valutazione - Modelli Base vs Fine-tuned")
    else:
        print("Nessun dato disponibile per la creazione del grafico.")

if __name__ == "__main__":
    main() 