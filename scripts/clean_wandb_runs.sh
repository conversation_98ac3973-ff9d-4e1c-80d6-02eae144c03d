#!/bin/bash

# Script per pulire le run vecchie di Weights & Biands

echo "Pulizia delle run vecchie di Weights & Biands..."

# Crea la directory per le run archiviate
ARCHIVED_RUNS_DIR="/work/tesi_ediluzio/wandb/archived"
mkdir -p "$ARCHIVED_RUNS_DIR"

# Ottieni la data di oggi
TODAY=$(date +%Y%m%d)

# Trova le run vecchie (non degli ultimi 7 giorni)
cd /work/tesi_ediluzio/wandb/wandb
OLD_RUNS=$(find . -maxdepth 1 -type d -name "run-*" -mtime +7 | grep -v "latest-run")

# Conta il numero di run trovate
NUM_RUNS=$(echo "$OLD_RUNS" | grep -v "^$" | wc -l)
echo "Trovate $NUM_RUNS run vecchie"

# Se non ci sono run, esci
if [ "$NUM_RUNS" -eq 0 ]; then
    echo "Nessuna run vecchia da archiviare"
    exit 0
fi

# Crea un archivio con le run vecchie
ARCHIVE_NAME="wandb_runs_archive_${TODAY}.tar.gz"
echo "Creazione dell'archivio $ARCHIVE_NAME..."

# Crea l'archivio
tar -czf "$ARCHIVED_RUNS_DIR/$ARCHIVE_NAME" $OLD_RUNS

# Verifica che l'archivio sia stato creato correttamente
if [ $? -eq 0 ]; then
    echo "Archivio creato con successo: $ARCHIVED_RUNS_DIR/$ARCHIVE_NAME"
    
    # Elimina le run originali
    echo "Eliminazione delle run originali..."
    for run in $OLD_RUNS; do
        rm -rf "$run"
    done
    
    echo "Archiviazione completata con successo"
else
    echo "Errore durante la creazione dell'archivio"
    exit 1
fi

echo "Operazione completata"
