#!/usr/bin/env python3
"""
Script per analizzare e pulire il filesystem del progetto SVG Captioning
Identifica file obsoleti, duplicati e non necessari
"""

import os
import json
import shutil
from pathlib import Path
from datetime import datetime
import subprocess

def get_file_size(path):
    """Ottiene la dimensione di un file o directory"""
    if os.path.isfile(path):
        return os.path.getsize(path)
    elif os.path.isdir(path):
        total = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total += os.path.getsize(filepath)
                except (OSError, IOError):
                    pass
        return total
    return 0

def analyze_filesystem():
    """Analizza il filesystem e identifica file da rimuovere"""
    
    # File e directory obsoleti da rimuovere
    obsolete_files = []
    
    # 1. File di documentazione duplicati/obsoleti
    docs_to_remove = [
        "DOCUMENTAZIONE_SCRIPT.md",  # Sostituito da docs/
        "PROJECT_STATUS_AFTER_CLEANUP.md",  # Obsoleto
        "SOLUZIONI_PROBLEMI_TRAINING.md",  # Integrato in memory.md
        "STRUTTURA_PROGETTO.md",  # Sostituito da docs/FILESYSTEM.md
        "fix_evaluation_strategy_issue.md",  # Risolto
        "parameter_count_results.md",  # Spostato in docs/
        "project_status_update.md",  # Obsoleto
        "report.md",  # Generico, sostituito da specifici
        "training_jobs.md",  # Obsoleto
        "wandb_runs_log.md",  # Obsoleto
        "wandb_runs_log_clean.md",  # Obsoleto
    ]
    
    # 2. Script obsoleti/duplicati
    scripts_to_remove = [
        "fix_multi_gpu_device_issue.py",  # Risolto
        "fix_pip.sh",  # Obsoleto
        "generate_metrics_radar_chart.py",  # Spostato in scripts/
        "install_deepspeed.sh",  # Obsoleto
        "test_hf_token.py",  # Test temporaneo
        "run_*.slurm",  # File SLURM nella root (spostati in scripts/slurm/)
    ]
    
    # 3. Directory temporanee/cache
    temp_dirs = [
        "chunks/",  # File temporanei di chunking
        "temp/",  # Directory temporanea
        "cache/huggingface/",  # Cache HuggingFace (può essere ricreata)
        "svg_captioning_env/",  # Ambiente virtuale duplicato
        "offload/",  # Directory vuota
        "html/",  # File HTML temporanei
    ]
    
    # 4. Log obsoleti (mantieni solo gli ultimi)
    logs_to_archive = []
    if os.path.exists("logs"):
        for file in os.listdir("logs"):
            if file.endswith(('.err', '.log', '.out')) and not file.startswith('2584'):
                # Mantieni solo i log dei job attuali (2584xxx)
                logs_to_archive.append(f"logs/{file}")
    
    # 5. Risultati obsoleti/duplicati
    results_to_remove = [
        "results/lora/",  # Risultati vecchi
        "results/zero_shot/",  # Risultati vecchi (sostituiti da evaluation/)
        "results/metrics/",  # Metriche vecchie
        "run_counters.json",  # File di contatori obsoleto
    ]
    
    # 6. Esperimenti obsoleti
    experiments_to_remove = [
        "experiments/fase4_test/",  # Test obsoleto
        "experiments/test_subset/",  # Test obsoleto
        "experiments/parameter_counts/",  # Spostato in docs/
        "experiments/training_optimization_report.txt",  # Obsoleto
    ]
    
    # 7. File di configurazione duplicati/obsoleti
    config_files_to_remove = [
        "requirements.txt",  # Duplicato (esiste in shared/)
        "slurm-*.out",  # Output SLURM temporanei
    ]
    
    # Combina tutte le categorie
    all_obsolete = (
        docs_to_remove + 
        scripts_to_remove + 
        temp_dirs + 
        logs_to_archive + 
        results_to_remove + 
        experiments_to_remove + 
        config_files_to_remove
    )
    
    # Analizza dimensioni
    analysis = {
        "total_files": 0,
        "total_size": 0,
        "obsolete_files": [],
        "large_files": [],
        "empty_dirs": []
    }
    
    for item in all_obsolete:
        if os.path.exists(item):
            size = get_file_size(item)
            analysis["obsolete_files"].append({
                "path": item,
                "size": size,
                "type": "directory" if os.path.isdir(item) else "file"
            })
            analysis["total_size"] += size
    
    # Trova file grandi (>100MB)
    for root, dirs, files in os.walk("."):
        for file in files:
            filepath = os.path.join(root, file)
            try:
                size = os.path.getsize(filepath)
                if size > 100 * 1024 * 1024:  # >100MB
                    analysis["large_files"].append({
                        "path": filepath,
                        "size": size
                    })
            except (OSError, IOError):
                pass
    
    # Trova directory vuote
    for root, dirs, files in os.walk(".", topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            try:
                if not os.listdir(dir_path):
                    analysis["empty_dirs"].append(dir_path)
            except (OSError, IOError):
                pass
    
    return analysis

def create_backup_archive():
    """Crea un archivio di backup prima della pulizia"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"filesystem_backup_{timestamp}.tar.gz"
    
    # Lista di file importanti da backuppare
    important_files = [
        "memory.md",
        "docs/",
        "scripts/",
        "experiments/xml_direct_input/configs/",
        "experiments/xml_direct_input/outputs/",
        "data/processed/xml_format/",
        "logs/",
    ]
    
    print(f"Creando backup: {backup_name}")
    cmd = ["tar", "-czf", backup_name] + [f for f in important_files if os.path.exists(f)]
    
    try:
        subprocess.run(cmd, check=True)
        print(f"✅ Backup creato: {backup_name}")
        return backup_name
    except subprocess.CalledProcessError as e:
        print(f"❌ Errore creazione backup: {e}")
        return None

def cleanup_filesystem(analysis, dry_run=True):
    """Pulisce il filesystem rimuovendo file obsoleti"""
    
    removed_size = 0
    removed_count = 0
    
    print(f"\n{'=' * 60}")
    print(f"PULIZIA FILESYSTEM ({'DRY RUN' if dry_run else 'ESECUZIONE REALE'})")
    print(f"{'=' * 60}")
    
    for item in analysis["obsolete_files"]:
        path = item["path"]
        size = item["size"]
        item_type = item["type"]
        
        print(f"{'[DRY RUN] ' if dry_run else ''}Rimuovendo {item_type}: {path} ({size / (1024*1024):.1f} MB)")
        
        if not dry_run:
            try:
                if os.path.isdir(path):
                    shutil.rmtree(path)
                else:
                    os.remove(path)
                removed_size += size
                removed_count += 1
            except (OSError, IOError) as e:
                print(f"❌ Errore rimozione {path}: {e}")
        else:
            removed_size += size
            removed_count += 1
    
    # Rimuovi directory vuote
    for empty_dir in analysis["empty_dirs"]:
        print(f"{'[DRY RUN] ' if dry_run else ''}Rimuovendo directory vuota: {empty_dir}")
        if not dry_run:
            try:
                os.rmdir(empty_dir)
            except (OSError, IOError) as e:
                print(f"❌ Errore rimozione directory {empty_dir}: {e}")
    
    print(f"\n📊 RIEPILOGO PULIZIA:")
    print(f"File/directory rimossi: {removed_count}")
    print(f"Spazio liberato: {removed_size / (1024*1024*1024):.2f} GB")
    
    return removed_size, removed_count

def update_filesystem_documentation():
    """Aggiorna la documentazione della struttura filesystem"""
    
    filesystem_doc = """# Struttura Filesystem - SVG Captioning Project

## Panoramica Generale
Struttura del progetto dopo pulizia e organizzazione (Aggiornato: {timestamp})

## Directory Principali

### 📁 `docs/` - Documentazione
- **README_jobs_technical_overview.md**: Panoramica tecnica job
- **job_*.md**: Documentazione dettagliata singoli job
- **FILESYSTEM.md**: Struttura filesystem (questo file)
- **ARCHITECTURE.md**: Architettura del sistema
- **PROJECT_STATUS.md**: Status del progetto

### 📁 `scripts/` - Script e Automazione
- **slurm/**: Script SLURM per job submission
- **training/**: Script di training (train_lora_multi_gpu_simple.py)
- **evaluation/**: Script di valutazione e metriche
- **inference/**: Script di inferenza
- **utils/**: Utility varie
- **cleanup/**: Script di pulizia e manutenzione

### 📁 `data/` - Dataset e Dati
- **processed/xml_format/**: Dataset in formato XML per training
- **results/**: Risultati di inferenza per CLIP evaluation
- **test_samples/**: Campioni di test

### 📁 `experiments/` - Esperimenti e Output
- **xml_direct_input/**: Esperimento principale
  - **configs/**: File di configurazione training
  - **outputs/**: Checkpoint e modelli salvati
- **complete_clip_evaluation/**: Risultati CLIP evaluation

### 📁 `logs/` - Log di Sistema
- **archive/**: Log archiviati per data
- ***_2584*.log/err**: Log dei job attuali
- **compressed/**: Log compressi

### 📁 `shared/` - Codice Condiviso
- **svg_core/**: Moduli core per SVG processing
- **utils/**: Utility condivise

### 📁 `analysis/` - Analisi e Visualizzazioni
- **wandb/**: Analisi WandB runs
- **zero_shot/**: Analisi zero-shot performance
- **checkpoint_cleanup/**: Analisi pulizia checkpoint

### 📁 `evaluation/` - Valutazione Modelli
- **reports/**: Report HTML e visualizzazioni
- **charts/**: Grafici e chart
- **zero_shot/**: Risultati zero-shot evaluation

## File Principali

### 📄 Documentazione Core
- **memory.md**: Memoria completa del progetto
- **README.md**: Documentazione principale

### 📄 Configurazione
- **shared/requirements.txt**: Dipendenze Python

### 📄 Ambiente
- **svg_env/**: Ambiente virtuale Python attivo

## Struttura Ottimizzata

### ✅ Mantenuto
- File di documentazione aggiornati
- Script attivi e funzionanti
- Dataset processati
- Checkpoint di training
- Log dei job attuali
- Risultati di evaluation

### ❌ Rimosso
- File di documentazione duplicati/obsoleti
- Script temporanei e di test
- Cache e file temporanei
- Log di job vecchi
- Risultati obsoleti
- Directory vuote

## Dimensioni Approssimative
- **Totale progetto**: ~15-20 GB
- **Dataset**: ~2-3 GB
- **Checkpoint**: ~8-10 GB
- **Log attuali**: ~500 MB
- **Documentazione**: ~50 MB
- **Script**: ~100 MB

## Manutenzione

### Pulizia Automatica
- **Log**: Archiviazione automatica ogni settimana
- **Checkpoint**: Cleanup automatico (mantieni ultimi 3)
- **Cache**: Pulizia periodica cache HuggingFace

### Backup
- **Configurazioni**: Backup giornaliero
- **Checkpoint**: Backup settimanale
- **Documentazione**: Versioning Git

Struttura ottimizzata per efficienza, manutenibilità e chiarezza del progetto.
""".format(timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    with open("docs/FILESYSTEM.md", "w") as f:
        f.write(filesystem_doc)
    
    print("✅ Documentazione filesystem aggiornata: docs/FILESYSTEM.md")

def main():
    """Funzione principale"""
    print("🔍 ANALISI FILESYSTEM SVG CAPTIONING PROJECT")
    print("=" * 60)
    
    # Analizza filesystem
    analysis = analyze_filesystem()
    
    print(f"\n📊 RISULTATI ANALISI:")
    print(f"File obsoleti identificati: {len(analysis['obsolete_files'])}")
    print(f"Spazio da liberare: {analysis['total_size'] / (1024*1024*1024):.2f} GB")
    print(f"File grandi (>100MB): {len(analysis['large_files'])}")
    print(f"Directory vuote: {len(analysis['empty_dirs'])}")
    
    # Mostra dettagli file obsoleti
    print(f"\n📋 FILE OBSOLETI DA RIMUOVERE:")
    for item in analysis["obsolete_files"]:
        print(f"  {item['type']}: {item['path']} ({item['size'] / (1024*1024):.1f} MB)")
    
    # Mostra file grandi
    if analysis["large_files"]:
        print(f"\n📦 FILE GRANDI (>100MB):")
        for item in analysis["large_files"]:
            print(f"  {item['path']}: {item['size'] / (1024*1024):.1f} MB")
    
    # Salva analisi
    with open("filesystem_analysis.json", "w") as f:
        json.dump(analysis, f, indent=2)
    print(f"\n💾 Analisi salvata in: filesystem_analysis.json")
    
    # Chiedi conferma per pulizia
    response = input(f"\n❓ Procedere con la pulizia? (y/N): ").strip().lower()
    
    if response == 'y':
        # Crea backup
        backup_file = create_backup_archive()
        if backup_file:
            # Esegui pulizia reale
            cleanup_filesystem(analysis, dry_run=False)
            
            # Aggiorna documentazione
            update_filesystem_documentation()
            
            print(f"\n✅ PULIZIA COMPLETATA!")
            print(f"📦 Backup salvato: {backup_file}")
            print(f"📚 Documentazione aggiornata: docs/FILESYSTEM.md")
        else:
            print("❌ Pulizia annullata per errore backup")
    else:
        # Esegui solo dry run
        cleanup_filesystem(analysis, dry_run=True)
        print(f"\n💡 Per eseguire la pulizia reale, rilancia con conferma")

if __name__ == "__main__":
    main()
