#!/usr/bin/env python3
"""
Script di pulizia finale del filesystem per il progetto tesi_ediluzio
Rimuove file temporanei, cache obsolete e file non necessari
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime

def get_size_mb(path):
    """Calcola la dimensione di un file o directory in MB"""
    if os.path.isfile(path):
        return os.path.getsize(path) / (1024 * 1024)
    elif os.path.isdir(path):
        total = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    pass
        return total / (1024 * 1024)
    return 0

def cleanup_filesystem():
    """Esegue la pulizia del filesystem"""
    
    print("🧹 PULIZIA FINALE FILESYSTEM TESI_EDILUZIO")
    print("=" * 50)
    print(f"Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Directory di lavoro
    base_dir = Path("/work/tesi_ediluzio")
    os.chdir(base_dir)
    
    # Statistiche iniziali
    total_removed = 0
    files_removed = []
    
    # 1. RIMOZIONE CHUNKS (2.3G)
    chunks_dir = base_dir / "chunks"
    if chunks_dir.exists():
        size_mb = get_size_mb(chunks_dir)
        print(f"🗑️  Rimozione chunks/ ({size_mb:.1f} MB)")
        try:
            shutil.rmtree(chunks_dir)
            total_removed += size_mb
            files_removed.append(f"chunks/ ({size_mb:.1f} MB)")
            print("   ✅ Rimosso con successo")
        except Exception as e:
            print(f"   ❌ Errore: {e}")
    
    # 2. PULIZIA CACHE HUGGINGFACE (opzionale - commentato per sicurezza)
    # cache_dir = base_dir / "cache" / "huggingface"
    # if cache_dir.exists():
    #     size_mb = get_size_mb(cache_dir)
    #     print(f"🗑️  Rimozione cache/huggingface/ ({size_mb:.1f} MB)")
    #     try:
    #         shutil.rmtree(cache_dir)
    #         total_removed += size_mb
    #         files_removed.append(f"cache/huggingface/ ({size_mb:.1f} MB)")
    #         print("   ✅ Rimosso con successo")
    #     except Exception as e:
    #         print(f"   ❌ Errore: {e}")
    
    # 3. PULIZIA WANDB ARCHIVED
    wandb_archived = base_dir / "wandb" / "archived"
    if wandb_archived.exists():
        size_mb = get_size_mb(wandb_archived)
        print(f"🗑️  Rimozione wandb/archived/ ({size_mb:.1f} MB)")
        try:
            shutil.rmtree(wandb_archived)
            total_removed += size_mb
            files_removed.append(f"wandb/archived/ ({size_mb:.1f} MB)")
            print("   ✅ Rimosso con successo")
        except Exception as e:
            print(f"   ❌ Errore: {e}")
    
    # 4. RIMOZIONE FILE SLURM OBSOLETI NELLA ROOT
    slurm_files = [
        "run_training_test_short.slurm",
        "run_training_fixed_updated.slurm", 
        "run_training_fixed.slurm",
        "run_training_test.slurm",
        "run_qualitative_eval.slurm",
        "run_radar_chart_with_clip.slurm",
        "run_inference.slurm",
        "run_external_captioner_eval.slurm"
    ]
    
    print(f"🗑️  Rimozione file SLURM obsoleti nella root...")
    for filename in slurm_files:
        filepath = base_dir / filename
        if filepath.exists():
            size_mb = get_size_mb(filepath)
            try:
                filepath.unlink()
                total_removed += size_mb
                files_removed.append(f"{filename} ({size_mb:.3f} MB)")
                print(f"   ✅ Rimosso {filename}")
            except Exception as e:
                print(f"   ❌ Errore rimozione {filename}: {e}")
    
    # 5. RIMOZIONE LOG SLURM OBSOLETI
    slurm_logs = list(base_dir.glob("slurm-*.out"))
    if slurm_logs:
        print(f"🗑️  Rimozione {len(slurm_logs)} log SLURM obsoleti...")
        for log_file in slurm_logs:
            size_mb = get_size_mb(log_file)
            try:
                log_file.unlink()
                total_removed += size_mb
                files_removed.append(f"{log_file.name} ({size_mb:.3f} MB)")
                print(f"   ✅ Rimosso {log_file.name}")
            except Exception as e:
                print(f"   ❌ Errore rimozione {log_file.name}: {e}")
    
    # 6. RIMOZIONE FILE PYTHON TEMPORANEI
    temp_files = [
        "test_llama_simple.py",
        "accelerate_config.yaml"
    ]
    
    print(f"🗑️  Rimozione file temporanei...")
    for filename in temp_files:
        filepath = base_dir / filename
        if filepath.exists():
            size_mb = get_size_mb(filepath)
            try:
                filepath.unlink()
                total_removed += size_mb
                files_removed.append(f"{filename} ({size_mb:.3f} MB)")
                print(f"   ✅ Rimosso {filename}")
            except Exception as e:
                print(f"   ❌ Errore rimozione {filename}: {e}")
    
    # 7. PULIZIA LOG VECCHI (più di 7 giorni)
    logs_dir = base_dir / "logs"
    if logs_dir.exists():
        print(f"🗑️  Pulizia log vecchi (>7 giorni)...")
        current_time = datetime.now().timestamp()
        week_ago = current_time - (7 * 24 * 3600)
        
        for log_file in logs_dir.glob("*.out"):
            try:
                if log_file.stat().st_mtime < week_ago:
                    size_mb = get_size_mb(log_file)
                    log_file.unlink()
                    total_removed += size_mb
                    files_removed.append(f"logs/{log_file.name} ({size_mb:.3f} MB)")
                    print(f"   ✅ Rimosso log vecchio {log_file.name}")
            except Exception as e:
                print(f"   ❌ Errore rimozione {log_file.name}: {e}")
    
    # REPORT FINALE
    print()
    print("📊 REPORT PULIZIA COMPLETATA")
    print("=" * 50)
    print(f"💾 Spazio liberato totale: {total_removed:.1f} MB ({total_removed/1024:.2f} GB)")
    print(f"📁 File/directory rimossi: {len(files_removed)}")
    print()
    
    if files_removed:
        print("📋 Dettaglio file rimossi:")
        for item in files_removed:
            print(f"   - {item}")
    
    # Salva report
    report = {
        "timestamp": datetime.now().isoformat(),
        "total_removed_mb": total_removed,
        "total_removed_gb": total_removed / 1024,
        "files_removed": files_removed,
        "files_count": len(files_removed)
    }
    
    with open("filesystem_cleanup_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print()
    print("✅ Pulizia completata! Report salvato in filesystem_cleanup_report.json")
    
    return total_removed, files_removed

if __name__ == "__main__":
    cleanup_filesystem()
