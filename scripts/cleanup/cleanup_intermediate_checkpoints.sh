#!/bin/bash

# Script per eliminare checkpoint intermedi e cache non necessari

echo "Inizializzazione pulizia checkpoint intermedi e cache..."

# Elimina checkpoint intermedi di Llama 3.1 8B
echo "Eliminazione checkpoint intermedi di Llama 3.1 8B..."
rm -rf /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/checkpoint-400
rm -rf /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/checkpoint-600

# Elimina checkpoint intermedi di Gemma 2 9B IT
echo "Eliminazione checkpoint intermedi di Gemma 2 9B IT..."
rm -rf /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence/checkpoint-600
rm -rf /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence/checkpoint-700

# Elimina la cache di Hugging Face
echo "Eliminazione cache di Hugging Face..."
rm -rf /work/tesi_ediluzio/.cache/huggingface/hub/models--*

echo "Pulizia completata."
echo "Spazio liberato:"
du -sh /work/tesi_ediluzio
