#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import re
import shutil
import datetime
import time

def parse_args():
    parser = argparse.ArgumentParser(description="Pulisce i file temporanei e obsoleti")
    parser.add_argument("--output_dir", type=str, default="/work/tesi_ediluzio/experiments/xml_direct_input/outputs", 
                        help="Directory di output dei training")
    parser.add_argument("--log_dir", type=str, default="/work/tesi_ediluzio/logs", 
                        help="Directory dei log")
    parser.add_argument("--archive_dir", type=str, default="/work/tesi_ediluzio/logs/archive", 
                        help="Directory di archiviazione dei log")
    parser.add_argument("--dry_run", action="store_true", help="Esegue una simulazione senza eliminare i file")
    parser.add_argument("--days", type=int, default=7, help="Elimina i file più vecchi di N giorni")
    parser.add_argument("--compress_logs", action="store_true", help="Comprime i log vecchi invece di eliminarli")
    parser.add_argument("--clean_checkpoints", action="store_true", help="Pulisce i checkpoint intermedi")
    parser.add_argument("--keep_last_n", type=int, default=3, help="Mantiene gli ultimi N checkpoint")
    return parser.parse_args()

def is_old_file(file_path, days):
    """Verifica se un file è più vecchio di N giorni"""
    file_time = os.path.getmtime(file_path)
    now = time.time()
    return (now - file_time) > (days * 24 * 60 * 60)

def clean_logs(log_dir, archive_dir, days, compress_logs, dry_run):
    """Pulisce i file di log vecchi"""
    if not os.path.exists(log_dir):
        print(f"La directory {log_dir} non esiste.")
        return
    
    # Crea la directory di archiviazione se non esiste
    if compress_logs and not os.path.exists(archive_dir):
        if not dry_run:
            os.makedirs(archive_dir)
        print(f"Creata directory di archiviazione: {archive_dir}")
    
    # Ottieni la lista dei file di log
    log_files = [f for f in os.listdir(log_dir) if os.path.isfile(os.path.join(log_dir, f)) and (f.endswith(".out") or f.endswith(".err"))]
    
    # Filtra i file vecchi
    old_log_files = [f for f in log_files if is_old_file(os.path.join(log_dir, f), days)]
    
    if not old_log_files:
        print(f"Nessun file di log più vecchio di {days} giorni trovato.")
        return
    
    print(f"Trovati {len(old_log_files)} file di log più vecchi di {days} giorni.")
    
    if compress_logs:
        # Raggruppa i file per job ID
        job_files = {}
        for f in old_log_files:
            # Estrai il job ID dal nome del file
            job_id_match = re.search(r"_(\d+)\.(out|err)$", f)
            if job_id_match:
                job_id = job_id_match.group(1)
                if job_id not in job_files:
                    job_files[job_id] = []
                job_files[job_id].append(f)
        
        # Comprimi i file per ogni job ID
        for job_id, files in job_files.items():
            archive_name = f"job_{job_id}_logs.tar.gz"
            archive_path = os.path.join(archive_dir, archive_name)
            
            print(f"Compressione di {len(files)} file per il job {job_id} in {archive_name}")
            
            if not dry_run:
                # Crea un archivio temporaneo
                import tarfile
                with tarfile.open(archive_path, "w:gz") as tar:
                    for f in files:
                        file_path = os.path.join(log_dir, f)
                        tar.add(file_path, arcname=f)
                
                # Elimina i file originali
                for f in files:
                    os.remove(os.path.join(log_dir, f))
    else:
        # Elimina i file vecchi
        for f in old_log_files:
            file_path = os.path.join(log_dir, f)
            print(f"Eliminazione del file: {file_path}")
            
            if not dry_run:
                os.remove(file_path)

def clean_checkpoints(output_dir, keep_last_n, dry_run):
    """Pulisce i checkpoint intermedi"""
    if not os.path.exists(output_dir):
        print(f"La directory {output_dir} non esiste.")
        return
    
    # Ottieni la lista dei modelli
    model_dirs = [d for d in os.listdir(output_dir) if os.path.isdir(os.path.join(output_dir, d))]
    
    for model_dir in model_dirs:
        model_path = os.path.join(output_dir, model_dir)
        
        # Ottieni la lista dei checkpoint
        checkpoint_dirs = [d for d in os.listdir(model_path) if d.startswith("checkpoint-") and os.path.isdir(os.path.join(model_path, d))]
        
        if not checkpoint_dirs:
            continue
        
        # Ordina i checkpoint per numero
        checkpoint_dirs.sort(key=lambda x: int(x.split("-")[1]))
        
        # Mantieni solo gli ultimi N checkpoint
        if len(checkpoint_dirs) > keep_last_n:
            checkpoints_to_remove = checkpoint_dirs[:-keep_last_n]
            
            print(f"Modello: {model_dir}")
            print(f"Trovati {len(checkpoint_dirs)} checkpoint, mantenendo gli ultimi {keep_last_n}.")
            print(f"Checkpoint da rimuovere: {len(checkpoints_to_remove)}")
            
            for checkpoint in checkpoints_to_remove:
                checkpoint_path = os.path.join(model_path, checkpoint)
                size_mb = sum(os.path.getsize(os.path.join(checkpoint_path, f)) 
                             for f in os.listdir(checkpoint_path) 
                             if os.path.isfile(os.path.join(checkpoint_path, f))) / (1024 * 1024)
                
                print(f"Eliminazione del checkpoint: {checkpoint} ({size_mb:.2f} MB)")
                
                if not dry_run:
                    shutil.rmtree(checkpoint_path)

def clean_output_files(output_dir, days, dry_run):
    """Pulisce i file di output obsoleti"""
    if not os.path.exists(output_dir):
        print(f"La directory {output_dir} non esiste.")
        return
    
    # Ottieni la lista dei file di output
    output_files = [f for f in os.listdir(output_dir) if os.path.isfile(os.path.join(output_dir, f)) and f.startswith("slurm_")]
    
    # Filtra i file vecchi
    old_output_files = [f for f in output_files if is_old_file(os.path.join(output_dir, f), days)]
    
    if not old_output_files:
        print(f"Nessun file di output più vecchio di {days} giorni trovato.")
        return
    
    print(f"Trovati {len(old_output_files)} file di output più vecchi di {days} giorni.")
    
    # Elimina i file vecchi
    for f in old_output_files:
        file_path = os.path.join(output_dir, f)
        print(f"Eliminazione del file: {file_path}")
        
        if not dry_run:
            os.remove(file_path)

def main():
    args = parse_args()
    
    print("=== Pulizia dei File Temporanei e Obsoleti ===")
    print(f"Modalità: {'Simulazione' if args.dry_run else 'Esecuzione'}")
    print(f"Data: {datetime.datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Pulisci i file di log
    print("=== Pulizia dei File di Log ===")
    clean_logs(args.log_dir, args.archive_dir, args.days, args.compress_logs, args.dry_run)
    print()
    
    # Pulisci i checkpoint
    if args.clean_checkpoints:
        print("=== Pulizia dei Checkpoint ===")
        clean_checkpoints(args.output_dir, args.keep_last_n, args.dry_run)
        print()
    
    # Pulisci i file di output
    print("=== Pulizia dei File di Output ===")
    clean_output_files(args.output_dir, args.days, args.dry_run)
    print()
    
    print("=== Pulizia Completata ===")
    if args.dry_run:
        print("Nota: Questa è stata una simulazione. Nessun file è stato eliminato.")
        print("Per eseguire la pulizia effettiva, rimuovi l'opzione --dry_run.")

if __name__ == "__main__":
    main()
