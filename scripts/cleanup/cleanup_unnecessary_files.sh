#!/bin/bash

# Script per rimuovere file non necessari mantenendo quelli essenziali
echo "Inizializzazione pulizia file non necessari..."

# Elimina checkpoint intermedi mantenendo solo quelli importanti
echo "Eliminazione checkpoint intermedi non necessari..."

# Lista dei checkpoint da mantenere (finali, migliori e quelli con convergenza)
KEEP_CHECKPOINTS=(
  "checkpoint-best"
  "checkpoint-final"
  "checkpoint-525"   # Checkpoint finali per test1
  "checkpoint-1800"  # Checkpoint finale per llama31_8b_lora_xml_custom_token
  "checkpoint-2400"  # Checkpoint finale per gemma2_9b_it_lora_xml_custom_token
  "checkpoint-3000"  # Checkpoint finale per gemma2_9b_it_lora_xml_no_token_convergence
  "checkpoint-4000"  # Checkpoint finale per llama31_8b_lora_xml_no_token_convergence
)

# Costruisci il pattern grep per escludere i checkpoint da mantenere
GREP_PATTERN=""
for checkpoint in "${KEEP_CHECKPOINTS[@]}"; do
  GREP_PATTERN="$GREP_PATTERN | grep -v \"$checkpoint\""
done

# Trova e rimuovi i checkpoint non necessari
echo "Cercando checkpoint non necessari..."
CHECKPOINTS_TO_REMOVE=$(find /work/tesi_ediluzio/experiments/xml_direct_input/outputs -type d -name "checkpoint-*" | grep -v "checkpoint-best" | grep -v "checkpoint-final" | grep -v "checkpoint-525" | grep -v "checkpoint-1800" | grep -v "checkpoint-2400" | grep -v "checkpoint-3000" | grep -v "checkpoint-4000")

# Mostra i checkpoint che verranno rimossi
echo "I seguenti checkpoint verranno rimossi:"
for checkpoint in $CHECKPOINTS_TO_REMOVE; do
  echo "  - $checkpoint"
done

# Chiedi conferma prima di procedere
read -p "Sei sicuro di voler eliminare questi checkpoint? (s/n): " confirm
if [[ $confirm != "s" ]]; then
  echo "Operazione annullata."
  exit 1
fi

# Rimuovi i checkpoint
for checkpoint in $CHECKPOINTS_TO_REMOVE; do
  echo "Rimozione $checkpoint..."
  rm -rf "$checkpoint"
done

# Elimina file __pycache__ che non sono necessari
echo "Eliminazione file __pycache__..."
find /work/tesi_ediluzio -type d -name "__pycache__" | xargs rm -rf

echo "Pulizia completata."
echo "Spazio liberato:"
du -sh /work/tesi_ediluzio/experiments/xml_direct_input/outputs
