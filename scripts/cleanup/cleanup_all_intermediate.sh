#!/bin/bash

# Script per liberare memoria rimuovendo pesi intermedi e file inutili

echo "Pulizia della memoria in corso..."

# Rimuovi i checkpoint intermedi (mantieni solo l'ultimo)
echo "Rimozione dei checkpoint intermedi..."
find /work/tesi_ediluzio -path "*/checkpoint-*" -type d | grep -v "final\|best\|last" | xargs rm -rf

# Rimuovi i file di cache
echo "Rimozione dei file di cache..."
find /work/tesi_ediluzio -name ".cache" -type d | xargs rm -rf
find /work/tesi_ediluzio -name "__pycache__" -type d | xargs rm -rf
find /work/tesi_ediluzio -name "*.pyc" -type f | xargs rm -f

# Rimuovi i file temporanei
echo "Rimozione dei file temporanei..."
find /work/tesi_ediluzio -name "temp_*" -type f | xargs rm -f
find /work/tesi_ediluzio -name "*.tmp" -type f | xargs rm -f

# Rimuovi i file di log vecchi
echo "Rimozione dei file di log vecchi..."
find /work/tesi_ediluzio/logs -name "*.err" -type f -mtime +7 | xargs rm -f
find /work/tesi_ediluzio/logs -name "*.out" -type f -mtime +7 | xargs rm -f

# Rimuovi i file di wandb non necessari
echo "Rimozione dei file wandb non necessari..."
find /work/tesi_ediluzio/wandb -name "*.h5" -type f | xargs rm -f
find /work/tesi_ediluzio/wandb -path "*/media/images" -type d | xargs rm -rf

# Rimuovi i file di output intermedi
echo "Rimozione dei file di output intermedi..."
find /work/tesi_ediluzio -name "output_*.txt" -type f | xargs rm -f

echo "Pulizia completata!"
echo "Spazio disco disponibile:"
df -h /work
