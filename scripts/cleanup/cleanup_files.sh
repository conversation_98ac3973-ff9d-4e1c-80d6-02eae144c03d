#!/bin/bash

echo "=== Pulizia dei file inutili ==="
echo "Data: $(date)"
echo

# Directory di base
BASE_DIR="/work/tesi_ediluzio"
EXPERIMENTS_DIR="$BASE_DIR/experiments/xml_direct_input"
OUTPUTS_DIR="$EXPERIMENTS_DIR/outputs"

# 1. Script di training obsoleti
echo "=== Eliminazione degli script di training obsoleti ==="
OBSOLETE_TRAINING_SCRIPTS=(
  "$EXPERIMENTS_DIR/train_lora_8bit_12h.slurm"
  "$EXPERIMENTS_DIR/train_lora_8bit_auth.slurm"
  "$EXPERIMENTS_DIR/train_lora_8bit_quick.slurm"
  "$EXPERIMENTS_DIR/train_lora_8bit.slurm"
  "$EXPERIMENTS_DIR/train_lora_8bit_test_10min.slurm"
  "$EXPERIMENTS_DIR/train_lora_8bit_test_20min.slurm"
)

for script in "${OBSOLETE_TRAINING_SCRIPTS[@]}"; do
  if [ -f "$script" ]; then
    echo "Eliminazione: $script"
    rm "$script"
  else
    echo "File non trovato: $script"
  fi
done
echo

# 2. Script di inferenza obsoleti
echo "=== Eliminazione degli script di inferenza obsoleti ==="
OBSOLETE_INFERENCE_SCRIPTS=(
  "$EXPERIMENTS_DIR/run_inference_auth_quick2.slurm"
  "$EXPERIMENTS_DIR/run_inference_auth_quick.slurm"
  "$EXPERIMENTS_DIR/run_inference_auth.slurm"
  "$EXPERIMENTS_DIR/run_lora_inference_quick2.slurm"
  "$EXPERIMENTS_DIR/run_lora_inference_quick.slurm"
  "$EXPERIMENTS_DIR/run_lora_inference.slurm"
  "$EXPERIMENTS_DIR/run_inference_xml_quick.slurm"
  "$EXPERIMENTS_DIR/run_lora_inference_xml_quick.slurm"
)

for script in "${OBSOLETE_INFERENCE_SCRIPTS[@]}"; do
  if [ -f "$script" ]; then
    echo "Eliminazione: $script"
    rm "$script"
  else
    echo "File non trovato: $script"
  fi
done
echo

# 3. File di output obsoleti
echo "=== Eliminazione dei file di output obsoleti ==="
OBSOLETE_OUTPUT_FILES=(
  "$OUTPUTS_DIR/slurm_infer_base_venv_fix_2551402.err"
  "$OUTPUTS_DIR/slurm_train_xml_2551295.err"
  "$OUTPUTS_DIR/slurm_infer_base_venv_fix_2551402.out"
  "$OUTPUTS_DIR/slurm_train_xml_2551295.out"
  "$OUTPUTS_DIR/slurm_train_lora_2551281.err"
  "$OUTPUTS_DIR/slurm_infer_base_fix_2551368.out"
  "$OUTPUTS_DIR/slurm_train_lora_fix_2551366.out"
  "$OUTPUTS_DIR/slurm_train_lora_fix_2551365.out"
  "$OUTPUTS_DIR/slurm_train_lora_2551281.out"
  "$OUTPUTS_DIR/slurm_infer_base_fix_2551368.err"
  "$OUTPUTS_DIR/slurm_train_lora_fix_2551366.err"
  "$OUTPUTS_DIR/slurm_train_lora_fix_2551365.err"
)

for file in "${OBSOLETE_OUTPUT_FILES[@]}"; do
  if [ -f "$file" ]; then
    echo "Eliminazione: $file"
    rm "$file"
  else
    echo "File non trovato: $file"
  fi
done
echo

# 4. Dataset non utilizzati
echo "=== Eliminazione dei dataset non utilizzati ==="
OBSOLETE_DATASETS=(
  "$BASE_DIR/data/processed/filtered_svg_1_3_paths_2k.json"
  "$BASE_DIR/data/processed/ki_dcount_max10_2k.json"
  "$BASE_DIR/data/processed/test_set.json"
  "$BASE_DIR/data/processed/train_set.json"
)

for dataset in "${OBSOLETE_DATASETS[@]}"; do
  if [ -f "$dataset" ]; then
    echo "Eliminazione: $dataset"
    rm "$dataset"
  else
    echo "File non trovato: $dataset"
  fi
done
echo

echo "=== Pulizia completata ==="
