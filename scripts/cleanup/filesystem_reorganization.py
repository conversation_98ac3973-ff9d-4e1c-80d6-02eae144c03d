#!/usr/bin/env python3
"""
Script di riorganizzazione filesystem per il progetto tesi_ediluzio
Mantiene tutte le dipendenze e crea una struttura più organizzata
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime

def get_size_gb(path):
    """Calcola la dimensione di un file o directory in GB"""
    if os.path.isfile(path):
        return os.path.getsize(path) / (1024 * 1024 * 1024)
    elif os.path.isdir(path):
        total = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    pass
        return total / (1024 * 1024 * 1024)
    return 0

def analyze_filesystem():
    """Analizza il filesystem attuale"""
    
    print("🔍 ANALISI FILESYSTEM TESI_EDILUZIO")
    print("=" * 50)
    
    base_dir = Path("/work/tesi_ediluzio")
    os.chdir(base_dir)
    
    # Categorizzazione directory
    categories = {
        "CORE_PROJECT": ["data", "experiments", "scripts", "shared", "clip_module"],
        "DOCUMENTATION": ["docs", "memory.md", "README.md", "CLEANUP_REPORT.md", "DEPENDENCY_MATRIX.md"],
        "RESULTS_ANALYSIS": ["results", "evaluation", "analysis", "reports", "examples"],
        "CACHE_TEMP": ["cache", ".cache", "chunks", "wandb", "logs"],
        "ENVIRONMENT": ["svg_env"],
        "DEVELOPMENT": [".git", ".idea", ".vscode", ".vscode-server"],
        "MISC": ["utils", "data_preparation", "documentation", "models"]
    }
    
    total_size = 0
    category_sizes = {}
    
    for category, dirs in categories.items():
        category_size = 0
        print(f"\n📂 {category}:")
        
        for dirname in dirs:
            path = base_dir / dirname
            if path.exists():
                size_gb = get_size_gb(path)
                category_size += size_gb
                total_size += size_gb
                
                if size_gb > 0.1:  # Solo se > 100MB
                    print(f"   {dirname:<20} {size_gb:>8.2f} GB")
                else:
                    size_mb = size_gb * 1024
                    print(f"   {dirname:<20} {size_mb:>8.1f} MB")
        
        category_sizes[category] = category_size
        print(f"   {'TOTALE ' + category:<20} {category_size:>8.2f} GB")
    
    print(f"\n💾 TOTALE FILESYSTEM: {total_size:.2f} GB")
    
    return category_sizes

def propose_reorganization():
    """Propone una riorganizzazione del filesystem"""
    
    print("\n🏗️ PROPOSTA RIORGANIZZAZIONE")
    print("=" * 50)
    
    # Struttura proposta
    new_structure = {
        "core/": {
            "description": "Codice e configurazioni principali",
            "contents": ["scripts/", "shared/", "clip_module/", "configs/"],
            "action": "move"
        },
        "data/": {
            "description": "Dataset e dati processati (mantieni)",
            "contents": ["processed/", "raw/", "test_samples/"],
            "action": "keep"
        },
        "experiments/": {
            "description": "Checkpoint e output training (mantieni)",
            "contents": ["xml_direct_input/", "baseline_*/"],
            "action": "keep"
        },
        "results/": {
            "description": "Risultati, analisi e report consolidati",
            "contents": ["evaluation/", "analysis/", "reports/", "examples/"],
            "action": "consolidate"
        },
        "docs/": {
            "description": "Documentazione completa",
            "contents": ["*.md", "docs/", "README.md"],
            "action": "consolidate"
        },
        "cache/": {
            "description": "Cache e file temporanei (pulizia)",
            "contents": ["huggingface/", "wandb/", "logs/"],
            "action": "cleanup"
        },
        "env/": {
            "description": "Ambiente Python (mantieni)",
            "contents": ["svg_env/"],
            "action": "rename"
        }
    }
    
    for dirname, info in new_structure.items():
        print(f"\n📁 {dirname}")
        print(f"   📝 {info['description']}")
        print(f"   📋 Contenuti: {', '.join(info['contents'])}")
        print(f"   🔧 Azione: {info['action']}")
    
    return new_structure

def cleanup_cache():
    """Pulisce le cache eccessive mantenendo solo l'essenziale"""
    
    print("\n🧹 PULIZIA CACHE HUGGINGFACE")
    print("=" * 50)
    
    base_dir = Path("/work/tesi_ediluzio")
    cache_dir = base_dir / ".cache" / "huggingface"
    
    if not cache_dir.exists():
        print("❌ Directory cache non trovata")
        return 0
    
    # Analizza cache hub (modelli)
    hub_dir = cache_dir / "hub"
    if hub_dir.exists():
        hub_size = get_size_gb(hub_dir)
        print(f"📊 Cache hub (modelli): {hub_size:.2f} GB")
        
        # Lista modelli in cache
        model_dirs = [d for d in hub_dir.iterdir() if d.is_dir()]
        print(f"📋 Modelli in cache: {len(model_dirs)}")
        
        # Modelli essenziali da mantenere
        essential_models = [
            "meta-llama",
            "google--gemma",
            "openai--clip",
            "microsoft--git",
            "salesforce--blip"
        ]
        
        space_freed = 0
        for model_dir in model_dirs:
            model_name = model_dir.name
            is_essential = any(essential in model_name for essential in essential_models)
            
            if not is_essential:
                size_gb = get_size_gb(model_dir)
                if size_gb > 0.5:  # Solo modelli > 500MB
                    print(f"🗑️  Candidato rimozione: {model_name} ({size_gb:.2f} GB)")
                    space_freed += size_gb
        
        print(f"💾 Spazio liberabile: {space_freed:.2f} GB")
    
    # Analizza cache datasets
    datasets_dir = cache_dir / "datasets"
    if datasets_dir.exists():
        datasets_size = get_size_gb(datasets_dir)
        print(f"📊 Cache datasets: {datasets_size:.2f} GB")
        print("🗑️  Cache datasets può essere rimossa completamente")
    
    return space_freed

def create_reorganization_script():
    """Crea script di riorganizzazione sicuro"""
    
    script_content = '''#!/bin/bash
# Script di riorganizzazione filesystem tesi_ediluzio
# ATTENZIONE: Eseguire solo dopo backup!

echo "🏗️ RIORGANIZZAZIONE FILESYSTEM TESI_EDILUZIO"
echo "=============================================="

# Vai nella directory base
cd /work/tesi_ediluzio

# Crea backup delle configurazioni critiche
echo "💾 Backup configurazioni critiche..."
mkdir -p backup_configs
cp memory.md backup_configs/
cp README.md backup_configs/
cp -r experiments/xml_direct_input/configs backup_configs/

# 1. CONSOLIDAMENTO DOCUMENTAZIONE
echo "📚 Consolidamento documentazione..."
mkdir -p docs_consolidated
mv docs/* docs_consolidated/ 2>/dev/null
mv *.md docs_consolidated/ 2>/dev/null
mv documentation/* docs_consolidated/ 2>/dev/null

# 2. CONSOLIDAMENTO RISULTATI
echo "📊 Consolidamento risultati..."
mkdir -p results_consolidated
mv results/* results_consolidated/ 2>/dev/null
mv evaluation/* results_consolidated/ 2>/dev/null
mv analysis/* results_consolidated/ 2>/dev/null
mv reports/* results_consolidated/ 2>/dev/null
mv examples/* results_consolidated/ 2>/dev/null

# 3. PULIZIA CACHE (OPZIONALE - COMMENTATO)
# echo "🧹 Pulizia cache..."
# rm -rf .cache/huggingface/datasets
# find .cache/huggingface/hub -name "*" -size +1G -type d | head -10

# 4. ORGANIZZAZIONE CORE
echo "🔧 Organizzazione core..."
mkdir -p core
mv shared core/ 2>/dev/null
mv clip_module core/ 2>/dev/null
mv utils core/ 2>/dev/null

echo "✅ Riorganizzazione completata!"
echo "📋 Verifica le nuove directory:"
echo "   - docs_consolidated/"
echo "   - results_consolidated/"
echo "   - core/"
echo "   - backup_configs/"
'''
    
    with open("reorganize_filesystem.sh", "w") as f:
        f.write(script_content)
    
    os.chmod("reorganize_filesystem.sh", 0o755)
    print("📝 Script di riorganizzazione creato: reorganize_filesystem.sh")

def main():
    """Funzione principale"""
    
    # Analisi filesystem
    category_sizes = analyze_filesystem()
    
    # Proposta riorganizzazione
    new_structure = propose_reorganization()
    
    # Analisi cache
    space_freed = cleanup_cache()
    
    # Crea script di riorganizzazione
    create_reorganization_script()
    
    # Report finale
    print("\n📋 RACCOMANDAZIONI FINALI")
    print("=" * 50)
    print("1. 🚨 CRITICO: .cache/huggingface (70GB) - Pulire cache non essenziali")
    print("2. 🗑️ chunks/ (2.3GB) - Richiedere rimozione admin")
    print("3. 📁 Consolidare risultati e documentazione")
    print("4. 🔧 Spostare utility in directory core/")
    print(f"5. 💾 Spazio liberabile stimato: {space_freed + 2.3 + 30:.1f} GB")
    print("\n⚠️  IMPORTANTE: Fare backup prima di eseguire reorganize_filesystem.sh")

if __name__ == "__main__":
    main()
