#!/bin/bash

# Script per riavviare i job di training con configurazioni stabili

# Pulisci la cache CUDA
echo "Pulizia della cache CUDA..."
nvidia-smi

# Rimuovi le directory di output esistenti per iniziare da zero
echo "Rimozione delle directory di output esistenti..."
rm -rf /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test2_multi_gpu
rm -rf /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test2_multi_gpu

# Crea le directory di output
echo "Creazione delle directory di output..."
mkdir -p /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test2_multi_gpu
mkdir -p /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test2_multi_gpu

# Riavvia i job con le nuove configurazioni
echo "Riavvio dei job con le nuove configurazioni stabili..."
cd /work/tesi_ediluzio
LLAMA_JOB_ID=$(sbatch --parsable scripts/slurm/run_llama_test2_multi_gpu.slurm)
GEMMA_JOB_ID=$(sbatch --parsable scripts/slurm/run_gemma_test2_multi_gpu.slurm)

echo "Job Llama riavviato con ID: $LLAMA_JOB_ID"
echo "Job Gemma riavviato con ID: $GEMMA_JOB_ID"

echo "Operazione completata"
