<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Qualitativo - Generazione Didascalie SVG</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            margin-top: 40px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        .model-section {
            margin-bottom: 50px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .example {
            margin: 20px 0;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .caption {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .true-caption {
            color: #28a745;
            font-weight: bold;
        }
        .generated-caption {
            color: #007bff;
        }
        .analysis {
            margin-top: 15px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .strengths, .weaknesses {
            margin-top: 20px;
        }
        .strengths h3, .weaknesses h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .strengths ul, .weaknesses ul {
            list-style-type: none;
            padding-left: 0;
        }
        .strengths li, .weaknesses li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .strengths li:before {
            content: "✓";
            color: #28a745;
            position: absolute;
            left: 0;
        }
        .weaknesses li:before {
            content: "✗";
            color: #dc3545;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Report Qualitativo - Generazione Didascalie SVG</h1>
        
        <div class="model-section">
            <h2>Google-Gemma-2-9B-IT</h2>
            
            <div class="example">
                <h3>Esempio 1: Testo Markdown</h3>
                <div class="caption">
                    <p class="true-caption">Didascalia Vera: The image depicts this is a markdown text</p>
                    <p class="generated-caption">Didascalia Generata: Quattro linee verticali nere spesse che rappresentano probabilmente delle colonne o pilastri.</p>
                </div>
                <div class="analysis">
                    <p>Il modello ha completamente frainteso la natura del contenuto, interpretando il testo markdown come linee verticali. Non ha riconosciuto la presenza di testo o la sua formattazione.</p>
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2: Cerchio con Punto</h3>
                <div class="caption">
                    <p class="true-caption">Didascalia Vera: The image depicts a black circle with a white dot in the center</p>
                    <p class="generated-caption">Didascalia Generata: Un cerchio pieno con un quadrato al centro, realizzato con una curva di Bézier.</p>
                </div>
                <div class="analysis">
                    <p>Il modello ha identificato correttamente la presenza di un cerchio, ma ha confuso il punto centrale con un quadrato. Inoltre, ha aggiunto dettagli tecnici non pertinenti (curva di Bézier).</p>
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3: Icona Wi-Fi</h3>
                <div class="caption">
                    <p class="true-caption">Didascalia Vera: The image depicts a simple, minimalist icon that represents a Wi-Fi signal</p>
                    <p class="generated-caption">Didascalia Generata: Il codice SVG rappresenta una forma geometrica composta da quattro curve e linee. Le linee e le curve formano un'immagine stilizzata che potrebbe essere interpretata come un fiore o un'altra forma organica.</p>
                </div>
                <div class="analysis">
                    <p>Il modello ha identificato correttamente la presenza di linee e curve, ma non ha riconosciuto il simbolo Wi-Fi. Ha invece interpretato l'immagine come una forma organica, mostrando una comprensione limitata dei simboli comuni.</p>
                </div>
            </div>

            <div class="strengths">
                <h3>Punti di Forza</h3>
                <ul>
                    <li>Buona capacità di identificare forme geometriche di base</li>
                    <li>Descrizioni dettagliate degli elementi visivi</li>
                    <li>Uso appropriato della terminologia tecnica</li>
                </ul>
            </div>

            <div class="weaknesses">
                <h3>Punti di Debolezza</h3>
                <ul>
                    <li>Difficoltà nel riconoscere il testo e la formattazione</li>
                    <li>Confusione tra forme geometriche simili</li>
                    <li>Tendenza a sovrainterpretare i dettagli tecnici</li>
                    <li>Mancata comprensione dei simboli comuni</li>
                </ul>
            </div>
        </div>

        <div class="model-section">
            <h2>Meta-Llama-3.1-8B-Instruct</h2>
            
            <div class="example">
                <h3>Esempio 1: Espressione Matematica</h3>
                <div class="caption">
                    <p class="true-caption">Didascalia Vera: The image depicts the image contains a simple mathematical expression</p>
                    <p class="generated-caption">Didascalia Generata: La didascalia per il codice SVG potrebbe essere la seguente: "Disegno composto da cinque curve ellittiche sovrapposte, con punti di controllo definiti da diverse linee rette."</p>
                </div>
                <div class="analysis">
                    <p>Il modello ha completamente frainteso la natura dell'immagine, descrivendo curve ellittiche che non esistono. Non ha riconosciuto la presenza di un'espressione matematica.</p>
                </div>
            </div>

            <div class="example">
                <h3>Esempio 2: Cursore del Mouse</h3>
                <div class="caption">
                    <p class="true-caption">Didascalia Vera: The image depicts a simple, minimalist design of a mouse cursor</p>
                    <p class="generated-caption">Didascalia Generata: "Perimetri di una regione urbana con strade e edifici"</p>
                </div>
                <div class="analysis">
                    <p>Il modello ha fornito una descrizione completamente fuori contesto, interpretando un cursore del mouse come una mappa urbana. Questo indica una grave mancanza di comprensione del contesto.</p>
                </div>
            </div>

            <div class="example">
                <h3>Esempio 3: Simbolo Geometrico</h3>
                <div class="caption">
                    <p class="true-caption">Didascalia Vera: The image depicts a geometric symbol that consists of a triangle with a cross at each of its vertices</p>
                    <p class="generated-caption">Didascalia Generata: "Disegno geometrico composto da otto segmenti rettilinei che si intersecano e si sovrappongono, creando una composizione complessa e astratta."</p>
                </div>
                <div class="analysis">
                    <p>Il modello ha fornito una descrizione vaga e generica, non cogliendo la struttura specifica del simbolo (triangolo con croci ai vertici). Ha invece descritto una composizione astratta.</p>
                </div>
            </div>

            <div class="strengths">
                <h3>Punti di Forza</h3>
                <ul>
                    <li>Descrizioni dettagliate delle forme geometriche</li>
                    <li>Uso appropriato della terminologia tecnica</li>
                    <li>Capacità di fornire descrizioni strutturate</li>
                </ul>
            </div>

            <div class="weaknesses">
                <h3>Punti di Debolezza</h3>
                <ul>
                    <li>Grave difficoltà nel riconoscere il contesto</li>
                    <li>Tendenza a fornire descrizioni completamente errate</li>
                    <li>Mancata comprensione degli elementi di interfaccia utente</li>
                    <li>Descrizioni troppo generiche e astratte</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html> 