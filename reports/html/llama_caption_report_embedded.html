<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Llama 3.1 8B - Report di Captioning SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .metrics {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
        }
        .metrics-table th, .metrics-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
        .example {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            background-color: #fff;
        }
        .example-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
        }
        .svg-container {
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .svg-container svg {
            max-width: 100%;
            max-height: 200px;
        }
        .caption-container {
            display: flex;
            flex-direction: column;
        }
        .caption-box {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 5px;
        }
        .ground-truth {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
        }
        .generated {
            background-color: #f8f5e8;
            border-left: 4px solid #f39c12;
        }
        .complexity-section {
            margin-top: 40px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        .metrics-image {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Llama 3.1 8B - Report di Captioning SVG</h1>
        
        <div class="metrics">
            <h2>Metriche di Valutazione</h2>
            <p>Il modello Llama 3.1 8B fine-tuned ha ottenuto i seguenti risultati nella generazione di caption per immagini SVG:</p>
            
            <table class="metrics-table">
                <tr>
                    <th>Metrica</th>
                    <th>Valore</th>
                    <th>Descrizione</th>
                </tr>
                <tr>
                    <td>BLEU-1</td>
                    <td>0.4644</td>
                    <td>Precisione delle singole parole</td>
                </tr>
                <tr>
                    <td>BLEU-2</td>
                    <td>0.3307</td>
                    <td>Precisione delle coppie di parole consecutive</td>
                </tr>
                <tr>
                    <td>BLEU-3</td>
                    <td>0.2455</td>
                    <td>Precisione delle triple di parole consecutive</td>
                </tr>
                <tr>
                    <td>BLEU-4</td>
                    <td>0.1652</td>
                    <td>Precisione delle quadruple di parole consecutive</td>
                </tr>
                <tr>
                    <td>METEOR</td>
                    <td>0.4745</td>
                    <td>Valutazione che considera sinonimi e variazioni morfologiche</td>
                </tr>
                <tr>
                    <td>CIDEr</td>
                    <td>0.8078</td>
                    <td>Metrica specifica per la valutazione di caption di immagini</td>
                </tr>
            </table>
            
            <div style="text-align: center; margin-top: 20px;">
                <img src="/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/metrics_radar.png" alt="Radar Chart delle Metriche" class="metrics-image">
            </div>
        </div>
        
        <h2>Esempi di Caption Generate</h2>
        <p>Di seguito sono riportati alcuni esempi di caption generate dal modello Llama 3.1 8B, suddivisi per complessità dell'SVG.</p>

        <!-- Sezione SVG Semplici -->
        <div class="complexity-section">
            <h3>SVG Semplici</h3>
            <p>Esempi di SVG con forme geometriche di base e pochi elementi.</p>
        </div>

        <!-- Esempio 1 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:98,129,192;stroke:None;stroke-width:1;opacity:1" d=""M102,512 A154,154,0,1,0,410,512 A154,154,0,1,0,102,512Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple geometric shape, specifically a circle
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple geometric shape, specifically a circle, which is colored a solid dark blue
                    </div>
                </div>
            </div>
        </div>

        <!-- Esempio 2 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:0,0,0;stroke:0,128,0;stroke-width:100;opacity:1" d=""M512,0 L512,512Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a vertical bar chart with a single green bar
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a green vertical bar that extends from the top to the bottom of the image
                    </div>
                </div>
            </div>
        </div>

        <!-- Esempio 3 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:0,0,0;stroke:None;stroke-width:1;opacity:1" d=""M255,0 L5,250 L151,250 L151,510 L359,510 L359,250 L505,250 L255,0Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple geometric shape, specifically a black triangle pointing upwards
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple geometric shape, specifically a right triangle with a black border
                    </div>
                </div>
            </div>
        </div>

        <!-- Sezione SVG di Media Complessità -->
        <div class="complexity-section">
            <h3>SVG di Media Complessità</h3>
            <p>Esempi di SVG con forme più elaborate o combinazioni di elementi.</p>
        </div>

        <!-- Esempio 4 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:0,0,0;stroke:None;stroke-width:1;opacity:1" d=""M480,448 C480,466,466,480,448,480 L64,480 C46,480,32,466,32,448 L32,64 C32,46,46,32,64,32 L448,32 C466,32,480,46,480,64 L480,448 M448,0 L64,0 C29,0,0,29,0,64 L0,448 C0,483,29,512,64,512 L448,512 C483,512,512,483,512,448 L512,64 C512,29,483,0,448,0 L448,0 M268,181 C265,177,261,176,256,176 C252,176,247,177,244,181 L101,324 C95,330,95,341,101,347 C107,353,118,353,124,347 L256,214 L389,347 C395,353,405,353,411,347 C418,341,418,330,411,324 L268,181Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple, minimalist icon that appears to be a graphical representation of an upward arrow
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple, minimalistic icon that represents a file folder
                    </div>
                </div>
            </div>
        </div>

        <!-- Esempio 5 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:0,0,0;stroke:None;stroke-width:1;opacity:1" d=""M440,202 C438,198,434,196,430,196 L256,196 L256,11 C256,6,252,2,248,0 C243,-1,238,1,235,6 L72,311 C70,314,70,318,72,321 C74,325,78,327,82,327 L234,327 L234,501 C234,506,237,510,242,511 C243,512,244,512,245,512 C249,512,252,510,254,507 L439,213 C441,209,442,205,440,202 M256,463 L256,316 C256,310,251,305,245,305 L100,305 L234,54 L234,207 C234,213,239,218,245,218 L410,218 L256,463Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple, minimalist design of a lightning bolt
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple, minimalist arrow pointing to the left
                    </div>
                </div>
            </div>
        </div>

        <!-- Esempio 6 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:255,218,68;stroke:None;stroke-width:1;opacity:1" d=""M0,0 L512,0 L512,512 L0,512 L0,0 Z" />
<path style="fill:0,82,180;stroke:None;stroke-width:1;opacity:1" d=""M0,0 L147,0 L147,512 L0,512 L0,0 Z" />
<path style="fill:109,165,68;stroke:None;stroke-width:1;opacity:1" d=""M256,412 L223,337 L256,262 L289,337 L256,412 M298,312 L264,237 L298,162 L331,237 L298,312 M214,312 L181,237 L214,162 L248,237 L214,312 M365,0 L512,0 L512,512 L365,512 L365,0Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a flag with a simple yet distinctive design
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a flag with a horizontal layout
                    </div>
                </div>
            </div>
        </div>

        <!-- Sezione SVG Complessi -->
        <div class="complexity-section">
            <h3>SVG Complessi</h3>
            <p>Esempi di SVG con forme complesse, più elementi o design elaborati.</p>
        </div>

        <!-- Esempio 7 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:8,3,65;stroke:None;stroke-width:1;opacity:1" d=""M128,256 C128,185,184,128,253,128 C299,128,340,154,362,192 L320,192 L320,224 L416,224 L416,128 L384,128 L384,167 C356,124,308,96,253,96 C166,96,96,168,96,256 L128,256 Z" />
<path style="fill:8,3,65;stroke:None;stroke-width:1;opacity:1" d=""M384,256 C384,327,328,384,259,384 C213,384,172,358,150,320 L192,320 L192,288 L96,288 L96,384 L128,384 L128,345 C156,388,204,416,259,416 C346,416,416,344,416,256 L384,256Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple, iconic symbol of a circular arrow forming a loop
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple, bold, and geometric logo consisting of two intersecting arrows
                    </div>
                </div>
            </div>
        </div>

        <!-- Esempio 8 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:255,255,255;stroke:None;stroke-width:1;opacity:1" d=""M16,256 A240,240,0,1,0,496,256 A240,240,0,1,0,16,256 Z" />
<path style="fill:142,36,170;stroke:None;stroke-width:1;opacity:1" d=""M256,16 C123,16,16,123,16,256 C16,389,123,496,256,496 C389,496,496,389,496,256 C496,123,389,16,256,16 M368,372 L323,372 L323,215 C323,211,323,204,323,196 C323,188,323,182,323,177 L279,372 L232,372 L189,177 C189,182,189,188,189,196 C189,204,189,211,189,215 L189,372 L144,372 L144,140 L214,140 L257,322 L298,140 L368,140 L368,372Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple, yet bold and striking graphic design featuring a large, uppercase letter "M" in a vibrant purple background
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple geometric shape, specifically a circle, which is divided into two distinct sections
                    </div>
                </div>
            </div>
        </div>

        <!-- Esempio 9 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg">
<path style="fill:0,0,0;stroke:127,223,255;stroke-width:100;opacity:1" d=""M256,256 L256,512 Z" />
<path style="fill:none;stroke:128,160,128;stroke-width:50;opacity:1" d=""M128,512 C128,384,51,384,51,256 A205,205,0,0,1,461,256 C461,384,384,384,384,512 Z" />
<path style="fill:255,255,255;stroke:170,170,170;stroke-width:50;opacity:1" d=""M512,128 L256,128 A128,128,0,0,0,256,384 L512,384Z" />
</svg>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a stylized, abstract design featuring a combination of curved lines and shapes
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple geometric shape consisting of two main components: a vertical blue rectangle and a horizontal green rectangle
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 0.9em;">
            <p>Report generato il 5 maggio 2025</p>
        </div>
    </div>
</body>
</html>
