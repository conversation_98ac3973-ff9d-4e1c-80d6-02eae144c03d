<!DOCTYPE html>
<html>
<head>
    <title>Schema di Classificazione della Complessità SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .complexity-section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 5px;
        }
        .simple {
            background-color: #e8f5e9;
            border-left: 5px solid #4caf50;
        }
        .medium {
            background-color: #fff8e1;
            border-left: 5px solid #ffc107;
        }
        .complex {
            background-color: #ffebee;
            border-left: 5px solid #f44336;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .example-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        .example-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: white;
        }
        .example-svg {
            width: 100%;
            height: 200px;
            border: 1px solid #eee;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f9f9f9;
        }
        .example-description {
            font-size: 0.9em;
            color: #666;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 0;
        }
        .feature-list li {
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
        }
        .complexity-score {
            font-weight: bold;
            color: #333;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Schema di Classificazione della Complessità SVG</h1>
        <p>Questo schema illustra i criteri utilizzati per classificare gli SVG in tre fasce di complessità: Simple, Medium e Complex.</p>
        
        <h2>Criteri di Classificazione</h2>
        <p>La classificazione della complessità degli SVG si basa su diversi fattori che contribuiscono a un punteggio di complessità complessivo:</p>
        
        <table>
            <tr>
                <th>Fattore</th>
                <th>Descrizione</th>
                <th>Impatto sulla Complessità</th>
            </tr>
            <tr>
                <td>Numero di elementi</td>
                <td>Conteggio totale degli elementi SVG (path, rect, circle, ecc.)</td>
                <td>+1 per elemento</td>
            </tr>
            <tr>
                <td>Elementi di testo</td>
                <td>Presenza di elementi text o tspan</td>
                <td>+2 per elemento di testo</td>
            </tr>
            <tr>
                <td>Attributi</td>
                <td>Numero di attributi per elemento</td>
                <td>+1 per attributo</td>
            </tr>
            <tr>
                <td>Comandi path</td>
                <td>Numero di comandi nei path (M, L, C, Z, ecc.)</td>
                <td>+1 per comando</td>
            </tr>
            <tr>
                <td>Token complessi</td>
                <td>Presenza di token avanzati come linearGradient, radialGradient, filter, ecc.</td>
                <td>Incremento significativo della complessità</td>
            </tr>
            <tr>
                <td>Nidificazione</td>
                <td>Livelli di nidificazione degli elementi</td>
                <td>Incremento proporzionale alla profondità</td>
            </tr>
        </table>
        
        <h2>Fasce di Complessità</h2>
        
        <div class="complexity-section simple">
            <h3>Simple (Semplice)</h3>
            <p>Gli SVG classificati come "Simple" hanno una struttura minima e utilizzano forme geometriche di base.</p>
            
            <div class="feature-list">
                <li><strong>Punteggio di complessità:</strong> &lt; 50</li>
                <li><strong>Elementi:</strong> Pochi elementi (tipicamente &lt; 10)</li>
                <li><strong>Forme:</strong> Principalmente forme geometriche di base (cerchi, rettangoli, linee)</li>
                <li><strong>Path:</strong> Path semplici con pochi comandi</li>
                <li><strong>Colori:</strong> Utilizzo limitato di colori</li>
                <li><strong>Token complessi:</strong> Nessuno</li>
            </div>
            
            <div class="example-grid">
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <circle cx="256" cy="256" r="154" fill="#6281C0" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Cerchio blu semplice
                    </div>
                </div>
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <rect x="462" y="0" width="50" height="512" fill="#008000" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Barra verticale verde
                    </div>
                </div>
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <polygon points="256,0 5,250 151,250 151,510 359,510 359,250 505,250" fill="#000000" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Triangolo nero
                    </div>
                </div>
            </div>
        </div>
        
        <div class="complexity-section medium">
            <h3>Medium (Media)</h3>
            <p>Gli SVG classificati come "Medium" hanno una struttura più elaborata e utilizzano combinazioni di forme o path più complessi.</p>
            
            <div class="feature-list">
                <li><strong>Punteggio di complessità:</strong> 50-200</li>
                <li><strong>Elementi:</strong> Numero moderato di elementi (10-30)</li>
                <li><strong>Forme:</strong> Combinazioni di forme o forme più elaborate</li>
                <li><strong>Path:</strong> Path con più comandi e curve</li>
                <li><strong>Colori:</strong> Utilizzo moderato di colori e stili</li>
                <li><strong>Token complessi:</strong> Utilizzo limitato</li>
            </div>
            
            <div class="example-grid">
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <path d="M268,181 C265,177,261,176,256,176 C252,176,247,177,244,181 L101,324 C95,330,95,341,101,347 C107,353,118,353,124,347 L256,214 L389,347 C395,353,405,353,411,347 C418,341,418,330,411,324 L268,181Z" fill="#000000" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Freccia con path curvi
                    </div>
                </div>
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <path d="M440,202 C438,198,434,196,430,196 L256,196 L256,11 C256,6,252,2,248,0 C243,-1,238,1,235,6 L72,311 C70,314,70,318,72,321 C74,325,78,327,82,327 L234,327 L234,501 C234,506,237,510,242,511 C243,512,244,512,245,512 C249,512,252,510,254,507 L439,213 C441,209,442,205,440,202 M256,463 L256,316 C256,310,251,305,245,305 L100,305 L234,54 L234,207 C234,213,239,218,245,218 L410,218 L256,463Z" fill="#000000" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Fulmine con path complesso
                    </div>
                </div>
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <rect x="0" y="0" width="512" height="512" fill="#FFDA44" />
                            <rect x="0" y="0" width="147" height="512" fill="#0052B4" />
                            <path d="M256,412 L223,337 L256,262 L289,337 L256,412 M298,312 L264,237 L298,162 L331,237 L298,312 M214,312 L181,237 L214,162 L248,237 L214,312 M365,0 L512,0 L512,512 L365,512 L365,0Z" fill="#6DA544" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Bandiera con più elementi e colori
                    </div>
                </div>
            </div>
        </div>
        
        <div class="complexity-section complex">
            <h3>Complex (Complesso)</h3>
            <p>Gli SVG classificati come "Complex" hanno una struttura elaborata, utilizzano molti elementi, path complessi e spesso token avanzati.</p>
            
            <div class="feature-list">
                <li><strong>Punteggio di complessità:</strong> &gt; 200</li>
                <li><strong>Elementi:</strong> Molti elementi (tipicamente &gt; 30)</li>
                <li><strong>Forme:</strong> Forme complesse e combinazioni elaborate</li>
                <li><strong>Path:</strong> Path complessi con molti comandi e curve</li>
                <li><strong>Colori:</strong> Utilizzo esteso di colori, gradienti e stili</li>
                <li><strong>Token complessi:</strong> Utilizzo di token avanzati come linearGradient, radialGradient, filter, ecc.</li>
            </div>
            
            <div class="example-grid">
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <path d="M128,256 C128,185,184,128,253,128 C299,128,340,154,362,192 L320,192 L320,224 L416,224 L416,128 L384,128 L384,167 C356,124,308,96,253,96 C166,96,96,168,96,256 L128,256 Z" fill="#080341" />
                            <path d="M384,256 C384,327,328,384,259,384 C213,384,172,358,150,320 L192,320 L192,288 L96,288 L96,384 L128,384 L128,345 C156,388,204,416,259,416 C346,416,416,344,416,256 L384,256Z" fill="#080341" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Simbolo di riciclo con path complessi
                    </div>
                </div>
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <circle cx="256" cy="256" r="240" fill="#FFFFFF" />
                            <path d="M256,16 C123,16,16,123,16,256 C16,389,123,496,256,496 C389,496,496,389,496,256 C496,123,389,16,256,16 M368,372 L323,372 L323,215 C323,211,323,204,323,196 C323,188,323,182,323,177 L279,372 L232,372 L189,177 C189,182,189,188,189,196 C189,204,189,211,189,215 L189,372 L144,372 L144,140 L214,140 L257,322 L298,140 L368,140 L368,372Z" fill="#8E24AA" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Lettera M con path complesso
                    </div>
                </div>
                <div class="example-card">
                    <div class="example-svg">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="100%" height="100%">
                            <path d="M256,256 L256,512" stroke="#7FDFFF" stroke-width="100" fill="none" />
                            <path d="M128,512 C128,384,51,384,51,256 A205,205,0,0,1,461,256 C461,384,384,384,384,512" stroke="#80A080" stroke-width="50" fill="none" />
                            <path d="M512,128 L256,128 A128,128,0,0,0,256,384 L512,384" stroke="#AAAAAA" stroke-width="50" fill="#FFFFFF" />
                        </svg>
                    </div>
                    <div class="example-description">
                        Design astratto con curve e archi
                    </div>
                </div>
            </div>
        </div>
        
        <h2>Algoritmo di Classificazione</h2>
        <p>L'algoritmo di classificazione calcola un punteggio di complessità basato sui fattori sopra descritti e assegna la fascia di complessità in base a soglie predefinite:</p>
        
        <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto;">
# Calcolo del punteggio di complessità
complexity = element_count                           # Numero di elementi
complexity += len(text_elements) * 2                 # Elementi di testo
complexity += sum([len(element.attrib) for element]) # Attributi
complexity += path_count                             # Comandi path

# Incremento per token complessi
if any(token in svg for token in complex_tokens):
    complexity += 50

# Classificazione in base al punteggio
if complexity < 50:
    return "simple"
elif complexity < 200:
    return "medium"
else:
    return "complex"
        </pre>
        
        <h2>Impatto sulla Generazione di Caption</h2>
        <p>La classificazione della complessità degli SVG ha un impatto significativo sulla generazione di caption:</p>
        
        <ul>
            <li><strong>Simple:</strong> Le caption per SVG semplici tendono ad essere più brevi e dirette, focalizzandosi sulle forme geometriche di base.</li>
            <li><strong>Medium:</strong> Le caption per SVG di media complessità descrivono combinazioni di forme e caratteristiche più elaborate.</li>
            <li><strong>Complex:</strong> Le caption per SVG complessi richiedono una descrizione più dettagliata e articolata, spesso menzionando relazioni spaziali tra elementi e caratteristiche stilistiche.</li>
        </ul>
        
        <p>Questa classificazione permette di valutare le prestazioni dei modelli di captioning in base alla complessità degli SVG, fornendo insight sulla loro capacità di gestire input di diversa difficoltà.</p>
    </div>
</body>
</html>
