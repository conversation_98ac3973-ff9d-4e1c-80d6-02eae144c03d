<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Llama 3.1 8B - Report di Captioning SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .metrics {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
        }
        .metrics-table th, .metrics-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .metrics-table th {
            background-color: #f2f2f2;
        }
        .example {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            background-color: #fff;
        }
        .example-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
        }
        .svg-container {
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .svg-container svg {
            max-width: 100%;
            max-height: 200px;
        }
        .caption-container {
            display: flex;
            flex-direction: column;
        }
        .caption-box {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 5px;
        }
        .ground-truth {
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
        }
        .generated {
            background-color: #f8f5e8;
            border-left: 4px solid #f39c12;
        }
        .complexity-section {
            margin-top: 40px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        .metrics-image {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Llama 3.1 8B - Report di Captioning SVG</h1>
        
        <div class="metrics">
            <h2>Metriche di Valutazione</h2>
            <p>Il modello Llama 3.1 8B fine-tuned ha ottenuto i seguenti risultati nella generazione di caption per immagini SVG:</p>
            
            <table class="metrics-table">
                <tr>
                    <th>Metrica</th>
                    <th>Valore</th>
                    <th>Descrizione</th>
                </tr>
                <tr>
                    <td>BLEU-1</td>
                    <td>0.4644</td>
                    <td>Precisione delle singole parole</td>
                </tr>
                <tr>
                    <td>BLEU-2</td>
                    <td>0.3307</td>
                    <td>Precisione delle coppie di parole consecutive</td>
                </tr>
                <tr>
                    <td>BLEU-3</td>
                    <td>0.2455</td>
                    <td>Precisione delle triple di parole consecutive</td>
                </tr>
                <tr>
                    <td>BLEU-4</td>
                    <td>0.1652</td>
                    <td>Precisione delle quadruple di parole consecutive</td>
                </tr>
                <tr>
                    <td>METEOR</td>
                    <td>0.4745</td>
                    <td>Valutazione che considera sinonimi e variazioni morfologiche</td>
                </tr>
                <tr>
                    <td>CIDEr</td>
                    <td>0.8078</td>
                    <td>Metrica specifica per la valutazione di caption di immagini</td>
                </tr>
            </table>
            
            <div style="text-align: center; margin-top: 20px;">
                <img src="/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/metrics_radar.png" alt="Radar Chart delle Metriche" class="metrics-image">
            </div>
        </div>
        
        <h2>Esempi di Caption Generate</h2>
        <p>Di seguito sono riportati alcuni esempi di caption generate dal modello Llama 3.1 8B, suddivisi per complessità dell'SVG.</p>
        
        <!-- Sezione SVG Semplici -->
        <div class="complexity-section">
            <h3>SVG Semplici</h3>
            <p>Esempi di SVG con forme geometriche di base e pochi elementi.</p>
        </div>
        
        <!-- Esempio 1 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/simple_example_1.svg" type="image/svg+xml">
                        [SVG: Cerchio blu]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple geometric shape, specifically a circle
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple geometric shape, specifically a circle, which is colored a solid dark blue
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Esempio 2 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/simple_example_2.svg" type="image/svg+xml">
                        [SVG: Barra verticale verde]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a vertical bar chart with a single green bar
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a green vertical bar that extends from the top to the bottom of the image
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Esempio 3 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/simple_example_3.svg" type="image/svg+xml">
                        [SVG: Triangolo nero]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple geometric shape, specifically a black triangle pointing upwards
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple geometric shape, specifically a right triangle with a black border
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sezione SVG di Media Complessità -->
        <div class="complexity-section">
            <h3>SVG di Media Complessità</h3>
            <p>Esempi di SVG con forme più elaborate o combinazioni di elementi.</p>
        </div>
        
        <!-- Esempio 4 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/medium_example_1.svg" type="image/svg+xml">
                        [SVG: Freccia]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple, minimalist icon that appears to be a graphical representation of an upward arrow
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple, minimalistic icon that represents a file folder
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Esempio 5 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/medium_example_2.svg" type="image/svg+xml">
                        [SVG: Fulmine]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple, minimalist design of a lightning bolt
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple, minimalist arrow pointing to the left
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Esempio 6 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/medium_example_3.svg" type="image/svg+xml">
                        [SVG: Bandiera]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a flag with a simple yet distinctive design
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a flag with a horizontal layout
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sezione SVG Complessi -->
        <div class="complexity-section">
            <h3>SVG Complessi</h3>
            <p>Esempi di SVG con forme complesse, più elementi o design elaborati.</p>
        </div>
        
        <!-- Esempio 7 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/complex_example_1.svg" type="image/svg+xml">
                        [SVG: Simbolo di riciclo]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple, iconic symbol of a circular arrow forming a loop
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple, bold, and geometric logo consisting of two intersecting arrows
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Esempio 8 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/complex_example_2.svg" type="image/svg+xml">
                        [SVG: Lettera M]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a simple, yet bold and striking graphic design featuring a large, uppercase letter "M" in a vibrant purple background
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple geometric shape, specifically a circle, which is divided into two distinct sections
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Esempio 9 -->
        <div class="example">
            <div class="example-grid">
                <div class="svg-container">
                    <object data="/work/tesi_ediluzio/evaluation/reports/svg_images/complex_example_3.svg" type="image/svg+xml">
                        [SVG: Design astratto]
                    </object>
                </div>
                <div class="caption-container">
                    <div class="caption-box ground-truth">
                        <strong>Ground Truth:</strong> The image depicts a stylized, abstract design featuring a combination of curved lines and shapes
                    </div>
                    <div class="caption-box generated">
                        <strong>Generated:</strong> The image depicts a simple geometric shape consisting of two main components: a vertical blue rectangle and a horizontal green rectangle
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 40px; text-align: center; color: #7f8c8d; font-size: 0.9em;">
            <p>Report generato il 5 maggio 2025</p>
        </div>
    </div>
</body>
</html>
