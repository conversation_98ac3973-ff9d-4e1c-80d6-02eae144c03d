<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Qualitativo - <PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        .model-section {
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .example {
            margin: 15px 0;
            padding: 15px;
            background-color: #fff;
            border-left: 4px solid #3498db;
        }
        .svg-preview {
            width: 100px;
            height: 100px;
            margin: 10px 0;
            border: 1px solid #ddd;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .comparison-table th {
            background-color: #3498db;
            color: white;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .example-item {
            display: flex;
            align-items: center;
            gap: 20px;
            margin: 20px 0;
            padding: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .svg-container {
            flex-shrink: 0;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
        }
        .svg-container svg {
            max-width: 100%;
            max-height: 100%;
        }
        .example-item p {
            margin: 0;
            flex-grow: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Report Qualitativo - Analisi Modelli</h1>

        <h2>Modelli Analizzati</h2>
        <ul>
            <li>meta-llama/Llama-3.1-8B-Instruct</li>
            <li>google/gemma-2-9b-it</li>
            <li>mistralai/Mistral-7B-Instruct-v0.2</li>
            <li>microsoft/phi-3-mini-4k-instruct</li>
            <li>Qwen/Qwen2-7B-Instruct</li>
        </ul>

        <h2>Tipi di SVG Analizzati</h2>
        <div class="model-section">
            <h3>Elementi Base</h3>
            <div class="example">
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <rect x="10" y="10" width="80" height="80" fill="blue" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Rettangolo blu con bordo nero</p>
                </div>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="40" fill="red" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Cerchio rosso con bordo nero</p>
                </div>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <polygon points="50,10 90,90 10,90" fill="green" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Triangolo verde con bordo nero</p>
                </div>
            </div>

            <h3>Elementi Intermedi</h3>
            <div class="example">
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <rect x="10" y="10" width="80" height="80" fill="blue" stroke="black" stroke-width="2"/>
                            <circle cx="50" cy="50" r="20" fill="red" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Composizione di forme base: rettangolo blu con cerchio rosso al centro</p>
                </div>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <path d="M10,50 C30,20 70,80 90,50" fill="none" stroke="black" stroke-width="2"/>
                            <path d="M10,30 C30,10 70,90 90,30" fill="none" stroke="blue" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Curve Bezier multiple con colori diversi</p>
                </div>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <rect x="10" y="10" width="80" height="80" fill="none" stroke="black" stroke-width="2"/>
                            <line x1="10" y1="10" x2="90" y2="90" stroke="red" stroke-width="2"/>
                            <line x1="90" y1="10" x2="10" y2="90" stroke="red" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Rettangolo con croce diagonale</p>
                </div>
            </div>

            <h3>Elementi Complessi</h3>
            <div class="example">
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <defs>
                                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:blue;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:red;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <rect x="10" y="10" width="80" height="80" fill="url(#grad1)" stroke="black" stroke-width="2"/>
                            <circle cx="50" cy="50" r="30" fill="none" stroke="white" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Rettangolo con gradiente e cerchio sovrapposto</p>
                </div>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <defs>
                                <pattern id="dots" x="0" y="0" width="10" height="10" patternUnits="userSpaceOnUse">
                                    <circle cx="5" cy="5" r="2" fill="black"/>
                                </pattern>
                            </defs>
                            <rect x="10" y="10" width="80" height="80" fill="url(#dots)" stroke="black" stroke-width="2"/>
                            <path d="M20,50 Q50,20 80,50 T20,50" fill="none" stroke="red" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Rettangolo con pattern di punti e curva Bezier</p>
                </div>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <defs>
                                <filter id="blur">
                                    <feGaussianBlur in="SourceGraphic" stdDeviation="2"/>
                                </filter>
                            </defs>
                            <circle cx="30" cy="30" r="20" fill="blue" filter="url(#blur)"/>
                            <circle cx="70" cy="70" r="20" fill="red" filter="url(#blur)"/>
                            <path d="M30,30 L70,70" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>Cerchi sfocati con linea di connessione</p>
                </div>
            </div>
        </div>

        <h2>Analisi per Modello</h2>
        
        <div class="model-section">
            <h3>Llama (meta-llama/Llama-3.1-8B-Instruct)</h3>
            <p>Stile: Tecnico e dettagliato</p>
            <div class="example">
                <h4>Esempi di Descrizioni:</h4>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <rect x="10" y="10" width="80" height="80" fill="blue" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>"Questo codice SVG definisce un rettangolo di dimensioni 80x80px con bordo nero e riempimento blu"</p>
                </div>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <path d="M10,30 Q50,10 90,30" fill="none" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>"Questa immagine SVG contiene un elemento path con coordinate M10,30 che definisce una curva Bezier quadratica"</p>
                </div>
            </div>
        </div>

        <div class="model-section">
            <h3>Gemma (google/gemma-2-9b-it)</h3>
            <p>Stile: Descrittivo e naturale</p>
            <div class="example">
                <h4>Esempi di Descrizioni:</h4>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <rect x="10" y="10" width="80" height="80" fill="blue" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>"L'immagine mostra un quadrato blu con bordo nero su sfondo bianco"</p>
                </div>
                <div class="example-item">
                    <div class="svg-container">
                        <svg width="100" height="100" viewBox="0 0 100 100">
                            <path d="M10,50 C30,20 70,80 90,50" fill="none" stroke="black" stroke-width="2"/>
                        </svg>
                    </div>
                    <p>"L'immagine mostra una linea ondulata che forma una curva elegante, simile a un'onda"</p>
                </div>
            </div>
        </div>

        <h2>Confronto delle Prestazioni</h2>
        <table class="comparison-table">
            <tr>
                <th>Caratteristica</th>
                <th>Llama</th>
                <th>Gemma</th>
            </tr>
            <tr>
                <td>Precisione Tecnica</td>
                <td>Molto Alta</td>
                <td>Alta</td>
            </tr>
            <tr>
                <td>Naturalità del Linguaggio</td>
                <td>Media</td>
                <td>Molto Alta</td>
            </tr>
            <tr>
                <td>Dettaglio delle Descrizioni</td>
                <td>Molto Alto</td>
                <td>Alto</td>
            </tr>
            <tr>
                <td>Consistenza</td>
                <td>Alta</td>
                <td>Alta</td>
            </tr>
        </table>

        <h2>Conclusioni</h2>
        <div class="model-section">
            <h3>Punti di Forza</h3>
            <ul>
                <li>Descrizioni dettagliate delle proprietà SVG</li>
                <li>Accuratezza tecnica</li>
                <li>Varietà di stili descrittivi</li>
                <li>Capacità di cogliere aspetti estetici</li>
            </ul>

            <h3>Aree di Miglioramento</h3>
            <ul>
                <li>Alcune descrizioni potrebbero essere più concise</li>
                <li>Variazione nella profondità delle descrizioni tra i modelli</li>
                <li>Alcune ripetizioni di frasi comuni</li>
            </ul>
        </div>

        <h2>Raccomandazioni</h2>
        <div class="model-section">
            <ul>
                <li>Utilizzare Llama per descrizioni tecniche dettagliate</li>
                <li>Utilizzare Gemma per descrizioni più naturali e fluide</li>
                <li>Considerare l'uso combinato dei modelli per ottenere il meglio di entrambi gli approcci</li>
            </ul>
        </div>
    </div>
</body>
</html> 