# Roadmap del Progetto

## PARTE 1: ESPERIMENTI PRELIMINARI (Dataset "Plain" - Solo campo 'data')
* **Stato**: ✅ **COMPLETATA**
* **Nota**: Conclusa. Utile per apprendimento iniziale ma approccio dati insufficiente.

## PARTE 2: NUOVA STRATEGIA - XML, MODELLI INSTRUCT E FINE-TUNING INCREMENTALE
* **Nota**: Strategia attuale focalizzata su XML completo, modelli Instruct e fine-tuning mirato post-valutazione zero-shot.

### Passo 0: Preparazione Fondamentale e Dataset
* **0.A: Creazione Dataset XML Iniziale**: ✅ **FATTO**
    * Creato filtered_svg_1_3_paths_2k.json. Superato dai passi successivi.
* **0.B: Integrazione Tokenizer Custom**: ✅ **FATTO**
    * Codice build_tokenizer pronto e integrato in train_xml_slurm_token_xml.py.
    * Implementazione in shared/svg_core/custom_tokenizer_utils.py.
* **0.C: Gestione Resize Embeddings**: ✅ **FATTO**
    * Logica model.resize_token_embeddings implementata negli script di training.
    * Supporto per aggiunta di 23 token SVG-specifici al vocabolario del modello.
* **0.D: Rigenerazione Dataset Esteso e Split Train/Test**: ✅ **COMPLETATO**
    * **0.D.i: Modifica Script Creazione Dataset**: ✅ **FATTO**
        * create_filtered_xml_dataset.py aggiornato per lunghezza, B/N, source.
    * **0.D.ii: Esecuzione Creazione Dataset Esteso**: ✅ **FATTO**
        * Generato filtered_bw_len512_ALL.json - 231k campioni.
    * **0.D.iii: Split Train/Test Stratificato**: ✅ **FATTO**
        * Creati train_set_final.json [229k] e test_set_final_2k.json [2k].

### Passo 1: Esperimenti Iniziali con Nemotron 4B
* **Stato**: ⚠️ **IN CORSO - RIAVVIATO**
* **Nota**: Inizialmente fallito per errore FSDP. Riavviato con configurazione corretta.
* **1.A: Script Pronti**: ✅ **FATTO**
    * train_xml_slurm_token_xml.py, script SLURM base.
* **1.B: Baseline (Nemotron + Token Custom)**: ✅ **FATTO**
    * Risultati scarsi.
* **LORA-PREP (Nemotron 4B)**: ✅ **FATTO**
    * Script check_lora_targets.py usato inizialmente.
* **1.C: Fine-tuning LoRA (Nemotron 4B)**: 🔄 **IN CORSO**
    * Precedente tentativo: Fallito per errore FSDP.
    * Stato attuale: Job 2554151 in coda con configurazione corretta (rimossa impostazione FSDP).
    * Configurazione: nemotron_4b_instruct_lora_config_v1_correct_targets.json
* **1.D: Test LoRA (Nemotron 4B)**: ⏳ **IN ATTESA**
    * Dipende dal completamento di 1.C.

### Passo 2: Valutazione Zero-Shot Modelli Instruct
* **Stato**: ✅ **COMPLETATO**
* **Nota**: Utilizzato test_set_final_2k.json per la valutazione.
* **2.A: Selezione Modelli Instruct**: ✅ **FATTO**
    * Selezionati modelli finali: Llama-3.1-8B-Instruct, google/gemma-2-9b-it.
* **2.B: Definizione Prompt Zero-Shot**: ✅ **FATTO**
    * Template implementato in evaluate_zero_shot.py.
* **2.C: Esecuzione Inferenza Zero-Shot**: ✅ **COMPLETATO**
    * **Stato**: Inferenza completata per entrambi i modelli.
    * **Dettaglio job**:
        * Llama 3.1 8B Instruct: Job 2566420
        * Gemma 2 9B IT: Job 2566421
    * **Output generato**:
        * File di esempi: `/work/tesi_ediluzio/evaluation/zero_shot/Llama-3.1-8B-Instruct_zero_shot_examples.json`
        * File di esempi: `/work/tesi_ediluzio/evaluation/zero_shot/gemma-2-9b-it_zero_shot_examples.json`
* **2.D: Valutazione Zero-Shot**: ✅ **COMPLETATO**
    * **Metriche calcolate**: BLEU-1/2/3/4, METEOR, CIDEr
    * **Risultati principali**:
        * Llama 3.1 8B: BLEU-1=0.0150, METEOR=0.0345, CIDEr=0.7434
        * Gemma 2 9B IT: BLEU-1=0.0114, METEOR=0.0291, CIDEr=0.8066
    * **Report generato**: `/work/tesi_ediluzio/evaluation/reports/zero_shot_evaluation_report.html`

### Passo 3: Selezione Modelli e Preparazione Fine-tuning LoRA
* **Stato**: ✅ **COMPLETATO**
* **Nota**: Utilizzato train_set_final.json per il training.
* **3.A: Analisi Risultati Zero-Shot**: ✅ **COMPLETATO**
    * Analisi delle performance zero-shot di Llama 3.1 8B e Gemma 2 9B IT
    * Entrambi i modelli selezionati per il fine-tuning basato sui risultati
* **3.B: Selezione Modelli per Fine-tuning**: ✅ **COMPLETATO**
    * Modelli selezionati: Llama 3.1 8B Instruct e Gemma 2 9B IT
    * Decisione basata su performance zero-shot e compatibilità con LoRA
* **3.C: Preparazione LoRA per Modelli Selezionati**: ✅ **COMPLETATO**
    * **Stato**:
        * Script check_lora_targets.py: ✅ **PRONTO**
        * Script SLURM check_lora_targets.slurm: ✅ **PRONTO**
        * Libreria bitsandbytes: ✅ **INSTALLATA**
        * File JSON template (*_lora_config.json): ✅ **CREATI**
    * **Verifica Target Modules**: ✅ **COMPLETATO**
        * Target modules identificati per entrambi i modelli selezionati
        * Llama 3.1 8B: q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj
        * Gemma 2 9B IT: q_proj, k_proj, v_proj, o_proj, gate_proj, up_proj, down_proj
    * **Finalizzazione Config**: ✅ **COMPLETATO**
        * Configurazioni ottimizzate per entrambi i modelli
        * Parametri LoRA: rank=64, alpha=16, dropout=0.1
        * Learning rate: 1e-4
        * Batch size: 2 per device, gradient accumulation=4 (effettivo: 8)

### Passo 4: Fine-tuning LoRA Incrementale (Modelli Selezionati)
* **Stato**: 🔄 **IN CORSO**
* **Nota**: Usa train_set_final.json. Valuta su test_set_final_2k.json.
* **4.A: Esperimento LoRA - Input XML Raw**: ✅ **COMPLETATO**
    * **Llama 3.1 8B**:
        * Checkpoint finale: 4000
        * Loss finale: 0.4776
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence/`
    * **Gemma 2 9B IT**:
        * Checkpoint finale: 3000
        * Loss finale: 0.317
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence/`
* **4.B: Esperimento LoRA - Input XML + Token Custom**: ✅ **COMPLETATO**
    * **Llama 3.1 8B**:
        * Checkpoint finale: 1800
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token/`
        * Tokenizer personalizzato con 17 token speciali per SVG
    * **Gemma 2 9B IT**:
        * Checkpoint finale: 2400
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_custom_token/`
        * Tokenizer personalizzato con 17 token speciali per SVG
* **4.C: Training con Dataset Diviso in Fasce di Complessità**: ✅ **COMPLETATO**
    * **Llama 3.1 8B**:
        * Checkpoint finale: 525
        * Loss finale: 0.4352
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1/`
    * **Gemma 2 9B IT**:
        * Checkpoint finale: 525
        * Loss finale: 0.3005
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1/`
* **4.D: Training Ottimizzato per Convergenza**: ✅ **COMPLETATO**
    * **Llama 3.1 8B**:
        * Checkpoint finale: 4000
        * Loss finale: 0.9478
        * Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
        * Learning rate: 1e-4 (ridotto rispetto ai precedenti)
        * Epoche: 10 (aumentate rispetto ai precedenti)
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/`
    * **Gemma 2 9B IT**:
        * Checkpoint finale: 4000
        * Loss finale: 0.0251
        * Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
        * Learning rate: 1e-4 (ridotto rispetto ai precedenti)
        * Epoche: 10 (aumentate rispetto ai precedenti)
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence/`
* **4.E: Analisi Comparativa Fine-tuning**: 🔄 **IN CORSO**
    * Valutazione zero-shot completata
    * Valutazione fine-tuned pianificata al completamento dei job di training
* **4.F: Training Multi-GPU con Ottimizzazioni Avanzate**: 🔄 **IN CORSO**
    * **Llama 3.1 8B Multi-GPU**:
        * Job SLURM: 2577378
        * Configurazione: LoRA con rank=128, alpha=256, dropout=0.1
        * Learning rate: 5e-5 (ridotto per stabilizzare il training)
        * Batch size: 16 per GPU (2 GPU)
        * Stato: Riavviato da zero (checkpoint corrotto)
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test2_multi_gpu/`
    * **Gemma 2 9B IT Multi-GPU**:
        * Job SLURM: 2577377
        * Nodo: "nullazzo"
        * Configurazione: LoRA con rank=128, alpha=256, dropout=0.1
        * Learning rate: 3e-5 (ridotto per stabilizzare il training)
        * Batch size: 16 per GPU (2 GPU)
        * Stato: Continua dal checkpoint 5200
        * Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test2_multi_gpu/`

### Passo 5: Sviluppi Futuri (Visione a Lungo Termine)
* **Stato**: ⏳ **PIANIFICAZIONE FUTURA**
* **Nota**: Basato sui risultati del Passo 4.C.
* **5.A-F**: Estensione Dataset, Fine-tuning Modelli Più Grandi, Architetture Encoder-Decoder, Allineamento Avanzato, Valutazione Rigorosa, Scalabilità HW/SW.

## Pipeline Completa Standardizzata

**IMPORTANTE: È stata definita una pipeline completa standardizzata che DEVE essere seguita per ogni ciclo di training e valutazione.**

La pipeline completa è documentata in dettaglio nel file [PIPELINE_COMPLETA.md](/docs/PIPELINE_COMPLETA.md) e include:

1. **Preparazione e Training**:
   * Configurazione dei job SLURM
   * Aggiornamento del log delle run
   * Monitoraggio su Weights & Biands

2. **Valutazione**:
   * Configurazione dei job di valutazione
   * Calcolo delle metriche (BLEU, METEOR, CIDEr, CLIP Score)

3. **Generazione di Report e Grafici**:
   * Creazione di report HTML con esempi qualitativi
   * Generazione di grafici radar per confrontare le metriche
   * Aggiornamento della documentazione

4. **Pulizia e Manutenzione**:
   * Gestione dei checkpoint
   * Aggiornamento dei log

**NOTA: Dopo ogni training, è OBBLIGATORIO generare il report HTML e i grafici radar utilizzando lo script `./scripts/generate_report.sh`.**

## Prossimi Passi Immediati:

1. **Monitoraggio Job di Training in Corso**:
   * Llama 3.1 8B Multi-GPU (Job 2578192) - Riavviato da zero con rank=4, alpha=8, lr=1e-5
   * Gemma 2 9B IT Multi-GPU (Job 2578193) - Riavviato da zero con rank=4, alpha=8, lr=1e-5
   * Test SVG Rendering (Job 2578203) - Test di rendering SVG con diverse librerie
   * Captioner Esterni (Job 2578163) - Valutazione di BLIP, vit-gpt2 e cogvlm

2. **Valutazione Modelli Fine-tuned**:
   * Eseguire valutazione completa dei modelli fine-tuned al completamento del training
   * Utilizzare gli script di valutazione già preparati
   * Calcolare metriche: BLEU-1/2/3/4, METEOR, CIDEr, CLIP Score
   * Generare report HTML con confronto tra modelli zero-shot e fine-tuned

3. **Analisi Comparativa dei Risultati**:
   * Confrontare le performance dei diversi approcci di fine-tuning
   * Analizzare l'impatto del tokenizer personalizzato
   * Valutare l'efficacia della divisione in fasce di complessità
   * Identificare il miglior approccio per ciascun modello

4. **Documentazione Finale**:
   * Preparare report completo con tutti i risultati
   * Documentare le best practices identificate
   * Creare presentazione con i risultati principali
