# Struttura del Filesystem del Progetto

Questo documento descrive la struttura del filesystem del progetto di generazione di didascalie per SVG.

Data di aggiornamento: 06/05/2025

## Directory Principali

- **/work/tesi_ediluzio/**: Directory principale del progetto
  - **analysis/**: Analisi dei dati e dei modelli
    - **checkpoint_cleanup/**: Script e log per la pulizia dei checkpoint
    - **lora_parameters/**: Analisi dei parametri LoRA
    - **tokenizer/**: Analisi dei tokenizer personalizzati
    - **wandb/**: Analisi dei dati di Weights & Biands
    - **zero_shot/**: Analisi dei risultati zero-shot
  - **clip_module/**: Modulo CLIP per il calcolo del CLIP Score
  - **data/**: Dati di input e output
    - **processed/**: Dati processati
      - **xml_format/**: Dati in formato XML
        - **train_set_final_xml.json**: Dataset di training in formato XML
        - **test_set_final_2k_xml.json**: Dataset di test in formato XML
  - **data_preparation/**: Script per la preparazione dei dataset
  - **evaluation/**: Script e risultati di valutazione
    - **charts/**: Grafici di valutazione
    - **checkpoint_metrics/**: Metriche di valutazione per i checkpoint
    - **comparisons/**: Confronti tra modelli
    - **complex_svg_single/**: Valutazione su SVG complessi
    - **metrics/**: Metriche di valutazione
    - **reports/**: Report HTML con i risultati
      - **svg_images/**: Immagini SVG per i report
  - **reports/**: Report e visualizzazioni
    - **html/**: Report HTML
    - **charts/**: Grafici e visualizzazioni
  - **temp/**: File temporanei
    - **archive/**: Archivi e backup
  - **zero_shot/**: Risultati della valutazione zero-shot
  - **experiments/**: Esperimenti e script di training
    - **xml_direct_input/**: Esperimenti con input XML diretto
      - **configs/**: Configurazioni per i modelli
      - **comprehensive_examples/**: Esempi completi di inferenza
      - **dataset_analysis/**: Analisi del dataset
      - **evaluation_results/**: Risultati della valutazione
        - **clip_scores/**: Risultati del CLIP Score
        - **gemma2_9b_it_test1_convergence/**: Risultati per Gemma 2 9B IT
        - **llama31_8b_test1_convergence/**: Risultati per Llama 3.1 8B
      - **generated_svgs/**: SVG generati durante l'inferenza
      - **inference_results/**: Risultati dell'inferenza
      - **outputs/**: Output dei modelli addestrati
      - **simple_examples/**: Esempi semplici di inferenza
      - **utils/**: Utility per gli esperimenti
      - **visual_examples/**: Esempi visivi di inferenza
  - **logs/**: Log dei job SLURM
    - **archive/**: Log archiviati
  - **models/**: Modelli salvati
  - **results/**: Risultati degli esperimenti
    - **lora/**: Risultati dell'inferenza LoRA con dataset originale
    - **lora_xml/**: Risultati dell'inferenza LoRA con dataset XML
    - **metrics/**: Metriche di valutazione
    - **zero_shot/**: Risultati dell'inferenza zero-shot con dataset originale
    - **zero_shot_xml/**: Risultati dell'inferenza zero-shot con dataset XML
  - **scripts/**: Script di utilità
    - **cleanup/**: Script per la pulizia dei file e dei checkpoint
    - **inference/**: Script per l'inferenza con modelli addestrati
    - **training/**: Script per il training dei modelli
    - **evaluation/**: Script per la valutazione dei modelli
    - **visualization/**: Script per la creazione di report e visualizzazioni
    - **utils/**: Utility varie
    - **slurm/**: Script SLURM per l'esecuzione su cluster
    - **data_processing/**: Script per la preparazione dei dati
  - **shared/**: Codice condiviso
    - **svg_core/**: Funzionalità core per SVG
    - **utils/**: Utility generiche
  - **utils/**: Utility per il progetto
  - **wandb/**: Cache e configurazioni di Weights & Biands

## Directory di Output dei Modelli

- **/work/tesi_ediluzio/experiments/xml_direct_input/outputs/**: Directory principale per gli output dei modelli
  - **llama31_8b_lora_xml_no_token_convergence/**: Llama 3.1 8B senza tokenizer personalizzato
    - Checkpoint finale: 4000
    - Loss finale: 0.4776
  - **gemma2_9b_it_lora_xml_no_token_convergence/**: Gemma 2 9B IT senza tokenizer personalizzato
    - Checkpoint finale: 3000
    - Loss finale: 0.317
  - **llama31_8b_lora_xml_custom_token/**: Llama 3.1 8B con tokenizer personalizzato
    - Checkpoint finale: 1800
    - Tokenizer personalizzato con 17 token speciali per SVG
  - **gemma2_9b_it_lora_xml_custom_token/**: Gemma 2 9B IT con tokenizer personalizzato
    - Checkpoint finale: 2400
    - Tokenizer personalizzato con 17 token speciali per SVG
  - **llama31_8b_test1/**: Llama 3.1 8B con dataset diviso in fasce
    - Checkpoint finale: 525
    - Loss finale: 0.4352
  - **gemma2_9b_it_test1/**: Gemma 2 9B IT con dataset diviso in fasce
    - Checkpoint finale: 525
    - Loss finale: 0.3005
  - **llama31_8b_test1_convergence/**: Llama 3.1 8B ottimizzato per convergenza
    - Checkpoint finale: 4000
    - Loss finale: 0.9478
    - Configurato per 15 epoche con early stopping (patience=30)
  - **gemma2_9b_it_test1_convergence/**: Gemma 2 9B IT ottimizzato per convergenza
    - Checkpoint finale: 4000
    - Loss finale: 0.0251
    - Configurato per 15 epoche con early stopping (patience=30)

## Directory di Valutazione

- **/work/tesi_ediluzio/evaluation/**: Directory principale per la valutazione
  - **checkpoint_metrics/**: Metriche di valutazione per i checkpoint
    - **llama31_8b_test1_convergence/**: Metriche per Llama 3.1 8B
    - **gemma2_9b_it_test1_convergence/**: Metriche per Gemma 2 9B IT
  - **reports/**: Report HTML con i risultati
    - **zero_shot_evaluation_report.html**: Report per la valutazione zero-shot
    - **svg_report_with_complex.html**: Report con SVG incorporati
    - **placeholder_only_report.html**: Report con placeholder per SVG
    - **simple_img_report.html**: Report con SVG incorporati
    - **svg_images/**: Directory con immagini SVG per i report
  - **zero_shot/**: Risultati della valutazione zero-shot
    - **Llama-3.1-8B-Instruct_zero_shot_examples.json**: Esempi generati da Llama 3.1 8B
    - **gemma-2-9b-it_zero_shot_examples.json**: Esempi generati da Gemma 2 9B IT
    - **zero_shot_metrics.json**: Metriche aggregate per tutti i modelli zero-shot
    - **zero_shot_examples.json**: File combinato di tutti gli esempi zero-shot

## Script Principali

- **/work/tesi_ediluzio/experiments/xml_direct_input/**: Directory principale per gli script
  - **train_xml_slurm_token_xml.py**: Script per il training con tokenizer custom
  - **train_xml_slurm.py**: Script per il training senza tokenizer custom
  - **evaluate_checkpoints.py**: Script per valutare i checkpoint
  - **run_zero_shot_inference.py**: Script per valutare i modelli base in modalità zero-shot
  - **generate_comprehensive_report.py**: Script per generare report HTML completi
  - **run_inference_unified.py**: Script unificato per l'inferenza
  - **run_llama31_8b_custom_token_convergence.slurm**: Job SLURM per Llama 3.1 8B con tokenizer personalizzato
  - **run_gemma2_9b_it_custom_token_convergence.slurm**: Job SLURM per Gemma 2 9B IT con tokenizer personalizzato
  - **monitor_convergence_and_launch_custom_token.py**: Script per monitorare la convergenza

## Documentazione

- **/work/tesi_ediluzio/docs/multi_gpu_training.md**: Documentazione sul training multi-GPU
- **/work/tesi_ediluzio/docs/assistant_knowledge_transfer.md**: Documentazione per il trasferimento di conoscenze
- **/work/tesi_ediluzio/docs/FILESYSTEM.md**: Struttura dettagliata del filesystem
- **/work/tesi_ediluzio/docs/ROADMAP.md**: Roadmap del progetto
- **/work/tesi_ediluzio/README.md**: Documentazione principale del progetto

## Metriche di Valutazione Implementate

- **Qualità**: BLEU-1/2/3/4, METEOR, CIDEr, CLIP Score
- **Efficienza**: Tempo di inferenza, Perplexity
- **Analisi linguistica**: Lunghezza didascalie, Diversità vocabolario, Type-Token Ratio, Self-BLEU

## Risultati Principali Zero-Shot

- **Llama 3.1 8B**: BLEU-1=0.0150, METEOR=0.0345, CIDEr=0.7434
- **Gemma 2 9B IT**: BLEU-1=0.0114, METEOR=0.0291, CIDEr=0.8066

## Implementazioni Recenti

- **CheckpointCleanupCallback**: Elimina automaticamente i checkpoint vecchi durante l'addestramento
- **Supporto per Training Multi-GPU**: Configurazione DeepSpeed per ottimizzare l'uso della memoria
- **Integrazione CLIP Score**: Calcolo della similarità semantica tra SVG e didascalie
- **Monitoraggio Automatico dei Checkpoint**: Script per monitorare e valutare automaticamente i nuovi checkpoint
