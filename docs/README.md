# Documentazione del Progetto SVG Captioning

Questa directory contiene la documentazione del progetto SVG Captioning. I documenti sono organizzati per argomento e forniscono informazioni dettagliate su vari aspetti del progetto.

## Documenti Principali

### `FILESYSTEM.md`
Descrizione dettagliata della struttura del filesystem del progetto, incluse le directory principali, i file più importanti e la loro organizzazione.

### `multi_gpu_training.md`
Documentazione sul training multi-GPU, inclusi dettagli su configurazione DeepSpeed, parallelizzazione dei modelli, ottimizzazioni per la memoria e configurazione SLURM.

### `assistant_knowledge_transfer.md`
Documento di trasferimento di conoscenze per assistenti AI, contenente panoramica del progetto, dettagli su infrastruttura, modelli, dataset e configurazioni.

### `ROADMAP.md`
Roadmap del progetto con obiettivi a breve, medio e lungo termine, stato di avanzamento delle varie fasi e piani futuri.

### `aggiornamento_implementazioni_recenti.md`
Documento che descrive le implementazioni recenti del progetto, inclusi fine-tuning LoRA con tokenizer personalizzato, training con dataset diviso in fasce di complessità e altre ottimizzazioni.

## Panoramica del Progetto

Questo progetto implementa modelli di linguaggio per la generazione di didascalie per immagini SVG.

## Struttura del Progetto

- **data/**: Dataset e dati processati
  - **processed/**: Dataset processati pronti per il training
    - **train_set_final.json**: Dataset di training con SVG in formato semplificato
    - **test_set_final_2k.json**: Dataset di test con SVG in formato semplificato
    - **xml_format/**: Dataset con SVG in formato XML completo
      - **train_set_final_xml.json**: Dataset di training con SVG in formato XML
      - **test_set_final_2k_xml.json**: Dataset di test con SVG in formato XML
- **data_preparation/**: Script per la preparazione dei dataset
  - **analyze_svg_dataset.py**: Analisi statistica degli SVG
  - **prepare_finetuning_data.py**: Preparazione dei dati per il fine-tuning
  - **convert_to_xml.py**: Conversione dei dataset in formato XML completo
- **decoder_only/**: Implementazione di modelli decoder-only
- **evaluation/**: Script e risultati per la valutazione delle didascalie
  - **metrics/**: Metriche di valutazione per i diversi modelli
  - **reports/**: Report HTML con i risultati dell'inferenza
  - **comparisons/**: Grafici e tabelle comparative
  - **checkpoint_metrics/**: Metriche di valutazione per i checkpoint dei modelli
  - **evaluate_captions.py**: Valutazione delle didascalie con metriche standard
  - **visualize_results.py**: Visualizzazione dei risultati in formato HTML
  - **compare_models.py**: Confronto tra diversi modelli
  - **evaluate_checkpoints.py**: Valutazione dei checkpoint con metriche multiple
  - **visualize_checkpoint_metrics.py**: Visualizzazione dei risultati delle valutazioni dei checkpoint

- **experiments/**: Esperimenti e configurazioni
  - **xml_direct_input/**: Esperimenti con input XML diretto
    - **dataset_analysis/**: Analisi del dataset e divisione in fasce di complessità
      - **splits/**: Dataset divisi in fasce di complessità
        - **train.json**: Dataset di training diviso in fasce
        - **val.json**: Dataset di validazione diviso in fasce
    - **configs/**: Configurazioni per i diversi modelli
      - **llama31_8b_lora_xml_no_token.json**: Configurazione per Llama 3.1 8B senza tokenizer personalizzato
      - **gemma2_9b_it_lora_xml_no_token.json**: Configurazione per Gemma 2 9B IT senza tokenizer personalizzato
      - **llama31_8b_lora_xml_custom_token.json**: Configurazione per Llama 3.1 8B con tokenizer personalizzato
      - **gemma2_9b_it_lora_xml_custom_token.json**: Configurazione per Gemma 2 9B IT con tokenizer personalizzato
      - **llama31_8b_test1.json**: Configurazione per Llama 3.1 8B con dataset diviso in fasce
      - **gemma2_9b_it_test1.json**: Configurazione per Gemma 2 9B IT con dataset diviso in fasce
      - **llama31_8b_test1_convergence.json**: Configurazione per Llama 3.1 8B ottimizzata per convergenza
      - **gemma2_9b_it_test1_convergence.json**: Configurazione per Gemma 2 9B IT ottimizzata per convergenza
    - **outputs/**: Output dei modelli addestrati
      - **llama31_8b_lora_xml_no_token_convergence/**: Modello Llama 3.1 8B senza tokenizer personalizzato
      - **gemma2_9b_it_lora_xml_no_token_convergence/**: Modello Gemma 2 9B IT senza tokenizer personalizzato
      - **llama31_8b_lora_xml_custom_token/**: Modello Llama 3.1 8B con tokenizer personalizzato
      - **gemma2_9b_it_lora_xml_custom_token/**: Modello Gemma 2 9B IT con tokenizer personalizzato
      - **llama31_8b_test1/**: Modello Llama 3.1 8B con dataset diviso in fasce
      - **gemma2_9b_it_test1/**: Modello Gemma 2 9B IT con dataset diviso in fasce
      - **llama31_8b_test1_convergence/**: Modello Llama 3.1 8B ottimizzato per convergenza
      - **gemma2_9b_it_test1_convergence/**: Modello Gemma 2 9B IT ottimizzato per convergenza
    - **train_lora_test1.py**: Script per il training con dataset diviso in fasce
    - **train_llama_test1.py**: Script specifico per Llama 3.1 8B
    - **train_gemma_test1.py**: Script specifico per Gemma 2 9B IT
    - **run_llama_test1.slurm**: Job SLURM per Llama 3.1 8B
    - **run_gemma_test1.slurm**: Job SLURM per Gemma 2 9B IT
    - **run_llama_test1_convergence.slurm**: Job SLURM per Llama 3.1 8B ottimizzato per convergenza
    - **run_gemma_test1_convergence.slurm**: Job SLURM per Gemma 2 9B IT ottimizzato per convergenza
    - **prompt_templates.py**: Template di prompt per l'inferenza e il fine-tuning
    - **run_inference_unified.py**: Script unificato per l'inferenza
    - **optimize_lora_config.py**: Ottimizzazione delle configurazioni LoRA
    - **evaluate_checkpoints.py**: Script per la valutazione dei checkpoint con metriche multiple
    - **run_checkpoint_evaluation.slurm**: Job SLURM per la valutazione dei checkpoint
    - **monitor_and_evaluate_checkpoints.py**: Script per il monitoraggio e la valutazione automatica dei checkpoint
    - **run_checkpoint_monitor.sh**: Script per avviare il monitoraggio dei checkpoint
    - **visualize_checkpoint_metrics.py**: Script per la visualizzazione dei risultati delle valutazioni
- **logs/**: Log dei job SLURM
  - **llama_test1_*.out/err**: Log per i job di Llama 3.1 8B
  - **gemma_test1_*.out/err**: Log per i job di Gemma 2 9B IT
  - **llama_test1_conv_*.out/err**: Log per i job di Llama 3.1 8B ottimizzati per convergenza
  - **gemma_test1_conv_*.out/err**: Log per i job di Gemma 2 9B IT ottimizzati per convergenza
- **models/**: Modelli salvati
- **results/**: Risultati degli esperimenti
- **shared/**: Codice condiviso tra i diversi esperimenti
  - **svg_core/**: Funzionalità core per la gestione degli SVG
    - **custom_tokenizer_utils.py**: Utility per la creazione di tokenizer personalizzati
- **utils/**: Utility generiche
  - **monitor_jobs.py**: Script per monitorare i job SLURM
  - **cleanup.py**: Script per la pulizia dei file temporanei
- **wandb/**: Directory per Weights & Biands
  - **wandb/**: Run di Weights & Biands

## Flusso di Lavoro Attuale

1. **Training con Tokenizer Standard**: Completato
   - Llama 3.1 8B: Checkpoint finale 4000, Loss finale 0.4776
   - Gemma 2 9B IT: Checkpoint finale 3000, Loss finale 0.317

2. **Training con Tokenizer Personalizzato**: Completato
   - Llama 3.1 8B: Checkpoint finale 1800
   - Gemma 2 9B IT: Checkpoint finale 2400

3. **Training con Dataset Diviso in Fasce**: Completato
   - Llama 3.1 8B: Checkpoint finale 525, Loss finale 0.4352
   - Gemma 2 9B IT: Checkpoint finale 525, Loss finale 0.3005

4. **Training Ottimizzato per Convergenza**: Completato
   - Llama 3.1 8B: Completato con job 2566826 sul nodo "rezzonico"
   - Gemma 2 9B IT: Completato con job 2566825 sul nodo "gervasoni"
   - Configurati per 15 epoche con early stopping (patience=30)
   - Valutazione ogni 50 step per monitorare l'overfitting
   - Tracciamento avanzato su Weights & Biands con grafici di confronto train/eval loss
   - Gemma ha raggiunto 15 epoche con loss finale 0.0251
   - Llama ha raggiunto convergenza con loss finale 0.9478

5. **Valutazione dei Checkpoint e Inferenza**: Completato
   - Sistema completo per valutare i checkpoint con metriche multiple
   - Metriche di qualità: BLEU-1/2/3/4, METEOR, CIDEr, CLIP Score
   - Metriche di efficienza: Tempo di inferenza, Perplexity
   - Metriche di analisi linguistica: Lunghezza didascalie, Diversità vocabolario, Type-Token Ratio, Self-BLEU
   - Monitoraggio automatico dei nuovi checkpoint
   - Visualizzazione avanzata con grafici comparativi
   - Inferenza completata su dataset di test per entrambi i modelli
   - Report HTML generati con visualizzazione SVG e didascalie
   - Grafici radar per confronto delle metriche tra modelli

6. **Training con Pesi Convergenti e Tokenizer Personalizzato**: Pianificato
   - Utilizzo dei pesi dei modelli convergenti come punto di partenza
   - Applicazione del tokenizer personalizzato con 17 token speciali per SVG
   - Monitoraggio automatico della convergenza
   - Avvio automatico del training successivo

## Configurazione

Le configurazioni per i diversi modelli si trovano in `experiments/xml_direct_input/configs/`.

### Configurazioni per Training con Dataset Diviso in Fasce

- **llama31_8b_test1.json**: Configurazione per Llama 3.1 8B
  - Batch size: 2
  - Gradient accumulation: 4
  - Learning rate: 2e-4
  - Epoche: 3
  - LoRA rank: 64, alpha: 16
  - Quantizzazione: 4-bit

- **gemma2_9b_it_test1.json**: Configurazione per Gemma 2 9B IT
  - Batch size: 1
  - Gradient accumulation: 8
  - Learning rate: 2e-4
  - Epoche: 3
  - LoRA rank: 64, alpha: 16
  - Quantizzazione: 4-bit
  - Lunghezza massima: 768 token

### Configurazioni per Training Ottimizzato per Convergenza

- **llama31_8b_test1_convergence.json**: Configurazione per Llama 3.1 8B
  - Batch size: 2
  - Gradient accumulation: 4
  - Learning rate: 1e-4
  - Epoche: 10
  - LoRA rank: 64, alpha: 16
  - Quantizzazione: 4-bit
  - Valutazione ogni 50 step
  - Early stopping con patience 30

- **gemma2_9b_it_test1_convergence.json**: Configurazione per Gemma 2 9B IT
  - Batch size: 1
  - Gradient accumulation: 8
  - Learning rate: 1e-4
  - Epoche: 10
  - LoRA rank: 64, alpha: 16
  - Quantizzazione: 4-bit
  - Lunghezza massima: 768 token
  - Valutazione ogni 50 step
  - Early stopping con patience 30

### Configurazioni per Training con Pesi Convergenti e Tokenizer Personalizzato

- **llama31_8b_custom_token_from_convergence.json**: Configurazione per Llama 3.1 8B
  - Inizializzazione: Pesi dal miglior checkpoint del training ottimizzato per convergenza
  - Tokenizer personalizzato: 17 token speciali per SVG
  - Batch size: 2
  - Gradient accumulation: 4
  - Learning rate: 1e-4
  - Epoche: 10
  - LoRA rank: 64, alpha: 16
  - Quantizzazione: 4-bit
  - Valutazione ogni 50 step
  - Early stopping con patience 30

- **gemma2_9b_it_custom_token_from_convergence.json**: Configurazione per Gemma 2 9B IT
  - Inizializzazione: Pesi dal miglior checkpoint del training ottimizzato per convergenza
  - Tokenizer personalizzato: 17 token speciali per SVG
  - Batch size: 1
  - Gradient accumulation: 8
  - Learning rate: 1e-4
  - Epoche: 10
  - LoRA rank: 64, alpha: 16
  - Quantizzazione: 4-bit
  - Lunghezza massima: 768 token
  - Valutazione ogni 50 step
  - Early stopping con patience 30

## Utilizzo

### Training con Dataset Diviso in Fasce

```bash
# Llama 3.1 8B
sbatch experiments/xml_direct_input/run_llama_test1.slurm

# Gemma 2 9B IT
sbatch experiments/xml_direct_input/run_gemma_test1.slurm
```

### Training Ottimizzato per Convergenza

```bash
# Llama 3.1 8B
sbatch experiments/xml_direct_input/run_llama_test1_convergence.slurm

# Gemma 2 9B IT
sbatch experiments/xml_direct_input/run_gemma_test1_convergence.slurm
```

### Training con Pesi Convergenti e Tokenizer Personalizzato

```bash
# Avvia la valutazione dei modelli con tokenizer personalizzato e il monitoraggio della convergenza
bash experiments/xml_direct_input/start_evaluation_and_monitoring.sh

# Oppure avvia manualmente i job quando i modelli hanno raggiunto la convergenza
sbatch experiments/xml_direct_input/run_llama_custom_token_from_convergence.slurm \
    /work/tesi_ediluzio/experiments/xml_direct_input/configs/llama31_8b_custom_token_from_convergence.json

sbatch experiments/xml_direct_input/run_gemma_custom_token_from_convergence.slurm \
    /work/tesi_ediluzio/experiments/xml_direct_input/configs/gemma2_9b_it_custom_token_from_convergence.json
```

### Monitoraggio dei Job

```bash
# Visualizza i job in esecuzione
squeue -u $USER

# Visualizza i log di un job specifico
cat logs/llama_test1_conv_JOBID.out
cat logs/gemma_test1_conv_JOBID.err
```

### Monitoraggio su Weights & Biands

I progressi del training possono essere monitorati in tempo reale su Weights & Biands:
- Entity: 337543-unimore
- Project: captioner
- Run names:
  - llama31_8b_test1_convergence
  - gemma2_9b_it_test1_convergence

### Valutazione dei Checkpoint

```bash
# Valutazione manuale di checkpoint specifici
sbatch experiments/xml_direct_input/run_checkpoint_evaluation.slurm \
    meta-llama/Llama-3.1-8B-Instruct \
    /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence \
    /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence \
    "" \
    "" \
    "--load_in_4bit" \
    100 \
    "--use_clip"

# Monitoraggio automatico dei checkpoint
bash experiments/xml_direct_input/run_checkpoint_monitor.sh \
    meta-llama/Llama-3.1-8B-Instruct \
    /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence \
    /work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json \
    /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence \
    "" \
    "--load_in_4bit" \
    100 \
    "--use_clip" \
    1800 \
    200 \
    5

# Visualizzazione dei risultati delle valutazioni
python experiments/xml_direct_input/visualize_checkpoint_metrics.py \
    --metrics_dir /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence \
    --output_dir /work/tesi_ediluzio/evaluation/checkpoint_metrics/llama31_8b_test1_convergence/visualizations \
    --model_name "Llama 3.1 8B (Test1 Convergence)" \
    --use_wandb
```

## Prossimi Passi

1. **Analisi dei Risultati del Training Ottimizzato per Convergenza**:
   - Analizzare le curve di loss per identificare overfitting/underfitting
   - Confrontare le performance dei modelli Llama e Gemma
   - Valutare l'impatto della convergenza sulla qualità delle didascalie

2. **Avviare Training con Pesi Convergenti e Tokenizer Personalizzato**:
   - Avviare il monitoraggio della convergenza con `start_evaluation_and_monitoring.sh`
   - Utilizzare i pesi dei modelli convergenti come punto di partenza
   - Applicare il tokenizer personalizzato con 17 token speciali per SVG
   - Monitorare le performance rispetto ai modelli precedenti

3. **Valutazione Comparativa Approfondita**:
   - Confrontare le performance dei modelli con diverse metriche
   - Analizzare l'evoluzione delle metriche durante il training
   - Valutare l'impatto del tokenizer personalizzato e della divisione in fasce
   - Valutare l'impatto dell'inizializzazione con pesi convergenti
   - Creare visualizzazioni comparative più dettagliate (grafici radar, heatmap)

4. **Analisi Approfondita delle Didascalie**:
   - Analizzare la qualità linguistica delle didascalie generate
   - Valutare la diversità del vocabolario e la lunghezza delle didascalie
   - Confrontare i tempi di inferenza e la perplexity tra i diversi modelli
   - Migliorare i report con esempi di didascalie per ciascun modello

5. **Inferenza su Dataset Complessi**:
   - Generare didascalie su SVG di alta complessità
   - Valutare la qualità delle didascalie con metriche standard e avanzate
   - Creare una demo interattiva per la generazione di didascalie

6. **Analisi Finale e Documentazione**:
   - Preparare report con confronto tra i diversi approcci
   - Documentare le best practices identificate
   - Creare visualizzazioni comparative delle performance dei modelli
   - Preparare la documentazione finale del progetto

## Dipendenze

Vedere il file `requirements.txt` per l'elenco delle dipendenze.

## Roadmap

Vedere il file `ROADMAP.md` per i dettagli sulla roadmap del progetto.
