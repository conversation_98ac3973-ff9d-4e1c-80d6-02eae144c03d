# Aggiornamento Implementazioni Recenti (18/05/2025)

Ecco la lista aggiornata dei passi implementati recentemente nel progetto:

## 1. Fine-tuning LoRA con Tokenizer Personalizzato
* **Llama 3.1 8B con tokenizer personalizzato**: ✅ COMPLETATO
    * Checkpoint finale: 1800
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token/
    * Parametri addestrabili: 41,943,040 (0.5196% del totale)
    * Tokenizer personalizzato con 17 token speciali per SVG
* **Gemma 2 9B IT con tokenizer personalizzato**: ✅ COMPLETATO
    * Checkpoint finale: 2400
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_custom_token/
    * Parametri addestrabili: 54,018,048 (0.5811% del totale)
    * Tokenizer personalizzato con 17 token speciali per SVG

## 2. Training con Dataset Diviso in Fasce di Complessità
* **Llama 3.1 8B**: ✅ COMPLETATO
    * Checkpoint finale: 525
    * Loss finale: 0.4352
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1/
    * Parametri addestrabili: 167,772,160 (3.5633% del totale)
    * Tracciamento con Weights & Biands completato (Run ID: uwdfy7oc)
* **Gemma 2 9B IT**: ✅ COMPLETATO
    * Checkpoint finale: 525
    * Loss finale: 0.3005
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1/
    * Parametri addestrabili: 216,072,192 (4.0799% del totale)
    * Tracciamento con Weights & Biands completato (Run ID: cdksnxwl)
    * Configurazione ottimizzata per evitare errori OOM (batch size=1, gradient accumulation=8, max_length=768)

## 3. Training fino a Convergenza
* **Llama 3.1 8B senza tokenizer personalizzato**: ✅ COMPLETATO
    * Checkpoint finale: 4000
    * Loss finale: 0.4776
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence/
    * Tracciamento con Weights & Biands completato (entity=337543-unimore, project=captioner)
* **Gemma 2 9B IT senza tokenizer personalizzato**: ✅ COMPLETATO
    * Checkpoint finale: 3000
    * Loss finale: 0.317
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence/
    * Tracciamento con Weights & Biands completato (entity=337543-unimore, project=captioner)
* **Llama 3.1 8B ottimizzato per convergenza**: ✅ COMPLETATO (Job 2566826)
    * Completato sul nodo "rezzonico"
    * Checkpoint finale: 4000
    * Loss finale: 0.9478
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/
    * Configurato per 15 epoche con early stopping (patience=30)
    * Valutazione ogni 50 step per monitorare l'overfitting
* **Gemma 2 9B IT ottimizzato per convergenza**: ✅ COMPLETATO (Job 2566825)
    * Completato sul nodo "gervasoni"
    * Checkpoint finale: 4000
    * Loss finale: 0.0251
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence/
    * Configurato per 15 epoche con early stopping (patience=30)
    * Valutazione ogni 50 step per monitorare l'overfitting

## 4. Training Multi-GPU con Ottimizzazioni Avanzate
* **Llama 3.1 8B Multi-GPU**: 🔄 IN CORSO (Job 2578151)
    * In attesa (Priority)
    * Riavviato da zero
    * Configurato per 2 GPU con batch size 4 per GPU
    * Gradient accumulation impostato a 2
    * Learning rate ridotto a 5e-5 per stabilizzare il training
    * Rango LoRA ridotto a 4 per evitare OOM
    * Alpha LoRA ridotto a 8 per evitare OOM
    * Early stopping sulla loss di validazione con patience=100
    * Tracciamento su Weights & Biands con nome "test2_llama31_8b_800"
    * Salvataggio automatico del miglior modello in base alla loss di validazione
    * Sequenze di 256 token
    * Partizione boost_usr_prod per accedere a GPU con più memoria
* **Gemma 2 9B IT Multi-GPU**: 🔄 IN CORSO (Job 2578150)
    * In attesa (Priority)
    * Riavviato da zero
    * Configurato per 2 GPU con batch size 4 per GPU
    * Gradient accumulation impostato a 2
    * Learning rate ridotto a 3e-5 per stabilizzare il training
    * Rango LoRA ridotto a 4 per evitare OOM
    * Alpha LoRA ridotto a 8 per evitare OOM
    * Early stopping sulla loss di validazione con patience=100
    * Tracciamento su Weights & Biands con nome "test2_gemma2_9b_it_800"
    * Salvataggio automatico del miglior modello in base alla loss di validazione
    * Sequenze di 256 token
    * Partizione boost_usr_prod per accedere a GPU con più memoria

## 5. Implementazione di Callback per Pulizia Automatica
* **CheckpointCleanupCallback**: ✅ IMPLEMENTATO
    * Elimina automaticamente i checkpoint vecchi durante l'addestramento
    * Mantiene solo gli ultimi N checkpoint (specificato da save_total_limit)
    * Riduce significativamente lo spazio disco occupato
    * Integrato negli script di training

## 6. Ottimizzazione Gestione Spazio Disco
* **Pulizia Checkpoint**: ✅ COMPLETATO
    * Rimossi checkpoint di test e duplicati
    * Rimossi checkpoint intermedi dai modelli principali
    * Ridotto lo spazio occupato dalla directory outputs da 55GB a 8.5GB
    * Ulteriore pulizia effettuata il 29/04/2025, liberando circa 31.8GB di spazio
    * Pulizia aggiuntiva effettuata il 04/05/2025, liberando circa 37GB di spazio (52% dello spazio totale)
    * Rimossi checkpoint intermedi (400, 600, 700) mantenendo solo quelli essenziali (500, 800, 900)
    * Rimossa cache di Hugging Face (33GB) per ottimizzare lo spazio
* **Archiviazione Log**: ✅ COMPLETATO
    * Creata struttura di archiviazione per i log vecchi
    * Implementata compressione automatica dei log archiviati
    * Ridotto il numero di file nella directory dei log

## 7. Miglioramenti agli Script di Training
* **Supporto per Formato Dati Flessibile**: ✅ IMPLEMENTATO
    * Aggiunto supporto per entrambi i formati di dati: svg_data = item.get("data", item.get("xml"))
    * Permette di utilizzare sia il dataset originale che il dataset in formato XML
* **Padding Corretto per i Batch**: ✅ IMPLEMENTATO
    * Aggiunto parametro padding="max_length" alla tokenizzazione
    * Risolve il problema di dimensioni inconsistenti nei batch
* **Tokenizer Personalizzato per SVG**: ✅ IMPLEMENTATO
    * Aggiunta di token speciali per SVG al vocabolario del modello
    * Migliorata la capacità del modello di comprendere e generare codice SVG
* **Supporto per Ripresa Training**: ✅ IMPLEMENTATO
    * Aggiunto parametro --resume_from_checkpoint per riprendere il training da un checkpoint esistente
    * Permette di continuare il training dopo interruzioni per limite di tempo
    * Mantiene la continuità del tracciamento su Weights & Biands
* **Supporto per Training Multi-GPU**: ✅ IMPLEMENTATO
    * Configurazione DeepSpeed per ottimizzare l'uso della memoria
    * Supporto per training distribuito con torchrun/torch.distributed.run
    * Configurazione ottimizzata per evitare errori OOM
* **Funzione per Calcolo Parametri Trainabili**: ✅ IMPLEMENTATO (07/05/2025)
    * Implementata funzione `calculate_trainable_parameters` che restituisce:
        * Numero di parametri trainabili
        * Numero totale di parametri
        * Percentuale di parametri trainabili rispetto al totale
    * Integrata negli script di training per tracciamento su Weights & Biands
    * Migliorata la formattazione dei numeri nei log con separatore delle migliaia

## 8. Integrazione con Weights & Biands
* **Tracciamento Completo**: ✅ IMPLEMENTATO
    * Configurato per tracciare loss, metriche e parametri di training
    * Supporto per entity personalizzata (337543-unimore)
    * Visualizzazione in tempo reale del progresso del training
    * Salvataggio automatico della configurazione e dei risultati
* **Debug Avanzato**: ✅ IMPLEMENTATO
    * Logging dettagliato dello stato di inizializzazione
    * Tracciamento della modalità di esecuzione (online/offline)
    * Gestione degli errori con traceback completo

## 9. Valutazione dei Modelli
* **Valutazione Modelli Zero-Shot**: ✅ COMPLETATO
    * Implementata valutazione completa dei modelli base in modalità zero-shot
    * Generazione di didascalie per 100 esempi di test per ogni modello
    * Calcolo di metriche quantitative: BLEU-1/2/3/4, METEOR, CIDEr
    * Risultati salvati in /work/tesi_ediluzio/evaluation/zero_shot/
    * Report HTML generato in /work/tesi_ediluzio/evaluation/reports/zero_shot_evaluation_report.html
    * Risultati principali:
        * Llama 3.1 8B: BLEU-1=0.0150, METEOR=0.0345, CIDEr=0.7434
        * Gemma 2 9B IT: BLEU-1=0.0114, METEOR=0.0291, CIDEr=0.8066
    * Grafico radar generato con metriche reali in /work/tesi_ediluzio/evaluation/reports/real_radar_chart.png
* **Valutazione Modelli Fine-tuned**: ✅ COMPLETATO
    * Eseguita valutazione dei modelli fine-tuned
    * Utilizzate le stesse metriche della valutazione zero-shot
    * Confronto diretto tra modelli zero-shot e fine-tuned
    * Report HTML generati con visualizzazione SVG e didascalie
    * Grafici radar per confronto delle metriche tra modelli
    * Report HTML con placeholder per SVG generato in /work/tesi_ediluzio/evaluation/reports/placeholder_only_report.html
    * Report HTML con SVG incorporati generato in /work/tesi_ediluzio/evaluation/reports/simple_img_report.html
* **Valutazione Checkpoint**: ✅ IMPLEMENTATO
    * Implementato sistema completo per valutazione dei checkpoint con metriche multiple
    * Script principale: experiments/xml_direct_input/evaluate_checkpoints.py
    * Supporto per valutazione automatica durante il training
    * Metriche implementate:
        * Qualità: BLEU-1/2/3/4, METEOR, CIDEr, CLIP Score
        * Efficienza: Tempo di inferenza, Perplexity
        * Analisi linguistica: Lunghezza didascalie, Diversità vocabolario, Type-Token Ratio, Self-BLEU
    * Visualizzazione avanzata con grafici comparativi
    * Integrazione con Weights & Biands per tracciamento dei risultati
* **Generazione Report HTML**: ✅ IMPLEMENTATO
    * Script per generare report HTML completi con valutazioni, grafici e esempi qualitativi
    * Supporto per confronto tra modelli zero-shot e fine-tuned
    * Visualizzazione di metriche, grafici LOESS e esempi qualitativi
    * Integrazione con Weights & Biands per tracciamento dei risultati

## 10. Pulizia e Organizzazione del Progetto
* **Rimozione File Inutili**: ✅ COMPLETATO
    * Rimossi checkpoint di test e duplicati
    * Rimossi file temporanei e di log non necessari
    * Rimossi pesi dei modelli non utilizzati
    * Rimossi modelli Nemotron non utilizzati
* **Compressione Log Vecchi**: ✅ COMPLETATO
    * Archiviati tutti i log vecchi in un archivio compresso
    * Mantenuti solo i log più recenti
* **Pulizia Run Weights & Biands**: ✅ COMPLETATO
    * Rimosse run vecchie e non necessarie
    * Mantenute solo le run più recenti e rilevanti
* **Riorganizzazione Filesystem**: ✅ COMPLETATO (07/05/2025)
    * Creata struttura di directory più organizzata
    * Raggruppati file per categoria in sottodirectory dedicate
    * Creata directory `/docs` per tutta la documentazione
    * Creata directory `/scripts` con sottodirectory per tipo di script
    * Creata directory `/reports` per report e visualizzazioni
    * Aggiornati i README con la nuova struttura

## 11. Flusso di Lavoro Ottimizzato
* **Training Sequenziale**: ✅ IMPLEMENTATO
    * Prima completare i training senza tokenizer personalizzato fino a convergenza (COMPLETATO)
    * Poi utilizzare i modelli convergenti come base per i training con tokenizer personalizzato (COMPLETATO)
    * Infine, training con dataset diviso in fasce di complessità (COMPLETATO)
    * Training ottimizzati per convergenza (COMPLETATO)
* **Gestione Automatica dei Job**: ✅ IMPLEMENTATO
    * Monitoraggio dello stato dei job SLURM
    * Ripresa automatica dei training interrotti
    * Verifica delle dipendenze tra job
    * Sistema di monitoraggio e riavvio automatico implementato (04/05/2025)

* **Monitoraggio Automatico dei Checkpoint**: ✅ IMPLEMENTATO
    * Script per monitorare e valutare automaticamente i nuovi checkpoint
    * Avvio automatico di job SLURM per la valutazione
    * Visualizzazione dei risultati in tempo reale su Weights & Biands

## 12. Preparazione per Training con Pesi Convergenti e Tokenizer Personalizzato
* **Configurazioni**: ✅ IMPLEMENTATO
    * llama31_8b_custom_token_from_convergence.json: Configurazione per Llama 3.1 8B
    * gemma2_9b_it_custom_token_from_convergence.json: Configurazione per Gemma 2 9B IT
    * Utilizzo dei pesi dei modelli convergenti come punto di partenza
    * Applicazione del tokenizer personalizzato con 17 token speciali per SVG
* **Script SLURM**: ✅ IMPLEMENTATO
    * run_llama_custom_token_from_convergence.slurm: Job SLURM per Llama 3.1 8B
    * run_gemma_custom_token_from_convergence.slurm: Job SLURM per Gemma 2 9B IT
    * Configurati per 10 epoche con early stopping (patience=30)
* **Monitoraggio della Convergenza**: ✅ IMPLEMENTATO
    * monitor_convergence_and_launch_custom_token.py: Script per monitorare la convergenza
    * Avvio automatico del training successivo quando i modelli raggiungono la convergenza
    * Utilizzo del miglior checkpoint (checkpoint-best) come punto di partenza
* **Script di Avvio**: ✅ IMPLEMENTATO
    * start_evaluation_and_monitoring.sh: Script per avviare la valutazione e il monitoraggio
    * Avvio della valutazione dei modelli con tokenizer personalizzato
    * Avvio del monitoraggio della convergenza dei modelli in training

## 13. Implementazione del CLIP Score
* **Integrazione del CLIP Score**: ✅ COMPLETATO (05/05/2025)
    * Implementato script per il calcolo del CLIP Score tra SVG e didascalie
    * Script principale: /work/tesi_ediluzio/clip_score_gpu_simple.py
    * Utilizzo di CLIP ViT-B/32 per calcolare la similarità semantica
    * Supporto per esecuzione su GPU tramite job SLURM
    * Risultati salvati in /work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/clip_scores/
* **Valutazione con CLIP Score**: ✅ COMPLETATO (06/05/2025)
    * Eseguita valutazione dei modelli fine-tuned con CLIP Score
    * Confronto tra didascalie generate e didascalie di riferimento
    * Calcolo di metriche aggregate: media, mediana, min/max
    * Integrazione dei risultati nei report di valutazione
    * Aggiunta visualizzazione dei CLIP Score nei grafici radar

## 14. Test di Captioner Esterni
* **Implementazione Script di Test**: ✅ COMPLETATO (07/05/2025)
    * Creato script /work/tesi_ediluzio/scripts/evaluation/test_external_captioners.py
    * Supporto per 3 modelli di captioning da Hugging Face:
        * BLIP Image Captioning Large (Salesforce/blip-image-captioning-large)
        * ViT-GPT2 Image Captioning (nlpconnect/vit-gpt2-image-captioning)
        * CogVLM2-LLaMA3-Caption (THUDM/cogvlm2-llama3-caption)
    * Rendering SVG in immagini per l'input ai modelli
    * Salvataggio dei risultati in formato JSONL
* **Script SLURM per Test Captioner**: ✅ COMPLETATO (11/05/2025)
    * Creato script /work/tesi_ediluzio/scripts/slurm/run_external_captioners_prerendered.slurm
    * Configurato per testare i 3 modelli in sequenza
    * Valutazione automatica con CLIP Score
    * Job completato con ID 2577381 sul nodo "huber"
    * Limite di tempo ridotto a 4 ore per garantire l'efficienza
    * Risultati disponibili in /work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/external_captioners_prerendered/
    * Solo il modello BLIP ha generato risultati validi, vit-gpt2 e cogvlm hanno fallito

## 15. Analisi della Capacità di Gestione SVG Complessi
* **Analisi delle Lunghezze degli SVG**: ✅ COMPLETATO (07/05/2025)
    * Implementato script per analizzare la distribuzione delle lunghezze degli SVG nel dataset
    * Analisi completa delle lunghezze in caratteri e token per entrambi i modelli
    * Risultati principali:
        * Lunghezza massima in token per Llama: 410 (limite: 1024)
        * Lunghezza massima in token per Gemma: 532 (limite: 768)
        * Nessun SVG supera la lunghezza massima del contesto per entrambi i modelli
    * Grafici di distribuzione generati in /work/tesi_ediluzio/analysis/
    * Statistiche dettagliate salvate in /work/tesi_ediluzio/analysis/svg_length_stats.json
* **Valutazione della Capacità di Gestione**: ✅ COMPLETATO (07/05/2025)
    * Verificato che entrambi i modelli possono gestire tutti gli SVG nel dataset senza troncarli
    * Gemma richiede circa 1.5 volte più token rispetto a Llama per rappresentare gli stessi SVG
    * Ampio margine di sicurezza per entrambi i modelli (SVG più lungo: 532 token per Gemma, limite: 768)
    * Nessuna necessità di modificare le configurazioni attuali dei modelli

## 16. Documentazione Tecnica
* **Documentazione Multi-GPU**: ✅ COMPLETATO (05/05/2025)
    * Creato documento /work/tesi_ediluzio/docs/multi_gpu_training.md
    * Descrizione dettagliata dell'implementazione del training multi-GPU
    * Spiegazione della configurazione hardware nei job SLURM
    * Dettagli sulla parallelizzazione a livello di batch
    * Descrizione dell'accumulo del gradiente e della distribuzione del carico
* **Documentazione Trasferimento Conoscenze**: ✅ COMPLETATO (06/05/2025)
    * Creato documento /work/tesi_ediluzio/docs/assistant_knowledge_transfer.md
    * Panoramica completa del progetto per futuri assistenti
    * Dettagli su infrastruttura, modelli, dataset e configurazioni
    * Descrizione del workflow e degli script principali
    * Indicazioni sulle preferenze dell'utente e problemi comuni
* **Documentazione Filesystem**: ✅ COMPLETATO (07/05/2025)
    * Creato documento /work/tesi_ediluzio/docs/FILESYSTEM.md
    * Descrizione dettagliata della struttura del filesystem
    * Spiegazione delle directory principali e dei file più importanti
    * Guida alla navigazione del progetto
    * Indicazioni per la manutenzione del filesystem
* **Documentazione Gestione SVG Complessi**: ✅ COMPLETATO (07/05/2025)
    * Creato documento /work/tesi_ediluzio/docs/svg_complexity_analysis.md
    * Analisi dettagliata della distribuzione delle lunghezze degli SVG
    * Confronto tra i tokenizer di Llama e Gemma nella rappresentazione degli SVG
    * Raccomandazioni per la gestione di SVG complessi in futuro
    * Grafici e statistiche inclusi nel documento

## 17. Prossimi Passi Pianificati
* **Analisi dei Risultati del Training Ottimizzato per Convergenza**: ✅ COMPLETATO (05/05/2025)
    * Analizzate le curve di loss per identificare overfitting/underfitting
    * Confrontate le performance dei modelli Llama e Gemma
    * Valutato l'impatto della convergenza sulla qualità delle didascalie
    * Analizzati i report HTML generati con visualizzazione SVG e didascalie
* **Avviare Training con Pesi Convergenti e Tokenizer Personalizzato**: ✅ AVVIATO (05/05/2025)
    * Avviato il monitoraggio della convergenza con start_evaluation_and_monitoring.sh
    * Utilizzati i pesi dei modelli convergenti come punto di partenza
    * Applicato il tokenizer personalizzato con 17 token speciali per SVG
    * Job in esecuzione sui nodi "vegeta" (Llama) e "gervasoni" (Gemma)
    * Monitoraggio attivo delle performance rispetto ai modelli precedenti
* **Analisi dei Risultati dei Captioner Esterni**: ⚠️ PARZIALE (11/05/2025)
    * Job completato (ID: 2577381) ma con risultati parziali
    * Solo il modello BLIP ha generato risultati validi
    * I modelli vit-gpt2 e cogvlm hanno fallito la generazione di didascalie
    * Confronto delle performance limitato al solo modello BLIP
    * Analisi delle didascalie generate dal modello BLIP
    * Valutazione dell'impatto del rendering SVG sulla qualità delle didascalie
    * Risultati disponibili in /work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/external_captioners_prerendered/
    * Necessario investigare i problemi con vit-gpt2 e cogvlm
* **Valutazione Comparativa Approfondita**: 🔄 IN CORSO
    * Confronto delle performance dei modelli con diverse metriche
    * Analisi dell'evoluzione delle metriche durante il training
    * Valutazione dell'impatto del tokenizer personalizzato e della divisione in fasce
    * Valutazione dell'impatto dell'inizializzazione con pesi convergenti
    * Creazione di visualizzazioni comparative più dettagliate (grafici radar, heatmap)
    * Miglioramento dei grafici radar esistenti con dati più accurati
* **Analisi Approfondita delle Didascalie**: 🔄 IN CORSO
    * Analisi della qualità linguistica delle didascalie generate
    * Valutazione della diversità del vocabolario e della lunghezza delle didascalie
    * Confronto dei tempi di inferenza e della perplexity tra i diversi modelli
    * Miglioramento dei report con esempi di didascalie per ciascun modello
    * Perfezionamento dei report HTML esistenti con visualizzazioni SVG più accurate
* **Inferenza su Dataset Complessi**: 🔄 PIANIFICATO
    * Generare didascalie su SVG di alta complessità
    * Valutare la qualità delle didascalie con metriche standard e avanzate
    * Creare una demo interattiva per la generazione di didascalie
    * Utilizzare i report HTML sviluppati per visualizzare i risultati dell'inferenza
* **Documentazione Finale e Preparazione Tesi**: 🔄 PIANIFICATO
    * Preparare report con confronto tra i diversi approcci
    * Documentare le best practices identificate
    * Creare visualizzazioni comparative delle performance dei modelli
    * Preparare la documentazione finale del progetto
    * Includere i report HTML e i grafici radar generati come parte della documentazione

## 18. Risoluzione Problemi Recenti (12/05/2025)
* **Installazione DeepSpeed**: ✅ COMPLETATO
    * Identificato problema con l'installazione di pip nell'ambiente virtuale
    * Creato e lanciato job per reinstallare pip e DeepSpeed (Job ID: 2567997)
    * DeepSpeed installato con successo e job di training riavviati
    * Job di training ora in esecuzione con supporto DeepSpeed per ottimizzazione della memoria
* **Risoluzione Problemi Job External Captioners**: ✅ COMPLETATO
    * Identificato problema con il job precedente (ID: 2571807) bloccato dopo 10+ ore
    * Creato nuovo script con timeout ridotto a 4 ore invece di 12/24 ore
    * Modificato script per saltare il modello BLIP già valutato con successo
    * Job riavviato con ID 2577381 sul nodo "huber"
    * Solo il modello BLIP ha generato risultati validi
* **Risoluzione Problemi Training Multi-GPU**: ✅ COMPLETATO
    * Identificato problema di memoria (OOM) con i job multi-GPU
    * Ridotto il rango LoRA da 128 a 4 e l'alpha da 256 a 8
    * Ridotto il batch size da 16 a 4 per GPU
    * Impostato gradient accumulation a 2
    * Riavviati job Gemma (ID: 2578150) e Llama (ID: 2578151) con configurazione ottimizzata
    * Spostati job nella partizione boost_usr_prod per accedere a GPU con più memoria
* **Risoluzione Ciclo Infinito di Riavvio**: ✅ COMPLETATO
    * Identificato problema di auto-pianificazione che causava riavvii infiniti
    * Disabilitata l'auto-pianificazione nei job SLURM
    * Implementato sistema di monitoraggio più robusto per i job
    * Aggiornati tutti i file di log e documentazione con i nuovi ID dei job

## 19. Aggiornamenti Recenti (Post 12/05/2025)

* **Miglioramenti Script Grafici Radar**: ✅ COMPLETATO
    * Script interessato: `generate_metrics_radar_chart.py`
    * Modifiche apportate:
        * Rinominata la funzione di plotting da `эмодзи_к_улыбке` a `generate_radar_chart`.
        * Migliorato posizionamento della legenda (`fig.legend()`, `loc='lower center'`, `bbox_to_anchor=(0.5, -0.05)`, `ncol=2`, `frameon=False`).
        * Ottimizzato layout e stile: `figsize=(12, 12)`, `linewidth=2.5`, `alpha=0.15` per il riempimento, titolo più grande e in grassetto (`y=1.12`, `fontweight='bold'`, dimensione 18).
        * Ridotta dimensione font per etichette metriche e tick radiali per migliore leggibilità.
        * Aggiunto `plt.tight_layout()` e `plt.subplots_adjust(bottom=0.15)`.
        * Modificati codici colore esadecimali per migliore distinguibilità.
        * Nome file output cambiato in `Confronto_Metriche_Modelli_Radar_Chart.png`.
* **Generazione Report HTML Qualitativi**: ✅ UTILIZZATO
    * Script utilizzato: `experiments/xml_direct_input/generate_qualitative_report.py`
    * Funzionalità principali:
        * Input: File JSON con risultati inferenza (`--results_files`) e nomi modelli (`--model_names`).
        * Carica risultati (ID, SVG, ground truth, caption generata).
        * Identifica esempi comuni a tutti i modelli.
        * Seleziona numero specificato di esempi.
        * Genera file HTML (`qualitative_report.html` di default).
        * Contenuto HTML: SVG (o rendering), didascalia vera, tabella con didascalie generate.
        * Opzioni: Rendering SVG (`--render_svg`), dimensione rendering, integrazione Weights & Biases.
* **Valutazione Captioner Esterni (Job SLURM)**: 🔄 IN ATTESA
    * Job ID: `2581382`
    * Nome Job: `ext_capt`
    * Partizione: `boost_usr`
    * Utente: `ediluzio`
    * Stato: `PD` (Pending)
    * Motivo attesa: Nodi richiesti non disponibili o riservati.
    * Script SLURM associato (presunto): `run_external_captioners_multi_gpu.slurm` o variante.
    * Dettagli tecnici e funzionalità dello script:
        * Obiettivo: Valutare performance di captioner esterni (es. BLIP, ViT-GPT2, CogVLM) su immagini SVG.
        * Input: Dataset di immagini SVG.
        * Rendering SVG: Converte SVG in formato immagine (es. PNG) usando `cairosvg`.
        * Elaborazione Multi-GPU: Utilizza 2 GPU (L40S o A40, 48GB VRAM) con `torch.nn.DataParallel`.
        * Generazione Didascalie: Carica modelli e processa immagini SVG renderizzate per generare didascalie.
        * Valutazione Semantica: Calcola CLIP Score per misurare similarità semantica tra immagine e didascalia.
        * Gestione e Robustezza: Script Python dinamico, checkpointing per ripresa job, gestione errori e timeout.
        * Output: Risultati (didascalie, ID immagini) in file JSONL, file di riepilogo con statistiche e CLIP Score.
        * Path output (presunto): `/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/external_captioners_multi_gpu/`.

