# Struttura del Filesystem

```
/work/tesi_ediluzio/
├── README.md                 # Documentazione principale del progetto
├── ROADMAP.md                # Roadmap dettagliata del progetto
├── FILESYSTEM_STRUCTURE.md   # Questo file - struttura del filesystem
├── wandb_runs_log.md         # Log delle run su Weights & Biands
├── requirements.txt          # Dipendenze del progetto
├── docs/                     # Documentazione dettagliata
│   ├── PIPELINE_COMPLETA.md  # Pipeline completa per training e valutazione
│   ├── PROJECT_STATUS.md     # Stato attuale del progetto
│   └── ROADMAP.md            # Roadmap dettagliata del progetto
│
├── data/                     # Dataset
│   └── processed/            # Dataset processati
│       ├── README.md         # Documentazione dei dataset
│       ├── train_set_final.json
│       ├── test_set_final_2k.json
│       ├── finetuning/       # Dataset per il fine-tuning
│       └── xml_format/       # Dataset in formato XML
│           ├── train_set_final_xml.json
│           └── test_set_final_2k_xml.json
│
├── data_preparation/         # Script per la preparazione dei dataset
│
├── decoder_only/             # Implementazione di modelli decoder-only
│
├── evaluation/               # Valutazione delle didascalie
│   ├── README.md             # Documentazione della valutazione
│   ├── evaluate_captions.py  # Script per la valutazione
│   ├── visualize_results.py  # Script per la visualizzazione
│   ├── compare_models.py     # Script per il confronto dei modelli
│   ├── metrics/              # Metriche di valutazione
│   ├── reports/              # Report HTML
│   └── comparisons/          # Grafici e tabelle comparative
│
├── experiments/              # Esperimenti e configurazioni
│   └── xml_direct_input/     # Esperimenti con input XML diretto
│       ├── README.md         # Documentazione degli esperimenti
│       ├── configs/          # Configurazioni dei modelli
│       ├── outputs/          # Output degli esperimenti
│       │   └── nemotron_4b_lora_8bit_v1/  # Modello LoRA addestrato
│       ├── run_inference_unified.py    # Script di inferenza unificato
│       ├── run_inference_unified.slurm # Script SLURM per l'inferenza
│       ├── run_zero_shot_inference.py  # Script per l'inferenza zero-shot
│       ├── run_inference_auth.slurm    # Script SLURM con autenticazione
│       ├── run_lora_inference.slurm    # Script SLURM per l'inferenza LoRA
│       ├── run_inference_xml_quick.slurm # Script SLURM per test XML
│       └── train_lora_8bit.slurm       # Script SLURM per il training
│
├── logs/                     # Log dei job SLURM
│
├── models/                   # Modelli salvati
│
├── scripts/                  # Script per il training e l'inferenza
│   ├── training/             # Script per il training
│   ├── inference/            # Script per l'inferenza
│   ├── evaluation/           # Script per la valutazione
│   ├── visualization/        # Script per la visualizzazione e report
│   │   ├── generate_training_report.py  # Script per generare report HTML
│   │   ├── generate_radar_chart.py      # Script per generare grafici radar
│   │   └── generate_enhanced_report.py  # Script per report HTML avanzati
│   ├── slurm/                # Script SLURM per l'esecuzione su cluster
│   │   ├── generate_training_report.slurm  # Job SLURM per generare report
│   │   ├── train_llama_lora.slurm          # Job SLURM per training Llama
│   │   └── train_gemma_lora.slurm          # Job SLURM per training Gemma
│   ├── generate_report.sh    # Script per generare report HTML e grafici
│   └── utils/                # Utility per gli script
│
├── results/                  # Risultati degli esperimenti
│   ├── zero_shot/            # Risultati dell'inferenza zero-shot con dataset originale
│   ├── zero_shot_xml/        # Risultati dell'inferenza zero-shot con dataset XML
│   ├── lora/                 # Risultati dell'inferenza LoRA con dataset originale
│   ├── lora_xml/             # Risultati dell'inferenza LoRA con dataset XML
│   └── metrics/              # Metriche di valutazione
│       ├── zero_shot/        # Metriche per zero-shot
│       ├── zero_shot_xml/    # Metriche per zero-shot XML
│       ├── lora/             # Metriche per LoRA
│       └── lora_xml/         # Metriche per LoRA XML
│
├── reports/                  # Report e visualizzazioni
│   ├── html/                 # Report HTML con risultati qualitativi
│   │   ├── training_evaluation_report.html  # Report HTML generato
│   │   └── radar_chart.png                  # Grafico radar generato
│   └── charts/               # Grafici e visualizzazioni
│
├── shared/                   # Codice condiviso
│   └── svg_core/             # Funzionalità core per SVG
│       └── README.md         # Documentazione del tokenizer
│
└── utils/                    # Utility generiche
    ├── README.md             # Documentazione delle utility
    ├── monitor_jobs.py       # Script per monitorare i job
    └── cleanup.py            # Script per la pulizia dei file temporanei
```

## Descrizione delle Directory

### Directory Principali

- **data/**: Contiene i dataset utilizzati per il training e la valutazione
- **data_preparation/**: Script per la preparazione e il preprocessing dei dataset
- **decoder_only/**: Implementazione di modelli decoder-only per la generazione di didascalie
- **evaluation/**: Script e risultati per la valutazione delle didascalie generate
- **experiments/**: Configurazioni e script per gli esperimenti
- **logs/**: Log dei job SLURM
- **models/**: Modelli salvati dopo il training
- **results/**: Risultati degli esperimenti
- **shared/**: Codice condiviso tra i diversi esperimenti
- **utils/**: Utility generiche per il progetto
- **chunks/**: Chunk di dati per il training

### Directory di Esperimenti

- **xml_direct_input/**: Esperimenti con input XML diretto per la generazione di didascalie

### Directory di Valutazione

- **metrics/**: Metriche di valutazione per i diversi modelli
- **reports/**: Report HTML con i risultati dell'inferenza
- **comparisons/**: Grafici e tabelle comparative per il confronto dei modelli

## File Principali

- **README.md**: Documentazione principale del progetto
- **ROADMAP.md**: Roadmap dettagliata del progetto
- **wandb_runs_log.md**: Log delle run su Weights & Biands
- **requirements.txt**: Dipendenze del progetto
- **docs/PIPELINE_COMPLETA.md**: Pipeline completa per training e valutazione
- **docs/PROJECT_STATUS.md**: Stato attuale del progetto
- **scripts/generate_report.sh**: Script per generare report HTML e grafici
- **scripts/visualization/generate_training_report.py**: Script per generare report HTML
- **scripts/visualization/generate_radar_chart.py**: Script per generare grafici radar
- **scripts/slurm/generate_training_report.slurm**: Job SLURM per generare report
- **evaluate_captions.py**: Script per la valutazione delle didascalie
- **visualize_results.py**: Script per la visualizzazione dei risultati
- **compare_models.py**: Script per il confronto dei modelli
- **run_inference_unified.py**: Script unificato per l'inferenza
- **monitor_jobs.py**: Script per monitorare i job SLURM
- **cleanup.py**: Script per la pulizia dei file temporanei
