# Stato Attuale del Progetto SVG Captioning per Agente (12/05/2024)

## Panoramica del Progetto
Questo progetto si concentra sul fine-tuning di Large Language Models (LLMs) per la generazione di didascalie (caption) per immagini SVG. Il progetto utilizza principalmente Llama 3.1 8B e Gemma 2 9B IT, con tecniche di fine-tuning basate su LoRA e tokenizer personalizzati.

## Stato Attuale (12/05/2024)

### Job in Esecuzione
1. **Test SVG** (Job ID: 2578252)
   - In esecuzione (R) nella partizione all_usr_prod
   - Test di rendering SVG con diverse librerie (CairoSVG, svglib, Inkscape)
   - Numero di campioni: 5
   - Tempo limite: 24 ore

2. **Valutazione Captioner Esterni** (Job ID: 2578232)
   - In esecuzione (R) nella partizione all_usr_prod
   - Valutazione di captioner esterni (BLIP, ViT-GPT2, CogVLM)
   - Metriche: BLEU-4, ROUGE-<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r, CLIP Score
   - Tempo limite: 24 ore

### Job in Attesa
1. **Llama 3.1 8B Multi-GPU** (Job ID: 2578401)
   - In attesa (Priority) nella partizione boost_usr_prod
   - Configurato per 2 GPU con batch size 4 per GPU (effettivo 8)
   - Rango LoRA: 16, Alpha: 32
   - Target modules: ["q_proj", "v_proj", "k_proj", "o_proj"]
   - Gradient accumulation: 1 (nessun accumulo)
   - Learning rate: 1e-5
   - Early stopping: patience=100, min_delta=0.0001 (sulla loss di validazione)
   - Tracciamento su Weights & Biands: test3_llama31_8b_multi_804

2. **Gemma 2 9B IT Multi-GPU** (Job ID: 2578402)
   - In attesa (Priority) nella partizione boost_usr_prod
   - Configurato per 2 GPU con batch size 4 per GPU (effettivo 8)
   - Rango LoRA: 16, Alpha: 32
   - Target modules: ["q_proj", "v_proj", "k_proj", "o_proj"]
   - Gradient accumulation: 1 (nessun accumulo)
   - Learning rate: 1e-5
   - Early stopping: patience=100, min_delta=0.0001 (sulla loss di validazione)
   - Tracciamento su Weights & Biands: test3_gemma2_9b_it_multi_804

### Miglioramenti Implementati Recentemente
1. **Rimozione Gradient Accumulation**
   - Impostato `gradient_accumulation_steps=1` nei nuovi job
   - Aumentato batch size a 4 per GPU per mantenere lo stesso batch effettivo totale

2. **Mitigazione Overfitting**
   - Implementato early stopping sulla loss di validazione (patience=100)
   - Configurato salvataggio del miglior modello (load_best_model_at_end=True)

3. **Aumento Parametri LoRA**
   - Aumentato rank da 4 a 16
   - Aumentato alpha da 8 a 32
   - Estesi target modules a tutti i componenti dell'attenzione
   - Implementata funzione per il conteggio dei parametri trainabili

4. **Ottimizzazione Training**
   - Ridotto learning rate a 1e-5 (era 2e-4 nei test precedenti)
   - Configurato salvataggio di checkpoint ogni 100 step
   - Impostato limite di 5 checkpoint salvati
   - Training su dataset completo senza divisioni per fasce di complessità

## Prossimi Passi

### 1. Monitoraggio dei Job di Training
- Monitorare i job `llama_te` (2578401) e `gemma_te` (2578402) fino al completamento
- Verificare che i job partano e procedano senza errori di memoria
- Controllare le curve di loss su Weights & Biands
- Verificare che i checkpoint vengano salvati correttamente
- Monitorare l'early stopping sulla loss di validazione

### 2. Analisi dei Risultati dei Captioner Esterni
- Analizzare i risultati del job `ext_capt` (2578232)
- Confrontare le performance dei captioner esterni (BLIP, ViT-GPT2, CogVLM)
- Valutare l'efficacia del CLIP Score come metrica
- Confrontare le performance dei captioner esterni con i modelli fine-tuned

### 3. Analisi dei Risultati del Test SVG
- Analizzare i risultati del job `test_svg` (2578252)
- Valutare le performance delle diverse librerie di rendering SVG
- Identificare la migliore libreria per il rendering SVG
- Verificare la qualità delle immagini renderizzate

### 4. Inferenza con Modelli Fine-tuned
- Eseguire inferenza con i modelli fine-tuned su diverse fasce di complessità
- Generare report HTML con esempi qualitativi
- Creare grafici radar per confrontare le performance
- Analizzare l'impatto dei parametri LoRA sulle performance

## Istruzioni per l'Agente

### Monitoraggio dei Job
1. **Verifica lo stato dei job**:
   ```bash
   squeue -u ediluzio
   ```

2. **Controlla i log dei job**:
   ```bash
   # Job di training
   tail -f /work/tesi_ediluzio/logs/llama_test3_multi_2578401.out
   tail -f /work/tesi_ediluzio/logs/gemma_test3_multi_2578402.out

   # Job di valutazione
   tail -f /work/tesi_ediluzio/logs/test_svg_2578252.out
   tail -f /work/tesi_ediluzio/logs/ext_capt_2578232.out
   ```

3. **Verifica i dettagli dei job**:
   ```bash
   sacct -j 2578401,2578402,2578252,2578232 --format=JobID,JobName,State,Elapsed,NodeList,ExitCode
   ```

### Gestione dei Job
1. **Se un job fallisce**:
   - Analizza i log di errore per identificare la causa
   - Verifica se ci sono problemi di memoria (OOM)
   - Controlla se ci sono errori nei parametri LoRA
   - Riavvia il job con la configurazione corretta

2. **Se un job è bloccato in attesa**:
   - Verifica la disponibilità di risorse sul cluster
   - Considera di modificare la partizione o i vincoli hardware
   - Monitora la coda dei job con `squeue -p boost_usr_prod`

### Analisi dei Risultati
1. **Controlla i risultati della valutazione**:
   ```bash
   ls -la /work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/
   ```

2. **Analizza le metriche di valutazione**:
   ```bash
   cat /work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/*/summary.json
   ```

3. **Visualizza i grafici su Weights & Biands**:
   - Entity: 337543-unimore
   - Project: captioner
   - Run names: test3_llama31_8b_multi_804, test3_gemma2_9b_it_multi_804

### Aggiornamento della Documentazione
1. **Aggiorna i file di stato del progetto**:
   ```bash
   nano /work/tesi_ediluzio/docs/project_status.md
   nano /work/tesi_ediluzio/docs/project_status_for_agent.md
   ```

2. **Aggiorna il log delle run su Weights & Biands**:
   ```bash
   nano /work/tesi_ediluzio/wandb_runs_log.md
   ```

3. **Aggiorna i file di documentazione dei parametri**:
   ```bash
   nano /work/tesi_ediluzio/docs/training_parameters.md
   nano /work/tesi_ediluzio/docs/lora_parameters.md
   ```

## Risorse Importanti

### Directory Principali
- Root: `/work/tesi_ediluzio/`
- Esperimenti: `/work/tesi_ediluzio/experiments/xml_direct_input/`
- Dataset: `/work/tesi_ediluzio/data/processed/xml_format/`
- Output: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/`
- Configurazioni: `/work/tesi_ediluzio/experiments/xml_direct_input/configs/`
- Log: `/work/tesi_ediluzio/logs/`
- Documentazione: `/work/tesi_ediluzio/docs/`

### File di Configurazione
- Llama: `/work/tesi_ediluzio/experiments/xml_direct_input/configs/llama31_8b_test3_multi_gpu.json`
- Gemma: `/work/tesi_ediluzio/experiments/xml_direct_input/configs/gemma2_9b_it_test3_multi_gpu.json`

### Script Principali
- Training: `/work/tesi_ediluzio/scripts/training/train_lora_multi_gpu_simple.py`
- Inferenza: `/work/tesi_ediluzio/scripts/inference/run_inference_lora.py`
- Valutazione: `/work/tesi_ediluzio/scripts/evaluation/evaluate_models_with_clip.py`
- Captioner Esterni: `/work/tesi_ediluzio/scripts/evaluation/run_external_captioners_prerendered.py`

### Script SLURM
- Llama: `/work/tesi_ediluzio/scripts/slurm/run_llama_test3_multi_gpu.slurm`
- Gemma: `/work/tesi_ediluzio/scripts/slurm/run_gemma_test3_multi_gpu.slurm`
- Test SVG: `/work/tesi_ediluzio/scripts/slurm/run_test_svg.slurm`
- Captioner Esterni: `/work/tesi_ediluzio/scripts/slurm/run_external_captioners_prerendered.slurm`

### Documentazione
- Parametri di Training: `/work/tesi_ediluzio/docs/training_parameters.md`
- Parametri LoRA: `/work/tesi_ediluzio/docs/lora_parameters.md`
- Stato del Progetto: `/work/tesi_ediluzio/docs/project_status.md`
- Captioner Esterni e CLIP: `/work/tesi_ediluzio/docs/external_captioners_and_clip.md`

### Credenziali
- Token Hugging Face: `*************************************`
- API key Weights & Biands: `6006c12f16afe29f1402ea7340dadad0cf62b347`

## Funzionalità Implementate

1. **Rimozione Gradient Accumulation** ✅
2. **Mitigazione Overfitting** ✅
3. **Aumento Parametri LoRA** ✅
4. **Funzione Conteggio Parametri** ✅
5. **Riduzione Learning Rate** ✅
6. **Gestione Checkpoint** ✅
7. **Training su Dataset Completo** ✅
8. **Early Stopping su Validazione** ✅
9. **Captioner Esterni e CLIP** ✅

## Note Finali
- Il progetto è in fase avanzata con tutte le funzionalità richieste implementate
- I job di training sono in attesa di risorse, mentre i job di valutazione sono in esecuzione
- La valutazione con CLIP Score è stata implementata e sta venendo eseguita
- L'utente preferisce un approccio autonomo e proattivo
- Aggiornare regolarmente questo documento con lo stato più recente del progetto
