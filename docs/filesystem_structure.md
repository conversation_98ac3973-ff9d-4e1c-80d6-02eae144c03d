# Struttura del Filesystem del Progetto

Data: 22/04/2025 14:04:06

Directory radice: `/work/tesi_ediluzio`

Dimensione totale: 93G

## Struttura

  - **.cache/** (54G)
    - **huggingface/** (54G)
      - **datasets/** (21G)
      - **hub/** (33G)
  - **.vscode/** (1.0K)
    - settings.json (84 B)
  - **.vscode-server/** (0)
  - **data/** (1.8G)
    - **processed/** (1.8G)
      - **finetuning/** (0)
      - **xml_format/** (157M)
      - README.md (1.1 KB)
      - filtered_bw_len512_ALL.json (119.2 MB)
      - filtered_svg_all_sources_FULL.json (1.3 GB)
      - test_set_final_2k.json (1.0 MB)
      - train_set_final.json (118.2 MB)
  - **data_preparation/** (39K)
    - analyze_svg_dataset.py (11.6 KB)
    - convert_to_xml.py (3.1 KB)
    - create_filtered_xml_dataset.py (10.1 KB)
    - prepare_finetuning_data.py (4.9 KB)
    - split_dataset.py (6.7 KB)
  - **decoder_only/** (78K)
    - **slurm_logs/** (3.5K)
      - slurm_train_dec_2549024.err (1.6 KB)
      - slurm_train_dec_2549024.out (958 B)
    - **svg_captioning/** (55K)
      - __init__.py (0 B)
      - dpo_slurm.sh (3.3 KB)
      - dpo_trainer.py (17.3 KB)
      - slurm_training_config.json (1.2 KB)
      - train_dpo.py (13.7 KB)
      - train_model_slurm.py (12.7 KB)
      - train_slurm.sh (4.3 KB)
    - **training_plots/** (0)
    - __init__.py (0 B)
    - final_training_config.json (670 B)
    - main_direct.py (3.9 KB)
    - svg_direct_captioning_pipeline.py (9.1 KB)
    - test_direct_decoder_only.py (4.7 KB)
  - **evaluation/** (24K)
    - **comparisons/** (0)
    - **metrics/** (0)
    - **reports/** (0)
    - README.md (2.1 KB)
    - compare_models.py (6.7 KB)
    - evaluate_captions.py (5.2 KB)
    - visualize_results.py (7.8 KB)
  - **experiments/** (25G)
    - **xml_direct_input/** (25G)
      - **configs/** (59K)
      - **generated_svgs/** (2.0M)
      - **generated_svgs_from_chunks/** (0)
      - **inference_results/** (7.5K)
      - **outputs/** (25G)
      - **utils/** (8.5K)
      - README.md (1.5 KB)
      - check_lora_targets.py (3.9 KB)
      - check_lora_targets.slurm (3.4 KB)
      - compare_models.py (4.8 KB)
      - compare_zero_shot_vs_lora.py (4.3 KB)
      - evaluate_captions.py (5.5 KB)
      - evaluate_captions_metrics.py (17.6 KB)
      - generate_comprehensive_report.py (15.1 KB)
      - generate_qualitative_report.py (11.2 KB)
      - generate_report.py (8.3 KB)
      - optimize_lora_config.py (11.0 KB)
      - prompt_templates.py (5.9 KB)
      - run_evaluation.slurm (2.4 KB)
      - run_evaluation_metrics.slurm (2.9 KB)
      - run_gemma2_9b_it_custom_token_convergence.slurm (3.4 KB)
      - run_gemma2_9b_it_custom_token_convergence_after_base.slurm (3.9 KB)
      - run_gemma2_9b_it_no_token_convergence.slurm (3.3 KB)
      - run_gemma2_9b_it_no_token_convergence_resume.slurm (3.5 KB)
      - run_inference.py (12.1 KB)
      - run_inference.slurm (3.9 KB)
      - run_inference_base.slurm (4.4 KB)
      - run_inference_base_xml.py (14.6 KB)
      - run_inference_gemma2_9b_it_custom_token.slurm (2.2 KB)
      - run_inference_gemma2_9b_it_no_token.slurm (2.2 KB)
      - run_inference_llama31_8b_custom_token.slurm (2.2 KB)
      - run_inference_llama31_8b_no_token.slurm (2.2 KB)
      - run_inference_lora.py (10.1 KB)
      - run_inference_lora.slurm (2.8 KB)
      - run_inference_unified.py (10.1 KB)
      - run_inference_unified.slurm (2.0 KB)
      - run_inference_unified_quick.py (9.2 KB)
      - run_llama31_8b_custom_token_convergence.slurm (3.4 KB)
      - run_llama31_8b_custom_token_convergence_after_base.slurm (3.9 KB)
      - run_llama31_8b_no_token_convergence.slurm (3.3 KB)
      - run_llama31_8b_no_token_convergence_resume.slurm (3.5 KB)
      - run_lora_inference_xml_eval.slurm (4.2 KB)
      - run_lora_training_wandb.slurm (3.9 KB)
      - run_qualitative_report.slurm (3.2 KB)
      - run_zero_shot_inference.py (12.1 KB)
      - train_lora_wandb.py (17.0 KB)
      - train_xml_base.slurm (2.5 KB)
      - train_xml_slurm.py (13.3 KB)
      - train_xml_slurm_token_xml.py (14.1 KB)
      - visualize_captions.py (4.3 KB)
  - **logs/** (639K)
    - **archive/** (41K)
      - logs_archive_20250420.tar.gz (12.7 KB)
      - old_logs.tar.gz (26.8 KB)
    - base_eval_2560145.err (868 B)
    - gemma2_9b_it_custom_token_convergence_2560805.err (390 B)
    - gemma2_9b_it_custom_token_convergence_2560819.err (2.8 KB)
    - gemma2_9b_it_custom_token_convergence_2560852.err (2.2 KB)
    - gemma2_9b_it_custom_token_convergence_2561639.err (37.9 KB)
    - gemma2_9b_it_custom_token_convergence_2561639.out (1.1 KB)
    - gemma2_9b_it_no_token_convergence_2560806.err (390 B)
    - gemma2_9b_it_no_token_convergence_2560832.err (3.5 KB)
    - gemma2_9b_it_no_token_convergence_2560854.err (3.5 KB)
    - gemma2_9b_it_no_token_convergence_2560863.err (2.1 KB)
    - gemma2_9b_it_no_token_convergence_2561103.err (3.3 KB)
    - gemma2_9b_it_no_token_convergence_2561108.err (3.6 KB)
    - gemma2_9b_it_no_token_convergence_2561115.err (20.4 KB)
    - gemma2_9b_it_no_token_convergence_2561119.err (24.6 KB)
    - gemma2_9b_it_no_token_convergence_2561124.err (3.2 KB)
    - gemma2_9b_it_no_token_convergence_2561126.err (3.2 KB)
    - gemma2_9b_it_no_token_convergence_2561129.err (6.5 KB)
    - gemma2_9b_it_no_token_convergence_2561158.err (13.3 KB)
    - gemma2_9b_it_no_token_convergence_2561158.out (1.0 KB)
    - gemma2_9b_it_no_token_convergence_2561161.err (40.9 KB)
    - gemma2_9b_it_no_token_convergence_2561161.out (1.0 KB)
    - gemma2_9b_it_no_token_convergence_2561256.err (5.7 KB)
    - gemma2_9b_it_no_token_convergence_2561256.out (1.0 KB)
    - gemma2_9b_it_no_token_convergence_2561260.err (2.8 KB)
    - gemma2_9b_it_no_token_convergence_2561260.out (1.0 KB)
    - gemma2_9b_it_no_token_convergence_2561262.err (2.6 KB)
    - gemma2_9b_it_no_token_convergence_2561262.out (1.0 KB)
    - gemma2_9b_it_no_token_convergence_2561264.err (2.7 KB)
    - gemma2_9b_it_no_token_convergence_2561264.out (1.0 KB)
    - gemma2_9b_it_no_token_convergence_2561266.err (51.3 KB)
    - gemma2_9b_it_no_token_convergence_2561266.out (1.0 KB)
    - gemma2_9b_it_no_token_convergence_resume_2561748.err (7.6 KB)
    - gemma2_9b_it_no_token_convergence_resume_2561748.out (1.2 KB)
    - infer_gemma2_9b_it_no_token_2561641.err (309 B)
    - infer_gemma2_9b_it_no_token_2561641.out (429 B)
    - infer_gemma2_9b_it_no_token_2561736.err (145 B)
    - infer_gemma2_9b_it_no_token_2561736.out (429 B)
    - infer_gemma2_9b_it_no_token_2561742.err (0 B)
    - infer_gemma2_9b_it_no_token_2561742.out (429 B)
    - infer_llama31_8b_no_token_2561640.err (309 B)
    - infer_llama31_8b_no_token_2561640.out (437 B)
    - infer_llama31_8b_no_token_2561734.err (145 B)
    - infer_llama31_8b_no_token_2561734.out (437 B)
    - infer_llama31_8b_no_token_2561741.err (0 B)
    - infer_llama31_8b_no_token_2561741.out (437 B)
    - llama31_8b_custom_token_convergence_2560803.err (390 B)
    - llama31_8b_custom_token_convergence_2560810.err (3.2 KB)
    - llama31_8b_custom_token_convergence_2560846.err (2.2 KB)
    - llama31_8b_custom_token_convergence_2561638.err (41.4 KB)
    - llama31_8b_custom_token_convergence_2561638.out (1.1 KB)
    - llama31_8b_no_token_convergence_2560804.err (390 B)
    - llama31_8b_no_token_convergence_2560811.err (3.5 KB)
    - llama31_8b_no_token_convergence_2560849.err (1.9 KB)
    - llama31_8b_no_token_convergence_2560862.err (2.1 KB)
    - llama31_8b_no_token_convergence_2561102.err (4.7 KB)
    - llama31_8b_no_token_convergence_2561107.err (3.8 KB)
    - llama31_8b_no_token_convergence_2561114.err (20.4 KB)
    - llama31_8b_no_token_convergence_2561118.err (24.6 KB)
    - llama31_8b_no_token_convergence_2561123.err (3.2 KB)
    - llama31_8b_no_token_convergence_2561125.err (3.2 KB)
    - llama31_8b_no_token_convergence_2561127.err (6.8 KB)
    - llama31_8b_no_token_convergence_2561136.err (22.6 KB)
    - llama31_8b_no_token_convergence_2561137.err (2.8 KB)
    - llama31_8b_no_token_convergence_2561157.err (4.7 KB)
    - llama31_8b_no_token_convergence_2561157.out (1.3 KB)
    - llama31_8b_no_token_convergence_2561160.err (27.6 KB)
    - llama31_8b_no_token_convergence_2561160.out (1.0 KB)
    - llama31_8b_no_token_convergence_2561254.err (6.2 KB)
    - llama31_8b_no_token_convergence_2561254.out (1.0 KB)
    - llama31_8b_no_token_convergence_2561259.err (2.6 KB)
    - llama31_8b_no_token_convergence_2561259.out (1.0 KB)
    - llama31_8b_no_token_convergence_2561261.err (2.3 KB)
    - llama31_8b_no_token_convergence_2561261.out (1.0 KB)
    - llama31_8b_no_token_convergence_2561263.err (2.4 KB)
    - llama31_8b_no_token_convergence_2561263.out (1.0 KB)
    - llama31_8b_no_token_convergence_2561265.err (64.0 KB)
    - llama31_8b_no_token_convergence_2561265.out (1.0 KB)
    - llama31_8b_no_token_convergence_resume_2561747.err (9.8 KB)
    - llama31_8b_no_token_convergence_resume_2561747.out (1.2 KB)
    - lora_eval_2560048_lora_eval.err (3.7 KB)
    - lora_eval_2560049_lora_eval.err (3.4 KB)
    - lora_eval_2560050_lora_eval.err (3.5 KB)
    - lora_eval_2560051_lora_eval.err (3.3 KB)
    - lora_eval_2560058_lora_eval.err (3.5 KB)
    - lora_eval_2560059_lora_eval.err (3.3 KB)
    - lora_wandb_2561151_lora_wandb.err (588 B)
    - lora_wandb_2561151_lora_wandb.out (1.2 KB)
    - lora_wandb_2561152_lora_wandb.err (588 B)
    - lora_wandb_2561152_lora_wandb.out (1.2 KB)
    - lora_wandb_2561153_lora_wandb.err (588 B)
    - lora_wandb_2561153_lora_wandb.out (1.2 KB)
    - lora_wandb_2561154_lora_wandb.err (588 B)
    - lora_wandb_2561154_lora_wandb.out (1.2 KB)
    - lora_wandb_2561155_lora_wandb.err (621 B)
    - lora_wandb_2561155_lora_wandb.out (1.2 KB)
    - lora_wandb_2561156_lora_wandb.err (621 B)
    - lora_wandb_2561156_lora_wandb.out (1.2 KB)
    - manual_eval_2560116.err (2.3 KB)
    - setup_wandb_2560794.err (302 B)
  - **models/** (512)
    - **nemotron_4b_lora_xml_custom_token_final/** (0)
    - **nemotron_4b_lora_xml_custom_token_fix/** (0)
  - **results/** (1.2M)
    - **lora/** (5.5K)
      - nemotron_4b_lora_quick_test2.jsonl (4.8 KB)
    - **lora_xml/** (14K)
      - gemma2_9b_it_lora_xml_custom_token_eval_test.jsonl (176 B)
      - gemma2_9b_it_lora_xml_eval_test.jsonl (147 B)
      - llama31_8b_base_eval_test_manual.jsonl (4.0 KB)
      - llama31_8b_lora_xml_custom_token_eval_test.jsonl (175 B)
      - llama31_8b_lora_xml_eval_test.jsonl (141 B)
      - nemotron_4b_lora_xml_test.jsonl (5.0 KB)
      - test.txt (0 B)
      - test_env.jsonl (56 B)
      - test_output.jsonl (56 B)
    - **metrics/** (512)
      - **lora/** (0)
      - **lora_xml/** (0)
      - **zero_shot/** (0)
      - **zero_shot_xml/** (0)
    - **zero_shot/** (1.1M)
      - deepseek_r1_8b_results.jsonl (1.1 MB)
      - gemma2_9b_it_quick_test2.jsonl (2.9 KB)
      - llama31_8b_quick_test2.jsonl (2.7 KB)
      - llama32_3b_quick_test2.jsonl (3.7 KB)
      - mistral_7b_v03_quick_test2.jsonl (3.7 KB)
    - **zero_shot_xml/** (16K)
      - gemma2_9b_it_xml_test.jsonl (2.6 KB)
      - llama31_8b_xml_test.jsonl (3.1 KB)
      - llama32_3b_xml_test.jsonl (4.0 KB)
      - mistral_7b_v03_xml_test.jsonl (4.0 KB)
    - preliminary_report.md (5.2 KB)
    - report.md (2.8 KB)
  - **shared/** (220K)
    - **svg_core/** (109K)
      - README.md (1.5 KB)
      - __init__.py (0 B)
      - custom_tokenizer_utils.py (4.2 KB)
      - evaluation.py (5.1 KB)
      - generate_candidates.py (19.3 KB)
      - nemotron_model.py (11.5 KB)
      - reward_model.py (7.6 KB)
      - svg_direct_tokenizer.py (17.6 KB)
      - svg_vector_processor.py (17.6 KB)
      - text_generation.py (6.4 KB)
      - train_model_slurm.py (11.3 KB)
    - **utils/** (95K)
      - __init__.py (689 B)
      - attention_visualization.py (22.6 KB)
      - checkpoint_manager.py (20.2 KB)
      - logging_config.py (1.0 KB)
      - logging_utils.py (22.0 KB)
      - metrics.py (7.7 KB)
      - visualization.py (18.2 KB)
    - __init__.py (0 B)
    - install_dependencies.sh (1.7 KB)
    - monitor_training.py (11.1 KB)
    - requirements.txt (675 B)
  - **utils/** (21K)
    - README.md (724 B)
    - archive_logs.sh (1.7 KB)
    - archive_logs.slurm (0 B)
    - backup_data.sh (1.8 KB)
    - cleanup.py (5.8 KB)
    - disk_usage_simple.sh (598 B)
    - logs_cron (120 B)
    - monitor_disk_usage.sh (1016 B)
    - monitor_jobs.py (5.7 KB)
    - monitor_jobs.sh (832 B)
  - .env (47 B)
  - FILESYSTEM_STRUCTURE.md (5.4 KB)
  - README.md (4.5 KB)
  - ROADMAP.md (6.1 KB)
  - __init__.py (0 B)
  - bash.sh (131 B)
  - check_xml.py (1.5 KB)
  - check_xml_validity.py (1.5 KB)
  - chunks_archive.tar.gz (147.0 MB)
  - cleanup_files.sh (2.9 KB)
  - cleanup_temp_files.py (7.7 KB)
  - compare_results.py (2.0 KB)
  - create_example_files.sh (1.3 KB)
  - document_filesystem.py (5.2 KB)
  - filesystem_structure.md (0 B)
  - generate_preliminary_report.py (15.9 KB)
  - generate_report.py (3.4 KB)
  - monitor_training_progress.py (12.3 KB)
  - plot_learning_curves.py (8.1 KB)
  - prepare_test_data.py (4.8 KB)
  - requirements.txt (410 B)
  - run_inference.sh (833 B)
  - run_inference_shell.sh (752 B)
  - run_workflow.py (19.0 KB)
  - run_workflow.slurm (2.4 KB)
  - setup_hf_auth.py (1.1 KB)
  - setup_wandb.py (2.8 KB)
  - setup_wandb.slurm (1.4 KB)
  - test_env.py (1.5 KB)
  - test_inference.py (3.8 KB)
  - test_inference_base.py (3.0 KB)
  - test_inference_custom.py (4.0 KB)
  - test_model_load.py (2.7 KB)
  - test_write.py (1014 B)
