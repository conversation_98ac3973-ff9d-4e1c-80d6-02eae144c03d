# 🚀 PIANO COMPLETO TEST 5 - SVG CAPTIONING

## 📋 **OVERVIEW**

**Obiettivo**: Implementare la FASE 5 del progetto con nuovi esperimenti utilizzando dataset ottimizzato e parametri migliorati per confronto LLAMA vs GEMMA.

**Data**: Dicembre 2024
**Status**: ✅ SETUP COMPLETATO - PRONTO PER TRAINING

---

## 🎯 **OBIETTIVI TEST 5**

### **📊 Dataset Esistente Ottimizzato**
- ✅ **230.946 esempi training** + **200 esempi test**
- ✅ **Lunghezze controllate**: 212-723 caratteri (media 474)
- ✅ **Context window**: 1500 token (vs 256 precedente)
- ✅ **Qualità**: Dataset già filtrato e ottimizzato

### **🔧 Parametri Training Migliorati**
- ✅ **Max steps**: 25.000 (vs epoche)
- ✅ **Batch size**: 8 per GPU (vs 4)
- ✅ **Early stopping**: patience=10, min_delta=0.001
- ✅ **LoRA**: r=16, alpha=32 (vs r=8, alpha=16)
- ✅ **Quantizzazione**: 4-bit con double_quant=true

### **📈 Monitoraggio Avanzato**
- ✅ **W&B**: Progetto dedicato "svg_captioning_test5"
- ✅ **Checkpoint**: Ogni 150 step
- ✅ **Valutazione**: Ogni epoca
- ✅ **Salvataggio**: Solo best model

---

## 📁 **STRUTTURA FILE CREATI**

### **🗂️ Dataset (data/processed/xml_format/)**
```
✅ train_set_final_xml_augmented.json    (230.946 esempi, 157 MB)
✅ test_set_final_xml_reduced_rgb.json   (200 esempi, 142 KB)
✅ Dataset già ottimizzato e filtrato
```

### **⚙️ Configurazioni (experiments/xml_direct_input/configs/)**
```
✅ llama31_8b_test5_multi_gpu.json
✅ gemma2_9b_it_test5_multi_gpu.json
```

### **🖥️ Script SLURM (scripts/slurm/)**
```
✅ run_llama_test5.slurm
✅ run_gemma_test5.slurm
```

### **📊 Script Preparazione (data_preparation/)**
```
✅ create_new_dataset_test5_optimized.py
```

---

## 🔧 **CONFIGURAZIONI TECNICHE**

### **🦙 LLAMA 3.1 8B TEST 5**
```json
{
    "model_name_or_path": "meta-llama/Llama-3.1-8B-Instruct",
    "output_dir": "experiments/xml_direct_input/outputs/llama31_8b_test5_multi_gpu",
    "data_file": "data/processed/xml_format/train_set_final_xml_augmented.json",
    "val_file": "data/processed/xml_format/test_set_final_xml_reduced_rgb.json",
    "per_device_train_batch_size": 8,
    "max_steps": 25000,
    "max_length": 1500,
    "lora_r": 16,
    "lora_alpha": 32,
    "load_in_4bit": true,
    "early_stopping_patience": 10
}
```

### **💎 GEMMA 2 9B IT TEST 5**
```json
{
    "model_name_or_path": "google/gemma-2-9b-it",
    "output_dir": "experiments/xml_direct_input/outputs/gemma2_9b_it_test5_multi_gpu",
    "data_file": "data/processed/xml_format/train_set_final_xml_augmented.json",
    "val_file": "data/processed/xml_format/test_set_final_xml_reduced_rgb.json",
    "per_device_train_batch_size": 8,
    "max_steps": 25000,
    "max_length": 1500,
    "lora_r": 16,
    "lora_alpha": 32,
    "load_in_4bit": true,
    "early_stopping_patience": 10
}
```

---

## 📊 **STATISTICHE DATASET TEST 5**

### **🔢 Numeri Chiave**
- **Training set**: 230.946 esempi
- **Test set**: 200 esempi
- **Range caratteri**: 212-723 caratteri
- **Media caratteri**: 474.1 caratteri
- **Mediana caratteri**: 483.0 caratteri
- **Dataset**: Già filtrato e ottimizzato

### **📈 Miglioramenti vs TEST 3**
| Parametro | TEST 3 | TEST 5 | Miglioramento |
|-----------|--------|--------|---------------|
| Context Window | 256 | 1500 | +485% |
| Batch Size | 4 | 8 | +100% |
| LoRA r | 8 | 16 | +100% |
| LoRA alpha | 16 | 32 | +100% |
| Dataset Size | ~50k | 230k | Aumentato |
| Char Range | Vario | 212-723 | Controllato |

---

## 🚀 **COMANDI ESECUZIONE**

### **📦 Preparazione Dataset**
```bash
# Dataset già creato ✅
cd /work/tesi_ediluzio
python data_preparation/create_new_dataset_test5_optimized.py
```

### **🦙 Training LLAMA TEST 5**
```bash
sbatch scripts/slurm/run_llama_test5.slurm
```

### **💎 Training GEMMA TEST 5**
```bash
sbatch scripts/slurm/run_gemma_test5.slurm
```

### **📊 Monitoraggio**
```bash
# SLURM
squeue -u $USER
tail -f logs/llama_test5_*.out
tail -f logs/gemma_test5_*.out

# W&B
# https://wandb.ai/337543-unimore/svg_captioning_test5
```

---

## 🎯 **RISULTATI ATTESI**

### **📈 Performance Target**
- **Convergenza**: Entro 15.000-20.000 step
- **Loss finale**: < 1.0
- **BLEU-4**: > 0.80 (vs 0.78 TEST 3)
- **CLIP Score**: > 0.72 (vs 0.71 TEST 3)

### **⚡ Efficienza**
- **Training time**: ~12-16 ore per modello
- **Memory usage**: ~40GB per GPU
- **Checkpoint size**: ~50MB (solo adapter)

### **🏆 Confronto Modelli**
- **GEMMA vs LLAMA**: Confronto diretto su stesso dataset
- **Context window**: Impact dell'aumento a 1500 token
- **Batch size**: Effetto del raddoppio su convergenza

---

## ✅ **CHECKLIST COMPLETAMENTO**

### **🔧 Setup (COMPLETATO)**
- [x] Dataset nuovo creato e validato
- [x] Configurazioni JSON aggiornate
- [x] Script SLURM preparati
- [x] Ambiente verificato
- [x] W&B configurato

### **🚀 Esecuzione (PROSSIMO)**
- [ ] Avvio training LLAMA TEST 5
- [ ] Avvio training GEMMA TEST 5
- [ ] Monitoraggio convergenza
- [ ] Salvataggio checkpoint finali

### **📊 Valutazione (FUTURO)**
- [ ] Calcolo metriche standard
- [ ] Confronto con TEST 3
- [ ] Analisi qualitativa
- [ ] Report finale

---

## 🔗 **RIFERIMENTI**

- **Progetto W&B**: `svg_captioning_test5`
- **Directory output**: `experiments/xml_direct_input/outputs/`
- **Dataset**: `data/new_dataset/`
- **Configurazioni**: `experiments/xml_direct_input/configs/`
- **Script**: `scripts/slurm/`

---

**🎯 READY TO LAUNCH! 🚀**
