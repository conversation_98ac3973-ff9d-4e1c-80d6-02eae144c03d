# Stato Attuale del Progetto SVG Captioning (12/05/2025)

## Panoramica

Questo documento descrive lo stato attuale del progetto di SVG Captioning, che mira a generare didascalie descrittive per immagini SVG utilizzando modelli di linguaggio di grandi dimensioni (LLM). Il progetto è parte di una tesi di master che esplora l'efficacia di diverse architetture di modelli e tecniche di fine-tuning per questo compito specifico.

## Modelli in Fase di Training

Attualmente, stiamo addestrando quattro configurazioni principali di modelli:

### 1. Modelli con Tokenizer Standard (Completati)

- **Llama 3.1 8B**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence/`
  - Checkpoint finale: 4000
  - Loss finale: 0.4776
  - Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
  - Parametri addestrabili: 41,943,040 (0.5196% del totale)

- **Gemma 2 9B IT**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence/`
  - Checkpoint finale: 3000
  - Loss finale: 0.317
  - Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
  - Parametri addestrabili: 54,018,048 (0.5811% del totale)

### 2. Modelli con Tokenizer Personalizzato (Completati)

- **Llama 3.1 8B**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token/`
  - Checkpoint finale: 1800
  - Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
  - Tokenizer personalizzato con 17 token speciali per SVG
  - Parametri addestrabili: 41,943,040 (0.5196% del totale)

- **Gemma 2 9B IT**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_custom_token/`
  - Checkpoint finale: 2400
  - Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
  - Tokenizer personalizzato con 17 token speciali per SVG
  - Parametri addestrabili: 54,018,048 (0.5811% del totale)

### 3. Modelli con Dataset Diviso in Fasce (Completati)

- **Llama 3.1 8B**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1/`
  - Checkpoint finale: 525
  - Loss finale: 0.4352
  - Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
  - Batch size: 2, Gradient accumulation: 4
  - Parametri addestrabili: 167,772,160 (3.5633% del totale)

- **Gemma 2 9B IT**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1/`
  - Checkpoint finale: 525
  - Loss finale: 0.3005
  - Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
  - Batch size: 1, Gradient accumulation: 8, max_length: 768
  - Parametri addestrabili: 216,072,192 (4.0799% del totale)

### 4. Modelli Ottimizzati per Convergenza (Completati)

- **Llama 3.1 8B**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/`
  - Checkpoint finale: 4000
  - Loss finale: 0.9478
  - Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
  - Learning rate: 1e-4 (ridotto rispetto ai precedenti)
  - Epoche: 10 (aumentate rispetto ai precedenti)
  - Valutazione ogni 50 step
  - Early stopping con patience 30
  - Tracciamento su Weights & Biands: llama31_8b_test1_convergence

- **Gemma 2 9B IT**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence/`
  - Checkpoint finale: 4000
  - Loss finale: 0.0251
  - Configurazione: LoRA con rank=64, alpha=16, dropout=0.1
  - Learning rate: 1e-4 (ridotto rispetto ai precedenti)
  - Epoche: 10 (aumentate rispetto ai precedenti)
  - Valutazione ogni 50 step
  - Early stopping con patience 30
  - Tracciamento su Weights & Biands: gemma2_9b_it_test1_convergence

### 5. Modelli Multi-GPU con Ottimizzazioni Avanzate (In Corso)

- **Llama 3.1 8B Multi-GPU**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test2_multi_gpu/`
  - Job SLURM: 2577378
  - Configurazione: LoRA con rank=128, alpha=256, dropout=0.1
  - Learning rate: 5e-5 (ridotto per stabilizzare il training)
  - Batch size: 16 per GPU (2 GPU)
  - Gradient accumulation: 1 (rimosso per prevenire overfitting precoce)
  - DeepSpeed: Configurato per ottimizzare l'uso della memoria
  - Early stopping: patience=100 sulla loss di validazione
  - Tracciamento su Weights & Biands: test2_llama31_8b_151
  - Stato: Riavviato da zero (checkpoint corrotto)

- **Gemma 2 9B IT Multi-GPU**:
  - Path: `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test2_multi_gpu/`
  - Job SLURM: 2577377
  - Nodo: "nullazzo"
  - Configurazione: LoRA con rank=128, alpha=256, dropout=0.1
  - Learning rate: 3e-5 (ridotto per stabilizzare il training)
  - Batch size: 16 per GPU (2 GPU)
  - Gradient accumulation: 1 (rimosso per prevenire overfitting precoce)
  - DeepSpeed: Configurato per ottimizzare l'uso della memoria
  - Early stopping: patience=100 sulla loss di validazione
  - Tracciamento su Weights & Biands: test2_gemma2_9b_it_153
  - Stato: Continua dal checkpoint 5200

## Pipeline Completa Standardizzata

**IMPORTANTE: È stata definita una pipeline completa standardizzata che DEVE essere seguita per ogni ciclo di training e valutazione.**

La pipeline completa è documentata in dettaglio nel file [PIPELINE_COMPLETA.md](/docs/PIPELINE_COMPLETA.md) e include:

1. **Preparazione e Training**:
   - Configurazione dei job SLURM
   - Aggiornamento del log delle run
   - Monitoraggio su Weights & Biands

2. **Valutazione**:
   - Configurazione dei job di valutazione
   - Calcolo delle metriche (BLEU, METEOR, CIDEr, CLIP Score)

3. **Generazione di Report e Grafici**:
   - Creazione di report HTML con esempi qualitativi
   - Generazione di grafici radar per confrontare le metriche
   - Aggiornamento della documentazione

4. **Pulizia e Manutenzione**:
   - Gestione dei checkpoint
   - Aggiornamento dei log

**NOTA: Dopo ogni training, è OBBLIGATORIO generare il report HTML e i grafici radar utilizzando lo script `./scripts/generate_report.sh`.**

## Miglioramenti Recenti

### 1. Ottimizzazione del Training

- **Monitoraggio dell'Overfitting**: Implementato tracciamento parallelo di training loss e validation loss
- **Grafici Comparativi**: Generazione automatica di grafici di confronto train/eval loss
- **Metriche di Convergenza**: Calcolo della pendenza della curva di loss per determinare la convergenza
- **Rilevazione Automatica dell'Overfitting**: Quando eval_loss > train_loss * 1.1

### 2. Gestione dello Spazio Disco

- **Pulizia Checkpoint**: Rimossi checkpoint intermedi dai modelli principali
- **Callback per Pulizia Automatica**: Implementato CheckpointCleanupCallback per eliminare automaticamente i checkpoint vecchi
- **Spazio Liberato**: Circa 37GB attraverso la rimozione di checkpoint intermedi e file non necessari (52% dello spazio totale)
- **Riorganizzazione Filesystem**: Creata struttura di directory più organizzata con sottodirectory dedicate per tipo di file

### 3. Integrazione con Weights & Biands

- **Tracciamento Avanzato**: Implementato WandbCallback migliorato per tracciare metriche di training e validation
- **Visualizzazione in Tempo Reale**: Grafici di confronto train/eval loss aggiornati durante il training
- **Metriche di Overfitting**: Calcolo del gap tra training e validation loss e del rapporto di overfitting

### 4. Sistema di Valutazione dei Checkpoint

- **Valutazione Completa**: Implementato sistema per valutare i checkpoint con metriche multiple
- **Metriche di Qualità**: BLEU-1/2/3/4, METEOR, CIDEr, CLIP Score
- **Metriche di Efficienza**: Tempo di inferenza, Perplexity
- **Metriche di Analisi Linguistica**: Lunghezza didascalie, Diversità vocabolario, Type-Token Ratio, Self-BLEU
- **Monitoraggio Automatico**: Script per monitorare e valutare automaticamente i nuovi checkpoint
- **Visualizzazione Avanzata**: Grafici comparativi, radar chart, tabelle di confronto

### 5. Preparazione per Training con Pesi Convergenti

- **Configurazioni Avanzate**: Preparate configurazioni per training con pesi pre-convergenti e tokenizer personalizzato
- **Monitoraggio della Convergenza**: Script per rilevare automaticamente quando i modelli raggiungono la convergenza
- **Avvio Automatico**: Sistema per avviare automaticamente il training successivo utilizzando i pesi convergenti
- **Valutazione Integrata**: Valutazione automatica dei modelli al termine del training

## Prossimi Passi

### 1. Completare Training Multi-GPU con Ottimizzazioni Avanzate

- Monitorare i job in corso fino al completamento (Job IDs: 2578150, 2578151)
- Analizzare le curve di loss per identificare overfitting/underfitting
- Verificare l'impatto delle ottimizzazioni per evitare OOM (rank=4, alpha=8)
- Valutare l'efficacia della configurazione con 2 GPU e batch size 4
- Valutare i checkpoint con le metriche implementate
- Nota: Entrambi i job sono stati riavviati da zero con configurazione ottimizzata

### 2. Analisi dei Risultati dei Captioner Esterni

- ✅ Job di test dei captioner esterni completato (Job ID: 2577381)
- ⚠️ Solo il modello BLIP ha generato risultati validi
- ❌ I modelli vit-gpt2 e cogvlm hanno fallito la generazione di didascalie
- Confrontare le performance del modello BLIP con i modelli fine-tuned
- Analizzare le didascalie generate dal modello BLIP
- Valutare l'impatto del rendering SVG sulla qualità delle didascalie
- Integrare i risultati nei report di valutazione
- Investigare i problemi con vit-gpt2 e cogvlm

### 3. Valutazione Comparativa dei Checkpoint

- Confrontare le performance dei checkpoint con diverse metriche
- Identificare i migliori checkpoint per ciascun modello
- Analizzare l'evoluzione delle metriche durante il training
- Valutare l'impatto del tokenizer personalizzato e della divisione in fasce

### 4. Analisi Approfondita delle Didascalie

- Analizzare la qualità linguistica delle didascalie generate
- Valutare la diversità del vocabolario e la lunghezza delle didascalie
- Confrontare i tempi di inferenza e la perplexity tra i diversi modelli
- Generare report con esempi di didascalie per ciascun modello

### 5. Inferenza e Generazione di Didascalie

- Generare didascalie con i migliori checkpoint identificati
- Valutare la qualità delle didascalie con metriche standard e avanzate
- Creare una demo interattiva per la generazione di didascalie
- Creare pagine HTML con esempi di inferenza per i modelli
- **IMPORTANTE**: Utilizzare lo script `./scripts/generate_report.sh` per generare report HTML e grafici radar

### 6. Analisi Finale

- Preparare report con confronto tra i diversi approcci
- Documentare le best practices identificate
- Creare visualizzazioni comparative delle performance dei modelli
- Creare una presentazione con i risultati principali

## Monitoraggio dei Job

I job di training possono essere monitorati in diversi modi:

### 1. SLURM

```bash
# Visualizza i job in esecuzione
squeue -u $USER

# Visualizza i dettagli di un job specifico
sacct -j JOBID --format=JobID,JobName,State,Elapsed,NodeList,ExitCode
```

### 2. Log

```bash
# Visualizza i log di output
cat /work/tesi_ediluzio/logs/llama_test1_conv_JOBID.out

# Visualizza i log di errore
cat /work/tesi_ediluzio/logs/llama_test1_conv_JOBID.err

# Visualizza i log del monitoraggio della convergenza
tail -f /work/tesi_ediluzio/logs/convergence_monitor_*.log
```

### 3. Weights & Biands

I progressi del training possono essere monitorati in tempo reale su Weights & Biands:
- Entity: 337543-unimore
- Project: captioner
- Run names:
  - llama31_8b_test1_convergence
  - gemma2_9b_it_test1_convergence
  - llama31_8b_custom_token_from_convergence (prossimamente)
  - gemma2_9b_it_custom_token_from_convergence (prossimamente)

### 4. Valutazione dei Checkpoint

I risultati della valutazione dei checkpoint possono essere visualizzati in diversi modi:

```bash
# Visualizza i risultati della valutazione
ls -la /work/tesi_ediluzio/evaluation/checkpoint_metrics/

# Visualizza i grafici generati
ls -la /work/tesi_ediluzio/evaluation/checkpoint_metrics/*/visualizations/

# Genera nuovi grafici comparativi
python /work/tesi_ediluzio/experiments/xml_direct_input/visualize_checkpoint_metrics.py \
    --metrics_dir /work/tesi_ediluzio/evaluation/checkpoint_metrics/MODEL_DIR \
    --output_dir /work/tesi_ediluzio/evaluation/checkpoint_metrics/MODEL_DIR/visualizations \
    --model_name "Model Name" \
    --use_wandb
```

## Nuovi Script Implementati

### 1. Generazione di Report e Grafici

- **generate_training_report.py**: Script per generare report HTML e grafici radar dopo il training
- **generate_training_report.slurm**: Job SLURM per la generazione automatica di report
- **generate_report.sh**: Script di utilità per eseguire manualmente la generazione di report
- **generate_radar_chart.py**: Script per generare grafici radar per confrontare le metriche
- **generate_enhanced_report.py**: Script per generare report HTML avanzati con esempi qualitativi

### 2. Valutazione dei Checkpoint

- **evaluate_checkpoints.py**: Script principale per la valutazione dei checkpoint con metriche multiple
- **run_checkpoint_evaluation.slurm**: Job SLURM per la valutazione dei checkpoint
- **monitor_and_evaluate_checkpoints.py**: Script per il monitoraggio e la valutazione automatica dei checkpoint
- **run_checkpoint_monitor.sh**: Script per avviare il monitoraggio dei checkpoint
- **visualize_checkpoint_metrics.py**: Script per la visualizzazione dei risultati delle valutazioni
- **evaluate_custom_tokenizer_models.sh**: Script per valutare i modelli con tokenizer personalizzato

### 3. Training con Pesi Convergenti

- **llama31_8b_custom_token_from_convergence.json**: Configurazione per Llama 3.1 8B con pesi convergenti e tokenizer personalizzato
- **gemma2_9b_it_custom_token_from_convergence.json**: Configurazione per Gemma 2 9B IT con pesi convergenti e tokenizer personalizzato
- **run_llama_custom_token_from_convergence.slurm**: Job SLURM per Llama 3.1 8B con pesi convergenti
- **run_gemma_custom_token_from_convergence.slurm**: Job SLURM per Gemma 2 9B IT con pesi convergenti
- **monitor_convergence_and_launch_custom_token.py**: Script per monitorare la convergenza e avviare il training successivo
- **start_evaluation_and_monitoring.sh**: Script per avviare la valutazione e il monitoraggio

## Analisi della Capacità di Gestione SVG Complessi

Abbiamo condotto un'analisi approfondita della capacità dei modelli di gestire SVG di diverse lunghezze e complessità. I risultati principali sono:

- **Lunghezze degli SVG**:
  - Lunghezza massima in token per Llama: 410 (limite: 1024)
  - Lunghezza massima in token per Gemma: 532 (limite: 768)
  - Nessun SVG supera la lunghezza massima del contesto per entrambi i modelli

- **Confronto tra Tokenizer**:
  - Il tokenizer di Llama è più efficiente, richiedendo circa il 66% dei token necessari a Gemma
  - Entrambi i tokenizer mostrano una distribuzione simile delle lunghezze
  - Ampio margine di sicurezza per entrambi i modelli

- **Implicazioni**:
  - Entrambi i modelli possono vedere l'intero SVG durante il training e l'inferenza
  - Non è necessario modificare le configurazioni attuali dei modelli
  - La capacità di vedere l'intero SVG dovrebbe portare a didascalie più accurate

Per maggiori dettagli, consultare il documento completo in `/work/tesi_ediluzio/docs/svg_complexity_analysis.md`.

## Conclusioni

Il progetto sta procedendo secondo i piani, con cinque configurazioni principali di modelli in fase di training e valutazione. I modelli con tokenizer standard, con tokenizer personalizzato, con dataset diviso in fasce e ottimizzati per convergenza hanno completato il training, mentre i modelli multi-GPU con ottimizzazioni avanzate sono attualmente in fase di training.

Recentemente, abbiamo implementato un sistema completo per la valutazione dei checkpoint con metriche multiple, per il monitoraggio automatico della convergenza e per l'analisi della capacità dei modelli di gestire SVG complessi. Inoltre, abbiamo risolto problemi tecnici come l'installazione di DeepSpeed e l'ottimizzazione della gestione dello spazio disco.

I prossimi passi includeranno il completamento del training multi-GPU con ottimizzazioni avanzate, l'analisi dei risultati dei captioner esterni, la valutazione comparativa dei checkpoint, l'analisi approfondita delle didascalie e la preparazione dell'analisi finale.
