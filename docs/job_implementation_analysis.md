# Analisi Job External Captioners

## Job di Valutazione

### 1. Valutazione Captioner Esterni
- **File**: `scripts/slurm/run_external_captioner_evaluation.slurm`
- **Job ID**: 2581923
- **Stato**: In esecuzione
- **Configurazione**:
  - GPU: 1x L40S 48GB
  - Memoria: 32GB
  - CPU: 4 core
  - Tempo: 24 ore

### 2. Calcolo CLIP Score
- **File**: `scripts/slurm/run_clip_evaluation_updated.slurm`
- **Job ID**: 2581924
- **Stato**: In esecuzione
- **Configurazione**:
  - GPU: 1x L40S 48GB
  - Memoria: 32GB
  - CPU: 4 core
  - Tempo: 24 ore

## Captioner Valutati

### 1. BLIP
- **Modello**: Salesforce/blip-image-captioning-base
- **Configurazione**:
  - Batch size: 32
  - DPI rasterizzazione: 150
  - Supporto multi-GPU: No

### 2. ViT-GPT2
- **Modello**: nlpconnect/vit-gpt2-image-captioning
- **Configurazione**:
  - Batch size: 32
  - DPI rasterizzazione: 150
  - Supporto multi-GPU: No

### 3. CogVLM
- **Modello**: THUDM/cogvlm-chat-hf
- **Configurazione**:
  - Batch size: 16
  - DPI rasterizzazione: 150
  - Supporto multi-GPU: No

## Metriche Calcolate

### 1. Metriche Standard
- BLEU-4
- METEOR
- CIDEr
- ROUGE-L

### 2. CLIP Score
- Versione GPU ottimizzata
- Batch size: 32
- Supporto multi-GPU: No

## Preprocessing

### 1. Rasterizzazione SVG
- **Libreria**: CairoSVG
- **DPI**: 150
- **Formato**: PNG
- **Dimensione**: Automatica

### 2. Dipendenze
- sympy==1.13.1
- cairosvg
- clip-score
- nltk

## Output

### 1. Risultati Metriche
- **Formato**: JSON
- **Directory**: `experiments/external_captioners_evaluation_results_v3_fix_attempts`
- **Contenuto**:
  - Metriche per ogni modello
  - Confronto tra modelli
  - Statistiche aggregate

### 2. Report HTML
- **File**: `qualitative_report.html`
- **Contenuto**:
  - Esempi di didascalie
  - Confronto visivo
  - Analisi qualitativa

## Monitoraggio

### 1. Log
- **Directory**: `logs/`
- **File**: `external_captioner_eval_*.out`
- **Contenuto**:
  - Progresso valutazione
  - Errori e warning
  - Metriche in tempo reale

### 2. Weights & Biases
- **Project**: captioner
- **Entity**: 337543-unimore
- **Metriche**:
  - Loss di validazione
  - Metriche di captioning
  - CLIP Score

## Note di Implementazione

### 1. Ottimizzazioni
- Rasterizzazione SVG ottimizzata
- Batch size adattato per memoria
- Supporto GPU per CLIP Score

### 2. Problemi Risolti
- Reinstallazione sympy e cairosvg
- Download automatico risorse NLTK
- Gestione memoria GPU

### 3. Best Practices
- Verifica dipendenze all'avvio
- Logging dettagliato
- Gestione errori robusta 