# Struttura Filesystem Aggiornata - SVG Captioning Project

## 📊 RIEPILOGO PULIZIA FILESYSTEM (25/05/2024)

### ✅ PULIZIA COMPLETATA
- **File rimossi**: 18 file obsoleti
- **Spazio liberato**: ~50 MB (file piccoli)
- **Status**: Documentazione e script puliti

### ⏳ PULIZIA RIMANENTE (Permission Issues)
- **chunks/**: 2.3 GB (file temporanei)
- **temp/**: 147 MB (directory temporanea)
- **svg_captioning_env/**: 5.6 GB (ambiente duplicato)
- **cache/**: 9.4 GB (cache HuggingFace - opzionale)

## 🗂️ STRUTTURA ATTUALE

### 📁 **docs/** - Documentazione Tecnica
```
docs/
├── README_jobs_technical_overview.md    # Panoramica 4 job
├── job_2584731_clip_evaluation.md       # CLIP eval (RUNNING)
├── job_2584729_baseline_evaluation.md   # Baseline (RUNNING)
├── job_2584772_llama_training.md        # Llama training (FIXED)
├── job_2584773_gemma_training.md        # Gemma training (FIXED)
├── FILESYSTEM_UPDATED.md                # Questo file
├── ARCHITECTURE.md                      # Architettura sistema
└── PROJECT_STATUS.md                    # Status progetto
```

### 📁 **scripts/** - Script Funzionanti
```
scripts/
├── slurm/
│   ├── run_llama_test3_multi_gpu.slurm     # Llama (FIXED)
│   ├── run_gemma_test3_multi_gpu.slurm     # Gemma (FIXED)
│   ├── run_complete_clip_evaluation.slurm  # CLIP eval
│   └── run_baseline_evaluation.slurm       # Baseline
├── training/
│   └── train_lora_multi_gpu_simple.py      # Training (NO quantizzazione)
├── evaluation/
├── inference/
├── utils/
└── cleanup/
    └── analyze_and_cleanup_filesystem.py   # Analisi filesystem
```

### 📁 **data/** - Dataset Core
```
data/
├── processed/
│   ├── xml_format/
│   │   ├── train_set_final_xml.json           # 155 MB
│   │   └── train_set_final_xml_augmented.json # 156 MB
│   ├── filtered_svg_all_sources_FULL.json     # 1.3 GB
│   └── train_set_final.json                   # 118 MB
├── results/                                   # Per CLIP evaluation
└── test_samples/                              # Campioni test
```

### 📁 **experiments/** - Checkpoint Training
```
experiments/
├── xml_direct_input/
│   ├── configs/                    # Configurazioni
│   └── outputs/
│       ├── llama31_8b_test1_convergence/     # 640 MB
│       └── gemma2_9b_it_test1_convergence/   # 824 MB
└── complete_clip_evaluation/       # Risultati CLIP
```

### 📁 **logs/** - Log Attuali
```
logs/
├── complete_clip_evaluation_2584731.log     # RUNNING
├── baseline_evaluation_2584729.log          # RUNNING
├── archive/                                 # Log archiviati
└── compressed/                              # Log compressi
```

### 📁 **svg_env/** - Ambiente Python
```
svg_env/                           # 5.6 GB
├── PyTorch 2.7.0+cu118           # FIXED
├── Transformers 4.52.3           # Compatible
├── PEFT 0.15.1                   # LoRA ready
└── NO BitsAndBytes                # Removed for compatibility
```

## 🔧 PROBLEMI RISOLTI

### ✅ Training Jobs (COMPLETAMENTE RIPARATI)
1. **CVE-2025-32434**: PyTorch 2.7.0+cu118 ✅
2. **BitsAndBytes-Triton**: Quantizzazione disabilitata ✅
3. **CUDA Compatibility**: cu118 matching ✅
4. **Job Status**: 2584894 (Llama) e 2584895 (Gemma) in coda ✅

### ✅ Evaluation Jobs (IN ESECUZIONE)
1. **CLIP Evaluation**: 8+ ore, progresso normale ✅
2. **Baseline Evaluation**: 8+ ore, 6 modelli ✅

## 📈 STATUS JOB ATTUALI

### 🟢 RUNNING
- **Job 2584731**: CLIP Evaluation (8:00+ ore)
- **Job 2584729**: Baseline Evaluation (8:00+ ore)

### 🟡 PENDING
- **Job 2584894**: Llama Training (Resources)
- **Job 2584895**: Gemma Training (Priority)

## 🧹 FILE RIMOSSI

### ✅ Documentazione Obsoleta
- DOCUMENTAZIONE_SCRIPT.md
- PROJECT_STATUS_AFTER_CLEANUP.md
- SOLUZIONI_PROBLEMI_TRAINING.md
- STRUTTURA_PROGETTO.md
- fix_evaluation_strategy_issue.md
- parameter_count_results.md
- project_status_update.md
- report.md
- training_jobs.md
- wandb_runs_log.md
- wandb_runs_log_clean.md

### ✅ Script Obsoleti
- fix_multi_gpu_device_issue.py
- fix_pip.sh
- generate_metrics_radar_chart.py
- install_deepspeed.sh
- test_hf_token.py

### ✅ File Configurazione
- requirements.txt (duplicato)
- run_counters.json

## 💾 DIMENSIONI ATTUALI

### Core Project (~3 GB)
- **Dataset**: 2 GB
- **Checkpoint**: 1.5 GB
- **Documentazione**: 50 MB
- **Script**: 100 MB
- **Log attuali**: 500 MB

### Cache/Temp (~25 GB)
- **Cache HuggingFace**: 9.4 GB
- **Cache datasets**: 15+ GB
- **Ambiente duplicato**: 5.6 GB

### Totale: ~28 GB (dopo pulizia parziale)

## 🎯 RACCOMANDAZIONI

### Pulizia Immediata Possibile
```bash
# Rimuovere cache HuggingFace (opzionale)
rm -rf cache/huggingface/  # 9.4 GB

# Pulire log obsoleti
find logs/ -name "*_258[0-3]*" -delete  # Log vecchi

# Rimuovere .cache datasets (opzionale)
rm -rf .cache/  # 15+ GB
```

### Mantenere
- **memory.md**: Documentazione completa
- **docs/**: Documentazione tecnica aggiornata
- **experiments/xml_direct_input/outputs/**: Checkpoint
- **scripts/**: Script funzionanti
- **data/processed/xml_format/**: Dataset training
- **svg_env/**: Ambiente funzionante

## ✅ FILESYSTEM OTTIMIZZATO

Il filesystem è ora organizzato, documentato e parzialmente pulito. I problemi di training sono risolti e i job di evaluation procedono normalmente. La struttura è ottimizzata per efficienza e manutenibilità.
