# Aggiornamento Implementazioni Recenti (06/05/2025)

Ecco la lista aggiornata dei passi implementati recentemente nel progetto:

## 1. Fine-tuning LoRA con Tokenizer Personalizzato
* **Llama 3.1 8B con tokenizer personalizzato**: ✅ COMPLETATO
    * Checkpoint finale: 1800
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_custom_token/
    * Parametri addestrabili: 41,943,040 (0.5196% del totale)
    * Tokenizer personalizzato con 17 token speciali per SVG
* **Gemma 2 9B IT con tokenizer personalizzato**: ✅ COMPLETATO
    * Checkpoint finale: 2400
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_custom_token/
    * Parametri addestrabili: 54,018,048 (0.5811% del totale)
    * Tokenizer personalizzato con 17 token speciali per SVG

## 2. Training con Dataset Diviso in Fasce di Complessità
* **Llama 3.1 8B**: ✅ COMPLETATO
    * Checkpoint finale: 525
    * Loss finale: 0.4352
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1/
    * Parametri addestrabili: 167,772,160 (3.5633% del totale)
    * Tracciamento con Weights & Biands completato (Run ID: uwdfy7oc)
* **Gemma 2 9B IT**: ✅ COMPLETATO
    * Checkpoint finale: 525
    * Loss finale: 0.3005
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1/
    * Parametri addestrabili: 216,072,192 (4.0799% del totale)
    * Tracciamento con Weights & Biands completato (Run ID: cdksnxwl)
    * Configurazione ottimizzata per evitare errori OOM (batch size=1, gradient accumulation=8, max_length=768)

## 3. Training fino a Convergenza
* **Llama 3.1 8B senza tokenizer personalizzato**: ✅ COMPLETATO
    * Checkpoint finale: 4000
    * Loss finale: 0.4776
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_lora_xml_no_token_convergence/
    * Tracciamento con Weights & Biands completato (entity=337543-unimore, project=captioner)
* **Gemma 2 9B IT senza tokenizer personalizzato**: ✅ COMPLETATO
    * Checkpoint finale: 3000
    * Loss finale: 0.317
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_lora_xml_no_token_convergence/
    * Tracciamento con Weights & Biands completato (entity=337543-unimore, project=captioner)
* **Llama 3.1 8B ottimizzato per convergenza**: ✅ COMPLETATO (Job 2566826)
    * Completato sul nodo "rezzonico"
    * Checkpoint finale: 4000
    * Loss finale: 0.9478
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/
    * Configurato per 15 epoche con early stopping (patience=30)
    * Valutazione ogni 50 step per monitorare l'overfitting
* **Gemma 2 9B IT ottimizzato per convergenza**: ✅ COMPLETATO (Job 2566825)
    * Completato sul nodo "gervasoni"
    * Checkpoint finale: 4000
    * Loss finale: 0.0251
    * Checkpoint salvati in /work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test1_convergence/
    * Configurato per 15 epoche con early stopping (patience=30)
    * Valutazione ogni 50 step per monitorare l'overfitting

## 4. Implementazione di Callback per Pulizia Automatica
* **CheckpointCleanupCallback**: ✅ IMPLEMENTATO
    * Elimina automaticamente i checkpoint vecchi durante l'addestramento
    * Mantiene solo gli ultimi N checkpoint (specificato da save_total_limit)
    * Riduce significativamente lo spazio disco occupato
    * Integrato negli script di training

## 5. Ottimizzazione Gestione Spazio Disco
* **Pulizia Checkpoint**: ✅ COMPLETATO
    * Rimossi checkpoint di test e duplicati
    * Rimossi checkpoint intermedi dai modelli principali
    * Ridotto lo spazio occupato dalla directory outputs da 55GB a 8.5GB
    * Ulteriore pulizia effettuata il 29/04/2025, liberando circa 31.8GB di spazio
    * Pulizia aggiuntiva effettuata il 04/05/2025, liberando circa 37GB di spazio (52% dello spazio totale)
    * Rimossi checkpoint intermedi (400, 600, 700) mantenendo solo quelli essenziali (500, 800, 900)
    * Rimossa cache di Hugging Face (33GB) per ottimizzare lo spazio
* **Archiviazione Log**: ✅ COMPLETATO
    * Creata struttura di archiviazione per i log vecchi
    * Implementata compressione automatica dei log archiviati
    * Ridotto il numero di file nella directory dei log

## 6. Miglioramenti agli Script di Training
* **Supporto per Formato Dati Flessibile**: ✅ IMPLEMENTATO
    * Aggiunto supporto per entrambi i formati di dati: svg_data = item.get("data", item.get("xml"))
    * Permette di utilizzare sia il dataset originale che il dataset in formato XML
* **Padding Corretto per i Batch**: ✅ IMPLEMENTATO
    * Aggiunto parametro padding="max_length" alla tokenizzazione
    * Risolve il problema di dimensioni inconsistenti nei batch
* **Tokenizer Personalizzato per SVG**: ✅ IMPLEMENTATO
    * Aggiunta di token speciali per SVG al vocabolario del modello
    * Migliorata la capacità del modello di comprendere e generare codice SVG
* **Supporto per Ripresa Training**: ✅ IMPLEMENTATO
    * Aggiunto parametro --resume_from_checkpoint per riprendere il training da un checkpoint esistente
    * Permette di continuare il training dopo interruzioni per limite di tempo
    * Mantiene la continuità del tracciamento su Weights & Biands
* **Supporto per Training Multi-GPU**: ✅ IMPLEMENTATO
    * Configurazione DeepSpeed per ottimizzare l'uso della memoria
    * Supporto per training distribuito con torchrun/torch.distributed.run
    * Configurazione ottimizzata per evitare errori OOM

## 7. Integrazione con Weights & Biands
* **Tracciamento Completo**: ✅ IMPLEMENTATO
    * Configurato per tracciare loss, metriche e parametri di training
    * Supporto per entity personalizzata (337543-unimore)
    * Visualizzazione in tempo reale del progresso del training
    * Salvataggio automatico della configurazione e dei risultati
* **Debug Avanzato**: ✅ IMPLEMENTATO
    * Logging dettagliato dello stato di inizializzazione
    * Tracciamento della modalità di esecuzione (online/offline)
    * Gestione degli errori con traceback completo

## 8. Valutazione dei Modelli
* **Valutazione Modelli Zero-Shot**: ✅ COMPLETATO
    * Implementata valutazione completa dei modelli base in modalità zero-shot
    * Generazione di didascalie per 100 esempi di test per ogni modello
    * Calcolo di metriche quantitative: BLEU-1/2/3/4, METEOR, CIDEr
    * Risultati salvati in /work/tesi_ediluzio/evaluation/zero_shot/
    * Report HTML generato in /work/tesi_ediluzio/evaluation/reports/zero_shot_evaluation_report.html
    * Risultati principali:
        * Llama 3.1 8B: BLEU-1=0.0150, METEOR=0.0345, CIDEr=0.7434
        * Gemma 2 9B IT: BLEU-1=0.0114, METEOR=0.0291, CIDEr=0.8066
    * Grafico radar generato con metriche reali in /work/tesi_ediluzio/evaluation/reports/real_radar_chart.png
* **Valutazione Modelli Fine-tuned**: ✅ COMPLETATO
    * Eseguita valutazione dei modelli fine-tuned
    * Utilizzate le stesse metriche della valutazione zero-shot
    * Confronto diretto tra modelli zero-shot e fine-tuned
    * Report HTML generati con visualizzazione SVG e didascalie
    * Grafici radar per confronto delle metriche tra modelli
    * Report HTML con placeholder per SVG generato in /work/tesi_ediluzio/evaluation/reports/placeholder_only_report.html
    * Report HTML con SVG incorporati generato in /work/tesi_ediluzio/evaluation/reports/simple_img_report.html
* **Valutazione Checkpoint**: ✅ IMPLEMENTATO
    * Implementato sistema completo per valutazione dei checkpoint con metriche multiple
    * Script principale: experiments/xml_direct_input/evaluate_checkpoints.py
    * Supporto per valutazione automatica durante il training
    * Metriche implementate:
        * Qualità: BLEU-1/2/3/4, METEOR, CIDEr, CLIP Score
        * Efficienza: Tempo di inferenza, Perplexity
        * Analisi linguistica: Lunghezza didascalie, Diversità vocabolario, Type-Token Ratio, Self-BLEU
    * Visualizzazione avanzata con grafici comparativi
    * Integrazione con Weights & Biands per tracciamento dei risultati
* **Generazione Report HTML**: ✅ IMPLEMENTATO
    * Script per generare report HTML completi con valutazioni, grafici e esempi qualitativi
    * Supporto per confronto tra modelli zero-shot e fine-tuned
    * Visualizzazione di metriche, grafici LOESS e esempi qualitativi
    * Integrazione con Weights & Biands per tracciamento dei risultati

## 9. Pulizia e Organizzazione del Progetto
* **Rimozione File Inutili**: ✅ COMPLETATO
    * Rimossi checkpoint di test e duplicati
    * Rimossi file temporanei e di log non necessari
    * Rimossi pesi dei modelli non utilizzati
    * Rimossi modelli Nemotron non utilizzati
* **Compressione Log Vecchi**: ✅ COMPLETATO
    * Archiviati tutti i log vecchi in un archivio compresso
    * Mantenuti solo i log più recenti
* **Pulizia Run Weights & Biands**: ✅ COMPLETATO
    * Rimosse run vecchie e non necessarie
    * Mantenute solo le run più recenti e rilevanti

## 10. Flusso di Lavoro Ottimizzato
* **Training Sequenziale**: ✅ IMPLEMENTATO
    * Prima completare i training senza tokenizer personalizzato fino a convergenza (COMPLETATO)
    * Poi utilizzare i modelli convergenti come base per i training con tokenizer personalizzato (COMPLETATO)
    * Infine, training con dataset diviso in fasce di complessità (COMPLETATO)
    * Training ottimizzati per convergenza (COMPLETATO)
* **Gestione Automatica dei Job**: ✅ IMPLEMENTATO
    * Monitoraggio dello stato dei job SLURM
    * Ripresa automatica dei training interrotti
    * Verifica delle dipendenze tra job
* **Monitoraggio Automatico dei Checkpoint**: ✅ IMPLEMENTATO
    * Script per monitorare e valutare automaticamente i nuovi checkpoint
    * Avvio automatico di job SLURM per la valutazione
    * Visualizzazione dei risultati in tempo reale su Weights & Biands

## 11. Preparazione per Training con Pesi Convergenti e Tokenizer Personalizzato
* **Configurazioni**: ✅ IMPLEMENTATO
    * llama31_8b_custom_token_from_convergence.json: Configurazione per Llama 3.1 8B
    * gemma2_9b_it_custom_token_from_convergence.json: Configurazione per Gemma 2 9B IT
    * Utilizzo dei pesi dei modelli convergenti come punto di partenza
    * Applicazione del tokenizer personalizzato con 17 token speciali per SVG
* **Script SLURM**: ✅ IMPLEMENTATO
    * run_llama_custom_token_from_convergence.slurm: Job SLURM per Llama 3.1 8B
    * run_gemma_custom_token_from_convergence.slurm: Job SLURM per Gemma 2 9B IT
    * Configurati per 10 epoche con early stopping (patience=30)
* **Monitoraggio della Convergenza**: ✅ IMPLEMENTATO
    * monitor_convergence_and_launch_custom_token.py: Script per monitorare la convergenza
    * Avvio automatico del training successivo quando i modelli raggiungono la convergenza
    * Utilizzo del miglior checkpoint (checkpoint-best) come punto di partenza
* **Script di Avvio**: ✅ IMPLEMENTATO
    * start_evaluation_and_monitoring.sh: Script per avviare la valutazione e il monitoraggio
    * Avvio della valutazione dei modelli con tokenizer personalizzato
    * Avvio del monitoraggio della convergenza dei modelli in training

## 12. Implementazione del CLIP Score
* **Integrazione del CLIP Score**: ✅ COMPLETATO (05/05/2025)
    * Implementato script per il calcolo del CLIP Score tra SVG e didascalie
    * Script principale: `/work/tesi_ediluzio/clip_score_gpu_simple.py`
    * Utilizzo di CLIP ViT-B/32 per calcolare la similarità semantica
    * Supporto per esecuzione su GPU tramite job SLURM
    * Risultati salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/clip_scores/`
* **Valutazione con CLIP Score**: ✅ COMPLETATO (06/05/2025)
    * Eseguita valutazione dei modelli fine-tuned con CLIP Score
    * Confronto tra didascalie generate e didascalie di riferimento
    * Calcolo di metriche aggregate: media, mediana, min/max
    * Integrazione dei risultati nei report di valutazione
    * Aggiunta visualizzazione dei CLIP Score nei grafici radar

## 13. Documentazione Tecnica
* **Documentazione Multi-GPU**: ✅ COMPLETATO (05/05/2025)
    * Creato documento `/work/tesi_ediluzio/multi_gpu_training.md`
    * Descrizione dettagliata dell'implementazione del training multi-GPU
    * Spiegazione della configurazione hardware nei job SLURM
    * Dettagli sulla parallelizzazione a livello di batch
    * Descrizione dell'accumulo del gradiente e della distribuzione del carico
* **Documentazione Trasferimento Conoscenze**: ✅ COMPLETATO (06/05/2025)
    * Creato documento `/work/tesi_ediluzio/assistant_knowledge_transfer.md`
    * Panoramica completa del progetto per futuri assistenti
    * Dettagli su infrastruttura, modelli, dataset e configurazioni
    * Descrizione del workflow e degli script principali
    * Indicazioni sulle preferenze dell'utente e problemi comuni

## 14. Prossimi Passi Pianificati
* **Analisi dei Risultati del Training Ottimizzato per Convergenza**: ✅ COMPLETATO (05/05/2025)
    * Analizzate le curve di loss per identificare overfitting/underfitting
    * Confrontate le performance dei modelli Llama e Gemma
    * Valutato l'impatto della convergenza sulla qualità delle didascalie
    * Analizzati i report HTML generati con visualizzazione SVG e didascalie
* **Avviare Training con Pesi Convergenti e Tokenizer Personalizzato**: ✅ AVVIATO (05/05/2025)
    * Avviato il monitoraggio della convergenza con start_evaluation_and_monitoring.sh
    * Utilizzati i pesi dei modelli convergenti come punto di partenza
    * Applicato il tokenizer personalizzato con 17 token speciali per SVG
    * Job in esecuzione sui nodi "vegeta" (Llama) e "gervasoni" (Gemma)
    * Monitoraggio attivo delle performance rispetto ai modelli precedenti
* **Valutazione Comparativa Approfondita**: 🔄 IN CORSO
    * Confronto delle performance dei modelli con diverse metriche
    * Analisi dell'evoluzione delle metriche durante il training
    * Valutazione dell'impatto del tokenizer personalizzato e della divisione in fasce
    * Valutazione dell'impatto dell'inizializzazione con pesi convergenti
    * Creazione di visualizzazioni comparative più dettagliate (grafici radar, heatmap)
    * Miglioramento dei grafici radar esistenti con dati più accurati
* **Analisi Approfondita delle Didascalie**: 🔄 IN CORSO
    * Analisi della qualità linguistica delle didascalie generate
    * Valutazione della diversità del vocabolario e della lunghezza delle didascalie
    * Confronto dei tempi di inferenza e della perplexity tra i diversi modelli
    * Miglioramento dei report con esempi di didascalie per ciascun modello
    * Perfezionamento dei report HTML esistenti con visualizzazioni SVG più accurate
* **Inferenza su Dataset Complessi**: 🔄 PIANIFICATO
    * Generare didascalie su SVG di alta complessità
    * Valutare la qualità delle didascalie con metriche standard e avanzate
    * Creare una demo interattiva per la generazione di didascalie
    * Utilizzare i report HTML sviluppati per visualizzare i risultati dell'inferenza
* **Documentazione Finale e Preparazione Tesi**: 🔄 PIANIFICATO
    * Preparare report con confronto tra i diversi approcci
    * Documentare le best practices identificate
    * Creare visualizzazioni comparative delle performance dei modelli
    * Preparare la documentazione finale del progetto
    * Includere i report HTML e i grafici radar generati come parte della documentazione
