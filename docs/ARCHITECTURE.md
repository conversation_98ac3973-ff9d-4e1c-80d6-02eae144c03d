# Architettura del Sistema per SVG Captioning

Questo documento descrive l'architettura del sistema per il progetto di SVG Captioning, che implementa modelli di linguaggio per la generazione di didascalie per immagini SVG.

## Panoramica dell'Architettura

Il sistema è organizzato in diversi componenti principali:

1. **Preparazione dei Dati**: Componenti per la preparazione e il preprocessing dei dataset SVG
2. **Tokenizzazione**: Implementazione di tokenizer standard e personalizzati per SVG
3. **Training**: Componenti per il fine-tuning dei modelli di linguaggio
4. **Inferenza**: Componenti per la generazione di didascalie
5. **Valutazione**: Componenti per la valutazione delle didascalie generate
6. **Monitoraggio e Gestione**: Strumenti per il monitoraggio dei job e la gestione delle risorse

## Componenti Principali

### 1. Preparazione dei Dati

- **Dataset Processing**: Filtraggio e preprocessing dei dataset SVG
  - `data_preparation/analyze_svg_dataset.py`: Analisi statistica degli SVG
  - `data_preparation/convert_to_xml.py`: Conversione dei dataset in formato XML completo
  - `data_preparation/create_filtered_xml_dataset.py`: Creazione di dataset filtrati in formato XML

- **Dataset Management**: Gestione e organizzazione dei dataset
  - `data/processed/xml_format/`: Dataset con SVG in formato XML completo
  - `prepare_test_data.py`: Estrazione di campioni casuali per la valutazione finale

### 2. Tokenizzazione

- **Standard Tokenizer**: Utilizzo dei tokenizer standard dei modelli
  - Supporto per Llama 3.1 8B e Gemma 2 9B IT

- **Custom Tokenizer**: Implementazione di tokenizer personalizzati per SVG
  - `shared/svg_core/custom_tokenizer_utils.py`: Utility per la creazione di tokenizer personalizzati
  - Aggiunta di 17 token speciali per SVG al vocabolario del modello

### 3. Training

- **LoRA Fine-tuning**: Fine-tuning dei modelli con LoRA (Low-Rank Adaptation)
  - `experiments/xml_direct_input/train_lora_wandb.py`: Script principale per il training con LoRA
  - `experiments/xml_direct_input/configs/`: Configurazioni per i diversi modelli
  - Supporto per quantizzazione a 4-bit e 8-bit

- **Training Management**: Gestione dei job di training
  - `run_llama31_8b_no_token_convergence_resume.slurm`: Script per riprendere il training di Llama 3.1 8B
  - `run_gemma2_9b_it_no_token_convergence_resume.slurm`: Script per riprendere il training di Gemma 2 9B IT
  - `run_llama31_8b_custom_token_convergence_after_base.slurm`: Script per il training di Llama 3.1 8B con tokenizer personalizzato
  - `run_gemma2_9b_it_custom_token_convergence_after_base.slurm`: Script per il training di Gemma 2 9B IT con tokenizer personalizzato

- **Weights & Biands Integration**: Integrazione con Weights & Biands per il tracciamento degli esperimenti
  - Tracciamento di loss, metriche e parametri di training
  - Visualizzazione in tempo reale del progresso del training

### 4. Inferenza

- **Unified Inference**: Sistema unificato per l'inferenza con diversi modelli
  - `experiments/xml_direct_input/run_inference_unified.py`: Script unificato per l'inferenza
  - Supporto per modelli con e senza tokenizer personalizzato
  - Supporto per quantizzazione a 4-bit e 8-bit

- **Inference Management**: Gestione dei job di inferenza
  - `run_inference_llama31_8b_no_token.slurm`: Script per l'inferenza con Llama 3.1 8B
  - `run_inference_gemma2_9b_it_no_token.slurm`: Script per l'inferenza con Gemma 2 9B IT

### 5. Valutazione

- **Metrics Calculation**: Calcolo delle metriche di valutazione
  - `evaluation/evaluate_captions.py`: Valutazione delle didascalie con metriche standard
  - Supporto per BLEU, ROUGE, METEOR, CIDEr, BERTScore

- **Results Visualization**: Visualizzazione dei risultati
  - `evaluation/visualize_results.py`: Visualizzazione dei risultati in formato HTML
  - `evaluation/compare_models.py`: Confronto tra diversi modelli

### 6. Monitoraggio e Gestione

- **Job Monitoring**: Monitoraggio dei job SLURM
  - `monitor_training_progress.py`: Monitoraggio del progresso dei training in corso
  - Stima del tempo di completamento e visualizzazione delle metriche

- **Resource Management**: Gestione delle risorse
  - `cleanup_temp_files.py`: Pulizia dei file temporanei e obsoleti
  - `cleanup_files.sh`: Script per l'eliminazione di file inutili

- **Documentation**: Documentazione del sistema
  - `document_filesystem.py`: Generazione della documentazione della struttura del filesystem
  - `generate_preliminary_report.py`: Generazione di report preliminari sui modelli addestrati
  - `plot_learning_curves.py`: Visualizzazione delle curve di apprendimento

## Flusso di Lavoro Completo

Il flusso di lavoro completo del sistema è il seguente:

1. **Preparazione dei Dataset**:
   - Analisi e filtraggio dei dataset SVG
   - Conversione in formato XML completo
   - Suddivisione in training e test set

2. **Training dei Modelli Base**:
   - Fine-tuning di Llama 3.1 8B e Gemma 2 9B IT senza tokenizer personalizzato
   - Training fino a convergenza con early stopping
   - Monitoraggio del progresso con Weights & Biands

3. **Training dei Modelli con Tokenizer Personalizzato**:
   - Utilizzo dei modelli base convergenti come punto di partenza
   - Aggiunta di token speciali per SVG al vocabolario
   - Fine-tuning con tokenizer personalizzato

4. **Inferenza e Generazione di Didascalie**:
   - Generazione di didascalie con i modelli addestrati
   - Utilizzo dello stesso tokenizer dell'addestramento per l'inferenza

5. **Valutazione e Confronto**:
   - Calcolo delle metriche di valutazione
   - Confronto tra i diversi modelli
   - Analisi dell'impatto del tokenizer personalizzato

6. **Monitoraggio e Manutenzione**:
   - Monitoraggio continuo dei job di training
   - Pulizia regolare dei file temporanei e obsoleti
   - Generazione di report e documentazione

## Modelli Supportati

Il sistema supporta attualmente i seguenti modelli:

1. **Llama 3.1 8B**:
   - Versione senza tokenizer personalizzato
   - Versione con tokenizer personalizzato (17 token speciali per SVG)

2. **Gemma 2 9B IT**:
   - Versione senza tokenizer personalizzato
   - Versione con tokenizer personalizzato (17 token speciali per SVG)

## Configurazione del Sistema

### Configurazione Hardware

- **GPU**: A40 48GB, L40S 48GB, RTX5000 16GB, RTXA5000 24GB, RTX6000 24GB
- **CPU**: 8 core per job
- **Memoria**: 32GB per job

### Configurazione Software

- **Sistema Operativo**: Linux
- **Ambiente Python**: Ambiente virtuale dedicato (`svg_captioning_env`)
- **Framework**: PyTorch, Transformers, PEFT, bitsandbytes
- **Tracciamento**: Weights & Biands (API key: 6006c12f16afe29f1402ea7340dadad0cf62b347)
- **Gestione Job**: SLURM

### Configurazione dei Job

- **Partizione**: all_usr_prod
- **Account**: tesi_ediluzio
- **QoS**: normal
- **Tempo Massimo**: 24 ore per job

## Ottimizzazioni

Il sistema include diverse ottimizzazioni per migliorare l'efficienza e le prestazioni:

1. **Quantizzazione**: Supporto per quantizzazione a 4-bit e 8-bit per ridurre l'utilizzo di memoria
2. **Gradient Checkpointing**: Riduzione dell'utilizzo di memoria durante il training
3. **Early Stopping**: Interruzione automatica del training quando non ci sono miglioramenti
4. **Checkpoint Management**: Gestione automatica dei checkpoint per risparmiare spazio su disco
5. **Resume Training**: Possibilità di riprendere il training da un checkpoint esistente

## Strumenti di Supporto

Il sistema include diversi strumenti di supporto per facilitare lo sviluppo e la gestione:

1. **monitor_training_progress.py**: Monitoraggio del progresso dei training in corso
2. **generate_preliminary_report.py**: Generazione di report preliminari sui modelli addestrati
3. **cleanup_temp_files.py**: Pulizia dei file temporanei e obsoleti
4. **plot_learning_curves.py**: Visualizzazione delle curve di apprendimento
5. **prepare_test_data.py**: Preparazione dei dati di test per la valutazione finale
6. **document_filesystem.py**: Documentazione della struttura del filesystem

## Conclusioni

L'architettura del sistema per SVG Captioning è progettata per essere flessibile, scalabile e facile da gestire. Il sistema supporta diversi modelli e configurazioni, permettendo di sperimentare con diverse architetture e approcci. L'integrazione con Weights & Biands facilita il tracciamento degli esperimenti e l'analisi dei risultati, mentre gli strumenti di supporto semplificano la gestione del sistema e la documentazione.

Il flusso di lavoro completo, dalla preparazione dei dataset alla valutazione dei modelli, è ben definito e documentato, permettendo di replicare facilmente gli esperimenti e di estendere il sistema con nuovi modelli e approcci.
