# Pipeline Completa per Training e Valutazione

Questo documento descrive la pipeline completa da seguire per ogni ciclo di training e valutazione dei modelli. **È FONDAMENTALE seguire questa pipeline in modo rigoroso per ogni esperimento.**

## 1. Preparazione del Training

### 1.1 Configurazione del Job di Training

1. **Modifica dello script SLURM**:
   - Apri `/work/tesi_ediluzio/scripts/slurm/train_llama_lora.slurm` o `/work/tesi_ediluzio/scripts/slurm/train_gemma_lora.slurm`
   - Aggiorna i parametri di training (batch size, learning rate, LoRA rank, ecc.)
   - Assicurati che il nome della run segua il formato `test2_MODEL_NUM` (es. `test2_llama31_8b_800`)

2. **Aggiornamento del Log delle Run**:
   - Apri `/work/tesi_ediluzio/wandb_runs_log.md`
   - Aggiungi una nuova riga nella sezione "Run Attualmente in Esecuzione"
   - Includi: modello, numero, nome run, data, note

### 1.2 Invio del Job di Training

```bash
cd /work/tesi_ediluzio
sbatch scripts/slurm/train_llama_lora.slurm
sbatch scripts/slurm/train_gemma_lora.slurm
```

### 1.3 Monitoraggio del Training

1. **Monitoraggio dello stato del job**:
   ```bash
   squeue -u ediluzio
   ```

2. **Monitoraggio dei log**:
   ```bash
   tail -f /work/tesi_ediluzio/logs/train_llama_*.out
   tail -f /work/tesi_ediluzio/logs/train_gemma_*.out
   ```

3. **Monitoraggio su Weights & Biands**:
   - Vai su [Weights & Biands](https://wandb.ai/337543-unimore/captioner)
   - Controlla le curve di loss, accuracy e altre metriche

## 2. Valutazione del Modello

### 2.1 Configurazione del Job di Valutazione

1. **Modifica dello script SLURM**:
   - Apri `/work/tesi_ediluzio/scripts/slurm/evaluate_model.slurm`
   - Aggiorna il percorso del checkpoint da valutare
   - Assicurati che il nome del job sia descrittivo

2. **Aggiornamento del Log delle Run**:
   - Apri `/work/tesi_ediluzio/wandb_runs_log.md`
   - Aggiungi una nuova riga nella sezione "Job di Valutazione"
   - Includi: nome job, job ID, data, stato, note

### 2.2 Invio del Job di Valutazione

```bash
cd /work/tesi_ediluzio
sbatch scripts/slurm/evaluate_model.slurm
```

### 2.3 Monitoraggio della Valutazione

1. **Monitoraggio dello stato del job**:
   ```bash
   squeue -u ediluzio
   ```

2. **Monitoraggio dei log**:
   ```bash
   tail -f /work/tesi_ediluzio/logs/evaluate_*.out
   ```

## 3. Generazione di Report e Grafici

### 3.1 Generazione del Report HTML

**QUESTO PASSAGGIO È OBBLIGATORIO DOPO OGNI TRAINING!**

```bash
cd /work/tesi_ediluzio
./scripts/generate_report.sh
```

Questo script:
1. Genera un report HTML con i risultati della valutazione
2. Crea grafici radar per confrontare le metriche tra i modelli
3. Aggiorna automaticamente il file di log delle run

### 3.2 Verifica dei Risultati

1. **Controlla il report HTML**:
   - Apri `/work/tesi_ediluzio/reports/html/training_evaluation_report.html`
   - Verifica che le metriche siano corrette
   - Controlla gli esempi qualitativi

2. **Controlla i grafici radar**:
   - Apri `/work/tesi_ediluzio/reports/html/radar_chart.png`
   - Verifica che il grafico mostri correttamente le metriche per tutti i modelli

3. **Aggiorna il log delle run**:
   - Apri `/work/tesi_ediluzio/wandb_runs_log.md`
   - Aggiorna lo stato delle run completate
   - Aggiungi note sui risultati ottenuti

## 4. Pulizia e Manutenzione

### 4.1 Pulizia dei Checkpoint

Per evitare di occupare troppo spazio, elimina i checkpoint non necessari:

```bash
cd /work/tesi_ediluzio
python scripts/cleanup/cleanup_checkpoints.py --keep_best 3 --keep_last 2
```

### 4.2 Aggiornamento della Documentazione

Aggiorna i file di documentazione con i nuovi risultati:

1. **Aggiorna il README.md**:
   - Aggiorna la sezione "Risultati Principali"
   - Aggiorna la data di aggiornamento

2. **Aggiorna il file RESULTS.md**:
   - Aggiungi i nuovi risultati con data e configurazione

## Risoluzione dei Problemi Comuni

### Job Terminato per Timeout

Se un job termina per timeout, aumenta il tempo limite nello script SLURM:

```bash
#SBATCH --time=24:00:00  # Aumenta a 24 ore o più se necessario
```

### Errori di Memoria

Se si verificano errori di memoria (OOM), prova a:
1. Ridurre il batch size
2. Aumentare il gradient accumulation
3. Utilizzare più GPU

### Job in Attesa (Priority)

Se il job rimane in attesa con motivo "Priority", potrebbe essere necessario attendere che altre risorse si liberino o contattare l'amministratore del cluster.

## Note Importanti

- **OGNI TRAINING DEVE ESSERE LOGGATO** nel file `wandb_runs_log.md`
- **OGNI TRAINING DEVE GENERARE UN REPORT HTML** con lo script `generate_report.sh`
- **OGNI TRAINING DEVE GENERARE GRAFICI RADAR** per il confronto delle metriche
- **MANTIENI LA DOCUMENTAZIONE AGGIORNATA** con i nuovi risultati

Seguendo rigorosamente questa pipeline, garantirai la coerenza e la tracciabilità di tutti gli esperimenti.
