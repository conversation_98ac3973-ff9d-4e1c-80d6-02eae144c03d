# Tecnologie e Metodologie Avanzate per il Fine-tuning di Modelli Linguistici su SVG Captioning

## Panoramica del Progetto

Il progetto si concentra sull'addestramento e l'ottimizzazione di modelli linguistici di grandi dimensioni (LLM) per la generazione di didascalie descrittive di immagini SVG (Scalable Vector Graphics). L'obiettivo è sviluppare modelli capaci di comprendere la struttura vettoriale delle immagini SVG e generare descrizioni accurate e dettagliate. Questo lavoro esplora diverse architetture, tecniche di fine-tuning e strategie di ottimizzazione per migliorare le performance dei modelli.

## Architetture e Modelli

### Modelli Base
Il progetto utilizza due architetture principali:

1. **Llama 3.1 8B Instruct**
   ```python
   model = AutoModelForCausalLM.from_pretrained(
       "meta-llama/Llama-3.1-8B-Instruct",
       quantization_config=quantization_config,
       device_map="auto",
       torch_dtype=torch.bfloat16
   )
   ```

2. **Gemma 2 9B IT**
   ```python
   model = AutoModelForCausalLM.from_pretrained(
       "google/gemma-2-9b-it",
       quantization_config=quantization_config,
       device_map="auto",
       torch_dtype=torch.bfloat16
   )
   ```

### Configurazioni di Fine-tuning

Abbiamo implementato diverse configurazioni di fine-tuning per esplorare l'impatto di vari fattori sulle performance dei modelli:

1. **Training con Tokenizer Standard**
   ```json
   {
     "model_name_or_path": "meta-llama/Llama-3.1-8B-Instruct",
     "per_device_train_batch_size": 2,
     "gradient_accumulation_steps": 4,
     "learning_rate": 1e-4,
     "num_train_epochs": 10,
     "use_peft": true,
     "lora_r": 64,
     "lora_alpha": 16,
     "load_in_4bit": true
   }
   ```

2. **Training con Tokenizer Personalizzato**
   ```python
   def build_tokenizer(tokenizer):
       """Aggiunge token speciali per SVG al tokenizer."""
       svg_tokens = [
           "<svg>", "</svg>", "<path>", "</path>", "<rect>", "</rect>",
           "<circle>", "</circle>", "<ellipse>", "</ellipse>",
           "<line>", "</line>", "<polyline>", "</polyline>",
           "<polygon>", "</polygon>", "<g>", "</g>"
       ]
       tokenizer.add_tokens(svg_tokens)
       return tokenizer
   ```

3. **Training con Dataset Diviso in Fasce di Complessità**
   ```python
   def categorize_complexity(svg_data):
       """Categorizza la complessità di un SVG in base al numero di elementi."""
       # Conta il numero di elementi SVG
       element_count = len(re.findall(r'<(path|rect|circle|ellipse|line|polyline|polygon)', svg_data))
       
       if element_count <= 5:
           return "simple"
       elif element_count <= 15:
           return "medium"
       else:
           return "complex"
   ```

4. **Training Ottimizzato per Convergenza**
   ```json
   {
     "num_train_epochs": 10,
     "evaluation_strategy": "steps",
     "eval_steps": 50,
     "save_strategy": "steps",
     "save_steps": 50,
     "load_best_model_at_end": true,
     "metric_for_best_model": "eval_loss",
     "early_stopping_patience": 30,
     "early_stopping_threshold": 0.01
   }
   ```

5. **Training Multi-GPU con Pesi Convergenti**
   ```json
   {
     "base_model_adapter_path": "/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test1_convergence/checkpoint-best",
     "use_custom_tokenizer": true,
     "deepspeed": {
       "zero_stage": 2,
       "offload_optimizer": {
         "device": "cpu",
         "pin_memory": true
       },
       "offload_param": {
         "device": "cpu",
         "pin_memory": true
       },
       "bf16": {
         "enabled": true
       }
     }
   }
   ```

## Tecniche di Ottimizzazione

### 1. Parameter-Efficient Fine-Tuning (PEFT) con LoRA

LoRA (Low-Rank Adaptation) permette di addestrare solo una piccola frazione dei parametri del modello, riducendo significativamente i requisiti di memoria e accelerando il training:

```python
from peft import LoraConfig, get_peft_model

lora_config = LoraConfig(
    r=64,                      # Dimensione del rango della matrice di adattamento
    lora_alpha=16,             # Fattore di scala per l'adattamento
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    lora_dropout=0.05,         # Dropout per regolarizzazione
    bias="none",
    task_type="CAUSAL_LM"
)

model = get_peft_model(model, lora_config)
```

Con questa configurazione, addestriamo solo una frazione dei parametri totali:
- Llama 3.1 8B: 167,772,160 parametri addestrabili (3.56% del totale)
- Gemma 2 9B IT: 216,072,192 parametri addestrabili (4.08% del totale)

### 2. Quantizzazione a 4-bit

La quantizzazione riduce la precisione dei pesi del modello da 32-bit a 4-bit, riducendo significativamente l'utilizzo di memoria:

```python
from transformers import BitsAndBytesConfig

quantization_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.bfloat16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)
```

### 3. Distributed Training con DeepSpeed ZeRO

DeepSpeed ZeRO (Zero Redundancy Optimizer) ottimizza l'utilizzo della memoria durante il training distribuito:

```json
"deepspeed": {
  "zero_stage": 2,
  "offload_optimizer": {
    "device": "cpu",
    "pin_memory": true
  },
  "offload_param": {
    "device": "cpu",
    "pin_memory": true
  },
  "bf16": {
    "enabled": true
  }
}
```

L'implementazione nel codice SLURM:

```bash
# Esegui lo script Python con torchrun per il training distribuito
torchrun --nproc_per_node=$SLURM_GPUS_ON_NODE \
    /work/tesi_ediluzio/experiments/xml_direct_input/train_lora_test1.py \
    --config_file "$CONFIG_FILE" \
    --deepspeed "$DEEPSPEED_CONFIG_FILE"
```

### 4. Tokenizer Personalizzato per SVG

Abbiamo implementato un tokenizer personalizzato che aggiunge token specifici per gli elementi SVG, migliorando la capacità del modello di comprendere la struttura del codice SVG:

```python
def build_tokenizer(tokenizer):
    svg_tokens = [
        "<svg>", "</svg>", "<path>", "</path>", "<rect>", "</rect>",
        "<circle>", "</circle>", "<ellipse>", "</ellipse>",
        "<line>", "</line>", "<polyline>", "</polyline>",
        "<polygon>", "</polygon>", "<g>", "</g>"
    ]
    num_added_tokens = tokenizer.add_tokens(svg_tokens)
    print(f"Aggiunti {num_added_tokens} token al vocabolario")
    return tokenizer

# Utilizzo nel training
if args.use_custom_tokenizer:
    tokenizer = build_tokenizer(tokenizer)
    model.resize_token_embeddings(len(tokenizer))
```

### 5. Early Stopping e Monitoraggio dell'Overfitting

Abbiamo implementato tecniche avanzate per monitorare e prevenire l'overfitting:

```python
class OverfittingDetectionCallback(TrainerCallback):
    def __init__(self, threshold=1.1):
        self.threshold = threshold
        self.best_eval_loss = float('inf')
        self.best_train_loss = float('inf')
        
    def on_evaluate(self, args, state, control, metrics, **kwargs):
        eval_loss = metrics.get("eval_loss", None)
        train_loss = metrics.get("train_loss", None)
        
        if eval_loss is not None and train_loss is not None:
            # Calcola il rapporto tra eval_loss e train_loss
            loss_ratio = eval_loss / train_loss
            
            # Se il rapporto supera la soglia, potrebbe esserci overfitting
            if loss_ratio > self.threshold:
                print(f"\n⚠️ Possibile overfitting rilevato! Rapporto eval_loss/train_loss: {loss_ratio:.4f}\n")
```

## Sistema di Valutazione dei Checkpoint

Abbiamo sviluppato un sistema completo per valutare i checkpoint dei modelli con metriche multiple:

```python
def evaluate_checkpoint(
    model_name_or_path: str,
    checkpoint_path: str,
    test_data: List[Dict[str, Any]],
    output_dir: str,
    use_custom_tokenizer: bool = False,
    load_in_4bit: bool = False,
    max_length: int = 512,
    use_clip: bool = False
) -> Dict[str, Any]:
    """Valuta un checkpoint specifico."""
    # Carica il modello e il tokenizer
    model, tokenizer = load_model_and_tokenizer(
        model_name_or_path,
        checkpoint_path,
        use_custom_tokenizer,
        load_in_4bit=load_in_4bit
    )
    
    # Misura il tempo di inferenza
    generated_captions = []
    inference_times = []
    
    for svg in tqdm(svgs, desc=f"Generazione caption"):
        start_time = time.time()
        caption = generate_caption(model, tokenizer, svg, max_length)
        end_time = time.time()
        
        inference_time = end_time - start_time
        generated_captions.append(caption)
        inference_times.append(inference_time)
    
    # Valuta le caption con le metriche COCO
    coco_metrics = evaluate_with_coco_metrics(references, generated_captions)
    
    # Calcola statistiche sulle didascalie
    caption_stats = calculate_caption_stats(generated_captions)
    
    # Calcola la perplexity
    perplexities = calculate_perplexity(model, tokenizer, svgs, references, max_length)
    
    # Calcola il CLIP score se richiesto
    if use_clip:
        clip_scores = calculate_clip_score(svgs, generated_captions)
        coco_metrics["clip_score"] = np.mean(clip_scores)
```

### Metriche Implementate

1. **Metriche di Qualità**:
   - BLEU-1/2/3/4
   - METEOR
   - CIDEr
   - CLIP Score

2. **Metriche di Efficienza**:
   - Tempo di inferenza
   - Perplexity

3. **Metriche di Analisi Linguistica**:
   - Lunghezza delle didascalie
   - Diversità del vocabolario
   - Type-Token Ratio
   - Self-BLEU

## Automazione del Workflow

Abbiamo implementato un sistema di automazione completo per gestire il workflow di training e valutazione:

### 1. Monitoraggio della Convergenza

```python
def monitor_convergence_and_launch_custom_token():
    """Monitora la convergenza dei modelli e avvia automaticamente il training successivo."""
    # Verifica lo stato dei job originali
    llama_job_running = check_job_status(args.llama_job_id)
    gemma_job_running = check_job_status(args.gemma_job_id)
    
    # Verifica se i job sono terminati e se è stato avviato un nuovo job
    if not llama_job_running and llama_new_job_id is None:
        # Cerca il miglior checkpoint
        llama_best_checkpoint = find_checkpoint_best(args.llama_dir)
        
        if llama_best_checkpoint:
            # Aggiorna la configurazione
            llama_config_path = update_config_with_best_checkpoint(
                args.llama_config, 
                llama_best_checkpoint
            )
            
            # Avvia il nuovo job
            llama_new_job_id = launch_training_job(
                llama_config_path, 
                "llama", 
                args.use_multi_gpu
            )
```

### 2. Training Multi-GPU con DeepSpeed

```bash
#!/bin/bash
#SBATCH --job-name=llama_custom_conv_multi
#SBATCH --ntasks-per-node=4
#SBATCH --gres=gpu:4
#SBATCH --mem=128G

# Crea la directory per il file di configurazione DeepSpeed
DEEPSPEED_CONFIG_DIR=$(dirname "$CONFIG_FILE")
DEEPSPEED_CONFIG_FILE="$DEEPSPEED_CONFIG_DIR/deepspeed_config.json"

# Estrai la configurazione DeepSpeed dal file di configurazione JSON
jq '.deepspeed' "$CONFIG_FILE" > "$DEEPSPEED_CONFIG_FILE"

# Esegui lo script Python con torchrun per il training distribuito
torchrun --nproc_per_node=$SLURM_GPUS_ON_NODE \
    /work/tesi_ediluzio/experiments/xml_direct_input/train_lora_test1.py \
    --config_file "$CONFIG_FILE" \
    --deepspeed "$DEEPSPEED_CONFIG_FILE"
```

### 3. Valutazione Automatica dei Checkpoint

```python
def monitor_and_evaluate_checkpoints():
    """Monitora e valuta automaticamente i nuovi checkpoint."""
    # Ottieni la lista dei checkpoint
    checkpoints = get_checkpoints(args.checkpoint_dir)
    
    # Trova il checkpoint più recente che soddisfa i criteri
    latest_checkpoint = None
    for checkpoint in reversed(checkpoints):
        if checkpoint["step"] > last_evaluated_step + args.min_step_interval:
            latest_checkpoint = checkpoint
            break
    
    if latest_checkpoint:
        # Invia un job per valutare solo questo checkpoint
        job_id = submit_evaluation_job(
            model_name=args.model_name,
            checkpoint_dir=latest_checkpoint["path"],
            test_file=args.test_file,
            output_dir=os.path.join(args.output_dir, latest_checkpoint["name"]),
            use_custom_tokenizer=args.use_custom_tokenizer,
            load_in_4bit=args.load_in_4bit,
            num_samples=args.num_samples,
            use_clip=args.use_clip
        )
```

## Risultati e Analisi

Il progetto ha prodotto risultati significativi attraverso diverse configurazioni di training:

1. **Training con Tokenizer Standard**:
   - Llama 3.1 8B: Loss finale 0.4776 (checkpoint 4000)
   - Gemma 2 9B IT: Loss finale 0.317 (checkpoint 3000)

2. **Training con Tokenizer Personalizzato**:
   - Llama 3.1 8B: Checkpoint finale 1800
   - Gemma 2 9B IT: Checkpoint finale 2400

3. **Training con Dataset Diviso in Fasce**:
   - Llama 3.1 8B: Loss finale 0.4352 (checkpoint 525)
   - Gemma 2 9B IT: Loss finale 0.3005 (checkpoint 525)

4. **Training Ottimizzato per Convergenza**:
   - Attualmente in corso con monitoraggio avanzato dell'overfitting

5. **Training Multi-GPU con Pesi Convergenti**:
   - Pianificato per iniziare automaticamente al termine del training ottimizzato per convergenza

## Conclusioni e Implicazioni per la Tesi

Questo progetto fornisce un'analisi approfondita delle tecniche di fine-tuning per modelli linguistici nel contesto della generazione di didascalie per immagini SVG. Le metodologie implementate rappresentano lo stato dell'arte nell'ottimizzazione di modelli di grandi dimensioni, con particolare attenzione a:

1. **Efficienza Computazionale**: Attraverso tecniche come LoRA, quantizzazione e distributed training, abbiamo dimostrato come sia possibile addestrare modelli di miliardi di parametri con risorse limitate.

2. **Specializzazione del Modello**: L'implementazione di tokenizer personalizzati e la divisione del dataset in fasce di complessità mostrano come sia possibile adattare modelli generici a domini specifici.

3. **Automazione del Workflow**: Il sistema di monitoraggio e valutazione automatica rappresenta un approccio innovativo alla gestione di esperimenti complessi di machine learning.

4. **Valutazione Multi-metrica**: L'implementazione di un sistema di valutazione che considera non solo la qualità delle didascalie ma anche l'efficienza e le caratteristiche linguistiche fornisce una visione olistica delle performance dei modelli.

Questi risultati e metodologie forniscono una base solida per una tesi che esplora l'intersezione tra modelli linguistici, computer vision e grafica vettoriale, con potenziali applicazioni in campi come l'accessibilità web, la generazione automatica di contenuti e l'interpretazione di immagini tecniche.
