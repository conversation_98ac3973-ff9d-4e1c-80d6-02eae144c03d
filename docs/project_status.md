# Stato Attuale del Progetto SVG Captioning

Questo documento fornisce una panoramica completa dello stato attuale del progetto di SVG captioning, inclusi i job in esecuzione, le configurazioni dei modelli e i prossimi passi.

## Job Attivi

### Job di Training

1. **`llama_te` (2578401)**
   - **Modello:** Llama 3.1 8B Instruct
   - **Configurazione:** Test 3 Multi-GPU
   - **Hardware:** 2x GPU (L40S 48GB o A40 48GB)
   - **Stato:** In attesa (PD)
   - **Parametri principali:**
     - Batch Size: 4 per GPU (effettivo 8)
     - Gradient Accumulation: 1 (nessun accumulo)
     - Learning Rate: 1e-5
     - LoRA: r=16, alpha=32
     - Target Modules: ["q_proj", "v_proj", "k_proj", "o_proj"]
     - Early Stopping: patience=100, min_delta=0.0001

2. **`gemma_te` (2578402)**
   - **Modello:** Gemma 2 9B IT
   - **Configurazione:** Test 3 Multi-GPU
   - **Hardware:** 2x GPU (L40S 48GB o A40 48GB)
   - **Stato:** In attesa (PD)
   - **Parametri principali:**
     - Batch Size: 4 per GPU (effettivo 8)
     - Gradient Accumulation: 1 (nessun accumulo)
     - Learning Rate: 1e-5
     - LoRA: r=16, alpha=32
     - Target Modules: ["q_proj", "v_proj", "k_proj", "o_proj"]
     - Early Stopping: patience=100, min_delta=0.0001

### Job di Valutazione

1. **`test_svg` (2578252)**
   - **Descrizione:** Test di rendering SVG con diverse librerie
   - **Librerie testate:** CairoSVG, svglib, Inkscape
   - **Numero di campioni:** 5
   - **Tempo limite:** 24 ore
   - **Stato:** In esecuzione (R)

2. **`ext_capt` (2578232)**
   - **Descrizione:** Valutazione di captioner esterni
   - **Modelli testati:** BLIP, ViT-GPT2, CogVLM
   - **Metriche:** BLEU-4, ROUGE-L, METEOR, CIDEr, CLIP Score
   - **Tempo limite:** 24 ore
   - **Stato:** In esecuzione (R)

## Evoluzione del Progetto

### Test 1 (Convergence)
- **Obiettivo:** Training fino a convergenza
- **Caratteristiche:** Single-GPU, parametri LoRA conservativi (r=4, alpha=8)
- **Problemi riscontrati:** Convergenza lenta, possibile underfitting

### Test 2 (Multi-GPU)
- **Obiettivo:** Accelerare il training con multi-GPU
- **Caratteristiche:** 2 GPU, batch size aumentato (4), gradient accumulation ridotto (2)
- **Problemi riscontrati:** Loss sempre 0.0, grad_norm NaN

### Test 3 (Semplificato)
- **Obiettivo:** Risolvere i problemi di training e migliorare la stabilità
- **Caratteristiche:** Single-GPU, parametri LoRA aumentati (r=16, alpha=32), DeepSpeed disabilitato, learning rate ridotto (1e-5)
- **Miglioramenti:** Debug avanzato, early stopping sulla loss di validazione, target modules estesi

### Test 3 Multi-GPU (Attuale)
- **Obiettivo:** Combinare i miglioramenti del Test 3 con l'accelerazione multi-GPU
- **Caratteristiche:** 2 GPU per modello, parametri LoRA aumentati (r=16, alpha=32), DeepSpeed disabilitato, learning rate ridotto (1e-5)
- **Miglioramenti:** Maggiore velocità di training mantenendo la stabilità del Test 3
- **Stato:** In attesa di risorse

## Miglioramenti Implementati

1. **Parametri LoRA ottimizzati:**
   - Rank aumentato da 4 a 16
   - Alpha aumentato da 8 a 32
   - Target modules estesi a tutti i componenti dell'attenzione

2. **Stabilità del training:**
   - Learning rate ridotto a 1e-5
   - Early stopping sulla loss di validazione
   - Batch size ottimizzato (4 per GPU)
   - Nessun gradient accumulation

3. **Monitoraggio e analisi:**
   - Funzione per il conteggio dei parametri trainabili
   - Logging dettagliato su Weights & Biands
   - Salvataggio del miglior modello in base alla loss di validazione

4. **Valutazione esterna:**
   - Test di rendering SVG con diverse librerie
   - Valutazione con captioner esterni (BLIP, ViT-GPT2, CogVLM)
   - Implementazione del CLIP Score

## Documentazione

1. **Parametri di training:**
   - `/work/tesi_ediluzio/docs/training_parameters.md`
   - Descrizione dettagliata di tutti i parametri utilizzati

2. **Parametri LoRA:**
   - `/work/tesi_ediluzio/docs/lora_parameters.md`
   - Guida completa ai parametri LoRA e al loro impatto

3. **Captioner esterni e CLIP:**
   - `/work/tesi_ediluzio/docs/external_captioners_and_clip.md`
   - Descrizione dei captioner esterni e della metodologia di valutazione CLIP

4. **Model cards:**
   - `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test3_multi_gpu/README.md`
   - `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test3_multi_gpu/README.md`
   - Descrizione dettagliata dei modelli e delle loro configurazioni

## Prossimi Passi

1. **Training:**
   - Monitorare i job di training `llama_te` e `gemma_te`
   - Analizzare le curve di loss (training vs validation)
   - Verificare la convergenza e l'assenza di overfitting/underfitting

2. **Valutazione:**
   - Analizzare i risultati dei captioner esterni
   - Confrontare le performance dei modelli fine-tuned con i captioner esterni
   - Valutare l'efficacia del CLIP Score come metrica

3. **Inferenza:**
   - Eseguire inferenza con i modelli fine-tuned su diverse fasce di complessità
   - Generare report HTML con esempi qualitativi
   - Creare grafici radar per confrontare le performance

4. **Documentazione:**
   - Aggiornare la documentazione con i risultati finali
   - Preparare materiale per la tesi

## Configurazione Attuale

### Dataset
- **Training:** `/work/tesi_ediluzio/data/processed/xml_format/train_set_final_xml.json`
- **Validazione:** `/work/tesi_ediluzio/data/processed/xml_format/test_set_final_2k_xml.json`
- **Formato input:** `<s>[INST] Descrivi questa immagine SVG:\n{svg} [/INST] {caption}</s>`

### Hardware
- **GPU:** 2x GPU (L40S 48GB o A40 48GB) per modello
- **Memoria:** 48GB per job
- **CPU:** 8 core per job

### Software
- **Framework:** PyTorch 2.2.0 + Transformers 4.51.3 + PEFT 0.15.1
- **Ambiente:** Python 3.10 (svg_captioning_env)
- **Librerie aggiuntive:**
  - BitsAndBytes 0.41.3 (per quantizzazione)
  - Weights & Biands (per tracking)

## Conclusioni

Il progetto ha attraversato diverse fasi di ottimizzazione, affrontando e risolvendo problemi di stabilità e performance. Attualmente, i job di training sono configurati con parametri ottimizzati per garantire un training stabile ed efficiente, mentre i job di valutazione stanno raccogliendo dati per confrontare le performance dei modelli fine-tuned con i captioner esterni.

I prossimi passi si concentreranno sull'analisi dei risultati, l'inferenza con i modelli fine-tuned e la preparazione della documentazione finale per la tesi.
