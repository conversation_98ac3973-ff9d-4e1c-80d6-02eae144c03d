# Sistema di Monitoraggio e Riavvio Automatico dei Job (12/05/2025)

## Panoramica
Il sistema di monitoraggio e riavvio automatico dei job è stato implementato per garantire la continuità dei training anche in caso di interruzioni dovute a limiti di tempo o altri problemi. Il sistema controlla periodicamente lo stato dei job di training e, se un job termina, lo riavvia automaticamente dall'ultimo checkpoint disponibile.

## Componenti del Sistema

### 1. Script di Riavvio per Llama
**File**: `/work/tesi_ediluzio/restart_llama_convergence.slurm`

Questo script è configurato per riavviare il training di Llama 3.1 8B dall'ultimo checkpoint disponibile. Caratteristiche principali:
- Trova automaticamente l'ultimo checkpoint disponibile
- Utilizza la configurazione originale del training
- Implementa early stopping con patience=30 per evitare overfitting
- Continua il tracciamento su Weights & Biands con lo stesso run name

### 2. Script di Riavvio per Gemma
**File**: `/work/tesi_ediluzio/restart_gemma_convergence.slurm`

Questo script è configurato per riavviare il training di Gemma 2 9B IT dall'ultimo checkpoint disponibile. Caratteristiche principali:
- Trova automaticamente l'ultimo checkpoint disponibile
- Utilizza la configurazione originale del training
- Implementa early stopping con patience=30 per evitare overfitting
- Continua il tracciamento su Weights & Biands con lo stesso run name

### 3. Script di Monitoraggio
**File**: `/work/tesi_ediluzio/monitor_and_restart_jobs.py`

Questo script monitora lo stato dei job di training e li riavvia se necessario. Caratteristiche principali:
- Controlla periodicamente lo stato dei job specificati
- Se un job termina, lo riavvia utilizzando lo script di riavvio corrispondente
- Aggiorna automaticamente l'ID del job dopo il riavvio
- Configurabile per monitorare più job contemporaneamente

### 4. Job di Monitoraggio
**File**: `/work/tesi_ediluzio/run_job_monitor.slurm`

Questo script SLURM avvia lo script di monitoraggio come un job a lungo termine. Caratteristiche principali:
- Configurato per eseguire il monitoraggio per 24 ore
- Utilizza risorse minime (1 CPU, 4GB di RAM)
- Genera log dettagliati delle operazioni di monitoraggio
- Job ID: 2566918

## Stato Attuale dei Job

### Llama 3.1 8B (Job 2578151)
- In attesa (Priority)
- Riavviato da zero
- Configurazione: LoRA con rank=4, alpha=8, dropout=0.1
- Batch size: 4 per GPU (2 GPU)
- Sequenze di 256 token
- Gradient accumulation: 2
- Tracciamento su Weights & Biands: test2_llama31_8b_800
- Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/llama31_8b_test2_multi_gpu/`

### Gemma 2 9B IT (Job 2578150)
- In attesa (Priority)
- Riavviato da zero
- Configurazione: LoRA con rank=4, alpha=8, dropout=0.1
- Batch size: 4 per GPU (2 GPU)
- Sequenze di 256 token
- Gradient accumulation: 2
- Tracciamento su Weights & Biands: test2_gemma2_9b_it_800
- Checkpoint salvati in `/work/tesi_ediluzio/experiments/xml_direct_input/outputs/gemma2_9b_it_test2_multi_gpu/`

### Captioner Esterni (Job 2577381)
- ✅ Completato sul nodo "huber"
- ⚠️ Solo il modello BLIP ha generato risultati validi
- ❌ I modelli vit-gpt2 e cogvlm hanno fallito la generazione di didascalie
- Risultati disponibili in `/work/tesi_ediluzio/experiments/xml_direct_input/evaluation_results/external_captioners_prerendered/`
- Necessario investigare i problemi con vit-gpt2 e cogvlm

## Monitoraggio e Controllo

### Monitoraggio del Job di Monitoraggio
```bash
tail -f /work/tesi_ediluzio/logs/job_monitor_2566918.out
```

### Monitoraggio dei Job di Training
```bash
tail -f /work/tesi_ediluzio/logs/llama_test2_2578151.out
tail -f /work/tesi_ediluzio/logs/gemma_test2_2578150.out
```

### Controllo dello Stato dei Job
```bash
squeue -u ediluzio
```

## Vantaggi del Sistema
1. **Continuità del Training**: Garantisce che i job di training continuino a funzionare anche in caso di interruzioni
2. **Utilizzo Efficiente dei Checkpoint**: Riprende sempre dall'ultimo checkpoint disponibile
3. **Monitoraggio Automatico**: Non richiede intervento manuale per riavviare i job
4. **Tracciamento Continuo**: Mantiene la continuità del tracciamento su Weights & Biands
5. **Configurazione Flessibile**: Facilmente adattabile per monitorare altri job

## Prossimi Passi
1. **Monitoraggio della Convergenza**: Implementare un sistema per monitorare la convergenza dei modelli
2. **Notifiche Automatiche**: Aggiungere notifiche via email o Slack quando un job viene riavviato
3. **Dashboard di Monitoraggio**: Creare una dashboard per visualizzare lo stato dei job in tempo reale
4. **Integrazione con Weights & Biands**: Migliorare l'integrazione con Weights & Biands per tracciare gli eventi di riavvio
