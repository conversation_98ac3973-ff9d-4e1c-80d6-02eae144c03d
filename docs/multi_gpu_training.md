# Divisione logica dei job di training su più CPU/GPU

## 1. Configurazione hardware nei job Slurm

Nei nostri script Slurm, abbiamo specificato l'allocazione delle risorse hardware in questo modo:

```bash
#SBATCH --nodes=1             # Numero di nodi
#SBATCH --ntasks-per-node=1   # Numero di task per nodo
#SBATCH --gpus=2              # Numero di GPU
#SBATCH --cpus-per-task=8     # Numero di CPU per task
#SBATCH --mem=64G             # Memoria allocata
```

Questa configurazione richiede:
- 1 nodo di calcolo
- 2 GPU su quel nodo
- 8 CPU per task
- 64GB di memoria RAM

## 2. Parallelizzazione a livello di batch

Nel codice di training, abbiamo implementato la parallelizzazione a livello di batch con:

```python
# Configurazione del batch size
per_device_train_batch_size = 8    # Batch size per GPU
gradient_accumulation_steps = 2    # Passi di accumulo del gradiente
num_gpus = 2                       # Numero di GPU
```

Il batch size effettivo è quindi: `8 (per GPU) × 2 (GPU) × 2 (accumulo) = 32`

## 3. Distribuzione del carico di lavoro

La distribuzione del carico avviene in diversi livelli:

### a) Distribuzione tra GPU
Abbiamo utilizzato il Distributed Data Parallel (DDP) di PyTorch, che:
- Divide automaticamente i batch di dati tra le GPU disponibili
- Sincronizza i gradienti tra le GPU dopo ogni passo forward/backward
- Mantiene copie identiche del modello su ogni GPU

### b) Accumulo del gradiente
L'accumulo del gradiente permette di simulare batch più grandi:
- Esegue più passi forward/backward senza aggiornare i pesi
- Accumula i gradienti per un numero specificato di passi (2 nel nostro caso)
- Aggiorna i pesi solo dopo aver accumulato i gradienti

## 4. Implementazione nel codice

Nel codice di training, questo è implementato tramite:

```python
# Inizializzazione del training distribuito
model = DistributedDataParallel(model, device_ids=[local_rank])

# Loop di training
for batch in dataloader:
    # Forward pass
    outputs = model(batch)
    loss = outputs.loss / gradient_accumulation_steps
    
    # Backward pass
    loss.backward()
    
    # Aggiornamento dei pesi solo dopo l'accumulo
    if (step + 1) % gradient_accumulation_steps == 0:
        optimizer.step()
        optimizer.zero_grad()
```

## 5. Vantaggi di questo approccio

1. **Efficienza di memoria**: Permette di addestrare modelli più grandi dividendo il carico su più GPU
2. **Velocità di training**: Riduce significativamente il tempo di addestramento
3. **Batch size più grandi**: Consente di utilizzare batch size più grandi, migliorando la stabilità del training
4. **Utilizzo ottimale delle risorse**: Massimizza l'utilizzo delle risorse hardware disponibili

## 6. Monitoraggio e bilanciamento

Per garantire un utilizzo efficiente delle risorse:
- Abbiamo monitorato l'utilizzo delle GPU con Weights & Biands
- Abbiamo bilanciato il carico tra le GPU per evitare colli di bottiglia
- Abbiamo ottimizzato i parametri di training (batch size, accumulo del gradiente) in base alle risorse disponibili

Questo approccio ci ha permesso di addestrare modelli complessi come Llama 3.1 8B e Gemma 2 9B IT in modo efficiente, sfruttando al massimo le risorse hardware disponibili.
