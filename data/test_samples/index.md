# Campioni di Test per la Valutazione Finale

Numero di campioni: 50

## Elenco dei Campioni

### Campione 1

**Didascalia originale**: The image depicts a bookmark with a rectangular shape

**SVG**: [sample_1.svg](svg/sample_1.svg)

**JSON**: [sample_1.json](sample_1.json)

### Campione 2

**Didascalia originale**: The image depicts the answer is simple and straightforward

**SVG**: [sample_2.svg](svg/sample_2.svg)

**JSON**: [sample_2.json](sample_2.json)

### Campione 3

**Didascalia originale**: The image depicts two overlapping rectangles

**SVG**: [sample_3.svg](svg/sample_3.svg)

**JSON**: [sample_3.json](sample_3.json)

### Campione 4

**Didascalia originale**: The image depicts a simple, iconic symbol of a circular arrow forming a loop

**SVG**: [sample_4.svg](svg/sample_4.svg)

**JSON**: [sample_4.json](sample_4.json)

### Campione 5

**Didascalia originale**: The image depicts a simple, abstract geometric figure that consists of a central circular shape with two elongated, symmetrical extensions extending from the top and bottom

**SVG**: [sample_5.svg](svg/sample_5.svg)

**JSON**: [sample_5.json](sample_5.json)

### Campione 6

**Didascalia originale**: The image depicts a red diamond shape with a unique design

**SVG**: [sample_6.svg](svg/sample_6.svg)

**JSON**: [sample_6.json](sample_6.json)

### Campione 7

**Didascalia originale**: The image depicts a simple, minimalistic logo consisting of two stylized leaves

**SVG**: [sample_7.svg](svg/sample_7.svg)

**JSON**: [sample_7.json](sample_7.json)

### Campione 8

**Didascalia originale**: The image depicts a stylized graphic consisting of two large, overlapping squares

**SVG**: [sample_8.svg](svg/sample_8.svg)

**JSON**: [sample_8.json](sample_8.json)

### Campione 9

**Didascalia originale**: The image depicts a simple directional sign with a black arrow pointing to the right

**SVG**: [sample_9.svg](svg/sample_9.svg)

**JSON**: [sample_9.json](sample_9.json)

### Campione 10

**Didascalia originale**: The image depicts a simple, black speech bubble with a white, horizontal line and a smaller, white, vertical line inside it

**SVG**: [sample_10.svg](svg/sample_10.svg)

**JSON**: [sample_10.json](sample_10.json)

### Campione 11

**Didascalia originale**: The image depicts 

**SVG**: [sample_11.svg](svg/sample_11.svg)

**JSON**: [sample_11.json](sample_11.json)

### Campione 12

**Didascalia originale**: The image depicts a simple geometric shape, specifically a vertical line with a circular cap at the top

**SVG**: [sample_12.svg](svg/sample_12.svg)

**JSON**: [sample_12.json](sample_12.json)

### Campione 13

**Didascalia originale**: The image depicts a simple, minimalist design of a pair of earrings

**SVG**: [sample_13.svg](svg/sample_13.svg)

**JSON**: [sample_13.json](sample_13.json)

### Campione 14

**Didascalia originale**: The image depicts a simple geometric shape, specifically a circle with a diameter that is twice the length of its radius

**SVG**: [sample_14.svg](svg/sample_14.svg)

**JSON**: [sample_14.json](sample_14.json)

### Campione 15

**Didascalia originale**: The image depicts a stylized graphic consisting of two primary shapes: a circle and a lightning bolt

**SVG**: [sample_15.svg](svg/sample_15.svg)

**JSON**: [sample_15.json](sample_15.json)

### Campione 16

**Didascalia originale**: The image depicts a diamond-shaped diamond with a pink fill color and a black outline

**SVG**: [sample_16.svg](svg/sample_16.svg)

**JSON**: [sample_16.json](sample_16.json)

### Campione 17

**Didascalia originale**: The image depicts 

**SVG**: [sample_17.svg](svg/sample_17.svg)

**JSON**: [sample_17.json](sample_17.json)

### Campione 18

**Didascalia originale**: The image depicts a simple geometric shape, specifically a black arrow pointing upwards

**SVG**: [sample_18.svg](svg/sample_18.svg)

**JSON**: [sample_18.json](sample_18.json)

### Campione 19

**Didascalia originale**: The image depicts a single musical note, which is a fundamental symbol used in music notation

**SVG**: [sample_19.svg](svg/sample_19.svg)

**JSON**: [sample_19.json](sample_19.json)

### Campione 20

**Didascalia originale**: The image depicts a single uppercase letter "A" in a bold, sans-serif font

**SVG**: [sample_20.svg](svg/sample_20.svg)

**JSON**: [sample_20.json](sample_20.json)

### Campione 21

**Didascalia originale**: The image depicts the image contains a single, black horizontal line that spans the width of the image

**SVG**: [sample_21.svg](svg/sample_21.svg)

**JSON**: [sample_21.json](sample_21.json)

### Campione 22

**Didascalia originale**: The image depicts a simple, minimalist graphic of a less-than symbol (

**SVG**: [sample_22.svg](svg/sample_22.svg)

**JSON**: [sample_22.json](sample_22.json)

### Campione 23

**Didascalia originale**: The image depicts the image is a photograph of a white rectangular piece of paper with a diagonal line running from the top left corner to the bottom right corner

**SVG**: [sample_23.svg](svg/sample_23.svg)

**JSON**: [sample_23.json](sample_23.json)

### Campione 24

**Didascalia originale**: The image depicts a simple graphic design featuring a red circle with a black border

**SVG**: [sample_24.svg](svg/sample_24.svg)

**JSON**: [sample_24.json](sample_24.json)

### Campione 25

**Didascalia originale**: The image depicts a simple geometric shape, specifically a half-circle

**SVG**: [sample_25.svg](svg/sample_25.svg)

**JSON**: [sample_25.json](sample_25.json)

### Campione 26

**Didascalia originale**: The image depicts a simple, minimalist design featuring a single, vertical, cylindrical shape with a circular base

**SVG**: [sample_26.svg](svg/sample_26.svg)

**JSON**: [sample_26.json](sample_26.json)

### Campione 27

**Didascalia originale**: The image depicts a simple, two-legged ladder

**SVG**: [sample_27.svg](svg/sample_27.svg)

**JSON**: [sample_27.json](sample_27.json)

### Campione 28

**Didascalia originale**: The image depicts 

**SVG**: [sample_28.svg](svg/sample_28.svg)

**JSON**: [sample_28.json](sample_28.json)

### Campione 29

**Didascalia originale**: The image depicts a simple, abstract symbol that resembles a stylized, upright, and elongated cup or goblet

**SVG**: [sample_29.svg](svg/sample_29.svg)

**JSON**: [sample_29.json](sample_29.json)

### Campione 30

**Didascalia originale**: The image depicts a simple geometric shape, specifically a black rightward-pointing arrow

**SVG**: [sample_30.svg](svg/sample_30.svg)

**JSON**: [sample_30.json](sample_30.json)

### Campione 31

**Didascalia originale**: The image depicts 

**SVG**: [sample_31.svg](svg/sample_31.svg)

**JSON**: [sample_31.json](sample_31.json)

### Campione 32

**Didascalia originale**: The image depicts a simple geometric shape, specifically a right-angled triangle

**SVG**: [sample_32.svg](svg/sample_32.svg)

**JSON**: [sample_32.json](sample_32.json)

### Campione 33

**Didascalia originale**: The image depicts a simple, minimalist graphic icon

**SVG**: [sample_33.svg](svg/sample_33.svg)

**JSON**: [sample_33.json](sample_33.json)

### Campione 34

**Didascalia originale**: The image depicts a minimalist and abstract design, featuring a series of geometric shapes and lines

**SVG**: [sample_34.svg](svg/sample_34.svg)

**JSON**: [sample_34.json](sample_34.json)

### Campione 35

**Didascalia originale**: The image depicts a flag with a rectangular shape

**SVG**: [sample_35.svg](svg/sample_35.svg)

**JSON**: [sample_35.json](sample_35.json)

### Campione 36

**Didascalia originale**: The image depicts a simple, bold, and straightforward graphic design featuring the number "4" in a large, white font against a solid blue background

**SVG**: [sample_36.svg](svg/sample_36.svg)

**JSON**: [sample_36.json](sample_36.json)

### Campione 37

**Didascalia originale**: The image depicts a simple, minimalistic design of a book

**SVG**: [sample_37.svg](svg/sample_37.svg)

**JSON**: [sample_37.json](sample_37.json)

### Campione 38

**Didascalia originale**: The image depicts a simple geometric shape, specifically a square

**SVG**: [sample_38.svg](svg/sample_38.svg)

**JSON**: [sample_38.json](sample_38.json)

### Campione 39

**Didascalia originale**: The image depicts a simple, geometric figure resembling a photograph frame

**SVG**: [sample_39.svg](svg/sample_39.svg)

**JSON**: [sample_39.json](sample_39.json)

### Campione 40

**Didascalia originale**: The image depicts a simple, minimalist design consisting of two primary elements: a large, black, rectangular shape and a horizontal, black line

**SVG**: [sample_40.svg](svg/sample_40.svg)

**JSON**: [sample_40.json](sample_40.json)

### Campione 41

**Didascalia originale**: The image depicts a simple, rectangular box with a black border and a black arrow pointing to the right

**SVG**: [sample_41.svg](svg/sample_41.svg)

**JSON**: [sample_41.json](sample_41.json)

### Campione 42

**Didascalia originale**: The image depicts a simple, two-dimensional heart shape

**SVG**: [sample_42.svg](svg/sample_42.svg)

**JSON**: [sample_42.json](sample_42.json)

### Campione 43

**Didascalia originale**: The image depicts a simple geometric shape, specifically a plus sign (

**SVG**: [sample_43.svg](svg/sample_43.svg)

**JSON**: [sample_43.json](sample_43.json)

### Campione 44

**Didascalia originale**: The image depicts a red and white sign with a simple, bold design

**SVG**: [sample_44.svg](svg/sample_44.svg)

**JSON**: [sample_44.json](sample_44.json)

### Campione 45

**Didascalia originale**: The image depicts a simple, single black arrow pointing to the left

**SVG**: [sample_45.svg](svg/sample_45.svg)

**JSON**: [sample_45.json](sample_45.json)

### Campione 46

**Didascalia originale**: The image depicts a simple, minimalist design of a paper airplane

**SVG**: [sample_46.svg](svg/sample_46.svg)

**JSON**: [sample_46.json](sample_46.json)

### Campione 47

**Didascalia originale**: The image depicts a symbol that is commonly known as the Ankh, which is an ancient Egyptian hieroglyphic symbol

**SVG**: [sample_47.svg](svg/sample_47.svg)

**JSON**: [sample_47.json](sample_47.json)

### Campione 48

**Didascalia originale**: The image depicts a credit card icon

**SVG**: [sample_48.svg](svg/sample_48.svg)

**JSON**: [sample_48.json](sample_48.json)

### Campione 49

**Didascalia originale**: The image depicts a simple, yet clear and straightforward graphic element

**SVG**: [sample_49.svg](svg/sample_49.svg)

**JSON**: [sample_49.json](sample_49.json)

### Campione 50

**Didascalia originale**: The image depicts a simple, minimalist design of a container, likely a box or a jar, with a lid

**SVG**: [sample_50.svg](svg/sample_50.svg)

**JSON**: [sample_50.json](sample_50.json)

