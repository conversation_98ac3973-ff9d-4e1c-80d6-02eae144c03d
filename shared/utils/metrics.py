# shared/utils/metrics.py
import numpy as np
import math
import logging
from collections import Counter
from functools import lru_cache
from typing import List, Dict, Tuple, Union
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from rouge_score import rouge_scorer
import time

logger = logging.getLogger(__name__)

class CaptionEvaluator:
    def __init__(self, n_grams: int = 4, cache_size: int = 10000):
        """
        Inizializza l'evaluator con configurazione per tutte le metriche
        
        Args:
            n_grams: Ordine massimo di n-grammi da considerare per CIDEr (default 4)
            cache_size: Dimensione della cache per i calcoli TF-IDF (default 10000)
        """
        self.scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
        self.smoothing = SmoothingFunction().method1
        self.n_grams = n_grams
        self.doc_freq = None
        self.ref_len = None
        self.avg_ref_length = None
        
        # Configura cache per TF-IDF
        self.get_tfidf = lru_cache(maxsize=cache_size)(self._get_tfidf)
        
        logger.info(f"CaptionEvaluator initialized with n_grams={n_grams}, cache_size={cache_size}")

    # Implementazione CIDEr (come nel tuo codice originale)
    def _precompute_doc_freq(self, all_references: List[List[str]]):
        """Pre-calcola le document frequencies per il corpus di riferimento"""
        logger.info("Precomputing document frequencies for CIDEr metric...")
        start_time = time.time()
        
        doc_freq = Counter()
        total_docs = 0
        
        for refs in all_references:
            all_words = ' '.join(refs).split()
            length = len(all_words)
            
            for i in range(1, self.n_grams + 1):
                for j in range(length - i + 1):
                    ngram = ' '.join(all_words[j:j+i])
                    doc_freq[ngram] += 1
            
            total_docs += 1
        
        self.doc_freq = doc_freq
        self.ref_len = total_docs
        self.avg_ref_length = sum(len(refs[0].split()) for refs in all_references) / len(all_references)
        
        logger.info(f"Document frequencies computed in {time.time()-start_time:.2f}s - {len(doc_freq)} ngrams")

    def _get_tfidf(self, text: str) -> Dict[str, float]:
        """Calcola vettore TF-IDF per un testo con caching"""
        words = text.split()
        length = len(words)
        vec = {}
        
        for i in range(1, self.n_grams + 1):
            for j in range(length - i + 1):
                ngram = ' '.join(words[j:j+i])
                vec[ngram] = vec.get(ngram, 0) + 1
        
        norm = 0
        for ngram, count in vec.items():
            df = max(1, self.doc_freq.get(ngram, 0))
            idf = math.log((self.ref_len + 1) / df)
            tf = count / (length - len(ngram.split()) + 1)
            vec[ngram] = tf * idf
            norm += vec[ngram] ** 2
        
        norm = norm ** 0.5
        if norm > 0:
            for ngram in vec:
                vec[ngram] /= norm
        
        return vec

    def _compute_cider_single(self, references: List[str], candidate: str) -> float:
        """Calcola CIDEr per un singolo esempio"""
        candidate_vec = self.get_tfidf(candidate)
        max_sim = 0
        
        for ref in references:
            ref_vec = self.get_tfidf(ref)
            sim = 0
            all_ngrams = set(ref_vec.keys()).union(set(candidate_vec.keys()))
            
            for ngram in all_ngrams:
                sim += ref_vec.get(ngram, 0) * candidate_vec.get(ngram, 0)
            
            if sim > max_sim:
                max_sim = sim
        
        candidate_len = len(candidate.split())
        length_penalty = np.exp(1 - (self.avg_ref_length / candidate_len)) if candidate_len < self.avg_ref_length else 1
        
        return max_sim * length_penalty * 10

    def compute_cider(self, references: List[List[str]], candidates: List[str]) -> float:
        """Calcola CIDEr su un batch di esempi"""
        assert len(references) == len(candidates), "References and candidates must have same length"
        
        if self.doc_freq is None:
            self._precompute_doc_freq(references)
        
        logger.info(f"Computing CIDEr for {len(candidates)} examples...")
        start_time = time.time()
        
        scores = []
        batch_size = len(candidates)
        
        for i, (refs, cand) in enumerate(zip(references, candidates)):
            score = self._compute_cider_single(refs, cand)
            scores.append(score)
            
            if (i + 1) % 1000 == 0:
                logger.info(f"Processed {i+1}/{batch_size} examples - {(i+1)/batch_size*100:.1f}%")
        
        avg_score = np.mean(scores)
        logger.info(f"CIDER computation completed in {time.time()-start_time:.2f}s - Average score: {avg_score:.4f}")
        
        return avg_score

    def compute_bleu(self, reference: List[str], candidate: str) -> Dict[str, float]:
        """Calcola i punteggi BLEU-1 a BLEU-4"""
        if isinstance(reference, str):
            reference = [reference]
            
        ref_tokens = [ref.split() for ref in reference]
        cand_tokens = candidate.split()
        
        return {
            'bleu1': sentence_bleu(ref_tokens, cand_tokens, weights=(1, 0, 0, 0), smoothing_function=self.smoothing),
            'bleu2': sentence_bleu(ref_tokens, cand_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=self.smoothing),
            'bleu3': sentence_bleu(ref_tokens, cand_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=self.smoothing),
            'bleu4': sentence_bleu(ref_tokens, cand_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=self.smoothing)
        }

    def compute_rouge(self, reference: str, candidate: str) -> Dict[str, float]:
        """Calcola i punteggi ROUGE-1, ROUGE-2, ROUGE-L"""
        scores = self.scorer.score(reference, candidate)
        return {
            'rouge1': scores['rouge1'].fmeasure,
            'rouge2': scores['rouge2'].fmeasure,
            'rougeL': scores['rougeL'].fmeasure
        }

    def evaluate_all(self, references: List[List[str]], candidates: List[str]) -> Dict[str, float]:
        """Calcola tutte le metriche per un batch di esempi"""
        assert len(references) == len(candidates), "References and candidates must have same length"
        
        bleu_scores = {'bleu1': [], 'bleu2': [], 'bleu3': [], 'bleu4': []}
        rouge_scores = {'rouge1': [], 'rouge2': [], 'rougeL': []}
        
        # Calcola BLEU e ROUGE
        for refs, cand in zip(references, candidates):
            # BLEU
            bleu = self.compute_bleu(refs, cand)
            for k in bleu_scores:
                bleu_scores[k].append(bleu[k])
            
            # ROUGE (massimo tra i riferimenti)
            max_rouge = {'rouge1': 0, 'rouge2': 0, 'rougeL': 0}
            for ref in refs:
                rouge = self.compute_rouge(ref, cand)
                for k in max_rouge:
                    if rouge[k] > max_rouge[k]:
                        max_rouge[k] = rouge[k]
            for k in rouge_scores:
                rouge_scores[k].append(max_rouge[k])
        
        # Calcola CIDEr
        cider_score = self.compute_cider(references, candidates)
        
        # Aggrega i risultati
        return {
            'bleu1': np.mean(bleu_scores['bleu1']),
            'bleu2': np.mean(bleu_scores['bleu2']),
            'bleu3': np.mean(bleu_scores['bleu3']),
            'bleu4': np.mean(bleu_scores['bleu4']),
            'rouge1': np.mean(rouge_scores['rouge1']),
            'rouge2': np.mean(rouge_scores['rouge2']),
            'rougeL': np.mean(rouge_scores['rougeL']),
            'cider': cider_score
        }
