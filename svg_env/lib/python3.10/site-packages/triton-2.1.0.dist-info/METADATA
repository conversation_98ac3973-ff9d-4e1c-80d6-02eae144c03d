Metadata-Version: 2.1
Name: triton
Version: 2.1.0
Summary: A language and compiler for custom Deep Learning operations
Home-page: https://github.com/openai/triton/
Author: <PERSON>
Author-email: <EMAIL>
Keywords: Compiler,Deep Learning
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Build Tools
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Dist: filelock
Provides-Extra: build
Requires-Dist: cmake >=3.18 ; extra == 'build'
Requires-Dist: lit ; extra == 'build'
Provides-Extra: tests
Requires-Dist: autopep8 ; extra == 'tests'
Requires-Dist: flake8 ; extra == 'tests'
Requires-Dist: isort ; extra == 'tests'
Requires-Dist: numpy ; extra == 'tests'
Requires-Dist: pytest ; extra == 'tests'
Requires-Dist: scipy >=1.7.1 ; extra == 'tests'
Provides-Extra: tutorials
Requires-Dist: matplotlib ; extra == 'tutorials'
Requires-Dist: pandas ; extra == 'tutorials'
Requires-Dist: tabulate ; extra == 'tutorials'

