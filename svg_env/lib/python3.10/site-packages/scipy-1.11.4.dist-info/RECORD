scipy-1.11.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.11.4.dist-info/LICENSE.txt,sha256=XUYZyeAkKF8a0K_ehjnpPLc_NJJ7OqvquFxrR45Upnk,46805
scipy-1.11.4.dist-info/METADATA,sha256=_fNlAehIwH8k8fvOKE4Zfnf5ObO32w4L6Fa1P9Yp8AA,60390
scipy-1.11.4.dist-info/RECORD,,
scipy-1.11.4.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.11.4.dist-info/WHEEL,sha256=sZM_NeUMz2G4fDenMf11eikcCxcLaQWiYRmjwQBavQs,137
scipy.libs/libgfortran-040039e1.so.5.0.0,sha256=FK-zEpsai1C8QKOwggx_EVLqm8EBIaqxUpQ_cFdHKIY,2686065
scipy.libs/libopenblasp-r0-23e5df77.3.21.dev.so,sha256=s3Bv8FQTdpmXEgI2tw4NB35Joe2zRTrfIvVp8Fz0qw8,32957169
scipy.libs/libquadmath-96973f99.so.0.0.0,sha256=k0wi3tDn0WnE1GeIdslgUa3z2UVF2pYvYLQWWbB12js,247609
scipy/__config__.py,sha256=8g1xTSWkSoGpCr3JPdh2WCQkwv7Kzb9nH6R3NwLzmuw,4801
scipy/__init__.py,sha256=XfMQa85oIzzsQ-hCCBNdjxj7bgfSdw-8FX_OMrqyjFA,6530
scipy/__pycache__/__config__.cpython-310.pyc,,
scipy/__pycache__/__init__.cpython-310.pyc,,
scipy/__pycache__/_distributor_init.cpython-310.pyc,,
scipy/__pycache__/conftest.cpython-310.pyc,,
scipy/__pycache__/version.cpython-310.pyc,,
scipy/_distributor_init.py,sha256=2LDC4c2QoxdDkay0RO61CkHdMYLo-TdsihTtkbjt7XA,331
scipy/_lib/__init__.py,sha256=CXrH_YBpZ-HImHHrqXIhQt_vevp4P5NXClp7hnFMVLM,353
scipy/_lib/__pycache__/__init__.cpython-310.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-310.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-310.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-310.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-310.pyc,,
scipy/_lib/__pycache__/_finite_differences.cpython-310.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-310.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-310.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-310.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-310.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-310.pyc,,
scipy/_lib/__pycache__/_util.cpython-310.pyc,,
scipy/_lib/__pycache__/decorator.cpython-310.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-310.pyc,,
scipy/_lib/__pycache__/doccer.cpython-310.pyc,,
scipy/_lib/__pycache__/uarray.cpython-310.pyc,,
scipy/_lib/_bunch.py,sha256=OE9I71OcQ98tFLe6EB1PdS2tXhuyuO0O8kaGOduH4YQ,8116
scipy/_lib/_ccallback.py,sha256=AV1yDxWBab2s0PmyadA9eMqTI3MH8SDDQWGRM7Rpm30,6963
scipy/_lib/_ccallback_c.cpython-310-x86_64-linux-gnu.so,sha256=mxvuST98Mzn3G5cE15Dr37URCykYUO9J8EB3cnNHuPw,86744
scipy/_lib/_disjoint_set.py,sha256=o_EUHZwnnI1m8nitEf8bSkF7TWZ65RSiklBN4daFruA,6160
scipy/_lib/_docscrape.py,sha256=Jfe_8d7gd5RBWRLaRxNfq09RXyaX2bGostPFIwvIj-A,21547
scipy/_lib/_finite_differences.py,sha256=llaIPvCOxpE4VA8O8EycPEU8i6LHJyOD-y7Y9OvQHt0,4172
scipy/_lib/_fpumode.cpython-310-x86_64-linux-gnu.so,sha256=Kk1mpVY1lns4OpLjvNrW4B9W-nLAOgt6nH-0O5oSRTg,16400
scipy/_lib/_gcutils.py,sha256=hajQd-HUw9ckK7QeBaqXVRpmnxPgyXO3QqqniEh7tRk,2669
scipy/_lib/_pep440.py,sha256=vo3nxbfjtMfGq1ektYzHIzRbj8W-NHOMp5WBRjPlDTg,14005
scipy/_lib/_test_ccallback.cpython-310-x86_64-linux-gnu.so,sha256=yGXELz3LHq-9jmN9DzA6APmFeL4wvY_rPypIid98qsg,23232
scipy/_lib/_test_deprecation_call.cpython-310-x86_64-linux-gnu.so,sha256=O_G5euEwhA15dnqHQb2vzTptWPC76IA_YuWvfK8MFYw,29464
scipy/_lib/_test_deprecation_def.cpython-310-x86_64-linux-gnu.so,sha256=r68pib5M5Wr_7DUvLWyd1Ul4wOgNn2yE2jDuUEumHBY,30696
scipy/_lib/_testutils.py,sha256=cKAbwgv3ffVqmK9P1UtsoTgxyWmOuXZqF35n31WiRV4,7847
scipy/_lib/_threadsafety.py,sha256=xuVqUS2jv46fOOQf7bcrhiYtnPVygqmrIVJc-7_LlI8,1455
scipy/_lib/_tmpdirs.py,sha256=z3IYpzACnWdN_BMjOvqYbkTvYyUbfbQvfehq7idENSo,2374
scipy/_lib/_uarray/LICENSE,sha256=yAw5tfzga6SJfhTgsKiLVEWDNNlR6xNhQC_60s-4Y7Q,1514
scipy/_lib/_uarray/__init__.py,sha256=JLZP3pTSOy4i3Usw4odj4P9dtImMNFrxT4_A9dcgzQU,4493
scipy/_lib/_uarray/__pycache__/__init__.cpython-310.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-310.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=9O0gKhdsTPp64eOw7nXH9wPrD-f6IzIIgE7m-tVqefA,20476
scipy/_lib/_uarray/_uarray.cpython-310-x86_64-linux-gnu.so,sha256=YiS4tN5cfie9g1WkGv6Lp_mIiz_ZLbf3qJf6Jyxlpgs,173872
scipy/_lib/_util.py,sha256=nybcx28YG4DsRK43WHtOmVYj0vwCBygWZeUtxydWaqY,25219
scipy/_lib/decorator.py,sha256=0A8CJu041BKVa8XSXRTVdYYlsekW6iGZIlyxr-WnVuM,15045
scipy/_lib/deprecation.py,sha256=gOpiVXL_1AvV5B2Z9rWROHL3SQu9V4PTXTYvgIFwSwE,3148
scipy/_lib/doccer.py,sha256=shdWIi3u7QBN5CyyKwqWW99qOEsiFewB8eH10FWhYLM,8362
scipy/_lib/messagestream.cpython-310-x86_64-linux-gnu.so,sha256=I-pP3QnSDjhqDCTRwCYBX-nmLUlY9AHESO1ok9V2hTs,61656
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-310.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-310.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=qvfxvemSmGvaqcpHwoEzdXYn5mrAf-B1X5qGGyasPC4,3416
scipy/_lib/tests/test__pep440.py,sha256=u9hPoolK4AoIIS-Rq74Du5SJu5og2RxMwgaAvGgWvRo,2277
scipy/_lib/tests/test__testutils.py,sha256=P4WDJpUgy19wD9tknQSjIivuQvZF7YUBGSBWlur2QRA,800
scipy/_lib/tests/test__threadsafety.py,sha256=qSfCF5OG_5lbnSl-grmDN_QCU4QLe-fS3sqnwL04pf8,1322
scipy/_lib/tests/test__util.py,sha256=e5Z4scq5YRV37-apeZwDaxPhhv156GGzFzwqZrjnyRc,13835
scipy/_lib/tests/test_bunch.py,sha256=sViE5aFSmAccfk8kYvt6EmzR5hyQ9nOSWMcftaDYDBg,6168
scipy/_lib/tests/test_ccallback.py,sha256=koqXiuyErPn3D3c5rkcZaUDCVMbHIYORR7xpOs26wag,6033
scipy/_lib/tests/test_deprecation.py,sha256=a_3r_9pFx1sxJXeFgiTSV9DXYnktc4fio1hR0ITPywA,364
scipy/_lib/tests/test_import_cycles.py,sha256=lsGEBuEMo4sbYdZNSOsxAQIJgquUIjcDhQjtr0cyFg4,500
scipy/_lib/tests/test_public_api.py,sha256=KxDHgq-CfUo1JLjL7O-opOjTMj_oayKl-0ZOzOx7Jfc,9522
scipy/_lib/tests/test_scipy_version.py,sha256=jgo-2YhCkBksXHM6xKiN_iJJZkqz0CvXqn2jVxx1djA,606
scipy/_lib/tests/test_tmpdirs.py,sha256=URQRnE_lTPw9MIJYBKXMfNATQ0mpsBDgoqAowkylbWQ,1240
scipy/_lib/tests/test_warnings.py,sha256=0H4rhAspo56LgfIIj8k1SpmiQKKdmgXLjUzLBZbSgSA,4275
scipy/_lib/uarray.py,sha256=wmH9RAWa-jXxiokMHx-nv0dazCR0UoPlitauJCWspQs,773
scipy/cluster/__init__.py,sha256=LNM_kFbT28cIYYgctilxYsxdjuF3KuiOaulZH4dFatE,876
scipy/cluster/__pycache__/__init__.cpython-310.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-310.pyc,,
scipy/cluster/__pycache__/vq.cpython-310.pyc,,
scipy/cluster/_hierarchy.cpython-310-x86_64-linux-gnu.so,sha256=l5wmB_GQ0QZDCMoyUJh-uAHYAj27QVnBT2QsphV94ns,406704
scipy/cluster/_optimal_leaf_ordering.cpython-310-x86_64-linux-gnu.so,sha256=sqdOFv7UQmZ_U9s9KrHwfYNxgWK24dsc64ZmnOeahy8,343120
scipy/cluster/_vq.cpython-310-x86_64-linux-gnu.so,sha256=3E6th9wKttwri1ORFei375WREIE5fp92VRm_nsWuJJQ,112808
scipy/cluster/hierarchy.py,sha256=_CMGtjfpkCIhg8WQEkkTZevoCcspY9ng-Cn1zjtsvcE,148427
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-310.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-310.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-310.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-310.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=7syUYdIaDVr7hgvMliX0CW4386utjBJn1DOgX0USXls,6850
scipy/cluster/tests/test_disjoint_set.py,sha256=EuHGBE3ZVEMnWFbCn8tjI-_6CWrNXfpnv5bUBa9qhWI,5525
scipy/cluster/tests/test_hierarchy.py,sha256=9JKt3Y10Kw4TGCreJUUh0ZpprpgLDKtbRSgcqgHoY68,43563
scipy/cluster/tests/test_vq.py,sha256=Z1MVXmkUIrg83Ans-uZ0R9B1DUFIOdQb2DSXr1sRNIE,13716
scipy/cluster/vq.py,sha256=xIvgAQ1O9f7kYFsVB2LASUXt92AtpYuEQcqgODA_6Tg,29256
scipy/conftest.py,sha256=eQdTX_PmDQf7_K-UGS0yP0xPzmBQTZgZwVhqQqLqgh4,3471
scipy/constants/__init__.py,sha256=2clP2hJ6dxjT-TFPt8EdtULcaI3h1XkjOZQLXf53ORY,12423
scipy/constants/__pycache__/__init__.cpython-310.pyc,,
scipy/constants/__pycache__/_codata.cpython-310.pyc,,
scipy/constants/__pycache__/_constants.cpython-310.pyc,,
scipy/constants/__pycache__/codata.cpython-310.pyc,,
scipy/constants/__pycache__/constants.cpython-310.pyc,,
scipy/constants/_codata.py,sha256=bqPhnH7EE3XXzfhzGcxmTGNFmIyPGQer_-tO7bnPzyA,155622
scipy/constants/_constants.py,sha256=kITfo169rtJQ6q3V-nED-OPauC-NsAs4vSLe0pSgkY4,10369
scipy/constants/codata.py,sha256=F87N9rObCx8B3y_wcoPEzFWGhZmdXJ6B0Nll7IUEfv8,1015
scipy/constants/constants.py,sha256=k8IODtGkknZ44clDFEihVparvjJFwEDG454V2of4BpQ,2477
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-310.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-310.pyc,,
scipy/constants/tests/test_codata.py,sha256=ToO_lhQOsusJlP3QjrYqa1vw7x6wTCuKH17fg87tH08,1959
scipy/constants/tests/test_constants.py,sha256=PY1oy6bbM2zoPAPgUeBqVThnVRuu4lBt_uMmxm7Ct38,1632
scipy/datasets/__init__.py,sha256=lO6WMYM5CbayWGLjzgcJdZoxQHUYijYbfzyHxo9Bbt0,2816
scipy/datasets/__pycache__/__init__.cpython-310.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-310.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-310.pyc,,
scipy/datasets/__pycache__/_registry.cpython-310.pyc,,
scipy/datasets/__pycache__/_utils.cpython-310.pyc,,
scipy/datasets/_download_all.py,sha256=iRPR2IUk6C3B5u2q77yOhac449MRSoRaTlCy2oCIknE,1701
scipy/datasets/_fetchers.py,sha256=Ef8RxSZkB0KIjmF-wFoW_QX8wbXHAgOzSAp1zFgE2QU,6759
scipy/datasets/_registry.py,sha256=br0KfyalEbh5yrQLznQ_QvBtmN4rMsm0UxOjnsJp4OQ,1072
scipy/datasets/_utils.py,sha256=kdZ-Opp7Dr1pCwM285p3GVjgZTx_mKWCvETur92FWg4,2967
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-310.pyc,,
scipy/datasets/tests/test_data.py,sha256=GelFTF2yZqiiQkgTv8ukv8sKTJBdmpsyK5fr0G6z7Ls,4064
scipy/fft/__init__.py,sha256=aihIkaW0Nr76Ct84OInhv-8AjbV8Z9ah44KiDEYEFSM,3567
scipy/fft/__pycache__/__init__.cpython-310.pyc,,
scipy/fft/__pycache__/_backend.cpython-310.pyc,,
scipy/fft/__pycache__/_basic.cpython-310.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-310.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-310.pyc,,
scipy/fft/__pycache__/_fftlog_multimethods.cpython-310.pyc,,
scipy/fft/__pycache__/_helper.cpython-310.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-310.pyc,,
scipy/fft/_backend.py,sha256=kK1vHXpTDR7uYseUBJosEm7CXipbFdwBccuIyt59YVQ,6388
scipy/fft/_basic.py,sha256=KXnf-LBgrD0XToSowdJ64Uvg5f0jyoinJV3-UwuklqA,62991
scipy/fft/_debug_backends.py,sha256=RlvyunZNqaDDsI3-I6QH6GSBz_faT6EN4OONWsvMtR8,598
scipy/fft/_fftlog.py,sha256=WVibBtg6iin0R-hBahHS-VnUxTlNZj3KTyL510D2MmE,11879
scipy/fft/_fftlog_multimethods.py,sha256=wFwqCnjY_DH6_XVm6cQ4pIlu0LpCp76c5GeXWvOvTH0,575
scipy/fft/_helper.py,sha256=Ik7ZC5HQzQZmixdY0XzRryCD6NB8BkYxujH37e0awI4,3414
scipy/fft/_pocketfft/LICENSE.md,sha256=wlSytf0wrjyJ02ugYXMFY7l2D8oE8bdGobLDFX2ix4k,1498
scipy/fft/_pocketfft/__init__.py,sha256=dROVDi9kRvkbSdynd3L09tp9_exzQ4QqG3xnNx78JeU,207
scipy/fft/_pocketfft/__pycache__/__init__.cpython-310.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-310.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-310.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-310.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=_0X3fEbYIpvobCbcYkjLKcSlA9TDi3X5kswl2e4cS58,9841
scipy/fft/_pocketfft/helper.py,sha256=eO8JIknSyuY0fw1naYKcJDHXbPllO8yyiw7Sw6LGYVY,5721
scipy/fft/_pocketfft/pypocketfft.cpython-310-x86_64-linux-gnu.so,sha256=mBIsH21Z3mNTbtzboLZZT8Y7SZ3Wif3pyEIuWsnjzps,1193752
scipy/fft/_pocketfft/realtransforms.py,sha256=69Ro8fMQ84luUfZ9Ms5faXMjI2JZ0-2uWnYH2thF11w,3378
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-310.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-310.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=VXdZ96DEdf8pJSl3WItg6z0mIF9aZPY5guVqbp7JToo,35617
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=VY5VOmdnZmCJ5TgBRLxLyKBdUTbWWZolFUvAWQe5St8,16438
scipy/fft/_realtransforms.py,sha256=y4PJZkRhuwnJTy4-J2U7SP-Soj69dtnYXSK073Ur06Y,25280
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-310.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-310.pyc,,
scipy/fft/tests/__pycache__/test_fft_function.cpython-310.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-310.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-310.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-310.pyc,,
scipy/fft/tests/__pycache__/test_numpy.cpython-310.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-310.pyc,,
scipy/fft/tests/mock_backend.py,sha256=00ZsBjrauFbGgMKB9-vh-CBvJPLsRFPVDp015PbiWjk,1769
scipy/fft/tests/test_backend.py,sha256=29ZzhDK9ySCXfqgazIgBfMtp1fUpQXl0xTS0IE-ccoc,4256
scipy/fft/tests/test_fft_function.py,sha256=ZVK0wunPrwE-LkgQOxp3B4sgqcD6aLmyWcpytKvDBWE,1048
scipy/fft/tests/test_fftlog.py,sha256=gcPRfbarV_rijIIbcU_oQuY2Y1J7s6CIShZqK8rxvQk,5819
scipy/fft/tests/test_helper.py,sha256=AxbqHohk5khZlno5goDzkPm6eqvKbtGxjf3sclsD01I,9795
scipy/fft/tests/test_multithreading.py,sha256=Ub0qD3_iSApPT9E71i0dvKnsKrctLiwMq95y3370POE,2132
scipy/fft/tests/test_numpy.py,sha256=kI1Y5jjZdLXHhklOFHDtDN2FGq4xKmbl5e-lceK5Zhw,14432
scipy/fft/tests/test_real_transforms.py,sha256=h-1onZkj2tcP4pn5Cd44zlK7w9-RjHWq-12hHlK2EfY,7591
scipy/fftpack/__init__.py,sha256=rLCBFC5Dx5ij_wmL7ChiGmScYlgu0mhaWtrJaz_rBt0,3155
scipy/fftpack/__pycache__/__init__.cpython-310.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-310.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-310.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-310.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-310.pyc,,
scipy/fftpack/__pycache__/basic.cpython-310.pyc,,
scipy/fftpack/__pycache__/helper.cpython-310.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-310.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-310.pyc,,
scipy/fftpack/_basic.py,sha256=Sk_gfswmWKb3za6wrU_mIrRVBl69qjzAu9ltznbDCKs,13098
scipy/fftpack/_helper.py,sha256=6oIZ6ErA0Bt61s460_WjQfwmpENR0NnjNmPlO3ImhXo,3354
scipy/fftpack/_pseudo_diffs.py,sha256=eCln0ZImNYr-wUWpOZ-SmKKIbhJsV8VBLmwT_C79RsQ,14200
scipy/fftpack/_realtransforms.py,sha256=ledb21L13ofGnOU4pkx8uWuARCxsh3IFQrHctxTgzzw,19214
scipy/fftpack/basic.py,sha256=DMX__JJaJK_FEPw5LhxVaiwqM8ive616PGZ1uzXBLNM,790
scipy/fftpack/convolve.cpython-310-x86_64-linux-gnu.so,sha256=uu_oGI_EzwCButut6x_1i7dcnv4av7TKv5LpMutkgX0,254120
scipy/fftpack/helper.py,sha256=RWzRMKNW8K5M2jHGRwWB7CtvYVEoWdP63LISGcGgMaI,795
scipy/fftpack/pseudo_diffs.py,sha256=gWafKeFKkbnvaxQAtgj7Vzj_q60xwLR3ghZn3ttO3wU,901
scipy/fftpack/realtransforms.py,sha256=79A6XfPab3kR0KN4XfkDrTzTZH41LQmW4AcMYYTnpyY,826
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-310.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-310.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-310.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-310.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-310.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=S48c4kKUC9-wIiyomEFSql21RImVGXklVSl-meUeGY4,30284
scipy/fftpack/tests/test_helper.py,sha256=8JaPSJOwsk5XXOf1zFahJ_ktUTfNGSk2-k3R6e420XI,1675
scipy/fftpack/tests/test_import.py,sha256=Sz4ZZmQpz_BtiO0Gbtctt6WB398wB17oopv5mkfOh0U,1120
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=SEVPHPDdSxDSUCC8qkwuKD7mIX8rFIx9puxGzBYd1uk,13389
scipy/fftpack/tests/test_real_transforms.py,sha256=OFPbGCP0kWtzyfK9nAD3olvuNsDaJTIdK5-KnQQlEro,23953
scipy/integrate/__init__.py,sha256=Efamd65mabAHO84TBm_w_bTH0GUqS-Me94E8DmjDPak,4074
scipy/integrate/__pycache__/__init__.cpython-310.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-310.pyc,,
scipy/integrate/__pycache__/_ode.cpython-310.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-310.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-310.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-310.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-310.pyc,,
scipy/integrate/__pycache__/dop.cpython-310.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-310.pyc,,
scipy/integrate/__pycache__/odepack.cpython-310.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-310.pyc,,
scipy/integrate/__pycache__/vode.cpython-310.pyc,,
scipy/integrate/_bvp.py,sha256=0HToAY1uw5WJ4vk5oELg3obr6c_k4gcD_dbmFz0Sakw,41067
scipy/integrate/_dop.cpython-310-x86_64-linux-gnu.so,sha256=AEEemt0zDJXtVSMzESGOBoaDLxSZRzNoJ2DYwEOw0IE,112857
scipy/integrate/_ivp/__init__.py,sha256=gKFR_pPjr8fRLgAGY5sOzYKGUFu2nGX8x1RrXT-GZZc,256
scipy/integrate/_ivp/__pycache__/__init__.cpython-310.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-310.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-310.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-310.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-310.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-310.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-310.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-310.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-310.pyc,,
scipy/integrate/_ivp/base.py,sha256=Mlef_dgmn0wzjFxZA3oBbtHrQgrfdZw_8k1mLYNZP4A,10295
scipy/integrate/_ivp/bdf.py,sha256=deQVxWq58ihFDWKC8teztUbe8MYN4mNgLCU-6aq_z1U,17522
scipy/integrate/_ivp/common.py,sha256=sFkd7FtIZbIqJQqvqWogChFxVLKZoJE5AWtWWLqewY0,14772
scipy/integrate/_ivp/dop853_coefficients.py,sha256=OrYvW0Hu6X7sOh37FU58gNkgC77KVpYclewv_ARGMAE,7237
scipy/integrate/_ivp/ivp.py,sha256=fWqn5B3XAIrEItQxgikpXWijadRjbt4Jv_t9d4HF2w4,28723
scipy/integrate/_ivp/lsoda.py,sha256=oyU-Ln4g50MDxRiN6U7pH3j7-1GiMbqJ20O5mqo4V3c,8599
scipy/integrate/_ivp/radau.py,sha256=7Ng-wYOdOBf4ke4-CYyNUQUH3jgYmDflpE1UXIYNOdU,19743
scipy/integrate/_ivp/rk.py,sha256=fD9qGAENx-F2EeaKkD0k-qCrdVSE0XL76mCsK5JVFaA,22766
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-310.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-310.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=pdBhTTFkOqaZGFAKIwP8hqzrqqmqOaSpyHklUsmxaQE,35070
scipy/integrate/_ivp/tests/test_rk.py,sha256=K9UxZghBzSL2BzmgLndPJcWOWV4Nr530TGKWakpsoeM,1326
scipy/integrate/_lsoda.cpython-310-x86_64-linux-gnu.so,sha256=FrqbCjToMkTRfmi5ukQrNXonD6i73a37qIDkYW9e6XI,109009
scipy/integrate/_ode.py,sha256=A0abSbXUJiwljB2fPfYz-kJ7fR1wPA3NSONW5pgdbxI,47921
scipy/integrate/_odepack.cpython-310-x86_64-linux-gnu.so,sha256=WbixrtVUmSQhC4gGrgabqMS-RotC1as5wLW_QJ2NajE,83537
scipy/integrate/_odepack_py.py,sha256=aTlpXCI0qvXG0Z8ibHqiWyHTo2Q16RN8MyolfiMIVB8,10769
scipy/integrate/_quad_vec.py,sha256=V1W21cWdgYgqm9OtkApx1wpjFTjEAjSIBb6WDknyz5Y,21166
scipy/integrate/_quadpack.cpython-310-x86_64-linux-gnu.so,sha256=dqrRMswzH9-_q3N3A7t9lZCztE6-KGmLK7BQZ-s9G1Q,116409
scipy/integrate/_quadpack_py.py,sha256=5r_XPoOH_d9_hpEwsimPtz4y7KjvOQD9US3dpWzriQM,52774
scipy/integrate/_quadrature.py,sha256=oIR2rKUul-g-DCcc_U4QaWXQTreXPZizzbOS06_afnc,51857
scipy/integrate/_test_multivariate.cpython-310-x86_64-linux-gnu.so,sha256=oCO9DKyKPy4ERYj4rP5sVzsJ2V1Goc521tLC5k-WlzE,16896
scipy/integrate/_test_odeint_banded.cpython-310-x86_64-linux-gnu.so,sha256=FofLXT-Yq_UgY_75QHIkQzUp6ni_dr-OHpk7juigciE,104625
scipy/integrate/_vode.cpython-310-x86_64-linux-gnu.so,sha256=oS1jjLSloU54M4oTn5joAOly1D2mkntyOAHqVgkRLSQ,166369
scipy/integrate/dop.py,sha256=yx0rG-U_s77y6_cRKkuIo27IFepKhow6VnXQmYHq6vk,622
scipy/integrate/lsoda.py,sha256=I4nTMQz101vjwrDVjO1eR7mZjwP7CJW1P5aA_Qo3394,610
scipy/integrate/odepack.py,sha256=bGHp-nnd-dVQHYxy_PogCiY8CODz6pok9adiUtgq7zI,771
scipy/integrate/quadpack.py,sha256=fy0Vz51sZkG5Cjdp_EXGEDfHlFGjLdOqz8EtnXdMwSY,845
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-310.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-310.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-310.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-310.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-310.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-310.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-310.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=PP1UrkSIt4YpKXw7v2uTFAXLSRbey-DVhicg9XEByvg,6286
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=kJWirYckJ7k4tfweg1ds-Tozp3GEhxTbuXfgSdeJw7k,6687
scipy/integrate/tests/test_bvp.py,sha256=Q3zw4r3lajNE9y2smIkAayRWrZ67r-yTuXODPeyvecY,20181
scipy/integrate/tests/test_integrate.py,sha256=JBgefIuE1f_dWtr3t1RxBLqozvqKhlcx9t4u-V7to90,24403
scipy/integrate/tests/test_odeint_jac.py,sha256=enXGyQQ4m-9kMPDaWvipIt3buYZ5jNjaxITP8GoS86s,1816
scipy/integrate/tests/test_quadpack.py,sha256=nsz064gPV_EURspeHz7lys8CO1tB4kpd7hiO-Y5OjC0,27976
scipy/integrate/tests/test_quadrature.py,sha256=ZwR5DR0aINThXiF4y4_jdC0i-h0RpH9aADz1Ja_tBAk,17674
scipy/integrate/vode.py,sha256=xv-9AX3Yh1T0w-YoIPRrpQwavFTnoak81AsWiH_HsGA,625
scipy/interpolate/__init__.py,sha256=zdsw-3YouJ38EFdizaPWGjsaq_G0nWRd2i5vVvHqNZs,3483
scipy/interpolate/__pycache__/__init__.cpython-310.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-310.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-310.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-310.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-310.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-310.pyc,,
scipy/interpolate/__pycache__/_interpnd_info.cpython-310.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-310.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-310.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-310.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-310.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-310.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-310.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-310.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-310.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-310.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-310.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-310.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-310.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-310.pyc,,
scipy/interpolate/_bspl.cpython-310-x86_64-linux-gnu.so,sha256=QBFgnpW-ehdVqHoNiFO51Wzbycy043TqEubHRXthze0,346648
scipy/interpolate/_bsplines.py,sha256=PIUFA941jGMF6JtLaIuUV_dmzhWFl90xjp9HGVgLHbk,69392
scipy/interpolate/_cubic.py,sha256=fFxiKYUSKYvhPx3y2ARsvYTZ10-4sO-Qu-woDwlAb9A,33920
scipy/interpolate/_fitpack.cpython-310-x86_64-linux-gnu.so,sha256=Scgc1LVNxuiZzoN9I4CU8HkkxGFjumB9dlzg96a6JMA,95465
scipy/interpolate/_fitpack2.py,sha256=OVhL7rKMg43WoRLgzoFsPTWlL_TguZ6008RTJCNQNt8,89209
scipy/interpolate/_fitpack_impl.py,sha256=5ldLkXeufGthYMsp254JTfsQZQ423JnnEiceGr7u3Pw,28110
scipy/interpolate/_fitpack_py.py,sha256=eXr9GcA1whGdQD4VSELTXw1LJDhWCusL0OUo2HUAJew,27528
scipy/interpolate/_interpnd_info.py,sha256=B0E0S3ozMrYkGSJ_XTX_Qj6U9vle0U59i8dlqpTCd4g,869
scipy/interpolate/_interpolate.py,sha256=zhyzB-HZRoSBEJplF9MNiiAOK33OEn_shS1EQwzylpY,87978
scipy/interpolate/_ndgriddata.py,sha256=6RoNvCJyDyWq1r358eCsudZAkVkp5HrnKHK1Explbag,9686
scipy/interpolate/_pade.py,sha256=OBorKWc3vCSGlsWrajoF1_7WeNd9QtdbX0wOHLdRI2A,1827
scipy/interpolate/_polyint.py,sha256=JhVMcu_Vbqn1m-EXmdxMK7MTRuVTV3t14sQX2-5npHo,26463
scipy/interpolate/_ppoly.cpython-310-x86_64-linux-gnu.so,sha256=m1SozY4XZFFhuB6OilL94cjiLWHI71kZj2sPGFuwO4I,400104
scipy/interpolate/_rbf.py,sha256=Y3FKCuhDlLa4IQZXPf6eBlfbEAIJ-R3J575uOeokaxQ,11672
scipy/interpolate/_rbfinterp.py,sha256=4gE3VS6TiIL4BJnkaoTUzwejA5qfxlvcLFtxEXbP4fk,19587
scipy/interpolate/_rbfinterp_pythran.cpython-310-x86_64-linux-gnu.so,sha256=LDS9zAlM6sslTEe6ftvxb3evf1aQAQ9yu1bmVrarudc,261272
scipy/interpolate/_rgi.py,sha256=84pUIXWLrVJqGim5JotMWW9JNAoXO-fHhEeQEyU4wEQ,26908
scipy/interpolate/_rgi_cython.cpython-310-x86_64-linux-gnu.so,sha256=1e2tjT9aN5Fb_mqQL-0CJOUixXjbxWpGdrdYOc5N1yQ,273384
scipy/interpolate/dfitpack.cpython-310-x86_64-linux-gnu.so,sha256=Wz_qVnP_DDZ6Ufa7PEIkIG-aaw950v4z2FUxu8l2lV8,362561
scipy/interpolate/fitpack.py,sha256=w__c8vjFPORQpgpmWIi6MN_PpsJGBeDPYxoxpkUOdRQ,948
scipy/interpolate/fitpack2.py,sha256=ivHIjzk8VqI33aDkUo58-pl5eOmDrhInm8CMhgd0lJs,1195
scipy/interpolate/interpnd.cpython-310-x86_64-linux-gnu.so,sha256=e4DxalJ_G-mJt5-c0sVf6A0xYNXVfjVyG2v0GGgqciI,409424
scipy/interpolate/interpolate.py,sha256=iz2Yifiy51N7r3tgsAdiSt1swa7C4kQOkbZWPBp_9GM,1180
scipy/interpolate/ndgriddata.py,sha256=AXW0AnerFGis7MyHWVvYBrnde7g5rBg3FeYV_NY-Xb0,912
scipy/interpolate/polyint.py,sha256=24_OrluWJYXC0hIuLf6O7h3B0Te364bTPhqKXsV5N3M,941
scipy/interpolate/rbf.py,sha256=X_dHEfyyCI_XSRmK1d1vnkkWwPlbx7kSHhORv9WByPk,818
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-310.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-310.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bsplines.py,sha256=PqPgJqnLT8-CJRsWfrA28kWLPnw0yLNcbA1C4lFAYB4,60548
scipy/interpolate/tests/test_fitpack.py,sha256=gSsY5rKPT3b71E-WqCyxiVVtG5d95FFXYIM5PuHTadM,16013
scipy/interpolate/tests/test_fitpack2.py,sha256=hiMu1aZfgH84pdYpvHhuEqIFLF0FLKoU2OXkpupcvm0,58667
scipy/interpolate/tests/test_gil.py,sha256=wt92CaxUlVgRGB-Wl2EuQxveqdARU8rZucD9IKl-pUE,1874
scipy/interpolate/tests/test_interpnd.py,sha256=RVc-0onUWODNoc_S3_21pAGDF5U0COyEXZqfLhGwPEw,13627
scipy/interpolate/tests/test_interpolate.py,sha256=3zCQTF87Gud83-DBnp3OUlgMBlCQSlhyvnJLIFkkfj0,95755
scipy/interpolate/tests/test_ndgriddata.py,sha256=lBMoAFWMMZcKLkZlvT2g4laNH3KsWIGpB3TQOaOvMjE,9445
scipy/interpolate/tests/test_pade.py,sha256=x5VyACjEgqIsz5e5vIOoCaIVb-ToZsFw6baxLQjRFZQ,3786
scipy/interpolate/tests/test_polyint.py,sha256=8HuqeZIBzmHxmcNUwMJdFYZn73GsLHsLnOKDDTYqSzU,30292
scipy/interpolate/tests/test_rbf.py,sha256=OitMk6wEbVeRS_TUeSa-ReWqR7apVez2n-wYOI08grg,6559
scipy/interpolate/tests/test_rbfinterp.py,sha256=5gnAZ2c8OFiT5AV_B160XzXNmVwNoMhoGX_iDt0OQLQ,18127
scipy/interpolate/tests/test_rgi.py,sha256=Wy9i-LvZUTmwkserKocXWvGAQPWDjIawpPDOc5jOqPI,41718
scipy/io/__init__.py,sha256=Mix0F-unwivh_VUKB-5Hwg9vmNM9MzGkTciRb86aEmY,2721
scipy/io/__pycache__/__init__.cpython-310.pyc,,
scipy/io/__pycache__/_fortran.cpython-310.pyc,,
scipy/io/__pycache__/_idl.cpython-310.pyc,,
scipy/io/__pycache__/_mmio.cpython-310.pyc,,
scipy/io/__pycache__/_netcdf.cpython-310.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-310.pyc,,
scipy/io/__pycache__/idl.cpython-310.pyc,,
scipy/io/__pycache__/mmio.cpython-310.pyc,,
scipy/io/__pycache__/netcdf.cpython-310.pyc,,
scipy/io/__pycache__/wavfile.cpython-310.pyc,,
scipy/io/_fortran.py,sha256=W0bSPzflN0JMpchslmYw2d1Zeqc_EOVuh7z3NbqxT-I,10891
scipy/io/_harwell_boeing/__init__.py,sha256=2iVxlj6ZquU8_XPA37npOdeHCXe8XbQrmMZO7k6Bzxs,574
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-310.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-310.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-310.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=h0UgeL5ZIM4v53MmMoZw0R0OHoZTyEyeDgI9aUmzWRs,8916
scipy/io/_harwell_boeing/hb.py,sha256=S6r5ObkQ3sOsfQZEOl0TbYB2VFoHEmIHK3c9AOYuMYY,19148
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-310.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-310.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=0LxOjUewBj1Fwf7EOxMWZG_PdzMbVrFYMUeGgs23VII,2360
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=3eLwxTSg_Ebt2pjBLvZhpq8WUMjkFhM1lsTu_mgvDTI,2284
scipy/io/_idl.py,sha256=osW6UcbiYMCEO7lpXbSEd_6glfbOiigUs0swyy5Klc0,26924
scipy/io/_mmio.py,sha256=c5phTQeYRCB1QnI2qTmLjnuwuBtnep9JWN-7yjCbrlw,31900
scipy/io/_netcdf.py,sha256=4j56RRusPvC3TAx4gKj927ab3LqWmCLcRk0aAWX3LxM,39085
scipy/io/_test_fortran.cpython-310-x86_64-linux-gnu.so,sha256=OEFZXOiGSLyEdvz4fIy0cYF-fK82MrILCU6vHYgjGAg,55225
scipy/io/arff/__init__.py,sha256=czaV8hvY6JnmEn2qyU3_fzcy_P55aXVT09OzGnhJT9I,805
scipy/io/arff/__pycache__/__init__.cpython-310.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-310.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-310.pyc,,
scipy/io/arff/_arffread.py,sha256=zyTvdmCe04fWyvFOZNfWvja6EerVthvupbAtNGm-ews,26359
scipy/io/arff/arffread.py,sha256=2_W-Wt0drknNg734xtup-U1AeuqGMYKQUzCE3I3CW0k,1364
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-310.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=fTS6VWSX6dwoM16mYoo30dvLoJChriDcLenHAy0ZkVM,7486
scipy/io/arff/tests/data/missing.arff,sha256=ga__Te95i1Yf-yu2kmYDBVTz0xpSTemz7jS74_OfI4I,120
scipy/io/arff/tests/data/nodata.arff,sha256=DBXdnIe28vrbf4C-ar7ZgeFIa0kGD4pDBJ4YP-z4QHQ,229
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=01mPSc-_OpcjXFy3EoIzKdHCmzWSag4oK1Ek2tUc6_U,286
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=bcMOl-E0I5uTT27E7bDTbW2mYOp9jS8Yrj0NfFjQdKU,292
scipy/io/arff/tests/data/test1.arff,sha256=nUFDXUbV3sIkur55rL4qvvBdqUTbzSRrTiIPwmtmG8I,191
scipy/io/arff/tests/data/test10.arff,sha256=va7cXiWX_AnHf-_yz25ychD8hOgf7-sEMJITGwQla30,199009
scipy/io/arff/tests/data/test11.arff,sha256=G-cbOUUxuc3859vVkRDNjcLRSnUu8-T-Y8n0dSpvweo,241
scipy/io/arff/tests/data/test2.arff,sha256=COGWCYV9peOGLqlYWhqG4ANT2UqlAtoVehbJLW6fxHw,300
scipy/io/arff/tests/data/test3.arff,sha256=jUTWGaZbzoeGBneCmKu6V6RwsRPp9_0sJaSCdBg6tyI,72
scipy/io/arff/tests/data/test4.arff,sha256=mtyuSFKUeiRR2o3mNlwvDCxWq4DsHEBHj_8IthNzp-M,238
scipy/io/arff/tests/data/test5.arff,sha256=2Q_prOBCfM_ggsGRavlOaJ_qnWPFf2akFXJFz0NtTIE,365
scipy/io/arff/tests/data/test6.arff,sha256=V8FNv-WUdurutFXKTOq8DADtNDrzfW65gyOlv-lquOU,195
scipy/io/arff/tests/data/test7.arff,sha256=rxsqdev8WeqC_nKJNwetjVYXA1-qCzWmaHlMvSaVRGk,559
scipy/io/arff/tests/data/test8.arff,sha256=c34srlkU8hkXYpdKXVozEutiPryR8bf_5qEmiGQBoG4,429
scipy/io/arff/tests/data/test9.arff,sha256=ZuXQQzprgmTXxENW7we3wBJTpByBlpakrvRgG8n7fUk,311
scipy/io/arff/tests/test_arffread.py,sha256=-Kx0QC7gkKuwxxUVvoxpGyGYva2JbzQWs6mw7FPf2ig,13084
scipy/io/harwell_boeing.py,sha256=Wdd3nB8n1bxmvbjYBUBa1_ZmWbsPaIy3AJBZt2JJQmQ,898
scipy/io/idl.py,sha256=YhznpLgDnxrm9bwG7PP8zb6volg9oofEXYBTL86X7E0,794
scipy/io/matlab/__init__.py,sha256=uBmpYjqjkLRikI2im0mk6SOa13aAuQpSqwHY79RsoSE,2022
scipy/io/matlab/__pycache__/__init__.cpython-310.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-310.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-310.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-310.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-310.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-310.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-310.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-310.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-310.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-310.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-310.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-310.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-310.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-310.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-310.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-310.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=8RmsRKpJt_48P6bWbQw4HmwwP6g6uZhmWM3DX_dIAok,1902
scipy/io/matlab/_mio.py,sha256=Rr89q5cFlKwH4svZy_VzHXFZ48PlXjSzcP9TpAr0MA0,12799
scipy/io/matlab/_mio4.py,sha256=9gZ9pV_Esuh63jDaFc1w7cH0zu6_V6ee24YKPJa3ryk,20612
scipy/io/matlab/_mio5.py,sha256=x-PPeUdpXvyS1pGz0NCV4xEiHxywHofdcjoib2teF88,33584
scipy/io/matlab/_mio5_params.py,sha256=2NBQ0IEVRQS5GQ7_AoKY3Dl_CqzaA3kltnw8-_D1tXU,8199
scipy/io/matlab/_mio5_utils.cpython-310-x86_64-linux-gnu.so,sha256=8UGE_g-MP2zeHn4kNaaXUa6mXpG2iRd3pqiRbjMo_Mg,231752
scipy/io/matlab/_mio_utils.cpython-310-x86_64-linux-gnu.so,sha256=rybljLlAN6tgKvd2YTqO3SSS1WSq_xKzO9Ojk2VXSo8,50360
scipy/io/matlab/_miobase.py,sha256=HGNwKjdejDz4SFA7NWXWq_Lx8gBKyjswnw6K4p7FTeg,12875
scipy/io/matlab/_streams.cpython-310-x86_64-linux-gnu.so,sha256=yPEaQmd8ggD0_E0dm68tfIne4d-bziHI66PTbP8aHOk,134536
scipy/io/matlab/byteordercodes.py,sha256=SjReEJ2PzTMsU5fNeZ2m3i05uX6LiJ_GLsFi-PVKXyE,849
scipy/io/matlab/mio.py,sha256=HQSGsh4b1F6KoHWV8uEdPIgu2nBjclubz0ZaE5mwup0,894
scipy/io/matlab/mio4.py,sha256=gsMWD_lpymj7BLAh0dwVHXMervPkdLu_79PZtABjcCM,1201
scipy/io/matlab/mio5.py,sha256=3dgxKJjhjooruN_ch9UxlAIN1_Re_to8I5v-x_PB7TE,1435
scipy/io/matlab/mio5_params.py,sha256=g3Jk-weBAqKSwV9IqtB-cf0DkuYrKcxsO4cojGRFwPk,1526
scipy/io/matlab/mio5_utils.py,sha256=K_ILFiIcD5EispmZtCidJJD69_ygB4OaFI6-fiiJ9oo,899
scipy/io/matlab/mio_utils.py,sha256=jthSqDwKuvQaNYuKx-02atSoNiQ5PD9uAVzlyWZeRIo,786
scipy/io/matlab/miobase.py,sha256=l6sTgtB3-CGjin4L_6Xbf-gnCOGFnBvh0yCEG3_U354,988
scipy/io/matlab/streams.py,sha256=wgX5MSEUPdAhxK0DHw9iQsLzwnHU9GIrxIDm6JdWMGg,809
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-310.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-310.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-310.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-310.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-310.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-310.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-310.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-310.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=FCHBAxeQZlhvTXw-AO-ukwTWvpN7NzmncBEDJ1P4de4,938
scipy/io/matlab/tests/test_mio.py,sha256=hIarSUjITjgr7XnHTRE3_doPipbWb_CvEfe0JOXOfxo,44565
scipy/io/matlab/tests/test_mio5_utils.py,sha256=eacgGg0TaQXOkG7iaeYovtWyjPgYCY50mHPoPjnHMTI,5389
scipy/io/matlab/tests/test_mio_funcs.py,sha256=fSDaeVPvCRBFzqjWtXR5xIv9UQ_yv6Y_Nl5D5u0HIGo,1392
scipy/io/matlab/tests/test_mio_utils.py,sha256=GX85RuLqr2HxS5_f7ZgrxbhswJy2GPQQoQbiQYg0s14,1594
scipy/io/matlab/tests/test_miobase.py,sha256=xH4ZOR_b25TJLyIGqYQdeSASpTi8j-oIkRcO4D-R4us,1464
scipy/io/matlab/tests/test_pathological.py,sha256=-Efeq2x2yAaLK28EKpai1vh4HsZTCteF_hY_vEGWndA,1055
scipy/io/matlab/tests/test_streams.py,sha256=-Yf5bbmFQnEdyW_zmQstHdMBkn95RYVxCzg-Cfdg9Qs,7319
scipy/io/mmio.py,sha256=TkHUGo7h8JCkFI5se5T_rSC3Wc_Ojkb-yLhp99cmV-M,779
scipy/io/netcdf.py,sha256=A5jSFgdrJGZHgeoFHvLuEHMFi0ZYZt76eyOErVHy04Q,1080
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-310.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-310.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-310.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-310.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-310.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-310.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=2NZb7RoXsoH5pqh1WHCH6j0PTf4q_Lnee_vmgcmU1Xs,7572
scipy/io/tests/test_idl.py,sha256=523EmsfiCoQUa9rSddGY7PNRD1W7tsiIg-RzMlGdIo4,19680
scipy/io/tests/test_mmio.py,sha256=TjvCF_s0_b3llhC_e6PF6MtJwra0zHG5AvEsWPuADQo,26906
scipy/io/tests/test_netcdf.py,sha256=3YN0UhGZ4o7XESi1pEWoh1QgYu9gMwWX2zIZQKPg0_E,19313
scipy/io/tests/test_paths.py,sha256=3ewh_1yXujx3NIZ3deUjepFJgJDa5IHIugxupLDhHoU,3178
scipy/io/tests/test_wavfile.py,sha256=UluHY_ZPAbAaot_5ykV2aArBmwMRlKhEdZHiTzj-JLc,15303
scipy/io/wavfile.py,sha256=CXcu2wq38iAExx-bBeGHeYbStPxF9uhss3nA9lgyUow,26642
scipy/linalg.pxd,sha256=0MlO-o_Kr8gg--_ipXEHFGtB8pZdHX8VX4wLYe_UzPg,53
scipy/linalg/__init__.py,sha256=8SGffW1EtfHxc2kjyDjflFIbz14gSYsNbXeMc-2UAdo,7733
scipy/linalg/__pycache__/__init__.cpython-310.pyc,,
scipy/linalg/__pycache__/_basic.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-310.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-310.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-310.pyc,,
scipy/linalg/__pycache__/_flinalg_py.cpython-310.pyc,,
scipy/linalg/__pycache__/_interpolative_backend.cpython-310.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-310.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-310.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-310.pyc,,
scipy/linalg/__pycache__/_misc.cpython-310.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-310.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-310.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-310.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-310.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-310.pyc,,
scipy/linalg/__pycache__/basic.cpython-310.pyc,,
scipy/linalg/__pycache__/blas.cpython-310.pyc,,
scipy/linalg/__pycache__/decomp.cpython-310.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-310.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-310.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-310.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-310.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-310.pyc,,
scipy/linalg/__pycache__/flinalg.cpython-310.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-310.pyc,,
scipy/linalg/__pycache__/lapack.cpython-310.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-310.pyc,,
scipy/linalg/__pycache__/misc.cpython-310.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-310.pyc,,
scipy/linalg/_basic.py,sha256=UflSOkjGw8j3qwZa4-ltaZnOTdnj0-Hf1wVreO0nfCs,69317
scipy/linalg/_blas_subroutine_wrappers.f,sha256=pnqlE8yxj0Uh8HGug6v0JsD76QbNdRE-_5ErKUXAOxs,7757
scipy/linalg/_blas_subroutines.h,sha256=iodn74tn1PwQFzOX-cbqOus6LjAx43ETe5YhndHhxs4,19068
scipy/linalg/_cythonized_array_utils.cpython-310-x86_64-linux-gnu.so,sha256=KeC6z4Dr7WfYoNHyzy6P4OeDK-eYHBVL-yItxbVV6PM,518568
scipy/linalg/_cythonized_array_utils.pxd,sha256=OlWTbJt3gmdrfRFyx_Vz7GTmDTjr8dids5HA4TfC6R0,890
scipy/linalg/_cythonized_array_utils.pyi,sha256=HZWXvJdpXGcydTEjkaL_kXIcxpcMqBBfFz7ZhscsRNo,340
scipy/linalg/_decomp.py,sha256=ZC8MHAd3dKW6embYA4wqekV-cuTSMomp334q9vz8Zh0,61381
scipy/linalg/_decomp_cholesky.py,sha256=iCRl5kCijw__9VXbrE5Fdy_X1yUAAaP4vi1XBtZH9nA,11903
scipy/linalg/_decomp_cossin.py,sha256=eDHjrwa8-qSab0RuEepIFifreWFC9R5CaG04HgQp0gQ,9094
scipy/linalg/_decomp_ldl.py,sha256=dUj9QPKS1o1jjKWNr37MAU3MGQE_00XyQdIIxR6lL_g,12516
scipy/linalg/_decomp_lu.py,sha256=vadIWhitrb5IWspaKOxwnCTQ2P97yKMoHi8v6Yuz_wg,11766
scipy/linalg/_decomp_lu_cython.cpython-310-x86_64-linux-gnu.so,sha256=JStmDONjzNJYKKqW9KHJhaHp4zHeBzIOuZEGfngP5Tc,245472
scipy/linalg/_decomp_lu_cython.pyi,sha256=V_4k7A_U_esY0DgcEZDnh1jRv15s3cys5kncVq9Yngc,262
scipy/linalg/_decomp_polar.py,sha256=arzJ40FP1-TFsRvXPCP1qdNTsT60lkBcKBHfhB2JxxY,3578
scipy/linalg/_decomp_qr.py,sha256=E-ibpl1QdMu8HnllINd81j-FufmWCCbL35ImOoOiWAA,13727
scipy/linalg/_decomp_qz.py,sha256=xk9KVxigb5j6GZz3bBeDwbya0V0ZRi9-I9CSsA_hFbY,16336
scipy/linalg/_decomp_schur.py,sha256=kQN2b1uVK5v2BtkBrCGJsryZYBDY9vna-144oW4f0S8,10355
scipy/linalg/_decomp_svd.py,sha256=51jqAHCpcMl0meGCVDRtOK5z7wotSF5Ns9fPx1mZjrE,14901
scipy/linalg/_decomp_update.cpython-310-x86_64-linux-gnu.so,sha256=NZhZlMumMbI0mE4bae44XmVcxC6vVKm_xgeGqirc3s8,347376
scipy/linalg/_expm_frechet.py,sha256=gJQcBbSQ_Q6OORSvHNPokB5ahvXt9LC48zA5t3jftB8,12326
scipy/linalg/_fblas.cpython-310-x86_64-linux-gnu.so,sha256=utrUkKVY-N8Z-VlomDSuRlCG_qVT-S7xUXB2maBozhw,682881
scipy/linalg/_flapack.cpython-310-x86_64-linux-gnu.so,sha256=0YNlUB7bkIIjQfWinGNkJkF_yn0avSFN44f73_rUb6E,2291617
scipy/linalg/_flinalg.cpython-310-x86_64-linux-gnu.so,sha256=2TCad992iG1MvK_Se9NW-J3mpxx0ee2Zg9rtxqteia4,83769
scipy/linalg/_flinalg_py.py,sha256=W1-9Gyx-2OE2Ej2x9eWULNsGOIVF4sosuSJBgt4zxcI,1489
scipy/linalg/_interpolative.cpython-310-x86_64-linux-gnu.so,sha256=5Q4-dk_z7iDtmAjER1CU0or1PnarvRdmGKcxGD3Md1E,473473
scipy/linalg/_interpolative_backend.py,sha256=yycf_ceX0dgf7Usjvtaxmkm_cT-2jmEMBuWY6tJST2g,45192
scipy/linalg/_lapack_subroutine_wrappers.f,sha256=lSvEytuOblN5KOmcHlyfj7MVfn5CyyTllZQAp7i_woM,34384
scipy/linalg/_lapack_subroutines.h,sha256=WOzLcinUl8EqEGuYUMDwOvEal_sviBjztpLIrTp3eZc,246836
scipy/linalg/_matfuncs.py,sha256=ZLUSyg0ItYh95NRw9o901v_GvnVK3GU4KufFrp6IYWQ,25032
scipy/linalg/_matfuncs_expm.cpython-310-x86_64-linux-gnu.so,sha256=SlXSv_GsVC7GN2kCK0Bla4v7r1tSr4mWlTGREKCH2OE,433904
scipy/linalg/_matfuncs_expm.pyi,sha256=GCTnQ9X_CNNpadcYhDFhjL2WBhzfdnt0mkW1ms34cjY,187
scipy/linalg/_matfuncs_inv_ssq.py,sha256=DU0VJe4HzY4rY6ms0QGQWKb0kvgUixkP6Bl22__g-o8,28030
scipy/linalg/_matfuncs_sqrtm.py,sha256=BjJtGbUsXXvAvgi4p2R3oZI2408Puhha1u9TNY-FOVM,6658
scipy/linalg/_matfuncs_sqrtm_triu.cpython-310-x86_64-linux-gnu.so,sha256=TMFHbAkM8BKv4qd3uj5nAplcBQ6F8Rbl9o4-OIOqyBE,249056
scipy/linalg/_misc.py,sha256=3IPq-LIQcxV7ELbtcgZK8Ri60YWbhpN_y7UYe6BKEgA,6283
scipy/linalg/_procrustes.py,sha256=f0B7Y6BpzNmupmjCEAiIRQ4SLvjFQIropnXqJF9NIrM,2786
scipy/linalg/_sketches.py,sha256=n6PEJILrFpzWhdf-sKFgGN-0elEwqvBlI0Z3H54tk0c,6145
scipy/linalg/_solve_toeplitz.cpython-310-x86_64-linux-gnu.so,sha256=fc8txE8wwTnAWqXZmVKpDefd3BQbV3b9wL8E21qte-A,275296
scipy/linalg/_solvers.py,sha256=tuEtNYXqs3xp-myKBLGAi6HStrQB-poNhoyzw30rayY,28380
scipy/linalg/_special_matrices.py,sha256=XySdXCfQVgHUf35HNMogage7VRWBU9FPlIQCBBhbqRw,40711
scipy/linalg/_testutils.py,sha256=iU4Jwevza64VbjL6MZ4XQD1D4v-G7MEj3gNn-7kCKKw,1730
scipy/linalg/basic.py,sha256=Bm9qs8IDccT4i5ZYP148BRMRDXM5ltzS7acZ3gJwg6s,1026
scipy/linalg/blas.py,sha256=Vj5McQyCYtwwhqq4eInKhJvGhzn0ljDg2wmpAy5G9ec,11677
scipy/linalg/cython_blas.cpython-310-x86_64-linux-gnu.so,sha256=_Yb-cWpjuuPMpncLs8xzbHTvqUjeU-83ls6CEuGinqY,311689
scipy/linalg/cython_blas.pxd,sha256=DjZQLdAQzbyJpiJVNRkH3l6WvKG8SXh04-f7Xn_XoRI,15735
scipy/linalg/cython_blas.pyx,sha256=QpRfsJm_TV0o4vN-XmWQUI0aLOZ_eL0OzFRInBT15h8,64739
scipy/linalg/cython_lapack.cpython-310-x86_64-linux-gnu.so,sha256=2zAqQkzv5ACKoaNRNwveCC6GHuVOHcT94JgwSceWKOk,820705
scipy/linalg/cython_lapack.pxd,sha256=uIHie7QsYsbQQ20kkjrRgc87NGBjq8gkM6z5DfPK594,206043
scipy/linalg/cython_lapack.pyx,sha256=uUTRGU1_NVbidm9vhmxyLzwDIkWkSbE0Khq9dzTRZtk,701624
scipy/linalg/decomp.py,sha256=2GO63DouH59OirrwuOdp1OsunrRrQqpUp_rh9cpzxAg,1057
scipy/linalg/decomp_cholesky.py,sha256=GqKhTklJCBpycKC_hSTwMJ73t6HS4GdOItHbXzjLRb8,917
scipy/linalg/decomp_lu.py,sha256=FS7TGi24g8Q33UBJagevgpbXB7i887Yk65VbKMLOF0U,856
scipy/linalg/decomp_qr.py,sha256=wwe2u4Fn6IAOqO50_WTCQrwPKrhPzIpbxkqfPER4WpI,796
scipy/linalg/decomp_schur.py,sha256=qTcDsoWVD4viq7eySdcIGaixEPuyvoLh0LESLzrzazI,882
scipy/linalg/decomp_svd.py,sha256=KoBb6aFnHzFkHTi_DIbD5L3rhBFvwcZ6Tb5ROHayfhA,850
scipy/linalg/flinalg.py,sha256=q4wlBcwHjfEYhUJ-qjI0FSpnb2-rjLPTqKFDu2fMGNw,677
scipy/linalg/interpolative.py,sha256=EXPMd_MQCbRW_JPBbC3d6P6-d8A3idlLpska6vBIOeQ,32188
scipy/linalg/lapack.py,sha256=B-sC0kfbRO7zON2Iji4WlSBzuuRpuIbWJJjiROHOBRA,15626
scipy/linalg/matfuncs.py,sha256=yj2Xh_u2Re1ytR3kuwK5L2o590JZANtEv_10Z92loa0,1098
scipy/linalg/misc.py,sha256=YY_fRbg979uqbgyYrsjsDTnhkyLc1MjNjKNLDHBNeCs,799
scipy/linalg/special_matrices.py,sha256=qaR-sECUZCHGI2G4tO4OOsC0kGs_C_AOW9YWnbBWCjo,1026
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_lu.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_misc.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-310.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-310.pyc,,
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=infOnybYi2x-sediFrQcdxlE_eGzxES8KHdWw_8L2C0,69319
scipy/linalg/tests/test_blas.py,sha256=o6BEfT7IQLvhciT3waCSZbTZCKoIWqf51QTemINUe14,40206
scipy/linalg/tests/test_cython_blas.py,sha256=0Y2w1Btw6iatfodZE7z0lisJJLVCr70DAW-62he_sz4,4087
scipy/linalg/tests/test_cython_lapack.py,sha256=EDhd6pmXxX0U4xxl5buBGH2ZjHj-J7LGq6rw6CZKA0k,574
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=O1EKWxsYt6k1zMWjFlQhTndQVOhHsJlSm-bHfPMny1U,3840
scipy/linalg/tests/test_decomp.py,sha256=yulpBvBMAVBXgd4c7Wki4Vd-_sOoNOHdMXFa_Es2Z0s,103064
scipy/linalg/tests/test_decomp_cholesky.py,sha256=O8kkqod4sj46DtNpeyuZrKQfMmJeU5sSRANXuUyP6PM,7265
scipy/linalg/tests/test_decomp_cossin.py,sha256=5PF6FGd-WisBFeWLJqKmgbbIdWedJ-skZ9NevCM5x1k,5772
scipy/linalg/tests/test_decomp_ldl.py,sha256=kypnSdeR7YMEA4aC62huu0Dnrs3hx1Ea75LN5MkR8OU,4933
scipy/linalg/tests/test_decomp_lu.py,sha256=i7K4zDx3PocMSPYJzaS0IiZuVRphC_CXzLreK1FNkIE,11186
scipy/linalg/tests/test_decomp_polar.py,sha256=5x5vz9rJE2U2nvo0kx6xMX5Z9OcnqxayPZvAd4dwsUQ,2646
scipy/linalg/tests/test_decomp_update.py,sha256=U1333Q_d13QnUeiXcQkJsE_rBJq6olHXu-6K3nUmEHg,68486
scipy/linalg/tests/test_fblas.py,sha256=TIdXGmuvQ_na6eMlq7k4UdCELNWWDa7VG4imiyrSC0I,18685
scipy/linalg/tests/test_interpolative.py,sha256=cAx8lJhE9YH-mXgC-Ltf4xv4nDhq0m0jq65tRkIut1g,8956
scipy/linalg/tests/test_lapack.py,sha256=420U95-qu0qtVLCR6yAOXAZ7Q6jYcp5B7RQTJ1_fhrw,125473
scipy/linalg/tests/test_matfuncs.py,sha256=xRT-wuDowvL64sSdp1dzvCbk4rSvNyvNR4kp5nvysL8,38693
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=Wd9T03zZRwX3M3ppkhYJiJbkWZ_xop4VKj57TjeozUs,3870
scipy/linalg/tests/test_misc.py,sha256=HP9jfKohbJIaKVcBqov9hAOHYk5dZck497-V5DMHe6E,76
scipy/linalg/tests/test_procrustes.py,sha256=WkNNarBf69izBmlOhu4-u0eWdzkSzYHQuDZh-w89fOU,6758
scipy/linalg/tests/test_sketches.py,sha256=FVEcNV43JteZZU7GDdBjtl-_alYDimxnjgKvpmtzVsI,3960
scipy/linalg/tests/test_solve_toeplitz.py,sha256=KuTAYh-8MRWjaHclgQuIaBBx8IBTGEzXgZnhM_gjWxo,4010
scipy/linalg/tests/test_solvers.py,sha256=SocvQQovhT_wZeLj1I4ixwC5xoeCZ_vLz6QZ0RrIgZU,31556
scipy/linalg/tests/test_special_matrices.py,sha256=Ku61vtp648WFiXfuBxb3CoAbVNoF3xERtuskEk7yqTc,27049
scipy/misc/__init__.py,sha256=CdX9k6HUYu_cqVF4l2X5h1eqd9xUCuKafO_0aIY5RNE,1726
scipy/misc/__pycache__/__init__.cpython-310.pyc,,
scipy/misc/__pycache__/_common.cpython-310.pyc,,
scipy/misc/__pycache__/common.cpython-310.pyc,,
scipy/misc/__pycache__/doccer.cpython-310.pyc,,
scipy/misc/_common.py,sha256=ndBktpW3llbZYf6IwS3lT7wBZIqV7AZygw2m9UTqoTA,11120
scipy/misc/ascent.dat,sha256=6KhJOUhEY6uAUa7cW0CqJiqzOpHWRYps0TxqHK1aAj0,527630
scipy/misc/common.py,sha256=BM-V8TKsvDKG_EtDRE4iIw8Of1q4U6JOwl7WSj6-1GI,869
scipy/misc/doccer.py,sha256=D-G2jEalH4nXXlDEfZ59Ao9aj1_1t2SIb5ZlW9NHONE,766
scipy/misc/ecg.dat,sha256=8grTNl-5t_hF0OXEi2_mcIE3fuRmw6Igt_afNciVi68,119035
scipy/misc/face.dat,sha256=nYsLTQgTE-K0hXSMdwRy5ale0XOBRog9hMcDBJPoKIY,1581821
scipy/misc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/misc/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/misc/tests/__pycache__/test_common.cpython-310.pyc,,
scipy/misc/tests/__pycache__/test_config.cpython-310.pyc,,
scipy/misc/tests/__pycache__/test_doccer.cpython-310.pyc,,
scipy/misc/tests/test_common.py,sha256=0h_qT7hwQnqx4Oc6ccvM-U79EkbXPq5LNlC3QSvR88M,833
scipy/misc/tests/test_config.py,sha256=j1Ppp6DCZy9wMxTmBEGxq4MScvsQXTQk7268EnNnPFQ,1244
scipy/misc/tests/test_doccer.py,sha256=V1B5Z-XfIQFiSyRNo3PXG-AQfToFmoQ1oOBGjxK2zmo,3738
scipy/ndimage/__init__.py,sha256=WKSnd4UmzibmbEZV-Sw31c9u7qDOa6WDqB7KMVcRIOU,5155
scipy/ndimage/__pycache__/__init__.cpython-310.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-310.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-310.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-310.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-310.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-310.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-310.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-310.pyc,,
scipy/ndimage/__pycache__/filters.cpython-310.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-310.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-310.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-310.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-310.pyc,,
scipy/ndimage/_ctest.cpython-310-x86_64-linux-gnu.so,sha256=h98uh-F0_Ywmq7sQkE-zVgPCuj5JX3uZqeFVBgpYS0A,17008
scipy/ndimage/_cytest.cpython-310-x86_64-linux-gnu.so,sha256=8sDMih18EfFpfFuYlokZP8vD9_6sZTGo2P-47nX-738,64384
scipy/ndimage/_filters.py,sha256=sdXjtsLDIJrpAS5pCQ0FKPgnRbibllxR5-BmmUpaaDk,63733
scipy/ndimage/_fourier.py,sha256=57ONJoo_8CmvhP5vCL7ijVvaK5U-gvp7LM0fL3YZ55o,11390
scipy/ndimage/_interpolation.py,sha256=ZxLkxmeehOtulo0qKrvdq2udOzlZar5j69wZUHVOZ9I,35441
scipy/ndimage/_measurements.py,sha256=A9t8ErYIC76Irs1LSaNH1z7zTCguginGHYcpSXfCafc,56014
scipy/ndimage/_morphology.py,sha256=w0S3_DMd-AE8FSIuembTbjHDbLxwuivToaJAcf6wCxw,94837
scipy/ndimage/_nd_image.cpython-310-x86_64-linux-gnu.so,sha256=_YHQznlCh0XDdnXV-RVYlyuLloTWZHB7-rfsdGWJ3l8,155184
scipy/ndimage/_ni_docstrings.py,sha256=9DSB07qpihY6Gv_czcMN3BNzKNJ0rq9zISTtFIe3LPk,8516
scipy/ndimage/_ni_label.cpython-310-x86_64-linux-gnu.so,sha256=1toZorjo8W0_5VlMKtjbFP4CBdT9n7UA_6qx_kj4yhw,366896
scipy/ndimage/_ni_support.py,sha256=BK_Y1vfMlE17vxsb3jZ9BR4LevHTWJ-DAtxDEpek-w0,4529
scipy/ndimage/filters.py,sha256=aflHOtOL7ZL3EtpYctkPPW-iqJhH2pAhN4JENdgv4kI,1217
scipy/ndimage/fourier.py,sha256=ftajFZrIBb9HBkLjDUT8PgdnrGuUCVskcca_FeswrFc,840
scipy/ndimage/interpolation.py,sha256=56huW77Dwa3DizXKT87dd4Jpf7Qt0ygq5dYyeOFbzuM,933
scipy/ndimage/measurements.py,sha256=9gi9FD20M8lPdLPkI8iA8VVdUZPbYKmjhlQfLxqfoPM,1015
scipy/ndimage/morphology.py,sha256=tufpeFNW3Amik0BGaITG9NToqtZtR6ejFtx1s75MNQM,1188
scipy/ndimage/tests/__init__.py,sha256=2a_qPOKpnJz78vEZXPKorEO8KTHkpQfURDGfvRgHxhE,426
scipy/ndimage/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-310.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-310.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-310.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-310.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-310.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-310.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-310.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-310.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=JPbEnncwUyhlAAv6grN8ysQW9w9M7ZSIn_NPopqU7z4,294
scipy/ndimage/tests/data/label_results.txt,sha256=Cf2_l7FCWNjIkyi-XU1MaGzmLnf2J7NK2SZ_10O-8d0,4309
scipy/ndimage/tests/data/label_strels.txt,sha256=AU2FUAg0WghfvnPDW6lhMB1kpNdfv3coCR8blcRNBJ8,252
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=vYLKm9sb7UeS7CLE-oNgIuJorBTfvJNL9gnrLdoZ6Ew,3452
scipy/ndimage/tests/test_datatypes.py,sha256=UCYf_2mKXeZHxUsBRCAbadB1ojEnKimbuV499h0Jb7E,2742
scipy/ndimage/tests/test_filters.py,sha256=JTZ36MaJc8CgajUhTTSzwZo3O5DzT6Yl4r06aPg4Wrs,93314
scipy/ndimage/tests/test_fourier.py,sha256=J0cRaJKijOgsI9magxNQP0ZrHi_Rf23O3_DQ_JlPvl4,6664
scipy/ndimage/tests/test_interpolation.py,sha256=3kTKe5U76lDnEGTAWW9SzHyCnkbcr2KM1CluN_nUicc,54771
scipy/ndimage/tests/test_measurements.py,sha256=CRjKR2-J5bZniI3tHom5MwwO1VxmXmHfPdK9kmIfsbw,47782
scipy/ndimage/tests/test_morphology.py,sha256=JezBYVHm0COzistD9IbtKvJiYViLQTm8QgKUqvVn-e4,106686
scipy/ndimage/tests/test_splines.py,sha256=4dXpWNMKwb2vHMdbNc2jEvAHzStziq8WRh4PTUkoYpQ,2199
scipy/odr/__init__.py,sha256=CErxMJ0yBfu_cvCoKJMu9WjqUaohLIqqf228Gm9XWJI,4325
scipy/odr/__odrpack.cpython-310-x86_64-linux-gnu.so,sha256=nxndZnnuiCgeAfAUQ2VuFSyf4qNpooSHKqhRE6tWJB0,222953
scipy/odr/__pycache__/__init__.cpython-310.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-310.pyc,,
scipy/odr/__pycache__/_models.cpython-310.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-310.pyc,,
scipy/odr/__pycache__/models.cpython-310.pyc,,
scipy/odr/__pycache__/odrpack.cpython-310.pyc,,
scipy/odr/_add_newdocs.py,sha256=zX9DJ9c4fJX-6RU9xYZEJVxlO72wmNxV6_aTKSQjoGk,1090
scipy/odr/_models.py,sha256=tfOLgqnV4LR3VKi7NAg1g1Jp_Zw8lG_PA5BHwU_pTH0,7800
scipy/odr/_odrpack.py,sha256=-Y_WHtDO3HuStG_wf7-168rEe9s_1LRNtCwI_Ia5NHE,42457
scipy/odr/models.py,sha256=EuQE3U_-9jUSMATZySrKiUXiB-WxgIBjI8kTrVHOSKw,793
scipy/odr/odrpack.py,sha256=nWDtxoCtRhx35KJPu2-UgH7YYuI_RxlwG4VZJqS8Ngo,837
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-310.pyc,,
scipy/odr/tests/test_odr.py,sha256=W91vQ0dNCSi3tJ2Qrn62ET524QC3gbzHCr_yEvFeGvg,20999
scipy/optimize.pxd,sha256=kFYBK9tveJXql1KXuOkKGvj4Fu67GmuyRP5kMVkMbyk,39
scipy/optimize/README,sha256=q7vAotiT7affj-8xYhiy0g9r0fQBE2caLUnvjqjgSv4,3416
scipy/optimize/__init__.py,sha256=sgQCAAvt3fpx8fwt_MrDdOVqoW4cAHeedaxQTrgeCVE,12880
scipy/optimize/__nnls.cpython-310-x86_64-linux-gnu.so,sha256=ihVnReFoqFAuL34X_YeYcwZyYpRHpDTPwxCUPWSbgqU,55065
scipy/optimize/__nnls.pyi,sha256=OcKuy0vD6qTjvkMU_qMfK0TX8rqw2Iv7E_M50OTd8fY,422
scipy/optimize/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-310.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-310.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-310.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-310.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-310.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-310.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-310.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-310.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-310.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-310.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-310.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-310.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-310.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-310.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-310.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-310.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-310.pyc,,
scipy/optimize/__pycache__/_milp.cpython-310.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-310.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-310.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-310.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-310.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-310.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-310.pyc,,
scipy/optimize/__pycache__/_qap.cpython-310.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-310.pyc,,
scipy/optimize/__pycache__/_root.cpython-310.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-310.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-310.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-310.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-310.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-310.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-310.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-310.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-310.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-310.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-310.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-310.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-310.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-310.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-310.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-310.pyc,,
scipy/optimize/__pycache__/minpack.cpython-310.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-310.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-310.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-310.pyc,,
scipy/optimize/__pycache__/optimize.cpython-310.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-310.pyc,,
scipy/optimize/__pycache__/tnc.cpython-310.pyc,,
scipy/optimize/__pycache__/zeros.cpython-310.pyc,,
scipy/optimize/_basinhopping.py,sha256=nb7tqPCiD-vspZwaJd7ecO4lvf8uQMqj3nXir7Fdsrg,30657
scipy/optimize/_bglu_dense.cpython-310-x86_64-linux-gnu.so,sha256=52CGkvpXs4GVYBC8AsSnSsrq45r1i0n0P9Lz3SWTYmg,337136
scipy/optimize/_cobyla.cpython-310-x86_64-linux-gnu.so,sha256=649SL_uf0Zfp-DFYGqaJODOAO-bJkcUMZaWij5gOz0s,96425
scipy/optimize/_cobyla_py.py,sha256=XGVyGI8QWv4GqJsq1escOzdsUXIHJ3cMvFXdb_mJjD8,10868
scipy/optimize/_constraints.py,sha256=ev97BW2KA6okid--ErAbt3PqNeX9XU9a3F_2oBRqU0Y,22552
scipy/optimize/_differentiable_functions.py,sha256=vpi8XCbBFAYgfA2DjSO7CfGWFIQvBFN-v-9g25vfbhk,22719
scipy/optimize/_differentialevolution.py,sha256=EvxOySbFq0uE-Je6IJn1UfE9YifBTkUXeObNDSWaqig,74593
scipy/optimize/_direct.cpython-310-x86_64-linux-gnu.so,sha256=_lwymWBKtgxvFQ3XD3chCzCRBsvtPku3RElZ-vGtsWc,43392
scipy/optimize/_direct_py.py,sha256=ShNGJHCdN02zGTQbBL5oEwxZ9yGH8dczXTsmnt1WJIg,11798
scipy/optimize/_dual_annealing.py,sha256=rG-GzWB2WFBcL2raLNniuVkElDR6xDDC015vVxjNEK8,30363
scipy/optimize/_group_columns.cpython-310-x86_64-linux-gnu.so,sha256=ZqqjnpMR93oSMczX8qxHLLf8O7kWt1_kTbs-40dSssA,95880
scipy/optimize/_hessian_update_strategy.py,sha256=PJcNU7ERwtm_WTMzXZzZsBowDtWia4kHAEuvzbkD8fg,15830
scipy/optimize/_highs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highs/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/_highs/_highs_constants.cpython-310-x86_64-linux-gnu.so,sha256=iQnOCLrP6AnIV6n0ebrwuOK9iXJYm6QtP9RX1xzSGfE,39984
scipy/optimize/_highs/_highs_wrapper.cpython-310-x86_64-linux-gnu.so,sha256=dTcnZPY9GyxO3uee5dReGgy0WR-Obg9ag6tcLPX5Oj8,4019992
scipy/optimize/_highs/src/cython/HConst.pxd,sha256=3a2mEDSx55TBAUYOuptSQYQzYTAHgP9c3anJGBlB7DE,5537
scipy/optimize/_highs/src/cython/Highs.pxd,sha256=gaLGR_nlEXhnN1WULlZQzOVr-bQO4QHdZvreQktFAsE,2173
scipy/optimize/_highs/src/cython/HighsIO.pxd,sha256=K7KXJoGm4s_cWtW4tQNByhujVF7SMIjDi86jWILzNoA,731
scipy/optimize/_highs/src/cython/HighsInfo.pxd,sha256=RUePUSonorGWf2DYHjvpUc-ZhVQI6LDiro5eZftZlOg,761
scipy/optimize/_highs/src/cython/HighsLp.pxd,sha256=_bcpjF7o-rK8gguxtXOF30XfVUrLjhLal90mAHumwAs,1132
scipy/optimize/_highs/src/cython/HighsLpUtils.pxd,sha256=be2KJVDOjTNYH8AfXqDdp7HPB0EiUWL8I1RGVqCeKz4,315
scipy/optimize/_highs/src/cython/HighsModelUtils.pxd,sha256=apvVhKle97Az82zThiHvCSdV9Bk2AsoVgx3rQdAsU2o,361
scipy/optimize/_highs/src/cython/HighsOptions.pxd,sha256=xwBux1AmfTUtvLa3PJSdxCrdxeD851Cf4_ktkw7BnjA,3186
scipy/optimize/_highs/src/cython/HighsRuntimeOptions.pxd,sha256=GyX_sgvBmUhzpDJBsex6LWeKesV1L3fbGdH166j6K68,287
scipy/optimize/_highs/src/cython/HighsStatus.pxd,sha256=s4nC9ViGKPTRD4VvqbZ5dZohVCtDqQPze_5svik6GjQ,365
scipy/optimize/_highs/src/cython/SimplexConst.pxd,sha256=I6RjfzaBahKI9Eerg52c5tYU1gS1ZA2DWfYfWefgWVE,5044
scipy/optimize/_highs/src/cython/highs_c_api.pxd,sha256=ut0M5I_pG2D6K3fUHzU9WD67Y1VMZuQOKhLLjv0umqo,358
scipy/optimize/_lbfgsb.cpython-310-x86_64-linux-gnu.so,sha256=QQInN1CfHz1WbsJ-ldlbfJtoJJTMnND-ZarCrBp4vP4,112833
scipy/optimize/_lbfgsb_py.py,sha256=Q0WVkEBSa7c0HzWDXwn1VH925gaPsGlSHAh_TMaDlHI,19097
scipy/optimize/_linesearch.py,sha256=oOKaVgiyLlIJkl6ThAJPoRSdx4j3pfxN_CcnsTGcr2g,27044
scipy/optimize/_linprog.py,sha256=ylTCd6uPhS6vQehF2nL8Bbn0tMrksN5CvmG_077zaJs,29666
scipy/optimize/_linprog_doc.py,sha256=epNaaFFZiynwRcHdBHfWg9aInfNSya_ZzGqf6IaSxd0,61943
scipy/optimize/_linprog_highs.py,sha256=G-wO0g46KUf7lJ0Go3Zr4meOxUzqZh8A-6vUGPJnFew,17571
scipy/optimize/_linprog_ip.py,sha256=7sgNEv0whXZx5lHM3tuB1pPhhHRHlquIV0U6MiflpHE,45749
scipy/optimize/_linprog_rs.py,sha256=JruGeJ2uPVQUxN_A5A_4WuRTQfx4Mz4xoH27HNgGcFI,23149
scipy/optimize/_linprog_simplex.py,sha256=4drRZGx5BhzOPmBAKlTgYu0ZgjHR7xhqxoqZPWmYub8,24725
scipy/optimize/_linprog_util.py,sha256=qF0Gkr-0S7BY2Mr-qZu-K23RbPqDXHx5BjIqfd5Uxt8,62773
scipy/optimize/_lsap.cpython-310-x86_64-linux-gnu.so,sha256=ICTb0TpUWjFZr6oXiCtiRzxzMETGH16SKMR-P4Jo3y0,26864
scipy/optimize/_lsq/__init__.py,sha256=Yk4FSVEqe1h-qPqVX7XSkQNBYDtZO2veTmMAebCxhIQ,172
scipy/optimize/_lsq/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-310.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-310.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-310.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-310.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-310.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-310.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-310.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=7u5B8LfUbv3ZRZ8DAZKuDTSNRfDEBmTsn25VZtMMsKk,5195
scipy/optimize/_lsq/common.py,sha256=tOZHMQ1H0HAHa3-bfFJh-kJmg7hHydsgEUuIIf6_cLI,20548
scipy/optimize/_lsq/dogbox.py,sha256=97htRlr-Yt-********************************,11682
scipy/optimize/_lsq/givens_elimination.cpython-310-x86_64-linux-gnu.so,sha256=zdZQy6iU1ldT95yscLysRpjp1dw8jINfP7gKDGfSjyA,209168
scipy/optimize/_lsq/least_squares.py,sha256=cLmnWy55L5DAFnJgzRZghgDAuTenmgI44Hi0J18Q-GM,39654
scipy/optimize/_lsq/lsq_linear.py,sha256=-ZEu0gJFBVC_mw2L9fAGwmCN5LPYhDIubYFT7-yy0rs,15217
scipy/optimize/_lsq/trf.py,sha256=ElVHnB2Un3eaQ4jJ8KHHp-hwXfYHMypnSthfRO33P90,19477
scipy/optimize/_lsq/trf_linear.py,sha256=jIs7WviOu_8Kpb7sTln8W7YLgkcndv0eGIP15g_mC4g,7642
scipy/optimize/_milp.py,sha256=gIXMrNNAg_21_fgAtVYzswyQZ7W3V9x65csBHUp8Jc4,15129
scipy/optimize/_minimize.py,sha256=nMor4CQF7a4X2MV90d6hekIq6JBvFx8obWyo0Q6ctgs,47713
scipy/optimize/_minpack.cpython-310-x86_64-linux-gnu.so,sha256=DTPfNKR5BwIKE3KjNgNV2uOHObzBxXPn3q9Llem-Dfs,78224
scipy/optimize/_minpack2.cpython-310-x86_64-linux-gnu.so,sha256=-qF8ZRrB506L2b9id66ljfyWuYA5GJ68TAteyBQRsK4,52560
scipy/optimize/_minpack_py.py,sha256=ctWek8czNzZRO0Lmvalk9P3JqX_6FCaYdLA9BC5K9PY,43032
scipy/optimize/_moduleTNC.cpython-310-x86_64-linux-gnu.so,sha256=tymCeJIk7b1I-6HViDjxExPFtNt0CSdjnxSqiw1nSDk,146064
scipy/optimize/_nnls.py,sha256=eC6mFJGW9svpP7zauCNu76Jgix_AW90yzyQCTcsztB8,2282
scipy/optimize/_nonlin.py,sha256=GutcvMZq-7yI3HQBHM1TYhxGabio3kIkljKzKhd3Stc,49031
scipy/optimize/_numdiff.py,sha256=EGBgLxKoD5zecdTC7ajXAOa6b6Eb5pks1jLa-_xwQmg,28279
scipy/optimize/_optimize.py,sha256=fdKiEiOWY0v19pufY92ipzzBOq_j66ZRtFuKgwAvtlQ,146177
scipy/optimize/_qap.py,sha256=UkIA7YMjoaw00Lj_tdZ4u9VjSPNOmMDINPMK9GTv3MM,27658
scipy/optimize/_remove_redundancy.py,sha256=Z-bdCyBuanuMzEhKGR-6Rs03b4L9uK7dKfWIdW1LA0E,18767
scipy/optimize/_root.py,sha256=rdtjKEmweSk1GA4Tv6rWo7VRuNIixuTrRQ5ZGzZWcIk,28280
scipy/optimize/_root_scalar.py,sha256=yMbri0R7W14em_FL-WJYe5xNq5kmFCpXnTSuXMKrxSs,19556
scipy/optimize/_shgo.py,sha256=b67znW5itRw-sXe_3sYKGrHTG1lAkfihJZh04EZAu1I,62233
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_complex.cpython-310.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_vertex.cpython-310.pyc,,
scipy/optimize/_shgo_lib/_complex.py,sha256=L2nW864qR0d1xj6seMQBeLkkByzjSeDo8EebnRflkU0,50352
scipy/optimize/_shgo_lib/_vertex.py,sha256=Su2dDpxPWHeO6mHTHuzcwUTD6RWbnG3RFbe0LqKInv8,13997
scipy/optimize/_slsqp.cpython-310-x86_64-linux-gnu.so,sha256=ZzczyopCKHgKDOYkAs40rwoiDqCRbyw7QrfDMTmEu10,82320
scipy/optimize/_slsqp_py.py,sha256=VPQzFxE-L2lD-ghFTvVcm6wkPR54LiH4fb-8NUNWgvI,18767
scipy/optimize/_spectral.py,sha256=32IgwND-YwNY6UlwBXuw7k4Wl0tSoKOY2L0ApTeVkeU,7920
scipy/optimize/_tnc.py,sha256=l9ardnG6P3rSTAtIjQiTYX9ALQ0pOZYd4zJafc2IQIA,16678
scipy/optimize/_trlib/__init__.py,sha256=cNGWE1VffijqhPtSaqwagtBJvjJK-XrJ6K80RURLd48,524
scipy/optimize/_trlib/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/_trlib/_trlib.cpython-310-x86_64-linux-gnu.so,sha256=80sSLr7wiz5wcAAk3NO_bgA-_WFlF1V-FF3MtWZMIAA,372465
scipy/optimize/_trustregion.py,sha256=SLWOLeLpHG10mBiZtHVYuEY19QAtakloXIvI1CYWjw8,10786
scipy/optimize/_trustregion_constr/__init__.py,sha256=c8J2wYGQZr9WpLIT4zE4MUgEj4YNbHEWYYYsFmxAeXI,180
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=690VxTb7JJ9RzGwa-LN2hASKlqQPmulyEDZA7I-XyLY,12538
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=5NiEruWnhYL2zhhgZsuLMn-yb5NOFs_bX3sm5giG7I8,8592
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=mWneWXy1bmte2nH_rq6VYPKXh9YlNIkiu3IG9uvRTck,25744
scipy/optimize/_trustregion_constr/projections.py,sha256=2V9GysEHMzuYcE93CpnK2Q5iwQQBIc1rbtOJJBIZUZQ,13105
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=EtAhRcEtSnGsEeEZ2HGEzm-7r0pnXMCgl9NemKWvdzg,22592
scipy/optimize/_trustregion_constr/report.py,sha256=_6b3C2G18tAgTstQSvqJbZVFYRxWKuUXFA1SAz95Y6k,1818
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-310.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=zVPxZDa0WkG_tw9Fm_eo_JzsQ8rQrUJyQicq4J12Nd4,9869
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=-UrTi0-lWm4hANoytCmyImSJUH9Ed4x3apHDyRdJg5o,8834
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=vrP52LzzAA3D8T5fhVzQv9Eo-G9t3F8qfrNuq7XrzQM,27719
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=lbr947QQxz681HxTXEZZ0B6_2VNKiN85Inkz7XYhe4A,1070
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=fXuyoZ5WmIwce2EA-Gdld7S2YrM7usImXWBNk3DnURw,13802
scipy/optimize/_trustregion_dogleg.py,sha256=HS783IZYHE-EEuF82c4rkFp9u3MNKUdCeynZ6ap8y8s,4389
scipy/optimize/_trustregion_exact.py,sha256=pcUzMYDUdLywjRxOifFxerUOiNd5GKPvjZhoyw9yfiw,15413
scipy/optimize/_trustregion_krylov.py,sha256=KGdudJsoXXROXAc82aZ8ACojD3rimvyx5PYitbo4UzQ,3030
scipy/optimize/_trustregion_ncg.py,sha256=y7b7QjFBfnB1wDtbwnvKD9DYpz7y7NqVrJ9RhNPcipw,4580
scipy/optimize/_tstutils.py,sha256=ASPguc3Zypn-yCOw3kqfnv3_V2LnaV-sw53uDJo_rzY,33043
scipy/optimize/_zeros.cpython-310-x86_64-linux-gnu.so,sha256=8wEb0RyMpKpbOPVfUTFDfN-vRonegrTDLDToQjlta0Y,21648
scipy/optimize/_zeros_py.py,sha256=hejN9Jq7ZIGO94HJtmKNBttBupmAROuMfgo6PMrlUZ0,51770
scipy/optimize/cobyla.py,sha256=sJD7CvPLBZgAU1y0JsdB6BtPAJE1hBViTdAEtNyj0no,840
scipy/optimize/cython_optimize.pxd,sha256=ecYJEpT0CXN-2vtaZfGCChD-oiIaJyRDIsTHE8eUG5M,442
scipy/optimize/cython_optimize/__init__.py,sha256=TmYM1xq7G9KdJMFsToTGBFGiQdsp5kzn_aZY1SMCO0o,4869
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/cython_optimize/_zeros.cpython-310-x86_64-linux-gnu.so,sha256=ZL_GkQO0qYJ1Dusm4-9inHgmr_PHoCIbZUc9n0gless,98896
scipy/optimize/cython_optimize/_zeros.pxd,sha256=anyu-MgWhq24f1bywI4TlohvJjOnpNpkCtSzpKBJSSo,1239
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=6Gc0l1q-1nlCO9uKrYeXFiHsbimRZzU3t6EoTa8MVvA,1118
scipy/optimize/lbfgsb.py,sha256=9bkq6iN1Gx6yPu-VE0K7bIMm1vsDhoccQf9waNmc7vQ,929
scipy/optimize/linesearch.py,sha256=oYmcsZxSYrEH5XDI_kIbeVywN-yVHZbsJuDaOmCndUQ,1007
scipy/optimize/minpack.py,sha256=tjMKdQWY6z4mQQ5G7nwy8i4eXWJjPSqfqMvfIuQntqU,1277
scipy/optimize/minpack2.py,sha256=oFSeWNLqI8ca-Aa0Kk5F0DMdNUjHdryvPLhtPo_k83o,769
scipy/optimize/moduleTNC.py,sha256=E43jvlDbe0G4glHXWRC8GsrTdVLIaPxVMP90Ir6U6gU,746
scipy/optimize/nonlin.py,sha256=9z4Q0LQ6mbuQBozfw98N9FgTvoOKeIPdDhf7nU7lOYY,1418
scipy/optimize/optimize.py,sha256=tJkFXkADd5aHUyaIMIIMJMKHX5UzCjhXFH-SGsj8wtA,1524
scipy/optimize/slsqp.py,sha256=b6vja9q2sl50Kmv-VxsMkj3bNcariO8IBL3Q1KRMhrc,1044
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-310.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-310.pyc,,
scipy/optimize/tests/test__basinhopping.py,sha256=QrDpRjbRnxgIDevxSovYFjC1UUrEr7g-goyzJHcFZms,18897
scipy/optimize/tests/test__differential_evolution.py,sha256=1ufXg3xzSfUoAjmQx7C2XtuSizZScdvFTAchEFLJyHw,61776
scipy/optimize/tests/test__dual_annealing.py,sha256=syotN4J2XhMSdTZaC95mlBRvzkh3Lce3mGtG05nH8dU,15173
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=lKN2ar1ORciBUO7r2J_ubPFYVy4vl6jxF3TYqaklQsY,11422
scipy/optimize/tests/test__numdiff.py,sha256=GvbNPMNBYV-JxbAHUBlgFBXzfZmf6RqypIq2zRDxpRw,31338
scipy/optimize/tests/test__remove_redundancy.py,sha256=gwakPkJo8Y8aRL4son1bp8USfwc9uMrLLnZFrDmfvxY,6799
scipy/optimize/tests/test__root.py,sha256=M0c-uzzERVQ4pR753qR1XwqYL2QXqZScr3gjnarSTV0,3727
scipy/optimize/tests/test__shgo.py,sha256=HM2zVlZrYryoYrjaP6uMxuEHZFM-XK-5b8c_3tw8iwE,40298
scipy/optimize/tests/test__spectral.py,sha256=M3HgWWxSc7uWnMCKVv0wfuyqJUymstZEnN-6lvMUyvs,6597
scipy/optimize/tests/test_cobyla.py,sha256=GWNgISLyZaaZMMBvxqI9fcGl5XOKwmfL4126dnX-RCE,5152
scipy/optimize/tests/test_constraint_conversion.py,sha256=vp-PUJNne1gnnvutl9mujO7HxnVcSMf5Ix3ti3AwDTI,11887
scipy/optimize/tests/test_constraints.py,sha256=YqI-ZRfE8DaogcUIkxv1Sn9to8TbE1S7Sd4u6gnwlDU,9402
scipy/optimize/tests/test_cython_optimize.py,sha256=n-HccBWoUmmBWq_OsNrAVnt4QrdssIYm4PWG29Ocias,2638
scipy/optimize/tests/test_differentiable_functions.py,sha256=KoU2GotR94yJgb0Pf4pDgKrwNNDP0X_NSd7HbmiHLFw,26154
scipy/optimize/tests/test_direct.py,sha256=dUfsmTx9phFmlwv93UYgjYBoHh-iuWUrdc_KBn7jGlY,13152
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=zwfXj6jxvihE0vXU5pBeOyERCpimSZu6gXSLcm-FRfo,10112
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=rpJbiCUfgJrjp-xVe4JiXjVNe6-l8-s8uPqzKROgmJQ,1137
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=44caMVc_OSIthB1SLFPK-k2m0mMWxN4pMiJ-cDnqnLU,3599
scipy/optimize/tests/test_least_squares.py,sha256=11hftsUpk9W8BrzrvUrZalTy0RQs6iQMz1Mr1UyUD5I,33858
scipy/optimize/tests/test_linear_assignment.py,sha256=84d4YHCf9RzjYDKUujQe2GbudkP8dtlSpZtMBwCf_Oc,4085
scipy/optimize/tests/test_linesearch.py,sha256=n7_m-TgE7uE1_4gSRh0nr6IUm2eVHl_lPLQQ12ZZFxw,10896
scipy/optimize/tests/test_linprog.py,sha256=eKUCala0JvSqawF_ilLjwDIYlu_a1hSOl6unIw5-Ouw,96628
scipy/optimize/tests/test_lsq_common.py,sha256=alCLPPQB4mrxLIAo_rn7eg9xrCEH7DerNBozSimOQRA,9500
scipy/optimize/tests/test_lsq_linear.py,sha256=VNDaly3QvDxujzvSp6hi9pyTR-3ubU8LPZEiVyTxfC4,10838
scipy/optimize/tests/test_milp.py,sha256=RDJe1CiL8-UMD8xqe4n2aVWp8qBe1hYufRx8qvad4wU,14553
scipy/optimize/tests/test_minimize_constrained.py,sha256=iH36VYNLN7AEQZRvLoRl7nhfv1R19oh7RIkmJJlpVKM,26476
scipy/optimize/tests/test_minpack.py,sha256=K1B4hT5it0-koP207KM9qcxB3-U-RB0anitUYnfr79A,40676
scipy/optimize/tests/test_nnls.py,sha256=VHlSu-AYWWgONgkRI7oGPXNzd5XuoLu4wRhp4dyAL9M,914
scipy/optimize/tests/test_nonlin.py,sha256=QDkMLp6nJPR36dNcKYCffSHP9cSBCt9ws21pA1_vakk,16945
scipy/optimize/tests/test_optimize.py,sha256=m6kbNOGF1UC6_frP3ZFvoyAj2OpCYZ1U1LMLVy9QaZk,119295
scipy/optimize/tests/test_quadratic_assignment.py,sha256=iZ6wJDGx4T0lDM2N5mjQhXwwTdIGGWrgCCGtLiMOC14,16309
scipy/optimize/tests/test_regression.py,sha256=CSg8X-hq6-6jW8vki6aVfEFYRUGTWOg58silM1XNXbU,1077
scipy/optimize/tests/test_slsqp.py,sha256=dhkRsHqAHPmn2j4DW9xdyBdNEuwhhaccmK-kkTAnp3I,23260
scipy/optimize/tests/test_tnc.py,sha256=ahSwu8F1tUcPV09l1MsbacUXXi1avQHzQNniYhZRf4s,12700
scipy/optimize/tests/test_trustregion.py,sha256=HJtCc8Gdjznkzyn7Ei3XByBM_10pqv7VXgXBR9kCc8k,4701
scipy/optimize/tests/test_trustregion_exact.py,sha256=lJ0RXXFvgqbDfWrjzm-6H1PNKHDLXPVEbDltbRHezNQ,12954
scipy/optimize/tests/test_trustregion_krylov.py,sha256=K90fBdvxYKgsdl_lvopRf28nfcBN1CgrR-N2zjVXvhQ,6587
scipy/optimize/tests/test_zeros.py,sha256=taGg7yN7QuwmrgKXr-cV3w5JdkSvHBU852PO4-H5jYg,34983
scipy/optimize/tnc.py,sha256=7HKQvI0end6nabnkAAtVcX8jMrvSCWi8CD-tBShfHkk,1148
scipy/optimize/zeros.py,sha256=ybE9F-jqrlzpGrXW9DLGluOkWjPqlNJGmAyJyv0qIBY,1008
scipy/signal/__init__.py,sha256=nnrEmfwPp1SDV4o1F_HrP118p7IeeKGbiTuAmoAJbbo,15901
scipy/signal/__pycache__/__init__.cpython-310.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-310.pyc,,
scipy/signal/__pycache__/_bsplines.cpython-310.pyc,,
scipy/signal/__pycache__/_czt.cpython-310.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-310.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-310.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-310.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-310.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-310.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-310.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-310.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-310.pyc,,
scipy/signal/__pycache__/_spectral.cpython-310.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-310.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-310.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-310.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-310.pyc,,
scipy/signal/__pycache__/bsplines.cpython-310.pyc,,
scipy/signal/__pycache__/filter_design.cpython-310.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-310.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-310.pyc,,
scipy/signal/__pycache__/ltisys.cpython-310.pyc,,
scipy/signal/__pycache__/signaltools.cpython-310.pyc,,
scipy/signal/__pycache__/spectral.cpython-310.pyc,,
scipy/signal/__pycache__/spline.cpython-310.pyc,,
scipy/signal/__pycache__/waveforms.cpython-310.pyc,,
scipy/signal/__pycache__/wavelets.cpython-310.pyc,,
scipy/signal/_arraytools.py,sha256=qHqX1pgjguFawwag8J81ZEQMAa2J64FBUG7ihSGGBWQ,7489
scipy/signal/_bsplines.py,sha256=c7JtN4aZCcxioHxu9qY9nbiJ0Vp1DHMgzRmV2QJsWPE,21248
scipy/signal/_czt.py,sha256=t5P1kRCM3iw3eCaL9hTgctMfQKezkqnjbghLjCkffQE,19445
scipy/signal/_filter_design.py,sha256=xkErB0IlUHFhWcsaw2olqxF0ow9EBFFsaUek1KGf_2g,185520
scipy/signal/_fir_filter_design.py,sha256=4tgCwGQTwrV58KdOfNH8EpklxQlpelkh3hFT4J0rzM8,49059
scipy/signal/_lti_conversion.py,sha256=P3v4T4O01N2E5oQEQVwF9rae17YJNjWpAHxZFx3ivdA,16130
scipy/signal/_ltisys.py,sha256=fUfLI2DgRIZJtKkOL2f1BlOAg8GmkxdbYzFFb4jA0Mo,130973
scipy/signal/_max_len_seq.py,sha256=FSOVHmSTQqBpUV3ThijyNdHYHNN7mwaTUjoDDn9m3eQ,5062
scipy/signal/_max_len_seq_inner.cpython-310-x86_64-linux-gnu.so,sha256=vtJT9QhWXD2ewcxk-P6t3DcG7Oy8RPPKcVs-LL3cv6I,77736
scipy/signal/_peak_finding.py,sha256=W2v4ZNjtXdRj-J6VS3KXRrr42ama-PhokFoKunAfDew,48807
scipy/signal/_peak_finding_utils.cpython-310-x86_64-linux-gnu.so,sha256=0My-l35zlfk7RZAv6HGXj9jaAMczWSLRUkzxiOLK9aI,279280
scipy/signal/_savitzky_golay.py,sha256=mnltOfknWRlNiZmNLLy-zKTCrw6nZSdJPEvpGi0kv8E,13417
scipy/signal/_signaltools.py,sha256=_O8Y_GRxsFSUmbxRtJ3faREaaSIbMr16E4nXiVQmrv0,157332
scipy/signal/_sigtools.cpython-310-x86_64-linux-gnu.so,sha256=_YYr9PI5KD62-lxkgVPgUMRcoeEFYG3VmIdwJ7FEWIc,108880
scipy/signal/_sosfilt.cpython-310-x86_64-linux-gnu.so,sha256=i9zy_RgrkCJvZm3-oWZZ7CoA9kfMyaerMK0JOTfRv3k,280912
scipy/signal/_spectral.cpython-310-x86_64-linux-gnu.so,sha256=ybbAalks_lv9qjt_6bNlHiLYfjBxIk1wF8kRKchvsyM,78024
scipy/signal/_spectral.py,sha256=tWz_fFeYGjfkpQLNmTlKR7RVkOqUsG_jkjzzisLN_9M,1940
scipy/signal/_spectral_py.py,sha256=2KFtWVeqRO07tHaqv6xKkq6ejgMxswHV31Bi4zp9WZM,76658
scipy/signal/_spline.cpython-310-x86_64-linux-gnu.so,sha256=keYIigRtikPRoqQEsvE8SDtv23MN9sKVoZPRv86m_Vk,85144
scipy/signal/_upfirdn.py,sha256=WsElY_Gj9RBlR8pMBqJmAU0Za-BR_Jy1SrTzKDJI5LE,7884
scipy/signal/_upfirdn_apply.cpython-310-x86_64-linux-gnu.so,sha256=2FJqv2Wzb_x-4gNmbr6nBLivceSqsglRh9ecq2PsrPk,356048
scipy/signal/_waveforms.py,sha256=Bm5WOBhk1nXwK0A6yFVTY7tCCv6trdrUjje_xmM878Y,20523
scipy/signal/_wavelets.py,sha256=KaPI52BvS4S-DVM3kcNT8LMni5MAa5f-TYdb3q1LXOI,14132
scipy/signal/bsplines.py,sha256=RFFNZHHyfJ1WEbdgboPvqV_rif6ZpP2XcQY6yAZFMvs,1085
scipy/signal/filter_design.py,sha256=eyU6-xkaC6gpGec_KU899jWj_J7NyHavnmH6ayjSNPE,1719
scipy/signal/fir_filter_design.py,sha256=4YYa4xY42pwC_ROuj_GyuWRcV-qJk9-3mWGQJxNWha8,1003
scipy/signal/lti_conversion.py,sha256=NLMKn39KEc0te0VpuQ8pi0ABrwq6T20JR9JQX_8K7NU,936
scipy/signal/ltisys.py,sha256=6VEgR9mC1lrVrCiMUgSOnM39TxdpkKTb5Ouw9Xe0m0o,1470
scipy/signal/signaltools.py,sha256=Ul0U0FNf6G3ifaxVu-nx66hW1JWy6bW_F2SxdNg-ME4,1401
scipy/signal/spectral.py,sha256=AGqvyefESNmSpYkZFBKr2gu5dMvNergOOxxZjvunrL0,944
scipy/signal/spline.py,sha256=iisoUmgbyuuEukQjBz99HM3SYao7j1ZsXXmtE-wo5cU,810
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-310.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-310.pyc,,
scipy/signal/tests/mpsig.py,sha256=DHB3eHB0KYA-E0SBebKG36YLk-T5egbwwryne3RwIHM,3308
scipy/signal/tests/test_array_tools.py,sha256=J9Mr5DtqmhiTReWvsk3YclL6Cnv32bDuklBnw2zprJY,3632
scipy/signal/tests/test_bsplines.py,sha256=v9btwrQ4FQ21RVx2K_P76Llabc4WImtuJesAy6PyNPg,10621
scipy/signal/tests/test_cont2discrete.py,sha256=3IkRfgGlgnX7X0bERpExPAxAkcGK0h6Ovy6GyrhnYS8,14605
scipy/signal/tests/test_czt.py,sha256=3HxxWwOWIrIc0GC-K5h6f0NRjkLrWRA5OhoB5y0zbw0,6993
scipy/signal/tests/test_dltisys.py,sha256=f4wDe0rF_FATRWHkHddbPDOsFGV-Kv2Unz8QeOUUs-k,21558
scipy/signal/tests/test_filter_design.py,sha256=MWLxrt1Y21usNnPm3JE05rbFHqtw7scJ2IhFgsAqXwE,188606
scipy/signal/tests/test_fir_filter_design.py,sha256=mG_6Bo1NHN9Gj2LAzGHwWKqlcVwYSMic6FojcSLiIC0,28932
scipy/signal/tests/test_ltisys.py,sha256=RemLvIX-PgAJs9GWNFw6UkMqtVOEztSqAP-P6yPsVHQ,48251
scipy/signal/tests/test_max_len_seq.py,sha256=X9oyCvW0Ny8hOAVX22HmKaMgi2oioe1cZWO3PTgPOgw,3106
scipy/signal/tests/test_peak_finding.py,sha256=ckPd0IqoaRcdCg8yJ2TzXdU1kWZPIEHw0cLdEC_VIlI,33667
scipy/signal/tests/test_result_type.py,sha256=25ha15iRfFZxy3nDODyOuvaWequyBpA42YNiiU43iAc,1627
scipy/signal/tests/test_savitzky_golay.py,sha256=hMD2YqRw3WypwzVQlHwAwa3s6yJHiujXd_Ccspk1yNs,12424
scipy/signal/tests/test_signaltools.py,sha256=G0RcF5dKdmBnUrXAgiK8KPZ_VXbjJasPJH2xpQagrsE,141300
scipy/signal/tests/test_spectral.py,sha256=HbW4i_DhuxCkewNo74zm-yzsbA4SYJL64w9WPT5U1_Q,59347
scipy/signal/tests/test_upfirdn.py,sha256=i3EjQKnwS6FRRRPPzwl1B_zWsQ20Dfa_6WUUYH8I3xM,11240
scipy/signal/tests/test_waveforms.py,sha256=sTT0DeOER5U9h8Xp54VGvGlbtcxhp_wjGNQXw1yOaGM,11975
scipy/signal/tests/test_wavelets.py,sha256=hzgIa5Bfzqc5uWVCIFmMUOEF87gBNSMGj0ZpML_3m0I,5977
scipy/signal/tests/test_windows.py,sha256=VbLvcVrMCSE-Ed2Y8GZ8gfAODNAuhftZpdLc0u3JSJw,41738
scipy/signal/waveforms.py,sha256=hHOTVCfrIOMD95n5v_jET4nJVTpB68SyMhnSraPTPhQ,890
scipy/signal/wavelets.py,sha256=Xkoj6JZqZKRb0CSB_BDQRclk-gMEJFhwqPY8PgRRk4U,828
scipy/signal/windows/__init__.py,sha256=BUSXzc_D5Agp59RacDdG6EE9QjkXXtlcfQrTop_IJwo,2119
scipy/signal/windows/__pycache__/__init__.cpython-310.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-310.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-310.pyc,,
scipy/signal/windows/_windows.py,sha256=6cFhKOVs4Ddpo5JKP3n9zJqep4wilGEpudO9CcXFXGE,83617
scipy/signal/windows/windows.py,sha256=a08un2az27LnmEwYno88Wwo4-yQaCUK8DogsOAcZwlE,1117
scipy/sparse/__init__.py,sha256=b4vZKKuuGEh_mAW8fhLgqg6h0t68j3a57sLIuHclE8A,8659
scipy/sparse/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/__pycache__/_base.cpython-310.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-310.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-310.pyc,,
scipy/sparse/__pycache__/_construct.cpython-310.pyc,,
scipy/sparse/__pycache__/_coo.cpython-310.pyc,,
scipy/sparse/__pycache__/_csc.cpython-310.pyc,,
scipy/sparse/__pycache__/_csr.cpython-310.pyc,,
scipy/sparse/__pycache__/_data.cpython-310.pyc,,
scipy/sparse/__pycache__/_dia.cpython-310.pyc,,
scipy/sparse/__pycache__/_dok.cpython-310.pyc,,
scipy/sparse/__pycache__/_extract.cpython-310.pyc,,
scipy/sparse/__pycache__/_index.cpython-310.pyc,,
scipy/sparse/__pycache__/_lil.cpython-310.pyc,,
scipy/sparse/__pycache__/_matrix.cpython-310.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-310.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-310.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-310.pyc,,
scipy/sparse/__pycache__/base.cpython-310.pyc,,
scipy/sparse/__pycache__/bsr.cpython-310.pyc,,
scipy/sparse/__pycache__/compressed.cpython-310.pyc,,
scipy/sparse/__pycache__/construct.cpython-310.pyc,,
scipy/sparse/__pycache__/coo.cpython-310.pyc,,
scipy/sparse/__pycache__/csc.cpython-310.pyc,,
scipy/sparse/__pycache__/csr.cpython-310.pyc,,
scipy/sparse/__pycache__/data.cpython-310.pyc,,
scipy/sparse/__pycache__/dia.cpython-310.pyc,,
scipy/sparse/__pycache__/dok.cpython-310.pyc,,
scipy/sparse/__pycache__/extract.cpython-310.pyc,,
scipy/sparse/__pycache__/lil.cpython-310.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-310.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-310.pyc,,
scipy/sparse/__pycache__/sputils.cpython-310.pyc,,
scipy/sparse/_base.py,sha256=jlXStvxScnDD9NOBP3wGeKXz2W345_IvGYyfGCxeZqo,50003
scipy/sparse/_bsr.py,sha256=ydsFexvnabxzV-fDlU4Zo0ByJV9VQKhrhe8ZDm_OX04,25782
scipy/sparse/_compressed.py,sha256=nrutJKAQWoBInzphfRI5HC4k-xeNBgRc1LCacll5CU0,51107
scipy/sparse/_construct.py,sha256=8Lp6KF90Sidj_zfcbhnt-frv8CU05tf-x8m6nYblLrU,30305
scipy/sparse/_coo.py,sha256=3bopWdvlqJezyEihE-6BqutUmt3Gf3LfbneTtOEs6f4,23095
scipy/sparse/_csc.py,sha256=sHyBEhOKs692GaucEiTvw1vTSPCTTXgybMQ3sAJd3P4,8297
scipy/sparse/_csparsetools.cpython-310-x86_64-linux-gnu.so,sha256=Z0Qsaysu0a8KcW2SFenJh8YZD4krguQc_fLAaNftc3M,662168
scipy/sparse/_csr.py,sha256=ISvU9Lbuzu4M_hEyCti1YbTzY2qjENPB3_MKIPmU2eE,12064
scipy/sparse/_data.py,sha256=D_KLNzshcP0afommcWFQTqtfy8r7XImRRuufTXoiApA,16957
scipy/sparse/_dia.py,sha256=v9WLBy8tzmCw0S77uoI8EVql_xdtPFa5D1SFFHRRV2A,16661
scipy/sparse/_dok.py,sha256=bN4TRHjVphhHWZpcLSYN81NAMyTM6HFcDrTyDUza0nU,16352
scipy/sparse/_extract.py,sha256=Pz2B8VAcBZod80FM7ssBhjs6QNMNlZOkjNg9pjgv36I,4648
scipy/sparse/_index.py,sha256=Y1Sdc87wfzUDcTRQfK6WYh2oT59dk-bOLvmNF99QWOk,12931
scipy/sparse/_lil.py,sha256=ZKgiUn0k188HzQI3SJKCnaU9SeerPikZaWkxq4ZO3kk,18670
scipy/sparse/_matrix.py,sha256=UP7wrH10_EtI25mlbmqoXzBsUgxUQ1y8m_3hsC94L_4,4036
scipy/sparse/_matrix_io.py,sha256=ufR60496NB4jf5sP5To3mS3MoZwlLD1lqXUZkSsNt6A,5347
scipy/sparse/_sparsetools.cpython-310-x86_64-linux-gnu.so,sha256=LuLiU84za3BbJiaMLzO1qTO5frRZ-2aJM-42WXRo8H0,4490976
scipy/sparse/_spfuncs.py,sha256=lDVTp6CiQIuMxTfSzOi3-k6p97ayXJxdKPTf7j_4GWc,1987
scipy/sparse/_sputils.py,sha256=9-hS-OkQ44ecnf0qg_441rnUpyO4ekB7L8AFeDcq3Hc,13023
scipy/sparse/base.py,sha256=qxhdhbOyBKTVIhO4s5kFAZEcm7QhKBzDHBzRYUCNO9I,1016
scipy/sparse/bsr.py,sha256=Ci4qlM-aGqtmHJD3RZgAwO0iuA7ziSnrdyfCUhjP3RE,1058
scipy/sparse/compressed.py,sha256=aWYREJ4E9KXgqmk5-yivQx88HMNcFNRABmtomE2vUOk,1286
scipy/sparse/construct.py,sha256=RdZSkoRKiwLOMiJxAzNOMCTTK5ZyW_m7cFkBFOa6MLk,1158
scipy/sparse/coo.py,sha256=lZhO4RChFbyJEt9AYjQ49JMiILNIwDIku2TuW2NtdJQ,1091
scipy/sparse/csc.py,sha256=CltVpoEbj8MM4iyNqsyLv7y2K_PUM9gFLhMiXvH1-KU,838
scipy/sparse/csgraph/__init__.py,sha256=UzgDvD2sNSRqbF7YNIeg9UgIuJdl3huUVfCogfiy3c0,7739
scipy/sparse/csgraph/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-310.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-310.pyc,,
scipy/sparse/csgraph/__pycache__/setup.cpython-310.pyc,,
scipy/sparse/csgraph/_flow.cpython-310-x86_64-linux-gnu.so,sha256=9PbWVI8Yu6-Pv-VDaCEXQ8lLT7qAYPwBShtcJsNDN_8,331872
scipy/sparse/csgraph/_laplacian.py,sha256=J4R2z0LcX6Y81IUxWtra3r8KJl1qtlj6GvwGrOgI1N4,17863
scipy/sparse/csgraph/_matching.cpython-310-x86_64-linux-gnu.so,sha256=YNgqV5UN7dvWs3tF8e-pP8nYHfbIpoDk8m11MLAixvs,328184
scipy/sparse/csgraph/_min_spanning_tree.cpython-310-x86_64-linux-gnu.so,sha256=GQrFk7eoc54XSKJa0d4KjOqo_juVXf3UUMdFPz9L1Rg,236176
scipy/sparse/csgraph/_reordering.cpython-310-x86_64-linux-gnu.so,sha256=3fbf7rkyGvDyF11G9Otg5IQpdoXnfOvh1YrAOFUCPQQ,307896
scipy/sparse/csgraph/_shortest_path.cpython-310-x86_64-linux-gnu.so,sha256=FnZ5HlyXh3gGeJuzyoCRYDFGWiMd9KVXQ5WKR8AxAKw,480160
scipy/sparse/csgraph/_tools.cpython-310-x86_64-linux-gnu.so,sha256=OKgNGN1OSo7JwApA6JN0y5Y798pQJUgoNOShv8c-xI0,193952
scipy/sparse/csgraph/_traversal.cpython-310-x86_64-linux-gnu.so,sha256=D7hFixtKkbdBp0LGZCJK5reNMx0LZOnAYx9uMdm2Jyg,583688
scipy/sparse/csgraph/_validation.py,sha256=3oleuC9D22ilrU5Oz8Os3GlsjfRPx-kAN3izkLGWdFE,2329
scipy/sparse/csgraph/setup.py,sha256=IsCLuoi1_g1Rh0UhpD8USX3yjtOj53y40XodHc8-cEM,1098
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-310.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-310.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=a2HZjm7HsC0STqiDnhN6OJL4yIMcM28VNVtMXDI2BqE,3948
scipy/sparse/csgraph/tests/test_conversions.py,sha256=Y48qwFRsE4tTxFYS_Bn8ndCkAwe8n0rovbaVYppCy34,1855
scipy/sparse/csgraph/tests/test_flow.py,sha256=BXhx0qBT3Ijy9all5OhNVNVzMbdTPySQuaZ1ajK6DTs,7420
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=DmqlkSwK73oSaml5Trfr8qZaYi5RztBtx9xP-GuNj-0,10943
scipy/sparse/csgraph/tests/test_matching.py,sha256=MkSKU_9_IIhRnhp5sbRbB8RYqVe_keS4xqhDVvV3EhM,11944
scipy/sparse/csgraph/tests/test_reordering.py,sha256=by-44sshHL-yaYE23lDp1EqnG-72MRbExi_HYSMJEz8,2613
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=RmRAk_RxMo3C9do0f01DsHSPyDUVEUZXuq4h6aALrDo,14441
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=uyOB_TB8E1O2JFDuB16_r3kw7fizTpKy5ce81AT9XP8,2115
scipy/sparse/csgraph/tests/test_traversal.py,sha256=UNTZXJ9bjDHcji_vUa1Ye5Kbp6xLfyHBG9LusToGUSY,2840
scipy/sparse/csr.py,sha256=cmPYY83pa6OwO19bquQiRi4BpVkUa-uHT5yFoCWROS4,887
scipy/sparse/data.py,sha256=dOqfmIpX9TfoosFAbq18WfFWfz10ai1a9-yhDrgvocQ,811
scipy/sparse/dia.py,sha256=UjBrPBeMEoIShw-qEEEK5pCLRHxJk2wu8Eztw5ohxXE,936
scipy/sparse/dok.py,sha256=5zAGkQHTx7ZOKaPcunLitFoROb4q4gyH48bva-Bg13A,980
scipy/sparse/extract.py,sha256=O-kmKGLk118RQfbUnvo_jFUd18bxgM32oww_5DSMTfI,781
scipy/sparse/lil.py,sha256=mWSsX2-CEsJL1DkRIghTf9GnEE8jaJ-gXVQ8-MMNlK4,981
scipy/sparse/linalg/__init__.py,sha256=UTXDqJ3GiSh9tU5dSO9GhUmY7xwh4R4zBzdKkTq0cQ8,3717
scipy/sparse/linalg/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-310.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-310.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=mB_3u89ASCCQA48XGBS3bwRj2agYvgTuIJ0tnLnJly0,1991
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-310.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-310.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=Sjol-MfXrIch0chc7T5TeCVxaJowfFqJnsBlGnX8DZ8,3795
scipy/sparse/linalg/_dsolve/_superlu.cpython-310-x86_64-linux-gnu.so,sha256=QJkHPTPpQHz0Yi1A_ZWrro9HYSQyBtCsET5q9re0Ti0,378921
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=Z3y_0U8TC9HnVZ8e-Z5M5QMokbCMM4y3F-LntGVZ7pQ,26029
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-310.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=m7E-i7MzX8dfdgxjV9x0H2sR_2N5cDBGLPAlvJ5i9m8,27633
scipy/sparse/linalg/_eigen/__init__.py,sha256=SwNho3iWZu_lJvcdSomA5cQdcDU8gocKbmRnm6Bf9-0,460
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=xZ1-wZSv5UYvGtR8f54Zwjsin7RFLJO08ZiSXihyHV4,20684
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=IV5s9jBFco0cddIPBQnXwdzx05D-R6IkPcZ3tmGGt1A,15524
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=CSZWb59AYXjRIU-Mx5bhZrEhPdfAXgxbRhqLisnlC74,1892
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=zDxf9LokyPitn3_0d-PUXoBCh6tWK0eUSvsAj6nkXI0,562
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cpython-310-x86_64-linux-gnu.so,sha256=H9livIo_PhrUYGHB6wFSQnJL8uFn_bDKrMVzW0nE-xI,486385
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=aq9vtOFhp8vPLd9m_-6KPn2yKpMTIdP2XIgG0PCZqVo,67272
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=-Yah0pUf8RB97uMPfjwAKhN6b-HXcQcxEt-JC0DJax0,23726
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=E5JEPRoVz-TaLrj_rPm5LP3jCwei4XD-RxbcxYwf5lM,420
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=CHIqK9vYoCmTf853WSMNEftlEYc-RJMEfFNK8YRzGWg,40882
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=CFOzzHRXvDECKzWR2utY1FntLjO0HUWLCEJJP6xgSRo,23518
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-310.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=3s_t66L4QJWqiu91l1Ca7xXFQrgKxz5qOvawd7xobY4,37553
scipy/sparse/linalg/_expm_multiply.py,sha256=gIr3suR0FTaYkieNFPS465XUNx1dIorWR-8nVSXba14,26296
scipy/sparse/linalg/_interface.py,sha256=LxJmwUncfqcSkTQKX16cC5tN98YnG2cDnZ8ZM8DREn4,27845
scipy/sparse/linalg/_isolve/__init__.py,sha256=Z_eQUYbe6RWMSNi09T9TfPEWm8RsVxcIKYAlihM-U-c,479
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=DiniPjOJFjRq8CJdZIpidGPxGmYLGx-61OREX_lKA3A,15984
scipy/sparse/linalg/_isolve/_iterative.cpython-310-x86_64-linux-gnu.so,sha256=lQLD0w67Ly-SQPxwSaX2XKvsznMrd9lITpWJm_Xt1wE,276537
scipy/sparse/linalg/_isolve/iterative.py,sha256=c87IWxbhvmWj_S0sp4QKlvMQtAPofw7e9sMYG9oX-EQ,30590
scipy/sparse/linalg/_isolve/lgmres.py,sha256=HXHikhzZRBGJnp775MlwLbteR05C4A2KypTB4O0-kZQ,8932
scipy/sparse/linalg/_isolve/lsmr.py,sha256=XVTLvHkEPe2sZPrIw6rGEkubdAoRRr8pdp9Estx9rio,15657
scipy/sparse/linalg/_isolve/lsqr.py,sha256=mJADMPk_aL_lf57tkaTydK4lYhkszmHf2-4jHJEe8Vs,21214
scipy/sparse/linalg/_isolve/minres.py,sha256=5VHjn7dk-OJbyMbO_jd7z7-o853SWJQEYQAD8K999LI,10878
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-310.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=7vJGIsxohY1QHMzNkQQ8T6PGNmleQUqUCntj-hdaNng,5408
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=pi8UkXm6Fi-PWo020I8K8eS2hnBYIXA3X7Bn297EH_4,26438
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=4I7jokIZTWBq_Zbd49JaK2QnfLUF2lYGCecWGoqHtLw,7060
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=YHEup3ioVWRIyY-1zWUEjwjMx5QEQ-i6Llpepe35xX8,6366
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=wDYpcLudHJA2YwhKWnYnFcvcvVdLIp40ni5TKN_hUUg,3754
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=17a2ezMO2OXKtruk_Rp9-e7QtGaXZ5h2sUUJz67JwHg,2446
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=whURuUHl3jyNnsS-QgHSfDe342LBTwf3C_JbK7q_Ft4,247
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=blnP76yRYJuYTkAYntQoZUFEqydaXVxjpzXDC_PBHf0,6241
scipy/sparse/linalg/_isolve/utils.py,sha256=I-Fjco_b83YKUtZPVdobTjPyY41-2SHruVvKZVOIXaU,3598
scipy/sparse/linalg/_matfuncs.py,sha256=knO6j91Z88KFHgYnFOzDVFxmeo1J82vIdq3amOtsdVk,27210
scipy/sparse/linalg/_norm.py,sha256=YRUM-eEnJ2D_8pvaz4LEeiV4MFaWHrO2jx546dw_KQ8,6062
scipy/sparse/linalg/_onenormest.py,sha256=8Yxe16ox-G9UV41iMN4yfGb_1JQxENeq2YhN8Icwg5M,15486
scipy/sparse/linalg/_propack/_cpropack.cpython-310-x86_64-linux-gnu.so,sha256=zxTKKBpQmCsMSAKEVGaqyQRl0rb1KqnaeijeUNM5EwU,158217
scipy/sparse/linalg/_propack/_dpropack.cpython-310-x86_64-linux-gnu.so,sha256=cX4afCOoqPDRZ_xt2YZ9xLLThDnzmcc4MpqHu6DnVug,133601
scipy/sparse/linalg/_propack/_spropack.cpython-310-x86_64-linux-gnu.so,sha256=RCvtYuBtbXJZVIgHQeEb4_z2N-YbV_9PMkyzynYLF3w,129505
scipy/sparse/linalg/_propack/_zpropack.cpython-310-x86_64-linux-gnu.so,sha256=h7iHi3-zpgF4OtegazrRwcagpaMKCQMmSZdCr2RDaGU,150025
scipy/sparse/linalg/_svdp.py,sha256=LKGK5i0PMvRejN82HHRsOgFsqHhAsDDudsdfr0Y8lDQ,11685
scipy/sparse/linalg/dsolve.py,sha256=s0PkMvkadWLa87Zi84K4fO3S82RyuOqA6xy1ZPaQEcs,1203
scipy/sparse/linalg/eigen.py,sha256=onUc3vZGnS8jtajnkSvYxbXA9kq4KG1Djv1EqOkuvmw,1151
scipy/sparse/linalg/interface.py,sha256=JyH79SJ72jqeE05MgNhNxXI8S-fucoR6B4d1yc448DU,935
scipy/sparse/linalg/isolve.py,sha256=QAcHU8MkRlKU0ZJbHEc_H99HCSY6XKop3bF7bydcg54,904
scipy/sparse/linalg/matfuncs.py,sha256=WgjTo4WEUMVlMoZK6iQ3C-R1bDQ6cNZRbTR1LHV1VdY,948
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-310.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-310.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-310.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-310.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-310.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-310.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-310.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=pDCHrcxhGvknCrsInqi_Y37Bl4FrDVsqWwNyRJEQzG0,13919
scipy/sparse/linalg/tests/test_interface.py,sha256=L9eWNHvqmfG1OU903QNFYnl9SmLsFJLYbt68Uth3Q8M,17943
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=67tWWI3Y3vGIa-w4k-DNr9NHw8QozESxqeBhKPneWT0,21280
scipy/sparse/linalg/tests/test_norm.py,sha256=8waDQ-csiw4jTIQPz8qlseqgosvjY9OHfAU7lJ8yLxo,6163
scipy/sparse/linalg/tests/test_onenormest.py,sha256=EYUVD6i7RGiMi_bclm1_4YkLZSAma5CHqRH9YeDvtwM,9227
scipy/sparse/linalg/tests/test_propack.py,sha256=6CL7xhQqPdAd1DGduqx0fmeo6NNn6anT5te3rl_yMkw,6284
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=MNBaBg4m-fnRrv4BHIPiyxsHGdRuU6iV_UphO7a2IbM,6124
scipy/sparse/sparsetools.py,sha256=pe8yKLT3FTs7C2d3ZB6V8sZRkMbp0KKEH_teY_mks3E,2390
scipy/sparse/spfuncs.py,sha256=-L313g_Rr1j-Gy8dqgKetxQFDGKYJu6P53l6CrYIWqg,842
scipy/sparse/sputils.py,sha256=rMARLPcXcI1v00eEt5bOCOI9uEh-kk7pxEpbQ_ijcNM,1187
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_deprecations.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-310.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-310.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_array_api.py,sha256=WfYgsVNGvVVCUR8sn_-9VmVKkiexLdBK5tZiik8o-Ts,13499
scipy/sparse/tests/test_base.py,sha256=PHqAN5g6-zjaeygyiNIB1wEu-5xu9cUx4wrfRS0L9Do,187224
scipy/sparse/tests/test_construct.py,sha256=sgZCU_nQqMUe7NqgB0uRq8WDPfynUi6Mh1T7tiLvmVw,24889
scipy/sparse/tests/test_csc.py,sha256=5JW9c3EiAPI_lgPwKXwHtx3yYPtAn9fskbSYAcoNVEw,2902
scipy/sparse/tests/test_csr.py,sha256=vgQ2nH5-73Qd_ujYGIaScGv9_ErAjXHxgkJHN_eN1vQ,5651
scipy/sparse/tests/test_deprecations.py,sha256=D3vKLk3m6qb5zQaPg9MtQl_TOxYYL1jo4mW8IgqEBuc,709
scipy/sparse/tests/test_extract.py,sha256=NhizzkOSFkX_qSQi3coKIaDJKcDOvrJYew98VJlTyeU,1313
scipy/sparse/tests/test_matrix_io.py,sha256=vU0N5HkcjleHZhQlDt83bk5NsUU_NePl7rUr2zmAFA0,2542
scipy/sparse/tests/test_sparsetools.py,sha256=-Us13-fx-Ht44EV1esZumKoB9jo_uxchJ9cbfElsThg,10553
scipy/sparse/tests/test_spfuncs.py,sha256=ECs34sgYYhTBWe4hIkx357obH2lLsnJWkh7TfacjThw,3258
scipy/sparse/tests/test_sputils.py,sha256=pScRFTdbXNh1a9TBDpar4IJgdxm2IasgXdszeqmaRd4,7297
scipy/spatial/__init__.py,sha256=PpWQnxLzKYqQFTcSSTv98xe0N2TsV8BAuI4oEH7hgvo,3683
scipy/spatial/__pycache__/__init__.cpython-310.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-310.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-310.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-310.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-310.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-310.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-310.pyc,,
scipy/spatial/__pycache__/distance.cpython-310.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-310.pyc,,
scipy/spatial/__pycache__/qhull.cpython-310.pyc,,
scipy/spatial/_ckdtree.cpython-310-x86_64-linux-gnu.so,sha256=SoaJaJHOBSOPXHxGYAbqu0dTlAuMTia-23dVhY90qmQ,1005016
scipy/spatial/_ckdtree.pyi,sha256=P4x8G8kp6nAth16JeNyPQ_wsTD5SEysZI9qrQ9j8UiA,5947
scipy/spatial/_distance_pybind.cpython-310-x86_64-linux-gnu.so,sha256=zld_kwRq18SrcjRoabV8mjbpxqsCieTdjlPJfhOteMw,636584
scipy/spatial/_distance_wrap.cpython-310-x86_64-linux-gnu.so,sha256=V6hnW-Wy55SN5CDE94T5sg_Xt7ehmXmC3wBYcYhnqjo,113120
scipy/spatial/_geometric_slerp.py,sha256=Ix-OSGGMTibHipoTLzApaVTmjtoOvA5y-A75b6uaTfs,7945
scipy/spatial/_hausdorff.cpython-310-x86_64-linux-gnu.so,sha256=mHPDhDcQxtWq8i7hMuSVZcFejJ5PqA0QOZLdlHHSDec,223976
scipy/spatial/_kdtree.py,sha256=jFcpz1pozP1KGz0hRpHiHtUAkHyEEae9oOzzoYa7pzI,33444
scipy/spatial/_plotutils.py,sha256=3IO7u0bDFNa6t1uPM5hkmj9uJFzgI76wAdUDM5ZB5AM,7168
scipy/spatial/_procrustes.py,sha256=So7XHpYPIZ5hhrGQkmokNTgkiZHqlvmczIgWHi8eiEc,4427
scipy/spatial/_qhull.cpython-310-x86_64-linux-gnu.so,sha256=MfAiV0mqSLSYyQQGgxOkNf8sx2d1kkgbdaRiCWPGggM,1155377
scipy/spatial/_qhull.pyi,sha256=vmt8RTz1Z2bOx3F9YgqqK5yuLsp4rIXzH5n6MUZgtlc,5966
scipy/spatial/_spherical_voronoi.py,sha256=jlzXJsxAMBt3VW5NPLhgW4YTip2bHtbWvwXB9s5CHJI,13564
scipy/spatial/_voronoi.cpython-310-x86_64-linux-gnu.so,sha256=vvu3_6z56yTQL4JaHz2B7uL9qmC7SUn7oWL7kv_PYNg,214776
scipy/spatial/_voronoi.pyi,sha256=wNkEsvadTBTlMMW_030tAWbr7gmiGkcXdFUt4BkPMZ4,112
scipy/spatial/ckdtree.py,sha256=60uL2ynovFUeQT9NmAqPmYK_rsiT1YKwaMAW-FMeBr8,862
scipy/spatial/distance.py,sha256=w-J3l7MMAEVeROydkF1JknLcSBBTup7e2ylf_csukG4,92867
scipy/spatial/distance.pyi,sha256=muAXPiXSunWLoCEn9G6Dp0lQ8BiaRZzaAmACFhXuTe4,5274
scipy/spatial/kdtree.py,sha256=L4l9CG0wUNP8ARMUagNXzNC8vA8k134tXpAjHsI3hpg,870
scipy/spatial/qhull.py,sha256=4jL-ImgXrnmMo4zxfaNgPxE6uTAbGID_CJ22NrSWbR4,889
scipy/spatial/qhull_src/COPYING.txt,sha256=NNsMDE-TGGHXIFVcnNei4ijRKQuimvDy7oDEG7IDivs,1635
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-310.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-310.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-310.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-310.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-310.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-310.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-310.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-310.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=ULnYAgX2_AwOVF-VE7XfnW5S0pzhx7UAoocxSnXMaWs,5750
scipy/spatial/tests/data/cdist-X2.txt,sha256=_IJVjXsp3pvd8NNPNTLmVbHOrzl_RiEXz7cb86NfvZ4,11500
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=k19QSfkqhMmByqNMzwWDmM6wf5dt6whdGyfAyUO3AW0,15000
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=5Z9SMsXrtmzeUwJlVmGkrPDC_Km7nVpZIbBl7p3Hdc0,50000
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Yerj1wqIzcdyULlha-q02WBNGyS2Q5o2wAr0XVEkzis,178801
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=NEd2b-DONqUMV9f8gJ2yod17C_5fXGHHZ38PeFsXkyw,3041
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=UCWZJeMkMajbpjeG0FW60b0q-4r1geAyguNY6Chx5bM,178801
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=8Iq7cF8oMJjpqd6qsDt_mKPQK0T8Ldot2P8C5rgbGIU,3041
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=l2kEAu0Pm3OsFJsQtHf9Qdy5jnnoOu1v3MooBISnjP0,178801
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=S4GY3z-rf_BGuHmsnColMvR8KwYDyE9lqEbYT_a3Qag,3041
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=hQzzoZrmw9OXAbqkxC8eTFXtJZrbFzMgcWMLbJlOv7U,178801
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=P92Tm6Ie8xg4jGSP7k7bmFRAP5MfxtVR_KacS73a6PI,3041
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=0Sx5yL8D8pyYDXTIBZAoTiSsRpG_eJz8uD2ttVrklhU,50000
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=3-UwBM7WZa4aCgmW_ZAdRSq8KYMq2gnkIUqU73Z0OLI,178801
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=rkQA2-_d7uByKmw003lFXbXNDjHrUGBplZ8nB_TU5pk,3041
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=IAYroplsdz6n7PZ-vIMIJ4FjG9jC1OSxc3-oVJdSFDM,3041
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=Zb42SoVEnlTj_N_ndnym3_d4RNZWeHm290hTtpp_zO8,3041
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=L7STTmlRX-z-YvksmiAxEe1UoTmDnQ_lnAjZH53Szp0,172738
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=-sZUikGMWskONojs6fJIMX8VEWpviYYg4u1vipY6Bak,2818
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=N5L5CxRT5yf_vq6pFjorJ09Sr-RcnrAlH-_F3kEsyUU,178801
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=DRgzqxRtvQVzFnpFAjNC9TDNgRtk2ZRkWPyAaeOx3q4,3041
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=jz7SGKU8GuJWASH2u428QL9c-G_-8nZvOFSOUlMdCyA,178801
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=37H01o6GibccR_hKIwwbWxGX0Tuxnb-4Qc6rmDxwwUI,178801
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=YmcI7LZ6i-Wg1wjAkLVX7fmxzCj621Pc5itO3PvCm_k,3041
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=IrtJmDQliv4lDZ_UUjkZNso3EZyu7pMACxMB-rvHUj0,3041
scipy/spatial/tests/data/random-bool-data.txt,sha256=MHAQdE4hPVzgu-csVVbm1DNJ80dP7XthJ1kb2In8ImM,6000
scipy/spatial/tests/data/random-double-data.txt,sha256=GA8hYrHsTBeS864GJf0X6JRTvGlbpM8P8sJairmfnBU,75000
scipy/spatial/tests/data/random-int-data.txt,sha256=xTUbCgoT4X8nll3kXu7S9lv-eJzZtwewwm5lFepxkdQ,10266
scipy/spatial/tests/data/random-uint-data.txt,sha256=8IPpXhwglxzinL5PcK-PEqleZRlNKdx3zCVMoDklyrY,8711
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=rkVhIL1mupGuqDrw1a5QFaODzZkdoaLMbGI_DbLLTzM,480
scipy/spatial/tests/test__plotutils.py,sha256=vmDDeXOe4N2XPMeyw8Zx1T8b8bl3Nw5ZwT9uXx21JkU,1943
scipy/spatial/tests/test__procrustes.py,sha256=wmmnUHRdw_oID0YLi404IEWPH6vEGhvHXSeGPY_idHo,4974
scipy/spatial/tests/test_distance.py,sha256=Wd-SMG_gwv0YiNnd9kUbAulvbpFlnbJKxEKRGz0Zh9c,83271
scipy/spatial/tests/test_hausdorff.py,sha256=n-Qm2gVF0zc11tDSCnXBznt5Mp0E1ekTtzfWXjqG54M,7114
scipy/spatial/tests/test_kdtree.py,sha256=UME7DSUo1pAbuDfVpjF3HaIBy0X7u5HDiSbR2NQtkj8,48213
scipy/spatial/tests/test_qhull.py,sha256=XSaU8Rya86fyFjxUILoW-LLfbkQpQsb1J76ko1R4W8M,43730
scipy/spatial/tests/test_slerp.py,sha256=hYH-2ROq0iswTsli4c-yBLZfACvQL0QVCKrPWTeBNls,16396
scipy/spatial/tests/test_spherical_voronoi.py,sha256=UJU6By1eOzOhxgVYTEF5RVEkryXN70PHsXMRDG9-awQ,14361
scipy/spatial/transform/__init__.py,sha256=vkvtowJUcu-FrMMXjEiyfnG94Cqwl000z5Nwx2F8OX0,700
scipy/spatial/transform/__pycache__/__init__.cpython-310.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-310.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-310.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-310.pyc,,
scipy/spatial/transform/_rotation.cpython-310-x86_64-linux-gnu.so,sha256=uLcMdHLZCuzmmFrxQ0yJa5E7JDVPwZQemsnUpOsULQ0,742640
scipy/spatial/transform/_rotation.pyi,sha256=Rtatt37zVf2LzP8UTY8g_VdAGQ-L1dHSE6kxR0VdYrM,2638
scipy/spatial/transform/_rotation_groups.py,sha256=XS-9K6xYnnwWywMMYMVznBYc1-0DPhADHQp_FIT3_f8,4422
scipy/spatial/transform/_rotation_spline.py,sha256=M2i8qbPQwQ49D3mNtqll31gsCMqfqBJe8vOxMPRlD5M,14083
scipy/spatial/transform/rotation.py,sha256=1c1MrrZJrKsQXLpqM0MWV-0d8XNYW9xytpcGQAVbtfk,872
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-310.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-310.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-310.pyc,,
scipy/spatial/transform/tests/test_rotation.py,sha256=jG2BBWPSnyyrnxryN_v55kQiGTFaUl5F8uGdP2bK6ao,47183
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=V6DiLWvJsrdklhS-GlzcA9qEy0cTQpwaNR-7vkhBt1M,5560
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=g3prW5afu_yJxevIz2LMdRFYLfe8zq-3b6TMGw06Ads,5105
scipy/special.pxd,sha256=l9Y21wnx5fZLvrxCeCMUWQvBI5gHx7LBhimDWptxke8,42
scipy/special/__init__.py,sha256=DsjkPTIWj272EK-WiVfxqM-02jC3YJgiPErjxvCQR7A,30387
scipy/special/__pycache__/__init__.cpython-310.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-310.pyc,,
scipy/special/__pycache__/_basic.cpython-310.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-310.pyc,,
scipy/special/__pycache__/_lambertw.cpython-310.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-310.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-310.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-310.pyc,,
scipy/special/__pycache__/_sf_error.cpython-310.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-310.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-310.pyc,,
scipy/special/__pycache__/_testutils.cpython-310.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-310.pyc,,
scipy/special/__pycache__/basic.cpython-310.pyc,,
scipy/special/__pycache__/orthogonal.cpython-310.pyc,,
scipy/special/__pycache__/sf_error.cpython-310.pyc,,
scipy/special/__pycache__/specfun.cpython-310.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-310.pyc,,
scipy/special/_add_newdocs.py,sha256=VQaNQ2gnubItD3nAePtlp8CIP2eVXYKFmXAhXac8hhM,391328
scipy/special/_basic.py,sha256=-cUo4SDVSxvW9tNxygDZbvf2aD5cpD21kC7SS6JnERE,95649
scipy/special/_comb.cpython-310-x86_64-linux-gnu.so,sha256=VV3GmqvSfR1aRtqyA3R-Wkv6w6Ic2Ljj_8cdfbWGYII,40592
scipy/special/_ellip_harm.py,sha256=VLIdzP4XHbSHGbfbtSXdLenSZnh3c6MsYUFmED5kqhM,5272
scipy/special/_ellip_harm_2.cpython-310-x86_64-linux-gnu.so,sha256=PCONSwjElvYtUV9z3T1vgOA8qZOpmbqf2jqSc292apU,117145
scipy/special/_lambertw.py,sha256=Hgl7ZGLNOXxCz_G0edXzSWnN7ve-SNtfPoZgZGoOsFU,3806
scipy/special/_logsumexp.py,sha256=YBUutkjQ35HNbJDPNvNLyhlQL2A3HqL7BJviY3DwjAY,8523
scipy/special/_mptestutils.py,sha256=w3xwpwLSZ7p7-hbDcouEXg-tBcFKY9lkRCb4GPV3acM,14517
scipy/special/_orthogonal.py,sha256=mBT4bvQtuEXTyLcbsxOIDi_PbCMRUV66dSsBhp6VRxc,74237
scipy/special/_orthogonal.pyi,sha256=yz1kuVWF6LqQIrjNYKTPmV4bHGwx5yzFYdBvjRnzZno,8323
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-310.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-310.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=ZGSeDDpLRsapyx2GbIrqqYR98fvaEQrLn7IE-fuodhE,354
scipy/special/_precompute/expn_asy.py,sha256=JAz0hY1gBJu3Q_dvscQrSJdgKuwpjqFZVwz-sOQQ21w,1265
scipy/special/_precompute/gammainc_asy.py,sha256=P5OFRcPkkpjGQeYCaMZ8SFSUmZG_CjrEHv8OLwgcGFc,2502
scipy/special/_precompute/gammainc_data.py,sha256=Y5taFAdCE3W14bavUACTA3XoCxyh7_Z2NHcs-DKS75E,4077
scipy/special/_precompute/lambertw.py,sha256=7f4F3ivouVNZwuvVX8TAi2lPB7LirPS8IfN5lEw9zI0,1961
scipy/special/_precompute/loggamma.py,sha256=iq7ZBrUmk8pXYZwO_wINI4u8ENsLbL9VUShGjGO0Pt0,1094
scipy/special/_precompute/struve_convergence.py,sha256=MvCWOE9_JfSTsBp-e6-ECj3dMBrTL_2T8jIvd7Ktmjk,3462
scipy/special/_precompute/utils.py,sha256=JXJuI07Jlm4bDHJFVtj0jHq05p-V1ofeXZB16Y05kzI,887
scipy/special/_precompute/wright_bessel.py,sha256=IVD8YYv01aZKGq6-iFY1mvdTHfAS66h59w6N7u6fkMo,12866
scipy/special/_precompute/wright_bessel_data.py,sha256=f1id2Gk5TPyUmSt-Evhoq2_hfRgLUU7Qu_mELKtaXGg,5647
scipy/special/_precompute/wrightomega.py,sha256=YpmLwtGJ4qazMDY0RXjhnQiuRAISI-Pr9MwKc7pZlhc,955
scipy/special/_precompute/zetac.py,sha256=LmhJP7JFg7XktHvfm-DgzuiWZFtVdpvYzzLOB1ePG1Q,591
scipy/special/_sf_error.py,sha256=q_Rbfkws1ttgTQKYLt6zFTdY6DFX2HajJe_lXiNWC0c,375
scipy/special/_specfun.cpython-310-x86_64-linux-gnu.so,sha256=lyM7jVg9xdsDtVPHdKTA5FPimJhJ581v4JZhu3GIWyU,522201
scipy/special/_spfun_stats.py,sha256=Xnh6seX993udMM_6ftVaUHHwKpRuD9IopER6lPixxS0,3806
scipy/special/_spherical_bessel.py,sha256=2gQUI5_JeJ-OZ5XCrYlYgOHk8s0E6O_Qx62PusdUWAA,10217
scipy/special/_test_internal.cpython-310-x86_64-linux-gnu.so,sha256=g_p7pcFcLgyOfHkC103Jr1Vtg1eVYVsvRdpPvgpj8Js,268608
scipy/special/_test_internal.pyi,sha256=BI0xSfTmREV92CPzaHbBo6LikARpqb9hubAQgTT0W6w,338
scipy/special/_testutils.py,sha256=BzBVRPzA-uzcttlBbs5sdPYcebOOLQ4FoFnJlLzh4ZM,11971
scipy/special/_ufuncs.cpython-310-x86_64-linux-gnu.so,sha256=OqzszXMvBjpL_hl4gDA1kqNhZu44jSUe0PKBkHrpcSA,2072481
scipy/special/_ufuncs.pyi,sha256=M9HnG-pMqs6J4i56CBzIxitD33X7XvhckANm49A-S3s,8832
scipy/special/_ufuncs.pyx,sha256=y2w_F-LBXOOYDPhs35B5XLW2p2VYbZsH59erYO-Vg_U,877590
scipy/special/_ufuncs_cxx.cpython-310-x86_64-linux-gnu.so,sha256=A0B85Sf24-mFX-hzQYDsdcidAPL92XBnlTsomsEaPK0,525784
scipy/special/_ufuncs_cxx.pxd,sha256=qc2KyQvT50Q5cAHJFCzq3np5wmf1M5ASo09534D1ogY,1360
scipy/special/_ufuncs_cxx.pyx,sha256=7FSQvcUozXj24WvIiquZGVzPZQLz2KQrJjysTFU9TqI,7496
scipy/special/_ufuncs_cxx_defs.h,sha256=ILA7eh7wHjkImCubKcRkgsGCBAfxq8qVOpelwXSyvzI,2005
scipy/special/_ufuncs_defs.h,sha256=-ME-CBeDJnXjY9qHIz0KAARhWI45axzrO5PdbcHAVi4,11119
scipy/special/add_newdocs.py,sha256=lapv7DVKDeAr2vYaZr_6lMUK9hAP6IXy-wvzx8Qifi8,644
scipy/special/basic.py,sha256=KUTMYKk038KKpJ8fsoQoZSEJV6djRQL_Y_RgdxbXe4k,1896
scipy/special/cython_special.cpython-310-x86_64-linux-gnu.so,sha256=JKqDgq8PvdzfBkmcOFpvLGL99qOctVyyeYoTPeIuGpM,2866921
scipy/special/cython_special.pxd,sha256=2cJj4QPgcjZuqKZJTs31mDMrrusI60FcRxJP0uPJMKw,14371
scipy/special/cython_special.pyi,sha256=BQVUCzV8lCylnmLCtnN0Yz_ttlqyzcLc-BZx2KPXPzM,58
scipy/special/cython_special.pyx,sha256=cA5Pv4IkE_nsp8thgt9InfqIT58S6KMColHOHJI7lFo,137960
scipy/special/orthogonal.py,sha256=nL0enQ_z9S6eHvkjlszRdlV1GFU8q75LEaQWnhGERKs,2053
scipy/special/sf_error.py,sha256=He7080Os7bMgBh9v42PDsv0pLDJ8u94GWjzU-5QemTc,792
scipy/special/specfun.py,sha256=107XC40GRWPXmx3X2Hge0K5AtNMJPIdCOCJQo2c7f9I,1059
scipy/special/spfun_stats.py,sha256=-oa8b53MxKJrtmgb5jbwLeEyZDp9l9yvsCuv6337p7U,770
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_log_softmax.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-310.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-310.pyc,,
scipy/special/tests/data/boost.npz,sha256=5W0gCfeZrv35tYaK94rAt7T3CHIgErAluzJcsQgk_9E,1270643
scipy/special/tests/data/gsl.npz,sha256=bYdFDfyS6OZmMQzJRafhILEfL2c-Kece2XOHwPICvGs,51433
scipy/special/tests/data/local.npz,sha256=jNPxQ6OlmIoO_uD7ZmoqLX9guMgSVBZNvblvgm2FJHg,203438
scipy/special/tests/test_basic.py,sha256=oJmVnCf9921XalsWsDG8esEho9oXBObm0E4TR8qBoPo,158842
scipy/special/tests/test_bdtr.py,sha256=QwGyt0tnutuou25mS0u2LjRgDTYI6ohM2cbZ-He6Os4,3231
scipy/special/tests/test_boxcox.py,sha256=gUrGF7Ql1adxiPl_YxpsGunDfg-B_WpqI9Zghzool7o,2672
scipy/special/tests/test_cdflib.py,sha256=-xY82T2Hg6V2ZBoKzcXpt0OQygUOcjPXQpqDx0hSUks,13419
scipy/special/tests/test_cdft_asymptotic.py,sha256=UMwy8bSxUzzcj9MkG4FHzojJRFeshe05ZqFk_32iHKA,1429
scipy/special/tests/test_cosine_distr.py,sha256=zL7aWLisIEy1oNKjcynqncgsCxcPKvPb9Odr-J5Xa1M,2690
scipy/special/tests/test_cython_special.py,sha256=c5TWmq9oaRQDvSmiV-hGCjM2hmuMALtYH4gl1ffUlCs,18656
scipy/special/tests/test_data.py,sha256=fdEc6s8OaP89ZJ0bbwVkmYB3bLotzUa3OBnOJ1G3nso,28485
scipy/special/tests/test_dd.py,sha256=GROHQEkzIAW6KXkj8J3nPcRDAONcf1nCoArcfx30_5s,1974
scipy/special/tests/test_digamma.py,sha256=NlaFqc08di2L5FXArct9r0GaP_ciAxTIx-bfk-kdaks,1394
scipy/special/tests/test_ellip_harm.py,sha256=51KiCpQjqmf2uLZEsty-Vmr0FhoABtvMUz4218WR_S0,9640
scipy/special/tests/test_erfinv.py,sha256=fzdEHd6MxfSyzQDO93qndXukG2jWj-XNY2X4BJRIdBI,3059
scipy/special/tests/test_exponential_integrals.py,sha256=hlzNhZEXjo5ioPteG0P85qXuMmVD-WVc67e049tvY8Q,3687
scipy/special/tests/test_faddeeva.py,sha256=YLY3Ylp4u_8zxTGxOb5kxNfXXEW0ld_GP2ceOR2ev_Y,2568
scipy/special/tests/test_gamma.py,sha256=hb-ZlA2ZNz6gUGvVtMBgXFl_w30HPmthuUEAmNcz0sw,258
scipy/special/tests/test_gammainc.py,sha256=Avv52EDQ7M8kUpiVU1BVsW_Gj5HDCzAOojLtoFojKbw,3815
scipy/special/tests/test_hyp2f1.py,sha256=n0cc9uZrI3YLhjImgSx_Kgv75XTEu1l_JCjonXo2lsk,78547
scipy/special/tests/test_hypergeometric.py,sha256=a9KvKiQRCAyXa0gVOiKr6vbG0SxWq5MwkSDpElQE7-A,5598
scipy/special/tests/test_kolmogorov.py,sha256=nRZHg4P3GEcax-vgB-LlG9KrdlDRd36ZvGTF3oCv9po,18407
scipy/special/tests/test_lambertw.py,sha256=A6SAKE2KBWY2YlqdOSnVRzxw1RKJ7f2ZYcBTg1-q7Bk,4556
scipy/special/tests/test_log_softmax.py,sha256=JdiC5C1Fm16rNdQHVWRu-FGMVOv24DPWRnguDDd1zEY,3415
scipy/special/tests/test_loggamma.py,sha256=x6kuJf-bEnn5ECdkDSgvk3An_A-9UxVsZpqa49IwAq8,1992
scipy/special/tests/test_logit.py,sha256=PvIgcK33vQjcvHE3_3fVarKTjZ0t35-ksZnhvoqKQrA,5540
scipy/special/tests/test_logsumexp.py,sha256=vcHdTDJQKvUfkO0I8VDRUQF4MhnF0dQi2pjDzRsggB0,6180
scipy/special/tests/test_mpmath.py,sha256=yXaU8yhq3xT9gdiIO2uJWT5DNgbbme80-0KYT5Chbk4,75189
scipy/special/tests/test_nan_inputs.py,sha256=7J16DMVm4CSt6UJYB76s8nPIeEoMwD8VHjCVVsS4aNE,1837
scipy/special/tests/test_ndtr.py,sha256=-UMxTIi4CaaLoJ5-SGW9THChPIM3e1_fTY0L877ioNA,2680
scipy/special/tests/test_ndtri_exp.py,sha256=13eabgdbfcL37RReiUH7g9amT9XMsTLOfwxFJXR_2Ww,3708
scipy/special/tests/test_orthogonal.py,sha256=tjRZasR73OJZzB6qAdpNj0KiOSOG9Zcc_P3n0we1wl8,31471
scipy/special/tests/test_orthogonal_eval.py,sha256=xj3-5r1s70kev3d-qiTk8m7tZ09b6JjJY9OTVxVVNx0,9319
scipy/special/tests/test_owens_t.py,sha256=zRbiKje7KrYJ25f1ZuIBfiFSyNtK_bnkIW7dRETIqME,1792
scipy/special/tests/test_pcf.py,sha256=RNjEWZGFS99DOGZkkPJ8HNqLULko8UkX0nEWFYX26NE,664
scipy/special/tests/test_pdtr.py,sha256=VmupC2ezUR3p5tgZx0rqXEHAtzsikBW2YgaIxuGwO5A,1284
scipy/special/tests/test_powm1.py,sha256=9hZeiQVKqV63J5oguYXv_vqolpnJX2XRO1JN0ouLWAM,2276
scipy/special/tests/test_precompute_expn_asy.py,sha256=bCQikPkWbxVUeimvo79ToVPgwaudzxGC7Av-hPBgIU4,583
scipy/special/tests/test_precompute_gammainc.py,sha256=6XSz0LTbFRT-k0SlnPhYtpzrlxKHaL_CZbPyDhhfT5E,4459
scipy/special/tests/test_precompute_utils.py,sha256=MOvdbLbzjN5Z1JQQgtIyjwjuIMPX4s2bTc_kxaX67wc,1165
scipy/special/tests/test_round.py,sha256=oZdjvm0Fxhv6o09IFOi8UUuLb3msbq00UdD8P_2Jwaw,421
scipy/special/tests/test_sf_error.py,sha256=leNORk4GIa8gYQH69OK0TK_SPXOMGPvtNC-x5aJ_nT8,3521
scipy/special/tests/test_sici.py,sha256=w4anBf8fiq2fmkwMSz3MX0uy35NLXVqfuW3Fwt2Nqek,1227
scipy/special/tests/test_spence.py,sha256=fChPw7xncNCTPMUGb0C8BC-lDKHWoEXSz8Rb4Wv8vNo,1099
scipy/special/tests/test_spfun_stats.py,sha256=A5SOVsQOyC12-BeIIHsi--hrog88mFmH190MOKdP4qU,1998
scipy/special/tests/test_sph_harm.py,sha256=ySUesSgZBb4RN-QES2L6G6k3QGOCdGLt86fjJ-6EYiQ,1106
scipy/special/tests/test_spherical_bessel.py,sha256=5f2tsw0DUbs_Q4A4-BNrrDA7NzFuKEGnSJ3nwnDNWqI,14284
scipy/special/tests/test_trig.py,sha256=WiZ-ryT7F8-kaACJKcXaA7PXSbuU4gIz_MK9Pv1gsTc,2097
scipy/special/tests/test_wright_bessel.py,sha256=v1yLL6Ki01VuKPj5nfL-9_FaACvwdIlDsarKsm-z9EQ,4155
scipy/special/tests/test_wrightomega.py,sha256=BW8TS_CuDjR7exA4l6ADnKhXwgFWUYaN1UIopMBJUZY,3560
scipy/special/tests/test_zeta.py,sha256=IoBUdssBRj7noPjW-xs9xGFFihZ7wvQpPJidgMOFCOs,1367
scipy/stats/__init__.py,sha256=kMxSRlC3Wfr4qnayYbiUBuLpuYpasm12cKgIKtQz0Qw,18098
scipy/stats/__pycache__/__init__.cpython-310.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-310.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-310.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-310.pyc,,
scipy/stats/__pycache__/_censored_data.cpython-310.pyc,,
scipy/stats/__pycache__/_common.cpython-310.pyc,,
scipy/stats/__pycache__/_constants.cpython-310.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-310.pyc,,
scipy/stats/__pycache__/_covariance.cpython-310.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-310.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-310.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-310.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-310.pyc,,
scipy/stats/__pycache__/_entropy.cpython-310.pyc,,
scipy/stats/__pycache__/_fit.cpython-310.pyc,,
scipy/stats/__pycache__/_generate_pyx.cpython-310.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-310.pyc,,
scipy/stats/__pycache__/_kde.cpython-310.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-310.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-310.pyc,,
scipy/stats/__pycache__/_morestats.cpython-310.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-310.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-310.pyc,,
scipy/stats/__pycache__/_multicomp.cpython-310.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-310.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-310.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-310.pyc,,
scipy/stats/__pycache__/_qmc.cpython-310.pyc,,
scipy/stats/__pycache__/_qmvnt.cpython-310.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-310.pyc,,
scipy/stats/__pycache__/_resampling.cpython-310.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-310.pyc,,
scipy/stats/__pycache__/_rvs_sampling.cpython-310.pyc,,
scipy/stats/__pycache__/_sensitivity_analysis.cpython-310.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-310.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-310.pyc,,
scipy/stats/__pycache__/_survival.cpython-310.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-310.pyc,,
scipy/stats/__pycache__/_variation.cpython-310.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-310.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-310.pyc,,
scipy/stats/__pycache__/contingency.cpython-310.pyc,,
scipy/stats/__pycache__/distributions.cpython-310.pyc,,
scipy/stats/__pycache__/kde.cpython-310.pyc,,
scipy/stats/__pycache__/morestats.cpython-310.pyc,,
scipy/stats/__pycache__/mstats.cpython-310.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-310.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-310.pyc,,
scipy/stats/__pycache__/mvn.cpython-310.pyc,,
scipy/stats/__pycache__/qmc.cpython-310.pyc,,
scipy/stats/__pycache__/sampling.cpython-310.pyc,,
scipy/stats/__pycache__/statlib.cpython-310.pyc,,
scipy/stats/__pycache__/stats.cpython-310.pyc,,
scipy/stats/_axis_nan_policy.py,sha256=dLUAcf90NJoxXqspdvvf9MaETBaDymSKSYwGemlQ3Cg,28384
scipy/stats/_biasedurn.cpython-310-x86_64-linux-gnu.so,sha256=RoXBfFEtefMsAhIt1ZY-x9H1Kj0KQ_AcbNuoLl0vE6Q,526264
scipy/stats/_biasedurn.pxd,sha256=bQC6xG4RH1E5h2jCKXRMADfgGctiO5TgNlJegKrR7DY,1046
scipy/stats/_binned_statistic.py,sha256=PcH7xt71nrqoukYvFsHkDqgDGCm0Fq05ZcN5ab7m6No,32704
scipy/stats/_binomtest.py,sha256=cbmBHbwpXRap9zZElMvdYhy7ccTvH1kgi_7_iNctD1A,13043
scipy/stats/_boost/__init__.py,sha256=e1_a5N-BBpz7qb0VeLQ7FOEURW9OfQ3tV42_fMDVkOU,1759
scipy/stats/_boost/__pycache__/__init__.cpython-310.pyc,,
scipy/stats/_boost/beta_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=GXbkX6BEsVOyAtna4HbeC4nV3HGkx-2NeCtVLXZl9vI,210392
scipy/stats/_boost/binom_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=9vx9W4jqnxqgZERMqRMa3LUrWSfJgEnJilJwQQPLDP8,177584
scipy/stats/_boost/hypergeom_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=EK5JHKYOQzCYcOXd8bHaJfr3MjIImPARmW67xm05ZkE,114216
scipy/stats/_boost/invgauss_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=H7aza5csVsDJ4h4fpDEXdqXw1k-Qd2n6ar4nXgmNx9g,172776
scipy/stats/_boost/nbinom_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=Lnbm_VJmOC13cu7TO8ueN-VczknlS0RaLH8CtBSNwGg,177832
scipy/stats/_boost/ncf_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=3zEy48d4s8FjkfCU4a5PR96T11cXYth9Qe9C1bWxsdw,175688
scipy/stats/_boost/nct_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=GBwD0g2u0i2ANumgnmB0sJHtXjaXnNHzG3MDsNlO4Qw,225432
scipy/stats/_boost/ncx2_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=ouXClU10zImSYDiTNGy4dFzl3pry_THv9uLL9tjINX4,176536
scipy/stats/_boost/skewnorm_ufunc.cpython-310-x86_64-linux-gnu.so,sha256=lcA9LLyG5SwX9NdELXBm8bqxilXIkw5t8NB8Y6YGjTo,106648
scipy/stats/_censored_data.py,sha256=Ts7GSYYti2z-8yoOJTedj6aCLnGhugLlDRdxZc4rPxs,18306
scipy/stats/_common.py,sha256=4RqXT04Knp1CoOJuSBV6Uy_XmcmtVr0bImAbSk_VHlQ,172
scipy/stats/_constants.py,sha256=_afhD206qrU0xVct9aXqc_ly_RFDbDdr0gul9Nz6LCg,962
scipy/stats/_continuous_distns.py,sha256=spOMThUP5aWZdDY6I_S6SIoVWDtZQ7nBXXLl9JTRU5E,364481
scipy/stats/_covariance.py,sha256=xNbA97aHKoBQaCrNqI2GQfc7VeXbQW9aBiVmlKTq1Xk,22467
scipy/stats/_crosstab.py,sha256=f4Sqooh-gPyTjLMHRbmhkVaOT-nhrOZ2NJ-gfPjvyuY,7355
scipy/stats/_discrete_distns.py,sha256=h5NEWOGeCzRsUhkLihJASz8foVQdtuAt3EDYek3J8vE,54614
scipy/stats/_distn_infrastructure.py,sha256=Wr0SJsL36qfKNWDFgejGBvXt946_jeVdtOL_Sdyw2fQ,145320
scipy/stats/_distr_params.py,sha256=_2pURyr-80fVZgSaHOIv5fur7Pw9WePht26hUZwfnuM,8577
scipy/stats/_entropy.py,sha256=hpJsp5sBTyNocGl8DWKECaeWN4ZUxcr4DFPzGEmxQJk,14279
scipy/stats/_fit.py,sha256=KKst-0FLH2Df081bQ5_KJWnYdQ79t5CDz7IFd5CyZME,58449
scipy/stats/_generate_pyx.py,sha256=27GRB_I5RyPJbDZy_0QH6BEWcNzZ47pDrXvSD5K4Z9k,1312
scipy/stats/_hypotests.py,sha256=iL80Bv8a3EpH_dSAXCCExgbQ7dF5xSq0b3GX5gp3t6A,78643
scipy/stats/_kde.py,sha256=49JD51NqYG4lr44miYGvYTsYQd9fqjinQuqRL0_JCAQ,24989
scipy/stats/_ksstats.py,sha256=02TTvWusChHFCbO_3TvjD7OySTMzwhHEdKVwFHGyo0k,20100
scipy/stats/_levy_stable/__init__.py,sha256=lNkHB8k771UBTaXoIbx3C2R4Hfjo12pHwfP5rJHHmkE,43850
scipy/stats/_levy_stable/__pycache__/__init__.cpython-310.pyc,,
scipy/stats/_levy_stable/levyst.cpython-310-x86_64-linux-gnu.so,sha256=Jhc7AhgYgTaWIoLEhnSeAbc1b0Kq20sfeV8iuuLolko,46368
scipy/stats/_mannwhitneyu.py,sha256=SXc7vyo3cdmrlZ17cCDzF_GIIfx_saj5alViD3CkhcU,18967
scipy/stats/_morestats.py,sha256=luqTHiZQ7NAdN4nfrmuuPQJeDcA0WYUhbOkLl3JPGhI,189767
scipy/stats/_mstats_basic.py,sha256=Q8eB9VpTmWvTYyKmMpSb0cyhetxGEk9eZDIQKoMlmY0,117891
scipy/stats/_mstats_extras.py,sha256=Eyg8-p6J5G20nZS0laPOFrLpfqcyqdK4t9Sn6z1laLs,15610
scipy/stats/_multicomp.py,sha256=ae_nYfCQVLduyPb5sRTCcV0MpcymnV4H8SM35u3E8NY,17282
scipy/stats/_multivariate.py,sha256=R3ETlFxKMbPgCNhZ5xOsXCsGvkA64Qmp36ors1hDoh4,234504
scipy/stats/_mvn.cpython-310-x86_64-linux-gnu.so,sha256=rVnd6IFJg-MpV3NCv1NRTzwwdCoDw_R7njCUNqgrbgk,84688
scipy/stats/_odds_ratio.py,sha256=a8hp9A5gcBkPUYAITl3ZaI-deLuZqg_Vp-l1wqh_-IA,17856
scipy/stats/_page_trend_test.py,sha256=Ku7yLzBVOzZ8uZwm9W5Cj1O_tSvQY2S7MNmifB84w0g,18999
scipy/stats/_qmc.py,sha256=iMN245995T26Q7pl5mp8-UXzzz0xaVYxPyrld48AvuE,94304
scipy/stats/_qmc_cy.cpython-310-x86_64-linux-gnu.so,sha256=uaHdR6O-ViC2bVJKyO8YBX5OsKmaymNZkH8lD8F5Kaw,273584
scipy/stats/_qmc_cy.pyi,sha256=xOpTSlaG_1YDZhkJjQQtukbcgOTAR9FpcRMkU5g9mXc,1134
scipy/stats/_qmvnt.py,sha256=Mss1xkmWwM3o4Y_Mw78JI-eB4pZBeig47oAVpBcrMMc,18767
scipy/stats/_rcont/__init__.py,sha256=dUzWdRuJNAxnGYVFjDqUB8DMYti3by1WziKEfBDOlB4,84
scipy/stats/_rcont/__pycache__/__init__.cpython-310.pyc,,
scipy/stats/_rcont/rcont.cpython-310-x86_64-linux-gnu.so,sha256=NVz8rU__S706--AmdPQJUUsz9sVWjLTooUKpqbJVDxg,470856
scipy/stats/_relative_risk.py,sha256=5zeYBMshYwtomiLTkaXc1nmWYD0FsaQNjf0iuDadtSc,9571
scipy/stats/_resampling.py,sha256=1mnQoU-ygcBPBP7EfnhVvwdiphe4D06g96bYVuFeHjo,80044
scipy/stats/_result_classes.py,sha256=_ghuGdpFsCMuEmnfHg1AeorR-fASc77ACXYWEmQzXjI,1085
scipy/stats/_rvs_sampling.py,sha256=Id0ONAO6T-2q8rdv0Ry-TH4aHIburd9CtnZoWd3MMEk,7203
scipy/stats/_sensitivity_analysis.py,sha256=Uqv7mZAqqhtcT6Ds6aph64eRfxno0YWluDUtZ4ndUs8,24753
scipy/stats/_sobol.cpython-310-x86_64-linux-gnu.so,sha256=Uwu18IHfaw1u7aXCwazHxfPiYjF-xVYQsUwtYZdmaNQ,339440
scipy/stats/_sobol.pyi,sha256=TAywylI75AF9th9QZY8TYfHvIQ1cyM5QZi7eBOAkrbg,971
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_statlib.cpython-310-x86_64-linux-gnu.so,sha256=lUvFT-lLNLLCCN9IRNFweIY8fE3IevOZnhK_-djKi3E,53600
scipy/stats/_stats.cpython-310-x86_64-linux-gnu.so,sha256=AzklwH7BRYbnFqRD9TyjKG7nipO57j2gI5bqTDAh3BA,716800
scipy/stats/_stats.pxd,sha256=US2p3SKahv_OPhZClWl_h3cZe7UncGZoQJeixoeFOPg,708
scipy/stats/_stats_mstats_common.py,sha256=Lz1Klxbt_m2z0Z34NixZQJIY1WZ4zA9KX3NYttZcAeA,18649
scipy/stats/_stats_py.py,sha256=Mf8E5e-Boy8JIz6i65J1-VMDoF0QMPwZJZwHkKef_Rk,394043
scipy/stats/_stats_pythran.cpython-310-x86_64-linux-gnu.so,sha256=HQt32BWCnl4ZeVtcVfZ2XPBV_9PihFw8xaWKE-__YU8,154704
scipy/stats/_survival.py,sha256=FVMfFgSbRi91g4kAx77WRkqe_1cJetil98xPta3i27I,25965
scipy/stats/_tukeylambda_stats.py,sha256=eodvo09rCVfcYa1Uh6BKHKvXyY8K5Zg2uGQX1phQ6Ew,6871
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-310.pyc,,
scipy/stats/_unuran/unuran.pxd,sha256=IIB-izI1fWQKXZ6xt7_y827d-Z6RzKiZkBJ-sJHEsEE,43357
scipy/stats/_unuran/unuran_wrapper.cpython-310-x86_64-linux-gnu.so,sha256=yY5R8ZtQ9qEu3mXEIvlgaQ0s-DZh6t3DOn4x-4gW9SI,1571912
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=k-uYStXKukfT_yh076YUUebm4axSEVv0JvuFySl88z4,5544
scipy/stats/_variation.py,sha256=eWxFtXvYcJDWHcL2ghvvBwIXFuV9trRD2npHchos9UQ,8328
scipy/stats/_warnings_errors.py,sha256=MpucxNFYEDytXh7vrZCMqTkRfuXTvvMpQ2W_Ak2OnPk,1196
scipy/stats/biasedurn.py,sha256=EPX7Ft1Uo28nEk1Bo_XIFNASdYfknXRbx-lXnrw4jLU,690
scipy/stats/contingency.py,sha256=rYcdlZWixDvI_AoM_xHD5tWgHTtEGwMyJd3NZxmEBwU,16283
scipy/stats/distributions.py,sha256=5DUBY0ZeEhA7hIXgIBSEkoU-Mk1AivF-1NUnNB26NDs,817
scipy/stats/kde.py,sha256=CteJ2T0-4kFFT0wqpwwa3nhewyE_LnAUC0qlLnfoWNo,923
scipy/stats/morestats.py,sha256=Lzo2TJSmmnO2VuujfDTx6R-1h2mc-rdutjbKXQx6bJ8,1620
scipy/stats/mstats.py,sha256=Uqwz-P46lDBWfL7uumXpD_qhV-M-OTJfSTCBJVUnJZk,2262
scipy/stats/mstats_basic.py,sha256=fcqdbCirE88xnXXOu2fEgFOISLDwobB9_oBKb7Ma9YI,2123
scipy/stats/mstats_extras.py,sha256=zpvhK6MODW78ymWOpnj-QHc7bxpdPdG22Yr2Rypndw8,1001
scipy/stats/mvn.py,sha256=lBrOC0EQSv585vPnhUCdNCSvqq4Ns5X1i7zKJDy3rXU,784
scipy/stats/qmc.py,sha256=D1GsIxfmBnXT6gN3KQM3sBFvmyGgoxGAdIlpwm8M1e4,11624
scipy/stats/sampling.py,sha256=Ca2PMnize44Q_bUlwysc31IrKKCs5VDkGghLcx8iRNE,1196
scipy/stats/statlib.py,sha256=KU5sYHMhlfnpCbLt5FdMASKQ57GLIDA4AZ4gZWDCK4w,776
scipy/stats/stats.py,sha256=Bb3Mx-MOQfU4FEyN-YEioQYj8AWYm9obPkVgiwB7Atg,2684
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-310.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_boost_ufuncs.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_censored_data.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_continuous_fit_censored.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_multicomp.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_sensitivity_analysis.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_survival.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-310.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-310.pyc,,
scipy/stats/tests/common_tests.py,sha256=EEsKXftW6ArCVfxIHrtJeaUZ_rbLaxXptfcPoUVK094,12269
scipy/stats/tests/data/__pycache__/_mvt.cpython-310.pyc,,
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-310.pyc,,
scipy/stats/tests/data/_mvt.py,sha256=Ltt-du3wtPM6gcFaidIiMFzcB2BtJ5gDKQwyUkDAWHQ,6920
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=BKxPAi4h3IOebcZYGxCbutYuAX0tlb40P0DEkfEi918,27349
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=Qdd0i7H4cNhAABfFOZPuplhi_9SCquFpO-hNkyRcMD8,3063
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=x9wJ2g1qnzf4DK_w9F_WiOiDMDEg4td2z6uU77G07xM,1947
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=KdnJedRthF7XLA-w7XkIPIMTgzu89yBAMmZA2H4uQOQ,6055
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=nCPyxRk1dAoSPWiC7kG4dLaXs2GL3-KRXRt2NwgXoIA,46561
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=6yPHiQSk0KI4oURQOk99t-uEm-IZN-8eIPHb_y0mQ1U,451566
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=fI-HpgJF9cdGdBinclhVzOcWCCc5ZJZuXalUwirV-lc,6815
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=iJTaAWUFn7DPLTd9bQh_EMKEK1DPG0fnN8xk7BQlPRE,53799
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=riOkYT-LRgmJhPpCK32x7xYnD38gwnh_Eo1X8OK3eN8,523605
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=QtSS11d-vkVvqaIEeJ6oNwyET1CKoyQqjlfBl2sTOJA,7381
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=qrxQQ0I6gnhrefygKwT48x-bz-8laD8Vpn7c81nITRg,59228
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=qmELOQyNlH7CWOMt8PQ0Z_yxgg9Hxc4lqZOuHZxxWuc,577633
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=zD_RTRxfqJHVZTAAyddzLDDbhCzKSfwFGr3hwZ1nq30,2591
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=icZGNBodwmJNzOyEki9MreI2lS6nQJNWfnVJiHRNRNM,29239
scipy/stats/tests/test_axis_nan_policy.py,sha256=Od_WdpAQBZYGdtanES3b4TuU-k13W_KfhFdR6Wl7tJo,47628
scipy/stats/tests/test_binned_statistic.py,sha256=WE5KdJq4zJxZ1LuYp8lv-RMcTEyjuSkjvFHWsGMujkM,18814
scipy/stats/tests/test_boost_ufuncs.py,sha256=y2ZKOEPqTFvs2bxZbgXmuW1La0VSO6La4QO0FUxMM58,1824
scipy/stats/tests/test_censored_data.py,sha256=pAQfSHhmcetcxoS1ZgIHVm1pEbapW7az7I-y_8phb5w,6935
scipy/stats/tests/test_contingency.py,sha256=fMeGnTldQjLa5CSaaQ6qH90JXzrUivthVD-9DafgQm0,7706
scipy/stats/tests/test_continuous_basic.py,sha256=_L1SjgTS2Es1rpIPawxWZmBgp8UBoLK2Gs4evUGDhb0,41156
scipy/stats/tests/test_continuous_fit_censored.py,sha256=Ok5K_he-TdoskVsVf4FsER2Ykqoz70_G9OUjpL-FrGE,24186
scipy/stats/tests/test_crosstab.py,sha256=tvCoZGfVasNIhYxLQIe3dcdMm34s2ykxxPmCRTIOFc0,3882
scipy/stats/tests/test_discrete_basic.py,sha256=yD_HwUrKku2--eccR78ZlOSmhgMoiP-ZSjr2SiYbMPo,19786
scipy/stats/tests/test_discrete_distns.py,sha256=PQozAj6m62DaZXABnbHC4FiVorqUxvUMxu4tgdKzUAo,20463
scipy/stats/tests/test_distributions.py,sha256=VwYh147OhIRdqRJiPdjQDKBIVec3Mykk-FwPt4ldGug,357086
scipy/stats/tests/test_entropy.py,sha256=YFQwSDdpv6e_KXxEbK2fCrE7GRiIBS0RsQmWwJp0gYw,11277
scipy/stats/tests/test_fit.py,sha256=Imo-SNgmTRtK_0abzXwObSd9fJBtgAAdE96hSHiyhrU,42951
scipy/stats/tests/test_hypotests.py,sha256=XSuUTDZwVCqwMY0uMNwh4_7NC-yRnED-6_WpR3rhoIY,74101
scipy/stats/tests/test_kdeoth.py,sha256=pLmpEoJxjLgEJxYzPVqtIQoI2yJv-Mz7i7DKgBdz7pY,20382
scipy/stats/tests/test_morestats.py,sha256=IX9DBC5V8ZwThAiEw7PO77Rgng5q_pJ3FmSzrpuqKDE,119434
scipy/stats/tests/test_mstats_basic.py,sha256=LDayfwcrSbw1MQr28L9nbECsDZqR-11NzXhkzFJPkGg,85050
scipy/stats/tests/test_mstats_extras.py,sha256=CCexzT1lksTG_WvGvHn6-CuWd_ZXoFviNGnBZd_hE7Y,7297
scipy/stats/tests/test_multicomp.py,sha256=xLlLP54cWsLAbSsfodoTkuJa9FJM1qKnlSrDGE-jRZ0,17826
scipy/stats/tests/test_multivariate.py,sha256=3k8M4vOVdGzhPsy7m_btkE4ELVoNnU6fnc78hyeR8fM,143630
scipy/stats/tests/test_odds_ratio.py,sha256=RIsmgnmUUH3DvynDRZUaS6llCbXm2oWIfPa48IJJ-gI,6705
scipy/stats/tests/test_qmc.py,sha256=xujLZXg1zIuJaRmx55NsfCwlshSitHxeL8ICMR4H8fI,52368
scipy/stats/tests/test_rank.py,sha256=kxt3Jeg2Gn1_ehDF-ThU4VokSbLRN05SR-OjoduLiTg,11321
scipy/stats/tests/test_relative_risk.py,sha256=jzOGNQ2y9_YfFnXiGAiRDrgahy66qQkw6ZkHgygCJMA,3646
scipy/stats/tests/test_resampling.py,sha256=qm3aD34S2R99q94vME6csgmMlHGnTDk4t0R-2ECpvpo,70650
scipy/stats/tests/test_sampling.py,sha256=Sj3eRMArPPmxst-rnugDetGK7ZIpDAMaJAdvwA9Kk6I,51422
scipy/stats/tests/test_sensitivity_analysis.py,sha256=ipZQ5eNwmsJAYH5Bx8Q0T2ta-oaxJPVNkxCONSwis0k,10160
scipy/stats/tests/test_stats.py,sha256=lTgVWqkAiXvrmeXP37XIIE6iXMoMDuWFDddcsJV0CSA,344202
scipy/stats/tests/test_survival.py,sha256=AvlAWcw3qQh5be910PQL2cgC4ELGCvT5zNeCQryVx6M,21992
scipy/stats/tests/test_tukeylambda_stats.py,sha256=6WUBNVoTseVjfrHfWXtU11gTgmRcdnwAPLQOI0y_5U8,3231
scipy/stats/tests/test_variation.py,sha256=WUZAV07LhaScxztzN7Vv2OAWjV-b42FxgsGZtGf0WYI,6245
scipy/version.py,sha256=QlVnKXYrYNUWr24_xFTLVaoGtTkzJp7RRZBgLuifA2s,264
