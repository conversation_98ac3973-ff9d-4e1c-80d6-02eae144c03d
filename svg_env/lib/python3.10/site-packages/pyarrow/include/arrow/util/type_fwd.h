// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

#pragma once

namespace arrow {

namespace internal {
struct Empty;
}  // namespace internal

template <typename T = internal::Empty>
class WeakFuture;
class FutureWaiter;

class TimestampParser;

namespace internal {

class Executor;
class TaskGroup;
class ThreadPool;
class CpuInfo;

namespace tracing {

struct Scope;

}  // namespace tracing
}  // namespace internal

struct Compression {
  /// \brief Compression algorithm
  enum type {
    UNCOMPRESSED,
    SNAPPY,
    GZIP,
    BROTLI,
    ZSTD,
    LZ4,
    LZ4_FRAME,
    LZO,
    BZ2,
    LZ4_HADOOP
  };
};

namespace util {
class AsyncTaskScheduler;
class Compressor;
class Decompressor;
class Codec;
class Uri;
}  // namespace util

template <typename T>
struct Enumerated {
  T value;
  int index;
  bool last;

  friend inline bool operator==(const Enumerated<T>& left, const Enumerated<T>& right) {
    return left.index == right.index && left.last == right.last &&
           left.value == right.value;
  }
};

}  // namespace arrow
