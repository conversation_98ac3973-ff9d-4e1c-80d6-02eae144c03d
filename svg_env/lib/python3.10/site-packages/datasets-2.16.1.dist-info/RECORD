../../../bin/datasets-cli,sha256=WKn9_MiIbQX82kkM7mTGfAxVR7YgZ8Un0SBVbAskjRg,255
datasets-2.16.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
datasets-2.16.1.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
datasets-2.16.1.dist-info/METADATA,sha256=_WE5z3e0eFDZNmD_ruPdFMyB6ttuyIm0NwFwvWLAxWM,20124
datasets-2.16.1.dist-info/RECORD,,
datasets-2.16.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets-2.16.1.dist-info/WHEEL,sha256=pkctZYzUS4AYVn6dJ-7367OJZivF2e8RA9b_ZBjif18,92
datasets-2.16.1.dist-info/entry_points.txt,sha256=vhdg1JXUleCZtwvozP5q5iHqRpSETfyhSDJ39zW3KUA,70
datasets-2.16.1.dist-info/top_level.txt,sha256=9A857YvCQm_Dg3UjeKkWPz9sDBos0t3zN2pf5krTemQ,9
datasets/__init__.py,sha256=o8kyE6wuhbCFdgJDrmpCSwjGIKCTH5r1Ajbc9Bm2yZ4,2587
datasets/__pycache__/__init__.cpython-310.pyc,,
datasets/__pycache__/arrow_dataset.cpython-310.pyc,,
datasets/__pycache__/arrow_reader.cpython-310.pyc,,
datasets/__pycache__/arrow_writer.cpython-310.pyc,,
datasets/__pycache__/builder.cpython-310.pyc,,
datasets/__pycache__/combine.cpython-310.pyc,,
datasets/__pycache__/config.cpython-310.pyc,,
datasets/__pycache__/data_files.cpython-310.pyc,,
datasets/__pycache__/dataset_dict.cpython-310.pyc,,
datasets/__pycache__/distributed.cpython-310.pyc,,
datasets/__pycache__/exceptions.cpython-310.pyc,,
datasets/__pycache__/fingerprint.cpython-310.pyc,,
datasets/__pycache__/info.cpython-310.pyc,,
datasets/__pycache__/inspect.cpython-310.pyc,,
datasets/__pycache__/iterable_dataset.cpython-310.pyc,,
datasets/__pycache__/keyhash.cpython-310.pyc,,
datasets/__pycache__/load.cpython-310.pyc,,
datasets/__pycache__/metric.cpython-310.pyc,,
datasets/__pycache__/naming.cpython-310.pyc,,
datasets/__pycache__/search.cpython-310.pyc,,
datasets/__pycache__/splits.cpython-310.pyc,,
datasets/__pycache__/streaming.cpython-310.pyc,,
datasets/__pycache__/table.cpython-310.pyc,,
datasets/arrow_dataset.py,sha256=QlnkmjEW_oUwW-RTPFFxaMwawEgeOcytwVQoL8I4q4I,287914
datasets/arrow_reader.py,sha256=4zgsJiqRoNU18eAjRGM4CT5cY06WYc6Ikbfa9dVfjTA,26920
datasets/arrow_writer.py,sha256=jjYPeYN39-IedKFpjq80VTdNWCFSb9DfEMkXeBHnkQk,33438
datasets/builder.py,sha256=LYXartNfitgkoQgP9DuPEKXwIuNcPjZiw53kmejKhxQ,110510
datasets/combine.py,sha256=OvMg-5A_cBraHyEXbNTTrWjd9sbUiyA7PG6aBJpbg5Q,10924
datasets/commands/__init__.py,sha256=rujbQtxJbwHhF9WQqp2DD9tfVTghDMJdl0v6H551Pcs,312
datasets/commands/__pycache__/__init__.cpython-310.pyc,,
datasets/commands/__pycache__/convert.cpython-310.pyc,,
datasets/commands/__pycache__/datasets_cli.cpython-310.pyc,,
datasets/commands/__pycache__/dummy_data.cpython-310.pyc,,
datasets/commands/__pycache__/env.cpython-310.pyc,,
datasets/commands/__pycache__/run_beam.cpython-310.pyc,,
datasets/commands/__pycache__/test.cpython-310.pyc,,
datasets/commands/convert.py,sha256=-VOqHh0ySkIOfEYmR7HVs7PzouVrkVShqyUtNGcNCYU,7914
datasets/commands/datasets_cli.py,sha256=mMYGiIYoE9kcZzcWvPDPuT2fEKlhL2hHN9RWgivQu2I,1381
datasets/commands/dummy_data.py,sha256=rBVQAN1wd9fvldw79PVoL3vNZdqosjO_PPO_SFEYUqw,23106
datasets/commands/env.py,sha256=8qg-hpXSXXsHvtYFvJkn5rn9IncqPsjjx3nR8no4a2I,1239
datasets/commands/run_beam.py,sha256=Cl6zWXA00C9PjgQyMv_E7SSMN2539no26OLFeznJxYM,6812
datasets/commands/test.py,sha256=fBTg83h0d8pAJYqIvOXXDQsdHd4-7G95iaBw513CCys,8483
datasets/config.py,sha256=I97jhxOQoqvFS3KQm0Tz9hc08CtsUf42RamoJaGhS0g,10004
datasets/data_files.py,sha256=j35-eDpVVtLkW9kHFylJLUxleC9PvhQk-vaRElfgBRI,31344
datasets/dataset_dict.py,sha256=6A75yqSZrt7RHBvJr8vTZD_Kx9TmFZAaSn38EMcOR7k,104612
datasets/distributed.py,sha256=jZ31II0mmlPMhZbEtbAsX6jlK0U69qdpV3uS5U5JFYw,1560
datasets/download/__init__.py,sha256=lbFOtITDaR7PHrhzJ8VfRnpaOT6NYozSxUcLv_GVfTg,281
datasets/download/__pycache__/__init__.cpython-310.pyc,,
datasets/download/__pycache__/download_config.cpython-310.pyc,,
datasets/download/__pycache__/download_manager.cpython-310.pyc,,
datasets/download/__pycache__/mock_download_manager.cpython-310.pyc,,
datasets/download/__pycache__/streaming_download_manager.cpython-310.pyc,,
datasets/download/download_config.py,sha256=OBsZBXFKphFysU0eocStWryF3QGFY-9A96RCtxxIi0I,4770
datasets/download/download_manager.py,sha256=rpn82JZ5Vh2wzLTLclhcvUYG9TVZYopjuki-QcOEyQk,21735
datasets/download/mock_download_manager.py,sha256=jpMYk8SFjqnoR9J-8qqldQyKCtzjCnUXKPkSp3og7DY,10351
datasets/download/streaming_download_manager.py,sha256=CECTd_sp7G1Fcy1Huz8HW_LH1k60VXFM6sM137fvNgQ,44680
datasets/exceptions.py,sha256=vUgW0Ow6qTG1p1XhhUcoVHz71L7upBsNU6L_teiW5XU,3163
datasets/features/__init__.py,sha256=05nCoWgkpLzZ4xhsNY-uDEeUsTaPcSjbR6pK1RyYswk,447
datasets/features/__pycache__/__init__.cpython-310.pyc,,
datasets/features/__pycache__/audio.cpython-310.pyc,,
datasets/features/__pycache__/features.cpython-310.pyc,,
datasets/features/__pycache__/image.cpython-310.pyc,,
datasets/features/__pycache__/translation.cpython-310.pyc,,
datasets/features/audio.py,sha256=VT3fc5HdVocE-PUQ7ylsHSBSEzT-72IGGQgcf_Dc5tM,12244
datasets/features/features.py,sha256=vQP1i1LOdSeH0gbQ2gNZraC7_PEAy95qyjVJjLTKAaM,87872
datasets/features/image.py,sha256=Ck7Rm3OZOVnIuQPGMuMQ1NNR8yBbO0mk5zXyczGcw4g,15152
datasets/features/translation.py,sha256=J6jxAcAPakmMwtaHhHAhDENi1AgIGmeNn4neuEeFWYg,4476
datasets/filesystems/__init__.py,sha256=-g3bf9vXlylmdzGvp4z4CVJFAOp5clekwFXdAmGxy1s,2804
datasets/filesystems/__pycache__/__init__.cpython-310.pyc,,
datasets/filesystems/__pycache__/compression.cpython-310.pyc,,
datasets/filesystems/__pycache__/s3filesystem.cpython-310.pyc,,
datasets/filesystems/compression.py,sha256=Rl_E9w_OsERYgIGOVePLHI5k1mcU1cIR8wFkjW8N5cM,6100
datasets/filesystems/s3filesystem.py,sha256=KowTCvTSsrdAU4syiaRffNw4g25-DTbjsoXBIMWz2tk,5725
datasets/fingerprint.py,sha256=cknpN_OFB_b3zv4jOh2eO4Ql4mctj7vtqFz_dlw01Fo,21979
datasets/formatting/__init__.py,sha256=3oQaTX0DeV03KNYmzSuSUgxUfjDyrBsDt5e0iqJv4LU,5161
datasets/formatting/__pycache__/__init__.cpython-310.pyc,,
datasets/formatting/__pycache__/formatting.cpython-310.pyc,,
datasets/formatting/__pycache__/jax_formatter.cpython-310.pyc,,
datasets/formatting/__pycache__/np_formatter.cpython-310.pyc,,
datasets/formatting/__pycache__/tf_formatter.cpython-310.pyc,,
datasets/formatting/__pycache__/torch_formatter.cpython-310.pyc,,
datasets/formatting/formatting.py,sha256=Lhvtui0OLexe4O3MN84TtSkdE8f4GWExMw4MMyJN_yE,25756
datasets/formatting/jax_formatter.py,sha256=KoTbq0XSUQ1Rp3G5IzN3cU192JZ9t5HAZtHiVpHPbB4,6839
datasets/formatting/np_formatter.py,sha256=DJBnt3oF0fHWJCqe4j6o9BOupZ0uGrw_xxFfsGBVoyk,4525
datasets/formatting/tf_formatter.py,sha256=QRzeq8f1ALa6961PBNFRTH3RT4S-_8soqfUl9a7F89I,4657
datasets/formatting/torch_formatter.py,sha256=qbETKRaNFh5WNddjENvX6gEOYyf11ieC9sN9E75kMIQ,4252
datasets/info.py,sha256=vjq-BM5iIJV8CPGo4NeJmPPLV-P8rEOUHOG80Esa7DM,26530
datasets/inspect.py,sha256=Y6UM7Al2dKq4RsojscAb1dbTBgDxzihfglXuYisjaus,26262
datasets/io/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/io/__pycache__/__init__.cpython-310.pyc,,
datasets/io/__pycache__/abc.cpython-310.pyc,,
datasets/io/__pycache__/csv.cpython-310.pyc,,
datasets/io/__pycache__/generator.cpython-310.pyc,,
datasets/io/__pycache__/json.cpython-310.pyc,,
datasets/io/__pycache__/parquet.cpython-310.pyc,,
datasets/io/__pycache__/spark.cpython-310.pyc,,
datasets/io/__pycache__/sql.cpython-310.pyc,,
datasets/io/__pycache__/text.cpython-310.pyc,,
datasets/io/abc.py,sha256=LwDMXYs6YkhZuz1JiMK4PDIqgNjv7I8xH3UMUELW2ys,1672
datasets/io/csv.py,sha256=5KJNsnZXqrTp7SZijsGZhqY8A5VY8Qha9aSVWhJxfKk,5162
datasets/io/generator.py,sha256=LSjU2_JUHKgM1LGU_rQdIMifojG3XNqYVoFu8K8uH_s,1827
datasets/io/json.py,sha256=2fMlYMRCHVTqpgrY2WTQKvqzdmbU_l7MqyvepGML2X8,6092
datasets/io/parquet.py,sha256=hv7dOTmO05IyPm3DekZoVYMpkguicptHdtQmbxirnS0,5777
datasets/io/spark.py,sha256=VUIODLHgIbiK0CI0UvthQ_gUO0MQDtHUozvw7Dfs8FI,1797
datasets/io/sql.py,sha256=1do015qt-ETG5pEC-DcOvwSDBz-0JCkRu76lyjYxIDw,4281
datasets/io/text.py,sha256=5XboSqdtjRNBfkgi8hFjKrp1pp6hwiaiQJqwVMvGvX0,2026
datasets/iterable_dataset.py,sha256=yRaQKDAtON0DuKWLlf3nH6owQOfr5M9AAJCtUsKgV7Y,107931
datasets/keyhash.py,sha256=gZLJ-0lIaj5mXP3fm0zFz8oY9L3Qu_OMkgil06oq0eg,3872
datasets/load.py,sha256=bgJPNisa357OcvmOSIWvJHk2nqAtEj0wnLOpG0LRBfk,123214
datasets/metric.py,sha256=brvPsU0gM3Ii6TrTD8W_nFolUXUryCHpcZ3QJgepc78,28065
datasets/naming.py,sha256=QIjMryT3PM3A-IQq_7yVECGHSMSzWPN7wTA5g99M1Fc,3000
datasets/packaged_modules/__init__.py,sha256=D-99_JOlK8KmLv454LFJirjvDyTiqta10DXoq3I7zj4,2917
datasets/packaged_modules/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/arrow/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/arrow/__pycache__/arrow.cpython-310.pyc,,
datasets/packaged_modules/arrow/arrow.py,sha256=MK8wKbYUErZqeCoq9ZDVMhN5DOX94k5wGMgsM-ONstY,3317
datasets/packaged_modules/audiofolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/audiofolder/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/audiofolder/__pycache__/audiofolder.cpython-310.pyc,,
datasets/packaged_modules/audiofolder/audiofolder.py,sha256=BXRlK57KvYdyEo-L-Qs6qtrG2tL0QUF0cmJvl6L1N-w,1633
datasets/packaged_modules/cache/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/cache/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/cache/__pycache__/cache.cpython-310.pyc,,
datasets/packaged_modules/cache/cache.py,sha256=8sDLgUuuhxSi462fCqgvKqFNmvoMH_g68PgQUOlrZxc,6240
datasets/packaged_modules/csv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/csv/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/csv/__pycache__/csv.cpython-310.pyc,,
datasets/packaged_modules/csv/csv.py,sha256=0fF5XJqcRWwZ8FqNS16H8bOkEth6FZcsIBZokhEPMRc,8388
datasets/packaged_modules/folder_based_builder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/folder_based_builder/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/folder_based_builder/__pycache__/folder_based_builder.cpython-310.pyc,,
datasets/packaged_modules/folder_based_builder/folder_based_builder.py,sha256=CSn0UY8qubyYauO47l553O3FWCBhvdyI_O8yaHICanc,22449
datasets/packaged_modules/generator/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/generator/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/generator/__pycache__/generator.cpython-310.pyc,,
datasets/packaged_modules/generator/generator.py,sha256=QZKrNB3ztWPXT_H5OFOl1CBlAlAeckW48kdyySyVVKw,928
datasets/packaged_modules/imagefolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/imagefolder/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/imagefolder/__pycache__/imagefolder.cpython-310.pyc,,
datasets/packaged_modules/imagefolder/imagefolder.py,sha256=SYu6yxe4iBZzclT7u3m0gaACa6udSi1YOfFSy7dzdwk,1975
datasets/packaged_modules/json/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/json/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/json/__pycache__/json.cpython-310.pyc,,
datasets/packaged_modules/json/json.py,sha256=Jei-FP1xNb3ysUgZQ6Mf_426sXrgZrUjP8q4xuzzWWo,9331
datasets/packaged_modules/pandas/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/pandas/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/pandas/__pycache__/pandas.cpython-310.pyc,,
datasets/packaged_modules/pandas/pandas.py,sha256=TRgC7LkwmUq0dThVuMVblX8wlyY4lvTExSIvzyUyV1w,2302
datasets/packaged_modules/parquet/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/parquet/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/parquet/__pycache__/parquet.cpython-310.pyc,,
datasets/packaged_modules/parquet/parquet.py,sha256=7izzvueyLPaptT_PT1rlPecHQCw0sur2Xvn-chdoIpo,4623
datasets/packaged_modules/spark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/spark/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/spark/__pycache__/spark.cpython-310.pyc,,
datasets/packaged_modules/spark/spark.py,sha256=7z8KuKSRVxvmdNekgAVWC5ULP3OFR-iUdXhhkLOF-kU,13916
datasets/packaged_modules/sql/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/sql/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/sql/__pycache__/sql.cpython-310.pyc,,
datasets/packaged_modules/sql/sql.py,sha256=Fcnok2-1uX2XnQah4BrtE5SPli6O3JKb9tzMy1lachk,4482
datasets/packaged_modules/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/text/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/text/__pycache__/text.cpython-310.pyc,,
datasets/packaged_modules/text/text.py,sha256=RXoZlE1Go08KXgo4RPX1GW0ads1a-6iz1QRi1c66OZg,6260
datasets/packaged_modules/webdataset/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/webdataset/__pycache__/__init__.cpython-310.pyc,,
datasets/packaged_modules/webdataset/__pycache__/_tenbin.cpython-310.pyc,,
datasets/packaged_modules/webdataset/__pycache__/webdataset.cpython-310.pyc,,
datasets/packaged_modules/webdataset/_tenbin.py,sha256=oovYsgR2R3eXSn1xSCLG3oTly1szKDP4UOiRp4ORdIk,8533
datasets/packaged_modules/webdataset/webdataset.py,sha256=WwjmwgVmj-OoZuzUyPGsSEjgRPluwT48I6inSbdeLL8,6992
datasets/parallel/__init__.py,sha256=dEhpBOLbCcsKClTXYJnJRp-ZtrfUV6jsH-CYqviXl-E,89
datasets/parallel/__pycache__/__init__.cpython-310.pyc,,
datasets/parallel/__pycache__/parallel.cpython-310.pyc,,
datasets/parallel/parallel.py,sha256=POcwLCtYmusW6vpop_UrQYP7OInhOSY978PP0ZOVimM,4553
datasets/search.py,sha256=5y99SIZzSpIObL8X9LJRzr1zUuNDFXphSwyWr6kh7uc,35345
datasets/splits.py,sha256=7nZtos2No9P_SfqnsLqGUs48TQ6Syogl3BbLhRyKLn4,23595
datasets/streaming.py,sha256=ZMSHc9p4dT5wj9NY-71XUCoKDaOKHTpTQDASNnb_ZJM,6423
datasets/table.py,sha256=R88j-wWuKhnmIglv8usHVYRxNYwKF5CTO3oN9YQ0FB4,96939
datasets/tasks/__init__.py,sha256=ExUcieN-G7_ybwfGqi3-Kt3akv3hrnlCq_RwEosFhiY,1614
datasets/tasks/__pycache__/__init__.cpython-310.pyc,,
datasets/tasks/__pycache__/audio_classification.cpython-310.pyc,,
datasets/tasks/__pycache__/automatic_speech_recognition.cpython-310.pyc,,
datasets/tasks/__pycache__/base.cpython-310.pyc,,
datasets/tasks/__pycache__/image_classification.cpython-310.pyc,,
datasets/tasks/__pycache__/language_modeling.cpython-310.pyc,,
datasets/tasks/__pycache__/question_answering.cpython-310.pyc,,
datasets/tasks/__pycache__/summarization.cpython-310.pyc,,
datasets/tasks/__pycache__/text_classification.cpython-310.pyc,,
datasets/tasks/audio_classification.py,sha256=fkR37qfJfJRPgCizf9iDV-dBnsGmLo2V0w8JpMwyX0M,1297
datasets/tasks/automatic_speech_recognition.py,sha256=zbTTsLX5N-_Da5oucuk6zBZhDdhD4N5_rzsni9lT_vo,1309
datasets/tasks/base.py,sha256=SlYEeDS87jruZNNkDRgz-U4q7EUijePL-RTN14ngwsk,1095
datasets/tasks/image_classification.py,sha256=llF5_koN5APq7cF_WlGy5c9hAVspRlYCprXgwAa7kCc,1297
datasets/tasks/language_modeling.py,sha256=Vdor-TdCGdiMpaIPZr0fRvgNrt5_D-1JElXKGbfQhvI,581
datasets/tasks/question_answering.py,sha256=z8a80QRTsouUuIYVKQRDMTxOGeSK1QMycyDHxUW42zg,1105
datasets/tasks/summarization.py,sha256=adrpmvgfAjXCyDRdZnZ52h0FKql5-EWU61Z2-v6rN-w,772
datasets/tasks/text_classification.py,sha256=KvlddXxnnzzjCjJmyY3Z-e1G4dpTN0UXqlmZ1L0LrjU,1403
datasets/utils/__init__.py,sha256=T0reKVlw1xdG_wjF7ZqbsGVu5Cd70Z3QWBTkPU2TBXg,1034
datasets/utils/__pycache__/__init__.cpython-310.pyc,,
datasets/utils/__pycache__/_datasets_server.cpython-310.pyc,,
datasets/utils/__pycache__/_dill.cpython-310.pyc,,
datasets/utils/__pycache__/_filelock.cpython-310.pyc,,
datasets/utils/__pycache__/beam_utils.cpython-310.pyc,,
datasets/utils/__pycache__/deprecation_utils.cpython-310.pyc,,
datasets/utils/__pycache__/doc_utils.cpython-310.pyc,,
datasets/utils/__pycache__/download_manager.cpython-310.pyc,,
datasets/utils/__pycache__/experimental.cpython-310.pyc,,
datasets/utils/__pycache__/extract.cpython-310.pyc,,
datasets/utils/__pycache__/file_utils.cpython-310.pyc,,
datasets/utils/__pycache__/filelock.cpython-310.pyc,,
datasets/utils/__pycache__/hub.cpython-310.pyc,,
datasets/utils/__pycache__/info_utils.cpython-310.pyc,,
datasets/utils/__pycache__/logging.cpython-310.pyc,,
datasets/utils/__pycache__/metadata.cpython-310.pyc,,
datasets/utils/__pycache__/patching.cpython-310.pyc,,
datasets/utils/__pycache__/py_utils.cpython-310.pyc,,
datasets/utils/__pycache__/readme.cpython-310.pyc,,
datasets/utils/__pycache__/sharding.cpython-310.pyc,,
datasets/utils/__pycache__/stratify.cpython-310.pyc,,
datasets/utils/__pycache__/tf_utils.cpython-310.pyc,,
datasets/utils/__pycache__/tqdm.cpython-310.pyc,,
datasets/utils/__pycache__/track.cpython-310.pyc,,
datasets/utils/__pycache__/typing.cpython-310.pyc,,
datasets/utils/__pycache__/version.cpython-310.pyc,,
datasets/utils/_datasets_server.py,sha256=y_2alNAyiL-Pd5pu2Sl8rlYxDOfp6iyZWXDeoEK0bB0,4440
datasets/utils/_dill.py,sha256=lyU8HqEWMvynRIuKFEzuNAsX-tKiJEhMhCt2wh9TEhM,16821
datasets/utils/_filelock.py,sha256=krXGrO350Py9bgHE9ptkcWT2jOGs7wYgNHqrc1y1owE,1804
datasets/utils/beam_utils.py,sha256=DvA0ZVrx4-T9iHpB9VpduKn435p4rFaJw0Ua5cKmpeI,2029
datasets/utils/deprecation_utils.py,sha256=hTHwlzRs92NfNVudH71LMpW70sjbsP5amebrIgi3A-U,3452
datasets/utils/doc_utils.py,sha256=HoSm0TFaQaCYGfDgNhpBJ4Xc2WQZuOD6dTxLd9D87fs,407
datasets/utils/download_manager.py,sha256=AXDA-dUNUOmmy4Z7e8A34BJtQPcbJhWSQuO4p5wnDWY,60
datasets/utils/experimental.py,sha256=hsTzoXR2lnVpOlRIsgrSTS0iiUhAJAwl7d2xN04N3hc,1096
datasets/utils/extract.py,sha256=URTa1Gv3aDN_NSaLYiv-l1wFXsRt2QVKhTURmV-yIEU,14195
datasets/utils/file_utils.py,sha256=3KUmXH9KpSwT4U4rgULd9cUnU3IVGIReVU1NwmchFjU,25338
datasets/utils/filelock.py,sha256=H6C5dQGFCzVKyeDRRY8fZ4YGTEvvNd-MTjpL_sWYb5k,352
datasets/utils/hub.py,sha256=QY-rfK4oA7ipwygrO3_x6RMwjrj6P14yN8buO7T2zII,2288
datasets/utils/info_utils.py,sha256=uadj74BSn08F75wNanZkf-7z3Yo6aVoFNO8zMkJfmkk,5050
datasets/utils/logging.py,sha256=Pe9ZBBiQDBgJoMQ8SWW3SUuq1S5gakfi96KdwW4Trm8,5406
datasets/utils/metadata.py,sha256=4-9OGC-m8vnSVY3DssEEckXGnN-qQgtBbLQwymZbmpE,12217
datasets/utils/patching.py,sha256=iTeb7XG4faLJKNylq55EcZyCndUXU_XBDvOOkuDz_sc,4955
datasets/utils/py_utils.py,sha256=-TERjEX5sh4G3Ip9cMznS6MvExUHVA-slqnmDmourNw,24245
datasets/utils/readme.py,sha256=JFlaLMCGrIz0nQCdnYKUZk5d9D9DErEYfjtRrX9VzIw,12627
datasets/utils/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/utils/resources/__pycache__/__init__.cpython-310.pyc,,
datasets/utils/resources/creators.json,sha256=XtIpMZefgBOdTevRrQTkFiufbgCbp_iyxseyphYQkn0,257
datasets/utils/resources/languages.json,sha256=Z0rQNPsfje8zMi8KdvvwxF4APwwqcskJFUvhNiLAgPM,199138
datasets/utils/resources/multilingualities.json,sha256=02Uc8RtRzfl13l98Y_alZm5HuMYwPzL78B0S5a1X-8c,205
datasets/utils/resources/readme_structure.yaml,sha256=hNf9msoBZw5jfakQrDb0Af8T325TXdcaHsAO2MUcZvY,3877
datasets/utils/resources/size_categories.json,sha256=_5nAP7z8R6t7_GfER81QudFO6Y1tqYu4AWrr4Aot8S8,171
datasets/utils/sharding.py,sha256=FDi895opKH7XkpfIu-ag9PqBQo2PGx0tSO3Dg-gDAAs,4288
datasets/utils/stratify.py,sha256=uMwuCDRbW342vy-lXDHs6IQusOr7c9nOG3PpnWyzJO4,4091
datasets/utils/tf_utils.py,sha256=YWmXP525b-kp1A-pnF-rGEOKqmg0Dv5j8RXJieSbkZc,25044
datasets/utils/tqdm.py,sha256=D-ZP_s5kPSX-MewLL1p_wPFQwd8rchLye4gklwDQhQw,4302
datasets/utils/track.py,sha256=k0HYom1uxmEvYdIOMei6ie39mrrOBmf-Qd0HAYVnfag,1407
datasets/utils/typing.py,sha256=LznosIqUzjXgwbRLAGCv4_7-yZo7muYY42Y3495oz5I,224
datasets/utils/version.py,sha256=Z82cHpjTbQVJyWgnwSU8DsW2G0y-sSbSoOVeQrAds9k,3281
