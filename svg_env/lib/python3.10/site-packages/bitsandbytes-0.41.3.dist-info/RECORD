bitsandbytes-0.41.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bitsandbytes-0.41.3.dist-info/LICENSE,sha256=UkEte8fOQVfqYou6rLiCngqcs8WPV_mRdhJryM8r_IU,1086
bitsandbytes-0.41.3.dist-info/METADATA,sha256=tQHBufuBwoDISo-JuBj71X9HjrSf9R_0kEnk56q7vv8,9810
bitsandbytes-0.41.3.dist-info/NOTICE.md,sha256=_4zDL2L8BqUwtmvoznR_wqhQmsP2QwdXHrAHnBMzAl8,265
bitsandbytes-0.41.3.dist-info/RECORD,,
bitsandbytes-0.41.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes-0.41.3.dist-info/WHEEL,sha256=oiQVh_5PnQM0E3gPdiz09WCNmwiHDMaGer_elqB3coM,92
bitsandbytes-0.41.3.dist-info/top_level.txt,sha256=RttH1rYsSQjh-S6_y8rqF6hmKVVQ-cigSmKp5lBdKk4,19
bitsandbytes/__init__.py,sha256=mQQknbw8xSpKDtEJgVEiyCemE4HaB-FtAddxY2-Uyhc,670
bitsandbytes/__main__.py,sha256=rWjs6LsifG_Vglj3WM4brY2IOCjwKpAjuBP3OIzYFPU,4014
bitsandbytes/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/__pycache__/__main__.cpython-310.pyc,,
bitsandbytes/__pycache__/cextension.cpython-310.pyc,,
bitsandbytes/__pycache__/functional.cpython-310.pyc,,
bitsandbytes/__pycache__/utils.cpython-310.pyc,,
bitsandbytes/autograd/__init__.py,sha256=Ltb59FJrcWYVsTfGW6SscEZtiDhHZe7EFrYnIhnASug,67
bitsandbytes/autograd/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/autograd/__pycache__/_functions.cpython-310.pyc,,
bitsandbytes/autograd/_functions.py,sha256=ueNhElKDD9Q0eSjLeHRUyctCfb3gfyaGu0MTr_PmfRM,22315
bitsandbytes/cextension.py,sha256=klJwL-8ZPylUOETDTW-fvUbZ_Bt_rdB6wRDND1fB_wk,1635
bitsandbytes/cuda_setup/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/cuda_setup/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/cuda_setup/__pycache__/env_vars.cpython-310.pyc,,
bitsandbytes/cuda_setup/__pycache__/main.cpython-310.pyc,,
bitsandbytes/cuda_setup/env_vars.py,sha256=vlj3mxBY0-XytWIq6CaCS_F2DvowKDMCFq3ytkQTYYY,1739
bitsandbytes/cuda_setup/main.py,sha256=o9YcJj87_t1yADdrMWY0c_XQRyX_8t3XGjwiERKtaVk,17946
bitsandbytes/functional.py,sha256=Rr0CyzLtEHCpShG5URUWYE0a_u1HMfzfEaEfdAzbgF0,85523
bitsandbytes/libbitsandbytes_cpu.so,sha256=nejNfivapxN6MN_bJxFfR423YImIeqNVhXdts2BcDR8,41608
bitsandbytes/libbitsandbytes_cuda110.so,sha256=1NM_-9xHfCz2djWods0YXQcDKITkX3KSJfklrUESkKw,5938904
bitsandbytes/libbitsandbytes_cuda110_nocublaslt.so,sha256=q_1Zn2FlCd6LaXYwjkDrE_rq0lFuNwDjGBJlWM_Nufg,11110784
bitsandbytes/libbitsandbytes_cuda111.so,sha256=JBLZ6wBWB5x1DasFqxcog59xxks5XHzLAdQFGZjCiDY,8974040
bitsandbytes/libbitsandbytes_cuda111_nocublaslt.so,sha256=1qsndcAVNcCz-LcXytWYx81hPJgifIgNDw1MSx81ays,20244864
bitsandbytes/libbitsandbytes_cuda114.so,sha256=kh0dVhz5EoSIcpFoRt9vB9rtMSYayFrT1uQmDAP_nCI,9313912
bitsandbytes/libbitsandbytes_cuda114_nocublaslt.so,sha256=7BfmpKsEYpxamIB7a9WhjhXN7FC1o0FpyqO8IXu1Ep4,20973856
bitsandbytes/libbitsandbytes_cuda115.so,sha256=ncH3CjlEB0fyXvvj9my_SkUyfGwj_FVo4D-adRX63Gs,9310152
bitsandbytes/libbitsandbytes_cuda115_nocublaslt.so,sha256=1vB8bV-E6pXTKZzOmfxFWiz3l7LrtQuSAh9n33oY1hM,20925040
bitsandbytes/libbitsandbytes_cuda117.so,sha256=bEkYZLxEKQZvsu3Agy-aDcIC2ZqQ8B6JDBHL2n1Osq0,9117944
bitsandbytes/libbitsandbytes_cuda117_nocublaslt.so,sha256=jqc_QsosEBzjd7cNFNA-6QG5e1GGG1cLfEoh7d23zxA,20741032
bitsandbytes/libbitsandbytes_cuda118.so,sha256=g-e5x_TTutfHSyKvaBqHXvCGb8NjIDo3c5mjzevTyUA,14918184
bitsandbytes/libbitsandbytes_cuda118_nocublaslt.so,sha256=GaYqo8N7cNkxbAhI-dizyyBbuOqbEbNRR0nyh8LIWW4,26516696
bitsandbytes/libbitsandbytes_cuda120.so,sha256=1olVGrA_Frm3ZzYaUxDKRyeWXbJlTTWhlPjO1a0il_o,14504296
bitsandbytes/libbitsandbytes_cuda120_nocublaslt.so,sha256=VUXyIHZb4V6-SOGPVPWVHyeKafG9xQPLEQIelTh69Oo,25709592
bitsandbytes/libbitsandbytes_cuda121.so,sha256=XRKDct-9s0poQp0sNFSgdvrGUMed2lRror6aVBU3hGM,14512488
bitsandbytes/libbitsandbytes_cuda121_nocublaslt.so,sha256=YeYH36m5h2N7tULUoZ8Gt-CAfb8szLDPW5m9OLAQFAE,25721880
bitsandbytes/libbitsandbytes_cuda122.so,sha256=FrhXhmfraDbGt5I6OzUI1igJ5OkUKWdKDDq5fPYMU0k,14561032
bitsandbytes/libbitsandbytes_cuda122_nocublaslt.so,sha256=WPSiBD_ozuUsk_aRdoJd5XVTcnpannmEmR6yok2mZTA,25803272
bitsandbytes/nn/__init__.py,sha256=i-gJR2uQrRvn8zZCZcS1KC0SbsUqCKTta4aV7HXZTT4,446
bitsandbytes/nn/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/nn/__pycache__/modules.cpython-310.pyc,,
bitsandbytes/nn/__pycache__/triton_based_modules.cpython-310.pyc,,
bitsandbytes/nn/modules.py,sha256=F_Zp0uB8HMc88gz8Ux3zsaAL01zGj8ZhUUr8vAIG_YU,21014
bitsandbytes/nn/triton_based_modules.py,sha256=eMEldLd7GX0Dc3dzX0XZpfgzofBPRAi-z1NXf84wCPs,9843
bitsandbytes/optim/__init__.py,sha256=TSl80yMFkwGBl8N0FBFcfBLt2vt4cZn-hbkuwHGuCUE,794
bitsandbytes/optim/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/adagrad.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/adam.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/adamw.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/lamb.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/lars.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/lion.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/optimizer.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/rmsprop.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/sgd.cpython-310.pyc,,
bitsandbytes/optim/adagrad.py,sha256=E4KsNJKOB2VfgkyKEoeYwFFXnedsxHZItdfzwc5_cdE,3719
bitsandbytes/optim/adam.py,sha256=nHHvXoeiAuosn4a9VWI3Z7_XmvYC6bOHb8en6mxiwkA,12776
bitsandbytes/optim/adamw.py,sha256=byibv4xoBM7FUK8FScRTx2KbI4-2Mi0yB8WJCb2x3wE,2699
bitsandbytes/optim/lamb.py,sha256=hfH4H9eVAHcbjL04DAI_lcPD1OPAmcY4_myow-o21aw,2313
bitsandbytes/optim/lars.py,sha256=PeUB8RlfaRtHEa-ZZZkrKDdmkHa7XEEfU81irU-mKsY,5653
bitsandbytes/optim/lion.py,sha256=jANwqVZSAxNZnoqi_OQ9XG8hKa6e84mkwJ9CchtpLHs,2304
bitsandbytes/optim/optimizer.py,sha256=219zPzx9dpeY0VndzlXt6jn2yV9sEiSXkrxe26wXjIo,25167
bitsandbytes/optim/rmsprop.py,sha256=1zGT9JIZh214fbBZ-CTirVKk1rQxSZe-BRJzhRtYL2U,2785
bitsandbytes/optim/sgd.py,sha256=YHVUeEkwxgYx_0GhH0Et6fCpk7rfhboDR2F06jRWz4E,2340
bitsandbytes/research/__init__.py,sha256=_MilJdwSRWObRfzzy14WD6HsJa6okT4d5YxH4aB9zg4,119
bitsandbytes/research/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/research/autograd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/research/autograd/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/research/autograd/__pycache__/_functions.cpython-310.pyc,,
bitsandbytes/research/autograd/_functions.py,sha256=k72rcf4hT3M5GOpGoijWkpTAqjRNoecGlOHmTTn3n80,15874
bitsandbytes/research/nn/__init__.py,sha256=j5XA_2ZA6efMtcbuUCyegfCLkDDQuL3ix5xS4yKZayY,53
bitsandbytes/research/nn/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/research/nn/__pycache__/modules.cpython-310.pyc,,
bitsandbytes/research/nn/modules.py,sha256=EnI2qVTosAMkH4G1fQleA0zvm8dZR9G-GJ4pFDo8V9M,2357
bitsandbytes/triton/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/triton/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/dequantize_rowwise.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_mixed_dequanitze.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_rowwise_dequantize.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/quantize_columnwise_and_transpose.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/quantize_global.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/quantize_rowwise.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/triton_utils.cpython-310.pyc,,
bitsandbytes/triton/dequantize_rowwise.py,sha256=qdh3f4O53faM6SFT_aYvrytWF_FQW3q2bhBll6Uwfc4,2193
bitsandbytes/triton/int8_matmul_mixed_dequanitze.py,sha256=QJ_hrZ94ZthnoPD0TCp5ZCPAMkxNNQQY-UNg50TWwHo,8256
bitsandbytes/triton/int8_matmul_rowwise_dequantize.py,sha256=EMiY3nfx0LIvYEGUqtzcfUonQxwoDcppYli9Qd6kViw,8240
bitsandbytes/triton/quantize_columnwise_and_transpose.py,sha256=K2fFegPtSsi2tgKxb5goO8YpUmQ6wgTvsXabgTRAFNI,2749
bitsandbytes/triton/quantize_global.py,sha256=5in9Plx1Kgf6Nx5B1RBXCiJnb0G4qwraGADNiq1LtVc,3957
bitsandbytes/triton/quantize_rowwise.py,sha256=sraX6TMubZQGiG9Gyh0UFzK823e_TkXZk9R1BILJdPU,2331
bitsandbytes/triton/triton_utils.py,sha256=f7CP_3lvUoTQJ-xSp4wAfiU8uX_trtGdUsoLzlcsHQY,103
bitsandbytes/utils.py,sha256=NomhCXFSFwHDfdPcjhFu63lUh5mLXaZfy6mOWcOJ2Ng,6589
tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tests/__pycache__/__init__.cpython-310.pyc,,
tests/__pycache__/test_autograd.cpython-310.pyc,,
tests/__pycache__/test_cuda_setup_evaluator.cpython-310.pyc,,
tests/__pycache__/test_functional.cpython-310.pyc,,
tests/__pycache__/test_generation.cpython-310.pyc,,
tests/__pycache__/test_linear4bit.cpython-310.pyc,,
tests/__pycache__/test_linear8bitlt.cpython-310.pyc,,
tests/__pycache__/test_modules.cpython-310.pyc,,
tests/__pycache__/test_optim.cpython-310.pyc,,
tests/__pycache__/test_triton.cpython-310.pyc,,
tests/test_autograd.py,sha256=k-Y4fhcWf1TrwzAnkFVUJV5_W89SE0tZuN3aWW20sYQ,23412
tests/test_cuda_setup_evaluator.py,sha256=u8ffT5q9ctNpQiPsDsHimGmD7Og-jzJ-ddg0featFiY,638
tests/test_functional.py,sha256=zDfKPVijfmaBoSw6PNUaowoLUYwntjNhlXMbakOS2fg,87704
tests/test_generation.py,sha256=3NWgcJbSX7O2tYThYO-VxOUNgTzlloCezDUCxSRbwxc,4401
tests/test_linear4bit.py,sha256=Yvgqwo7D5ES7pRjKTMwVwwJVJKnnrqQCa9jcgn5jU24,3866
tests/test_linear8bitlt.py,sha256=3-KFQ2zfMczzrEV9d6nN8_aPuEAWwjU26YQhZo58BAs,5415
tests/test_modules.py,sha256=kB0EUUUubOQLZwgPyn_5zQqzsKl7dJ3lUaDDnHuo-xs,23184
tests/test_optim.py,sha256=AVAKl8Ah_OxIc8Vo9cbymgj68pxJeXCz7N335Tlazxc,21111
tests/test_triton.py,sha256=Zekyy8LHj_sTmBAEjMKXzDPmzmLaMEpUQTz7aa5cH4g,2531
