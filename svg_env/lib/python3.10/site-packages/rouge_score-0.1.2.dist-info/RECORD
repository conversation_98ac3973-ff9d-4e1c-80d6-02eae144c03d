rouge_score-0.1.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
rouge_score-0.1.2.dist-info/METADATA,sha256=7WDvKtmt7kSlGmL7EtYtGazIMH5kgWesPYy6l3DtApk,4186
rouge_score-0.1.2.dist-info/RECORD,,
rouge_score-0.1.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
rouge_score-0.1.2.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
rouge_score-0.1.2.dist-info/top_level.txt,sha256=xXbfZGc99vE5G15chRRzRXSrJ1Mc065jCgs8XWnnOKk,12
rouge_score/__init__.py,sha256=fQB5BePjj8ELQYIc9PSYB3MiCOaD3siy_0isHEssvJE,590
rouge_score/__pycache__/__init__.cpython-310.pyc,,
rouge_score/__pycache__/create_pyrouge_files.cpython-310.pyc,,
rouge_score/__pycache__/io.cpython-310.pyc,,
rouge_score/__pycache__/io_test.cpython-310.pyc,,
rouge_score/__pycache__/rouge.cpython-310.pyc,,
rouge_score/__pycache__/rouge_scorer.cpython-310.pyc,,
rouge_score/__pycache__/rouge_scorer_test.cpython-310.pyc,,
rouge_score/__pycache__/scoring.cpython-310.pyc,,
rouge_score/__pycache__/scoring_test.cpython-310.pyc,,
rouge_score/__pycache__/test_util.cpython-310.pyc,,
rouge_score/__pycache__/tokenize.cpython-310.pyc,,
rouge_score/__pycache__/tokenize_test.cpython-310.pyc,,
rouge_score/__pycache__/tokenizers.cpython-310.pyc,,
rouge_score/__pycache__/tokenizers_test.cpython-310.pyc,,
rouge_score/create_pyrouge_files.py,sha256=_GtmIniVeVjSWtBMoZIBbeH6XMunz1a3bjsn1DmGboA,2492
rouge_score/io.py,sha256=tQdxk0ICl0gV3BeXp0-lQuQX-CpIPUU__ZdCDrUs8F8,6892
rouge_score/io_test.py,sha256=-NKUTtsUR0aSE6nxPcAW707a2YIJf2FcwOn48l_Va0k,3637
rouge_score/rouge.py,sha256=1KJx8Yrw7qQRNpmK_XCtAfNhMOvtZkOFVCRznTuIqtg,3101
rouge_score/rouge_scorer.py,sha256=lITF_QXiLNKLUFO_neWGs2IM9lsMOAUrhpC63VHzHRo,10707
rouge_score/rouge_scorer_test.py,sha256=ODscYxBy3M05gyH1AeOn39id1RqCmbQuYh_SyyTtkNo,13589
rouge_score/scoring.py,sha256=s_wVNJnkhGZSlN2664dkUrQKj6ISDoJLgEkoyW4_LBo,5712
rouge_score/scoring_test.py,sha256=ZOdRwJnaDT_0k0xfGQovIBr1X-1G0u-vxAMFTOZOIjY,7850
rouge_score/test_util.py,sha256=ka6AzXzzCWOEBUb4nfKQZgG3k7ygJ6z2Xkm0bHSutTw,1291
rouge_score/tokenize.py,sha256=3JHOqPCVB_dEVJFgxFgDHQlW41-iMMgNAfmQ66IKdAM,1882
rouge_score/tokenize_test.py,sha256=mkwTMrvsnE08T7P8PnzS_Skl2aYUpEKmA37wSTXRwLI,1137
rouge_score/tokenizers.py,sha256=K3udrlBc6HOQZLqOR5G4cbE6ofZbxU4laS8am6hgBQg,1648
rouge_score/tokenizers_test.py,sha256=if3Xx_--SdJywmYbYQEWi3NMigQrPpzjQ7qneNINcbM,1375
