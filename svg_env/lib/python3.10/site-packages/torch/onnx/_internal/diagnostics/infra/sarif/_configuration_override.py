# DO NOT EDIT! This file was generated by jschema_to_python version 0.0.1.dev29,
# with extension for dataclasses and type annotation.

from __future__ import annotations

import dataclasses
from typing import Optional

from torch.onnx._internal.diagnostics.infra.sarif import (
    _property_bag,
    _reporting_configuration,
    _reporting_descriptor_reference,
)


@dataclasses.dataclass
class ConfigurationOverride(object):
    """Information about how a specific rule or notification was reconfigured at runtime."""

    configuration: _reporting_configuration.ReportingConfiguration = dataclasses.field(
        metadata={"schema_property_name": "configuration"}
    )
    descriptor: _reporting_descriptor_reference.ReportingDescriptorReference = (
        dataclasses.field(metadata={"schema_property_name": "descriptor"})
    )
    properties: Optional[_property_bag.PropertyBag] = dataclasses.field(
        default=None, metadata={"schema_property_name": "properties"}
    )


# flake8: noqa
