# DO NOT EDIT! This file was generated by jschema_to_python version 0.0.1.dev29,
# with extension for dataclasses and type annotation.

from torch.onnx._internal.diagnostics.infra.sarif._address import Address
from torch.onnx._internal.diagnostics.infra.sarif._artifact import Artifact
from torch.onnx._internal.diagnostics.infra.sarif._artifact_change import ArtifactChange
from torch.onnx._internal.diagnostics.infra.sarif._artifact_content import (
    ArtifactContent,
)
from torch.onnx._internal.diagnostics.infra.sarif._artifact_location import (
    ArtifactLocation,
)
from torch.onnx._internal.diagnostics.infra.sarif._attachment import Attachment
from torch.onnx._internal.diagnostics.infra.sarif._code_flow import CodeFlow
from torch.onnx._internal.diagnostics.infra.sarif._configuration_override import (
    ConfigurationOverride,
)
from torch.onnx._internal.diagnostics.infra.sarif._conversion import Conversion
from torch.onnx._internal.diagnostics.infra.sarif._edge import Edge
from torch.onnx._internal.diagnostics.infra.sarif._edge_traversal import EdgeTraversal
from torch.onnx._internal.diagnostics.infra.sarif._exception import Exception
from torch.onnx._internal.diagnostics.infra.sarif._external_properties import (
    ExternalProperties,
)
from torch.onnx._internal.diagnostics.infra.sarif._external_property_file_reference import (
    ExternalPropertyFileReference,
)
from torch.onnx._internal.diagnostics.infra.sarif._external_property_file_references import (
    ExternalPropertyFileReferences,
)
from torch.onnx._internal.diagnostics.infra.sarif._fix import Fix
from torch.onnx._internal.diagnostics.infra.sarif._graph import Graph
from torch.onnx._internal.diagnostics.infra.sarif._graph_traversal import GraphTraversal
from torch.onnx._internal.diagnostics.infra.sarif._invocation import Invocation
from torch.onnx._internal.diagnostics.infra.sarif._location import Location
from torch.onnx._internal.diagnostics.infra.sarif._location_relationship import (
    LocationRelationship,
)
from torch.onnx._internal.diagnostics.infra.sarif._logical_location import (
    LogicalLocation,
)
from torch.onnx._internal.diagnostics.infra.sarif._message import Message
from torch.onnx._internal.diagnostics.infra.sarif._multiformat_message_string import (
    MultiformatMessageString,
)
from torch.onnx._internal.diagnostics.infra.sarif._node import Node
from torch.onnx._internal.diagnostics.infra.sarif._notification import Notification
from torch.onnx._internal.diagnostics.infra.sarif._physical_location import (
    PhysicalLocation,
)
from torch.onnx._internal.diagnostics.infra.sarif._property_bag import PropertyBag
from torch.onnx._internal.diagnostics.infra.sarif._rectangle import Rectangle
from torch.onnx._internal.diagnostics.infra.sarif._region import Region
from torch.onnx._internal.diagnostics.infra.sarif._replacement import Replacement
from torch.onnx._internal.diagnostics.infra.sarif._reporting_configuration import (
    ReportingConfiguration,
)
from torch.onnx._internal.diagnostics.infra.sarif._reporting_descriptor import (
    ReportingDescriptor,
)
from torch.onnx._internal.diagnostics.infra.sarif._reporting_descriptor_reference import (
    ReportingDescriptorReference,
)
from torch.onnx._internal.diagnostics.infra.sarif._reporting_descriptor_relationship import (
    ReportingDescriptorRelationship,
)
from torch.onnx._internal.diagnostics.infra.sarif._result import Result
from torch.onnx._internal.diagnostics.infra.sarif._result_provenance import (
    ResultProvenance,
)
from torch.onnx._internal.diagnostics.infra.sarif._run import Run
from torch.onnx._internal.diagnostics.infra.sarif._run_automation_details import (
    RunAutomationDetails,
)
from torch.onnx._internal.diagnostics.infra.sarif._sarif_log import SarifLog
from torch.onnx._internal.diagnostics.infra.sarif._special_locations import (
    SpecialLocations,
)
from torch.onnx._internal.diagnostics.infra.sarif._stack import Stack
from torch.onnx._internal.diagnostics.infra.sarif._stack_frame import StackFrame
from torch.onnx._internal.diagnostics.infra.sarif._suppression import Suppression
from torch.onnx._internal.diagnostics.infra.sarif._thread_flow import ThreadFlow
from torch.onnx._internal.diagnostics.infra.sarif._thread_flow_location import (
    ThreadFlowLocation,
)
from torch.onnx._internal.diagnostics.infra.sarif._tool import Tool
from torch.onnx._internal.diagnostics.infra.sarif._tool_component import ToolComponent
from torch.onnx._internal.diagnostics.infra.sarif._tool_component_reference import (
    ToolComponentReference,
)
from torch.onnx._internal.diagnostics.infra.sarif._translation_metadata import (
    TranslationMetadata,
)
from torch.onnx._internal.diagnostics.infra.sarif._version_control_details import (
    VersionControlDetails,
)
from torch.onnx._internal.diagnostics.infra.sarif._web_request import WebRequest
from torch.onnx._internal.diagnostics.infra.sarif._web_response import WebResponse

# flake8: noqa
