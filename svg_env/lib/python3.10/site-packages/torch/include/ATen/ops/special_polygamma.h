#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/special_polygamma_ops.h>

namespace at {


// aten::special_polygamma(int n, Tensor self) -> Tensor
inline at::Tensor special_polygamma(int64_t n, const at::Tensor & self) {
    return at::_ops::special_polygamma::call(n, self);
}

// aten::special_polygamma.out(int n, Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & special_polygamma_out(at::Tensor & out, int64_t n, const at::Tensor & self) {
    return at::_ops::special_polygamma_out::call(n, self, out);
}
// aten::special_polygamma.out(int n, Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & special_polygamma_outf(int64_t n, const at::Tensor & self, at::Tensor & out) {
    return at::_ops::special_polygamma_out::call(n, self, out);
}

}
