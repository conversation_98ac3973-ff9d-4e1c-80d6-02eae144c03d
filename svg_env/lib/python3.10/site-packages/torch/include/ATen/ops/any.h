#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/any_ops.h>

namespace at {


// aten::any.dim(Tensor self, int dim, bool keepdim=False) -> Tensor
inline at::Tensor any(const at::Tensor & self, int64_t dim, bool keepdim=false) {
    return at::_ops::any_dim::call(self, dim, keepdim);
}

// aten::any.out(Tensor self, int dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & any_out(at::Tensor & out, const at::Tensor & self, int64_t dim, bool keepdim=false) {
    return at::_ops::any_out::call(self, dim, keepdim, out);
}
// aten::any.out(Tensor self, int dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & any_outf(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & out) {
    return at::_ops::any_out::call(self, dim, keepdim, out);
}

// aten::any.dimname(Tensor self, Dimname dim, bool keepdim=False) -> Tensor
inline at::Tensor any(const at::Tensor & self, at::Dimname dim, bool keepdim=false) {
    return at::_ops::any_dimname::call(self, dim, keepdim);
}

// aten::any.dimname_out(Tensor self, Dimname dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & any_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, bool keepdim=false) {
    return at::_ops::any_dimname_out::call(self, dim, keepdim, out);
}
// aten::any.dimname_out(Tensor self, Dimname dim, bool keepdim=False, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & any_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & out) {
    return at::_ops::any_dimname_out::call(self, dim, keepdim, out);
}

// aten::any(Tensor self) -> Tensor
inline at::Tensor any(const at::Tensor & self) {
    return at::_ops::any::call(self);
}

// aten::any.all_out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & any_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::any_all_out::call(self, out);
}
// aten::any.all_out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & any_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::any_all_out::call(self, out);
}

}
