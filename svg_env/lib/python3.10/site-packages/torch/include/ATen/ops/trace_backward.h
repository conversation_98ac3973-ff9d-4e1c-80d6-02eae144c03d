#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/trace_backward_ops.h>

namespace at {


// aten::trace_backward(Tensor grad, SymInt[] sizes) -> Tensor
inline at::Tensor trace_backward(const at::Tensor & grad, at::IntArrayRef sizes) {
    return at::_ops::trace_backward::call(grad, c10::fromIntArrayRefSlow(sizes));
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, int64_t>::value>>
  at::Tensor trace_backward(const at::Tensor & grad, at::IntArrayRef sizes) {
    return at::_ops::trace_backward::call(grad, c10::fromIntArrayRefSlow(sizes));
  }
}

// aten::trace_backward(Tensor grad, SymInt[] sizes) -> Tensor
inline at::Tensor trace_backward_symint(const at::Tensor & grad, c10::SymIntArrayRef sizes) {
    return at::_ops::trace_backward::call(grad, sizes);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same<T, c10::SymInt>::value>>
  at::Tensor trace_backward(const at::Tensor & grad, c10::SymIntArrayRef sizes) {
    return at::_ops::trace_backward::call(grad, sizes);
  }
}

}
