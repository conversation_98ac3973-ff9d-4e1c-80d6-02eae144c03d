#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/_saturate_weight_to_fp16_ops.h>

namespace at {


// aten::_saturate_weight_to_fp16(Tensor weight) -> Tensor
inline at::Tensor _saturate_weight_to_fp16(const at::Tensor & weight) {
    return at::_ops::_saturate_weight_to_fp16::call(weight);
}

}
