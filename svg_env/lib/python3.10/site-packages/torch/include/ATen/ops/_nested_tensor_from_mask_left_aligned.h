#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/_nested_tensor_from_mask_left_aligned_ops.h>

namespace at {


// aten::_nested_tensor_from_mask_left_aligned(Tensor t, Tensor mask) -> bool
inline bool _nested_tensor_from_mask_left_aligned(const at::Tensor & t, const at::Tensor & mask) {
    return at::_ops::_nested_tensor_from_mask_left_aligned::call(t, mask);
}

}
