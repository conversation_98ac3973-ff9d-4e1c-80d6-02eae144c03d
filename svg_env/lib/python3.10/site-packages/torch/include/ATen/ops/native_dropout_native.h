#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> native_dropout_out(const at::Tensor & input, double p, c10::optional<bool> train, at::Tensor & out0, at::Tensor & out1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> native_dropout_cpu(const at::Tensor & input, double p, c10::optional<bool> train);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> native_dropout_cuda(const at::Tensor & input, double p, c10::optional<bool> train);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> native_dropout_nested(const at::Tensor & input, double p, c10::optional<bool> train);
} // namespace native
} // namespace at
