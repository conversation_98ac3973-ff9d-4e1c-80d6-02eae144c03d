#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>
#include <ATen/ops/_linalg_slogdet_meta.h>

namespace at {
namespace native {
struct TORCH_API structured__linalg_slogdet_out : public at::meta::structured__linalg_slogdet {
void impl(const at::Tensor & A, const at::Tensor & sign, const at::Tensor & logabsdet, const at::Tensor & LU, const at::Tensor & pivots);
};
} // namespace native
} // namespace at
