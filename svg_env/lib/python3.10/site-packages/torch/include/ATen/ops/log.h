#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/log_ops.h>

namespace at {


// aten::log(Tensor self) -> Tensor
inline at::Tensor log(const at::Tensor & self) {
    return at::_ops::log::call(self);
}

// aten::log_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & log_(at::Tensor & self) {
    return at::_ops::log_::call(self);
}

// aten::log.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & log_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::log_out::call(self, out);
}
// aten::log.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & log_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::log_out::call(self, out);
}

}
