#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor & count_nonzero_dim_IntList_out(const at::Tensor & self, at::IntArrayRef dim, at::Tensor & out);
TORCH_API at::Tensor count_nonzero_cpu(const at::Tensor & self, at::IntArrayRef dim);
TORCH_API at::Tensor count_nonzero_cuda(const at::Tensor & self, at::IntArrayRef dim);
TORCH_API at::Tensor count_nonzero(const at::Tensor & self, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API at::Tensor & count_nonzero_out(const at::Tensor & self, c10::optional<int64_t> dim, at::Tensor & out);
} // namespace native
} // namespace at
