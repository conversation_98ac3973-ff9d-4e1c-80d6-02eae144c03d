#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/pixel_unshuffle_ops.h>

namespace at {


// aten::pixel_unshuffle(Tensor self, int downscale_factor) -> Tensor
inline at::Tensor pixel_unshuffle(const at::Tensor & self, int64_t downscale_factor) {
    return at::_ops::pixel_unshuffle::call(self, downscale_factor);
}

// aten::pixel_unshuffle.out(Tensor self, int downscale_factor, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pixel_unshuffle_out(at::Tensor & out, const at::Tensor & self, int64_t downscale_factor) {
    return at::_ops::pixel_unshuffle_out::call(self, downscale_factor, out);
}
// aten::pixel_unshuffle.out(Tensor self, int downscale_factor, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pixel_unshuffle_outf(const at::Tensor & self, int64_t downscale_factor, at::Tensor & out) {
    return at::_ops::pixel_unshuffle_out::call(self, downscale_factor, out);
}

}
