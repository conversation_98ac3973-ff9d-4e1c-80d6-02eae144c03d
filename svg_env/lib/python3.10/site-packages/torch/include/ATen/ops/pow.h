#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/pow_ops.h>

namespace at {


// aten::pow.Tensor_Tensor_out(Tensor self, Tensor exponent, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pow_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & exponent) {
    return at::_ops::pow_Tensor_Tensor_out::call(self, exponent, out);
}
// aten::pow.Tensor_Tensor_out(Tensor self, Tensor exponent, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pow_outf(const at::Tensor & self, const at::Tensor & exponent, at::Tensor & out) {
    return at::_ops::pow_Tensor_Tensor_out::call(self, exponent, out);
}

// aten::pow.Tensor_Tensor(Tensor self, Tensor exponent) -> Tensor
inline at::Tensor pow(const at::Tensor & self, const at::Tensor & exponent) {
    return at::_ops::pow_Tensor_Tensor::call(self, exponent);
}

// aten::pow.Scalar_out(Scalar self, Tensor exponent, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pow_out(at::Tensor & out, const at::Scalar & self, const at::Tensor & exponent) {
    return at::_ops::pow_Scalar_out::call(self, exponent, out);
}
// aten::pow.Scalar_out(Scalar self, Tensor exponent, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pow_outf(const at::Scalar & self, const at::Tensor & exponent, at::Tensor & out) {
    return at::_ops::pow_Scalar_out::call(self, exponent, out);
}

// aten::pow.Scalar(Scalar self, Tensor exponent) -> Tensor
inline at::Tensor pow(const at::Scalar & self, const at::Tensor & exponent) {
    return at::_ops::pow_Scalar::call(self, exponent);
}

// aten::pow.Tensor_Scalar_out(Tensor self, Scalar exponent, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pow_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & exponent) {
    return at::_ops::pow_Tensor_Scalar_out::call(self, exponent, out);
}
// aten::pow.Tensor_Scalar_out(Tensor self, Scalar exponent, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & pow_outf(const at::Tensor & self, const at::Scalar & exponent, at::Tensor & out) {
    return at::_ops::pow_Tensor_Scalar_out::call(self, exponent, out);
}

// aten::pow.Tensor_Scalar(Tensor self, Scalar exponent) -> Tensor
inline at::Tensor pow(const at::Tensor & self, const at::Scalar & exponent) {
    return at::_ops::pow_Tensor_Scalar::call(self, exponent);
}

}
