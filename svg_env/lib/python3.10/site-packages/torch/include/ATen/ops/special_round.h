#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/special_round_ops.h>

namespace at {


// aten::special_round(Tensor self, *, int decimals=0) -> Tensor
inline at::Tensor special_round(const at::Tensor & self, int64_t decimals=0) {
    return at::_ops::special_round::call(self, decimals);
}

// aten::special_round.out(Tensor self, *, int decimals=0, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & special_round_out(at::Tensor & out, const at::Tensor & self, int64_t decimals=0) {
    return at::_ops::special_round_out::call(self, decimals, out);
}
// aten::special_round.out(Tensor self, *, int decimals=0, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & special_round_outf(const at::Tensor & self, int64_t decimals, at::Tensor & out) {
    return at::_ops::special_round_out::call(self, decimals, out);
}

}
