#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor argsort(const at::Tensor & self, int64_t dim=-1, bool descending=false);
TORCH_API at::Tensor & argsort_stable_out(const at::Tensor & self, bool stable, int64_t dim, bool descending, at::Tensor & out);
TORCH_API at::Tensor argsort_stable(const at::Tensor & self, bool stable, int64_t dim=-1, bool descending=false);
TORCH_API at::Tensor argsort(const at::Tensor & self, at::Dimname dim, bool descending=false);
} // namespace native
} // namespace at
