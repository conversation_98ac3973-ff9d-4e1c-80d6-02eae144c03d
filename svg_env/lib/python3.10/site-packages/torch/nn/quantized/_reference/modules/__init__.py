# flake8: noqa: F401
r"""Quantized Reference Modules

This module is in the process of migration to
`torch/ao/nn/quantized/reference`, and is kept here for
compatibility while the migration process is ongoing.
If you are adding a new entry/functionality, please, add it to the
appropriate file under the `torch/ao/nn/quantized/reference`,
while adding an import statement here.
"""

from torch.ao.nn.quantized.reference.modules.linear import Linear
from torch.ao.nn.quantized.reference.modules.conv import Conv1d, Conv2d, Conv3d, ConvTranspose1d, ConvTranspose2d, ConvTranspose3d
from torch.ao.nn.quantized.reference.modules.rnn import RN<PERSON><PERSON>, LSTMCell, GRUCell, LSTM
from torch.ao.nn.quantized.reference.modules.sparse import Embedding, EmbeddingBag

__all__ = [
    'Linear',
    'Conv1d',
    'Conv2d',
    'Conv3d',
    'ConvTranspose1d',
    'ConvTranspose2d',
    'ConvTranspose3d',
    'RN<PERSON><PERSON>',
    'LSTMCell',
    'GR<PERSON>ell',
    'LSTM',
    'Embedding',
    'EmbeddingBag',
]
