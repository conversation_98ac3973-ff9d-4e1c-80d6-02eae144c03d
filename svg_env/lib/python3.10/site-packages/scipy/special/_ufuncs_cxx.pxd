from . cimport sf_error
cdef void _set_action(sf_error.sf_error_t, sf_error.sf_action_t) noexcept nogil
cdef void *_export_faddeeva_dawsn
cdef void *_export_faddeeva_dawsn_complex
cdef void *_export_fellint_RC
cdef void *_export_cellint_RC
cdef void *_export_fellint_RD
cdef void *_export_cellint_RD
cdef void *_export_fellint_RF
cdef void *_export_cellint_RF
cdef void *_export_fellint_RG
cdef void *_export_cellint_RG
cdef void *_export_fellint_RJ
cdef void *_export_cellint_RJ
cdef void *_export_faddeeva_erf
cdef void *_export_faddeeva_erfc_complex
cdef void *_export_faddeeva_erfcx
cdef void *_export_faddeeva_erfcx_complex
cdef void *_export_faddeeva_erfi
cdef void *_export_faddeeva_erfi_complex
cdef void *_export_erfinv_float
cdef void *_export_erfinv_double
cdef void *_export_expit
cdef void *_export_expitf
cdef void *_export_expitl
cdef void *_export_hyp1f1_double
cdef void *_export_log_expit
cdef void *_export_log_expitf
cdef void *_export_log_expitl
cdef void *_export_faddeeva_log_ndtr
cdef void *_export_faddeeva_log_ndtr_complex
cdef void *_export_logit
cdef void *_export_logitf
cdef void *_export_logitl
cdef void *_export_faddeeva_ndtr
cdef void *_export_powm1_float
cdef void *_export_powm1_double
cdef void *_export_faddeeva_voigt_profile
cdef void *_export_faddeeva_w
cdef void *_export_wrightomega
cdef void *_export_wrightomega_real