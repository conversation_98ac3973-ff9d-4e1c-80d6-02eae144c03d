cairocffi-1.7.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
cairocffi-1.7.1.dist-info/LICENSE,sha256=UBwOyupfTbF3Egs-QTxtqBT1V1JGtgNGOJnvtiBLVSg,1534
cairocffi-1.7.1.dist-info/METADATA,sha256=_sA6C0y_veWUU9xYPhwYd41Pt2VqL9zcnC9X5F0Cyug,3302
cairocffi-1.7.1.dist-info/RECORD,,
cairocffi-1.7.1.dist-info/WHEEL,sha256=EZbGkh7Ie4PoZfRQ8I0ZuP9VklN_TvcZ6DSE5Uar4z4,81
cairocffi/__init__.py,sha256=hysVFgktnYyWHILiD3fn6HhaSDMIfJun6go-<PERSON>c<PERSON><PERSON><PERSON><PERSON>,4579
cairocffi/__pycache__/__init__.cpython-310.pyc,,
cairocffi/__pycache__/constants.cpython-310.pyc,,
cairocffi/__pycache__/context.cpython-310.pyc,,
cairocffi/__pycache__/ffi.cpython-310.pyc,,
cairocffi/__pycache__/fonts.cpython-310.pyc,,
cairocffi/__pycache__/matrix.cpython-310.pyc,,
cairocffi/__pycache__/patterns.cpython-310.pyc,,
cairocffi/__pycache__/pixbuf.cpython-310.pyc,,
cairocffi/__pycache__/surfaces.cpython-310.pyc,,
cairocffi/__pycache__/test_cairo.cpython-310.pyc,,
cairocffi/__pycache__/test_numpy.cpython-310.pyc,,
cairocffi/__pycache__/test_pixbuf.cpython-310.pyc,,
cairocffi/__pycache__/test_xcb.cpython-310.pyc,,
cairocffi/__pycache__/xcb.cpython-310.pyc,,
cairocffi/constants.py,sha256=vMFlchltVPb-uZtkKb66VLbJWCY4BkfpJB177I__haE,56004
cairocffi/context.py,sha256=HYvobmHpB6h3GDW2QGiUE2ztMr-a-04r-Hbs65_l9Sc,85175
cairocffi/ffi.py,sha256=vc9T6i_E2uvFKUW-ttaIa3ylxt9ZsO2rpxI1MklxCPE,3231
cairocffi/fonts.py,sha256=L0Tj1BGekNcT2l-Qu2jlo3WokTPRM9Qx_YtcJmqaJqk,19496
cairocffi/matrix.py,sha256=BrNdFCCiFcmJGZHjlT5pL8BTrhJkeKdCnjH0a-Cfm2g,8038
cairocffi/patterns.py,sha256=2QUftfajlbqiLSyZ9HtiTI8fHUHr1hhVJF0G3iuguE8,12969
cairocffi/pixbuf.py,sha256=NCGTGrJVwTVCI7VscwX2HpmRT_RNL1A6VZlhM_ypepA,7244
cairocffi/surfaces.py,sha256=qgpUdFeJiwOvU0WrlooFtlBfJxnwrArhjbbffUP44UM,57812
cairocffi/test_cairo.py,sha256=Ubt2WJp91BLQ1SKuVp8VnyrpR2xve2zDaTI7CMMVWqc,47328
cairocffi/test_numpy.py,sha256=Rymj2fJEF-HfEwkok4Ty0-eLEAaw5Nczbi7FC797PD8,406
cairocffi/test_pixbuf.py,sha256=65Q9zZUoi3fWLkdSitEqOlSfoOCzhmeQmcDITmdyfOg,2398
cairocffi/test_xcb.py,sha256=JW1l6vOCs6W0AFIl98jNpo88xcIwbETKGNmkYLliUNo,6330
cairocffi/xcb.py,sha256=ksfTbOwTUXEDp4nMG2nWH4MR5cmJf8IrNvEsbeQMBms,2011
