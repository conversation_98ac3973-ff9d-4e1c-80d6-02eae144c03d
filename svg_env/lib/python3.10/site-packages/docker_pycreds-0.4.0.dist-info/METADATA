Metadata-Version: 2.1
Name: docker-pycreds
Version: 0.4.0
Summary: Python bindings for the docker credentials store API
Home-page: https://github.com/shin-/dockerpy-creds
Author: UNKNOWN
Author-email: UNKNOWN
License: Apache License 2.0
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Other Environment
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Topic :: Utilities
Classifier: License :: OSI Approved :: Apache Software License
Requires-Dist: six (>=1.4.0)

# docker-pycreds

[![CircleCI](https://circleci.com/gh/shin-/dockerpy-creds/tree/master.svg?style=svg)](https://circleci.com/gh/shin-/dockerpy-creds/tree/master)

Python bindings for the docker credentials store API

## Credentials store info

[Docker documentation page](https://docs.docker.com/engine/reference/commandline/login/#/credentials-store)

## Requirements

On top of the dependencies in `requirements.txt`, the `docker-credential`
executable for the platform must be installed on the user's system.

## API usage

```python

import dockerpycreds

store = dockerpycreds.Store('secretservice')
store.store(
    server='https://index.docker.io/v1/', username='johndoe',
    secret='hunter2'
)

print(store.list())

print(store.get('https://index.docker.io/v1/'))


store.erase('https://index.docker.io/v1/')
```


