../../../bin/transformers-cli,sha256=0w96s9KjxfU_MFDa2OU4w4EWhtHRUYKGwOzOBjRCA7g,263
transformers-4.43.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
transformers-4.43.3.dist-info/LICENSE,sha256=d_1HEN757DwPYiWADgI18VpCWr1KiwNVkSf814JhIEk,11418
transformers-4.43.3.dist-info/METADATA,sha256=3zl-RNIrmYuWYNTyuaP6CRj3XiluC8FGcgXsOp4mrMc,43655
transformers-4.43.3.dist-info/RECORD,,
transformers-4.43.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers-4.43.3.dist-info/WHEEL,sha256=GJ7t_kWBFywbagK5eo9IoUwLW6oyOeTKmQ-9iHFVNxQ,92
transformers-4.43.3.dist-info/entry_points.txt,sha256=NC_VjQxHu59c5WStu_7imUSlBjuk86IvLxhEtlrO-2k,82
transformers-4.43.3.dist-info/top_level.txt,sha256=GLBaeTo_CSdhnHvbxQ0kzpEHdlLuA_33foIogaWxntI,13
transformers/__init__.py,sha256=8M2PW3QdBMLY5D5sv7tkKkk3g1O5GGFzJZNdBQbFDL8,294268
transformers/__pycache__/__init__.cpython-310.pyc,,
transformers/__pycache__/activations.cpython-310.pyc,,
transformers/__pycache__/activations_tf.cpython-310.pyc,,
transformers/__pycache__/audio_utils.cpython-310.pyc,,
transformers/__pycache__/cache_utils.cpython-310.pyc,,
transformers/__pycache__/configuration_utils.cpython-310.pyc,,
transformers/__pycache__/convert_graph_to_onnx.cpython-310.pyc,,
transformers/__pycache__/convert_pytorch_checkpoint_to_tf2.cpython-310.pyc,,
transformers/__pycache__/convert_slow_tokenizer.cpython-310.pyc,,
transformers/__pycache__/convert_slow_tokenizers_checkpoints_to_fast.cpython-310.pyc,,
transformers/__pycache__/convert_tf_hub_seq_to_seq_bert_to_pytorch.cpython-310.pyc,,
transformers/__pycache__/debug_utils.cpython-310.pyc,,
transformers/__pycache__/deepspeed.cpython-310.pyc,,
transformers/__pycache__/dependency_versions_check.cpython-310.pyc,,
transformers/__pycache__/dependency_versions_table.cpython-310.pyc,,
transformers/__pycache__/dynamic_module_utils.cpython-310.pyc,,
transformers/__pycache__/feature_extraction_sequence_utils.cpython-310.pyc,,
transformers/__pycache__/feature_extraction_utils.cpython-310.pyc,,
transformers/__pycache__/file_utils.cpython-310.pyc,,
transformers/__pycache__/hf_argparser.cpython-310.pyc,,
transformers/__pycache__/hyperparameter_search.cpython-310.pyc,,
transformers/__pycache__/image_processing_base.cpython-310.pyc,,
transformers/__pycache__/image_processing_utils.cpython-310.pyc,,
transformers/__pycache__/image_processing_utils_fast.cpython-310.pyc,,
transformers/__pycache__/image_transforms.cpython-310.pyc,,
transformers/__pycache__/image_utils.cpython-310.pyc,,
transformers/__pycache__/keras_callbacks.cpython-310.pyc,,
transformers/__pycache__/modelcard.cpython-310.pyc,,
transformers/__pycache__/modeling_attn_mask_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_flash_attention_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_flax_outputs.cpython-310.pyc,,
transformers/__pycache__/modeling_flax_pytorch_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_flax_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_gguf_pytorch_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_outputs.cpython-310.pyc,,
transformers/__pycache__/modeling_rope_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_tf_outputs.cpython-310.pyc,,
transformers/__pycache__/modeling_tf_pytorch_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_tf_utils.cpython-310.pyc,,
transformers/__pycache__/modeling_utils.cpython-310.pyc,,
transformers/__pycache__/optimization.cpython-310.pyc,,
transformers/__pycache__/optimization_tf.cpython-310.pyc,,
transformers/__pycache__/processing_utils.cpython-310.pyc,,
transformers/__pycache__/pytorch_utils.cpython-310.pyc,,
transformers/__pycache__/safetensors_conversion.cpython-310.pyc,,
transformers/__pycache__/testing_utils.cpython-310.pyc,,
transformers/__pycache__/tf_utils.cpython-310.pyc,,
transformers/__pycache__/time_series_utils.cpython-310.pyc,,
transformers/__pycache__/tokenization_utils.cpython-310.pyc,,
transformers/__pycache__/tokenization_utils_base.cpython-310.pyc,,
transformers/__pycache__/tokenization_utils_fast.cpython-310.pyc,,
transformers/__pycache__/trainer.cpython-310.pyc,,
transformers/__pycache__/trainer_callback.cpython-310.pyc,,
transformers/__pycache__/trainer_pt_utils.cpython-310.pyc,,
transformers/__pycache__/trainer_seq2seq.cpython-310.pyc,,
transformers/__pycache__/trainer_utils.cpython-310.pyc,,
transformers/__pycache__/training_args.cpython-310.pyc,,
transformers/__pycache__/training_args_seq2seq.cpython-310.pyc,,
transformers/__pycache__/training_args_tf.cpython-310.pyc,,
transformers/activations.py,sha256=EMN-kVzitS1TmltS7Kr2ROKwxW0oLbAHeAmNdDQuvu4,8177
transformers/activations_tf.py,sha256=u2Y9dgDRgW-YbN_J-xmd05EK4p24rV8ZkzrQzpz4lCI,4689
transformers/agents/__init__.py,sha256=VpU-6Dn9Rd8bZYMCmfYA551plsP5G6tAaD48BeFTx3s,2536
transformers/agents/__pycache__/__init__.cpython-310.pyc,,
transformers/agents/__pycache__/agent_types.cpython-310.pyc,,
transformers/agents/__pycache__/agents.cpython-310.pyc,,
transformers/agents/__pycache__/default_tools.cpython-310.pyc,,
transformers/agents/__pycache__/document_question_answering.cpython-310.pyc,,
transformers/agents/__pycache__/evaluate_agent.cpython-310.pyc,,
transformers/agents/__pycache__/image_question_answering.cpython-310.pyc,,
transformers/agents/__pycache__/llm_engine.cpython-310.pyc,,
transformers/agents/__pycache__/prompts.cpython-310.pyc,,
transformers/agents/__pycache__/python_interpreter.cpython-310.pyc,,
transformers/agents/__pycache__/speech_to_text.cpython-310.pyc,,
transformers/agents/__pycache__/text_to_speech.cpython-310.pyc,,
transformers/agents/__pycache__/tools.cpython-310.pyc,,
transformers/agents/__pycache__/translation.cpython-310.pyc,,
transformers/agents/agent_types.py,sha256=wOcwV3xitPov3QuCZECVGSOAD66c6H_xsUP3Avmjm30,8235
transformers/agents/agents.py,sha256=h0Ffja2VV269-zphmynTOi9vIaGXP_axNBYIlDa80Co,42308
transformers/agents/default_tools.py,sha256=lpe6QC3bus1hUVqn2RIMRC3lWGqNdsuJajnm57KQvJg,5319
transformers/agents/document_question_answering.py,sha256=dPtkrDiRYJfbJxYwaHpF3rxbkRMPegZ3YsZqFg2MMo4,3624
transformers/agents/evaluate_agent.py,sha256=VtipCsaTAEqNSWjhLHKFiqTnZ7FpU1KbmSNEJSHxRZg,14929
transformers/agents/image_question_answering.py,sha256=jLTz35XWwvt_Tbi6FxWfftdbXA6Frk7lhNbRWVnntpU,2000
transformers/agents/llm_engine.py,sha256=5vXeiyKBNfzuY-m-aLst5rXbOcNi7IN8EpDOCySfUTw,3005
transformers/agents/prompts.py,sha256=OkX5V44Jd2KiWo-LYssdtqRNjlw7YU_lZKG9qNmFtps,19776
transformers/agents/python_interpreter.py,sha256=7PVft9oJS1R4h2F6zjRu_C3VljyfYJlTDgcLOUqqeCM,38423
transformers/agents/speech_to_text.py,sha256=rCif-HQS6CyqoHM_wHcYTwBuePlWaFkIGdDv6d0RrXU,1494
transformers/agents/text_to_speech.py,sha256=qC56OvSOLkXYptMpRO5osiqILwamFWCWHBFjEYiIktU,2466
transformers/agents/tools.py,sha256=yiAtO3RP9T1fNfXr2NG8Kb1CjGbwsC9dCv_8PngGCK8,31062
transformers/agents/translation.py,sha256=x5hQ9CY-zIsiANqLavoTDSSkvE51zVaLwftTrJw1w-s,8663
transformers/audio_utils.py,sha256=TQ0qmsLQbOsTosc1elFRuHyc7UOG6rtT4MY49bha1H4,50346
transformers/benchmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/benchmark/__pycache__/__init__.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_args.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_args_tf.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_args_utils.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_tf.cpython-310.pyc,,
transformers/benchmark/__pycache__/benchmark_utils.cpython-310.pyc,,
transformers/benchmark/benchmark.py,sha256=efSeIH606x5u9IA6CkKXL7t7CoH9kORKSrXypR8gfHk,10747
transformers/benchmark/benchmark_args.py,sha256=djFAjBC11MnI-auxByCWSVVAqRqXGV650Leosd60VmA,4050
transformers/benchmark/benchmark_args_tf.py,sha256=bAcsgf7bOUyoo8AGFSiQhciR8S5wMJqnL5iVlvbQzow,4735
transformers/benchmark/benchmark_args_utils.py,sha256=pkgvor3IuC5v9BubOCFVuwbgGHsoGkNp1CDdgJlyBi4,6499
transformers/benchmark/benchmark_tf.py,sha256=wSXldL0TVlNzBB0YOUTQ9REia8Eib6nz4WsNg6UXBsU,13246
transformers/benchmark/benchmark_utils.py,sha256=ZzHCNnPKtygQpdq3vD1q_p5Y2rwo4ctmKjOc5e8sca8,37599
transformers/cache_utils.py,sha256=3THwpF-G9U1E76UDIWKUI-rypilNewNrjaXI-a7Kq4s,62978
transformers/commands/__init__.py,sha256=aFO3I7C6G9OLA9JZSc_yMaZl0glOQtjNPjqMFfu9wfQ,923
transformers/commands/__pycache__/__init__.cpython-310.pyc,,
transformers/commands/__pycache__/add_new_model_like.cpython-310.pyc,,
transformers/commands/__pycache__/convert.cpython-310.pyc,,
transformers/commands/__pycache__/download.cpython-310.pyc,,
transformers/commands/__pycache__/env.cpython-310.pyc,,
transformers/commands/__pycache__/lfs.cpython-310.pyc,,
transformers/commands/__pycache__/pt_to_tf.cpython-310.pyc,,
transformers/commands/__pycache__/run.cpython-310.pyc,,
transformers/commands/__pycache__/serving.cpython-310.pyc,,
transformers/commands/__pycache__/train.cpython-310.pyc,,
transformers/commands/__pycache__/transformers_cli.cpython-310.pyc,,
transformers/commands/__pycache__/user.cpython-310.pyc,,
transformers/commands/add_new_model_like.py,sha256=XrXMElhnbUwpI8wXhQ_Wm_6h9V0ZLa3dJtu1YLvMLXU,70908
transformers/commands/convert.py,sha256=lHz2sQti9HubMNwObLCc_sw9Y7L-IPcaYJMSJR_AVWM,7068
transformers/commands/download.py,sha256=GKPadx-YGBL7dHJSEcUp-QNOP3R2L71-gPGP0z6NNQI,2395
transformers/commands/env.py,sha256=g54Hwtyp3ZpYq782cKwvLrnliLg2PhUrZ7yOQsOgZ5c,5756
transformers/commands/lfs.py,sha256=4QDGBbJxBcRpgmhHXvigZQUsXuTPwrRY60t1qGjzfWU,8001
transformers/commands/pt_to_tf.py,sha256=nHQ-vqVPbb9GdGFVaDtSiot0p4h3DKAx4YLsHlNqEvM,21043
transformers/commands/run.py,sha256=nyEe2lOoj6e0EOxjKeF08hdW9WVWa101r9hWXl9v3Jo,4249
transformers/commands/serving.py,sha256=CnNHFVM_SK_-aNxEJnq7vJK5dBqDBw7bxxQiv5truEU,8027
transformers/commands/train.py,sha256=FKlH-IYr3mVc7_mS5ObCyJaHs9JincYLg3Zt6WQz1ag,6341
transformers/commands/transformers_cli.py,sha256=ynsTme9TyR81oBP6a9hEbDdR8vZvQwOaE2-lRlsnLvs,1941
transformers/commands/user.py,sha256=UxHTw7L2qU8Svdj6v2Nb8OB8nGI5dRXawrXt398DkXQ,7091
transformers/configuration_utils.py,sha256=x_nINuxRmo6eHj4HW2VVVbrXjlbxUKG1dScCGoB90MU,57080
transformers/convert_graph_to_onnx.py,sha256=eoA4_4LmxwK-dirCgB0A75dAIMn_v9BoYEoJ_HaJc1Q,20151
transformers/convert_pytorch_checkpoint_to_tf2.py,sha256=hTMei3XHETc9a9gxVLtNPpF5m4QVh_lQE8DI-Jn_NaI,14653
transformers/convert_slow_tokenizer.py,sha256=MRhZ5J6zpyTexZ8dKd62VcxdfJhatyqUA8FvYILZnYs,61970
transformers/convert_slow_tokenizers_checkpoints_to_fast.py,sha256=Tf95ld64ELqxJStWL2g7WUBPSIFBXV1_9GfhgXQdczQ,4981
transformers/convert_tf_hub_seq_to_seq_bert_to_pytorch.py,sha256=dy9yjETinWJl2MeQ-wv1J5HtmGm3j6Ki3r65optejRg,2910
transformers/data/__init__.py,sha256=JWIY7GLKedWilK2mpd_qtVeXLQK2ZXki6ISkRUua09Y,1423
transformers/data/__pycache__/__init__.cpython-310.pyc,,
transformers/data/__pycache__/data_collator.cpython-310.pyc,,
transformers/data/data_collator.py,sha256=XuZc0QvoistVJcwSB2uNbA8ohYRM4FdmL5F5Zf1DVvs,80382
transformers/data/datasets/__init__.py,sha256=PGzUJjdmTPOPMyjV4-Tj3sNrmmh-lspjyxrVbrfJoX8,909
transformers/data/datasets/__pycache__/__init__.cpython-310.pyc,,
transformers/data/datasets/__pycache__/glue.cpython-310.pyc,,
transformers/data/datasets/__pycache__/language_modeling.cpython-310.pyc,,
transformers/data/datasets/__pycache__/squad.cpython-310.pyc,,
transformers/data/datasets/glue.py,sha256=K3h2KxjIg0kWegPCw6ikbOL-lCFbKoQewb7R8wLZoIc,6163
transformers/data/datasets/language_modeling.py,sha256=E-VGwuyb09J4KmV8v37bNH5in90wDPuZHCYsqGdT7W0,23721
transformers/data/datasets/squad.py,sha256=OUTQDd687SQns7HRWDCgAjnuo_ZXihifLS6jF2bhUhc,9219
transformers/data/metrics/__init__.py,sha256=o9t_VTQtqU3lEhqvocDzFMm7OvAKD-uxrjPWy0r74BI,3632
transformers/data/metrics/__pycache__/__init__.cpython-310.pyc,,
transformers/data/metrics/__pycache__/squad_metrics.cpython-310.pyc,,
transformers/data/metrics/squad_metrics.py,sha256=mP6eaDcGTLsS4EhnvnD3U_Yyvcua_LVgElCkuxy2XJE,29697
transformers/data/processors/__init__.py,sha256=lvN5mp9mdrr5v6QvZT6VcoZ78zZUvXiumTm6Gdvlgvo,1014
transformers/data/processors/__pycache__/__init__.cpython-310.pyc,,
transformers/data/processors/__pycache__/glue.cpython-310.pyc,,
transformers/data/processors/__pycache__/squad.cpython-310.pyc,,
transformers/data/processors/__pycache__/utils.cpython-310.pyc,,
transformers/data/processors/__pycache__/xnli.cpython-310.pyc,,
transformers/data/processors/glue.py,sha256=1sHcfSWbl-ooNIEu3emKmDlpW-95UZT1JfDlGYx5TFA,23218
transformers/data/processors/squad.py,sha256=_4WNLcZA6TAy7uNZO46948tmL5ngVF0LSB0y8nUn6rs,33153
transformers/data/processors/utils.py,sha256=GSaZbJ--XYq57vqyRVx_5LHSR4tklzFyR7ZKHGWsTAs,13829
transformers/data/processors/xnli.py,sha256=sgcYz9YSfHY9NS0LO_YeFRRjq-nJFsDhFUP4NJeu-Q4,3481
transformers/debug_utils.py,sha256=6q8ArB104GdcIC2qfBQzKLxO7PfXmHEKdYtfL2FOK2w,12907
transformers/deepspeed.py,sha256=gOUpVxXSw1s3Fpek-f6GyRvd63AQadqhW7VlfeRHz0Y,1479
transformers/dependency_versions_check.py,sha256=6HbgtT2Wp-QZGOAdyUOklHvNA4rOVITGHrX34dtMOqg,2115
transformers/dependency_versions_table.py,sha256=deACbNtLX5sq1HVcLpR60DK-_3-6Q73EMJG09_qQ_ck,3409
transformers/dynamic_module_utils.py,sha256=leD_aMcn58ivCjFAbVGfikImdUDZJqd2dAA-e4T5aCA,27777
transformers/feature_extraction_sequence_utils.py,sha256=xE5f0cSWWodEjCwNDsG0Dl9kL3B9KPs-SsF4YTWNh0M,18307
transformers/feature_extraction_utils.py,sha256=1lDA7cgz5RujJd0Yth0mogaaoNYP1qkN1c0nOni5mwA,29823
transformers/file_utils.py,sha256=qI7cWTYpFy0v9HZSRBASv2yvD2U1OJgYShIOsQ7cCUg,3744
transformers/generation/__init__.py,sha256=vqotmQK5X9jLpoEWT2_g6mklnGG-tUKiZtToUDuIS1U,11676
transformers/generation/__pycache__/__init__.cpython-310.pyc,,
transformers/generation/__pycache__/beam_constraints.cpython-310.pyc,,
transformers/generation/__pycache__/beam_search.cpython-310.pyc,,
transformers/generation/__pycache__/candidate_generator.cpython-310.pyc,,
transformers/generation/__pycache__/configuration_utils.cpython-310.pyc,,
transformers/generation/__pycache__/flax_logits_process.cpython-310.pyc,,
transformers/generation/__pycache__/flax_utils.cpython-310.pyc,,
transformers/generation/__pycache__/logits_process.cpython-310.pyc,,
transformers/generation/__pycache__/stopping_criteria.cpython-310.pyc,,
transformers/generation/__pycache__/streamers.cpython-310.pyc,,
transformers/generation/__pycache__/tf_logits_process.cpython-310.pyc,,
transformers/generation/__pycache__/tf_utils.cpython-310.pyc,,
transformers/generation/__pycache__/utils.cpython-310.pyc,,
transformers/generation/__pycache__/watermarking.cpython-310.pyc,,
transformers/generation/beam_constraints.py,sha256=3r3Y4sP0zTetOGSlkoHW5AiW_bsOMHiJaz7fSOfhCV0,19100
transformers/generation/beam_search.py,sha256=rGjKNmKPa61POCkEh5VmZn9oUaqM3eIJ1jXhkn7CPYU,49536
transformers/generation/candidate_generator.py,sha256=_jeqVTDfN8LsLqm4AW3COWbBVOZAsFH1rCI1v4DQKNw,21478
transformers/generation/configuration_utils.py,sha256=rtWbFMiw6AS4b83_VWmKVkLHavNKuSJWbPIwqC69U0c,71187
transformers/generation/flax_logits_process.py,sha256=99pNEs2Lw63VO6014gBesHxyg8n3PKG2oAsEwhd_7EY,23006
transformers/generation/flax_utils.py,sha256=x4J5blTRgFVp8o0lK-UvjOYzpeTP54kdy5m5eK8apzQ,50078
transformers/generation/logits_process.py,sha256=ku5EpdZIK7T3w1aCrv6JOM3HerWiTDUi5_YP--XuLYM,119046
transformers/generation/stopping_criteria.py,sha256=5RQMCqgPXhEi8YevLayKtKkPtusOV4erZgDMqHWQIew,29151
transformers/generation/streamers.py,sha256=ArJCKAVRKIKALqdGBAsQu038-BwZbo05tzOXZWP9yng,9213
transformers/generation/tf_logits_process.py,sha256=ZsIBDrFJ3egkk8aWYKtCvqH4M7INnlBa2zoCAIT5MR0,28114
transformers/generation/tf_utils.py,sha256=kuKYrCvLa_JDAFAe3wD064byH6t62gtC2Y-qQVg07a0,175608
transformers/generation/utils.py,sha256=KvQf4UpL0inF5XUCH8g7CchyS8Mrjx-STKlLvOghlIQ,239356
transformers/generation/watermarking.py,sha256=4s4qr-FqUD7zYUehtEY11AoOYo-4vVLsMqr7tuKQYQg,10927
transformers/hf_argparser.py,sha256=t6EC7gJ6yWJPCDScgrppfgXOAkjZxEJJO6pe8W-aK_0,19823
transformers/hyperparameter_search.py,sha256=wmfAWk_NTUQj3MezO_6CaDaJyUt9pbARcs-tbo_BdeM,4171
transformers/image_processing_base.py,sha256=w8VwDpHHGhF2CUd6_w_kUnrkmR5O84wB6upmbtKHgbM,25014
transformers/image_processing_utils.py,sha256=EDSL4O-qWBofy1a5sZLNtSG9b-PvFpteAmP7U9_ai0I,12742
transformers/image_processing_utils_fast.py,sha256=C0DJr5wuXMMI66J8NQJQDgQpDBIHR--qnAoJXy9-O1Q,2090
transformers/image_transforms.py,sha256=_gAh_kttyn-Pdu8NTY6xmVhWgE_LEEXazriZpljyW3I,35702
transformers/image_utils.py,sha256=bw4DEv8BkFCVCcp2DLgkpT_0C4qxVXM5pGSKff7bU5Q,31052
transformers/integrations/__init__.py,sha256=7rkOOVBh7A3nAfwxUXeDGd6eMv_YKs8VhgZU02qprJw,5698
transformers/integrations/__pycache__/__init__.cpython-310.pyc,,
transformers/integrations/__pycache__/aqlm.cpython-310.pyc,,
transformers/integrations/__pycache__/awq.cpython-310.pyc,,
transformers/integrations/__pycache__/bitsandbytes.cpython-310.pyc,,
transformers/integrations/__pycache__/deepspeed.cpython-310.pyc,,
transformers/integrations/__pycache__/eetq.cpython-310.pyc,,
transformers/integrations/__pycache__/fbgemm_fp8.cpython-310.pyc,,
transformers/integrations/__pycache__/ggml.cpython-310.pyc,,
transformers/integrations/__pycache__/hqq.cpython-310.pyc,,
transformers/integrations/__pycache__/integration_utils.cpython-310.pyc,,
transformers/integrations/__pycache__/peft.cpython-310.pyc,,
transformers/integrations/__pycache__/quanto.cpython-310.pyc,,
transformers/integrations/__pycache__/tpu.cpython-310.pyc,,
transformers/integrations/aqlm.py,sha256=he7KlYGpXrYExkosOo-Gbsf21mdyrRhxQOICmpHZPqI,4461
transformers/integrations/awq.py,sha256=uRPQhnAJ_y2uR6wwiajaYo95T1DviMZWeHd9xmRrBwM,19749
transformers/integrations/bitsandbytes.py,sha256=KS6WFDkKmaEcZkbIiFXPYIbu3pN5hfkcB4w9xn7wfnc,21306
transformers/integrations/deepspeed.py,sha256=CVi4BdPh07H_5Gk_-NJ4zWmHp2kAOjKhgWoeMVaFfkM,18704
transformers/integrations/eetq.py,sha256=ZDxYk6vfjGsjQoQeFOQ2odn58hc6xrYbwdKtfLdDp-Q,5365
transformers/integrations/fbgemm_fp8.py,sha256=7B9CyZTY3EKup-GCNZnMQgbPMfW22tvv9B0xAWqLjrM,7244
transformers/integrations/ggml.py,sha256=uXJ4or3p-4iF6Bkye8wA3-CejKd5Lzj1og0SKQAkxNg,27652
transformers/integrations/hqq.py,sha256=7aoqtcq9jjsP-D6MHS0zOj-TnEaLGVY3pRc1w329aD4,4810
transformers/integrations/integration_utils.py,sha256=G_VtHKP_s-yj3h3DQ166Hqys-sNN4hha1t2RApmVNGA,94958
transformers/integrations/peft.py,sha256=ca3RHifoSMJf9IEf_CWc2aU_bPpD15l5of5kXkmQto0,22921
transformers/integrations/quanto.py,sha256=VR7GV9KG6mFweixYDaUdhYzfGoLZVurwXWU24Idg32w,4250
transformers/integrations/tpu.py,sha256=Y8YMwIrEgh1s-OCNbOQZFD1_3Tvqpo3e1H6eECTceSU,1392
transformers/keras_callbacks.py,sha256=i95nrEd_QsEo10x3T9RqZf3xGzfPiMOhmU1Ef_HvnGE,20675
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deformable_detr/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cu,sha256=l7UQ6zn1qbeve1meY0QLq2RKk3X6fGpp2UfKt4aEYJ4,7466
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.cuh,sha256=HD7bMWLoGrDKw7XUPPgILCAdOSo1IC8RIv_KyKAnLb0,61539
transformers/kernels/deformable_detr/cuda/ms_deform_attn_cuda.h,sha256=xxP17aer-SiU9J5ASLHdtLIyhFmHC5iLcPIPNW2xkrg,1694
transformers/kernels/deformable_detr/cuda/ms_deform_im2col_cuda.cuh,sha256=BRN8-yfSHY8ChLij8jFl2_z2LL0LEFKuVF6Byi-YLAY,54695
transformers/kernels/deformable_detr/ms_deform_attn.h,sha256=H2bBXGyl0R-v2DqGVz11asoRvxbjZ9iWB9djomZTpgY,1837
transformers/kernels/deformable_detr/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/deta/cpu/ms_deform_attn_cpu.cpp,sha256=VcCGm9IrvgVvmyZt0KyP16Q-ONmbeg6bKwccP6KadL0,1255
transformers/kernels/deta/cpu/ms_deform_attn_cpu.h,sha256=nvVsKj9nabQ7IaNY4di5xVx6u-0lIifQvLg2JCoxiik,1138
transformers/kernels/deta/cuda/ms_deform_attn_cuda.cu,sha256=M5-bW9g5z-upTFMNPIfnyLAqKTxGMCjAPqBr0GmWHX8,7360
transformers/kernels/deta/cuda/ms_deform_attn_cuda.cuh,sha256=hygB20Vh3RttOSdCuTFz8V0d3CXNp-Q89x22rYmD258,61433
transformers/kernels/deta/cuda/ms_deform_attn_cuda.h,sha256=rPWOOMo3QyFdB5kMiexpApLFZ4dnRtx4CluEAGwsfO8,1139
transformers/kernels/deta/cuda/ms_deform_im2col_cuda.cuh,sha256=BRN8-yfSHY8ChLij8jFl2_z2LL0LEFKuVF6Byi-YLAY,54695
transformers/kernels/deta/ms_deform_attn.h,sha256=H2bBXGyl0R-v2DqGVz11asoRvxbjZ9iWB9djomZTpgY,1837
transformers/kernels/deta/vision.cpp,sha256=8RvZy7P_MMx5QEszo_MwNODddJLQ8mKcmmMfgLYC_HA,798
transformers/kernels/mra/cuda_kernel.cu,sha256=LxxRYTymSoBEQpWXHA0PMzwZwpolcwX7mFAjwU8-ZMc,11678
transformers/kernels/mra/cuda_kernel.h,sha256=UJvYq_MDzhcp07bZpYcOBn8ZGFcf_Ax1dynuiVTBvmA,1682
transformers/kernels/mra/cuda_launch.cu,sha256=Ox5MTACriC30CGyn-g1Kb5EgQSMAZSaN6fpit3xLFWc,4072
transformers/kernels/mra/cuda_launch.h,sha256=RVCkN_euasvgPK0zADNRvRYGWd4ah5l9X-7UG_AcdH8,707
transformers/kernels/mra/torch_extension.cpp,sha256=N0YdBLVX0lZabckJzV_RYTHS2atCNvn13E4Ivobt25g,1405
transformers/kernels/rwkv/wkv_cuda.cu,sha256=EvaUrEnw_qr2EjMKP-Pq7VPzFfGlMJnFhdHNLtn1fPU,6219
transformers/kernels/rwkv/wkv_cuda_bf16.cu,sha256=DG9hTtOAlrnpDFahjt-MmnOxjMuhGU55GPsmV21HtrQ,6633
transformers/kernels/rwkv/wkv_op.cpp,sha256=qSExhKdT6p3hyaTv5SypCnH_c7EmaX6HbhTcCntvZWg,4022
transformers/kernels/yoso/common.h,sha256=Tq2rOUtE8Y4DRAUrRISvwIwVI3u8JBf21WgWSAYiDlQ,273
transformers/kernels/yoso/common_cuda.h,sha256=Sji70AuVcuZSotLF7Gotmun9MJuOHo8wEkxizKXLRtc,258
transformers/kernels/yoso/common_cuda_device.h,sha256=y6WUgAiapnMKqthRMS5s-DMSWNVkar_i8g4KPFvqiuk,2063
transformers/kernels/yoso/fast_lsh_cumulation.cu,sha256=LA4LGNgyXT3osIyQtFBcRanSyNQWm8yqmpz7AeLP7cw,19061
transformers/kernels/yoso/fast_lsh_cumulation.h,sha256=1cTWZjOm751HGiEB5P-UPJ8SE1VO7XRyXmBgyxYDyjI,1575
transformers/kernels/yoso/fast_lsh_cumulation_cuda.cu,sha256=HKGLWl-WFz5BXjaAPHTNTbG6IUkJjhBdvFf2K7hrDVQ,32870
transformers/kernels/yoso/fast_lsh_cumulation_cuda.h,sha256=_KGI8HQbVFtCN5KAcSGpyiJ2foGi26RKen138CUc2fY,5490
transformers/kernels/yoso/fast_lsh_cumulation_torch.cpp,sha256=-Rh7o39Z3rtOPwNnEM-c51TCqywpVdK0WVaA7VRrXbQ,3154
transformers/modelcard.py,sha256=7MtPjiopl9_f0KasQAcJzPuNLJuhuvf_Rt3ubAqkeVw,35153
transformers/modeling_attn_mask_utils.py,sha256=kNkNqix19Ml7MK4jDl40WG-iKuqlcfMxTwDW2KzBPXU,21330
transformers/modeling_flash_attention_utils.py,sha256=anWPlpL4eJXFnhnMO2kY3s3yRd05727ZUZtq0oYEemg,9781
transformers/modeling_flax_outputs.py,sha256=wXse1g9VyQyVOZ9DrbPALeoZBdS45fsBA9fNrGnwaZc,41961
transformers/modeling_flax_pytorch_utils.py,sha256=bNDgwGW9hnmafbFmkeOhp_9-tQJGYBRMh76TME8PBEk,21884
transformers/modeling_flax_utils.py,sha256=Q0qsTzUNN2Jj9ABkzfQ2_79VPYUa5GrllS-3y1-_v8o,61485
transformers/modeling_gguf_pytorch_utils.py,sha256=yX2kWU-1r0mkfW6_lt7ejRxYg9yWBt_pBg1XJslYbOg,7046
transformers/modeling_outputs.py,sha256=CYpjijqZNOVUc-kixDLI-jMFru9MhpDQvnncSfp0wb4,112567
transformers/modeling_rope_utils.py,sha256=pUZ5l4CdpDAEFrn8GDpC4ZO2CbQ4d3f1-d_UuL6nBiA,27349
transformers/modeling_tf_outputs.py,sha256=nXCMOmFZ7IZFVuiQr7EU2ciV9QqwOYPYld_r2jBxVpE,56074
transformers/modeling_tf_pytorch_utils.py,sha256=F9RBC-lPIZwTCsnIZlC68JCpdUJH1yzgIzGf7pnqfi0,27906
transformers/modeling_tf_utils.py,sha256=JFIjIYhXOiaxATxpf-6lRnn8fra1G5Efg0nLFl4rXIc,166816
transformers/modeling_utils.py,sha256=6uM52ADooPinIOsKd1MQ1hwYIwawrD9C7eVoaeFZgr8,253718
transformers/models/__init__.py,sha256=SNdc_sfcJAMJmrYTPtGrw2uraBW87NDo5WKStlqpxmM,4170
transformers/models/__pycache__/__init__.cpython-310.pyc,,
transformers/models/albert/__init__.py,sha256=Pp-10hfpG5RP3uZhOsyhRhTkxGIZmIHUsMtD-Zs3KR8,5202
transformers/models/albert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/albert/__pycache__/configuration_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/convert_albert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/albert/__pycache__/modeling_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/modeling_flax_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/modeling_tf_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/tokenization_albert.cpython-310.pyc,,
transformers/models/albert/__pycache__/tokenization_albert_fast.cpython-310.pyc,,
transformers/models/albert/configuration_albert.py,sha256=hjJpNZtG4j5duCxDQ8tOogxYulLPa4QSkYHM1IX0E4M,8088
transformers/models/albert/convert_albert_original_tf_checkpoint_to_pytorch.py,sha256=sj2zzT7a1poQRuEpKB_9RUHaIPV6uc0P5zhgvSBfZV0,2161
transformers/models/albert/modeling_albert.py,sha256=0nnGFX3XvA09BeWPWtzotkKo4isTJuXtGEDAohOyG5U,60668
transformers/models/albert/modeling_flax_albert.py,sha256=u2EEkckxVFt5WA8oQNbLJGcV5mhHGIJ6DMS867O150U,40739
transformers/models/albert/modeling_tf_albert.py,sha256=qKG80OTDkWgqRO-ozkJkRthkIdv40lv96FrNKn8AwZY,68847
transformers/models/albert/tokenization_albert.py,sha256=IRZ5iP4Dqfq80EfT5dYwILfais7utRizpqUliThSAVE,14421
transformers/models/albert/tokenization_albert_fast.py,sha256=mU4gt_qDDamz2_SFthxXAq4WaGjGNRil7Jm6pQEaFmU,8830
transformers/models/align/__init__.py,sha256=KJAlYcAgYK8kaV0_YWVBIbVyVVcNblM2QE1Nqafjhcg,1876
transformers/models/align/__pycache__/__init__.cpython-310.pyc,,
transformers/models/align/__pycache__/configuration_align.cpython-310.pyc,,
transformers/models/align/__pycache__/convert_align_tf_to_hf.cpython-310.pyc,,
transformers/models/align/__pycache__/modeling_align.cpython-310.pyc,,
transformers/models/align/__pycache__/processing_align.cpython-310.pyc,,
transformers/models/align/configuration_align.py,sha256=VxRIaqPDl_U3Noft-GzDe7C8qF9MNmjwa1Pl0naV5oo,18098
transformers/models/align/convert_align_tf_to_hf.py,sha256=tzPoEMyLV_ckVngYdvJ6uAFZ6RgsuX55JYjEkIMtPTg,15536
transformers/models/align/modeling_align.py,sha256=dyg_4fHOKABCfcCZXPBsQcUJGakePKt1RZgfP5GoK6A,71883
transformers/models/align/processing_align.py,sha256=TZC74dfePJvXfGKlzCTuSrlzYrqLxWBbU0CMz6ubigU,7214
transformers/models/altclip/__init__.py,sha256=3qZWyXWFlz69MPKa4NUm6c___7M-tQaypRpTswpS3C8,1930
transformers/models/altclip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/altclip/__pycache__/configuration_altclip.cpython-310.pyc,,
transformers/models/altclip/__pycache__/modeling_altclip.cpython-310.pyc,,
transformers/models/altclip/__pycache__/processing_altclip.cpython-310.pyc,,
transformers/models/altclip/configuration_altclip.py,sha256=H0EHFr73gRgHEPbjhVWH8oXGCXKbtNo_gojEOauSPHg,19705
transformers/models/altclip/modeling_altclip.py,sha256=z8gPQb1WjhcPbI53tc7zuvbiWN0xjiEtBYFxhYu55L4,77845
transformers/models/altclip/processing_altclip.py,sha256=Kea9qDSSdAidsTqi7Fi7F9M2sc4xHLyhOoy0Zj24bAo,6397
transformers/models/audio_spectrogram_transformer/__init__.py,sha256=WkcfCgQ_K5we_2ErzqZoiJY8WcWpPgT5Trw3uN09Fy8,1860
transformers/models/audio_spectrogram_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/configuration_audio_spectrogram_transformer.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/convert_audio_spectrogram_transformer_original_to_pytorch.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/feature_extraction_audio_spectrogram_transformer.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/__pycache__/modeling_audio_spectrogram_transformer.cpython-310.pyc,,
transformers/models/audio_spectrogram_transformer/configuration_audio_spectrogram_transformer.py,sha256=7OByAwVjcjeCGAwrnSgE_6nyka5QzrcNPFUUv1wKJYY,5426
transformers/models/audio_spectrogram_transformer/convert_audio_spectrogram_transformer_original_to_pytorch.py,sha256=clecd7YFd7_RqtMhbHrCjnC7KCiPZSRcJ7Ee7Eg_9Fw,11125
transformers/models/audio_spectrogram_transformer/feature_extraction_audio_spectrogram_transformer.py,sha256=CLMcdUUk8ehA2PC9wEBwvWd68tIMFtZswNhVbVwXWc8,9908
transformers/models/audio_spectrogram_transformer/modeling_audio_spectrogram_transformer.py,sha256=nOXgnrXovtvr1xhyn3ZYDMz_BHsjS_e_oFdxcX6eWqw,27548
transformers/models/auto/__init__.py,sha256=ZX05Z1Q5v4YLn24_TWDOU9FbiZbn5-9nyb-tBNmgTls,16768
transformers/models/auto/__pycache__/__init__.cpython-310.pyc,,
transformers/models/auto/__pycache__/auto_factory.cpython-310.pyc,,
transformers/models/auto/__pycache__/configuration_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/feature_extraction_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/image_processing_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/modeling_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/modeling_flax_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/modeling_tf_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/processing_auto.cpython-310.pyc,,
transformers/models/auto/__pycache__/tokenization_auto.cpython-310.pyc,,
transformers/models/auto/auto_factory.py,sha256=897tG3bsawRViHksNJv1WWxh7o_niAxavFazrk9hHsw,43111
transformers/models/auto/configuration_auto.py,sha256=pUGPKrznzFqMu8BSteECWbM8XpXPoGtFSXqJ3itJtPM,39555
transformers/models/auto/feature_extraction_auto.py,sha256=qpfE3LCTJOSDCNP2g2FkBg2ckjViFgQosCiP_UJosW8,19468
transformers/models/auto/image_processing_auto.py,sha256=4f0o4ZBcdR4AsqiJz4wBH28j_XPjE3i0shousgQsRRw,28239
transformers/models/auto/modeling_auto.py,sha256=fg2X9z5Fa7rr5s3DrIuj-vMhcKzxz_TV8R0ZIoZYcno,70417
transformers/models/auto/modeling_flax_auto.py,sha256=UI4jDZ6r3j_MGI_gnAwpYR9eInyAHYuypq5pJrcFr14,14473
transformers/models/auto/modeling_tf_auto.py,sha256=hgO8Re6rW2lPm1r9etcjQLmgF2aiIVL5m1aWyyZ5szE,28420
transformers/models/auto/processing_auto.py,sha256=AtmluNbTNmoaH9JWhG307Koa__tw0LnSNqYTzneVU3Y,17335
transformers/models/auto/tokenization_auto.py,sha256=S0b6cWpkjxdUGCxuZaSDXjhPgn6EG9DYnbseRpdTwWU,48559
transformers/models/autoformer/__init__.py,sha256=5xv9eb6R-4PmNJ4v-ogeo7pgobDRokFl4nWqqjnWII0,1691
transformers/models/autoformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/autoformer/__pycache__/configuration_autoformer.cpython-310.pyc,,
transformers/models/autoformer/__pycache__/modeling_autoformer.cpython-310.pyc,,
transformers/models/autoformer/configuration_autoformer.py,sha256=nUob6DtR8W7oVMdRTIOtM7-0raU5Vmtpvalo0QT4t0s,12141
transformers/models/autoformer/modeling_autoformer.py,sha256=nH0M5AHGv6ft8hcVU2PpGPZOBfh0gGnNM2QZ3oZx0Ms,108745
transformers/models/bark/__init__.py,sha256=0ycNDLHODu0Xlo6TJoe-0AJFyEHwUb8biq6aCAtmKRw,2028
transformers/models/bark/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bark/__pycache__/configuration_bark.cpython-310.pyc,,
transformers/models/bark/__pycache__/convert_suno_to_hf.cpython-310.pyc,,
transformers/models/bark/__pycache__/generation_configuration_bark.cpython-310.pyc,,
transformers/models/bark/__pycache__/modeling_bark.cpython-310.pyc,,
transformers/models/bark/__pycache__/processing_bark.cpython-310.pyc,,
transformers/models/bark/configuration_bark.py,sha256=JG07VbDPKgp8INf4NlWC4qPmVQ1vXpM_8ZpP8Vwy_Z0,12835
transformers/models/bark/convert_suno_to_hf.py,sha256=9Al9mccdhIX1pT3k_bECmGOXii25gx6D1A7dSs94U4Y,9374
transformers/models/bark/generation_configuration_bark.py,sha256=NebuWEiDWpcX68pVYLY4rgtbYLe1GTjefdvymgerLtM,14991
transformers/models/bark/modeling_bark.py,sha256=U7sJ-CnqN0r5oQI4PvPLgSVUr4qPIVACHzA6qNnfo1I,81518
transformers/models/bark/processing_bark.py,sha256=tJrG0Oc5z90mNQD8wZNFxKZQnFT_ikFeAMM9xljT3MA,13310
transformers/models/bart/__init__.py,sha256=1waRouI1H1jDBwRDuupPEI3b9ZNUC3E4gPl9IBjZr34,4229
transformers/models/bart/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bart/__pycache__/configuration_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/convert_bart_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bart/__pycache__/modeling_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/modeling_flax_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/modeling_tf_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/tokenization_bart.cpython-310.pyc,,
transformers/models/bart/__pycache__/tokenization_bart_fast.cpython-310.pyc,,
transformers/models/bart/configuration_bart.py,sha256=LO1wezTZSHet8E4-o54nHosRErJqYPj8FBGHIReBOZ8,18783
transformers/models/bart/convert_bart_original_pytorch_checkpoint_to_pytorch.py,sha256=kJ1H5FbOt_p3YqeR8ezBKaOV0Po2UcOHJRiRdUcJ90c,6054
transformers/models/bart/modeling_bart.py,sha256=4fRi5BRzE_fWRS9WKWP7xqgJPopTi_DjviMMrgIxgZo,104901
transformers/models/bart/modeling_flax_bart.py,sha256=CIFvtyJhEHDasBeahGeJy03JFaced47wau5UD6LZR9E,82706
transformers/models/bart/modeling_tf_bart.py,sha256=pHqwdJ68DT4V4AWdVWr8Sz0UCg6SsqBSUdejwnCoFN0,80771
transformers/models/bart/tokenization_bart.py,sha256=fqHZCAZwmM4QoFAk6unDGwrbXtZkV1kPWMsVIu7HrPg,16250
transformers/models/bart/tokenization_bart_fast.py,sha256=h-DIyLB7ii7QjTUFF78nU8e5fPVEMUCXaPKkHqMI71E,11723
transformers/models/barthez/__init__.py,sha256=7IXg6okZoJ10NCYRWn0GvoWWUvGUN27eIw7CzJ5CVGA,1848
transformers/models/barthez/__pycache__/__init__.cpython-310.pyc,,
transformers/models/barthez/__pycache__/tokenization_barthez.cpython-310.pyc,,
transformers/models/barthez/__pycache__/tokenization_barthez_fast.cpython-310.pyc,,
transformers/models/barthez/tokenization_barthez.py,sha256=I0jL8PMdPvxb_uLh7IRqVnSnzzMgo_dSx8J_wB_tGlg,12064
transformers/models/barthez/tokenization_barthez_fast.py,sha256=DAC2_iaNphgJCF1ZQHFUDHDPW47d8JCPzxz30lhMTY4,7836
transformers/models/bartpho/__init__.py,sha256=Q0mAOPJGQaHHigdajLg5-2TPOw9NWw5uIRQlmfhh8Ds,1362
transformers/models/bartpho/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bartpho/__pycache__/tokenization_bartpho.cpython-310.pyc,,
transformers/models/bartpho/tokenization_bartpho.py,sha256=bxgUG0U4zgzBJKapa7snw3facOwyj12YJePC-jwQDno,13523
transformers/models/beit/__init__.py,sha256=zVVV08SMWyks3fXFUsyS5jK2-dZGLOyvcZmp4RvnWpY,3171
transformers/models/beit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/beit/__pycache__/configuration_beit.cpython-310.pyc,,
transformers/models/beit/__pycache__/convert_beit_unilm_to_pytorch.cpython-310.pyc,,
transformers/models/beit/__pycache__/feature_extraction_beit.cpython-310.pyc,,
transformers/models/beit/__pycache__/image_processing_beit.cpython-310.pyc,,
transformers/models/beit/__pycache__/modeling_beit.cpython-310.pyc,,
transformers/models/beit/__pycache__/modeling_flax_beit.cpython-310.pyc,,
transformers/models/beit/configuration_beit.py,sha256=4oK_IfBO94rh0qkLYwhdW3qthVpPyQQOMg54HU79F1U,11548
transformers/models/beit/convert_beit_unilm_to_pytorch.py,sha256=Pdqslow71bJYQioU21U5rWEjXSQC4z_dOnVgKfPxWYI,16601
transformers/models/beit/feature_extraction_beit.py,sha256=C9wchKLt3K__wzqOkDWsbK0hMPzVn9HZtm5KPI5Oq2s,1172
transformers/models/beit/image_processing_beit.py,sha256=3-vR1CISdkww0gHbmmfzukEp108okffY46PhhPZZnS8,24416
transformers/models/beit/modeling_beit.py,sha256=CjfLNmyWQ5Q3t1BJytIEAvGCMg2s8ylbe10FbnDTeL4,66502
transformers/models/beit/modeling_flax_beit.py,sha256=9_xkFN7xtiLrxbShhpX8EgpY8kuOKIui-OlRidmNUAI,36996
transformers/models/bert/__init__.py,sha256=HWErikSRkfAKjM5ugAe71O36-EzQ3wJdvgYCErXI4Ew,5789
transformers/models/bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bert/__pycache__/configuration_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/convert_bert_original_tf2_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bert/__pycache__/convert_bert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bert/__pycache__/convert_bert_pytorch_checkpoint_to_original_tf.cpython-310.pyc,,
transformers/models/bert/__pycache__/convert_bert_token_dropping_original_tf2_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bert/__pycache__/modeling_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/modeling_flax_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/modeling_tf_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/tokenization_bert.cpython-310.pyc,,
transformers/models/bert/__pycache__/tokenization_bert_fast.cpython-310.pyc,,
transformers/models/bert/__pycache__/tokenization_bert_tf.cpython-310.pyc,,
transformers/models/bert/configuration_bert.py,sha256=OQ1E5rqkz3RcpeMBO1sybR7VCoKefI938yyILrbcot8,7244
transformers/models/bert/convert_bert_original_tf2_checkpoint_to_pytorch.py,sha256=q7RA_4S9OkAxFdmmGUV_Lf9qKYoRshJJbQMAHxscsRU,10491
transformers/models/bert/convert_bert_original_tf_checkpoint_to_pytorch.py,sha256=eSJs7TBQPBfZBPAmBJ7L8JKqKWBewvWKsnVbklmHkNc,2158
transformers/models/bert/convert_bert_pytorch_checkpoint_to_original_tf.py,sha256=6nISsCdgO_sJFFiLpnkGGsmTqC9Yp-gzDPDM-EafVXA,4112
transformers/models/bert/convert_bert_token_dropping_original_tf2_checkpoint_to_pytorch.py,sha256=ksipaccVHXMHrGrNJp_zAHemziQvYMMcG7fzIo00DQw,7607
transformers/models/bert/modeling_bert.py,sha256=3egZuEmHB8FYrqowebbaujTrCvrj0zwjCbSApEtv2kc,90845
transformers/models/bert/modeling_flax_bert.py,sha256=UMRUMxvvwu8oIzkLfVjXWP9Y47WolZPtZFELypsG-pg,63672
transformers/models/bert/modeling_tf_bert.py,sha256=hCYLufTiN_5TCQAxE-r476PYpQMVsDejLaDFH2Zoni0,94291
transformers/models/bert/tokenization_bert.py,sha256=shsy_B0WVgGgH0N8XC2xoyiUoeubTYKyHeNcG7F7tPA,20527
transformers/models/bert/tokenization_bert_fast.py,sha256=FoAutpMtmt_D77Z82RtBcttl8Cl5P2Rdt_HFIKUT2m8,7652
transformers/models/bert/tokenization_bert_tf.py,sha256=1zWzz3FPrh5zWqRG7YVY_wIVCzzB8iNGR6MGx48ke3c,11895
transformers/models/bert_generation/__init__.py,sha256=2XUvSVePne5Hspjzn6l_PonKfZ9WXjRBub9bevOv8R4,2275
transformers/models/bert_generation/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bert_generation/__pycache__/configuration_bert_generation.cpython-310.pyc,,
transformers/models/bert_generation/__pycache__/modeling_bert_generation.cpython-310.pyc,,
transformers/models/bert_generation/__pycache__/tokenization_bert_generation.cpython-310.pyc,,
transformers/models/bert_generation/configuration_bert_generation.py,sha256=zrKF15fHFYwjaQxRB0dOjQWYVoHl1DZfQ90d3gkamlU,6340
transformers/models/bert_generation/modeling_bert_generation.py,sha256=NqiXY2282mWcW1ex0uov3wNqXQGTyhTe1TdFPWnxhc8,48503
transformers/models/bert_generation/tokenization_bert_generation.py,sha256=rN1VJOLn-nw8AU3ev9DXeXQs0xzzEt7qt_eI6UtMGJU,7076
transformers/models/bert_japanese/__init__.py,sha256=6prQNXS2J4cWXqAqkqDyxNmzx-vaFQtOjJQio-ZUc4g,1053
transformers/models/bert_japanese/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bert_japanese/__pycache__/tokenization_bert_japanese.cpython-310.pyc,,
transformers/models/bert_japanese/tokenization_bert_japanese.py,sha256=rVDIJTYV2sbWx66vH4GjSia1ii2S060RwOgZBPri_gk,39027
transformers/models/bertweet/__init__.py,sha256=sXE2NweoWp8UIaJkuSaLSw4EaSEzpWwBe3pegec_Kj0,959
transformers/models/bertweet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bertweet/__pycache__/tokenization_bertweet.cpython-310.pyc,,
transformers/models/bertweet/tokenization_bertweet.py,sha256=Zgm2IY94qRnlptRVyc6ibyKCG-FptmGaRPrt8ehWmNM,26986
transformers/models/big_bird/__init__.py,sha256=rBiXDLcASar91jtVI6lzsJBIrydXU_CJUHdIkY49LQs,4390
transformers/models/big_bird/__pycache__/__init__.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/configuration_big_bird.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/convert_bigbird_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/modeling_big_bird.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/modeling_flax_big_bird.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/tokenization_big_bird.cpython-310.pyc,,
transformers/models/big_bird/__pycache__/tokenization_big_bird_fast.cpython-310.pyc,,
transformers/models/big_bird/configuration_big_bird.py,sha256=1YAMtLXa4rK4fKUCGcj7AgM1faAoKC7O3UglBl-1EQI,7832
transformers/models/big_bird/convert_bigbird_original_tf_checkpoint_to_pytorch.py,sha256=qWXzJVxALd_0AYy1OETdK7LuVZFICtSiqv2vtv04zE0,2492
transformers/models/big_bird/modeling_big_bird.py,sha256=fLVIJc0Y91oeb_ZbL8sgs80NNLtG64vk76rAfoGyP6s,142464
transformers/models/big_bird/modeling_flax_big_bird.py,sha256=ePVW-6VwD8sgJYIlX4eWv0EVNaInVosJW_CtqlyzpGs,109510
transformers/models/big_bird/tokenization_big_bird.py,sha256=gdDnyZbg5JcymJHvjRk7lgAelB4hm9hEM2loRL39n5o,14217
transformers/models/big_bird/tokenization_big_bird_fast.py,sha256=aHbMkEFSbFIz2DNd9xNJPOijyojQnIF1lASFJfbZMbM,10166
transformers/models/bigbird_pegasus/__init__.py,sha256=FdQfzkjpqYAf02mpcAIkFoGoN8m9Gwfgz4vb_wxYJHQ,2088
transformers/models/bigbird_pegasus/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bigbird_pegasus/__pycache__/configuration_bigbird_pegasus.cpython-310.pyc,,
transformers/models/bigbird_pegasus/__pycache__/convert_bigbird_pegasus_tf_to_pytorch.cpython-310.pyc,,
transformers/models/bigbird_pegasus/__pycache__/modeling_bigbird_pegasus.cpython-310.pyc,,
transformers/models/bigbird_pegasus/configuration_bigbird_pegasus.py,sha256=PMwMgvudJ2vCl9jUj1AexknnjGI8w5BdOdM_Or9mbZU,19215
transformers/models/bigbird_pegasus/convert_bigbird_pegasus_tf_to_pytorch.py,sha256=Wc7aoNvtzxt-DPi655Kl30CgDgq_hp08psISb8dWpLU,6288
transformers/models/bigbird_pegasus/modeling_bigbird_pegasus.py,sha256=5oV9EVTWQOhh286baXT1k2F_1BhFnnDgTlWENSrBnP0,146660
transformers/models/biogpt/__init__.py,sha256=Gwpn8dB_Bc1wXlZq9s7gGBwA3DvQR6cx1GBYqPlTIMg,1882
transformers/models/biogpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/biogpt/__pycache__/configuration_biogpt.cpython-310.pyc,,
transformers/models/biogpt/__pycache__/convert_biogpt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/biogpt/__pycache__/modeling_biogpt.cpython-310.pyc,,
transformers/models/biogpt/__pycache__/tokenization_biogpt.cpython-310.pyc,,
transformers/models/biogpt/configuration_biogpt.py,sha256=08V2Gvyw0oQnuSXSLgZvt3GNDGZ4jnnH5V6eQWQqjUg,6178
transformers/models/biogpt/convert_biogpt_original_pytorch_checkpoint_to_pytorch.py,sha256=5zNYzaEy7QPc99LCHTcofXSCI3tr0pzlIpFpwT1ZgN0,10578
transformers/models/biogpt/modeling_biogpt.py,sha256=olfLN6jqVRoIlh3wo1HNu2YNomfWYo222Ln95HeYRzM,41610
transformers/models/biogpt/tokenization_biogpt.py,sha256=SW4-KCJ8piZPhAZK75hX-NqkyAN9eYkVt4fghFfOTVA,13257
transformers/models/bit/__init__.py,sha256=chlptH4-lUvMRhJ1Hkf1s7BrMJckyhi8K3DghWWPj_k,2080
transformers/models/bit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bit/__pycache__/configuration_bit.cpython-310.pyc,,
transformers/models/bit/__pycache__/convert_bit_to_pytorch.cpython-310.pyc,,
transformers/models/bit/__pycache__/image_processing_bit.cpython-310.pyc,,
transformers/models/bit/__pycache__/modeling_bit.cpython-310.pyc,,
transformers/models/bit/configuration_bit.py,sha256=gBrTquEDWGjBOhwsF4O171YesHpqRsy4IrdSINcucK4,6269
transformers/models/bit/convert_bit_to_pytorch.py,sha256=fheumYRIVQwZoZCWcl0btZh7LhandaEeAoS6itUmkwk,5954
transformers/models/bit/image_processing_bit.py,sha256=NjlrvLfIuCExl48RLRO-5kft5NwqwhZPjex7qBjDSr8,16395
transformers/models/bit/modeling_bit.py,sha256=_DOKmn1TfiAMeloRlOTNOKVCAUFr7A0sUCYuMeLpxv0,32211
transformers/models/blenderbot/__init__.py,sha256=RzwSoueJsJE2O3rQvrpWqrL2n-j4-HI4fG6iID2cDYI,3823
transformers/models/blenderbot/__pycache__/__init__.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/configuration_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/convert_blenderbot_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/modeling_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/modeling_flax_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/modeling_tf_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/tokenization_blenderbot.cpython-310.pyc,,
transformers/models/blenderbot/__pycache__/tokenization_blenderbot_fast.cpython-310.pyc,,
transformers/models/blenderbot/configuration_blenderbot.py,sha256=d9ammp_ehMYsLl3hw5Q8lK0vvHaZQK1ZVinFaLhi5uE,18781
transformers/models/blenderbot/convert_blenderbot_original_pytorch_checkpoint_to_pytorch.py,sha256=86QBWYTeyJvxMUOfxqmGHwpDneadfqbEGSujMYw3yuU,3702
transformers/models/blenderbot/modeling_blenderbot.py,sha256=jovr9nwHNqBMM9lsivAR6SSfkOvSKG1GTA2MF5pAXkg,76398
transformers/models/blenderbot/modeling_flax_blenderbot.py,sha256=cvS4Pzq8B61WHLKM_LmItSB5wY_jfNJgy6DVDhzofuk,64984
transformers/models/blenderbot/modeling_tf_blenderbot.py,sha256=8ANVm-AnugLGSGpaYJLQRybg-GnLxCih92FDEShlsjw,72694
transformers/models/blenderbot/tokenization_blenderbot.py,sha256=-K3W7daa8Hjm2toOFN-WB6EdA6KuKPn0N2xjXMzYWqM,18652
transformers/models/blenderbot/tokenization_blenderbot_fast.py,sha256=p1yYVlyeDvREQ5OjPIPvOxbj_zqInO2S78SAJhZSaj0,13455
transformers/models/blenderbot_small/__init__.py,sha256=PtsaXqUCp7b95CsjdWPFMJu6dy2ZQBKaU2T2k1E89M4,4031
transformers/models/blenderbot_small/__pycache__/__init__.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/configuration_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_flax_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/modeling_tf_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/tokenization_blenderbot_small.cpython-310.pyc,,
transformers/models/blenderbot_small/__pycache__/tokenization_blenderbot_small_fast.cpython-310.pyc,,
transformers/models/blenderbot_small/configuration_blenderbot_small.py,sha256=on2JyBGUYPH5q1hTT7KAPnODclvuJ_w6NJfRQBwZ9zs,18213
transformers/models/blenderbot_small/modeling_blenderbot_small.py,sha256=ChhmqaddpkT1F_ni0NYSjzlHwc_sRtf_1bFjhRibD3U,74445
transformers/models/blenderbot_small/modeling_flax_blenderbot_small.py,sha256=kNoW_xxZluiAntCS5lokqBhyQNPe8a7UVEsADMAO1z0,65944
transformers/models/blenderbot_small/modeling_tf_blenderbot_small.py,sha256=ueMse3vwu4mqDf5pLpORjKNHpPZWfbMMJWwilJoiauM,71606
transformers/models/blenderbot_small/tokenization_blenderbot_small.py,sha256=e-3c3xbu43MWvKdWy6-fYp0XYbZmNtWT73xqw4prfkM,8488
transformers/models/blenderbot_small/tokenization_blenderbot_small_fast.py,sha256=USm81G1vpi9sDzwfo5KaQAZe3RuEOuJtGhwEsEnxKWw,3887
transformers/models/blip/__init__.py,sha256=sqnXET78HA864F5e7zbXks3PmHhE88rsQ9qQuI4OLAc,3416
transformers/models/blip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/blip/__pycache__/configuration_blip.cpython-310.pyc,,
transformers/models/blip/__pycache__/convert_blip_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/blip/__pycache__/image_processing_blip.cpython-310.pyc,,
transformers/models/blip/__pycache__/modeling_blip.cpython-310.pyc,,
transformers/models/blip/__pycache__/modeling_blip_text.cpython-310.pyc,,
transformers/models/blip/__pycache__/modeling_tf_blip.cpython-310.pyc,,
transformers/models/blip/__pycache__/modeling_tf_blip_text.cpython-310.pyc,,
transformers/models/blip/__pycache__/processing_blip.cpython-310.pyc,,
transformers/models/blip/configuration_blip.py,sha256=7QX-b9m2MtWIWmCzCuwM1N4xmmCTNbyw0XeP3VIO4PQ,16474
transformers/models/blip/convert_blip_original_pytorch_to_hf.py,sha256=olLA10DbRUnCUOY2uHxF70u3W9wY2EBwm7eyAGfm8nM,6992
transformers/models/blip/image_processing_blip.py,sha256=hn7D0Svr6hfuggT9wPSgXuHkEg_bjjpHZL4oVM7d1So,15692
transformers/models/blip/modeling_blip.py,sha256=s9l88BxrtteVh4fuXy5RqdMSH6CzHeVOn1hvgJfbIZM,67264
transformers/models/blip/modeling_blip_text.py,sha256=PL6N5wwyUjUT7hpmACr0W5R4TYWJBIWDhCnT8WnMPpI,43931
transformers/models/blip/modeling_tf_blip.py,sha256=owkHeUXz-Zx1FNN35vNOae-WlMqZO4MHKB4zidpBm-Q,71312
transformers/models/blip/modeling_tf_blip_text.py,sha256=iJiYcnZpqJhoNrfUcxPxtokT_qMJGgLyz1hAcAWZ-t4,49972
transformers/models/blip/processing_blip.py,sha256=8d8Rag_jCOF27MMaWfUOhqK7QkJNBSxBqAL42IffLek,6237
transformers/models/blip_2/__init__.py,sha256=AePPXCpPEVals6ZIt4-bjO_1Q633dsMFddpS2gj4znU,1961
transformers/models/blip_2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/blip_2/__pycache__/configuration_blip_2.cpython-310.pyc,,
transformers/models/blip_2/__pycache__/convert_blip_2_original_to_pytorch.cpython-310.pyc,,
transformers/models/blip_2/__pycache__/modeling_blip_2.cpython-310.pyc,,
transformers/models/blip_2/__pycache__/processing_blip_2.cpython-310.pyc,,
transformers/models/blip_2/configuration_blip_2.py,sha256=tT3HIwO_PcV92JuSxVEbsMoS6L5nMnUZDOXZlRmc2K8,16487
transformers/models/blip_2/convert_blip_2_original_to_pytorch.py,sha256=0343xouUoM4JqP29bgDyCbNIJfSl8BO-e278133ytSA,12276
transformers/models/blip_2/modeling_blip_2.py,sha256=iz5gxY1JXiK3CLXt9o81wv-mUmNaIA835ubwEBdextY,85907
transformers/models/blip_2/processing_blip_2.py,sha256=CUbXNzkwRbVQRm50EblnDzmZfE4gha0Vydnwrf_WdA4,6731
transformers/models/bloom/__init__.py,sha256=nV2bxWJBx9x3oa5SidEJB9XAnRLwu1LDqi0bFfz7f4Q,2926
transformers/models/bloom/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bloom/__pycache__/configuration_bloom.cpython-310.pyc,,
transformers/models/bloom/__pycache__/convert_bloom_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/bloom/__pycache__/modeling_bloom.cpython-310.pyc,,
transformers/models/bloom/__pycache__/modeling_flax_bloom.cpython-310.pyc,,
transformers/models/bloom/__pycache__/tokenization_bloom_fast.cpython-310.pyc,,
transformers/models/bloom/configuration_bloom.py,sha256=5BF8i4K8j3ACmkFnq5iGlsPnehhUxukn376O9AOgU30,10138
transformers/models/bloom/convert_bloom_original_checkpoint_to_pytorch.py,sha256=WtPFsgC47dhGDW6dHm5PC4SaZgZ6tF3umTFd8Qiw-1Q,10301
transformers/models/bloom/modeling_bloom.py,sha256=IzFypd7FAct0rYgAEgpqWnZiHqq5MPc1xSVlfzl8yfQ,54902
transformers/models/bloom/modeling_flax_bloom.py,sha256=zBWwHZI6OBs9S1h9JSSAaEnskPKpa8jHn5AROhbLXpw,30092
transformers/models/bloom/tokenization_bloom_fast.py,sha256=Z_NnzsKzbIgIOV8hoV75q1oQNyEEqvOauNaFhkdqOgM,6609
transformers/models/bridgetower/__init__.py,sha256=FK_XyizrV1Qzgy7F_Qbh_olKkVi0KjEQ-VQCqS-9vJo,2652
transformers/models/bridgetower/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bridgetower/__pycache__/configuration_bridgetower.cpython-310.pyc,,
transformers/models/bridgetower/__pycache__/image_processing_bridgetower.cpython-310.pyc,,
transformers/models/bridgetower/__pycache__/modeling_bridgetower.cpython-310.pyc,,
transformers/models/bridgetower/__pycache__/processing_bridgetower.cpython-310.pyc,,
transformers/models/bridgetower/configuration_bridgetower.py,sha256=b_oHdh7u1A0nWRUDnprSJojcjAdS_1gYw9Ut2zS1-kI,16209
transformers/models/bridgetower/image_processing_bridgetower.py,sha256=MS7LDFMYTUJHF-WIbxpcJAVPuhZGFkQCC8f7qgKkLxk,26821
transformers/models/bridgetower/modeling_bridgetower.py,sha256=uRjUEibUOLiUGKq7uN8gkaRY9o6wAOwH9nidDzwG8AI,88480
transformers/models/bridgetower/processing_bridgetower.py,sha256=FriChYR6CPgyDBUwOJrDlCJBuHo9RBIWXwN_NxgSGN8,5057
transformers/models/bros/__init__.py,sha256=HwARvDde808WqrRVjT5CxnacGOQpyxpVZF1EJ7wsCUQ,2277
transformers/models/bros/__pycache__/__init__.cpython-310.pyc,,
transformers/models/bros/__pycache__/configuration_bros.cpython-310.pyc,,
transformers/models/bros/__pycache__/convert_bros_to_pytorch.cpython-310.pyc,,
transformers/models/bros/__pycache__/modeling_bros.cpython-310.pyc,,
transformers/models/bros/__pycache__/processing_bros.cpython-310.pyc,,
transformers/models/bros/configuration_bros.py,sha256=VTx0gJQUJQepZFmYP7HPiLaLx04Kdz3CyxgAqkKLLEs,6391
transformers/models/bros/convert_bros_to_pytorch.py,sha256=kxZDGzvIYxz9hbIzzJOfOj5tixji5efb2884rqwoY6A,4871
transformers/models/bros/modeling_bros.py,sha256=UBiX26UhzlfXlsfhhIxEl-nYkmZH8Ng_dtujCoTawsM,57838
transformers/models/bros/processing_bros.py,sha256=FQUu5czHHvQzZ1P5N9GhfjZu4cmZw_mYKuX0VNjrB54,4193
transformers/models/byt5/__init__.py,sha256=06YhQd8TFNbc9lU5qzERZUdcSWIFxOeBOaqQh6S4WC4,942
transformers/models/byt5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/byt5/__pycache__/convert_byt5_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/byt5/__pycache__/tokenization_byt5.cpython-310.pyc,,
transformers/models/byt5/convert_byt5_original_tf_checkpoint_to_pytorch.py,sha256=LEibHPdlDdKdyB6XHB5s7pHRsqB5qQxUWN93H8G_q5k,2119
transformers/models/byt5/tokenization_byt5.py,sha256=MioRjnE5Bco3OgdbKcjfYZ7BMCm1ymu5ydwZUGrzC5U,10029
transformers/models/camembert/__init__.py,sha256=t1UUX2ZobpQSWOvBrjnshAEI0EVlDwngdxH-GIMyXTU,4145
transformers/models/camembert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/camembert/__pycache__/configuration_camembert.cpython-310.pyc,,
transformers/models/camembert/__pycache__/modeling_camembert.cpython-310.pyc,,
transformers/models/camembert/__pycache__/modeling_tf_camembert.cpython-310.pyc,,
transformers/models/camembert/__pycache__/tokenization_camembert.cpython-310.pyc,,
transformers/models/camembert/__pycache__/tokenization_camembert_fast.cpython-310.pyc,,
transformers/models/camembert/configuration_camembert.py,sha256=GxEMDYRqz9PXWIl_z9WU0_sceBrIGINiKx6abd0pogo,7349
transformers/models/camembert/modeling_camembert.py,sha256=nCg-XqvoRFxcSlD42tn8DiK1ZJMxOyPN_piQpu9Xijg,72578
transformers/models/camembert/modeling_tf_camembert.py,sha256=O3fafrTFS8DIuwinVZhxz4PisUQHjMHeP5Woe5WukdA,81530
transformers/models/camembert/tokenization_camembert.py,sha256=D_CDhvAXCbAQ-l2JQQ-H2LNjjnkggP5uvgsbMldZa0A,13976
transformers/models/camembert/tokenization_camembert_fast.py,sha256=g48XHTSlBEN99pWh8qyYh7wyJTkMJPd_x6xUdoyEzzA,8272
transformers/models/canine/__init__.py,sha256=O2uU4VpK9tqfnyM9-KsksCeIp46u9RcL5fJ1m4eNviM,2096
transformers/models/canine/__pycache__/__init__.cpython-310.pyc,,
transformers/models/canine/__pycache__/configuration_canine.cpython-310.pyc,,
transformers/models/canine/__pycache__/convert_canine_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/canine/__pycache__/modeling_canine.cpython-310.pyc,,
transformers/models/canine/__pycache__/tokenization_canine.cpython-310.pyc,,
transformers/models/canine/configuration_canine.py,sha256=8oAa0oJcRE7vPVtQRsxvsXH0Celi3QMbJTrk6rQANb0,6555
transformers/models/canine/convert_canine_original_tf_checkpoint_to_pytorch.py,sha256=zJ6VDpE58I4-ntOXDUCKPYXPnnhWkuYXCfejDchr9jY,2116
transformers/models/canine/modeling_canine.py,sha256=Nsv1UzVH_B7ve5R08HOT6wkcutDJReznF3KR2PMb7m8,73394
transformers/models/canine/tokenization_canine.py,sha256=bLQPsvTpk8GFuH3rsUR6o0l6f9Ldvb4S3Os0H-oFQQc,9287
transformers/models/chameleon/__init__.py,sha256=b8WOpzMVLP34ESkZnCmvO4gc-kGFFL0YW-ERjZQBgfU,2440
transformers/models/chameleon/__pycache__/__init__.cpython-310.pyc,,
transformers/models/chameleon/__pycache__/configuration_chameleon.cpython-310.pyc,,
transformers/models/chameleon/__pycache__/convert_chameleon_weights_to_hf.cpython-310.pyc,,
transformers/models/chameleon/__pycache__/image_processing_chameleon.cpython-310.pyc,,
transformers/models/chameleon/__pycache__/modeling_chameleon.cpython-310.pyc,,
transformers/models/chameleon/__pycache__/processing_chameleon.cpython-310.pyc,,
transformers/models/chameleon/configuration_chameleon.py,sha256=Ev7K4eenCbDlZgXhD8t_ihnHVnkK8TU0cJeUIy50aGI,13149
transformers/models/chameleon/convert_chameleon_weights_to_hf.py,sha256=BnVBFs3irmx_ai9SU45deZZ9OzsIPQ8JxQ4ubpsmDSU,20228
transformers/models/chameleon/image_processing_chameleon.py,sha256=TO3JrIBSE0sMj98eBNlsDZFMNDe1ersJCnsm_PyS2ug,18108
transformers/models/chameleon/modeling_chameleon.py,sha256=OujRnyrHgn-qIRugJLIMh4Qia2Mk0nFOdPJ0hgWkKy8,75424
transformers/models/chameleon/processing_chameleon.py,sha256=p0vEUgOYDC8RbL4wS5dRq4aKkarfE4An_QprGjnmlVE,8606
transformers/models/chinese_clip/__init__.py,sha256=KQXPKJM-dsey8SwyxD7CRSaoOEEhuVMHWDet9xcDPGQ,2703
transformers/models/chinese_clip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/configuration_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/convert_chinese_clip_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/feature_extraction_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/image_processing_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/modeling_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/__pycache__/processing_chinese_clip.cpython-310.pyc,,
transformers/models/chinese_clip/configuration_chinese_clip.py,sha256=n5dF26ntwtSqzhseCEUzSixh09gnzFzC1P0mMM9N0r0,22329
transformers/models/chinese_clip/convert_chinese_clip_original_pytorch_to_hf.py,sha256=-0bnVcdXxStmygkyj6S1hIGCVbpEbe3cM7AoshHH5ZE,5069
transformers/models/chinese_clip/feature_extraction_chinese_clip.py,sha256=znduyOyJ-Qdx4MC5CPb6MFZ-Wrb5PLgHWRh0xfoULR0,1247
transformers/models/chinese_clip/image_processing_chinese_clip.py,sha256=eIjF9ejRpZBkmGpzNSopH8FicTbd_5GuzvnA1vY0ia4,15946
transformers/models/chinese_clip/modeling_chinese_clip.py,sha256=GG6eLon0R68o-jZKicrkLiVWYLmegLkccsthrXX0x1o,73292
transformers/models/chinese_clip/processing_chinese_clip.py,sha256=xeAbYW_LoAVP3wwtnOdjoJ3kvCPUVoE8OFWMldm_QXY,6706
transformers/models/clap/__init__.py,sha256=MrwPO_4Wek_q0Lp735dzBv5HyOO43W8IeGk4cuu6HY4,2138
transformers/models/clap/__pycache__/__init__.cpython-310.pyc,,
transformers/models/clap/__pycache__/configuration_clap.cpython-310.pyc,,
transformers/models/clap/__pycache__/convert_clap_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/clap/__pycache__/feature_extraction_clap.cpython-310.pyc,,
transformers/models/clap/__pycache__/modeling_clap.cpython-310.pyc,,
transformers/models/clap/__pycache__/processing_clap.cpython-310.pyc,,
transformers/models/clap/configuration_clap.py,sha256=Ho885NS8ExbtZNoJ9dDsRUK-S5dEP_Ous1QqoEQ_EJI,20383
transformers/models/clap/convert_clap_original_pytorch_to_hf.py,sha256=FqHoVAYXIzfUY9342azwlm9zfSP7QdS8p-u9Q6RE_K4,5149
transformers/models/clap/feature_extraction_clap.py,sha256=8wvUSggjHicJBtnTDF56UsV-J2Nvelu16Pk7UMaim9o,18691
transformers/models/clap/modeling_clap.py,sha256=SDbOChSHE1VG2qFGK75d8SGJQTb1PIIyKobiBkghEqY,104792
transformers/models/clap/processing_clap.py,sha256=Ayo5mK5ZcvEvcEp9DhHOLFxaRJ9gwTNRhxi96WSHZdk,5678
transformers/models/clip/__init__.py,sha256=VVJWqRbDN0AUa7BrJMaexjKXJtJ3NgsCmRNQTAM4aM0,4909
transformers/models/clip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/clip/__pycache__/configuration_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/convert_clip_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/clip/__pycache__/feature_extraction_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/image_processing_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/modeling_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/modeling_flax_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/modeling_tf_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/processing_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/tokenization_clip.cpython-310.pyc,,
transformers/models/clip/__pycache__/tokenization_clip_fast.cpython-310.pyc,,
transformers/models/clip/configuration_clip.py,sha256=NzuGoSFLSGu-70X4pIPvgEOwFbu2yiS6UXdRSffHUUo,20896
transformers/models/clip/convert_clip_original_pytorch_to_hf.py,sha256=EOdVlGGBObrXAIdRyND_Ko3G1ZWf0miyga1QcAH9Q8I,5570
transformers/models/clip/feature_extraction_clip.py,sha256=hgRfD-s9DoI7tzDLAJ0EW3rSbkY9dOiGqoGClOiRiBM,1172
transformers/models/clip/image_processing_clip.py,sha256=aVAVslfxX12S3D0PZ4tTaBYpp1eSEYXSR8DJ2bDzNNQ,16852
transformers/models/clip/modeling_clip.py,sha256=UablOGZLrN2L2U5eeZgcw-oJPLSCKVoxBVfcLXxWkHw,70450
transformers/models/clip/modeling_flax_clip.py,sha256=4uabm9t6i4bnqRR3DZrGk7X1NcaV78L6b6E6i0Gkl2U,50517
transformers/models/clip/modeling_tf_clip.py,sha256=TEom4goA3lt5lH8X4N6P6tnxhLbJHHiGZnm15zN5aGw,60358
transformers/models/clip/processing_clip.py,sha256=xXp4RfloqWH1K1dFCL81jGvaOowCNQ2s0CU1vz2ClP8,7148
transformers/models/clip/tokenization_clip.py,sha256=lDCHtIoqfquNg1n69Eok2jnV1mv5ebZStvt32cnowRU,20584
transformers/models/clip/tokenization_clip_fast.py,sha256=IB9fydB2vYm5llPx-NmmpO5oIgFcxbu2FZubWrs16a8,6746
transformers/models/clipseg/__init__.py,sha256=C9quQq_rkeAfAm2P2CFj9VDG7ZMdH8Q_l0Wp8LYe4F0,1983
transformers/models/clipseg/__pycache__/__init__.cpython-310.pyc,,
transformers/models/clipseg/__pycache__/configuration_clipseg.cpython-310.pyc,,
transformers/models/clipseg/__pycache__/convert_clipseg_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/clipseg/__pycache__/modeling_clipseg.cpython-310.pyc,,
transformers/models/clipseg/__pycache__/processing_clipseg.cpython-310.pyc,,
transformers/models/clipseg/configuration_clipseg.py,sha256=deTnGyDuir1SJXmiTa6QIiEUolPK4J5_1Rnkc4unXas,20930
transformers/models/clipseg/convert_clipseg_original_pytorch_to_hf.py,sha256=kYyPxdpdtt6nSxD65tXUTMbN0xPyyzjfTOOMbQ8OL0Y,11114
transformers/models/clipseg/modeling_clipseg.py,sha256=8wlrJ3bzTW3NrrFWFMk8M3KE760u5kInVBMYdap36ZU,64453
transformers/models/clipseg/processing_clipseg.py,sha256=dm7u-6S5Hg1ITAc0lYzXRJssiR92LOMkWbnR7p4eHzE,7790
transformers/models/clvp/__init__.py,sha256=uLsRO6PqkUJo5CfxII7wW4AN2G944EQwS_EonAoKwMc,2212
transformers/models/clvp/__pycache__/__init__.cpython-310.pyc,,
transformers/models/clvp/__pycache__/configuration_clvp.cpython-310.pyc,,
transformers/models/clvp/__pycache__/convert_clvp_to_hf.cpython-310.pyc,,
transformers/models/clvp/__pycache__/feature_extraction_clvp.cpython-310.pyc,,
transformers/models/clvp/__pycache__/modeling_clvp.cpython-310.pyc,,
transformers/models/clvp/__pycache__/number_normalizer.cpython-310.pyc,,
transformers/models/clvp/__pycache__/processing_clvp.cpython-310.pyc,,
transformers/models/clvp/__pycache__/tokenization_clvp.cpython-310.pyc,,
transformers/models/clvp/configuration_clvp.py,sha256=PmxrzTpXR1efn7Od-JJh29W27Br_ixO6lbVBJSb8S3k,20933
transformers/models/clvp/convert_clvp_to_hf.py,sha256=1WYf_vwj1CeQ_VU9iMqu7Grr_MmlAsaKEK1Lojk6yM4,9326
transformers/models/clvp/feature_extraction_clvp.py,sha256=rq0Ygr1pCT1DK4mMzv6f4b06zgXeAwT29GYSzu1Fprw,10935
transformers/models/clvp/modeling_clvp.py,sha256=EIHR4ODlAeeDjf6xYM2Z9ItebnkUt4ujaIStdRFGQF8,91112
transformers/models/clvp/number_normalizer.py,sha256=lW1MjRY8PDAWjWLA-S2Fk-LVWaqkmBVCACmF2765Vps,8856
transformers/models/clvp/processing_clvp.py,sha256=ui2qQtxtTBoJ72SclUqGLH14w6RNDfeEnrM5b2J2w4Q,3604
transformers/models/clvp/tokenization_clvp.py,sha256=dNbrXIhYcqum_vonAZ7xsxvKimu1to6CdDDu5T5-0XA,14800
transformers/models/code_llama/__init__.py,sha256=S1xpVZ6cLZxN1ADmRNp7dCsoKQKnb3-Tw-HkHjHcnBY,1882
transformers/models/code_llama/__pycache__/__init__.cpython-310.pyc,,
transformers/models/code_llama/__pycache__/tokenization_code_llama.cpython-310.pyc,,
transformers/models/code_llama/__pycache__/tokenization_code_llama_fast.cpython-310.pyc,,
transformers/models/code_llama/tokenization_code_llama.py,sha256=l7Z4CEE0gVAvnq6q0m1QDDL-xQPw8lXRKs6KYeCwB50,22538
transformers/models/code_llama/tokenization_code_llama_fast.py,sha256=pgWy53PEdUiVNrBEQe-5bFsH_iVQSQh05DI2jj7vLQE,19337
transformers/models/codegen/__init__.py,sha256=sPF7DZrwSndm5qiuHISm2dE0WVS5TFJaFQwOYKTGzNE,2263
transformers/models/codegen/__pycache__/__init__.cpython-310.pyc,,
transformers/models/codegen/__pycache__/configuration_codegen.cpython-310.pyc,,
transformers/models/codegen/__pycache__/modeling_codegen.cpython-310.pyc,,
transformers/models/codegen/__pycache__/tokenization_codegen.cpython-310.pyc,,
transformers/models/codegen/__pycache__/tokenization_codegen_fast.cpython-310.pyc,,
transformers/models/codegen/configuration_codegen.py,sha256=wcAkmUIRzGOD_nQxaFHpItclyi1F5nwui1eF93rnoVw,9492
transformers/models/codegen/modeling_codegen.py,sha256=r0dHDRSHzZvb3eTpeoiOUiLccJf0DI82p5-mBMNXsO4,31591
transformers/models/codegen/tokenization_codegen.py,sha256=yxDlGUZCpKKb4SzTOwA4gcsQ79aMfHGT3_dTDZ0H-o4,16530
transformers/models/codegen/tokenization_codegen_fast.py,sha256=pBxZDJdp3XcW5Mpxq50bc0zFBVCz5ZW77ay3vaPBWeA,11432
transformers/models/cohere/__init__.py,sha256=Ge4jqRB26DsPZxH_-GvXnHatLWrd249yf73l0QRGjXc,2136
transformers/models/cohere/__pycache__/__init__.cpython-310.pyc,,
transformers/models/cohere/__pycache__/configuration_cohere.cpython-310.pyc,,
transformers/models/cohere/__pycache__/modeling_cohere.cpython-310.pyc,,
transformers/models/cohere/__pycache__/tokenization_cohere_fast.cpython-310.pyc,,
transformers/models/cohere/configuration_cohere.py,sha256=FZry6Yg0UtG-rtCxLLwDK_jBy3peJfGf7e4Ujh51SmQ,7318
transformers/models/cohere/modeling_cohere.py,sha256=EvzxGT7qneTmHt_llqB0gK9u2K3364BuvwjHQT7vMuo,50146
transformers/models/cohere/tokenization_cohere_fast.py,sha256=m5dA4j50jCP5pX59WpkZ8uqm3uDKMog1K_x9XJJffP4,41378
transformers/models/conditional_detr/__init__.py,sha256=E-BEmIVL_HIdLs7ZuFwAO1IUlVSfRpGeUDuTxWUz3Gk,2596
transformers/models/conditional_detr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/configuration_conditional_detr.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/convert_conditional_detr_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/feature_extraction_conditional_detr.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/image_processing_conditional_detr.cpython-310.pyc,,
transformers/models/conditional_detr/__pycache__/modeling_conditional_detr.cpython-310.pyc,,
transformers/models/conditional_detr/configuration_conditional_detr.py,sha256=tVlh17C6pBbT8bx5vWiD-k4j8DAscC2lDUsDb1a4GjA,13285
transformers/models/conditional_detr/convert_conditional_detr_original_pytorch_checkpoint_to_pytorch.py,sha256=pECO3PVooqZicWn5ycbfTg69C0oicbbSigv55fVCwIM,15929
transformers/models/conditional_detr/feature_extraction_conditional_detr.py,sha256=opHXZebd-6cMJnO6RbrAdmVYmnkNzK1up_fPlHTSLrk,1553
transformers/models/conditional_detr/image_processing_conditional_detr.py,sha256=P3xo8GgUI-AaPsIZIji6I611yI3lBPhSBYBlLQXZHng,85728
transformers/models/conditional_detr/modeling_conditional_detr.py,sha256=LxiJhyWEX-hoGujQtnB14MbOKdkPzc89b3dtPtna-hE,127372
transformers/models/convbert/__init__.py,sha256=o7dzZB2PPMospq0oIrNa52aP9Jztd2psXfTiCui6YGE,3777
transformers/models/convbert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/convbert/__pycache__/configuration_convbert.cpython-310.pyc,,
transformers/models/convbert/__pycache__/convert_convbert_original_tf1_checkpoint_to_pytorch_and_tf2.cpython-310.pyc,,
transformers/models/convbert/__pycache__/modeling_convbert.cpython-310.pyc,,
transformers/models/convbert/__pycache__/modeling_tf_convbert.cpython-310.pyc,,
transformers/models/convbert/__pycache__/tokenization_convbert.cpython-310.pyc,,
transformers/models/convbert/__pycache__/tokenization_convbert_fast.cpython-310.pyc,,
transformers/models/convbert/configuration_convbert.py,sha256=afd7mH7LyoTnSVZXGIqFOdJj_zpBrFQiJ9fa5LWs5FU,6833
transformers/models/convbert/convert_convbert_original_tf1_checkpoint_to_pytorch_and_tf2.py,sha256=vTZyGhG9v7o4rDuP9-xM26gX1EzlCda7Sn_ELT9n3Gk,2108
transformers/models/convbert/modeling_convbert.py,sha256=ImBND8ccVRdlLi8drUFMipG1q16TiNGI6_-D5AD5WA8,58322
transformers/models/convbert/modeling_tf_convbert.py,sha256=EAsYjUQYSMAVR4-iidy1zpiwgyibbDF8YQSdMZ1lDXM,61363
transformers/models/convbert/tokenization_convbert.py,sha256=AnpBBYWxYmECDHfYkW-rbVcF2aUpvdLH2TdpTWo8w-g,20971
transformers/models/convbert/tokenization_convbert_fast.py,sha256=uUvyyeooIaO3AjG1sPC4v_CHt4XW2C7V-76hjNyv1xg,7781
transformers/models/convnext/__init__.py,sha256=m3Fbl4B0HNR-AUeLRDuiJOEFyz8yqQjIXXHwfP9qDcc,2960
transformers/models/convnext/__pycache__/__init__.cpython-310.pyc,,
transformers/models/convnext/__pycache__/configuration_convnext.cpython-310.pyc,,
transformers/models/convnext/__pycache__/convert_convnext_to_pytorch.cpython-310.pyc,,
transformers/models/convnext/__pycache__/feature_extraction_convnext.cpython-310.pyc,,
transformers/models/convnext/__pycache__/image_processing_convnext.cpython-310.pyc,,
transformers/models/convnext/__pycache__/modeling_convnext.cpython-310.pyc,,
transformers/models/convnext/__pycache__/modeling_tf_convnext.cpython-310.pyc,,
transformers/models/convnext/configuration_convnext.py,sha256=tAui8-VEgoL1KcEYR299QC_EjkWjIDzzPmd7PIGXCKE,6126
transformers/models/convnext/convert_convnext_to_pytorch.py,sha256=h8WJeh02GFWMYkq-9MdxyiBbsmWQJFSMhNzCHrxUI8o,10219
transformers/models/convnext/feature_extraction_convnext.py,sha256=TyFMochXYlN3vKH7Ud0nXagzxGhio2Bfma4ofceR_zA,1200
transformers/models/convnext/image_processing_convnext.py,sha256=JIXegI7ZMZUxJApMMoY4JTmA5iNxJm9FN3UQnQwRpNc,16288
transformers/models/convnext/modeling_convnext.py,sha256=R5X7-cOkZSWUd1Lhjoa39VMCsay-X1AVsjJpl9YQTfU,21823
transformers/models/convnext/modeling_tf_convnext.py,sha256=oUrL6zNxFR-4xnbKJ8yQ9VyQ1BM8uimkYIGC849A3VQ,27193
transformers/models/convnextv2/__init__.py,sha256=5pJ19hB5LGhgDHzdvKpVLCveyfAx8pIMvMGxntaxcZg,2596
transformers/models/convnextv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/convnextv2/__pycache__/configuration_convnextv2.cpython-310.pyc,,
transformers/models/convnextv2/__pycache__/convert_convnextv2_to_pytorch.cpython-310.pyc,,
transformers/models/convnextv2/__pycache__/modeling_convnextv2.cpython-310.pyc,,
transformers/models/convnextv2/__pycache__/modeling_tf_convnextv2.cpython-310.pyc,,
transformers/models/convnextv2/configuration_convnextv2.py,sha256=lqKGN-xfim3qJtC3IGIvxks1V6ATpQlZF0LmxTUezmA,5421
transformers/models/convnextv2/convert_convnextv2_to_pytorch.py,sha256=Yswl5UwLP0t0tC8O2b8wix2beNaMtPy7areKFCuEccg,12473
transformers/models/convnextv2/modeling_convnextv2.py,sha256=mo-BUNy1CkwQPUT1k9NmxYk7D9QI7uTS0684ZHQ1IqY,23595
transformers/models/convnextv2/modeling_tf_convnextv2.py,sha256=dtP27dyVMSzhVob-wcayYD6T3vOzepsKrjdWbpE1v2g,27593
transformers/models/cpm/__init__.py,sha256=9SmT0nL5DgGjXxmPaQFi9GGPXWuhFic2DX2GsF-BynQ,1816
transformers/models/cpm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/cpm/__pycache__/tokenization_cpm.cpython-310.pyc,,
transformers/models/cpm/__pycache__/tokenization_cpm_fast.cpython-310.pyc,,
transformers/models/cpm/tokenization_cpm.py,sha256=n3oSrIyZqxMmKxHvcunWyCdePRXj4Jf3Ne5_ZghwRp8,15027
transformers/models/cpm/tokenization_cpm_fast.py,sha256=kPBVfiQelkwPOIqffQfJhh-iJlQIky2HFjF1tjRcUQc,10426
transformers/models/cpmant/__init__.py,sha256=3_P7eR-tIaj1masTqk1CFS7s9o3e8ZP3c2DWxx6MO58,1941
transformers/models/cpmant/__pycache__/__init__.cpython-310.pyc,,
transformers/models/cpmant/__pycache__/configuration_cpmant.cpython-310.pyc,,
transformers/models/cpmant/__pycache__/modeling_cpmant.cpython-310.pyc,,
transformers/models/cpmant/__pycache__/tokenization_cpmant.cpython-310.pyc,,
transformers/models/cpmant/configuration_cpmant.py,sha256=dsHPnES62-rb9ULOwA12K3VYku_HdCx5EaY5mXBj4LQ,5116
transformers/models/cpmant/modeling_cpmant.py,sha256=W4KpLoyao_DmMnh2x37VJ8x16duAzc8jpLPE5O5_zMY,37412
transformers/models/cpmant/tokenization_cpmant.py,sha256=rf4pXV7ntgxCg9hrTaDNv6BKgRyKBDVckXjYmMQeR_E,9712
transformers/models/ctrl/__init__.py,sha256=s588iXEw4SSQ_tqqySa9FY8XsDN8T2Xz4b1SN-CO7t4,2420
transformers/models/ctrl/__pycache__/__init__.cpython-310.pyc,,
transformers/models/ctrl/__pycache__/configuration_ctrl.cpython-310.pyc,,
transformers/models/ctrl/__pycache__/modeling_ctrl.cpython-310.pyc,,
transformers/models/ctrl/__pycache__/modeling_tf_ctrl.cpython-310.pyc,,
transformers/models/ctrl/__pycache__/tokenization_ctrl.cpython-310.pyc,,
transformers/models/ctrl/configuration_ctrl.py,sha256=yB5HxF8EKPMwxPneu7s8eTLdrBw_KviGNec0FB9GbwY,4657
transformers/models/ctrl/modeling_ctrl.py,sha256=Pm5yMipPnggxN5IR6yZz4csPVolHM5hdBiNlV1FJ_3E,35605
transformers/models/ctrl/modeling_tf_ctrl.py,sha256=cB5t6WgGFG1kkN7xWOFCDCfonge2qV37CJM_L76RxiY,39635
transformers/models/ctrl/tokenization_ctrl.py,sha256=xc_a4G1ejLqOnAdIajIL4XVbAjNgRxGFA2nnsbtYSrU,8057
transformers/models/cvt/__init__.py,sha256=y-aTzOJFVO4xGssvMp0glSPOABTl0d1p7nQLpB0ux3Q,2172
transformers/models/cvt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/cvt/__pycache__/configuration_cvt.cpython-310.pyc,,
transformers/models/cvt/__pycache__/convert_cvt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/cvt/__pycache__/modeling_cvt.cpython-310.pyc,,
transformers/models/cvt/__pycache__/modeling_tf_cvt.cpython-310.pyc,,
transformers/models/cvt/configuration_cvt.py,sha256=1hs8UwBMA1sT7tgj5ffgYd3iS4V5zNX2lqICMA8nQ4U,6658
transformers/models/cvt/convert_cvt_original_pytorch_checkpoint_to_pytorch.py,sha256=zoed0S0LFkqKv3Or-8O512mjeVBo4dZ7bgnOCaqOU4E,13578
transformers/models/cvt/modeling_cvt.py,sha256=8mHd9bUWbZYNTXgCJ4RB7zZQExSeIsnstyCfiYn-wlA,28704
transformers/models/cvt/modeling_tf_cvt.py,sha256=WhdxLoIK_kOsV_l5lBLEfDS78FOJMRAUJsYdw4Ja4H8,43463
transformers/models/data2vec/__init__.py,sha256=kWv5gOdSpA7i3lUunb4f2A41Svy_PEzLsQOshWE0srs,4277
transformers/models/data2vec/__pycache__/__init__.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_audio.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_text.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/configuration_data2vec_vision.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_audio_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_text_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/convert_data2vec_vision_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_audio.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_text.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/modeling_data2vec_vision.cpython-310.pyc,,
transformers/models/data2vec/__pycache__/modeling_tf_data2vec_vision.cpython-310.pyc,,
transformers/models/data2vec/configuration_data2vec_audio.py,sha256=yTleiPDKyeN-0rL5ZqhYidr8ndaKuRCReSajgksSScI,16321
transformers/models/data2vec/configuration_data2vec_text.py,sha256=j2P1eGFb65GRlu0DkcS_57PjPnZMltntQ803pZk6JJ4,7275
transformers/models/data2vec/configuration_data2vec_vision.py,sha256=UXIX55iv3jJY617KZCKdRhXKqvzL0c5zIL7DXI6ZoM0,9240
transformers/models/data2vec/convert_data2vec_audio_original_pytorch_checkpoint_to_pytorch.py,sha256=dvcTq8C9Zl4axc0gYcqYaTWTqUxgwve1O7xhXMeWu8c,10881
transformers/models/data2vec/convert_data2vec_text_original_pytorch_checkpoint_to_pytorch.py,sha256=eryzP47_SwQ2keZGhuTodpoNS4WtFVU34XoG8dBafSw,9579
transformers/models/data2vec/convert_data2vec_vision_original_pytorch_checkpoint_to_pytorch.py,sha256=qKjV-jqIgL-6i17m4yQLW_93SbPpGxQnvHjuy1xVxQU,15340
transformers/models/data2vec/modeling_data2vec_audio.py,sha256=f1nmj-AQjHGOM8oI5zZruk-9ewpMBD56s6k_l_mavsc,78707
transformers/models/data2vec/modeling_data2vec_text.py,sha256=FkEOYtCMenjLhudUEZxqo8razKX8-rUYsy8Y-uZAd08,71344
transformers/models/data2vec/modeling_data2vec_vision.py,sha256=asFP247s49TcBRNiqWCj_IWHF3Bik3xC_xVI0QRJlw4,60241
transformers/models/data2vec/modeling_tf_data2vec_vision.py,sha256=_axJdTtCFsStQYMNqul2vZzhMPW74OQejWzX5U6r_xg,73348
transformers/models/dbrx/__init__.py,sha256=n36C-BWFjJ9wkgBAv764sGksJFOL_fkME1fe1cTm-sg,1513
transformers/models/dbrx/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dbrx/__pycache__/configuration_dbrx.cpython-310.pyc,,
transformers/models/dbrx/__pycache__/modeling_dbrx.cpython-310.pyc,,
transformers/models/dbrx/configuration_dbrx.py,sha256=S-rhMKK507WJ968t9YtQLUs6r7Z0L9G6Tpl26IY38jU,11052
transformers/models/dbrx/modeling_dbrx.py,sha256=fLf08gQQBqG3iZIyIWKteb18M7qjN-POAcwryMT6S_E,62468
transformers/models/deberta/__init__.py,sha256=6ewAfJ8yBFyXvqBfBp19QDEs3fTgGwp0m-cUHZOVWd8,3391
transformers/models/deberta/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deberta/__pycache__/configuration_deberta.cpython-310.pyc,,
transformers/models/deberta/__pycache__/modeling_deberta.cpython-310.pyc,,
transformers/models/deberta/__pycache__/modeling_tf_deberta.cpython-310.pyc,,
transformers/models/deberta/__pycache__/tokenization_deberta.cpython-310.pyc,,
transformers/models/deberta/__pycache__/tokenization_deberta_fast.cpython-310.pyc,,
transformers/models/deberta/configuration_deberta.py,sha256=Zu9LnXUysvG4n9mrOQN6AUzDYPFNaBeoSKKkujfvkm4,8683
transformers/models/deberta/modeling_deberta.py,sha256=V_x0Ec8qaBfgU4qbbxlpscJNvAGKSWAL36a-l1ial7s,57941
transformers/models/deberta/modeling_tf_deberta.py,sha256=Nc86IbrsA5oxVrxcTc9KV54NW3K1fBUAaYT_elrSC5o,68831
transformers/models/deberta/tokenization_deberta.py,sha256=9bOtmoaHBPSpIlHd8fw-fY4WPYtqv6aRa90wS1xTdQw,17051
transformers/models/deberta/tokenization_deberta_fast.py,sha256=_iy08DJFzQsrrET1RJKAp6lGl2KxZFpQ7BLE4mXmaGI,10721
transformers/models/deberta_v2/__init__.py,sha256=IsGFqPGb_7oEEDNCQsO_IBCTa2tJl1OjKWojytY-XHw,3669
transformers/models/deberta_v2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/configuration_deberta_v2.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/modeling_deberta_v2.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/modeling_tf_deberta_v2.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/tokenization_deberta_v2.cpython-310.pyc,,
transformers/models/deberta_v2/__pycache__/tokenization_deberta_v2_fast.cpython-310.pyc,,
transformers/models/deberta_v2/configuration_deberta_v2.py,sha256=-oDDoVepIQKb9Dijp3dCfrSFN5XxtzNy3rY0ds31tVs,8619
transformers/models/deberta_v2/modeling_deberta_v2.py,sha256=ENlx0kk2a6s9DhSIRjzS1UjVycImjFCUMKcQq8lfspc,67513
transformers/models/deberta_v2/modeling_tf_deberta_v2.py,sha256=HyttnhhaBcPnUHdQLesM95yYRGV-PBIkaoZpWy9eV6M,81128
transformers/models/deberta_v2/tokenization_deberta_v2.py,sha256=aqeowwNFhBcI9sKcCCaFeBFmNkZ7LTxqkYHHD1g8KBE,20702
transformers/models/deberta_v2/tokenization_deberta_v2_fast.py,sha256=qwgQDjV0k_3d40RgzlUiIDBqwRO1v_dbqwtcf2WiZ-M,9758
transformers/models/decision_transformer/__init__.py,sha256=9MNMqQI1zNwuXzhI8TXALNHhWUuY2YOdoOx72l3io8M,1861
transformers/models/decision_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/decision_transformer/__pycache__/configuration_decision_transformer.cpython-310.pyc,,
transformers/models/decision_transformer/__pycache__/modeling_decision_transformer.cpython-310.pyc,,
transformers/models/decision_transformer/configuration_decision_transformer.py,sha256=lLxwvAYUIMm4Ql3sxL4RMlBFzbtd02kxAvGuqNOzP_8,6986
transformers/models/decision_transformer/modeling_decision_transformer.py,sha256=LVE1vz3D1D86k_haThUqVQWAU3Om-4_40eLTtTeOUjk,42929
transformers/models/deformable_detr/__init__.py,sha256=m4YrIA0yACKjAqP0j5ngC4JwCqzofZ8kP2CdwulB6zo,2387
transformers/models/deformable_detr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/configuration_deformable_detr.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/convert_deformable_detr_to_pytorch.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/feature_extraction_deformable_detr.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/image_processing_deformable_detr.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/load_custom.cpython-310.pyc,,
transformers/models/deformable_detr/__pycache__/modeling_deformable_detr.cpython-310.pyc,,
transformers/models/deformable_detr/configuration_deformable_detr.py,sha256=yBHZe0RsUZgspWv71znrkwJK3nceBYzga7HzYzNLlN0,14534
transformers/models/deformable_detr/convert_deformable_detr_to_pytorch.py,sha256=ytsMFj4rqS-8fxDL9wojqFN-PscjWJ79rVcVQQk_1s8,9460
transformers/models/deformable_detr/feature_extraction_deformable_detr.py,sha256=GwYaT6B6-Fu2Jbl8CALodb7Lz4gr9jSRfq01QfLQc7Y,1546
transformers/models/deformable_detr/image_processing_deformable_detr.py,sha256=Gr3QdyYRIr6nHX7I9Ou18EiNXuuRwEtYMJcUkWqS0fg,73187
transformers/models/deformable_detr/load_custom.py,sha256=GvDeH883HST8-vH5Xl5jcR9VS_e0GSzbDoImSLug9rA,1559
transformers/models/deformable_detr/modeling_deformable_detr.py,sha256=vur2r2cQDqdKlEybrgO601cuSHbgNCXDa3f_ofUkrnY,121189
transformers/models/deit/__init__.py,sha256=vru6_KBdFt9--qknQVxBMEccT9ICPaNXD9_YW38FvZA,3218
transformers/models/deit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deit/__pycache__/configuration_deit.cpython-310.pyc,,
transformers/models/deit/__pycache__/convert_deit_timm_to_pytorch.cpython-310.pyc,,
transformers/models/deit/__pycache__/feature_extraction_deit.cpython-310.pyc,,
transformers/models/deit/__pycache__/image_processing_deit.cpython-310.pyc,,
transformers/models/deit/__pycache__/modeling_deit.cpython-310.pyc,,
transformers/models/deit/__pycache__/modeling_tf_deit.cpython-310.pyc,,
transformers/models/deit/configuration_deit.py,sha256=9LopM7dLKd7jiKVojcybRE5KdYXHxbAZbYaGmjHwr2g,5695
transformers/models/deit/convert_deit_timm_to_pytorch.py,sha256=7wWjhmCpS972rYukIRZsfhVfklvbdl8nclFqiRwb82Y,9216
transformers/models/deit/feature_extraction_deit.py,sha256=1j_aV0oAZUofSYJGCEFRo0WNd_zVEXjj3SFlTQSuV1E,1172
transformers/models/deit/image_processing_deit.py,sha256=VgMa1Wp87jIbbkcfqNUj_61sapJtXzOFZ0vFIbKpcdA,15720
transformers/models/deit/modeling_deit.py,sha256=CZPXDTeZyWZUHGjcuE8EQ4YY8uS7rGfwMttN7eCwNDQ,42339
transformers/models/deit/modeling_tf_deit.py,sha256=X_VeI4xWzh4UMCQWlZWfqhmdkPsxDJ1MPFsfQyOzfCc,51569
transformers/models/deprecated/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/bort/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/deprecated/bort/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/bort/__pycache__/convert_bort_original_gluonnlp_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/bort/convert_bort_original_gluonnlp_checkpoint_to_pytorch.py,sha256=9aY4nB-A4-WeVVH6CzE9kxoHr9uqL5f5NW2_OirzWxo,14067
transformers/models/deprecated/deta/__init__.py,sha256=sRdhN6pSfT1G8VY04s6jNnZBKgyZrB4DsrBsAPs8Rw8,2038
transformers/models/deprecated/deta/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/deta/__pycache__/configuration_deta.cpython-310.pyc,,
transformers/models/deprecated/deta/__pycache__/convert_deta_resnet_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/deta/__pycache__/convert_deta_swin_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/deta/__pycache__/image_processing_deta.cpython-310.pyc,,
transformers/models/deprecated/deta/__pycache__/modeling_deta.cpython-310.pyc,,
transformers/models/deprecated/deta/configuration_deta.py,sha256=GTfTPOaP2JzdNKQa9zg3CQ52QPuQOkoxU9VLc-uZt2s,13948
transformers/models/deprecated/deta/convert_deta_resnet_to_pytorch.py,sha256=Eu3xpsuFwp-vkyOPHhAjWYIlN1zvm9TirpGMTq5GzGw,16799
transformers/models/deprecated/deta/convert_deta_swin_to_pytorch.py,sha256=RWx7DMt5PMBoOhQM3poOILmIAJPHEWhH9mY7DeZTVDg,18997
transformers/models/deprecated/deta/image_processing_deta.py,sha256=3KSMJGPTYhQeZDFDo8q1jD39OWyUppgpmvXbnx7QcBo,54891
transformers/models/deprecated/deta/modeling_deta.py,sha256=BgpuMsL7MekxwXlJGTk8SpVd_z2-3gP-ZxKEX6XeSt4,135671
transformers/models/deprecated/efficientformer/__init__.py,sha256=u4KA4byDgoRkQ9uuNGgkA2PtpIh0BpZVTObA0Vgil-E,3188
transformers/models/deprecated/efficientformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/configuration_efficientformer.cpython-310.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/convert_efficientformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/image_processing_efficientformer.cpython-310.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/modeling_efficientformer.cpython-310.pyc,,
transformers/models/deprecated/efficientformer/__pycache__/modeling_tf_efficientformer.cpython-310.pyc,,
transformers/models/deprecated/efficientformer/configuration_efficientformer.py,sha256=QFiBTmFQU6P8VllghZ5jQpR1Dthnm9uylTgf7z3uHMc,7719
transformers/models/deprecated/efficientformer/convert_efficientformer_original_pytorch_checkpoint_to_pytorch.py,sha256=1ni0wyhRjTbF8U4BZ_FXU-_9Jzy43HMLKI3vGlyPjFc,9381
transformers/models/deprecated/efficientformer/image_processing_efficientformer.py,sha256=a9tDStbg9089nTXxCU3pjWBG6HB6Jbw2snlQzVWdDuY,15698
transformers/models/deprecated/efficientformer/modeling_efficientformer.py,sha256=029SXmbp69FUn13m9OdoTHivIAe-x-RkFUtk62yYLWk,33580
transformers/models/deprecated/efficientformer/modeling_tf_efficientformer.py,sha256=tRs9Ljxf8bf1B-6MwpUPuUinzWik24kzQH50Ke58cjw,49194
transformers/models/deprecated/ernie_m/__init__.py,sha256=V6C21iE8AKYSpDNW4Ffn3IGiKp69T4ro2LFcRXc0mq4,2458
transformers/models/deprecated/ernie_m/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/ernie_m/__pycache__/configuration_ernie_m.cpython-310.pyc,,
transformers/models/deprecated/ernie_m/__pycache__/modeling_ernie_m.cpython-310.pyc,,
transformers/models/deprecated/ernie_m/__pycache__/tokenization_ernie_m.cpython-310.pyc,,
transformers/models/deprecated/ernie_m/configuration_ernie_m.py,sha256=bGRUXTL8NdJEevZNBmDBh_aB_RwRNL8G1rfdNYMW69s,5885
transformers/models/deprecated/ernie_m/modeling_ernie_m.py,sha256=flc9_1HuMrChGoHcx4au3IlEbt9VaWDYY29yaSmG3Dc,47028
transformers/models/deprecated/ernie_m/tokenization_ernie_m.py,sha256=oGKdPntR5sjU3XrxbaRNySX76bQaSLlFWNTgLJfmXBI,16169
transformers/models/deprecated/gptsan_japanese/__init__.py,sha256=8a1T_PBkN2MKzJDSTVJan5kSknwon3cRtUicoKVt2SY,2083
transformers/models/deprecated/gptsan_japanese/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/gptsan_japanese/__pycache__/configuration_gptsan_japanese.cpython-310.pyc,,
transformers/models/deprecated/gptsan_japanese/__pycache__/convert_gptsan_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/gptsan_japanese/__pycache__/modeling_gptsan_japanese.cpython-310.pyc,,
transformers/models/deprecated/gptsan_japanese/__pycache__/tokenization_gptsan_japanese.cpython-310.pyc,,
transformers/models/deprecated/gptsan_japanese/configuration_gptsan_japanese.py,sha256=T8buHMjH3XFnx7BXXis6M5aTvWLwwnleTf-YDyySwNM,7124
transformers/models/deprecated/gptsan_japanese/convert_gptsan_tf_checkpoint_to_pytorch.py,sha256=syF4TCbLQByZhm5VqIFgXfzQ4zImmCua8UNjCYJP5t8,9793
transformers/models/deprecated/gptsan_japanese/modeling_gptsan_japanese.py,sha256=JbBnTAclyfHQHkWyRMfEVxlmK8I5IemxcPidnSMRyc8,64953
transformers/models/deprecated/gptsan_japanese/tokenization_gptsan_japanese.py,sha256=f9bb-o42da51NQ4F4M5VAHBO6R_irJwaauGcrBBcNXs,23065
transformers/models/deprecated/graphormer/__init__.py,sha256=ltRElMWou0jRd50T50NHoJoSUbvM5IrcT41EcFQ7mV0,1682
transformers/models/deprecated/graphormer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/graphormer/__pycache__/collating_graphormer.cpython-310.pyc,,
transformers/models/deprecated/graphormer/__pycache__/configuration_graphormer.cpython-310.pyc,,
transformers/models/deprecated/graphormer/__pycache__/modeling_graphormer.cpython-310.pyc,,
transformers/models/deprecated/graphormer/algos_graphormer.pyx,sha256=b_Qlm1hKCHnAqx6oOLGC9LkivAV0K_AZRGgXT9MmBas,3635
transformers/models/deprecated/graphormer/collating_graphormer.py,sha256=KRew-2p9_7heLTflAYA6dObor_Hxy47yIP8HFEgaj1U,6087
transformers/models/deprecated/graphormer/configuration_graphormer.py,sha256=ZzNCBEZj_G1S1lg3MouwutiSeO9G47yFob14WGXXN9g,10380
transformers/models/deprecated/graphormer/modeling_graphormer.py,sha256=Y3aYbgX5vIYB7FfM8jRkv2xZRLdoHZuS5aBtesLXqX8,37006
transformers/models/deprecated/jukebox/__init__.py,sha256=96yLuu-yOBcAHaz1zhvc4RWwIvqkjycikMa-GXFcWm8,1889
transformers/models/deprecated/jukebox/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/jukebox/__pycache__/configuration_jukebox.cpython-310.pyc,,
transformers/models/deprecated/jukebox/__pycache__/convert_jukebox.cpython-310.pyc,,
transformers/models/deprecated/jukebox/__pycache__/modeling_jukebox.cpython-310.pyc,,
transformers/models/deprecated/jukebox/__pycache__/tokenization_jukebox.cpython-310.pyc,,
transformers/models/deprecated/jukebox/configuration_jukebox.py,sha256=-gLq4uKdqdjCWuV9ZbChsUiFGEI0a58st5oapPTixGI,26749
transformers/models/deprecated/jukebox/convert_jukebox.py,sha256=RBgOPbwIMv_42mUFJYxRv4IAGZn4cAzjTqjrMI7HtVg,11789
transformers/models/deprecated/jukebox/modeling_jukebox.py,sha256=O0xBJi3UyMF8Aj0TyXYbN_2wtouHjy9pIdMbUUdkiZQ,119471
transformers/models/deprecated/jukebox/tokenization_jukebox.py,sha256=r1YcKG2OkPWAKdriQ2BXgX-MBsQHbeyccoc5aKLCpac,17352
transformers/models/deprecated/mctct/__init__.py,sha256=aaM-CVsMyEUWqGHH5xAgnqUu6B5D730X_lTo7CMpo7o,1732
transformers/models/deprecated/mctct/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/mctct/__pycache__/configuration_mctct.cpython-310.pyc,,
transformers/models/deprecated/mctct/__pycache__/feature_extraction_mctct.cpython-310.pyc,,
transformers/models/deprecated/mctct/__pycache__/modeling_mctct.cpython-310.pyc,,
transformers/models/deprecated/mctct/__pycache__/processing_mctct.cpython-310.pyc,,
transformers/models/deprecated/mctct/configuration_mctct.py,sha256=OmrxkatPuycQORmuIQWznAHsi20nF9CM-HHtHWyh1gM,9073
transformers/models/deprecated/mctct/feature_extraction_mctct.py,sha256=JsaSE20NeqBX8Uw-07Y5HdUcQtbYZqCrTN18Wu2B4rI,13460
transformers/models/deprecated/mctct/modeling_mctct.py,sha256=gpVWxEIPAixIYX7qXiecGfPb2St0hYjzzim4JLJrN6Q,32807
transformers/models/deprecated/mctct/processing_mctct.py,sha256=EkokdjeJPgzsSxriPNmAthZ6WgO_iQyFMpQKXDeS7Uo,5931
transformers/models/deprecated/mega/__init__.py,sha256=i_9dHqDl6RZJ1zebhj8pn3zPlgxRqEynwsM_mj9eWMs,1973
transformers/models/deprecated/mega/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/mega/__pycache__/configuration_mega.cpython-310.pyc,,
transformers/models/deprecated/mega/__pycache__/convert_mega_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/mega/__pycache__/modeling_mega.cpython-310.pyc,,
transformers/models/deprecated/mega/configuration_mega.py,sha256=0m3Fsv9KqcZECi7Dbgjdz7nidKqf8MQbdfMsYMlMF_4,12588
transformers/models/deprecated/mega/convert_mega_original_pytorch_checkpoint_to_pytorch.py,sha256=RqYrXvQNCa-mSlF9L0ayNvdrdaAayIsEIXpJ_j8c7FE,13155
transformers/models/deprecated/mega/modeling_mega.py,sha256=pwTQTskg780rpkgvOcDxE4RIjYAYbb_iFm2hrWu_AxM,109416
transformers/models/deprecated/mmbt/__init__.py,sha256=0CCmesCwGIMNFlf2oDsL0gYaCSpsfAC1_bMOXRcAgF4,1480
transformers/models/deprecated/mmbt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/mmbt/__pycache__/configuration_mmbt.cpython-310.pyc,,
transformers/models/deprecated/mmbt/__pycache__/modeling_mmbt.cpython-310.pyc,,
transformers/models/deprecated/mmbt/configuration_mmbt.py,sha256=cqE1keuKawo4zBNuTh4H0Z8gsoDsVhsqhB3xWTjxgQ8,1605
transformers/models/deprecated/mmbt/modeling_mmbt.py,sha256=ms_fa8G6Ww3kyk7jqLeAdba6k2E6VMBq82zMz5GvFKQ,18913
transformers/models/deprecated/nat/__init__.py,sha256=1KgeUYAs8Ypq1rZgA1tS_cq0GNjTINvjycdQR-m0P7s,1613
transformers/models/deprecated/nat/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/nat/__pycache__/configuration_nat.cpython-310.pyc,,
transformers/models/deprecated/nat/__pycache__/modeling_nat.cpython-310.pyc,,
transformers/models/deprecated/nat/configuration_nat.py,sha256=o-nifDP9IvftvMzVeoXGGUycwUJ-wox1K6QVd4kpaik,6975
transformers/models/deprecated/nat/modeling_nat.py,sha256=G2ggfJ_RJYBIIAbjvJu2DaAdgEzf74chqVvaNenvcSQ,39728
transformers/models/deprecated/nezha/__init__.py,sha256=p4YuR6FmvGSeCniAaJaTWtL_9kqzMfnGeKAYskaWyeM,2062
transformers/models/deprecated/nezha/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/nezha/__pycache__/configuration_nezha.cpython-310.pyc,,
transformers/models/deprecated/nezha/__pycache__/modeling_nezha.cpython-310.pyc,,
transformers/models/deprecated/nezha/configuration_nezha.py,sha256=hfpG7tYqEHfddMFZ4Ni6h0DRAuG6UwNEsmlxmK562ew,4817
transformers/models/deprecated/nezha/modeling_nezha.py,sha256=37Yuhy26qkonrj7djUw9tgJTaQp5yF1fVx8ctByXSo4,73924
transformers/models/deprecated/open_llama/__init__.py,sha256=KJ11JLm0-ytx2jZjEVggJMC-BxjklPJqLVF2Fm1Okjw,2702
transformers/models/deprecated/open_llama/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/open_llama/__pycache__/configuration_open_llama.cpython-310.pyc,,
transformers/models/deprecated/open_llama/__pycache__/modeling_open_llama.cpython-310.pyc,,
transformers/models/deprecated/open_llama/configuration_open_llama.py,sha256=5O8r3FXbzYE4gHGqnhYj7LtSYVujAXKR4ZKkAsTGKLM,7771
transformers/models/deprecated/open_llama/modeling_open_llama.py,sha256=NjVSfK6VevxwYUZHFoY-9cet0V5ZNLwIo7ZiZGT3M3c,43279
transformers/models/deprecated/qdqbert/__init__.py,sha256=xDttpfygkbkttNuKo3pW6e-Z0_MwTbj5uICeiXWgppw,2223
transformers/models/deprecated/qdqbert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/qdqbert/__pycache__/configuration_qdqbert.cpython-310.pyc,,
transformers/models/deprecated/qdqbert/__pycache__/modeling_qdqbert.cpython-310.pyc,,
transformers/models/deprecated/qdqbert/configuration_qdqbert.py,sha256=qYx_V85qk4g_o3L-OxiLObqndD834k0j0bDjQUNcfT8,5689
transformers/models/deprecated/qdqbert/modeling_qdqbert.py,sha256=XjGtF8iB8I6lfvL0PQK6cVWRbY2-76Lp_RLi8F-y7Z8,77002
transformers/models/deprecated/realm/__init__.py,sha256=_xkblqSgmTTryPK_c0N_ugcMVYc871UxI2hOskvo_pw,2504
transformers/models/deprecated/realm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/realm/__pycache__/configuration_realm.cpython-310.pyc,,
transformers/models/deprecated/realm/__pycache__/modeling_realm.cpython-310.pyc,,
transformers/models/deprecated/realm/__pycache__/retrieval_realm.cpython-310.pyc,,
transformers/models/deprecated/realm/__pycache__/tokenization_realm.cpython-310.pyc,,
transformers/models/deprecated/realm/__pycache__/tokenization_realm_fast.cpython-310.pyc,,
transformers/models/deprecated/realm/configuration_realm.py,sha256=kUxwVQ0A99hr2wEFALWfvgoDJKp0OpxGjls42Q-yVZU,7557
transformers/models/deprecated/realm/modeling_realm.py,sha256=NtHppLE8iK6rMwGjqeFRRMmGFLkm-Kc-54h1ijrvuGk,83476
transformers/models/deprecated/realm/retrieval_realm.py,sha256=cebNTe43Mb5VN1xUzR13ewbvkGnlZ5nlJjGSj0ewoWc,6372
transformers/models/deprecated/realm/tokenization_realm.py,sha256=wdUhDxPztEBEwKNjnKz97F9yb4RJT2k_7TyTo-_ZhMc,23130
transformers/models/deprecated/realm/tokenization_realm_fast.py,sha256=eZ76_VPhjsqdEEHRr0_4Yt3HK25y0AfOlDohgPf7790,10953
transformers/models/deprecated/retribert/__init__.py,sha256=I5aMwp2FJw4zoL69qlsk2krQjpJMYIh436X47YxUMu8,2163
transformers/models/deprecated/retribert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/retribert/__pycache__/configuration_retribert.cpython-310.pyc,,
transformers/models/deprecated/retribert/__pycache__/modeling_retribert.cpython-310.pyc,,
transformers/models/deprecated/retribert/__pycache__/tokenization_retribert.cpython-310.pyc,,
transformers/models/deprecated/retribert/__pycache__/tokenization_retribert_fast.cpython-310.pyc,,
transformers/models/deprecated/retribert/configuration_retribert.py,sha256=7liSa4MonQVeLEz2VlxuapZglk6Z_CzyG5i8Nxi2MTM,5200
transformers/models/deprecated/retribert/modeling_retribert.py,sha256=tF4Sd2lY3_h0-DNqWQoiFXKxxbjfTqqsxPtJnKLEnd0,9297
transformers/models/deprecated/retribert/tokenization_retribert.py,sha256=PGd_R5LBlFIfKPcJF2Rh-69cpn81XWfGxCgwCoeS7dI,20666
transformers/models/deprecated/retribert/tokenization_retribert_fast.py,sha256=hIkbxCjKbkfblfYAyEE6VOf-l7aEmgXO3myRQ917gho,7820
transformers/models/deprecated/speech_to_text_2/__init__.py,sha256=FrO5Wtn6Uznx5DzVHyfLpdo7iDkeBpFgXC4bHXXgxxo,1951
transformers/models/deprecated/speech_to_text_2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/speech_to_text_2/__pycache__/configuration_speech_to_text_2.cpython-310.pyc,,
transformers/models/deprecated/speech_to_text_2/__pycache__/modeling_speech_to_text_2.cpython-310.pyc,,
transformers/models/deprecated/speech_to_text_2/__pycache__/processing_speech_to_text_2.cpython-310.pyc,,
transformers/models/deprecated/speech_to_text_2/__pycache__/tokenization_speech_to_text_2.cpython-310.pyc,,
transformers/models/deprecated/speech_to_text_2/configuration_speech_to_text_2.py,sha256=HpJVunbFUp23kPZBCr9K5sIXKVp238icRyv8_YGcmCI,6001
transformers/models/deprecated/speech_to_text_2/modeling_speech_to_text_2.py,sha256=asJ2UlO6N-HsLjO1eg6rFCWtf2UbH-5NgB7lsPGB0u4,43880
transformers/models/deprecated/speech_to_text_2/processing_speech_to_text_2.py,sha256=7AWU3_OegyHwNxluEMSHjLzBGfYcg3m-TNHq9VHYJTo,4792
transformers/models/deprecated/speech_to_text_2/tokenization_speech_to_text_2.py,sha256=S7biDmProh43S6iAbA0cIJyCVqb6fG6itYkbsI2Ccfc,8405
transformers/models/deprecated/tapex/__init__.py,sha256=lQutKYtwbU8ztPva0tyRnnV-zOWw6rxkGyoOUSuvnUo,926
transformers/models/deprecated/tapex/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/tapex/__pycache__/tokenization_tapex.cpython-310.pyc,,
transformers/models/deprecated/tapex/tokenization_tapex.py,sha256=MPuB1JknrO9WY_j-Hgy8JWGNKvcowBDrjhFi-bCGALw,64347
transformers/models/deprecated/trajectory_transformer/__init__.py,sha256=XnXDCm4ePannQqnQnMn1Fpqvmq9-1L0_mTeoqObM8-0,1806
transformers/models/deprecated/trajectory_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/configuration_trajectory_transformer.cpython-310.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/convert_trajectory_transformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/trajectory_transformer/__pycache__/modeling_trajectory_transformer.cpython-310.pyc,,
transformers/models/deprecated/trajectory_transformer/configuration_trajectory_transformer.py,sha256=qH3gf0InhrlutKUQNA4-OrqWp72n_Ha4B6jA_kZy55U,7061
transformers/models/deprecated/trajectory_transformer/convert_trajectory_transformer_original_pytorch_checkpoint_to_pytorch.py,sha256=sUPWNSvy46IYr8eFEyxqLraW90abuzy4Snrt3uKFW34,3138
transformers/models/deprecated/trajectory_transformer/modeling_trajectory_transformer.py,sha256=ts1LBqIjnC9gB53or9STcRJTAE7UaSdZnYHoh4jdtq4,25593
transformers/models/deprecated/transfo_xl/__init__.py,sha256=5IURzrZTTTFlLaPUn2R1ErF8SvQ9nF9QcNM8ENdntyg,2879
transformers/models/deprecated/transfo_xl/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/configuration_transfo_xl.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/convert_transfo_xl_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_tf_transfo_xl.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_tf_transfo_xl_utilities.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_transfo_xl.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/modeling_transfo_xl_utilities.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/__pycache__/tokenization_transfo_xl.cpython-310.pyc,,
transformers/models/deprecated/transfo_xl/configuration_transfo_xl.py,sha256=U3zrDVAkNbmSdcyLRnGVvHEo6BCZMONZKYHhrKIMGi0,7874
transformers/models/deprecated/transfo_xl/convert_transfo_xl_original_tf_checkpoint_to_pytorch.py,sha256=pGQ5iNKdCDjPilFBI_dwOmBl6Ha4pOkt7Guq4mSShBo,4937
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl.py,sha256=ZTxGgzubBKUieotTK-Z71Tvt7KKO9S-CF5pmOtAWl_U,45905
transformers/models/deprecated/transfo_xl/modeling_tf_transfo_xl_utilities.py,sha256=Dlv3ZzRduWFBnZZHn8RegbW45XeCecuYCzzzZC3bDXs,7633
transformers/models/deprecated/transfo_xl/modeling_transfo_xl.py,sha256=tqUEFzTSvwGe55FfIeQ3X5dJovQUyG03XhuA5PWArlw,55892
transformers/models/deprecated/transfo_xl/modeling_transfo_xl_utilities.py,sha256=L1l4K7sj8rwXzvhn7_-RK2UbOnYtfDUF0VdFr4L8nxA,10859
transformers/models/deprecated/transfo_xl/tokenization_transfo_xl.py,sha256=H4UYLnj5IqdFcnEqpOGz6Gk3WZ3AVvgQiIS75TqEPpk,31971
transformers/models/deprecated/tvlt/__init__.py,sha256=Ryp_kcJdg3sqjFKgyRVZjXAMUp_Epg9CL2GLQAHD0k0,2520
transformers/models/deprecated/tvlt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/tvlt/__pycache__/configuration_tvlt.cpython-310.pyc,,
transformers/models/deprecated/tvlt/__pycache__/feature_extraction_tvlt.cpython-310.pyc,,
transformers/models/deprecated/tvlt/__pycache__/image_processing_tvlt.cpython-310.pyc,,
transformers/models/deprecated/tvlt/__pycache__/modeling_tvlt.cpython-310.pyc,,
transformers/models/deprecated/tvlt/__pycache__/processing_tvlt.cpython-310.pyc,,
transformers/models/deprecated/tvlt/configuration_tvlt.py,sha256=uGh6Ie-Nu-uf5987LLtFBvpEqd8rLEjFzzkMKIol6b4,8623
transformers/models/deprecated/tvlt/feature_extraction_tvlt.py,sha256=Mx7tuGJvK-1YnS7ggYL6j_emzolu8L8Hrce5ATPtPR0,10558
transformers/models/deprecated/tvlt/image_processing_tvlt.py,sha256=1r9oEs7iP1gMAxLekBzXZq6CNDJyf5J65UiaEOw5_kQ,20090
transformers/models/deprecated/tvlt/modeling_tvlt.py,sha256=hQ0PyxS8oOjiokuLtMAxY1jNSL9WoKmH7k6RBrm6scs,56698
transformers/models/deprecated/tvlt/processing_tvlt.py,sha256=pC3zQjapxdhkqrl1QdJ7mXkEOSGNooP7kTUEWKUr_nE,3507
transformers/models/deprecated/van/__init__.py,sha256=UGKlepGOpOuVmsb6Mmess6kPa2qP4rLzfzJ0dsdQDno,1564
transformers/models/deprecated/van/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/van/__pycache__/configuration_van.cpython-310.pyc,,
transformers/models/deprecated/van/__pycache__/convert_van_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/van/__pycache__/modeling_van.cpython-310.pyc,,
transformers/models/deprecated/van/configuration_van.py,sha256=QpN-p2Hg0C59if2JSEG47j_zepx1f4KpCgIBYgLhCOY,4657
transformers/models/deprecated/van/convert_van_to_pytorch.py,sha256=UMaipjtB68OPj6OQzoSyquPlBkG-IbN4q1FZvs51Lxg,10373
transformers/models/deprecated/van/modeling_van.py,sha256=YwNP7YVzKpyURSQ3taijiOBDTlF086hEcT2fEGqQaTE,21130
transformers/models/deprecated/vit_hybrid/__init__.py,sha256=Ld0UOl3F4y-YaI42jk7ym_wvTFswFGJj_M6jpSeXU_E,2125
transformers/models/deprecated/vit_hybrid/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/vit_hybrid/__pycache__/configuration_vit_hybrid.cpython-310.pyc,,
transformers/models/deprecated/vit_hybrid/__pycache__/convert_vit_hybrid_timm_to_pytorch.cpython-310.pyc,,
transformers/models/deprecated/vit_hybrid/__pycache__/image_processing_vit_hybrid.cpython-310.pyc,,
transformers/models/deprecated/vit_hybrid/__pycache__/modeling_vit_hybrid.cpython-310.pyc,,
transformers/models/deprecated/vit_hybrid/configuration_vit_hybrid.py,sha256=VwJpgMa1l9rL-Rx9jF-POC_mz69y88tnHqgS431Qa10,8230
transformers/models/deprecated/vit_hybrid/convert_vit_hybrid_timm_to_pytorch.py,sha256=NT-72ZHHEaQbmmsHgh9_UI7gf_40Aykff8cW8XXspQQ,13412
transformers/models/deprecated/vit_hybrid/image_processing_vit_hybrid.py,sha256=tlTYKn9xVEO99MzL4FnCbRMyem-4oaZ7YNZAbPRK4UY,16303
transformers/models/deprecated/vit_hybrid/modeling_vit_hybrid.py,sha256=pvAArv3yZEtcbOxogWXR-e4ubBf7a4JyRpm8-Kk4Pew,32445
transformers/models/deprecated/xlm_prophetnet/__init__.py,sha256=OYkcL5jhbHEaLLvOAd0v-CppyQxbuRzNEzaSDBhXxKA,2408
transformers/models/deprecated/xlm_prophetnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/deprecated/xlm_prophetnet/__pycache__/configuration_xlm_prophetnet.cpython-310.pyc,,
transformers/models/deprecated/xlm_prophetnet/__pycache__/modeling_xlm_prophetnet.cpython-310.pyc,,
transformers/models/deprecated/xlm_prophetnet/__pycache__/tokenization_xlm_prophetnet.cpython-310.pyc,,
transformers/models/deprecated/xlm_prophetnet/configuration_xlm_prophetnet.py,sha256=dfa6w3RvOEZLxS9GAdQOKaJF1vT6Gc-w06a8Eujg5Sk,8916
transformers/models/deprecated/xlm_prophetnet/modeling_xlm_prophetnet.py,sha256=EX5mzNPUYfWqcN5B-YquCXvM_SE7TfZcbtzslv1WPBI,115593
transformers/models/deprecated/xlm_prophetnet/tokenization_xlm_prophetnet.py,sha256=HpD7sKmAaelMUUDwNrAyHgBLhiWI2rnDaxn-yFFjdEY,13272
transformers/models/depth_anything/__init__.py,sha256=R2x2xhnw4yvOwjciXSpDKWmz9fBoGsb7oh3Wk5hvQYw,1644
transformers/models/depth_anything/__pycache__/__init__.cpython-310.pyc,,
transformers/models/depth_anything/__pycache__/configuration_depth_anything.cpython-310.pyc,,
transformers/models/depth_anything/__pycache__/convert_depth_anything_to_hf.cpython-310.pyc,,
transformers/models/depth_anything/__pycache__/modeling_depth_anything.cpython-310.pyc,,
transformers/models/depth_anything/configuration_depth_anything.py,sha256=tRPafToHA6JrK9qq5iHImWj-Uh2TRn4vXTwvTUyGjNc,7188
transformers/models/depth_anything/convert_depth_anything_to_hf.py,sha256=COIzcjXuIpPrJb7xDK-ajsYbDkpK-mGPyH6qdjffjHQ,15021
transformers/models/depth_anything/modeling_depth_anything.py,sha256=EnBdujnsQZrmakkFDBf-fA74poUaAqOiJZumsDxZ0s4,18047
transformers/models/detr/__init__.py,sha256=laiAFgWbYIbhlVuVopDiDE33GsPHy3Oc8CJ1rwvkkjM,2270
transformers/models/detr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/detr/__pycache__/configuration_detr.cpython-310.pyc,,
transformers/models/detr/__pycache__/convert_detr_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/detr/__pycache__/convert_detr_to_pytorch.cpython-310.pyc,,
transformers/models/detr/__pycache__/feature_extraction_detr.cpython-310.pyc,,
transformers/models/detr/__pycache__/image_processing_detr.cpython-310.pyc,,
transformers/models/detr/__pycache__/modeling_detr.cpython-310.pyc,,
transformers/models/detr/configuration_detr.py,sha256=G0fL0UmbYPDum5mDWZEZIc-tXZ244MzekqOYjEfKS58,13486
transformers/models/detr/convert_detr_original_pytorch_checkpoint_to_pytorch.py,sha256=k1F8poVxIjsqQUUJu7CJl2Bj3x1IuivlgTi54Y_miKk,13560
transformers/models/detr/convert_detr_to_pytorch.py,sha256=1Y_Tz2WSdybHscDL-iuF5sJG2Ni2o2-85unaJLtaykw,18992
transformers/models/detr/feature_extraction_detr.py,sha256=gMyG16pNJKoimImXOyqi589hGj37OYGWb7ZoTx84d5I,1474
transformers/models/detr/image_processing_detr.py,sha256=c_aoZY6ni7Kbe4_J6b2XSCMVcMm0WtWBdUZ30KpHHcA,93999
transformers/models/detr/modeling_detr.py,sha256=NGn6UDcBs5ugWENhOv-5IYt9RXAb4Z4SUgbC7AGNMtU,111771
transformers/models/dialogpt/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dialogpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dialogpt/__pycache__/convert_dialogpt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/dialogpt/convert_dialogpt_original_pytorch_checkpoint_to_pytorch.py,sha256=Zp59TmLBKEs-x1-quZZeqARhpS3cTnnmgT4nCI0zsHY,1537
transformers/models/dinat/__init__.py,sha256=wgLyGigRXX4o4Wh3zpZLGv0NnurzO4G4t2scbEWAvUE,1640
transformers/models/dinat/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dinat/__pycache__/configuration_dinat.cpython-310.pyc,,
transformers/models/dinat/__pycache__/modeling_dinat.cpython-310.pyc,,
transformers/models/dinat/configuration_dinat.py,sha256=n4DS9D5u7yK_VsvwNm3opDn6tl4iPoiVELMZrsP5n9Q,7328
transformers/models/dinat/modeling_dinat.py,sha256=Xy0F64jRBN45WyHBhmy4nBwEo9YS3V54FR3geJ-DDOc,40324
transformers/models/dinov2/__init__.py,sha256=a4lmglUMyVC2iYsYljR2CIN8i95GUq6FlxgM_gMUIaM,1708
transformers/models/dinov2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dinov2/__pycache__/configuration_dinov2.cpython-310.pyc,,
transformers/models/dinov2/__pycache__/convert_dinov2_to_hf.cpython-310.pyc,,
transformers/models/dinov2/__pycache__/modeling_dinov2.cpython-310.pyc,,
transformers/models/dinov2/configuration_dinov2.py,sha256=9vrpyOD3dppEy5UhdLe5PP9ISMox8sIPffxkVo-1B2I,8041
transformers/models/dinov2/convert_dinov2_to_hf.py,sha256=UpXqLbLJ8sF9LUFJ2TgU_5DIZe6ViZmIXdWEmDhVUL0,11863
transformers/models/dinov2/modeling_dinov2.py,sha256=0YZbvRyS6U8Eg1p1FqmLCQ2aaIykJiOHvdvkmkUXvbU,36214
transformers/models/distilbert/__init__.py,sha256=ZXSxjAeG8GvUcX7j-euPmZ2YJFqWCdeaub0oa2v1eUE,4847
transformers/models/distilbert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/configuration_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/modeling_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/modeling_flax_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/modeling_tf_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/tokenization_distilbert.cpython-310.pyc,,
transformers/models/distilbert/__pycache__/tokenization_distilbert_fast.cpython-310.pyc,,
transformers/models/distilbert/configuration_distilbert.py,sha256=KuM3O1gMdZMxfgWvKWANMpzuXW4gIu7EuJFzQXT_j0s,5989
transformers/models/distilbert/modeling_distilbert.py,sha256=LrzzIprcO6dXGrf9I4hEv8o7cNhQUmb1wppfBza-BM4,56330
transformers/models/distilbert/modeling_flax_distilbert.py,sha256=cBRX7sUX2G9aSX6_I15sZ_H1yTXOMvwM7Gw3xbgOL6Q,32629
transformers/models/distilbert/modeling_tf_distilbert.py,sha256=CLK_eoeq4qJc9q3H2fzSF_Wut9SUQ4MJLC48T1zt8P8,48845
transformers/models/distilbert/tokenization_distilbert.py,sha256=Y9TPnqWaB-k8PbebKeZRoTyRFk2tgwySxcyZ_zybNOY,21906
transformers/models/distilbert/tokenization_distilbert_fast.py,sha256=oO1CanamHXMuG6eJH8LOwRBEgK2yCSe80lF2fDGz5zo,8037
transformers/models/dit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
transformers/models/dit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dit/__pycache__/convert_dit_unilm_to_pytorch.cpython-310.pyc,,
transformers/models/dit/convert_dit_unilm_to_pytorch.py,sha256=GEMnwc1iMEobjwXLOxLJZarjSqvABOIkUAfgSp9Gnyw,9419
transformers/models/donut/__init__.py,sha256=KB3xtqV9O8j-xyrwIR2ujn9fDMKUVtjVAAqsZyzUEvs,2263
transformers/models/donut/__pycache__/__init__.cpython-310.pyc,,
transformers/models/donut/__pycache__/configuration_donut_swin.cpython-310.pyc,,
transformers/models/donut/__pycache__/convert_donut_to_pytorch.cpython-310.pyc,,
transformers/models/donut/__pycache__/feature_extraction_donut.cpython-310.pyc,,
transformers/models/donut/__pycache__/image_processing_donut.cpython-310.pyc,,
transformers/models/donut/__pycache__/modeling_donut_swin.cpython-310.pyc,,
transformers/models/donut/__pycache__/processing_donut.cpython-310.pyc,,
transformers/models/donut/configuration_donut_swin.py,sha256=rjGgevf-z_sZ7BrVI7CD0-8I0h3ZixjxBfsigmlwj_M,5753
transformers/models/donut/convert_donut_to_pytorch.py,sha256=lIIm3j7oyJYa2xy-piZ3bvUEHOSP9mQ0x_MZhqPiBOY,9337
transformers/models/donut/feature_extraction_donut.py,sha256=jBSpDfoiCg_IWr4gcphIcxs7DA760JnH6V6hAfaoYPM,1179
transformers/models/donut/image_processing_donut.py,sha256=vL7BsBj43uQsQEHJXXw3oMHmjOGFVH_IXRxQXzbzfK4,22310
transformers/models/donut/modeling_donut_swin.py,sha256=7s2KZP-mxryTuH4SeW5k2gziueoyMj2gpRNrVKyPk3g,45526
transformers/models/donut/processing_donut.py,sha256=KM7zaIWJQ5LDEUExopXQg7dyblJWHgBozqqF8wTBsm4,8221
transformers/models/dpr/__init__.py,sha256=8dPmc9s-P5LDxICyD2E8jWZdQgRBKpaxxCsvIhKIec8,3733
transformers/models/dpr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dpr/__pycache__/configuration_dpr.cpython-310.pyc,,
transformers/models/dpr/__pycache__/convert_dpr_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/dpr/__pycache__/modeling_dpr.cpython-310.pyc,,
transformers/models/dpr/__pycache__/modeling_tf_dpr.cpython-310.pyc,,
transformers/models/dpr/__pycache__/tokenization_dpr.cpython-310.pyc,,
transformers/models/dpr/__pycache__/tokenization_dpr_fast.cpython-310.pyc,,
transformers/models/dpr/configuration_dpr.py,sha256=dY4rToJ-PPTeDESOfwkYaNZvcgfgYTz35oPRcfUDShs,6390
transformers/models/dpr/convert_dpr_original_checkpoint_to_pytorch.py,sha256=XsxG5FBg46-EHlDsMq4w21C9W4wl8RZ6GZvx5coBmfk,6132
transformers/models/dpr/modeling_dpr.py,sha256=Ms-XpQBlIT4-Ma5vOtgednsL9kKCJ8F18t-iiI8xnAA,28334
transformers/models/dpr/modeling_tf_dpr.py,sha256=Gf2Naxx1LocjSvhaLxSkORR17RbQGbZ7JveuYhVEECc,33635
transformers/models/dpr/tokenization_dpr.py,sha256=d4zRo3bnaI5Gfpao-SJ7RemQPQHAEE3nJt9CDwUHaac,15725
transformers/models/dpr/tokenization_dpr_fast.py,sha256=wswsSm3NV2dmbQOdLnpkkVEbm-EuIVm6nUHZyIvAnuU,16111
transformers/models/dpt/__init__.py,sha256=AFD_wxuPhcC9KLJa9e6lDAR8l-iKhwOcU1bA0QIl_9s,2280
transformers/models/dpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/dpt/__pycache__/configuration_dpt.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dinov2_depth_to_hf.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_beit_to_hf.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_hybrid_to_pytorch.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_swinv2_to_hf.cpython-310.pyc,,
transformers/models/dpt/__pycache__/convert_dpt_to_pytorch.cpython-310.pyc,,
transformers/models/dpt/__pycache__/feature_extraction_dpt.cpython-310.pyc,,
transformers/models/dpt/__pycache__/image_processing_dpt.cpython-310.pyc,,
transformers/models/dpt/__pycache__/modeling_dpt.cpython-310.pyc,,
transformers/models/dpt/configuration_dpt.py,sha256=R553pt-IRpm21vtt4eaj0GTirzG3Bcc5CZsIZjVH_TU,14042
transformers/models/dpt/convert_dinov2_depth_to_hf.py,sha256=1ZdgsarM7zj6DL7tl_wpA60O4uSXV_zW9qpNurv-Kc8,16934
transformers/models/dpt/convert_dpt_beit_to_hf.py,sha256=Xs1RAcAQitVKUyjyEYTxYgbY2udQyyFbnOtnDhEN64k,14346
transformers/models/dpt/convert_dpt_hybrid_to_pytorch.py,sha256=j0D4Fo2lx9ABo_cSjAO6shyyXKYwOlu6_x-7n-GnVro,12977
transformers/models/dpt/convert_dpt_swinv2_to_hf.py,sha256=LH0aOWueCHqVl8Huz_MLdoQqso5C8Tgh7_ArUxxvGYg,15175
transformers/models/dpt/convert_dpt_to_pytorch.py,sha256=ezSKskhntUX7V-Y396PA7a5XhmdYfJDQPechPiy_Lr4,11877
transformers/models/dpt/feature_extraction_dpt.py,sha256=ZgBcSKNDX0_Fstv94sp1r9jpr9zvXCLPwvIek76Fkso,1165
transformers/models/dpt/image_processing_dpt.py,sha256=6SlcemNXhoI1HGU34YmZny77WfEhrij7SXk_fvlEExg,23017
transformers/models/dpt/modeling_dpt.py,sha256=QTKMvo9D9HEHNAW5neS3BQXxOh4wU_0qYBb6d_L-LQA,57234
transformers/models/efficientnet/__init__.py,sha256=ZE1lrYitMe2LaWqpWSWF7l87JzMl5Y2brMoh8U3WFWU,2454
transformers/models/efficientnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/efficientnet/__pycache__/configuration_efficientnet.cpython-310.pyc,,
transformers/models/efficientnet/__pycache__/convert_efficientnet_to_pytorch.cpython-310.pyc,,
transformers/models/efficientnet/__pycache__/image_processing_efficientnet.cpython-310.pyc,,
transformers/models/efficientnet/__pycache__/modeling_efficientnet.cpython-310.pyc,,
transformers/models/efficientnet/configuration_efficientnet.py,sha256=srZrPZPIa8h2ALPjhZ-nOlDFkL4aSONuxX3WCgdvoZk,7596
transformers/models/efficientnet/convert_efficientnet_to_pytorch.py,sha256=e2Na1xvNc7z9XvvI7v6v1V2uFWr88MSTN3JPKR5GstM,12756
transformers/models/efficientnet/image_processing_efficientnet.py,sha256=t2SCJE3ChbM8bkQzd2QvxAsQ2SGB-sv8ywxw-MyGVc8,18848
transformers/models/efficientnet/modeling_efficientnet.py,sha256=85JtUWn3jHhiXuElYcB0Z56AybLNS9WDU6IDur27m5M,23946
transformers/models/electra/__init__.py,sha256=oGxwD4U-g4EcdBcHfXM5rPU0kqFEgZ7ZfSTtcQ20iYE,4971
transformers/models/electra/__pycache__/__init__.cpython-310.pyc,,
transformers/models/electra/__pycache__/configuration_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/convert_electra_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/electra/__pycache__/modeling_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/modeling_flax_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/modeling_tf_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/tokenization_electra.cpython-310.pyc,,
transformers/models/electra/__pycache__/tokenization_electra_fast.cpython-310.pyc,,
transformers/models/electra/configuration_electra.py,sha256=1YBlbWbul0b043xyZmAjErD2HVi6RllrZxxgkwmvlbE,9094
transformers/models/electra/convert_electra_original_tf_checkpoint_to_pytorch.py,sha256=8G7yTj4TTecFVf1OPFeqFuPSuyzronf3gSeiQQeEMG4,2861
transformers/models/electra/modeling_electra.py,sha256=niYxVN4A9XsjfW8t1zkPRmkK6qxU21_1OKJTDqY8uuk,75885
transformers/models/electra/modeling_flax_electra.py,sha256=S5TkUbjF-9GNOxeiGfXTjc3tnINV18R8CLLFf30A9zU,62268
transformers/models/electra/modeling_tf_electra.py,sha256=XEePvRRTHB1N2BfahChTAjdbtyJ84W3SYxsy8CPmAq4,78333
transformers/models/electra/tokenization_electra.py,sha256=m1-PY3o6pZVr2xcqP4tfv8oMZHKzrHNF0x6HqAOsGUo,20909
transformers/models/electra/tokenization_electra_fast.py,sha256=zPqzst_6dX5eiFgR2iVsZuzYIS-KTe8BKDkh3fsPTQo,7685
transformers/models/encodec/__init__.py,sha256=ZL-u3DPRBfztDjGggKvhnUSZiLc16s-WJHLvGlaKlwE,1699
transformers/models/encodec/__pycache__/__init__.cpython-310.pyc,,
transformers/models/encodec/__pycache__/configuration_encodec.cpython-310.pyc,,
transformers/models/encodec/__pycache__/convert_encodec_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/encodec/__pycache__/feature_extraction_encodec.cpython-310.pyc,,
transformers/models/encodec/__pycache__/modeling_encodec.cpython-310.pyc,,
transformers/models/encodec/configuration_encodec.py,sha256=awVFWJyNfS4nRnrdtkkiw3l2kF_UDTZefrfwcBwC6XQ,8495
transformers/models/encodec/convert_encodec_checkpoint_to_pytorch.py,sha256=zF2ZSOCFsiMNvtIvRhjoucoF2G3m0nW-cHXimF_2uwQ,15253
transformers/models/encodec/feature_extraction_encodec.py,sha256=luYd1uGvvQC_mDYlUsnMtSBn_S0dhbazYJ9zYGuQ1Kc,9873
transformers/models/encodec/modeling_encodec.py,sha256=QK6oVGQF482fWUZW_yRpc6WRVwXUF9Satt4TfjYDzEo,33451
transformers/models/encoder_decoder/__init__.py,sha256=bR1yPbuqKHUYXaxI_QuDz6ccBSWpCr0THhPBM3lnttA,2451
transformers/models/encoder_decoder/__pycache__/__init__.cpython-310.pyc,,
transformers/models/encoder_decoder/__pycache__/configuration_encoder_decoder.cpython-310.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_encoder_decoder.cpython-310.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_flax_encoder_decoder.cpython-310.pyc,,
transformers/models/encoder_decoder/__pycache__/modeling_tf_encoder_decoder.cpython-310.pyc,,
transformers/models/encoder_decoder/configuration_encoder_decoder.py,sha256=HaF1rtwzf_tDXJYrfycr4ktA8-LlBia_RdAWD60RTu8,4362
transformers/models/encoder_decoder/modeling_encoder_decoder.py,sha256=EsU-CI1i1c2OXbpDI49y9A0cj4rxem5NUuVIkTICuVw,35925
transformers/models/encoder_decoder/modeling_flax_encoder_decoder.py,sha256=MDrdjzKTc3rNKfI0Yed7Lo5tgGiFnfmMK74IgI9cEr8,43527
transformers/models/encoder_decoder/modeling_tf_encoder_decoder.py,sha256=y8wz5f_5cl-t8bbE0hF7vBkNof47FuvqwrOO-sAM4Jk,34306
transformers/models/ernie/__init__.py,sha256=hBeUclYWth7qZpSPBK5cXngWMYnRz2ptK0kL4Zn2NNY,2159
transformers/models/ernie/__pycache__/__init__.cpython-310.pyc,,
transformers/models/ernie/__pycache__/configuration_ernie.cpython-310.pyc,,
transformers/models/ernie/__pycache__/modeling_ernie.cpython-310.pyc,,
transformers/models/ernie/configuration_ernie.py,sha256=kOlh5l_oR5zz5x5lrNdIt5Ofg3CQm1xMlDdOZnUxA5c,7647
transformers/models/ernie/modeling_ernie.py,sha256=-vbDXxq0GuvMJuAVuyWmws-i3GdVwIJ9-MSy8xkT8CU,84214
transformers/models/esm/__init__.py,sha256=EVmrmVPfknDF1TKnqInKZt1dPJUJaRTf9RZRwW3bfZk,2716
transformers/models/esm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/esm/__pycache__/configuration_esm.cpython-310.pyc,,
transformers/models/esm/__pycache__/convert_esm.cpython-310.pyc,,
transformers/models/esm/__pycache__/modeling_esm.cpython-310.pyc,,
transformers/models/esm/__pycache__/modeling_esmfold.cpython-310.pyc,,
transformers/models/esm/__pycache__/modeling_tf_esm.cpython-310.pyc,,
transformers/models/esm/__pycache__/tokenization_esm.cpython-310.pyc,,
transformers/models/esm/configuration_esm.py,sha256=BBItEALaPj2mm3XfsMgouwwuoAihq_uE1jeZ08p_o7k,14359
transformers/models/esm/convert_esm.py,sha256=x7Bz5lkBXMQJ_vYZ0-L3_z61lcTRQ9clIyC5WEkX2Kg,18469
transformers/models/esm/modeling_esm.py,sha256=J2cFiiv2F_uvEaDG29O76mTJ4OMPthJDhSF_YzlonQI,55567
transformers/models/esm/modeling_esmfold.py,sha256=GgMkBeEhTvZBj61fNGqDkZsWTGeRwrhkHSGYa0otbJ4,86908
transformers/models/esm/modeling_tf_esm.py,sha256=SZaKfpy0OWjpqa6oqygW43uNHsxOcqzzLBovlwQrPiQ,68963
transformers/models/esm/openfold_utils/__init__.py,sha256=Xy2uqvFsLC8Ax-OOce5PgoBDiZgEJgJPqs__p5SBWUY,446
transformers/models/esm/openfold_utils/__pycache__/__init__.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/chunk_utils.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/data_transforms.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/feats.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/loss.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/protein.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/residue_constants.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/rigid_utils.cpython-310.pyc,,
transformers/models/esm/openfold_utils/__pycache__/tensor_utils.cpython-310.pyc,,
transformers/models/esm/openfold_utils/chunk_utils.py,sha256=-i6hJyc_rHh4oaWXjznZuQH41LZLRHF3M2rltQVGGgY,14390
transformers/models/esm/openfold_utils/data_transforms.py,sha256=F4wGANRhKLd6MLHrwg2IxpqCxCJEx8aFSxqAdsXsBMo,3764
transformers/models/esm/openfold_utils/feats.py,sha256=RHH65TclSlcI-fuGP16f6xr_QolV0aGRXEWUq-0boIU,8368
transformers/models/esm/openfold_utils/loss.py,sha256=wY2ONqbuRvWMomjkpfPwfoa7dqCO2vFkM-kmNfhjivo,3705
transformers/models/esm/openfold_utils/protein.py,sha256=R7diEvvIOtJY28B-_6TSMZdWmLFY4NOwaMzQmAg0x_w,11491
transformers/models/esm/openfold_utils/residue_constants.py,sha256=FtPlWVweacknPfmi4XCrR66kFr4EuYXywvx0IEY8KAs,37992
transformers/models/esm/openfold_utils/rigid_utils.py,sha256=EF79POBO-abRsdXrfdKLaqJUVIPp4EOMFVt5oOjx504,41122
transformers/models/esm/openfold_utils/tensor_utils.py,sha256=cySnhhaYbdq4SqyWyAF3qGeUWPfWKsuTYWRnX-h21sE,4781
transformers/models/esm/tokenization_esm.py,sha256=HD6JXwd33oSwMAvusgaAKpb-kFPbI0pPXz3TlLySB3s,5356
transformers/models/falcon/__init__.py,sha256=-fQlTGZokpudg-zyBQzFtOEcHfHSuBe_2lR8umLnouU,1891
transformers/models/falcon/__pycache__/__init__.cpython-310.pyc,,
transformers/models/falcon/__pycache__/configuration_falcon.cpython-310.pyc,,
transformers/models/falcon/__pycache__/convert_custom_code_checkpoint.cpython-310.pyc,,
transformers/models/falcon/__pycache__/modeling_falcon.cpython-310.pyc,,
transformers/models/falcon/configuration_falcon.py,sha256=T7iIbkjYv6LqdMNj-MArzM3eDc23Bghqg_wUt2Gg7p4,9824
transformers/models/falcon/convert_custom_code_checkpoint.py,sha256=XPJ1owRjRno_Y1AD5UeoPE4oo6a-SeQR9w9u-EIUktE,3061
transformers/models/falcon/modeling_falcon.py,sha256=LxQugPetcFyD6ondwLuQbKpbJ0AjS8ZPSpsqQ2i2ibk,71058
transformers/models/fastspeech2_conformer/__init__.py,sha256=JCdE6cdG8sTDdpGXf74zZiLRa62mzZtlwy5xAwOFqzA,2228
transformers/models/fastspeech2_conformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/configuration_fastspeech2_conformer.cpython-310.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/convert_fastspeech2_conformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/convert_hifigan.cpython-310.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/convert_model_with_hifigan.cpython-310.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/modeling_fastspeech2_conformer.cpython-310.pyc,,
transformers/models/fastspeech2_conformer/__pycache__/tokenization_fastspeech2_conformer.cpython-310.pyc,,
transformers/models/fastspeech2_conformer/configuration_fastspeech2_conformer.py,sha256=org-i9kez6BwbLLuLRUUZLtn0I4rdvP7ljQxK_IhGeU,24344
transformers/models/fastspeech2_conformer/convert_fastspeech2_conformer_original_pytorch_checkpoint_to_pytorch.py,sha256=-ToJHpwI-xoLLMzLYdqFrBL6j6nsSPlNbkQ3pfTgJ6Y,8939
transformers/models/fastspeech2_conformer/convert_hifigan.py,sha256=RC1PaVnl1cLx8c2LdYycNti7iYRhUM7_KrX2mF5WyCM,5431
transformers/models/fastspeech2_conformer/convert_model_with_hifigan.py,sha256=wT4pQGgEHVFoWI1Lb71L7_i6ujfNrSMDGYuDGb4oeh8,3471
transformers/models/fastspeech2_conformer/modeling_fastspeech2_conformer.py,sha256=ifod752_t_M3JfKK2I2dOaPT_8Ae8GtyLNibX0_63tE,77562
transformers/models/fastspeech2_conformer/tokenization_fastspeech2_conformer.py,sha256=mpruo8WwH8Y6fDseUyVFut6EhOMNU7hcHEQt3DjM4Ts,6219
transformers/models/flaubert/__init__.py,sha256=x8RYQ-yMENmOOnKrjRqkfy4wvg5ouoM0-wHev1l6fs4,3196
transformers/models/flaubert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/flaubert/__pycache__/configuration_flaubert.cpython-310.pyc,,
transformers/models/flaubert/__pycache__/modeling_flaubert.cpython-310.pyc,,
transformers/models/flaubert/__pycache__/modeling_tf_flaubert.cpython-310.pyc,,
transformers/models/flaubert/__pycache__/tokenization_flaubert.cpython-310.pyc,,
transformers/models/flaubert/configuration_flaubert.py,sha256=GRxy8bDdliFToCO_4DLylbWdg-k_IwzjiDFb8MuAHuc,11188
transformers/models/flaubert/modeling_flaubert.py,sha256=0Po4xaVS3RzG4gLhZsRbIVK4tjXTxJtgTgHCHdOfGok,57498
transformers/models/flaubert/modeling_tf_flaubert.py,sha256=qsVYT7DRjUHPNEra75BHhfDU7odwDazmUblicyFt8WQ,57079
transformers/models/flaubert/tokenization_flaubert.py,sha256=7VRBuxhpy6D-nSfb1gpstsebanBp97lsc7zd7QBmIx8,22174
transformers/models/flava/__init__.py,sha256=o7tPGlidhlsAcX2A8qNW1cg434KxyHDgxNkRZXz9Ho0,2842
transformers/models/flava/__pycache__/__init__.cpython-310.pyc,,
transformers/models/flava/__pycache__/configuration_flava.cpython-310.pyc,,
transformers/models/flava/__pycache__/convert_dalle_to_flava_codebook.cpython-310.pyc,,
transformers/models/flava/__pycache__/convert_flava_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/flava/__pycache__/feature_extraction_flava.cpython-310.pyc,,
transformers/models/flava/__pycache__/image_processing_flava.cpython-310.pyc,,
transformers/models/flava/__pycache__/modeling_flava.cpython-310.pyc,,
transformers/models/flava/__pycache__/processing_flava.cpython-310.pyc,,
transformers/models/flava/configuration_flava.py,sha256=AJGwPYOZj6peMecbsYFguCzz39XOOu-1ANa7Fn1ACW0,37092
transformers/models/flava/convert_dalle_to_flava_codebook.py,sha256=iEJM9W_cKk3HK0gKS6i2ygEMeyymWCMl18LDaQXRAhY,3428
transformers/models/flava/convert_flava_original_pytorch_to_hf.py,sha256=LilQpbe6qeN2P_uXljae6zEPx_KoepoRv4uvCEAo0QA,4372
transformers/models/flava/feature_extraction_flava.py,sha256=mA1uAn29yv9PV7gYXauz0VTAJDgcpl9DPHvH99Ed__s,1201
transformers/models/flava/image_processing_flava.py,sha256=88KY6CipM_6HkY3SKu9dfdv_KhSAoYrFZRkKqrpal7A,38581
transformers/models/flava/modeling_flava.py,sha256=AEkkRgDoKUl8chsGUjms8zXPNXq23UCgIO-MEUiYqag,96527
transformers/models/flava/processing_flava.py,sha256=fj9uFlMerVGFnB9hV1XJ61c3q82qstjPwmWUdMiL46U,6832
transformers/models/fnet/__init__.py,sha256=Id1nhNi9gK--czTIMMMfP-Ex_cotBwChJrEN3O3Y57E,3011
transformers/models/fnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/fnet/__pycache__/configuration_fnet.cpython-310.pyc,,
transformers/models/fnet/__pycache__/convert_fnet_original_flax_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/fnet/__pycache__/modeling_fnet.cpython-310.pyc,,
transformers/models/fnet/__pycache__/tokenization_fnet.cpython-310.pyc,,
transformers/models/fnet/__pycache__/tokenization_fnet_fast.cpython-310.pyc,,
transformers/models/fnet/configuration_fnet.py,sha256=vGw3_fqJyOObEAMIscbpGEZAkXQMxXMduMgoV4uLIRM,5540
transformers/models/fnet/convert_fnet_original_flax_checkpoint_to_pytorch.py,sha256=s2hJZxxZnljY-aQE5S5CT6tL2yYbwH28KxCMKrzzHMY,6911
transformers/models/fnet/modeling_fnet.py,sha256=DLkU8zyQEVF6oLcbpNeb5CsUHWV0wUk-sdD3OD7whmQ,49261
transformers/models/fnet/tokenization_fnet.py,sha256=P-4pz_Sbk-ZNOS6Pm9j8F_nwmX5O6yL6LC6LwR5oydY,14548
transformers/models/fnet/tokenization_fnet_fast.py,sha256=CPT2yPJLBf2ropYgcC1HCOyK5EyPnHpAlgkqISeT9T4,8062
transformers/models/focalnet/__init__.py,sha256=iq_lDemgRSttskuPWZ8ynH7hYj0RAsVsgW1oEqrpSKg,1805
transformers/models/focalnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/focalnet/__pycache__/configuration_focalnet.cpython-310.pyc,,
transformers/models/focalnet/__pycache__/convert_focalnet_to_hf_format.cpython-310.pyc,,
transformers/models/focalnet/__pycache__/modeling_focalnet.cpython-310.pyc,,
transformers/models/focalnet/configuration_focalnet.py,sha256=xEsO6ZaSfSoc9ABEuwwIPbvTl1kb4UjUNcMvY_54Jxs,8026
transformers/models/focalnet/convert_focalnet_to_hf_format.py,sha256=xBoop7K4unfPawCbmlv7BTQHpbJkaUWasrwsw8dW_KI,9450
transformers/models/focalnet/modeling_focalnet.py,sha256=ViZOenMEuyg3oazS9BZJILvulGAb9VSKejFBxvPClpE,43126
transformers/models/fsmt/__init__.py,sha256=pgbhoHULNweTt72ejl57_M5p5dPFn-Hy0rmBrt39yxw,1601
transformers/models/fsmt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/fsmt/__pycache__/configuration_fsmt.cpython-310.pyc,,
transformers/models/fsmt/__pycache__/convert_fsmt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/fsmt/__pycache__/modeling_fsmt.cpython-310.pyc,,
transformers/models/fsmt/__pycache__/tokenization_fsmt.cpython-310.pyc,,
transformers/models/fsmt/configuration_fsmt.py,sha256=cwwkeXb4iqqVUxQVBTMh_zcO3nBy4TL-BA_nqd6Gfmw,10063
transformers/models/fsmt/convert_fsmt_original_pytorch_checkpoint_to_pytorch.py,sha256=BWtn90XQAuWGp8k9zns5St9On_os395ESNgkaXy6y2g,11264
transformers/models/fsmt/modeling_fsmt.py,sha256=R3d88t2VCsW-AOiEO5i7A6vWm-7-l7EJ_fFjDHbERFw,58496
transformers/models/fsmt/tokenization_fsmt.py,sha256=bdkwFgl9kUloobCmnpXpQN6bsUmH5xlFt7Je3AYeRXE,19233
transformers/models/funnel/__init__.py,sha256=Ne8K6BFtuQ9o4xhZd6Um8ErszGxzcmQp1S28yxG105w,3846
transformers/models/funnel/__pycache__/__init__.cpython-310.pyc,,
transformers/models/funnel/__pycache__/configuration_funnel.cpython-310.pyc,,
transformers/models/funnel/__pycache__/convert_funnel_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/funnel/__pycache__/modeling_funnel.cpython-310.pyc,,
transformers/models/funnel/__pycache__/modeling_tf_funnel.cpython-310.pyc,,
transformers/models/funnel/__pycache__/tokenization_funnel.cpython-310.pyc,,
transformers/models/funnel/__pycache__/tokenization_funnel_fast.cpython-310.pyc,,
transformers/models/funnel/configuration_funnel.py,sha256=nHVPG52luFHXMx5TWb0jIWKFMvh4_WUdWJEvH0fco6g,7651
transformers/models/funnel/convert_funnel_original_tf_checkpoint_to_pytorch.py,sha256=d4-Yv59rKaIrXIyXpW4S3LIP_nEjPvptO4izNiuQFKE,2334
transformers/models/funnel/modeling_funnel.py,sha256=B5xSWBe7yLRH9U3QK9DsKMiaLMH1wMeasfnQFb9AVYg,69434
transformers/models/funnel/modeling_tf_funnel.py,sha256=B2wzV1dufK_PtBYaVCTSVmNtPg4l6s3-Ug92yRg6UMo,80164
transformers/models/funnel/tokenization_funnel.py,sha256=LQLpmic2PeJRTRTYgtOTcyzRRrG9-d0xulppuRalLEU,22368
transformers/models/funnel/tokenization_funnel_fast.py,sha256=6orKsz7fGZjcgW5LJXo8QwCL4-ceaVS0FrgmqnaiNvE,8643
transformers/models/fuyu/__init__.py,sha256=W6x4OjGc1H2KkVqkoLdUdizZjUYWSaXGMqoBzlw_qbg,2110
transformers/models/fuyu/__pycache__/__init__.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/configuration_fuyu.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/convert_fuyu_model_weights_to_hf.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/image_processing_fuyu.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/modeling_fuyu.cpython-310.pyc,,
transformers/models/fuyu/__pycache__/processing_fuyu.cpython-310.pyc,,
transformers/models/fuyu/configuration_fuyu.py,sha256=yBjEOiSElEgy9cQJ8bHr1_Vlt0LqpINnjs9HSraIuoU,10450
transformers/models/fuyu/convert_fuyu_model_weights_to_hf.py,sha256=c8A4qiUY47MfPeEG518qofxFdzut0me3EtFNizEHv6Q,4847
transformers/models/fuyu/image_processing_fuyu.py,sha256=jYB8EWiRio_c5g4EkReAxLFFrv7fdoONlKGVGZnadxM,33810
transformers/models/fuyu/modeling_fuyu.py,sha256=RG3duAO_hbaDN_bIqTK_-w3wzt98zp2hsuvDGK8yTL0,18888
transformers/models/fuyu/processing_fuyu.py,sha256=hI5JW166jfAPEJqImz0K8ykn6XV04UNGqozj4EeIsX8,31929
transformers/models/gemma/__init__.py,sha256=k9d4O_4d3dgILjsnNPwrCz2lmgryfXzBJvkNWIBqRrA,3343
transformers/models/gemma/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gemma/__pycache__/configuration_gemma.cpython-310.pyc,,
transformers/models/gemma/__pycache__/convert_gemma_weights_to_hf.cpython-310.pyc,,
transformers/models/gemma/__pycache__/diff_gemma.cpython-310.pyc,,
transformers/models/gemma/__pycache__/modeling_flax_gemma.cpython-310.pyc,,
transformers/models/gemma/__pycache__/modeling_gemma.cpython-310.pyc,,
transformers/models/gemma/__pycache__/tokenization_gemma.cpython-310.pyc,,
transformers/models/gemma/__pycache__/tokenization_gemma_fast.cpython-310.pyc,,
transformers/models/gemma/configuration_gemma.py,sha256=S6wDVn3Jj4VcjUUF0DrwO2qXQMS5kxgmDo8WM2Yr5vo,7655
transformers/models/gemma/convert_gemma_weights_to_hf.py,sha256=UCoyJd4wVYKlikKMK0-9GRFAa-Cm3OtLt7oSJjXOuPA,7366
transformers/models/gemma/diff_gemma.py,sha256=5TQk2FyD8siylUwtKGT1ApWfRqiDVXpMTwMm--YcOWM,28080
transformers/models/gemma/modeling_flax_gemma.py,sha256=YnBPw6MsDvNt9QXpPWhlXRJMZ6lOV-HXax_tI3vpqwI,32333
transformers/models/gemma/modeling_gemma.py,sha256=mMiUzWq99AuFaRYaiqgfqXnpX1ehnoT5khye0eQG7Qk,61028
transformers/models/gemma/tokenization_gemma.py,sha256=NEp7agr9cZhh9OoSh5IVCg0v_PjOTbXkyncST9S3omU,13982
transformers/models/gemma/tokenization_gemma_fast.py,sha256=bTIi46E_PXDmRwD8hQG0AI6HRlj-03Y7itWFA6tclQE,8279
transformers/models/gemma2/__init__.py,sha256=37KwSviwoiPmxwyXiBLr9hRQAVpCHOds0cRBsR8M7t4,1776
transformers/models/gemma2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gemma2/__pycache__/configuration_gemma2.cpython-310.pyc,,
transformers/models/gemma2/__pycache__/convert_gemma2_weights_to_hf.cpython-310.pyc,,
transformers/models/gemma2/__pycache__/diff_gemma2.cpython-310.pyc,,
transformers/models/gemma2/__pycache__/modeling_gemma2.cpython-310.pyc,,
transformers/models/gemma2/configuration_gemma2.py,sha256=_fLeKIVmoZGq_OSZCPn-huiAVVbNiYU311qPSqDXUGQ,8291
transformers/models/gemma2/convert_gemma2_weights_to_hf.py,sha256=Dp4GAX_zZ5GCs8bLWmUuc7Pbw4pNOUIfFKOZ3KMYi8A,8543
transformers/models/gemma2/diff_gemma2.py,sha256=PgGpD0xAL4e0HzSLpGBNVhOQftAFr95oL6qsgxPvKvs,25112
transformers/models/gemma2/modeling_gemma2.py,sha256=sHXSQNAC8RyiPC5Za3xHrnHmTQBbLhk18B3Sg4ufSL8,56322
transformers/models/git/__init__.py,sha256=2S18LwDgxXkRd4kszbkAVbEFqfnGFIhiARQOOVRdoGM,1724
transformers/models/git/__pycache__/__init__.cpython-310.pyc,,
transformers/models/git/__pycache__/configuration_git.cpython-310.pyc,,
transformers/models/git/__pycache__/convert_git_to_pytorch.cpython-310.pyc,,
transformers/models/git/__pycache__/modeling_git.cpython-310.pyc,,
transformers/models/git/__pycache__/processing_git.cpython-310.pyc,,
transformers/models/git/configuration_git.py,sha256=6OgVliEpJxVvVoG7ty-HeSeX_xS7S3dFsL0sX5_Y_lM,11214
transformers/models/git/convert_git_to_pytorch.py,sha256=e9TOTlxKaGXPB7qMQni-LlLJExP9vE-qEa_4cOTTP38,22389
transformers/models/git/modeling_git.py,sha256=qteQG7Jv53gUnJcvd04rn73E9zRwlfP-RA-I8T46NqY,69184
transformers/models/git/processing_git.py,sha256=ITyJdm7Wwsh5ao7cWdseUolXmziXaLzbFBRC3ZXImUQ,5756
transformers/models/glpn/__init__.py,sha256=1uX_pAqV1QHOi0d0L428UupMAcFBY6OWSDE6VXugxOE,2216
transformers/models/glpn/__pycache__/__init__.cpython-310.pyc,,
transformers/models/glpn/__pycache__/configuration_glpn.cpython-310.pyc,,
transformers/models/glpn/__pycache__/convert_glpn_to_pytorch.cpython-310.pyc,,
transformers/models/glpn/__pycache__/feature_extraction_glpn.cpython-310.pyc,,
transformers/models/glpn/__pycache__/image_processing_glpn.cpython-310.pyc,,
transformers/models/glpn/__pycache__/modeling_glpn.cpython-310.pyc,,
transformers/models/glpn/configuration_glpn.py,sha256=fyIPU3XI3ip18MTyoha9pbGqs4cqfRv1ZO7mztj3TnI,5971
transformers/models/glpn/convert_glpn_to_pytorch.py,sha256=i-_gJfqz7ethjzLpEz82rajkhSvpXULlI443jFdczsM,8557
transformers/models/glpn/feature_extraction_glpn.py,sha256=S263LFeHVRym_jKt8KkTOjjtA1_BqARnUgbSFExgPN4,1172
transformers/models/glpn/image_processing_glpn.py,sha256=-vAlAJdllzBjNJdB_OJn9NOx5gkDaB_sUYZN23Y7xGY,11003
transformers/models/glpn/modeling_glpn.py,sha256=vr8a7Zd9MZmlPRbeWLb6DHjIYcrVYZUfeVQGTe1UCjU,31431
transformers/models/gpt2/__init__.py,sha256=G--pddL9u3r2dd5kVlk_w9W5HCthhUt2w4nQyS6kejc,4406
transformers/models/gpt2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/configuration_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/convert_gpt2_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/modeling_flax_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/modeling_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/modeling_tf_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2_fast.cpython-310.pyc,,
transformers/models/gpt2/__pycache__/tokenization_gpt2_tf.cpython-310.pyc,,
transformers/models/gpt2/configuration_gpt2.py,sha256=Nonqto0eILMcr_OJdvn3sSSEeRMh2EpP9715GyBcrQE,11983
transformers/models/gpt2/convert_gpt2_original_tf_checkpoint_to_pytorch.py,sha256=3qlC-RgCP9x3U-mXQlUD7OEF3q4-b-EF3njeqp7X-wM,2531
transformers/models/gpt2/modeling_flax_gpt2.py,sha256=6vAeL1SwHlYUxTwHmfHXEYLuvTJoLRq5zl_GwUm5PiE,32014
transformers/models/gpt2/modeling_gpt2.py,sha256=nMvog06JuZngjk3cuuZ9lDYmbJVLBlijInPEEIYXKi4,89296
transformers/models/gpt2/modeling_tf_gpt2.py,sha256=fMFJ5OUH4ZQjp9XhXbNeDv-VYGJnp7TkHBj7yiWmRKw,56596
transformers/models/gpt2/tokenization_gpt2.py,sha256=3azHjqgJlRgN4dAem-LWm7GH4Tbid3mG7zYTQKZ1TXo,13425
transformers/models/gpt2/tokenization_gpt2_fast.py,sha256=1B2BWfgmOHCRvYEP5_5jzHsaKZAThwnj1nefJ4HZf7U,6112
transformers/models/gpt2/tokenization_gpt2_tf.py,sha256=Ptg01f1bV0fAvI1JK6v-FE4lVKUPIiXrxxPrf8M7kgU,3833
transformers/models/gpt_bigcode/__init__.py,sha256=lyCKNUBPoNKvoI_J7Q6Bdwk81Iu8aiuEJEpoOXQJN9k,1841
transformers/models/gpt_bigcode/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_bigcode/__pycache__/configuration_gpt_bigcode.cpython-310.pyc,,
transformers/models/gpt_bigcode/__pycache__/modeling_gpt_bigcode.cpython-310.pyc,,
transformers/models/gpt_bigcode/configuration_gpt_bigcode.py,sha256=9NbDfger_JmNbVLgzv7RCMQj5JhZQD75lEVPYYVT0VM,6278
transformers/models/gpt_bigcode/modeling_gpt_bigcode.py,sha256=mFFtG3wNoxz0ks5_Ec6v2AF4PGNoOHfjAw2qvlUTmOU,65734
transformers/models/gpt_neo/__init__.py,sha256=BrgSXDoRNyH8RJzscHXLBvWPB5fTe5EmEWL-8a2ZNxs,2538
transformers/models/gpt_neo/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_neo/__pycache__/configuration_gpt_neo.cpython-310.pyc,,
transformers/models/gpt_neo/__pycache__/convert_gpt_neo_mesh_tf_to_pytorch.cpython-310.pyc,,
transformers/models/gpt_neo/__pycache__/modeling_flax_gpt_neo.cpython-310.pyc,,
transformers/models/gpt_neo/__pycache__/modeling_gpt_neo.cpython-310.pyc,,
transformers/models/gpt_neo/configuration_gpt_neo.py,sha256=0Lm6Ye2G-NP5cPkaKpV8s9Ta4FuxgilWQ8WeBbAnT8c,11831
transformers/models/gpt_neo/convert_gpt_neo_mesh_tf_to_pytorch.py,sha256=qerp3UqKhts8pfHtNNBJ30YpyX_Qsjfc33ubRabrRkU,2588
transformers/models/gpt_neo/modeling_flax_gpt_neo.py,sha256=xgwE5UixFan9wDb9ScOd8DcEH-o1Iu-AX1bNkMWQFEA,28074
transformers/models/gpt_neo/modeling_gpt_neo.py,sha256=haAWlwgLGf5GXsrwmWA0QVCbkPwpIERRy0mp2Np_hf0,53055
transformers/models/gpt_neox/__init__.py,sha256=soLYUXlaucUIDwxsGAQc5xc7XN702tpm3mLHg4FgN3A,2411
transformers/models/gpt_neox/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_neox/__pycache__/configuration_gpt_neox.cpython-310.pyc,,
transformers/models/gpt_neox/__pycache__/modeling_gpt_neox.cpython-310.pyc,,
transformers/models/gpt_neox/__pycache__/tokenization_gpt_neox_fast.cpython-310.pyc,,
transformers/models/gpt_neox/configuration_gpt_neox.py,sha256=XRTslaSfEIsdiCfucUO97-zFlFRBgqbrthQjRpC81OY,8772
transformers/models/gpt_neox/modeling_gpt_neox.py,sha256=SSu-0ZMqLD6xSuCQqj0ieN2OVaD0VkbgtGzAJnJETGg,62315
transformers/models/gpt_neox/tokenization_gpt_neox_fast.py,sha256=ikyshoGxJqCCA3QdbHZgf78aiy8TdJkTqSPzIQLJn98,9789
transformers/models/gpt_neox_japanese/__init__.py,sha256=6JL1l12MGqBc3mWdwXkFAOjBwJnvI4Pay8tKAvtvHxo,1934
transformers/models/gpt_neox_japanese/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/configuration_gpt_neox_japanese.cpython-310.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/modeling_gpt_neox_japanese.cpython-310.pyc,,
transformers/models/gpt_neox_japanese/__pycache__/tokenization_gpt_neox_japanese.cpython-310.pyc,,
transformers/models/gpt_neox_japanese/configuration_gpt_neox_japanese.py,sha256=_yCYYRJCYPRxnoW998HvVxN_ERAH96RO-WdAFrcB_S0,5558
transformers/models/gpt_neox_japanese/modeling_gpt_neox_japanese.py,sha256=-Yn50D3d0iuuF8Ij70t9saFuLcC_k0_XiQ7A0eCa3yc,32276
transformers/models/gpt_neox_japanese/tokenization_gpt_neox_japanese.py,sha256=KLny2d8WH4bGzl6mVUKVvfqUb95l1-ASp_FmZMG92GM,16660
transformers/models/gpt_sw3/__init__.py,sha256=qJj7vF8ES37BwsKbJE1zV2rPUdmM3vx8mckIFuWrJSU,1361
transformers/models/gpt_sw3/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gpt_sw3/__pycache__/convert_megatron_to_pytorch.cpython-310.pyc,,
transformers/models/gpt_sw3/__pycache__/tokenization_gpt_sw3.cpython-310.pyc,,
transformers/models/gpt_sw3/convert_megatron_to_pytorch.py,sha256=CBBDQ0Kb94o1VGiJ2XbdZ4vrPiFkwnaASQXxlGVWwRc,8155
transformers/models/gpt_sw3/tokenization_gpt_sw3.py,sha256=Vv76NxY57VHNGGs2eXzJ5TnB7G74vbcUKOX4JO-qyn4,13094
transformers/models/gptj/__init__.py,sha256=x39m5y1hikryeIRrGQt9e9exsjZp2FQlyQxmJ2y6haU,3112
transformers/models/gptj/__pycache__/__init__.cpython-310.pyc,,
transformers/models/gptj/__pycache__/configuration_gptj.cpython-310.pyc,,
transformers/models/gptj/__pycache__/modeling_flax_gptj.cpython-310.pyc,,
transformers/models/gptj/__pycache__/modeling_gptj.cpython-310.pyc,,
transformers/models/gptj/__pycache__/modeling_tf_gptj.cpython-310.pyc,,
transformers/models/gptj/configuration_gptj.py,sha256=5UHoRlDFy7aPnVObM2A1txoJXmIswU_h9XAffkxRpQk,8784
transformers/models/gptj/modeling_flax_gptj.py,sha256=VaYTrxQosqkIqHcbKcDFinT_z3aofwdJLasWAqxjRlM,28525
transformers/models/gptj/modeling_gptj.py,sha256=71NypmpDs0iLmUClovloD-pCvdvzjA4aWRmkMU5fSlU,57914
transformers/models/gptj/modeling_tf_gptj.py,sha256=qF0_MGa2117_wVtli_uuIZqB6GIIEFkVU2txpwVUnHQ,48087
transformers/models/grounding_dino/__init__.py,sha256=IFa_5LTMMYXVAjNYxHnjSaqMczCg4SONylcFap272nI,2331
transformers/models/grounding_dino/__pycache__/__init__.cpython-310.pyc,,
transformers/models/grounding_dino/__pycache__/configuration_grounding_dino.cpython-310.pyc,,
transformers/models/grounding_dino/__pycache__/convert_grounding_dino_to_hf.cpython-310.pyc,,
transformers/models/grounding_dino/__pycache__/image_processing_grounding_dino.cpython-310.pyc,,
transformers/models/grounding_dino/__pycache__/modeling_grounding_dino.cpython-310.pyc,,
transformers/models/grounding_dino/__pycache__/processing_grounding_dino.cpython-310.pyc,,
transformers/models/grounding_dino/configuration_grounding_dino.py,sha256=FU7rFbSjt643c__xQ50lg5_HeKM5YxSIKKfl-EulIRg,14782
transformers/models/grounding_dino/convert_grounding_dino_to_hf.py,sha256=U3T2-FjtYv9unK36_iA17Ifww7kWWYWpPbVC6u6-unQ,25445
transformers/models/grounding_dino/image_processing_grounding_dino.py,sha256=NB2t45NN-VComePx1OvZUf0yF1wSRTAe7cb7tR9ig3g,70793
transformers/models/grounding_dino/modeling_grounding_dino.py,sha256=jXR4F33jKccA7yeRk2NjyL952lWC6KQ2MNtQbys1Wzk,154747
transformers/models/grounding_dino/processing_grounding_dino.py,sha256=kRjQfahwF3QAdW4_lgVmE1M2S-TX2PgLX-4XjKGL53c,9628
transformers/models/groupvit/__init__.py,sha256=Dcf-QxcWYp2gVxoLrKPdvzHUZPi-XR1Cvni0i9dU2GA,2567
transformers/models/groupvit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/groupvit/__pycache__/configuration_groupvit.cpython-310.pyc,,
transformers/models/groupvit/__pycache__/convert_groupvit_nvlab_to_hf.cpython-310.pyc,,
transformers/models/groupvit/__pycache__/modeling_groupvit.cpython-310.pyc,,
transformers/models/groupvit/__pycache__/modeling_tf_groupvit.cpython-310.pyc,,
transformers/models/groupvit/configuration_groupvit.py,sha256=3k3xxy0YagQHmOgcNgnaBsHb7eOr5UJRXw46sZmhKxM,20696
transformers/models/groupvit/convert_groupvit_nvlab_to_hf.py,sha256=9gQxkcjVNCP5lvV54SbbSsOjkKCHORcoiwq2gcczYCM,9775
transformers/models/groupvit/modeling_groupvit.py,sha256=HmE4Ki97HZIUyHFJAawxKtRFPs-LiniGkRTguCtG9Ok,67760
transformers/models/groupvit/modeling_tf_groupvit.py,sha256=Mv4b1Oh0p0sWbW0ueAL2LihpEIaU1sX_-jWooJ8jDXc,90069
transformers/models/herbert/__init__.py,sha256=Sp9gQIqlUhZHausuaL2MFYDqJW4vvsVGLbVryR-kNl0,1472
transformers/models/herbert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/herbert/__pycache__/tokenization_herbert.cpython-310.pyc,,
transformers/models/herbert/__pycache__/tokenization_herbert_fast.cpython-310.pyc,,
transformers/models/herbert/tokenization_herbert.py,sha256=5xsZeIOJU5UQ1-u75m8c_pOtS5IoFdaMOiL43-dYR5Q,25042
transformers/models/herbert/tokenization_herbert_fast.py,sha256=QITcJycMNQuUIumL_an1mNz55fJADkW9S6jdBmO99KM,5926
transformers/models/hiera/__init__.py,sha256=5roInXycWWxhrz_rALK_Sh7-Ic9gyUKZX3AH7vt-AFI,1720
transformers/models/hiera/__pycache__/__init__.cpython-310.pyc,,
transformers/models/hiera/__pycache__/configuration_hiera.cpython-310.pyc,,
transformers/models/hiera/__pycache__/convert_hiera_to_hf.cpython-310.pyc,,
transformers/models/hiera/__pycache__/modeling_hiera.cpython-310.pyc,,
transformers/models/hiera/configuration_hiera.py,sha256=IIcOJUBI7oYesrr86F1aD35D9fH7XLhcQtYl15Rf7BM,9291
transformers/models/hiera/convert_hiera_to_hf.py,sha256=mFswCFkaqq_0sJJFOGZ2eQEAnF4YijmFJexrY6W9G3c,16620
transformers/models/hiera/modeling_hiera.py,sha256=Ei4RiFVZYKh3HdtLYVSQPS3NMoy9HVUE4RBgTp71-ww,69705
transformers/models/hubert/__init__.py,sha256=6A-uKqqjcmhIYBpcLhnYjAmSGzWTGzN67scpN5p9ruk,2256
transformers/models/hubert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/hubert/__pycache__/configuration_hubert.cpython-310.pyc,,
transformers/models/hubert/__pycache__/convert_distilhubert_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/hubert/__pycache__/convert_hubert_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/hubert/__pycache__/convert_hubert_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/hubert/__pycache__/modeling_hubert.cpython-310.pyc,,
transformers/models/hubert/__pycache__/modeling_tf_hubert.cpython-310.pyc,,
transformers/models/hubert/configuration_hubert.py,sha256=q2Z23TAXdS-GmQq_-RPtO8WZoMUTekXvB6z9goVZ-9c,14675
transformers/models/hubert/convert_distilhubert_original_s3prl_checkpoint_to_pytorch.py,sha256=4EOE_E4BIIbVesPmNCL4bVO5A91wxMpAhfAdOMpTDzg,8941
transformers/models/hubert/convert_hubert_original_pytorch_checkpoint_to_pytorch.py,sha256=0uu-lG5QooIoF0GCZ4Dr0gaTjo6idRqdlVZntMh80-E,10379
transformers/models/hubert/convert_hubert_original_s3prl_checkpoint_to_pytorch.py,sha256=QuwvhsVyqcAXCPQN-peyGc-1C6Gjk6d-ha4Kt5TYF3w,2894
transformers/models/hubert/modeling_hubert.py,sha256=KPBEv_X4bG32jTfpxw0w3xRqIdcDLl_f0yGu1kp3L3g,74005
transformers/models/hubert/modeling_tf_hubert.py,sha256=MFIb8UpsfeuVetYKTfu66dNIA7eJrM-1QGd_sOjcw6s,70700
transformers/models/ibert/__init__.py,sha256=V63S09fVvVVCjY4lMvVL8tHlW6KjrOtyCthRjsLxehI,1914
transformers/models/ibert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/ibert/__pycache__/configuration_ibert.cpython-310.pyc,,
transformers/models/ibert/__pycache__/modeling_ibert.cpython-310.pyc,,
transformers/models/ibert/__pycache__/quant_modules.cpython-310.pyc,,
transformers/models/ibert/configuration_ibert.py,sha256=fFf0tHcXK3Cbp6BYOc96slvvxSPfKn7PaRrYodG-hQI,7047
transformers/models/ibert/modeling_ibert.py,sha256=BcdnNitBtU2fVoudiXpMwROuQWJb2OvTSwGxDr8GeKI,56881
transformers/models/ibert/quant_modules.py,sha256=ItU76CIx0XcZCPOR21dz99J9k5rK2fzffQz0jJCuNmM,30072
transformers/models/idefics/__init__.py,sha256=Xr43c6otTmkh8HlSFETZFaEO21Ocjkw_hc3JZlw4Kng,2834
transformers/models/idefics/__pycache__/__init__.cpython-310.pyc,,
transformers/models/idefics/__pycache__/configuration_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/image_processing_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/modeling_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/modeling_tf_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/perceiver.cpython-310.pyc,,
transformers/models/idefics/__pycache__/perceiver_tf.cpython-310.pyc,,
transformers/models/idefics/__pycache__/processing_idefics.cpython-310.pyc,,
transformers/models/idefics/__pycache__/vision.cpython-310.pyc,,
transformers/models/idefics/__pycache__/vision_tf.cpython-310.pyc,,
transformers/models/idefics/configuration_idefics.py,sha256=ncVIg3OCqZFS3bU4lJXPDEAM_QvTZipSbfxIYOsMAY4,15366
transformers/models/idefics/image_processing_idefics.py,sha256=0_fj1Kf8WNyWohSeuCIJHP6jddHaCfzb2LIlExbOjvU,7764
transformers/models/idefics/modeling_idefics.py,sha256=d-0NrWmxSjzfXuwt9iS5DFzIm7K7neSK83JA9QpxjXU,73124
transformers/models/idefics/modeling_tf_idefics.py,sha256=jQZxhqTtEXfwn8yZPiHIMqzz9QlkcmrASXtIK_jtt_w,80221
transformers/models/idefics/perceiver.py,sha256=uGv8FH2wZ-NO1EIaFclI1nkwUqaTA7i0PS9XxY7ivn0,9433
transformers/models/idefics/perceiver_tf.py,sha256=rYqXv9j6bmr4NyZLAV1MhVMiiIMV7RZ9CafybPtYc9I,10006
transformers/models/idefics/processing_idefics.py,sha256=5mu-3GKalu58pVxiixV2eE4-GmHBGOX7wGOhQHnblug,22592
transformers/models/idefics/vision.py,sha256=EVQ5lOtdV00gK_3TAuLI4zUeHbw4zV1RdZNXZqUXXiQ,22493
transformers/models/idefics/vision_tf.py,sha256=Kf_PenRY1vhlBA62PvjdvUDyQTKIi30XqB_bMBN1Mrw,26010
transformers/models/idefics2/__init__.py,sha256=qQHmSTElW4-t5lQVlzJJUO-m9Sr2RWfsRTp6V0YewAk,2213
transformers/models/idefics2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/idefics2/__pycache__/configuration_idefics2.cpython-310.pyc,,
transformers/models/idefics2/__pycache__/convert_idefics2_weights_to_hf.cpython-310.pyc,,
transformers/models/idefics2/__pycache__/image_processing_idefics2.cpython-310.pyc,,
transformers/models/idefics2/__pycache__/modeling_idefics2.cpython-310.pyc,,
transformers/models/idefics2/__pycache__/processing_idefics2.cpython-310.pyc,,
transformers/models/idefics2/configuration_idefics2.py,sha256=Zc2TYclmia19Xza5iID33BlGy6TKjT2U4qWh_CvnxK8,11809
transformers/models/idefics2/convert_idefics2_weights_to_hf.py,sha256=3nd_V1qNTv7DehZZQLLAeKh0086xvjECNuWBvJmFbNM,6669
transformers/models/idefics2/image_processing_idefics2.py,sha256=RAJpeMN3iNaL4YJtV_6ATVYlva5h_pVFR_bRgHL3MWY,27422
transformers/models/idefics2/modeling_idefics2.py,sha256=4L4NU0HYeoz0xY6f2biuvUV1QVHTLmq4V8HTe89xEO4,82131
transformers/models/idefics2/processing_idefics2.py,sha256=UunvM7LaNk4LqBHdiUd1Q0JoZ35p0ewpIJ_HqBIC-AM,14637
transformers/models/imagegpt/__init__.py,sha256=QdFa7OkvADEoPElX5MqjuRX1wzKmGpPdsHp-E6Gn3Lw,2468
transformers/models/imagegpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/configuration_imagegpt.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/convert_imagegpt_original_tf2_to_pytorch.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/feature_extraction_imagegpt.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/image_processing_imagegpt.cpython-310.pyc,,
transformers/models/imagegpt/__pycache__/modeling_imagegpt.cpython-310.pyc,,
transformers/models/imagegpt/configuration_imagegpt.py,sha256=eAa9tRieq02IuqLO3O2uGsttkU_hdXVBf6oG2tkA56A,8719
transformers/models/imagegpt/convert_imagegpt_original_tf2_to_pytorch.py,sha256=BMqKNGn1Jv4rntP9fG5C0yG_lF1MY_0h9yv0Qt4rjpM,2690
transformers/models/imagegpt/feature_extraction_imagegpt.py,sha256=iCpQ4tU3Vml44KgO43kYJvv-RcZVxe8tc794gxUktuU,1200
transformers/models/imagegpt/image_processing_imagegpt.py,sha256=UH8YSyNGl4jI4rrPb0HrjbPnKp3PSlykBCY4vdGhjA0,14692
transformers/models/imagegpt/modeling_imagegpt.py,sha256=tjGDLDqZBQwSLYLlT_THqmc-tGIxpKHY_4-UrJ-dVnc,53664
transformers/models/informer/__init__.py,sha256=v8NQJEtKbrVWXQZa7urA_8qDWVoGSn5bJNhIPYOxYNQ,1650
transformers/models/informer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/informer/__pycache__/configuration_informer.cpython-310.pyc,,
transformers/models/informer/__pycache__/modeling_informer.cpython-310.pyc,,
transformers/models/informer/configuration_informer.py,sha256=yWIqTlCyX0QcQRNuqktsfoSr2EHss9fe4Vf6yM7zOvg,12412
transformers/models/informer/modeling_informer.py,sha256=3X4IPVrOJohAPshisSSCukjPQryYSIjb2Sic1KV-paI,101496
transformers/models/instructblip/__init__.py,sha256=HjvAHdrlmHFfBe-bHP_UOOgegMc4BsHLw5SNu8hFLtQ,2063
transformers/models/instructblip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/instructblip/__pycache__/configuration_instructblip.cpython-310.pyc,,
transformers/models/instructblip/__pycache__/convert_instructblip_original_to_pytorch.cpython-310.pyc,,
transformers/models/instructblip/__pycache__/modeling_instructblip.cpython-310.pyc,,
transformers/models/instructblip/__pycache__/processing_instructblip.cpython-310.pyc,,
transformers/models/instructblip/configuration_instructblip.py,sha256=Ku29pNKqoIbxX7wxisSL93ABP5z1kaIlNnBXc4Xyk7s,17169
transformers/models/instructblip/convert_instructblip_original_to_pytorch.py,sha256=iustpBsjHHzjQzbAhPJvhI7ZBSXCDoa9njtK9m_gm_I,13399
transformers/models/instructblip/modeling_instructblip.py,sha256=F_XkCtYtqtMhkAQEhSDN0-KGxcEbBXth4BUGwMrBZ3w,74075
transformers/models/instructblip/processing_instructblip.py,sha256=ZGPNtYo7vGdZK_bW5QeHgTDRO99CcBJBhVik7Pr2OlI,8092
transformers/models/instructblipvideo/__init__.py,sha256=sTOPrPaq8f-igvxw5Bd9Tu9_bPMDDg2fmHk9sjjLpw0,2688
transformers/models/instructblipvideo/__pycache__/__init__.cpython-310.pyc,,
transformers/models/instructblipvideo/__pycache__/configuration_instructblipvideo.cpython-310.pyc,,
transformers/models/instructblipvideo/__pycache__/convert_instructblipvideo_original_to_pytorch.cpython-310.pyc,,
transformers/models/instructblipvideo/__pycache__/diff_instructblipvideo.cpython-310.pyc,,
transformers/models/instructblipvideo/__pycache__/image_processing_instructblipvideo.cpython-310.pyc,,
transformers/models/instructblipvideo/__pycache__/modeling_instructblipvideo.cpython-310.pyc,,
transformers/models/instructblipvideo/__pycache__/processing_instructblipvideo.cpython-310.pyc,,
transformers/models/instructblipvideo/configuration_instructblipvideo.py,sha256=72kR7yUxo4XeFmVpj2QQpl33qWsEOUbRrgPqfXJR0ko,18150
transformers/models/instructblipvideo/convert_instructblipvideo_original_to_pytorch.py,sha256=F69HaLiFYx7EyneqAjLG7q5jiPEy0bMTF8Q1qF2h7d4,13508
transformers/models/instructblipvideo/diff_instructblipvideo.py,sha256=uWmaRGXp4GMRBKoJ2_WjlHdlO-p14P4LVx536XcmYp8,18360
transformers/models/instructblipvideo/image_processing_instructblipvideo.py,sha256=g60lbKpn70vKy9xzdtVZy-j3XlwSMuNQwttRAnHn6z8,17738
transformers/models/instructblipvideo/modeling_instructblipvideo.py,sha256=WtpSPT8GRI2rrRBr3uQaX6AaW30CUeLQsMxxF3O0RzM,77133
transformers/models/instructblipvideo/processing_instructblipvideo.py,sha256=674i2nIxoh7PLvjHYYnHiC82MuxpHDslJAsvGrZXvrU,8096
transformers/models/jamba/__init__.py,sha256=aD1sOCM0Rjk2I3Zh_fEh3xeot9EVYE3X3nKi8rP-KyI,1661
transformers/models/jamba/__pycache__/__init__.cpython-310.pyc,,
transformers/models/jamba/__pycache__/configuration_jamba.cpython-310.pyc,,
transformers/models/jamba/__pycache__/modeling_jamba.cpython-310.pyc,,
transformers/models/jamba/configuration_jamba.py,sha256=UK7ToBmUbfLGIrVdb86RRcL78V_O4n7rz0tJDp0H2Ps,11250
transformers/models/jamba/modeling_jamba.py,sha256=_f09c5VFcErHCaYn2nmly2wSTUZhB4426L0y0lPqoZw,81362
transformers/models/jetmoe/__init__.py,sha256=PQ_qABkj9KXL5ALjWoHuYScccKDv_a05cEP0aN-vEM0,1692
transformers/models/jetmoe/__pycache__/__init__.cpython-310.pyc,,
transformers/models/jetmoe/__pycache__/configuration_jetmoe.cpython-310.pyc,,
transformers/models/jetmoe/__pycache__/modeling_jetmoe.cpython-310.pyc,,
transformers/models/jetmoe/configuration_jetmoe.py,sha256=xIqVx4vO25zQkBb5h77BSRvwG2jx0oTwDvEKeIaEUiw,6775
transformers/models/jetmoe/modeling_jetmoe.py,sha256=xmT9IHI7gSdZ3idHX9DrJEatbRY6H3sE4M7cXnZTHF8,65929
transformers/models/kosmos2/__init__.py,sha256=l-fM--wymMrVutXFv4V5xF7CKmLkiGFccADN9bhcsLU,1787
transformers/models/kosmos2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/kosmos2/__pycache__/configuration_kosmos2.cpython-310.pyc,,
transformers/models/kosmos2/__pycache__/convert_kosmos2_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/kosmos2/__pycache__/modeling_kosmos2.cpython-310.pyc,,
transformers/models/kosmos2/__pycache__/processing_kosmos2.cpython-310.pyc,,
transformers/models/kosmos2/configuration_kosmos2.py,sha256=AczOUgyNRdGvUympqJQK4GyBJjac-4xZU8B-kD9LyuI,13219
transformers/models/kosmos2/convert_kosmos2_original_pytorch_checkpoint_to_pytorch.py,sha256=3ejv6hUd6irzFnmSuFVI6Eu1NVWmtJf3_ql2h9P4AHk,2724
transformers/models/kosmos2/modeling_kosmos2.py,sha256=lbb1v5-ITek4Cg3gTLhjeN0IF4E0Z8LO9gxdCQIAl5E,94903
transformers/models/kosmos2/processing_kosmos2.py,sha256=gJMjfO4zX-13eDLeUioEVfxK78paABe-G12jiwnnka0,29815
transformers/models/layoutlm/__init__.py,sha256=ToNTDJFtx8GiUufvjVOwBGLBVszbtuPwQRVw0GyZz7o,3495
transformers/models/layoutlm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/configuration_layoutlm.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/modeling_layoutlm.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/modeling_tf_layoutlm.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/tokenization_layoutlm.cpython-310.pyc,,
transformers/models/layoutlm/__pycache__/tokenization_layoutlm_fast.cpython-310.pyc,,
transformers/models/layoutlm/configuration_layoutlm.py,sha256=Lk-F-JN7G4cGWl4LOjED8-5qauxEmqvQqNtLq_kXsn4,9081
transformers/models/layoutlm/modeling_layoutlm.py,sha256=J4pDOQtDslQ6b6w6tz5gxQF4DUeKXWvsqEAruPPPdD4,61013
transformers/models/layoutlm/modeling_tf_layoutlm.py,sha256=skvosKy_iLHtFzQf5-NK3ONSZs3C8TJJ2xpVxV2cyiI,73116
transformers/models/layoutlm/tokenization_layoutlm.py,sha256=RRwLh_WYHbYnaAWAd_De1KCqPUgYrxCyj0CM9PUkTSE,20942
transformers/models/layoutlm/tokenization_layoutlm_fast.py,sha256=eUfhfNdwyBP6YvV0Jm9UVsHP-6PAVzFavgeCOYqdgTY,7786
transformers/models/layoutlmv2/__init__.py,sha256=HbfFYtc7N47btSqz6mHBI8ztlHUtiF2g3L5AR4ECAVk,3247
transformers/models/layoutlmv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/configuration_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/feature_extraction_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/image_processing_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/modeling_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/processing_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/tokenization_layoutlmv2.cpython-310.pyc,,
transformers/models/layoutlmv2/__pycache__/tokenization_layoutlmv2_fast.cpython-310.pyc,,
transformers/models/layoutlmv2/configuration_layoutlmv2.py,sha256=0aIGCavEpz0r9tGScL4ocX2FbZ5T7NQqYGrjRITA_js,10881
transformers/models/layoutlmv2/feature_extraction_layoutlmv2.py,sha256=M9bDCpKBLI5paxor4ioa2JjEDhSH9Np-PTbgHh2V9KI,1195
transformers/models/layoutlmv2/image_processing_layoutlmv2.py,sha256=yV8J93JD6AR2chy87LDh2zvl5N60MvDEreNEzaI211Y,13809
transformers/models/layoutlmv2/modeling_layoutlmv2.py,sha256=bIEnl8Z0mpNHd2m611Shnu0uF1PsdS87rBb0LtLz-0g,61911
transformers/models/layoutlmv2/processing_layoutlmv2.py,sha256=xyhBq9pYYmNYOfK2c13gA-f1cWzu1fp0kO6FC7J9DfI,9292
transformers/models/layoutlmv2/tokenization_layoutlmv2.py,sha256=ZpOPGao8BUSVPrIjNW16HRM_GkgJCXgS-VrSuW9zjmg,72063
transformers/models/layoutlmv2/tokenization_layoutlmv2_fast.py,sha256=jEDcU7MIeieUnorFQQ9S-DYPbyqpaooQt5KRexDzxB8,37284
transformers/models/layoutlmv3/__init__.py,sha256=7LXVZdSdhLshkjwfXi0cNhsgrhFsenwN44DLzWZUq3Q,4192
transformers/models/layoutlmv3/__pycache__/__init__.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/configuration_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/feature_extraction_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/image_processing_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/modeling_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/modeling_tf_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/processing_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/tokenization_layoutlmv3.cpython-310.pyc,,
transformers/models/layoutlmv3/__pycache__/tokenization_layoutlmv3_fast.cpython-310.pyc,,
transformers/models/layoutlmv3/configuration_layoutlmv3.py,sha256=nMiSp7O0bCmu6dKdZsd2ITIsaf8nFGRTMJOZcXN--J8,13204
transformers/models/layoutlmv3/feature_extraction_layoutlmv3.py,sha256=jWsmsi2mym0meek1lHWqfqxlJgMJdY3cgfQ_4ASEbto,1195
transformers/models/layoutlmv3/image_processing_layoutlmv3.py,sha256=3zmcx39HvcXzHJeI70U0Jo2e6fkpUUorArlXlDHX-ow,18813
transformers/models/layoutlmv3/modeling_layoutlmv3.py,sha256=Njg7PzstKbLybpn7eYTgN0pkoVRVp_QuI8rclzpME5o,60391
transformers/models/layoutlmv3/modeling_tf_layoutlmv3.py,sha256=S1z_MxVYQMuj_IxTf0_45fIoYnTEDhbXaP7gx3OnCYs,76775
transformers/models/layoutlmv3/processing_layoutlmv3.py,sha256=ShtvBmZjGHbprdB14v2QsIgVir-74gEnTGHzvL31vCI,9143
transformers/models/layoutlmv3/tokenization_layoutlmv3.py,sha256=UrCNLze0Z_6WX5KJ-qYxaAfEzOZfEofjx2Ky5hCiC_8,72055
transformers/models/layoutlmv3/tokenization_layoutlmv3_fast.py,sha256=36lBDAt8h1qmAI2ACWUODe2Umcn4RWxSzl2MBmPtlnQ,39532
transformers/models/layoutxlm/__init__.py,sha256=AIvjzuqRPFXFuWXxnOlp9pBXaIT5Zzx7fwtg2KKVETA,2037
transformers/models/layoutxlm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/layoutxlm/__pycache__/processing_layoutxlm.cpython-310.pyc,,
transformers/models/layoutxlm/__pycache__/tokenization_layoutxlm.cpython-310.pyc,,
transformers/models/layoutxlm/__pycache__/tokenization_layoutxlm_fast.cpython-310.pyc,,
transformers/models/layoutxlm/processing_layoutxlm.py,sha256=BCF0b56oTulabhz45Z8wXWl5EXVRbeI-ypoE49PYz7k,9243
transformers/models/layoutxlm/tokenization_layoutxlm.py,sha256=VOwN2dPPDhTbbA_1NXDI9txyGyeZ13eDJH7KBzfIAd0,57297
transformers/models/layoutxlm/tokenization_layoutxlm_fast.py,sha256=K2w7WaCAHqzSRU45m6_P7BXFLDr9FosWMj3xOtBz8Is,39913
transformers/models/led/__init__.py,sha256=6B96iGVauRaXeU-4rEOxQVdFcNHoTKm0Zqk_BjTzDl0,2844
transformers/models/led/__pycache__/__init__.cpython-310.pyc,,
transformers/models/led/__pycache__/configuration_led.cpython-310.pyc,,
transformers/models/led/__pycache__/modeling_led.cpython-310.pyc,,
transformers/models/led/__pycache__/modeling_tf_led.cpython-310.pyc,,
transformers/models/led/__pycache__/tokenization_led.cpython-310.pyc,,
transformers/models/led/__pycache__/tokenization_led_fast.cpython-310.pyc,,
transformers/models/led/configuration_led.py,sha256=_YJsNOVeRtKJSAffbitjohX6ZdLiXirBLRmZOT2XKDM,7419
transformers/models/led/modeling_led.py,sha256=O_lkYqD_dFAZkjHedeBn2Nlzc-7JwALlXGEajBYJ9jo,139056
transformers/models/led/modeling_tf_led.py,sha256=MZjMSGc0K7qe1DatVfwzpxynUxzwW3kNvcWVVs5yUvA,123070
transformers/models/led/tokenization_led.py,sha256=H2B8JdOoxg6O0a_ul477ToPDDBmoUfiPGsg7zwFfe7U,19752
transformers/models/led/tokenization_led_fast.py,sha256=oaeDl37DL_c_RzX1XbplbdQLT5F85m_cprAQ_8QQRHc,14542
transformers/models/levit/__init__.py,sha256=LNIUEn_GOEZQo0sP4HZFMu2s2ZRnbXLfq8Q2Reqhdt8,2336
transformers/models/levit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/levit/__pycache__/configuration_levit.cpython-310.pyc,,
transformers/models/levit/__pycache__/convert_levit_timm_to_pytorch.cpython-310.pyc,,
transformers/models/levit/__pycache__/feature_extraction_levit.cpython-310.pyc,,
transformers/models/levit/__pycache__/image_processing_levit.cpython-310.pyc,,
transformers/models/levit/__pycache__/modeling_levit.cpython-310.pyc,,
transformers/models/levit/configuration_levit.py,sha256=EnmjmpzPJ22bPbKqI1kGvtk_R7lVE7rxz-r152Txlq4,5716
transformers/models/levit/convert_levit_timm_to_pytorch.py,sha256=TN87M03CQV4LRb0jH0SCNTtfoZ8rGPvbLgykEC14kLE,6257
transformers/models/levit/feature_extraction_levit.py,sha256=l2RHbrbg9MzRqKr_ErOo_AuiSv93Gj-Oq6w0v2p-Izw,1204
transformers/models/levit/image_processing_levit.py,sha256=CD7HBX2SVeEV9eF6E3hvh6-Y051LSjvpkjy4Y8QUO3Q,17058
transformers/models/levit/modeling_levit.py,sha256=gAyS5fxB2MW1APxIFbfmE42kKSDpLspHBVNUcnwShDs,29365
transformers/models/lilt/__init__.py,sha256=qGFJxFObaHb-jxb0fwz0GBNczbCUlO4kQh1u2huUW-Q,1741
transformers/models/lilt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/lilt/__pycache__/configuration_lilt.cpython-310.pyc,,
transformers/models/lilt/__pycache__/modeling_lilt.cpython-310.pyc,,
transformers/models/lilt/configuration_lilt.py,sha256=BCiHtpFR6NmEOJxZ8wWIvqzD9XE-vwaiDuc16Qw4guQ,6694
transformers/models/lilt/modeling_lilt.py,sha256=zinA1GM1fu9wYIsxvIiu7244fN7VfQ_UypCgmnp4aes,52704
transformers/models/llama/__init__.py,sha256=DYt0fKALZE7Q6l5HUhOp0FRWxtjU8DGNDIDIHmYY-CI,3353
transformers/models/llama/__pycache__/__init__.cpython-310.pyc,,
transformers/models/llama/__pycache__/configuration_llama.cpython-310.pyc,,
transformers/models/llama/__pycache__/convert_llama_weights_to_hf.cpython-310.pyc,,
transformers/models/llama/__pycache__/modeling_flax_llama.cpython-310.pyc,,
transformers/models/llama/__pycache__/modeling_llama.cpython-310.pyc,,
transformers/models/llama/__pycache__/tokenization_llama.cpython-310.pyc,,
transformers/models/llama/__pycache__/tokenization_llama_fast.cpython-310.pyc,,
transformers/models/llama/configuration_llama.py,sha256=C5O3r6ck6FjOBzsJuMIOQZkIz5-fbuo8qdD9Hk9xcgM,11037
transformers/models/llama/convert_llama_weights_to_hf.py,sha256=GQKPF6YMbsoVUQiO8-On5kf5y5ay-G93xNEPh7i2UpA,18700
transformers/models/llama/modeling_flax_llama.py,sha256=We2ACGpzoMuqq2B2lY83qCZk6tKajVaf-k2aeXxrBss,30832
transformers/models/llama/modeling_llama.py,sha256=9FSWR-bjwM1ffIaewWxsgvMYdg039X1w9aU6-sWzAu0,72022
transformers/models/llama/tokenization_llama.py,sha256=-TkRHsPHVErlD2JTSXXsI2LQDueXj0Kuir28pejbkn4,21883
transformers/models/llama/tokenization_llama_fast.py,sha256=OpVCBy_A1OohKXKnXaYrhAO0nJdAKVRuf3pfB8tDiMQ,14469
transformers/models/llava/__init__.py,sha256=1KBPz09g-qQ9VaNmfhS_sYps51avbl42MEmbsgcacCg,1625
transformers/models/llava/__pycache__/__init__.cpython-310.pyc,,
transformers/models/llava/__pycache__/configuration_llava.cpython-310.pyc,,
transformers/models/llava/__pycache__/convert_llava_weights_to_hf.cpython-310.pyc,,
transformers/models/llava/__pycache__/modeling_llava.cpython-310.pyc,,
transformers/models/llava/__pycache__/processing_llava.cpython-310.pyc,,
transformers/models/llava/configuration_llava.py,sha256=-JhgYYXggFjBOtaRfkKjlvoOWWWyN2J6EIh3ojhBuj8,5261
transformers/models/llava/convert_llava_weights_to_hf.py,sha256=UKi6SAOGbu8RbcjE6rT1P53l25zhg_hQ79M0MPQQdns,7559
transformers/models/llava/modeling_llava.py,sha256=CIIjruAvyRtmzY6pxOzZddBHQ34-9JJSFQm6H4Aed9w,29750
transformers/models/llava/processing_llava.py,sha256=XaHukD1xy851fdyrUQgr1Pwf9dx8GVrHPiSP_hYdGzU,7403
transformers/models/llava_next/__init__.py,sha256=t1b-q3rCJUMLT_V3kdOw8_kcpmYfV4LFncLAzIuV574,2171
transformers/models/llava_next/__pycache__/__init__.cpython-310.pyc,,
transformers/models/llava_next/__pycache__/configuration_llava_next.cpython-310.pyc,,
transformers/models/llava_next/__pycache__/convert_llava_next_weights_to_hf.cpython-310.pyc,,
transformers/models/llava_next/__pycache__/image_processing_llava_next.cpython-310.pyc,,
transformers/models/llava_next/__pycache__/modeling_llava_next.cpython-310.pyc,,
transformers/models/llava_next/__pycache__/processing_llava_next.cpython-310.pyc,,
transformers/models/llava_next/configuration_llava_next.py,sha256=xx-qnEmXbxpZP50EZh_aeYxT-hmVAQxNfO0GShnSMwQ,6273
transformers/models/llava_next/convert_llava_next_weights_to_hf.py,sha256=WpC-jvY4TtlZGpW9MCI82HUIcQl9lpsUZT_cmzfJqfU,20785
transformers/models/llava_next/image_processing_llava_next.py,sha256=oOEcHJjGL_kbIfIjV-8qcG56k8DCWEQKGZvcFHy_XEA,36430
transformers/models/llava_next/modeling_llava_next.py,sha256=7-p3R6bzjJAa26GSlrkh2e6aA3_vNSMGdeyZMDyZBsE,49418
transformers/models/llava_next/processing_llava_next.py,sha256=H-F3hdpM4x1V9H8ikhB8xK4GXNVoQVFeMl2znqjPGy8,7785
transformers/models/llava_next_video/__init__.py,sha256=kx1mBbGJWuj8mrHXgCiLuSK4soIoVH2hDE2qFINonqg,2267
transformers/models/llava_next_video/__pycache__/__init__.cpython-310.pyc,,
transformers/models/llava_next_video/__pycache__/configuration_llava_next_video.cpython-310.pyc,,
transformers/models/llava_next_video/__pycache__/convert_llava_next_video_weights_to_hf.cpython-310.pyc,,
transformers/models/llava_next_video/__pycache__/diff_llava_next_video.cpython-310.pyc,,
transformers/models/llava_next_video/__pycache__/image_processing_llava_next_video.cpython-310.pyc,,
transformers/models/llava_next_video/__pycache__/modeling_llava_next_video.cpython-310.pyc,,
transformers/models/llava_next_video/__pycache__/processing_llava_next_video.cpython-310.pyc,,
transformers/models/llava_next_video/configuration_llava_next_video.py,sha256=vvy1Sdxi0HMbDlq7gWog0F07-oW2tynNfwgdcDQGzpI,7379
transformers/models/llava_next_video/convert_llava_next_video_weights_to_hf.py,sha256=-2knTp91EFTMB3vNXPGoaIW9u1s7yAXv1XySmGVTpJo,10511
transformers/models/llava_next_video/diff_llava_next_video.py,sha256=SP6H7bNIhdoCZRR1DmwWK2Ph_gxOGpfjlvz9cTzJS5Q,27093
transformers/models/llava_next_video/image_processing_llava_next_video.py,sha256=WlNLhJmEMEW5raRg-3W_XwCnUVqKMdQ3nr_1Iilxh7o,21509
transformers/models/llava_next_video/modeling_llava_next_video.py,sha256=Nu7muwY-ONrfoT487SeBDIAfD4Lu1jGHvixGRjqajJQ,56721
transformers/models/llava_next_video/processing_llava_next_video.py,sha256=RMgpcgNFjim1bJygb2OZ9rtoNPeX-kRgQHfNk-MdqZI,10929
transformers/models/longformer/__init__.py,sha256=1lCNrBs3duWbx51kKZTB4afLXTiF1QTxK0a2wXiHkB4,3876
transformers/models/longformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/longformer/__pycache__/configuration_longformer.cpython-310.pyc,,
transformers/models/longformer/__pycache__/convert_longformer_original_pytorch_lightning_to_pytorch.cpython-310.pyc,,
transformers/models/longformer/__pycache__/modeling_longformer.cpython-310.pyc,,
transformers/models/longformer/__pycache__/modeling_tf_longformer.cpython-310.pyc,,
transformers/models/longformer/__pycache__/tokenization_longformer.cpython-310.pyc,,
transformers/models/longformer/__pycache__/tokenization_longformer_fast.cpython-310.pyc,,
transformers/models/longformer/configuration_longformer.py,sha256=s_l49e6imnOlCPkqcqiJMFabklp-O0pGlIYtcfdssqA,8765
transformers/models/longformer/convert_longformer_original_pytorch_lightning_to_pytorch.py,sha256=bt-0zsqIGsD8WONirTLHw44ZjIJf-NvMXlLwIg5rqJk,3025
transformers/models/longformer/modeling_longformer.py,sha256=0rIpRUjtDNuoDXzCmHefMW53YixNFDv6RQbKkTazkgo,113826
transformers/models/longformer/modeling_tf_longformer.py,sha256=MlZVpQCdEG6IYX1AtNDAmQprWrVWzfWKL5w-KvXEYhI,129344
transformers/models/longformer/tokenization_longformer.py,sha256=t64oV7_8WJMD8b2uRr1SH9OaBPddZXBxAKsWbHpGCrk,16797
transformers/models/longformer/tokenization_longformer_fast.py,sha256=PS7I9o5WPEyTCANiRk0WHUcOhKDyc5woV-GBgiX9Lec,11672
transformers/models/longt5/__init__.py,sha256=XovR-KglV-9y3YQZ2uPJmU3TXgcCkAweVSAtg6E0zXE,2370
transformers/models/longt5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/longt5/__pycache__/configuration_longt5.cpython-310.pyc,,
transformers/models/longt5/__pycache__/convert_longt5x_checkpoint_to_flax.cpython-310.pyc,,
transformers/models/longt5/__pycache__/modeling_flax_longt5.cpython-310.pyc,,
transformers/models/longt5/__pycache__/modeling_longt5.cpython-310.pyc,,
transformers/models/longt5/configuration_longt5.py,sha256=NAwd6oMuOYqZkyx8_UNIEi_R3mEV8Zy1pcr1iV_JlwM,7999
transformers/models/longt5/convert_longt5x_checkpoint_to_flax.py,sha256=att5rZjP3_mlIRv1icJO3wxQxuwFYEvCz4bFr2seW4k,11091
transformers/models/longt5/modeling_flax_longt5.py,sha256=7vhDWHBGIt2v5x9waY_4gV84TxkqFhKOmYjBy8Wij7c,105670
transformers/models/longt5/modeling_longt5.py,sha256=_rBgLQU6BIFJyCRaR4dv6QC8Bu_th477ugXOOW-hLe8,105936
transformers/models/luke/__init__.py,sha256=S3a0nDUUxdNNm_hpHGmseMc2r25-8WTlCQPsyCYjLeA,2215
transformers/models/luke/__pycache__/__init__.cpython-310.pyc,,
transformers/models/luke/__pycache__/configuration_luke.cpython-310.pyc,,
transformers/models/luke/__pycache__/convert_luke_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/luke/__pycache__/modeling_luke.cpython-310.pyc,,
transformers/models/luke/__pycache__/tokenization_luke.cpython-310.pyc,,
transformers/models/luke/configuration_luke.py,sha256=D1UhPDtRfrXnbLTJFGb6iamTjvFYMLEdUfVK03SuQiM,6593
transformers/models/luke/convert_luke_original_pytorch_checkpoint_to_pytorch.py,sha256=pfnDfBvJDRyCLBLdcsalZaKV01aEz0W1og2Z364hTDs,7467
transformers/models/luke/modeling_luke.py,sha256=tbTMuJxuxQoWVFpsru4lrNl3L0Csa7V_VcxQODwM8k0,103762
transformers/models/luke/tokenization_luke.py,sha256=t0MGsfhovYFseHE3bp_BX3yjJ-C59AnJXaFscO2mmDo,84405
transformers/models/lxmert/__init__.py,sha256=PXJlmgyBlzdmyVm2O6-kTfcFuUNYIog7i2bIl5yNp_k,3214
transformers/models/lxmert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/configuration_lxmert.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/convert_lxmert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/modeling_lxmert.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/modeling_tf_lxmert.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/tokenization_lxmert.cpython-310.pyc,,
transformers/models/lxmert/__pycache__/tokenization_lxmert_fast.cpython-310.pyc,,
transformers/models/lxmert/configuration_lxmert.py,sha256=Oiv-8r6fTsvXG2pnGP-zs-XhJhfN63mjqQNT4gBpwXk,8905
transformers/models/lxmert/convert_lxmert_original_tf_checkpoint_to_pytorch.py,sha256=Ps5iNo91Yj3XLkEuXrP2KFLSjWhYJyP-1vtqMyt1Lqk,2108
transformers/models/lxmert/modeling_lxmert.py,sha256=YLR9O8ruS-R1f2UlIqgoEnRIvIOgb8PVy8nt28xIdS4,65842
transformers/models/lxmert/modeling_tf_lxmert.py,sha256=XGMaLwj3fGRBrxbthJsFVYFrNOOxbMPuxHoHHmaFLRc,72618
transformers/models/lxmert/tokenization_lxmert.py,sha256=0Se6v8_PKzz7B5twxG-as9qvq_VFNgd-PFU7WhJmDeM,20966
transformers/models/lxmert/tokenization_lxmert_fast.py,sha256=-mHMI4WCLJoXt8nYvJRibyOYfJKByPW_a6nQV_L11PM,7720
transformers/models/m2m_100/__init__.py,sha256=64Jndx6My4y-BYuQtMonbig8Rc3iFC777z--DgHXITw,1812
transformers/models/m2m_100/__pycache__/__init__.cpython-310.pyc,,
transformers/models/m2m_100/__pycache__/configuration_m2m_100.cpython-310.pyc,,
transformers/models/m2m_100/__pycache__/convert_m2m100_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/m2m_100/__pycache__/modeling_m2m_100.cpython-310.pyc,,
transformers/models/m2m_100/__pycache__/tokenization_m2m_100.cpython-310.pyc,,
transformers/models/m2m_100/configuration_m2m_100.py,sha256=oxz9d5PZiwRdOrswXdjANddnOaaQwKUAM9ByOP96cfE,13362
transformers/models/m2m_100/convert_m2m100_original_checkpoint_to_pytorch.py,sha256=xNG8NE20odOve8Z1zKPDHJr5Ev8jM30N-mJsJqfsXtM,3159
transformers/models/m2m_100/modeling_m2m_100.py,sha256=1W9ZxFq_nt1yhZC-k5Wspd7sc3XD3HK8QCnbq0B5uDM,70954
transformers/models/m2m_100/tokenization_m2m_100.py,sha256=CTRnlsxgTpS3KVUDFH_D4C4UsgG2cuQfie2lHzvvGrk,16321
transformers/models/mamba/__init__.py,sha256=xue7XUej0GVyK7yZrP58iMYuJYYUTvmTAu_pAQIqn2U,1626
transformers/models/mamba/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mamba/__pycache__/configuration_mamba.cpython-310.pyc,,
transformers/models/mamba/__pycache__/convert_mamba_ssm_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mamba/__pycache__/modeling_mamba.cpython-310.pyc,,
transformers/models/mamba/configuration_mamba.py,sha256=TEkcOBRKZxKgEQb3PQe1pqPKyx3jRLt0aW03c-TzqDA,7404
transformers/models/mamba/convert_mamba_ssm_checkpoint_to_pytorch.py,sha256=BK6M1tYEwLwoM7NE3fdm0BRpZH8LvCTGvdvsee9taZA,6454
transformers/models/mamba/modeling_mamba.py,sha256=JMMtvZ7HqRi3dlyWBduNyIP1DRQ51uE2BpcgzprxtXM,36313
transformers/models/marian/__init__.py,sha256=Exac7OwEPmDA-_t37rrhbP3XT2fkL-P2rysKMKgvP3U,3268
transformers/models/marian/__pycache__/__init__.cpython-310.pyc,,
transformers/models/marian/__pycache__/configuration_marian.cpython-310.pyc,,
transformers/models/marian/__pycache__/convert_marian_tatoeba_to_pytorch.cpython-310.pyc,,
transformers/models/marian/__pycache__/convert_marian_to_pytorch.cpython-310.pyc,,
transformers/models/marian/__pycache__/modeling_flax_marian.cpython-310.pyc,,
transformers/models/marian/__pycache__/modeling_marian.cpython-310.pyc,,
transformers/models/marian/__pycache__/modeling_tf_marian.cpython-310.pyc,,
transformers/models/marian/__pycache__/tokenization_marian.cpython-310.pyc,,
transformers/models/marian/configuration_marian.py,sha256=ugov-OB0oSAHCFgmrJFF8eqKEkoP8j6LHG-MR3napJs,18328
transformers/models/marian/convert_marian_tatoeba_to_pytorch.py,sha256=KXL31oNi5WuLN25fwurL1ujDwItoQYn-FmTOF2PXTUM,36375
transformers/models/marian/convert_marian_to_pytorch.py,sha256=fwRys8kKuQ4NjGHRzWvdxpFP7kRYe_WFAGJRf-OYH3I,26984
transformers/models/marian/modeling_flax_marian.py,sha256=sMJwqGgBMHQyTf9TcFZKI7jXRf-gEt_t0PdC1oDzWLg,64261
transformers/models/marian/modeling_marian.py,sha256=_ycy9TudRy4tsrl32bs4GQ1mOq_vlP8kF2vzhc5CiHA,82079
transformers/models/marian/modeling_tf_marian.py,sha256=vwYLwoS-U-3iSfWmakWF3Do9YT0QfmxxZ-hzpW9osXQ,72680
transformers/models/marian/tokenization_marian.py,sha256=wo7Hy2uRzFP_hX84DhoZTvWUeXtWhqG5-BxmcFAsg_0,16812
transformers/models/markuplm/__init__.py,sha256=cK07sEFFcaJXQ6tsst5H19AQWvnsAx33OFj9qsDA9Qo,2622
transformers/models/markuplm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/configuration_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/feature_extraction_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/modeling_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/processing_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/tokenization_markuplm.cpython-310.pyc,,
transformers/models/markuplm/__pycache__/tokenization_markuplm_fast.cpython-310.pyc,,
transformers/models/markuplm/configuration_markuplm.py,sha256=Iy6p9K9Cf0bgtNIWHfpGLp3av2MBTd1IhlIR3Nqcw1E,7311
transformers/models/markuplm/feature_extraction_markuplm.py,sha256=3V8MR36mQskKYQeaGrWuqWo9w5JG67nhRvxzWu7fR9s,6404
transformers/models/markuplm/modeling_markuplm.py,sha256=LdBVyEBOAjpZOnlLy2ZNaMn44NZQZQmElBtfgjx5zv0,58348
transformers/models/markuplm/processing_markuplm.py,sha256=ePyVpFNW7t6oFkcxbEAG4y-_ht7WzWWLmlTmeFFIoJ4,6349
transformers/models/markuplm/tokenization_markuplm.py,sha256=qVpE_pPBwLioGbNHM2-uqoNiwFCoDACSCaExhZ0MvDc,68972
transformers/models/markuplm/tokenization_markuplm_fast.py,sha256=v75Kirn2W7tCxDT-AULtW9G3Ew_53mg0HiHFhYYUFys,42939
transformers/models/mask2former/__init__.py,sha256=0IxG5aY2Tg_zZxw_lEVYd2q4nXILDY3gda4jhGQ_78s,2138
transformers/models/mask2former/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mask2former/__pycache__/configuration_mask2former.cpython-310.pyc,,
transformers/models/mask2former/__pycache__/convert_mask2former_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mask2former/__pycache__/image_processing_mask2former.cpython-310.pyc,,
transformers/models/mask2former/__pycache__/modeling_mask2former.cpython-310.pyc,,
transformers/models/mask2former/configuration_mask2former.py,sha256=tpaioPloLCu7uU95tLpAZSjhe8dnErn29x_ZtPbhnn8,12340
transformers/models/mask2former/convert_mask2former_original_pytorch_checkpoint_to_pytorch.py,sha256=v4a-VTdnEHxZLAykOn5AgqLXZ9yFZzhY4CUu4c3XHUE,45688
transformers/models/mask2former/image_processing_mask2former.py,sha256=nqMIFSKkfZANZZ-No5awfaphyz3raIYWPdtWDebVlDw,56966
transformers/models/mask2former/modeling_mask2former.py,sha256=5CZYW67Fxwww5u-wwqmtjzE0BULRgIl8Z4nsx-7rlc0,121090
transformers/models/maskformer/__init__.py,sha256=VyRyI2kk-wqsxPvIf5XocifIE8g0uSa0F144s79fEzI,2753
transformers/models/maskformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/configuration_maskformer.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/configuration_maskformer_swin.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_resnet_to_pytorch.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/convert_maskformer_swin_to_pytorch.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/feature_extraction_maskformer.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/image_processing_maskformer.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/modeling_maskformer.cpython-310.pyc,,
transformers/models/maskformer/__pycache__/modeling_maskformer_swin.cpython-310.pyc,,
transformers/models/maskformer/configuration_maskformer.py,sha256=C09gAgP3eH9aFJB1FGIscfG5foqumfovJ7IWGi5VVck,10259
transformers/models/maskformer/configuration_maskformer_swin.py,sha256=ER7k7WSqJXvglLLr0kh1emetNU0kOuzrrYlcUs8Xl_U,7216
transformers/models/maskformer/convert_maskformer_original_pytorch_checkpoint_to_pytorch.py,sha256=CEKaBhurc8x3mvE7YMqfULIoybxq0Guj0hGHJouG5s8,32237
transformers/models/maskformer/convert_maskformer_resnet_to_pytorch.py,sha256=LhKIHvwqSPTV6_UegT-_G-dxsSpmwmX9x_Dvfa9GnRc,20740
transformers/models/maskformer/convert_maskformer_swin_to_pytorch.py,sha256=ylQYD_1uSmCZLz-PtxIc7I2wCSkC-XDgbfMafM7Vs44,20329
transformers/models/maskformer/feature_extraction_maskformer.py,sha256=MMPQuQY2EnK4vixDve-I-PIFqCDWQNYYeVdAYvIY8HY,1214
transformers/models/maskformer/image_processing_maskformer.py,sha256=rPNRsOu2wKNwnWPHs0f2WfS1cYgzG-jadsGPGYh0K98,57898
transformers/models/maskformer/modeling_maskformer.py,sha256=k_FgIueFxn2_GlqZaRhMt2WByMaDApVatq9joQDIALU,90775
transformers/models/maskformer/modeling_maskformer_swin.py,sha256=9b8DSeashxGC0AsmPUmLvgF1IqW2UqkFnHSHWhYWAWs,42633
transformers/models/mbart/__init__.py,sha256=YjI533IlrLIES5x6U7S41gDRjPlGtpynh3xu96hIj1g,4231
transformers/models/mbart/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mbart/__pycache__/configuration_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/convert_mbart_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mbart/__pycache__/modeling_flax_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/modeling_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/modeling_tf_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/tokenization_mbart.cpython-310.pyc,,
transformers/models/mbart/__pycache__/tokenization_mbart_fast.cpython-310.pyc,,
transformers/models/mbart/configuration_mbart.py,sha256=t1QPTFbSv5_269wlbS6Fb0BnkSFj1jTnossAzVmj6Hg,18162
transformers/models/mbart/convert_mbart_original_checkpoint_to_pytorch.py,sha256=xVW9Mj-jd7X_MImJCgS52Aok1CGPf-E6u8ptvG1hK8o,3035
transformers/models/mbart/modeling_flax_mbart.py,sha256=zIgVhp_d1kFpeG9uHFTavay5TOgku3n2su1vGFBblD4,75089
transformers/models/mbart/modeling_mbart.py,sha256=KkGIEVYfZC72Fgc-cgn0FIE-vVBQ2SGDrqsiVrndgH8,96466
transformers/models/mbart/modeling_tf_mbart.py,sha256=OEy5AgJLUrB2jTPMBIhDsw-MLuRelamEItyXStJayPs,74193
transformers/models/mbart/tokenization_mbart.py,sha256=cyxJpDRR-_GxBmUqaxwXzWC5SOmgvlSSIsDdtF8N8xo,14106
transformers/models/mbart/tokenization_mbart_fast.py,sha256=1ieIvKkfDtKZe_hHOaZNbSt6fzVPylKoYOtNI3T6rpw,10997
transformers/models/mbart50/__init__.py,sha256=5ekQCS9OkL3_5UJXnu7Z5cVeCi76pVgAxHkC8qQ8XKk,1847
transformers/models/mbart50/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mbart50/__pycache__/tokenization_mbart50.cpython-310.pyc,,
transformers/models/mbart50/__pycache__/tokenization_mbart50_fast.cpython-310.pyc,,
transformers/models/mbart50/tokenization_mbart50.py,sha256=INTdGnO_YBeB7mWdpBgkz8PH-prQOKd1dP92qbBsKDE,16307
transformers/models/mbart50/tokenization_mbart50_fast.py,sha256=4XQPT5nXMLElCwfHfy4uTolWe2VmD1HcXdVJH0jQ3oA,11594
transformers/models/megatron_bert/__init__.py,sha256=syriQ2RlmrQuszQMRpmUaD_i2EK_TdlY4t_qdivZehU,2302
transformers/models/megatron_bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/megatron_bert/__pycache__/configuration_megatron_bert.cpython-310.pyc,,
transformers/models/megatron_bert/__pycache__/convert_megatron_bert_checkpoint.cpython-310.pyc,,
transformers/models/megatron_bert/__pycache__/modeling_megatron_bert.cpython-310.pyc,,
transformers/models/megatron_bert/configuration_megatron_bert.py,sha256=iewFczA_jEjpgax1zQ9T0SVF-sL0p3hFwtIHZQVm9M8,6466
transformers/models/megatron_bert/convert_megatron_bert_checkpoint.py,sha256=VAMD1MFdVG8w9cQkRfmlZCEvaMgoo-lyFI9deunD5OA,13686
transformers/models/megatron_bert/modeling_megatron_bert.py,sha256=0xkptThTSSDDdS4VXY6IBKdWETB3vSBXIW4uRAEMPjk,83454
transformers/models/megatron_gpt2/__init__.py,sha256=WycFl9cUevoXIBhB76qKtnNRIPMk2LoTDkmkfAfOy9M,630
transformers/models/megatron_gpt2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/megatron_gpt2/__pycache__/checkpoint_reshaping_and_interoperability.cpython-310.pyc,,
transformers/models/megatron_gpt2/__pycache__/convert_megatron_gpt2_checkpoint.cpython-310.pyc,,
transformers/models/megatron_gpt2/checkpoint_reshaping_and_interoperability.py,sha256=NPoWPPSaT29iHoGRoyc1B_hdc67QNoytsVj_glQF430,36692
transformers/models/megatron_gpt2/convert_megatron_gpt2_checkpoint.py,sha256=UPLXCjF4Fixnw_gy6kzxTK64ioxo_EIxwSVO6oKCqqQ,13661
transformers/models/mgp_str/__init__.py,sha256=2wNKbfWxSFX5xHRL8pE6jWJ3oPFmHT30cCFp1iPS_v4,1984
transformers/models/mgp_str/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mgp_str/__pycache__/configuration_mgp_str.cpython-310.pyc,,
transformers/models/mgp_str/__pycache__/modeling_mgp_str.cpython-310.pyc,,
transformers/models/mgp_str/__pycache__/processing_mgp_str.cpython-310.pyc,,
transformers/models/mgp_str/__pycache__/tokenization_mgp_str.cpython-310.pyc,,
transformers/models/mgp_str/configuration_mgp_str.py,sha256=UxvaYkn_jG5igokA3dk-QrtUyvATGbTvg6wzB8nYpRY,5781
transformers/models/mgp_str/modeling_mgp_str.py,sha256=dZovmq21WcD5FykXJnCUfiXf6CnfEG_NYRRO4YPRoY4,21924
transformers/models/mgp_str/processing_mgp_str.py,sha256=dh1MJ17yNZdoorG_Mi31Q7waqTnyRock-s4c2k_g0DQ,9298
transformers/models/mgp_str/tokenization_mgp_str.py,sha256=CIz9yrKh2VPsckVtYJ0pynFgPhwYY9XuyJasmKD9mKo,3776
transformers/models/mistral/__init__.py,sha256=agxkLCh2R8fnK-uMAQ42R5BO5dR9h7MoqFOnxz1RnN4,3168
transformers/models/mistral/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mistral/__pycache__/configuration_mistral.cpython-310.pyc,,
transformers/models/mistral/__pycache__/convert_mistral_weights_to_hf.cpython-310.pyc,,
transformers/models/mistral/__pycache__/modeling_flax_mistral.cpython-310.pyc,,
transformers/models/mistral/__pycache__/modeling_mistral.cpython-310.pyc,,
transformers/models/mistral/__pycache__/modeling_tf_mistral.cpython-310.pyc,,
transformers/models/mistral/configuration_mistral.py,sha256=S6dBidOaV0yR9LkW_qAHdLKulYU-MhIE3dxr848lLEM,7111
transformers/models/mistral/convert_mistral_weights_to_hf.py,sha256=d9o13aFoduxGYFQwvD5Gw-Ghfltq_555uS3gH1VQg48,11297
transformers/models/mistral/modeling_flax_mistral.py,sha256=M2Fio6tl63E0fATg077-CwLoT1iNV7pZWmquxNvOTAI,31682
transformers/models/mistral/modeling_mistral.py,sha256=qhDQVrQwgNp6afio9_AQkwZ-8Pgc577JSTwasvi3HQA,61003
transformers/models/mistral/modeling_tf_mistral.py,sha256=_EYZLvK6OjG5YWlWuVrjijG3h0O2aSi5f4tEwqHiMgk,45314
transformers/models/mixtral/__init__.py,sha256=0Q23S1QP17oHGMN8qHK-BeGQF7oGW6a2zB2MdtAH69Y,1810
transformers/models/mixtral/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mixtral/__pycache__/configuration_mixtral.cpython-310.pyc,,
transformers/models/mixtral/__pycache__/convert_mixtral_weights_to_hf.cpython-310.pyc,,
transformers/models/mixtral/__pycache__/modeling_mixtral.cpython-310.pyc,,
transformers/models/mixtral/configuration_mixtral.py,sha256=wEpokmFxB_r_oGvnKZNZ3WInSy6fPBjcWKYC_pqiMR4,8106
transformers/models/mixtral/convert_mixtral_weights_to_hf.py,sha256=WExicalIwkZccqWyRjUU2LBvbL6cM6yiOG_Oby6t3Ok,9156
transformers/models/mixtral/modeling_mixtral.py,sha256=FpTFBKxBdgExhTkXfqz5V4_9hc92-5_PqoLiH3lDxFU,71313
transformers/models/mluke/__init__.py,sha256=Pj0GBjIU6vYdhEzO7M8O35c5Jj4ivIIGAiLABhN4K7U,1356
transformers/models/mluke/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mluke/__pycache__/convert_mluke_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mluke/__pycache__/tokenization_mluke.cpython-310.pyc,,
transformers/models/mluke/convert_mluke_original_pytorch_checkpoint_to_pytorch.py,sha256=G6Z94-1_AiilSTU96PSjX_pdgFIx-b_bk8xlMKX5TuE,10185
transformers/models/mluke/tokenization_mluke.py,sha256=AhukGnED8GRglu8IH_DyUqBpeU0u65WkpOYIij4PXrE,80830
transformers/models/mobilebert/__init__.py,sha256=1GUULZMxdkLK0h0aPRB1MTzkWPqTd4AIls0lD44WOPE,4284
transformers/models/mobilebert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/configuration_mobilebert.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/convert_mobilebert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/modeling_mobilebert.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/modeling_tf_mobilebert.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/tokenization_mobilebert.cpython-310.pyc,,
transformers/models/mobilebert/__pycache__/tokenization_mobilebert_fast.cpython-310.pyc,,
transformers/models/mobilebert/configuration_mobilebert.py,sha256=zfPgqqwx8_L5I0DHN0s-0B_SWT3LE28CbLy-ei2bhvw,8217
transformers/models/mobilebert/convert_mobilebert_original_tf_checkpoint_to_pytorch.py,sha256=MRW9sorswIo4RiWq7PVVmaZsYm4wJEc1-DhcLzssDRU,2200
transformers/models/mobilebert/modeling_mobilebert.py,sha256=A6zMrtP5lsWA7SiPNvmzv4B7Odzq9clbgnTq3ZmRTac,70581
transformers/models/mobilebert/modeling_tf_mobilebert.py,sha256=f5tdUOv2rwB7eFUqfdb_CZtU14bCoHK5mskp_2KN5ic,83717
transformers/models/mobilebert/tokenization_mobilebert.py,sha256=VXF5i0l7PZMTkBrR5Eur6cCbIOHYo1DpK-dtOMAAmlI,20950
transformers/models/mobilebert/tokenization_mobilebert_fast.py,sha256=Yp5FcPJNWWdqk41D6xDu44gN8OWd0jj104BdDbGaqdg,7798
transformers/models/mobilenet_v1/__init__.py,sha256=3W-E87nKaX8p2BLUS_7gJB29idu5-LYPBXdETzYe7a0,2519
transformers/models/mobilenet_v1/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/configuration_mobilenet_v1.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/convert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/feature_extraction_mobilenet_v1.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/image_processing_mobilenet_v1.cpython-310.pyc,,
transformers/models/mobilenet_v1/__pycache__/modeling_mobilenet_v1.cpython-310.pyc,,
transformers/models/mobilenet_v1/configuration_mobilenet_v1.py,sha256=6QZfS8Adb_wz-OOqaxjRtw2C5WhTp7TVdQFvKmBsLb8,4871
transformers/models/mobilenet_v1/convert_original_tf_checkpoint_to_pytorch.py,sha256=kT9UyoJ0ZfpG2-oVhG8TNs18R3cTgXUnK1CcC-57IYM,4931
transformers/models/mobilenet_v1/feature_extraction_mobilenet_v1.py,sha256=goR0AC-IhWMrQlvzSK_0Zej42JYN-oswSGNQWnIOENU,1222
transformers/models/mobilenet_v1/image_processing_mobilenet_v1.py,sha256=7cu5EhkSZEaw2acPGiFQ9Dthq775OjiDA1THH3O_Rec,15814
transformers/models/mobilenet_v1/modeling_mobilenet_v1.py,sha256=N9j5QZL-6akG4Rf4MJkzsMxziXPZQsvTRwrM2OvLiuI,18594
transformers/models/mobilenet_v2/__init__.py,sha256=lJEJ0CiUrI6abdoZwCY_RgSdL1pZqYvj-PQ7xINBHqI,2614
transformers/models/mobilenet_v2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/configuration_mobilenet_v2.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/convert_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/feature_extraction_mobilenet_v2.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/image_processing_mobilenet_v2.cpython-310.pyc,,
transformers/models/mobilenet_v2/__pycache__/modeling_mobilenet_v2.cpython-310.pyc,,
transformers/models/mobilenet_v2/configuration_mobilenet_v2.py,sha256=0JTuxeBnxwVuI_gZvTM8BMHiAAEq_58CXoV9882mnqw,6767
transformers/models/mobilenet_v2/convert_original_tf_checkpoint_to_pytorch.py,sha256=mJVhzYCMvutwkrULewE2GGsVOxOsW4wjjE8XzTWlWIk,6401
transformers/models/mobilenet_v2/feature_extraction_mobilenet_v2.py,sha256=_IUVvyoMBsqymCoh-CVmoswZ4nOBpqFJlaoUfD8WQ3E,1222
transformers/models/mobilenet_v2/image_processing_mobilenet_v2.py,sha256=MebPYCgZFQzhQO6-ImjmUte7VEyVdE-NoOP9-16mnds,18168
transformers/models/mobilenet_v2/modeling_mobilenet_v2.py,sha256=ajJbZ5iT8hysB6-L5xNu8ZkEutYDwwt00-0JkX6SFeE,34481
transformers/models/mobilevit/__init__.py,sha256=28c3SWBGbR5MekJFDEjiAeiU5beF376M0gxHhI0vMvM,3194
transformers/models/mobilevit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/configuration_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/convert_mlcvnets_to_pytorch.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/feature_extraction_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/image_processing_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/modeling_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/__pycache__/modeling_tf_mobilevit.cpython-310.pyc,,
transformers/models/mobilevit/configuration_mobilevit.py,sha256=0vrPrSgS3AgNixlQW9dE2mR3I3DMqROwlHSpJfyvf38,7532
transformers/models/mobilevit/convert_mlcvnets_to_pytorch.py,sha256=nphj4DM8G43EUUWhQIL6TYHWB855GmZI9KPk8mLXX8E,12401
transformers/models/mobilevit/feature_extraction_mobilevit.py,sha256=na2H01bKIhQsyCHayPaVase5HRGRmmO7zVDDuY76Uj0,1207
transformers/models/mobilevit/image_processing_mobilevit.py,sha256=4R2jNDd2WCJLkHoY7Tcw2vTxNK-DHtZXo5EBcVz4_CE,21926
transformers/models/mobilevit/modeling_mobilevit.py,sha256=Mhp93dli1hspLXmb2qK4EcAh1x_FsFUyCs_p3rpIyXk,40130
transformers/models/mobilevit/modeling_tf_mobilevit.py,sha256=Ugu6rl5CD9JI2TgpNp6IWl6WE1eSgw21dND9CgK5uOQ,54676
transformers/models/mobilevitv2/__init__.py,sha256=48cbQxsCyX2oNyTy5-aYHIFf1O25lmbHgyYmjTJ0pCg,1899
transformers/models/mobilevitv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mobilevitv2/__pycache__/configuration_mobilevitv2.cpython-310.pyc,,
transformers/models/mobilevitv2/__pycache__/convert_mlcvnets_to_pytorch.cpython-310.pyc,,
transformers/models/mobilevitv2/__pycache__/modeling_mobilevitv2.cpython-310.pyc,,
transformers/models/mobilevitv2/configuration_mobilevitv2.py,sha256=BanCv_BeSMLx8d_Oj777JBqhljevKIeTJ8ZgmbeqMyU,7091
transformers/models/mobilevitv2/convert_mlcvnets_to_pytorch.py,sha256=oNRu_bXNnwXq12ze7C1-JdL_Ekkp8N8SMTXeelWVhZs,12556
transformers/models/mobilevitv2/modeling_mobilevitv2.py,sha256=fEJg_XTYQ2FEPRvUI9ILH3_IGPfYazVhyyfkFOyjZC4,38205
transformers/models/mpnet/__init__.py,sha256=ywHSRm-Qy-kCoL99sLHEOjHRhpO3JEFC9J2GwZaMDlQ,3601
transformers/models/mpnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/configuration_mpnet.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/modeling_mpnet.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/modeling_tf_mpnet.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/tokenization_mpnet.cpython-310.pyc,,
transformers/models/mpnet/__pycache__/tokenization_mpnet_fast.cpython-310.pyc,,
transformers/models/mpnet/configuration_mpnet.py,sha256=pV8B4TLaMqc9yI6YaK4V_ZpXP9-ZaEtpRbbPTppBWxs,5299
transformers/models/mpnet/modeling_mpnet.py,sha256=h9xfUV2oSpZfBNN_iQM2k3MvgB5ToRgpCqd4Oexd8f4,42601
transformers/models/mpnet/modeling_tf_mpnet.py,sha256=dHLMAG_b7V-s88gl4nQZBxtf2mS1DOnAbj8W6h_ylY4,55463
transformers/models/mpnet/tokenization_mpnet.py,sha256=rcUWsm6ts7ICY0V3R4edO3SEtrStuERIkOz1-T8C8JI,22126
transformers/models/mpnet/tokenization_mpnet_fast.py,sha256=DUx1wGlRfhxppFPHfvT68EFY9MBLdcyzhBebBdcT5lQ,9158
transformers/models/mpt/__init__.py,sha256=meTcujvVHkqcgK-Ppw4UA8LO7-n8m9AmkZQ6z0deoSI,1813
transformers/models/mpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mpt/__pycache__/configuration_mpt.cpython-310.pyc,,
transformers/models/mpt/__pycache__/modeling_mpt.cpython-310.pyc,,
transformers/models/mpt/configuration_mpt.py,sha256=a2arJuhKMsjPVf8m6SD3cYdI1KZF0rWUyqiPPTV7bkw,11233
transformers/models/mpt/modeling_mpt.py,sha256=EqkRCk5nqZ52WFr5wSbLf7B0L_ZkkxnmcMLhwyVejZw,40852
transformers/models/mra/__init__.py,sha256=lDPumVYp9ej4hsyIVltTxw6UbaouFH_aMwkk7Ufoz7U,2090
transformers/models/mra/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mra/__pycache__/configuration_mra.cpython-310.pyc,,
transformers/models/mra/__pycache__/convert_mra_pytorch_to_pytorch.cpython-310.pyc,,
transformers/models/mra/__pycache__/modeling_mra.cpython-310.pyc,,
transformers/models/mra/configuration_mra.py,sha256=6Re8q99pn6otoT8q3mYcISJHhVhX_mpeLvIu7N-d4Ac,6510
transformers/models/mra/convert_mra_pytorch_to_pytorch.py,sha256=LhaVlQ4q88gtewg-geRYZ748xQ3brLLhyDIo-OGWSdI,4247
transformers/models/mra/modeling_mra.py,sha256=psubuZxkjXzgNvt4SU2Yav0x8wAG1NoHVYVJedAV64E,61975
transformers/models/mt5/__init__.py,sha256=q5f0AWvlyU1eQjk0OXCpMZ4OM3qNDq35Pv6RuxrWQeI,3597
transformers/models/mt5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mt5/__pycache__/configuration_mt5.cpython-310.pyc,,
transformers/models/mt5/__pycache__/modeling_flax_mt5.cpython-310.pyc,,
transformers/models/mt5/__pycache__/modeling_mt5.cpython-310.pyc,,
transformers/models/mt5/__pycache__/modeling_tf_mt5.cpython-310.pyc,,
transformers/models/mt5/configuration_mt5.py,sha256=FWf_o09OLTVKDlGkxu-30VKuojQil1gbw2WiRyQQ7tw,7900
transformers/models/mt5/modeling_flax_mt5.py,sha256=nbooo3tWzSe943NzLlZC7r4Sfk0oV5WBN0aN7TxcKXk,4242
transformers/models/mt5/modeling_mt5.py,sha256=doZc_OSyIRHhCiYW_AFMiB7a72NHxSeZmdoVjAnswlc,113083
transformers/models/mt5/modeling_tf_mt5.py,sha256=NTS87VyUcufilfS17a2VN4uCx0dvYswLBNATv6gBRsQ,3325
transformers/models/musicgen/__init__.py,sha256=_B93S_nGM8Syv750EV9xwR0ACv9GPAB-G-Uwsox0TLM,1899
transformers/models/musicgen/__pycache__/__init__.cpython-310.pyc,,
transformers/models/musicgen/__pycache__/configuration_musicgen.cpython-310.pyc,,
transformers/models/musicgen/__pycache__/convert_musicgen_transformers.cpython-310.pyc,,
transformers/models/musicgen/__pycache__/modeling_musicgen.cpython-310.pyc,,
transformers/models/musicgen/__pycache__/processing_musicgen.cpython-310.pyc,,
transformers/models/musicgen/configuration_musicgen.py,sha256=URJLQcaZRW3Kr4ad9MSMnbbWMUVXXN2EIphfHVTLJE8,11384
transformers/models/musicgen/convert_musicgen_transformers.py,sha256=ky4nceHZ-78GAp0L4vY-cFXsZ6lGvkYzX9k8MGUZ2kQ,9384
transformers/models/musicgen/modeling_musicgen.py,sha256=TyjpJyb5upAr_-oMAbr0UTVxWpRGQK7MyhMy3XN7rNI,136042
transformers/models/musicgen/processing_musicgen.py,sha256=A4008UqJ3Wm4nixG-GIR6b5Ul1ZcV2Nnq4ZB22VsMDQ,5667
transformers/models/musicgen_melody/__init__.py,sha256=v3FVLsoE2TEh_eAaYKcb8v114HPo9RZN-p5TSS4eD_I,2594
transformers/models/musicgen_melody/__pycache__/__init__.cpython-310.pyc,,
transformers/models/musicgen_melody/__pycache__/configuration_musicgen_melody.cpython-310.pyc,,
transformers/models/musicgen_melody/__pycache__/convert_musicgen_melody_transformers.cpython-310.pyc,,
transformers/models/musicgen_melody/__pycache__/feature_extraction_musicgen_melody.cpython-310.pyc,,
transformers/models/musicgen_melody/__pycache__/modeling_musicgen_melody.cpython-310.pyc,,
transformers/models/musicgen_melody/__pycache__/processing_musicgen_melody.cpython-310.pyc,,
transformers/models/musicgen_melody/configuration_musicgen_melody.py,sha256=FDso8d2_JSiimU9n8pg-QUIixDz-qHYQxqO50Z2XP_I,12489
transformers/models/musicgen_melody/convert_musicgen_melody_transformers.py,sha256=uaWtwMCLmSCXc7c0PK9TinKJeBsGjDfRwmXVHH_Mxig,11356
transformers/models/musicgen_melody/feature_extraction_musicgen_melody.py,sha256=0-gMjuGhG4JMeM-44wa3aTo3Nph-_cjZs7k3nhc6cfE,15227
transformers/models/musicgen_melody/modeling_musicgen_melody.py,sha256=IK9f8q2ZbXY-rD2tnRconglMpVO_BFebGuuFUPsrxK8,129788
transformers/models/musicgen_melody/processing_musicgen_melody.py,sha256=W2dEpemPPhX10YgTwm0T3zvy71Z8QxJ_LSoz6Z00UAI,8634
transformers/models/mvp/__init__.py,sha256=GjxZ0S34sqQUeirY-0gnhXgr9R8r2oUV4kIg7CjrwGQ,2372
transformers/models/mvp/__pycache__/__init__.cpython-310.pyc,,
transformers/models/mvp/__pycache__/configuration_mvp.cpython-310.pyc,,
transformers/models/mvp/__pycache__/modeling_mvp.cpython-310.pyc,,
transformers/models/mvp/__pycache__/tokenization_mvp.cpython-310.pyc,,
transformers/models/mvp/__pycache__/tokenization_mvp_fast.cpython-310.pyc,,
transformers/models/mvp/configuration_mvp.py,sha256=Q8sbJx-7VBNufbcWUTOzdcp4ylzXemiOEwaf2S2VNQs,8409
transformers/models/mvp/modeling_mvp.py,sha256=LRfUxn1rRt0nEnMNyRK3IIRs8wxxwx7LMsvNPQ-pqqY,92711
transformers/models/mvp/tokenization_mvp.py,sha256=JlR6l5XT5U4eU_20FRb69tmEXvexJa1d88moJ3jxj3E,16192
transformers/models/mvp/tokenization_mvp_fast.py,sha256=Pa8ZaGtDrfhrWLnB9FPsO2OGU131E1l5HEAS25Nv6bc,12268
transformers/models/nllb/__init__.py,sha256=tM7_FdmE7zOQm68GoRQiRt1jbYfPea9kC24QJSSMgIE,1868
transformers/models/nllb/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nllb/__pycache__/tokenization_nllb.cpython-310.pyc,,
transformers/models/nllb/__pycache__/tokenization_nllb_fast.cpython-310.pyc,,
transformers/models/nllb/tokenization_nllb.py,sha256=MOHPuxJpEvUpzuUntOced_qH77DGWlYTY59GF2TQwN8,19065
transformers/models/nllb/tokenization_nllb_fast.py,sha256=H-ohJEWhMSGNmGqaKdvRK_feqkxrekNVaEbeiu08KH8,15940
transformers/models/nllb_moe/__init__.py,sha256=BjASPLRDJ-mU8cNXB-T9KzLyqeqoYEqinlKYrjUyt5o,1757
transformers/models/nllb_moe/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nllb_moe/__pycache__/configuration_nllb_moe.cpython-310.pyc,,
transformers/models/nllb_moe/__pycache__/convert_nllb_moe_sharded_original_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/nllb_moe/__pycache__/modeling_nllb_moe.cpython-310.pyc,,
transformers/models/nllb_moe/configuration_nllb_moe.py,sha256=bys0xdiIRKz8W6NXWmgUOh4V9lcet_3KFZfN-cxjP-4,11168
transformers/models/nllb_moe/convert_nllb_moe_sharded_original_checkpoint_to_pytorch.py,sha256=c9Zab9qVzNESk0U2exJNaoDwUQo_Q7ZpcZHViZjqTQQ,6477
transformers/models/nllb_moe/modeling_nllb_moe.py,sha256=dzb19cfAtCx8EWqro-zffkoJhnv_waA8oUno2nZgXrk,85850
transformers/models/nougat/__init__.py,sha256=2cSw40yf-T81USela2GvWs-NSXWHkOa6zJ_3BO7QSCY,1914
transformers/models/nougat/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nougat/__pycache__/convert_nougat_to_hf.cpython-310.pyc,,
transformers/models/nougat/__pycache__/image_processing_nougat.cpython-310.pyc,,
transformers/models/nougat/__pycache__/processing_nougat.cpython-310.pyc,,
transformers/models/nougat/__pycache__/tokenization_nougat_fast.cpython-310.pyc,,
transformers/models/nougat/convert_nougat_to_hf.py,sha256=3KHG9mTikCDX88hKbRB8_aVQzKdlheP1_TiOydwQoIw,10949
transformers/models/nougat/image_processing_nougat.py,sha256=AfDySnr8HCJcNiMRLP8WM1Nl7d6ey7RFbLtd6bho2ts,24253
transformers/models/nougat/processing_nougat.py,sha256=65OZ7-XvFeiEwFjEi69ZDY931w6NvHTHGo9EixCVxKU,6731
transformers/models/nougat/tokenization_nougat_fast.py,sha256=gPZ7-ekhASu3KKPoPUOqrvxL5y7bYDRVnTNOuOgcwFU,24703
transformers/models/nystromformer/__init__.py,sha256=qp6ceFtyZJeQie59gdO4cMoIX8yQhxdzuqkm7H9iVmg,2133
transformers/models/nystromformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/nystromformer/__pycache__/configuration_nystromformer.cpython-310.pyc,,
transformers/models/nystromformer/__pycache__/convert_nystromformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/nystromformer/__pycache__/modeling_nystromformer.cpython-310.pyc,,
transformers/models/nystromformer/configuration_nystromformer.py,sha256=PSwSfYNJRM_svcUtyQStJma5Ub7Uvt5SxtqFsE7DcaM,6366
transformers/models/nystromformer/convert_nystromformer_original_pytorch_checkpoint_to_pytorch.py,sha256=8K5IGFosME-LAljFLuTc09oce1IwxZDcxw1KPHsamqc,4197
transformers/models/nystromformer/modeling_nystromformer.py,sha256=NU1olBZgN_kTzOVO8-Pcx74GeF7OWcwLgzIa6cgjdg0,48758
transformers/models/olmo/__init__.py,sha256=_dNlQLxAlwk4Yt9djxtrLXy90ben8LUx4LtD8wZR5hU,1658
transformers/models/olmo/__pycache__/__init__.cpython-310.pyc,,
transformers/models/olmo/__pycache__/configuration_olmo.cpython-310.pyc,,
transformers/models/olmo/__pycache__/convert_olmo_weights_to_hf.cpython-310.pyc,,
transformers/models/olmo/__pycache__/modeling_olmo.cpython-310.pyc,,
transformers/models/olmo/configuration_olmo.py,sha256=qqY8AF6TOEYKn6q-KKKuFB9WE_I6649dyvWBp-FAPh4,8810
transformers/models/olmo/convert_olmo_weights_to_hf.py,sha256=SI91Kn_B_m0oel2kuJ2LUMGqfaNZL4Q4sT2ydqNYZlE,9413
transformers/models/olmo/modeling_olmo.py,sha256=-tPo2LJNjgCgqZClsATHqh7uFnHsfGmF1gKtzO9gbyc,53410
transformers/models/oneformer/__init__.py,sha256=1HGB0SzVFLYt8u0pwJgEan3swBlh0TFVns6ZlcD3kP0,2214
transformers/models/oneformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/configuration_oneformer.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/convert_to_hf_oneformer.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/image_processing_oneformer.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/modeling_oneformer.cpython-310.pyc,,
transformers/models/oneformer/__pycache__/processing_oneformer.cpython-310.pyc,,
transformers/models/oneformer/configuration_oneformer.py,sha256=IRKqjxjrS3yRjnGL6QDzz6F7B3zBmgkLawrvRkc7gF8,13435
transformers/models/oneformer/convert_to_hf_oneformer.py,sha256=yBWS0SE1sGS9UqCzX2EdbhAiIWvBCumSBwutJ8VQFF4,50691
transformers/models/oneformer/image_processing_oneformer.py,sha256=ZbswKFNx55YQWqNQDc9zsVa5SWe3TZI8YDp00JvLruU,61227
transformers/models/oneformer/modeling_oneformer.py,sha256=Tn9gFG_CVHoya_n_4LSH_ZXIXdMn6-Tjs4oWHHOCQdQ,143533
transformers/models/oneformer/processing_oneformer.py,sha256=ahso8fGMLGb078QfY8T5o1bDj5OaptoMbIxiTIJGM7c,9377
transformers/models/openai/__init__.py,sha256=OIO2mLuPtZemDpjaDro864qLEfR3dpHRBm7C96gA8bE,3354
transformers/models/openai/__pycache__/__init__.cpython-310.pyc,,
transformers/models/openai/__pycache__/configuration_openai.cpython-310.pyc,,
transformers/models/openai/__pycache__/convert_openai_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/openai/__pycache__/modeling_openai.cpython-310.pyc,,
transformers/models/openai/__pycache__/modeling_tf_openai.cpython-310.pyc,,
transformers/models/openai/__pycache__/tokenization_openai.cpython-310.pyc,,
transformers/models/openai/__pycache__/tokenization_openai_fast.cpython-310.pyc,,
transformers/models/openai/configuration_openai.py,sha256=a2yPfNdQ-cQxCpnWgJ5SykpLZ6aS5D-1BWcfUWoO10g,7077
transformers/models/openai/convert_openai_original_tf_checkpoint_to_pytorch.py,sha256=WVyibwB1gaKbNqJubia_mbh-N8Qy4a77W7XAroTw0yA,2665
transformers/models/openai/modeling_openai.py,sha256=1yjKRobX3dnNVXl-KxKVECjJ1QtEgI_-KOKG6Xh7lBs,38250
transformers/models/openai/modeling_tf_openai.py,sha256=jrIefNuYXxKJhQm1jwDHWOHHguugSTd5MDZizTvyeZk,41056
transformers/models/openai/tokenization_openai.py,sha256=p6kc9W4iZVyVcAoUjRTHoxj-qA6kEL_H8bHVDjL4y4c,15160
transformers/models/openai/tokenization_openai_fast.py,sha256=SBwvthsGHgtEOs3nnAVuQ0KIlkDzD7WG9oXrA1hYtHg,2521
transformers/models/opt/__init__.py,sha256=_dgUsKmKmgcZ_Nz2395ycZOeHLCSHwpofYpYT8RsZKY,2813
transformers/models/opt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/opt/__pycache__/configuration_opt.cpython-310.pyc,,
transformers/models/opt/__pycache__/convert_opt_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/opt/__pycache__/modeling_flax_opt.cpython-310.pyc,,
transformers/models/opt/__pycache__/modeling_opt.cpython-310.pyc,,
transformers/models/opt/__pycache__/modeling_tf_opt.cpython-310.pyc,,
transformers/models/opt/configuration_opt.py,sha256=ePwJEkN9jNy45Q3iFMiR9TMzNTNX-3PYorRhTbf1KKk,6660
transformers/models/opt/convert_opt_original_pytorch_checkpoint_to_pytorch.py,sha256=NIAQgoFpeWZyZCQ6zN4EylZdojsIQm2wtvOIuMZKj64,3857
transformers/models/opt/modeling_flax_opt.py,sha256=EYnpmtEQrhf6vLT4JyyWd8V76NzKxNiFn0Mw22gxXeY,31540
transformers/models/opt/modeling_opt.py,sha256=b61UFCfJYhWwQ_vg7k54rOteies8Cqz3i_Ken9OB1sE,62649
transformers/models/opt/modeling_tf_opt.py,sha256=mYrf_6x5FAZ4HCyP1cdCvTg71H2lkzPAnudp_FdKXMc,49552
transformers/models/owlv2/__init__.py,sha256=YqVTBiltWupkgF5jfNjjVzeo8AhMXFYed6EpgWNseXk,2418
transformers/models/owlv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/configuration_owlv2.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/convert_owlv2_to_hf.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/image_processing_owlv2.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/modeling_owlv2.cpython-310.pyc,,
transformers/models/owlv2/__pycache__/processing_owlv2.cpython-310.pyc,,
transformers/models/owlv2/configuration_owlv2.py,sha256=Zs25z_nUwnpgSP4FssJgRzd65UQJ1_lGG6fiZaHZHy8,15470
transformers/models/owlv2/convert_owlv2_to_hf.py,sha256=rF02k9XWTswf4P4ZZ76ekB3be6pRsFJLtbuWaJpyx3Y,22018
transformers/models/owlv2/image_processing_owlv2.py,sha256=S2qJFxIQ_3ZKv5i3-pL-NhgoWzXaoCW6gcJY1GkgHFI,26918
transformers/models/owlv2/modeling_owlv2.py,sha256=b0f_Pvv3Tja1ajDcw4-51ZWtrZO8uLi2D7euXcwvh_U,80232
transformers/models/owlv2/processing_owlv2.py,sha256=WUAZC5nLIqVLseH1odt8F32mHZV2R2iaGe1eWq-9dMY,10046
transformers/models/owlvit/__init__.py,sha256=kT4pypCGo36tZhYV4moH0R0FgqeHuFH4BAq-kc6_PDg,2723
transformers/models/owlvit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/configuration_owlvit.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/convert_owlvit_original_flax_to_hf.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/feature_extraction_owlvit.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/image_processing_owlvit.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/modeling_owlvit.cpython-310.pyc,,
transformers/models/owlvit/__pycache__/processing_owlvit.cpython-310.pyc,,
transformers/models/owlvit/configuration_owlvit.py,sha256=k18t7qjhCJ4PyiwrjRLT6Lh1NbIPj48PyZGsZ-z9hqs,16660
transformers/models/owlvit/convert_owlvit_original_flax_to_hf.py,sha256=tofzNZcVROwfYoV7pV6u50Am3TFm-XmuJEAGwNvRT9o,13988
transformers/models/owlvit/feature_extraction_owlvit.py,sha256=yPO8FbUw3YabKbsV_ozKpIr6JixO9knVw1eMIHeiCtY,1186
transformers/models/owlvit/image_processing_owlvit.py,sha256=vYcwjzcsheXUv-ZQARjVwuJGK6rJuAkQPy6GQPWE7uo,28604
transformers/models/owlvit/modeling_owlvit.py,sha256=_zKEZ2LXCCe5xewh_Uw9uz2gAs4EqZRj6QE3rrxARg4,75669
transformers/models/owlvit/processing_owlvit.py,sha256=0nSZZV8HtYmywaCfUqMCWYadqAO3QtMi8S-Jt_y8ai0,11042
transformers/models/paligemma/__init__.py,sha256=ZIdi9pt3rI2ONhbv1Rix7e66R94-K3aST57W4GPGAJ0,1698
transformers/models/paligemma/__pycache__/__init__.cpython-310.pyc,,
transformers/models/paligemma/__pycache__/configuration_paligemma.cpython-310.pyc,,
transformers/models/paligemma/__pycache__/convert_paligemma_weights_to_hf.cpython-310.pyc,,
transformers/models/paligemma/__pycache__/modeling_paligemma.cpython-310.pyc,,
transformers/models/paligemma/__pycache__/processing_paligemma.cpython-310.pyc,,
transformers/models/paligemma/configuration_paligemma.py,sha256=KNSOqZc3_gPBcbye1KMOaA2VsWa4t9XFEwstOi9jbr8,6048
transformers/models/paligemma/convert_paligemma_weights_to_hf.py,sha256=lRp8Fi7CwaeuySEoRWlcdCAt2QgPJ5cIjoBbm8mUbbk,16896
transformers/models/paligemma/modeling_paligemma.py,sha256=iXqm_fcZXEXFbE3O0Sy-mzMyqJdUqL-ANT5ZCrGL47U,28788
transformers/models/paligemma/processing_paligemma.py,sha256=YjB38qRqQwsifnKL3ZtW9pTOmiDS8MCUrwNHnXurbX8,14353
transformers/models/patchtsmixer/__init__.py,sha256=lrY8Epa2mGlbzcJUxAdLW72aCHKl17PzJzMTaCSVvU4,1973
transformers/models/patchtsmixer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/patchtsmixer/__pycache__/configuration_patchtsmixer.cpython-310.pyc,,
transformers/models/patchtsmixer/__pycache__/modeling_patchtsmixer.cpython-310.pyc,,
transformers/models/patchtsmixer/configuration_patchtsmixer.py,sha256=M1SuKLfqjuXcBSFB8gcvi_owPZkfW1xpkn_nt3E6DkM,12531
transformers/models/patchtsmixer/modeling_patchtsmixer.py,sha256=vVPoPVn7p8Y1Pm8YXHR3bhLr8rFBwaOLt3DU73uhcI4,87825
transformers/models/patchtst/__init__.py,sha256=a7SZ6h8LEpH8FTberrHpDnWQTpkDDra342dbj9EfKYk,1864
transformers/models/patchtst/__pycache__/__init__.cpython-310.pyc,,
transformers/models/patchtst/__pycache__/configuration_patchtst.cpython-310.pyc,,
transformers/models/patchtst/__pycache__/modeling_patchtst.cpython-310.pyc,,
transformers/models/patchtst/configuration_patchtst.py,sha256=1naWgHGmNeEvBDfHmouAZeNdb0sQ-7taUXPLSW6fX2g,12284
transformers/models/patchtst/modeling_patchtst.py,sha256=rxFpaGipKkKcmSR3XbXSPpdSJ1Ctg09GMl4kdxmTDfI,91725
transformers/models/pegasus/__init__.py,sha256=j7jvv7ORuLEB9kCOedIadaT-NaSFS0M1-mFFjK7InL8,3931
transformers/models/pegasus/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/configuration_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/convert_pegasus_tf_to_pytorch.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/modeling_flax_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/modeling_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/modeling_tf_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/tokenization_pegasus.cpython-310.pyc,,
transformers/models/pegasus/__pycache__/tokenization_pegasus_fast.cpython-310.pyc,,
transformers/models/pegasus/configuration_pegasus.py,sha256=vtVKqwlk0hgnPlYWuUn4sylNykdIBptmjePd-e5owxk,7471
transformers/models/pegasus/convert_pegasus_tf_to_pytorch.py,sha256=9geJowNAukZc9FE2OEq0pXQi6ynw9k-2NFtlmISxpUg,5359
transformers/models/pegasus/modeling_flax_pegasus.py,sha256=L12HwH0GbiOY4lrJXXrKj8knEaX2B3EvAZe1ZK8oIAs,65972
transformers/models/pegasus/modeling_pegasus.py,sha256=TP2u8--ZRhqTR2QUP22mC0UP8B4D9VhYUfTM9T5XMO8,80777
transformers/models/pegasus/modeling_tf_pegasus.py,sha256=3Y-rYRgfpKlDckfmVkwZu8tcnak6Xl5PeNrzSIE0uys,74200
transformers/models/pegasus/tokenization_pegasus.py,sha256=zRyVOMqZunsKvEp0Hq4ZIdP8fwlMASOB_bTKk_TNaPg,13125
transformers/models/pegasus/tokenization_pegasus_fast.py,sha256=7gt2LpNZCzqprN0pwxmYis0d1LBKjqgb2O_l3QWCkbk,9940
transformers/models/pegasus_x/__init__.py,sha256=EJwH0GJ5O6nR5iWGcB6kZfh4tluB0S7DTHAHXzmW6Z4,1640
transformers/models/pegasus_x/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pegasus_x/__pycache__/configuration_pegasus_x.cpython-310.pyc,,
transformers/models/pegasus_x/__pycache__/modeling_pegasus_x.cpython-310.pyc,,
transformers/models/pegasus_x/configuration_pegasus_x.py,sha256=4aDNEccBs7KEXmS6LxvEYMsSh0CEnjltLa_E7i9quxU,8085
transformers/models/pegasus_x/modeling_pegasus_x.py,sha256=6QRb9DNNyXY4Ybi6vDWezRcY9e0KxVpS0QNaAXIxKSk,76641
transformers/models/perceiver/__init__.py,sha256=l4oTV7LUBhR5m0sY7ifLdjbj4Z3wZqbxtOfsHPSrUi4,3105
transformers/models/perceiver/__pycache__/__init__.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/configuration_perceiver.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/convert_perceiver_haiku_to_pytorch.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/feature_extraction_perceiver.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/image_processing_perceiver.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/modeling_perceiver.cpython-310.pyc,,
transformers/models/perceiver/__pycache__/tokenization_perceiver.cpython-310.pyc,,
transformers/models/perceiver/configuration_perceiver.py,sha256=4OevU9EjFFptCRygu9-1eVC76YXY_PSLNKyd8FHgKu8,12154
transformers/models/perceiver/convert_perceiver_haiku_to_pytorch.py,sha256=r9nb8tWR170S786U85dagjFFbgwL37vmpdOqU-DPenQ,21285
transformers/models/perceiver/feature_extraction_perceiver.py,sha256=0lW_qh5ONtUwr0ARM9RB9hizA76wL6fmeofDrhbIsXI,1207
transformers/models/perceiver/image_processing_perceiver.py,sha256=cAMSnIE8lGaciJZNu6BxdT4YccgYPwYPTZOjP5GQOVY,17940
transformers/models/perceiver/modeling_perceiver.py,sha256=K1SYaaRLPmmI9zrXC7JAvIAWDtbNCypMcLO7IBrN0kw,148756
transformers/models/perceiver/tokenization_perceiver.py,sha256=ghYlQHviP1Pjdxuin3oR1VkcH5Ov9wHI8DEodLQHI2w,8018
transformers/models/persimmon/__init__.py,sha256=xrG3P_nWFWNov6mzHxKyRE4M7zZsXu__4h948LOV5_M,1839
transformers/models/persimmon/__pycache__/__init__.cpython-310.pyc,,
transformers/models/persimmon/__pycache__/configuration_persimmon.cpython-310.pyc,,
transformers/models/persimmon/__pycache__/convert_persimmon_weights_to_hf.cpython-310.pyc,,
transformers/models/persimmon/__pycache__/modeling_persimmon.cpython-310.pyc,,
transformers/models/persimmon/configuration_persimmon.py,sha256=EVwc6GyNi-mQXxKDQLFVhOGx8jk2Xt0O92wwt2JzaGw,7563
transformers/models/persimmon/convert_persimmon_weights_to_hf.py,sha256=F3NFcbCWD-UxFwgp2h-Nv78_M0p0LELPq4re30ZNIjU,4644
transformers/models/persimmon/modeling_persimmon.py,sha256=wlLGRuXLbd_ILG86_SkUGBfIwtlgHCNhdOtyJeTI8qs,54499
transformers/models/phi/__init__.py,sha256=qMWyJRn1PnnyX647VO4xrJbR7hlTiwvtEkyQVDEKHxw,1807
transformers/models/phi/__pycache__/__init__.cpython-310.pyc,,
transformers/models/phi/__pycache__/configuration_phi.cpython-310.pyc,,
transformers/models/phi/__pycache__/convert_phi_weights_to_hf.cpython-310.pyc,,
transformers/models/phi/__pycache__/modeling_phi.cpython-310.pyc,,
transformers/models/phi/configuration_phi.py,sha256=J2WgVgHmOOshM6togfr5DhPw2q6jUnl8anikT1Ch6yQ,8984
transformers/models/phi/convert_phi_weights_to_hf.py,sha256=XrjgtZm6GZQx01rZ0q52g6e4ajyZhl8n02QNchAD6BQ,7685
transformers/models/phi/modeling_phi.py,sha256=kJ6cI_uKhia6BwBqqCIv8eVc4x9_XB3hgQAuniVjg0g,67693
transformers/models/phi3/__init__.py,sha256=NdSAgnhYHRU5kcPOw8sulyJozgiAP1MIfPfCLj1sypU,1823
transformers/models/phi3/__pycache__/__init__.cpython-310.pyc,,
transformers/models/phi3/__pycache__/configuration_phi3.cpython-310.pyc,,
transformers/models/phi3/__pycache__/modeling_phi3.cpython-310.pyc,,
transformers/models/phi3/configuration_phi3.py,sha256=qh8ZzVWfLIzP6RpVTuUuRFn1I2lI0Y5QXAWAf4dl9Oo,10609
transformers/models/phi3/modeling_phi3.py,sha256=B7ovhGYoTHPdJ8zr4QmiDhJGzHh-fEoVy9yEpo8IBV4,70627
transformers/models/phobert/__init__.py,sha256=JDAAoG6FOpN1o5kgFBbHkoko9NsiioFi-ZAeAgR79nY,955
transformers/models/phobert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/phobert/__pycache__/tokenization_phobert.cpython-310.pyc,,
transformers/models/phobert/tokenization_phobert.py,sha256=nAFBv2fjPBCZ1r8z1Cle5WrhOeTGyvykavOim8Hat70,13091
transformers/models/pix2struct/__init__.py,sha256=XWzS791vtHNvjfyvFmES5k4t3017GzvIN_6GPwG2_Xw,2493
transformers/models/pix2struct/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/configuration_pix2struct.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/convert_pix2struct_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/image_processing_pix2struct.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/modeling_pix2struct.cpython-310.pyc,,
transformers/models/pix2struct/__pycache__/processing_pix2struct.cpython-310.pyc,,
transformers/models/pix2struct/configuration_pix2struct.py,sha256=EYHO8znXYMwxsJFEA5aVdsB5C6-CD6vHpn0JXo4pzK0,17288
transformers/models/pix2struct/convert_pix2struct_original_pytorch_to_hf.py,sha256=m_S-9oxyN4PQafRbWQIP-G0NUDrTqxOmr8IwiHNCOuU,5886
transformers/models/pix2struct/image_processing_pix2struct.py,sha256=JGjJuxIAYDk-02MYLT2ZZIk4HW095Wdzmdi96yQHoyE,19728
transformers/models/pix2struct/modeling_pix2struct.py,sha256=LXgryQiJy1mxt2VgCCku1TpgkvBlsW2a4BhrDl4eVU0,82649
transformers/models/pix2struct/processing_pix2struct.py,sha256=YFwg3KSy0SKXAkBucCTOwsOFSm7pFYj-M6bCViLYVqU,6960
transformers/models/plbart/__init__.py,sha256=yXgEZtshXavQ8--GOfh3sRPoYU8US_qdRxia8FhJ4H0,2253
transformers/models/plbart/__pycache__/__init__.cpython-310.pyc,,
transformers/models/plbart/__pycache__/configuration_plbart.cpython-310.pyc,,
transformers/models/plbart/__pycache__/convert_plbart_original_checkpoint_to_torch.cpython-310.pyc,,
transformers/models/plbart/__pycache__/modeling_plbart.cpython-310.pyc,,
transformers/models/plbart/__pycache__/tokenization_plbart.cpython-310.pyc,,
transformers/models/plbart/configuration_plbart.py,sha256=C6cS2yNel6oJy4U9y682ZAmcDqcC2IGeZj3i5Kj_PIo,8503
transformers/models/plbart/convert_plbart_original_checkpoint_to_torch.py,sha256=BOXNudNSr1xevmHnvNpa_4ya3Q89m6J4lndQhCWSLB8,3553
transformers/models/plbart/modeling_plbart.py,sha256=rLlLz3_MIDJZN95nZJ1hoz9O0QuLDr_VBHLdxQDcgqM,85070
transformers/models/plbart/tokenization_plbart.py,sha256=GE-X-wTX7ML8tS8_-9SjfPSUs4fkq5gRt_6hOyGqbnk,18746
transformers/models/poolformer/__init__.py,sha256=EL5af3hvwpyXCzs_rNTnADz448Naku5CRiLaE-qWW94,2378
transformers/models/poolformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/configuration_poolformer.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/convert_poolformer_original_to_pytorch.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/feature_extraction_poolformer.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/image_processing_poolformer.cpython-310.pyc,,
transformers/models/poolformer/__pycache__/modeling_poolformer.cpython-310.pyc,,
transformers/models/poolformer/configuration_poolformer.py,sha256=4AgFouKSNFulZLh-0Wbgdd4bHV8y4Jlfh29HbFnb6Ys,5575
transformers/models/poolformer/convert_poolformer_original_to_pytorch.py,sha256=Vvlp7ju7kr2sg1NdXKma6vYGABjs4sVhPKhgFKPJRpk,7947
transformers/models/poolformer/feature_extraction_poolformer.py,sha256=KDL4tg7hxwzQKYmGc6jMZfzeD9UCTb00oNfbejIjzmk,1214
transformers/models/poolformer/image_processing_poolformer.py,sha256=fObDfm06UHCQ3fl6JeLmKM-UREdAiV5RPlwO4aYCaCQ,18325
transformers/models/poolformer/modeling_poolformer.py,sha256=Nzw2Ao80Jz9q8XQzqWgdOubHCV8kmsxlbk1mZXpmn1M,17780
transformers/models/pop2piano/__init__.py,sha256=6mtBPEtxQ047_pZaH3xnfbRiry7VRzw-GkjTy6u-aTg,3631
transformers/models/pop2piano/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/configuration_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/convert_pop2piano_weights_to_hf.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/feature_extraction_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/modeling_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/processing_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/__pycache__/tokenization_pop2piano.cpython-310.pyc,,
transformers/models/pop2piano/configuration_pop2piano.py,sha256=CSk7dDvdmcNog_5xwUIbSgcSW_ZOy5bAMTPt4h2H3mo,5927
transformers/models/pop2piano/convert_pop2piano_weights_to_hf.py,sha256=5B4ARCF8lCWeW2fsgNe0lgy5nmYvLmNPQFyg5O3kj-A,8624
transformers/models/pop2piano/feature_extraction_pop2piano.py,sha256=eHyA7sjP-tkY4QXHTGYl83RCnCEoNgcQ9Tf6rSd9Pb8,19838
transformers/models/pop2piano/modeling_pop2piano.py,sha256=GGyL9uOne3q8aeQbXjQhM1hREA6zxUg5CxSb21HuIYA,65517
transformers/models/pop2piano/processing_pop2piano.py,sha256=QmobKR_Z3Ro_6t12TXMaileqUH1lAjGVY6n2wOevzwY,5524
transformers/models/pop2piano/tokenization_pop2piano.py,sha256=Y3grUs2_4YvgUDxDAhe4hBBJe0RyAZq_ofx11jw1M5A,32677
transformers/models/prophetnet/__init__.py,sha256=5w4pH7Xx3gp9dCSy4OWgfoR66vUEGXilZ2VMnNMNHas,1965
transformers/models/prophetnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/prophetnet/__pycache__/configuration_prophetnet.cpython-310.pyc,,
transformers/models/prophetnet/__pycache__/convert_prophetnet_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/prophetnet/__pycache__/modeling_prophetnet.cpython-310.pyc,,
transformers/models/prophetnet/__pycache__/tokenization_prophetnet.cpython-310.pyc,,
transformers/models/prophetnet/configuration_prophetnet.py,sha256=dSnqTxvccVfqBf2HtCA8e-m0nJ9q2i8gRXynaZmlvwA,8870
transformers/models/prophetnet/convert_prophetnet_original_pytorch_checkpoint_to_pytorch.py,sha256=NsV4OQ2M5Qmg-RzBOhW588PP2zMJADXwcE1vFIA9FPE,7054
transformers/models/prophetnet/modeling_prophetnet.py,sha256=yWppy5Jz6jqJ4jM-_hRdM29hPLFjhRayfzW7gIG9Lls,115355
transformers/models/prophetnet/tokenization_prophetnet.py,sha256=5vc6UgMSkJlybAN5nDfDCeqxkItxl-1RUFsWLfX0LPg,20874
transformers/models/pvt/__init__.py,sha256=ncmEQQxrm_BMJIy2avwU_aL4OEHetwJVa3jUzn18X9c,2220
transformers/models/pvt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pvt/__pycache__/configuration_pvt.cpython-310.pyc,,
transformers/models/pvt/__pycache__/convert_pvt_to_pytorch.cpython-310.pyc,,
transformers/models/pvt/__pycache__/image_processing_pvt.cpython-310.pyc,,
transformers/models/pvt/__pycache__/modeling_pvt.cpython-310.pyc,,
transformers/models/pvt/configuration_pvt.py,sha256=xrEwEjEP775TfGbVVs3OhEI-PwHbbjHsLHTSgSoT00o,6919
transformers/models/pvt/convert_pvt_to_pytorch.py,sha256=BLoYbECmvvKnWQQqMjM3zlm8lMjYc6L8xrcYwionges,9737
transformers/models/pvt/image_processing_pvt.py,sha256=dRcMJCdWkBPZek4hG6gbJ2zyDGRBWbpEGm4caGJZAIc,14267
transformers/models/pvt/modeling_pvt.py,sha256=-JIS80kqpahED6Tkia4lGEL-WDv_P6WFvfbDp07tGYA,28281
transformers/models/pvt_v2/__init__.py,sha256=juUzRcgqzQAI5MHUbyhwB3lIeQeTk05FR9n3YQFWAQo,1832
transformers/models/pvt_v2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/pvt_v2/__pycache__/configuration_pvt_v2.cpython-310.pyc,,
transformers/models/pvt_v2/__pycache__/convert_pvt_v2_to_pytorch.cpython-310.pyc,,
transformers/models/pvt_v2/__pycache__/modeling_pvt_v2.cpython-310.pyc,,
transformers/models/pvt_v2/configuration_pvt_v2.py,sha256=-vu-7Yg-hVJ6S5FSyqKTqX8M7w0cDO_S8k2UEKYQy6c,7963
transformers/models/pvt_v2/convert_pvt_v2_to_pytorch.py,sha256=OqYTYB1bssEh4C-AwCFG0VDDcEWZa1Su5kUkrn_UcOo,12077
transformers/models/pvt_v2/modeling_pvt_v2.py,sha256=iQL_48n_xGDak3-7A2TDLixYEa8t-HCdzLkuBhVnlrw,29417
transformers/models/qwen2/__init__.py,sha256=-Ir_2m_K82OmbVv-jE8sFK3HKDm_1cJH8gr3gOhjOSQ,2358
transformers/models/qwen2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/qwen2/__pycache__/configuration_qwen2.cpython-310.pyc,,
transformers/models/qwen2/__pycache__/modeling_qwen2.cpython-310.pyc,,
transformers/models/qwen2/__pycache__/tokenization_qwen2.cpython-310.pyc,,
transformers/models/qwen2/__pycache__/tokenization_qwen2_fast.cpython-310.pyc,,
transformers/models/qwen2/configuration_qwen2.py,sha256=6gJHK1YSRnqPua6HpvBUa05fczNcvMD_7qxkSpZ5Hgw,6664
transformers/models/qwen2/modeling_qwen2.py,sha256=SXVMLFEyvJCJi3LTjSgJV3HwKDhMO_P7VhwDY2joYGU,62345
transformers/models/qwen2/tokenization_qwen2.py,sha256=y9hRJ6oYYRa_4UyoQUPU_BlsrnTPKoEByiCQ3zelSmE,13913
transformers/models/qwen2/tokenization_qwen2_fast.py,sha256=NL0QjEs36hiJUo0yu6X3-kp74LAjioKyoJeqnxhdsY8,5182
transformers/models/qwen2_moe/__init__.py,sha256=a0Cc_ATUvzShfF6bBg4H7K_l_gSB6sS_D00zbrKKN4g,1833
transformers/models/qwen2_moe/__pycache__/__init__.cpython-310.pyc,,
transformers/models/qwen2_moe/__pycache__/configuration_qwen2_moe.cpython-310.pyc,,
transformers/models/qwen2_moe/__pycache__/modeling_qwen2_moe.cpython-310.pyc,,
transformers/models/qwen2_moe/configuration_qwen2_moe.py,sha256=dWuiftaNt_4Sk5SK-Hgy_NOnuRuCzxSfZry8IrzvuPo,8881
transformers/models/qwen2_moe/modeling_qwen2_moe.py,sha256=be0LgF6xhHVTrirlzReSwtOa8hZ6aSUeSlEvW5PFpX0,71958
transformers/models/rag/__init__.py,sha256=omMwtpcTWBHYKZvt8NIxbACHhICmYWfeTgiC7O4U88g,2426
transformers/models/rag/__pycache__/__init__.cpython-310.pyc,,
transformers/models/rag/__pycache__/configuration_rag.cpython-310.pyc,,
transformers/models/rag/__pycache__/modeling_rag.cpython-310.pyc,,
transformers/models/rag/__pycache__/modeling_tf_rag.cpython-310.pyc,,
transformers/models/rag/__pycache__/retrieval_rag.cpython-310.pyc,,
transformers/models/rag/__pycache__/tokenization_rag.cpython-310.pyc,,
transformers/models/rag/configuration_rag.py,sha256=LnkRTI4AmVfYpM8IVYiOPPmb3BYGSAAEZQ3AEpu3kOE,8337
transformers/models/rag/modeling_rag.py,sha256=yLhJaEvZdacjFgYPvpy0byE3sfWGP2Gvg8RvL-AsRlQ,86367
transformers/models/rag/modeling_tf_rag.py,sha256=ahXK9ChEuiUbGX41j7-r4WxQqyvXszSdin58md2qpk4,88805
transformers/models/rag/retrieval_rag.py,sha256=uLbdUiLB0uLPDnDG-1hRLZN8PbZvlpDtIteyAFg-sQg,29922
transformers/models/rag/tokenization_rag.py,sha256=DHvekTpL1KW_wItW_Tksiub5nHFn7qTwcDySpfyHmeE,4577
transformers/models/recurrent_gemma/__init__.py,sha256=gUE-KRPGeD_b-aklMGn9oDbnSdZ8twDOQXUxL2zWkIo,1708
transformers/models/recurrent_gemma/__pycache__/__init__.cpython-310.pyc,,
transformers/models/recurrent_gemma/__pycache__/configuration_recurrent_gemma.cpython-310.pyc,,
transformers/models/recurrent_gemma/__pycache__/convert_recurrent_gemma_to_hf.cpython-310.pyc,,
transformers/models/recurrent_gemma/__pycache__/modeling_recurrent_gemma.cpython-310.pyc,,
transformers/models/recurrent_gemma/configuration_recurrent_gemma.py,sha256=8EjkIeTbmy7LMDtl1g0lqhfqzCTTahRMerPJfQzfVzQ,7713
transformers/models/recurrent_gemma/convert_recurrent_gemma_to_hf.py,sha256=jZGkZ2FmNFWsZXz37gf86NjLRFbgLTK6C-ZO6-JChks,7965
transformers/models/recurrent_gemma/modeling_recurrent_gemma.py,sha256=eWsIMkuQj7UsDCLwNMup-gFAitwn3IPsiLu5gGljgKY,43234
transformers/models/reformer/__init__.py,sha256=dlDgyR3wG2nCJYg4S8x6lHZCxFUeGdtx0LK2KoQ5ydI,2955
transformers/models/reformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/reformer/__pycache__/configuration_reformer.cpython-310.pyc,,
transformers/models/reformer/__pycache__/convert_reformer_trax_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/reformer/__pycache__/modeling_reformer.cpython-310.pyc,,
transformers/models/reformer/__pycache__/tokenization_reformer.cpython-310.pyc,,
transformers/models/reformer/__pycache__/tokenization_reformer_fast.cpython-310.pyc,,
transformers/models/reformer/configuration_reformer.py,sha256=hYsqMbCaFDu7ZdPiQsi_IdTaKK5eXn2j45PSBWVZZvk,13165
transformers/models/reformer/convert_reformer_trax_checkpoint_to_pytorch.py,sha256=w6ykDyk5bIhrHAE5O_YtGa011VqdLsmI_GXNPzAcXd4,7817
transformers/models/reformer/modeling_reformer.py,sha256=J49Cx_wH3whJLWzmifvQ0e_PiWByDchrRbO8jzRGjBo,115419
transformers/models/reformer/tokenization_reformer.py,sha256=3NBWAU7B2kJwNnega3fB4Ys-E4VDKf57nffGvh0Prxw,6726
transformers/models/reformer/tokenization_reformer_fast.py,sha256=06LhqDifGmV9ZrpV0aSC9rFc5Rz3e896o4HMO5eVuUY,4245
transformers/models/regnet/__init__.py,sha256=TO3o_dEoG5M-5bbVo00Gseprgk71WQtquqEXFTezM-4,2888
transformers/models/regnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/regnet/__pycache__/configuration_regnet.cpython-310.pyc,,
transformers/models/regnet/__pycache__/convert_regnet_seer_10b_to_pytorch.cpython-310.pyc,,
transformers/models/regnet/__pycache__/convert_regnet_to_pytorch.cpython-310.pyc,,
transformers/models/regnet/__pycache__/modeling_flax_regnet.cpython-310.pyc,,
transformers/models/regnet/__pycache__/modeling_regnet.cpython-310.pyc,,
transformers/models/regnet/__pycache__/modeling_tf_regnet.cpython-310.pyc,,
transformers/models/regnet/configuration_regnet.py,sha256=qncSfSeJHffzN4kTtxwffJeWJNkaNXE_awMIShK0MuM,3945
transformers/models/regnet/convert_regnet_seer_10b_to_pytorch.py,sha256=PH1ePsaNgxZZu9YWob3FTbWZkxnLKoN9bcJCIVK2pYI,11754
transformers/models/regnet/convert_regnet_to_pytorch.py,sha256=kyfKbY-kwlaj-VsXnMZJZwaqZPbuyR-MU58lFDG9F_Y,18702
transformers/models/regnet/modeling_flax_regnet.py,sha256=2Ao7eODWcHufpZoNbGC4FbX6tZVE2bfWWrZSMbPGcMg,28410
transformers/models/regnet/modeling_regnet.py,sha256=zQr0tjypqtdoWZW-hGbsNQWCCoro0z7WlATrDoQgeM8,17687
transformers/models/regnet/modeling_tf_regnet.py,sha256=icuYegqG5RiaC3qajlzXd7AUCEY2BlnFyzOayukst7o,24300
transformers/models/rembert/__init__.py,sha256=TKbwvoTPIwEcR-OxwBZGRof0dhr8LaXMibjKWsu7H94,4222
transformers/models/rembert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/rembert/__pycache__/configuration_rembert.cpython-310.pyc,,
transformers/models/rembert/__pycache__/convert_rembert_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/rembert/__pycache__/modeling_rembert.cpython-310.pyc,,
transformers/models/rembert/__pycache__/modeling_tf_rembert.cpython-310.pyc,,
transformers/models/rembert/__pycache__/tokenization_rembert.cpython-310.pyc,,
transformers/models/rembert/__pycache__/tokenization_rembert_fast.cpython-310.pyc,,
transformers/models/rembert/configuration_rembert.py,sha256=-aRK_7SAzgP7lcdp-GajWLgSic9KhK_878GmZUbl_KQ,7240
transformers/models/rembert/convert_rembert_tf_checkpoint_to_pytorch.py,sha256=_FwapBBnk_xv1QPby_PGnFvIZfGe9vooclAUwU3Ve10,2207
transformers/models/rembert/modeling_rembert.py,sha256=8zH2MLc-1RcaC7mXd7YBcAKmUUFg6K4MIHXtw3Cx_Cw,68141
transformers/models/rembert/modeling_tf_rembert.py,sha256=aB0aDCKmebPHD5d2S-6mXjpNQJ14wzF-A5MFNz4jMn8,77681
transformers/models/rembert/tokenization_rembert.py,sha256=B8uwBMPsa6bsOzoip_7IZTITdplv2KuOzNp2UpBPd7I,10592
transformers/models/rembert/tokenization_rembert_fast.py,sha256=dzMtvv0EAC7HhM8MtlSvGow1-r5oTWrPhW0VACAgy6w,9995
transformers/models/resnet/__init__.py,sha256=40E1scjeQvfyDxwea_ToMN_QmBHgHmje44-3Jtk44cw,2930
transformers/models/resnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/resnet/__pycache__/configuration_resnet.cpython-310.pyc,,
transformers/models/resnet/__pycache__/convert_resnet_to_pytorch.cpython-310.pyc,,
transformers/models/resnet/__pycache__/modeling_flax_resnet.cpython-310.pyc,,
transformers/models/resnet/__pycache__/modeling_resnet.cpython-310.pyc,,
transformers/models/resnet/__pycache__/modeling_tf_resnet.cpython-310.pyc,,
transformers/models/resnet/configuration_resnet.py,sha256=Orw9QRpt_rNg-vPdOVZGsPZMschjyTGN6Amrc8LlDjU,6018
transformers/models/resnet/convert_resnet_to_pytorch.py,sha256=fVar-ifk_-_sENkZiTA93wIaEunPMInlp0mmDdH5KOQ,7286
transformers/models/resnet/modeling_flax_resnet.py,sha256=uJMz2FgVXm6ffwjiorCHkuPbCRra8VdN1vYILRuIgxY,24607
transformers/models/resnet/modeling_resnet.py,sha256=jM-MMRgicf-WROgzc85ZylO_VRjd893SF-IEe4mu7V0,19788
transformers/models/resnet/modeling_tf_resnet.py,sha256=buR7Tx6RRt4QHpRrRWwhET1cwZ-E1P3WQNHOQ0O8a_I,23650
transformers/models/roberta/__init__.py,sha256=dmIGqFO7JVd5GiDNTYdE7JQNP-db75ML424yh_t2jR8,4805
transformers/models/roberta/__pycache__/__init__.cpython-310.pyc,,
transformers/models/roberta/__pycache__/configuration_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/convert_roberta_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/roberta/__pycache__/modeling_flax_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/modeling_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/modeling_tf_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/tokenization_roberta.cpython-310.pyc,,
transformers/models/roberta/__pycache__/tokenization_roberta_fast.cpython-310.pyc,,
transformers/models/roberta/configuration_roberta.py,sha256=YPFSikBG6eFypgG1TQ4j96Bx9ZpuX18NIsmAgrz6NNg,7260
transformers/models/roberta/convert_roberta_original_pytorch_checkpoint_to_pytorch.py,sha256=5sX5PtUseHWXH78xTBXqGsrzn8YttrZsuN0E0H9CWi4,8001
transformers/models/roberta/modeling_flax_roberta.py,sha256=Bz5VgKKwWnVVmRFyHD11Ug7IlvgwOLIMbGI0lBkMHt8,56976
transformers/models/roberta/modeling_roberta.py,sha256=fZz1xdFEKo0DHPLfbCJdKSPmVdE8T--UdVs84ZZzNpM,71250
transformers/models/roberta/modeling_tf_roberta.py,sha256=EgKfpBE8VeXfuIkP2QEwtZloOra-yub8X_QrzxxOTaw,79875
transformers/models/roberta/tokenization_roberta.py,sha256=4Ft2MWhG4ESEO2yJ0a_8jaUU98IxIH2oLaorhotpC1w,16451
transformers/models/roberta/tokenization_roberta_fast.py,sha256=O3W9P8i_5m-TmRuKf-egQgPrTkoOHeO15BbHW6A1lyc,11423
transformers/models/roberta_prelayernorm/__init__.py,sha256=vTJRMqCxWTYlYx_Mj59lZFIbKSgCeYHyDoCseysFgjM,5011
transformers/models/roberta_prelayernorm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/configuration_roberta_prelayernorm.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/convert_roberta_prelayernorm_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_flax_roberta_prelayernorm.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_roberta_prelayernorm.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/__pycache__/modeling_tf_roberta_prelayernorm.cpython-310.pyc,,
transformers/models/roberta_prelayernorm/configuration_roberta_prelayernorm.py,sha256=ExP5e6JYnDgbZUYwm6LXzE4A58pyxTx-XFLSYGZNRgQ,7808
transformers/models/roberta_prelayernorm/convert_roberta_prelayernorm_original_pytorch_checkpoint_to_pytorch.py,sha256=DWhNuDUo_dZsWSjJIPzNqxAY9KzWlAWnpgNxE88qpWQ,2974
transformers/models/roberta_prelayernorm/modeling_flax_roberta_prelayernorm.py,sha256=k83Cf_FpzLguXIHtJJSMpj6yuVzy_GddFDpukVmjscI,60537
transformers/models/roberta_prelayernorm/modeling_roberta_prelayernorm.py,sha256=3Of_smh_EEikMk4Sfn9zMGt8j8Xtbl9W9luQCvYH70I,73677
transformers/models/roberta_prelayernorm/modeling_tf_roberta_prelayernorm.py,sha256=ITLJNojw7RSiX2cYlUwcZrkbMkzSvNl_AHBaWUE6Yks,83040
transformers/models/roc_bert/__init__.py,sha256=w2OI8OfSZd4zSlfMFm1f4uy5WMZkD09SBjoZdkql9n0,2691
transformers/models/roc_bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/roc_bert/__pycache__/configuration_roc_bert.cpython-310.pyc,,
transformers/models/roc_bert/__pycache__/modeling_roc_bert.cpython-310.pyc,,
transformers/models/roc_bert/__pycache__/tokenization_roc_bert.cpython-310.pyc,,
transformers/models/roc_bert/configuration_roc_bert.py,sha256=baWRtDQNSYg14hirr7V0BX43sb_9W1dG-VKc9CphJ0k,8498
transformers/models/roc_bert/modeling_roc_bert.py,sha256=h9kt7tbQ0ECY8mI6ap8pmLGRE4BZT2S8KXWO-YDwoJE,93182
transformers/models/roc_bert/tokenization_roc_bert.py,sha256=PTfS5M-zkr_j65IkmdDcTdrcFiyP2J2VSIpCNoFB2vM,50197
transformers/models/roformer/__init__.py,sha256=dkJYDhebvEE_ZktrSP9dOVqvn3Y7phvG0Zfahzjmwpk,4929
transformers/models/roformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/roformer/__pycache__/configuration_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/convert_roformer_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/roformer/__pycache__/modeling_flax_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/modeling_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/modeling_tf_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/tokenization_roformer.cpython-310.pyc,,
transformers/models/roformer/__pycache__/tokenization_roformer_fast.cpython-310.pyc,,
transformers/models/roformer/__pycache__/tokenization_utils.cpython-310.pyc,,
transformers/models/roformer/configuration_roformer.py,sha256=GhBdfvr7q94pkaHshseO4WQ65xf_ZLlVC4uOpbAlcME,6803
transformers/models/roformer/convert_roformer_original_tf_checkpoint_to_pytorch.py,sha256=TS6-r9GV2DJXof9onFihBgvXXdn08al0_kJutS8cwEQ,2239
transformers/models/roformer/modeling_flax_roformer.py,sha256=vtWuZyPBfTzBhlVjzJtembSDTogwYO_Am9GxjPhlvd4,39099
transformers/models/roformer/modeling_roformer.py,sha256=lkWpK4pj3ggdpXJVCQZRN4_GEIHQECJFrnnsELbWwNw,69305
transformers/models/roformer/modeling_tf_roformer.py,sha256=XkmJaRSmPdOQc3q5q3029iWlmwdDu9YUr5OrUiHnLs0,65913
transformers/models/roformer/tokenization_roformer.py,sha256=k0JWRQlo_yDxGQ7AvvHVVbMA--Q8fCdGJW-5F38TsC0,21993
transformers/models/roformer/tokenization_roformer_fast.py,sha256=5vmo29dE7bH0GyLxDc_cMhuZ37ZvW3_iX2EE7xSyQfs,6679
transformers/models/roformer/tokenization_utils.py,sha256=0ciH13qW2kCa5my1rPwfwAuSXX-jGzN0nzemvGvOBxw,2652
transformers/models/rt_detr/__init__.py,sha256=xfmcPdTDU5so1PRqVx-Ink2NaZ_NLmyDbyKxXZ7Yn3U,2451
transformers/models/rt_detr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/rt_detr/__pycache__/configuration_rt_detr.cpython-310.pyc,,
transformers/models/rt_detr/__pycache__/configuration_rt_detr_resnet.cpython-310.pyc,,
transformers/models/rt_detr/__pycache__/convert_rt_detr_original_pytorch_checkpoint_to_hf.cpython-310.pyc,,
transformers/models/rt_detr/__pycache__/image_processing_rt_detr.cpython-310.pyc,,
transformers/models/rt_detr/__pycache__/modeling_rt_detr.cpython-310.pyc,,
transformers/models/rt_detr/__pycache__/modeling_rt_detr_resnet.cpython-310.pyc,,
transformers/models/rt_detr/configuration_rt_detr.py,sha256=LCHY1oYxYA93LvCdhT2t8dosrjWVlXM95mgnjUAN6ng,17772
transformers/models/rt_detr/configuration_rt_detr_resnet.py,sha256=8jIlVh_RXW-oTbiMJQU9EFq3hEwuDbyOpsTG1H1OHgk,5522
transformers/models/rt_detr/convert_rt_detr_original_pytorch_checkpoint_to_hf.py,sha256=L2tEnPp4NNJlXsI-fBBpib0Vg76qL3xN-OLvrd429Jo,32779
transformers/models/rt_detr/image_processing_rt_detr.py,sha256=hFe5mODql_KseClWjyRj03qZ8LDgs6YaDssQb8dkhWU,52240
transformers/models/rt_detr/modeling_rt_detr.py,sha256=pUchSabB7MZK0ODB6p8l822BranqQLOuxtf2x6-dV78,129457
transformers/models/rt_detr/modeling_rt_detr_resnet.py,sha256=cCEG9jMK8qh917EvaJmRux0Hpwuqiw4er3Gl1h2bIHc,16383
transformers/models/rwkv/__init__.py,sha256=tMP2bqMTiU6OvXfECe_AOOepRs_6lDHmNsbbnam7ly0,1612
transformers/models/rwkv/__pycache__/__init__.cpython-310.pyc,,
transformers/models/rwkv/__pycache__/configuration_rwkv.cpython-310.pyc,,
transformers/models/rwkv/__pycache__/convert_rwkv_checkpoint_to_hf.cpython-310.pyc,,
transformers/models/rwkv/__pycache__/modeling_rwkv.cpython-310.pyc,,
transformers/models/rwkv/configuration_rwkv.py,sha256=b3jryVZOsrARcWbgqXW0mg-vuwrcDkXj70ouJu-aczo,5176
transformers/models/rwkv/convert_rwkv_checkpoint_to_hf.py,sha256=V6l5MkYhQisv7bA4LUb966KDGSAq48dk2VKBie_iSDs,6993
transformers/models/rwkv/modeling_rwkv.py,sha256=vuFkV5DQK5obhR6b9NAP3WISGV5vnnBUyF6jtoxD6Dk,36798
transformers/models/sam/__init__.py,sha256=lx31A1rBHuy1QsPWGvxULXzHmTiFMS3Ow2UCMhrcqN0,2726
transformers/models/sam/__pycache__/__init__.cpython-310.pyc,,
transformers/models/sam/__pycache__/configuration_sam.cpython-310.pyc,,
transformers/models/sam/__pycache__/convert_sam_to_hf.cpython-310.pyc,,
transformers/models/sam/__pycache__/image_processing_sam.cpython-310.pyc,,
transformers/models/sam/__pycache__/modeling_sam.cpython-310.pyc,,
transformers/models/sam/__pycache__/modeling_tf_sam.cpython-310.pyc,,
transformers/models/sam/__pycache__/processing_sam.cpython-310.pyc,,
transformers/models/sam/configuration_sam.py,sha256=_tMyQWQbBsdstkiRpz7TEFA-z8NACH597Ls0s-55dCs,13761
transformers/models/sam/convert_sam_to_hf.py,sha256=Ter22qOhmN1eQyKi387HL0KCBrirnPnr1asU4BobQkk,8543
transformers/models/sam/image_processing_sam.py,sha256=VQ1-UC_qL0IdRAFM9AswU5GUsHQux9WZFWplQRwhp_4,67314
transformers/models/sam/modeling_sam.py,sha256=ozfOdGE5tYkSQqTifgnlREyq2ROOvbNVzWn9HDnUmKE,64695
transformers/models/sam/modeling_tf_sam.py,sha256=Q5B3rVJ0jEVzGIG5gLDveUQquzyiqx3VMkRaVbyaVbA,75450
transformers/models/sam/processing_sam.py,sha256=UthL1YhtKbS_hnLUoWFEZoZIE2oOqeBGPD5RuPyR-z8,10931
transformers/models/seamless_m4t/__init__.py,sha256=OQWV0sb2N7e3FZpMt9jHKMjYmljFaoIfE6LjYhTzEzw,3506
transformers/models/seamless_m4t/__pycache__/__init__.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/configuration_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/convert_fairseq2_to_hf.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/feature_extraction_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/modeling_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/processing_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/tokenization_seamless_m4t.cpython-310.pyc,,
transformers/models/seamless_m4t/__pycache__/tokenization_seamless_m4t_fast.cpython-310.pyc,,
transformers/models/seamless_m4t/configuration_seamless_m4t.py,sha256=wAX2u-yO1N2G_gkdu4fe7hy3hxxwSKVx-1kZ0BRzX2Y,23463
transformers/models/seamless_m4t/convert_fairseq2_to_hf.py,sha256=AQ75kQXD0Yv1Xn5y_mf3FhrHShAkO66wqg_Y-o93qy4,15958
transformers/models/seamless_m4t/feature_extraction_seamless_m4t.py,sha256=pSStJq6iPGHLWGDiIWN-ZuGBmYSbTkT2ISrFK7Bj7W8,13561
transformers/models/seamless_m4t/modeling_seamless_m4t.py,sha256=VK4Z8mK2P1mA29lTlRsBEhR5ZSSAkktbGs-xkIQ6aeg,202357
transformers/models/seamless_m4t/processing_seamless_m4t.py,sha256=OrPvDJkAAIuoWglyxt1Z4H993tm-AyX3OxDcu4Gmps0,5893
transformers/models/seamless_m4t/tokenization_seamless_m4t.py,sha256=vtccvp928RIRKpuPxLKjCZdrDG82X7ZSEF5uBdhCC2Q,26000
transformers/models/seamless_m4t/tokenization_seamless_m4t_fast.py,sha256=_KTkh1y6aGAHY4GO5J5ATqqZJYCvyelMRczfoMjH3oA,19885
transformers/models/seamless_m4t_v2/__init__.py,sha256=cPa9YAy0a2YBBLpS08YZA6tOcy3kLDpy8nRrUqnt2VU,1947
transformers/models/seamless_m4t_v2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/configuration_seamless_m4t_v2.cpython-310.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/convert_fairseq2_to_hf.cpython-310.pyc,,
transformers/models/seamless_m4t_v2/__pycache__/modeling_seamless_m4t_v2.cpython-310.pyc,,
transformers/models/seamless_m4t_v2/configuration_seamless_m4t_v2.py,sha256=-BmLD5-GE4f4U1uz9e63GDSAqpxnOIosdEqXXQDTKP8,24320
transformers/models/seamless_m4t_v2/convert_fairseq2_to_hf.py,sha256=3fuW2IGoJpNKVGCE9YXrWbXZvdoko6cfQLyLMn5wycg,15082
transformers/models/seamless_m4t_v2/modeling_seamless_m4t_v2.py,sha256=rr5a4HI2MS57CCv8PCaIZHejNsFYZc8UMdP5Ek1z8Dw,228745
transformers/models/segformer/__init__.py,sha256=t4nnS6zRMt_WWrULCuLLlTsywy3qsOU0BpP-KBklOPI,3372
transformers/models/segformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/segformer/__pycache__/configuration_segformer.cpython-310.pyc,,
transformers/models/segformer/__pycache__/convert_segformer_original_to_pytorch.cpython-310.pyc,,
transformers/models/segformer/__pycache__/feature_extraction_segformer.cpython-310.pyc,,
transformers/models/segformer/__pycache__/image_processing_segformer.cpython-310.pyc,,
transformers/models/segformer/__pycache__/modeling_segformer.cpython-310.pyc,,
transformers/models/segformer/__pycache__/modeling_tf_segformer.cpython-310.pyc,,
transformers/models/segformer/configuration_segformer.py,sha256=xXEa-6pfoWJUJH_Pj_NpwH22PVejPFzKqhb9aI-meCc,7365
transformers/models/segformer/convert_segformer_original_to_pytorch.py,sha256=JxOpEDBJ_IQHNlvbXADlMg9PBQwPdSgI4ybc8kj2hPY,17091
transformers/models/segformer/feature_extraction_segformer.py,sha256=yaRckmbmTyh1Oow3PnHLsjW4MURaWqddhTzG-PVcywk,1207
transformers/models/segformer/image_processing_segformer.py,sha256=1-1iT14G11Nft3koeh3nGdzlW1K1XUUGuDgG6dJUFK4,22744
transformers/models/segformer/modeling_segformer.py,sha256=hD_5jKL8a2c_Ec_kr0OLxBhYKgtYphNxHY1KNiXlVYY,35352
transformers/models/segformer/modeling_tf_segformer.py,sha256=PnPn8OXgaJ84Ga_XnWM-85Rig2KT1_9RF-KwqAn-IQk,43610
transformers/models/seggpt/__init__.py,sha256=E_Pm_8e-v0tEwe5EJuMOPW9frmS1Jd-4R9e2EJ0ISNY,2102
transformers/models/seggpt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/seggpt/__pycache__/configuration_seggpt.cpython-310.pyc,,
transformers/models/seggpt/__pycache__/convert_seggpt_to_hf.cpython-310.pyc,,
transformers/models/seggpt/__pycache__/image_processing_seggpt.cpython-310.pyc,,
transformers/models/seggpt/__pycache__/modeling_seggpt.cpython-310.pyc,,
transformers/models/seggpt/configuration_seggpt.py,sha256=HuZd7Rk_Tp71kEbi_yrgQyTk6p2etcw9wEGhP1lJvzM,6463
transformers/models/seggpt/convert_seggpt_to_hf.py,sha256=BZLBrTnCLMLgbcRZbUk8sflo1R_jpMS2Ru90GwpQtxw,9778
transformers/models/seggpt/image_processing_seggpt.py,sha256=1TUOWkQy5b7z1I9an_379vC-s7qM7VHJ15f6AcQ10Cc,31472
transformers/models/seggpt/modeling_seggpt.py,sha256=nukR-x90GXHgrn_6YEikWa5xjGB-rZVtM1RXNHln0y4,45636
transformers/models/sew/__init__.py,sha256=4bFyAwCuucy4NQ_QrtlPwi-K-jmAvU7-OmQEWg2Htes,1614
transformers/models/sew/__pycache__/__init__.cpython-310.pyc,,
transformers/models/sew/__pycache__/configuration_sew.cpython-310.pyc,,
transformers/models/sew/__pycache__/convert_sew_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/sew/__pycache__/modeling_sew.cpython-310.pyc,,
transformers/models/sew/configuration_sew.py,sha256=iACEkWqbDjre1GtdxXLDQmJ0PbWB2h5BEZQQ9qoxFA4,14181
transformers/models/sew/convert_sew_original_pytorch_checkpoint_to_pytorch.py,sha256=SOWT4r47np8zJwW6fdWqtgCfuQAHjXUVUgMHZ6STCDA,12744
transformers/models/sew/modeling_sew.py,sha256=BVZMt0_xcCfxOWnI-dObngNO0lmNT_XT2ygvZwY_wZ4,66946
transformers/models/sew_d/__init__.py,sha256=jqzaSuSywiyUNzonA2Heh5TFVrIaBoxvu2o3TVGIGSI,1632
transformers/models/sew_d/__pycache__/__init__.cpython-310.pyc,,
transformers/models/sew_d/__pycache__/configuration_sew_d.cpython-310.pyc,,
transformers/models/sew_d/__pycache__/convert_sew_d_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/sew_d/__pycache__/modeling_sew_d.cpython-310.pyc,,
transformers/models/sew_d/configuration_sew_d.py,sha256=tcwJ607_TuIlYz5wcoAfbKQ4-AIp2_lxgPP8CCcDJr4,16148
transformers/models/sew_d/convert_sew_d_original_pytorch_checkpoint_to_pytorch.py,sha256=5Cy_FfZr1-7Kgmu8ipmETQt33BTyPFjX93tuaPV_tRw,13574
transformers/models/sew_d/modeling_sew_d.py,sha256=BQHVVZOLLXzNVmmtISg_M4j3haebBtKE9C51Chw-Fa0,73906
transformers/models/siglip/__init__.py,sha256=EqNmxsDysOinTc26ViLXvWcaPjrG-UvNY1n24H5fd00,2932
transformers/models/siglip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/siglip/__pycache__/configuration_siglip.cpython-310.pyc,,
transformers/models/siglip/__pycache__/convert_siglip_to_hf.cpython-310.pyc,,
transformers/models/siglip/__pycache__/image_processing_siglip.cpython-310.pyc,,
transformers/models/siglip/__pycache__/modeling_siglip.cpython-310.pyc,,
transformers/models/siglip/__pycache__/processing_siglip.cpython-310.pyc,,
transformers/models/siglip/__pycache__/tokenization_siglip.cpython-310.pyc,,
transformers/models/siglip/configuration_siglip.py,sha256=nEQScf4Qkb6BpnXkKP4LnL1lwqJiQsyn4KA2ZhG3GXs,13525
transformers/models/siglip/convert_siglip_to_hf.py,sha256=JWlMzjGcS_OgIE7KGlVX1yWQJsVpWG7X3rLCoXkUJ90,20829
transformers/models/siglip/image_processing_siglip.py,sha256=eLaKYiyDnDiy3ManIf8WxF9WFRgedyFkeg4XtUFpS4c,12383
transformers/models/siglip/modeling_siglip.py,sha256=vyaYITG36M--kL3bITnc8YL9eTtZujhIR1gUc5qL748,69226
transformers/models/siglip/processing_siglip.py,sha256=x5A9CKyzNzOF0udXvMVQ4hMFBCbAdH-WnLAXqop75zk,7302
transformers/models/siglip/tokenization_siglip.py,sha256=vJYlCDDT8g5GR_hIc33Ztc8941kga42tktBdZM0AvY0,15952
transformers/models/speech_encoder_decoder/__init__.py,sha256=987NzBteEbQy0IYY43B_JKolw2BbyX6Ox9s__xH0daQ,2037
transformers/models/speech_encoder_decoder/__pycache__/__init__.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/configuration_speech_encoder_decoder.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/convert_mbart_wav2vec2_seq2seq_original_to_pytorch.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/convert_speech_to_text_wav2vec2_seq2seq_original_to_pytorch.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/modeling_flax_speech_encoder_decoder.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/__pycache__/modeling_speech_encoder_decoder.cpython-310.pyc,,
transformers/models/speech_encoder_decoder/configuration_speech_encoder_decoder.py,sha256=7hzCE73LcHbiq3b4pTsMdSwjtl4izOtoZE-ldVs8Bx4,4575
transformers/models/speech_encoder_decoder/convert_mbart_wav2vec2_seq2seq_original_to_pytorch.py,sha256=T7TlN5DEbuMVgAtK0xwljBOEGjHGTzZ6Ndj7V-XVScA,14753
transformers/models/speech_encoder_decoder/convert_speech_to_text_wav2vec2_seq2seq_original_to_pytorch.py,sha256=wyaXE2ICf7YBoaJxi4W4a9gbxjGW7zwpdOxJCPBsLHA,11970
transformers/models/speech_encoder_decoder/modeling_flax_speech_encoder_decoder.py,sha256=a46D8AZ5BOcq7j5gnTmlyCYT9Pm0pmcIU9-RL99Xwww,44642
transformers/models/speech_encoder_decoder/modeling_speech_encoder_decoder.py,sha256=DVQ3DGfn3r9DDNYu9sNH-osgjYezgwru-mBTFo0waTI,32408
transformers/models/speech_to_text/__init__.py,sha256=etRxtayhPA4oGlVEQI6x2nXD-x-uQxH9FxQ5g8kPw8E,3163
transformers/models/speech_to_text/__pycache__/__init__.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/configuration_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/convert_s2t_fairseq_to_tfms.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/feature_extraction_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/modeling_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/modeling_tf_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/processing_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/__pycache__/tokenization_speech_to_text.cpython-310.pyc,,
transformers/models/speech_to_text/configuration_speech_to_text.py,sha256=MYjnDsybG3WziBxl3xinpNRcriUdNccCaoz18K1WxVA,9775
transformers/models/speech_to_text/convert_s2t_fairseq_to_tfms.py,sha256=v-5aSPwuCKCtqwU8gREj9wA2nm14Z97tg6wQ3S47gos,4478
transformers/models/speech_to_text/feature_extraction_speech_to_text.py,sha256=bW4mXxoo1FKXFhfvstyPbWm8fMRMN1G7KXwkGN-vdxw,13176
transformers/models/speech_to_text/modeling_speech_to_text.py,sha256=AjWYCGnWYd3lsTKfYm69K0B0V4bytTXI0UrlcpO7y70,64398
transformers/models/speech_to_text/modeling_tf_speech_to_text.py,sha256=qWuPI6X_dS452tVe19ImwhksKj39txcoJqxzMdIYbO4,74312
transformers/models/speech_to_text/processing_speech_to_text.py,sha256=lSRyp08-pwXHmGsuMK_c_1Fj9NuL3u82QtHK8EvpSOE,4819
transformers/models/speech_to_text/tokenization_speech_to_text.py,sha256=SsUbAjjZZm8SzbToKOxIkI-UX3rHyHRAjTwYaEMq_CE,11401
transformers/models/speecht5/__init__.py,sha256=wj_1b9q7LXw_6Yuojtk5cJmM94OatTr9O_31EiLhui0,2657
transformers/models/speecht5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/configuration_speecht5.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/convert_hifigan.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/convert_speecht5_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/feature_extraction_speecht5.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/modeling_speecht5.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/number_normalizer.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/processing_speecht5.cpython-310.pyc,,
transformers/models/speecht5/__pycache__/tokenization_speecht5.cpython-310.pyc,,
transformers/models/speecht5/configuration_speecht5.py,sha256=10zI3UsOS2LeSdqU7XoPz1sZssUvN67NMyY-xnUTDM8,23378
transformers/models/speecht5/convert_hifigan.py,sha256=CL9GSX_bimjm_hU2rE55MaNvTUjTtWD6qCtqNMaXy7I,4241
transformers/models/speecht5/convert_speecht5_original_pytorch_checkpoint_to_pytorch.py,sha256=AyAjaeibe3002YZRT2maq1Yi8-iP1j7Ahs5qxYMjiJ0,17194
transformers/models/speecht5/feature_extraction_speecht5.py,sha256=lcKx3NaIXx0PGITRKP0kA8SZK75kd1Sn8PNHLBn-ST0,17809
transformers/models/speecht5/modeling_speecht5.py,sha256=hGFQGKFTFlpvEAMF-DLiTyhi3AlX7oaUg0YAKqH6CFk,153986
transformers/models/speecht5/number_normalizer.py,sha256=cxnEUdHSISW5eAo15cLuVkZa65zMFuMFaJ8zAOQCsAA,7019
transformers/models/speecht5/processing_speecht5.py,sha256=smqFdqKJQp9Vm1FDfmj7EvJeAZKSPB6u2AZMfsjsQa0,7562
transformers/models/speecht5/tokenization_speecht5.py,sha256=bSBbTWN-2yTgkT-IlmlGbzJ9fVyonH5y-OuOA-mNjK0,8912
transformers/models/splinter/__init__.py,sha256=dQmgY1NCNXi8cHdeszUdXje1pSGdTRxsJufCL3wZVIA,2348
transformers/models/splinter/__pycache__/__init__.cpython-310.pyc,,
transformers/models/splinter/__pycache__/configuration_splinter.cpython-310.pyc,,
transformers/models/splinter/__pycache__/modeling_splinter.cpython-310.pyc,,
transformers/models/splinter/__pycache__/tokenization_splinter.cpython-310.pyc,,
transformers/models/splinter/__pycache__/tokenization_splinter_fast.cpython-310.pyc,,
transformers/models/splinter/configuration_splinter.py,sha256=-fn94zHBMtHYNTCbCLXLiQ3JgODMvSBovYowM98VW5M,5594
transformers/models/splinter/modeling_splinter.py,sha256=7nI5DGeYKLdla98yu-81jntqA2oTzvvFEFHMnSWAR2Y,53299
transformers/models/splinter/tokenization_splinter.py,sha256=6y_XSErTLNRpI2faZTsiA5AJ6K-kOJVuhtUKv0flhvY,20920
transformers/models/splinter/tokenization_splinter_fast.py,sha256=t-gbV9OTlANeZQ_XLiV5GYpp9qZW9i7VllaLKf47ztI,8565
transformers/models/squeezebert/__init__.py,sha256=bp45xndCYIUnwmiLsbse3SMxx6qACtyGBHBELrhekgA,2784
transformers/models/squeezebert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/squeezebert/__pycache__/configuration_squeezebert.cpython-310.pyc,,
transformers/models/squeezebert/__pycache__/modeling_squeezebert.cpython-310.pyc,,
transformers/models/squeezebert/__pycache__/tokenization_squeezebert.cpython-310.pyc,,
transformers/models/squeezebert/__pycache__/tokenization_squeezebert_fast.cpython-310.pyc,,
transformers/models/squeezebert/configuration_squeezebert.py,sha256=Hw3_FthVdI0SFkx5XTWRoW-IKRRKVR-z83fByxpJlCQ,7244
transformers/models/squeezebert/modeling_squeezebert.py,sha256=1dL1pCmHMybCEgHQV451UTHelfApjgR6xasjT4hyTj8,45053
transformers/models/squeezebert/tokenization_squeezebert.py,sha256=jTVxnrL-DWDzP7VopDyagQlSN161QuIto05TyUk9-z0,20893
transformers/models/squeezebert/tokenization_squeezebert_fast.py,sha256=J22q1PJ-qa7ymcvvpmiX2ft2OxUDHi2Gdiny4rOiOZM,7819
transformers/models/stablelm/__init__.py,sha256=rly3o76iQXhys33WzZBj8yHAIAVUvMkNr1-HYteGqb4,1828
transformers/models/stablelm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/stablelm/__pycache__/configuration_stablelm.cpython-310.pyc,,
transformers/models/stablelm/__pycache__/modeling_stablelm.cpython-310.pyc,,
transformers/models/stablelm/configuration_stablelm.py,sha256=duBnQ7xDdWHxkwPzy6qU4VmdRPKiskdRv4K4eRsYD7c,9248
transformers/models/stablelm/modeling_stablelm.py,sha256=6JWIo2HhLuYM6ujL60ZWS-YKpFcwF6fhju6_werMRLQ,67951
transformers/models/starcoder2/__init__.py,sha256=KvuuG416ad_oZhxX-zzkAkpTFpw4Qy9XlMs-CQYOugs,1855
transformers/models/starcoder2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/starcoder2/__pycache__/configuration_starcoder2.cpython-310.pyc,,
transformers/models/starcoder2/__pycache__/modeling_starcoder2.cpython-310.pyc,,
transformers/models/starcoder2/configuration_starcoder2.py,sha256=NTU3zXF-quxJBI-N7t4QzXMauRzgOG7tnWCtoPH0h1U,6836
transformers/models/starcoder2/modeling_starcoder2.py,sha256=_MsaCRssg3lD-H_Gd3JqHGO7aQau20Ns-tub88mOC7Y,62403
transformers/models/superpoint/__init__.py,sha256=F689OHjjtn1F35ezCfMPus5aDWiMTR8UkbxBjq_Mjwg,2105
transformers/models/superpoint/__pycache__/__init__.cpython-310.pyc,,
transformers/models/superpoint/__pycache__/configuration_superpoint.cpython-310.pyc,,
transformers/models/superpoint/__pycache__/convert_superpoint_to_pytorch.cpython-310.pyc,,
transformers/models/superpoint/__pycache__/image_processing_superpoint.cpython-310.pyc,,
transformers/models/superpoint/__pycache__/modeling_superpoint.cpython-310.pyc,,
transformers/models/superpoint/configuration_superpoint.py,sha256=qA0lWTHbPEKf2aB9g1qvstCi24EllKpmlfOSpDdSoyE,4039
transformers/models/superpoint/convert_superpoint_to_pytorch.py,sha256=tO1P6yqW46LY1hnWIJPOs4KjW0uZWkiVWW-GTOXbJGg,7243
transformers/models/superpoint/image_processing_superpoint.py,sha256=Om_ry5alSPtghMVDfFXI2CwDYRNm4siwZGYPmqdNFlE,12510
transformers/models/superpoint/modeling_superpoint.py,sha256=yptk889VYdYKaQOzKsH0qKLOSQKrrIwE-V2zS7XKKfs,21456
transformers/models/swiftformer/__init__.py,sha256=Zff4CKLyTCG1bb4OL1g0QaCgJN6OqZyXAdG5YN26QCg,2452
transformers/models/swiftformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/swiftformer/__pycache__/configuration_swiftformer.cpython-310.pyc,,
transformers/models/swiftformer/__pycache__/convert_swiftformer_original_to_hf.cpython-310.pyc,,
transformers/models/swiftformer/__pycache__/modeling_swiftformer.cpython-310.pyc,,
transformers/models/swiftformer/__pycache__/modeling_tf_swiftformer.cpython-310.pyc,,
transformers/models/swiftformer/configuration_swiftformer.py,sha256=FkKIxalmrqw_PV_KuZVjpneGoK6VH5SAaO5ji3hfzKc,5799
transformers/models/swiftformer/convert_swiftformer_original_to_hf.py,sha256=f3WE1QJGHW6Ak8fu3n37S69m92xgICfsbYYKDvibn6c,6238
transformers/models/swiftformer/modeling_swiftformer.py,sha256=jdntmvVPb7cre-tEPBa7rHn7YKHQPSJvW4kEsDHBprg,22746
transformers/models/swiftformer/modeling_tf_swiftformer.py,sha256=N7x7HbuKGvxOe-We-iEwbNVsdZ--e8-vom2L0LKPUTM,34860
transformers/models/swin/__init__.py,sha256=0KzgLwL1tvPlqAmu_3FogqSM_dNHx3fgdOMQdrIeCAs,2435
transformers/models/swin/__pycache__/__init__.cpython-310.pyc,,
transformers/models/swin/__pycache__/configuration_swin.cpython-310.pyc,,
transformers/models/swin/__pycache__/convert_swin_simmim_to_pytorch.cpython-310.pyc,,
transformers/models/swin/__pycache__/convert_swin_timm_to_pytorch.cpython-310.pyc,,
transformers/models/swin/__pycache__/modeling_swin.cpython-310.pyc,,
transformers/models/swin/__pycache__/modeling_tf_swin.cpython-310.pyc,,
transformers/models/swin/configuration_swin.py,sha256=aomGg6BFigfdVHi-1YHEPvZUwl5MI9bEw3hxp4D5t7Y,7904
transformers/models/swin/convert_swin_simmim_to_pytorch.py,sha256=35aBdY_dHVSumVv-vl6qS9wuJeNrqWT5EkMTvo8zVfo,6631
transformers/models/swin/convert_swin_timm_to_pytorch.py,sha256=UE5XxkTNJZjTKwfK_upI-bOqZwE5fIgQjJDSoPKkT-g,5809
transformers/models/swin/modeling_swin.py,sha256=RRAToOYvEpntC5vaBoXQqlG_3sTC9jXTtfd4LKDazZc,62676
transformers/models/swin/modeling_tf_swin.py,sha256=-PeP-jpAE9gUCI3zzI-vxe8l-HPtw-LV-n-cnyamlr0,70673
transformers/models/swin2sr/__init__.py,sha256=5asgXPZTPzEfh2hmTWc8ztYBmNtEAoSzTOGR37_U-ws,2097
transformers/models/swin2sr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/swin2sr/__pycache__/configuration_swin2sr.cpython-310.pyc,,
transformers/models/swin2sr/__pycache__/convert_swin2sr_original_to_pytorch.cpython-310.pyc,,
transformers/models/swin2sr/__pycache__/image_processing_swin2sr.cpython-310.pyc,,
transformers/models/swin2sr/__pycache__/modeling_swin2sr.cpython-310.pyc,,
transformers/models/swin2sr/configuration_swin2sr.py,sha256=FmiFWU4tecLaORaEUmSViGE4i80wdSMTk0F6DlfO6Iw,6811
transformers/models/swin2sr/convert_swin2sr_original_to_pytorch.py,sha256=xv4DkXMn4keBRk8lLaG5Kq91DdQMn4q_25WudPCYhyo,11363
transformers/models/swin2sr/image_processing_swin2sr.py,sha256=9GDG_McVWO6VSAZd64WZkSij78wIlxAq2LYVmyyfeeU,9544
transformers/models/swin2sr/modeling_swin2sr.py,sha256=HUFNUQtLVwIYpzQg50nlPMf4mGz9xU9787TYrSeLFso,50778
transformers/models/swinv2/__init__.py,sha256=GEPTL7RC14eQrc8dpdGIqv046z5U6EyZErY9cDuhF2o,1745
transformers/models/swinv2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/swinv2/__pycache__/configuration_swinv2.cpython-310.pyc,,
transformers/models/swinv2/__pycache__/convert_swinv2_timm_to_pytorch.cpython-310.pyc,,
transformers/models/swinv2/__pycache__/modeling_swinv2.cpython-310.pyc,,
transformers/models/swinv2/configuration_swinv2.py,sha256=PQykgFSB4-MX2_wjFO7Ec_FYe25SPGRS3qa1P6K1f2E,7518
transformers/models/swinv2/convert_swinv2_timm_to_pytorch.py,sha256=Eg5ASHw46_huYnDn-QCdVXJnSb_U5QfzrktX__nP_D0,7693
transformers/models/swinv2/modeling_swinv2.py,sha256=Efc34qvbvL5WpzSRJkMnopyKMHDJSxBHOXxRxtaCM1k,66457
transformers/models/switch_transformers/__init__.py,sha256=I7mMOnAQlrQQlaWIzu6axcmn46k5FZuGjp9yXf9qX5o,2240
transformers/models/switch_transformers/__pycache__/__init__.cpython-310.pyc,,
transformers/models/switch_transformers/__pycache__/configuration_switch_transformers.cpython-310.pyc,,
transformers/models/switch_transformers/__pycache__/convert_big_switch.cpython-310.pyc,,
transformers/models/switch_transformers/__pycache__/convert_switch_transformers_original_flax_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/switch_transformers/__pycache__/modeling_switch_transformers.cpython-310.pyc,,
transformers/models/switch_transformers/configuration_switch_transformers.py,sha256=v8XcyDGyi0DILV2Rrkxfu0I1WUdktwMSbcFvfxHVlJc,9005
transformers/models/switch_transformers/convert_big_switch.py,sha256=wjMGjHXAqVool6fZQhdG_Av2Ujx9EDoZrtHC8RdDLk4,7659
transformers/models/switch_transformers/convert_switch_transformers_original_flax_checkpoint_to_pytorch.py,sha256=AAJNkPcr_THjPN_8RUnOdBYbbYc6GOqXdgdjhx9FZyw,7593
transformers/models/switch_transformers/modeling_switch_transformers.py,sha256=2mNl6TT8EOPh4gqq_FJJtO8C1fOVAuxoXh7OW4r-YuA,88009
transformers/models/t5/__init__.py,sha256=0b3q_e-t6U3nDHzc2h88XPscvUDRCnzvx_K0lscUtAs,4236
transformers/models/t5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/t5/__pycache__/configuration_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/convert_t5_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/t5/__pycache__/convert_t5x_checkpoint_to_flax.cpython-310.pyc,,
transformers/models/t5/__pycache__/convert_t5x_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/t5/__pycache__/modeling_flax_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/modeling_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/modeling_tf_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/tokenization_t5.cpython-310.pyc,,
transformers/models/t5/__pycache__/tokenization_t5_fast.cpython-310.pyc,,
transformers/models/t5/configuration_t5.py,sha256=dJPi5DVoEPBAQqf5JmXTsErS9wHmKkIg-vVD8C7IQ-Y,7272
transformers/models/t5/convert_t5_original_tf_checkpoint_to_pytorch.py,sha256=LEibHPdlDdKdyB6XHB5s7pHRsqB5qQxUWN93H8G_q5k,2119
transformers/models/t5/convert_t5x_checkpoint_to_flax.py,sha256=PLgfe5u_gcFjBduCmAeuKaDW4VjJtP6KKsx4zIRX8hs,10580
transformers/models/t5/convert_t5x_checkpoint_to_pytorch.py,sha256=GTF0FYHDDDBl2tcYgHcirqHOI2KOE2YkDG4ekzjh_Ao,10483
transformers/models/t5/modeling_flax_t5.py,sha256=AJ_KxZuAvPeBTFrXLSDAWB_61h0JqLM5Hs_99KvDDSc,74164
transformers/models/t5/modeling_t5.py,sha256=2n0jxcgrhO4OdjX66gZcLsbTHglUg_y2_OmwPaOCl8Q,108562
transformers/models/t5/modeling_tf_t5.py,sha256=KRNhg23WlBDGcWH_U9MD9ddImh8vb5JPGjfPN_w03I4,77079
transformers/models/t5/tokenization_t5.py,sha256=864VAVjWy2gf7O075adPqRqNBB_VJuIGUdft1N0iwTI,20015
transformers/models/t5/tokenization_t5_fast.py,sha256=8Xcc-nHK9IXeKiMrOo9GgCD3ZRt18ON1sYm3ZdXTrY4,10110
transformers/models/table_transformer/__init__.py,sha256=5VJzy-OFzZtMyOI2vK3fY5Fi7xoHFU23ujDsO1iwxPo,1829
transformers/models/table_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/table_transformer/__pycache__/configuration_table_transformer.cpython-310.pyc,,
transformers/models/table_transformer/__pycache__/convert_table_transformer_to_hf.cpython-310.pyc,,
transformers/models/table_transformer/__pycache__/convert_table_transformer_to_hf_no_timm.cpython-310.pyc,,
transformers/models/table_transformer/__pycache__/modeling_table_transformer.cpython-310.pyc,,
transformers/models/table_transformer/configuration_table_transformer.py,sha256=38_xDcXUcXfdFpkNej0pKty6SNimCLFWmzDOEBts-M8,13304
transformers/models/table_transformer/convert_table_transformer_to_hf.py,sha256=Fw7-BfEEli4_Qk4AXZ9sNfiitz6IJwyTGPdtCGlkBg8,15094
transformers/models/table_transformer/convert_table_transformer_to_hf_no_timm.py,sha256=9A3hwdQWayhc8RoxUWpLoPyhhpHDuVgheIN_MOkLnYI,21185
transformers/models/table_transformer/modeling_table_transformer.py,sha256=8k67DXV0lY98IyhESAZp9s1JMlTjYZZ4dxu8IKjD_aA,92455
transformers/models/tapas/__init__.py,sha256=iyb5L4YVgub8YAXp89LsHjrc81hPtTk0xoUbRZB_1oI,2678
transformers/models/tapas/__pycache__/__init__.cpython-310.pyc,,
transformers/models/tapas/__pycache__/configuration_tapas.cpython-310.pyc,,
transformers/models/tapas/__pycache__/convert_tapas_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/tapas/__pycache__/modeling_tapas.cpython-310.pyc,,
transformers/models/tapas/__pycache__/modeling_tf_tapas.cpython-310.pyc,,
transformers/models/tapas/__pycache__/tokenization_tapas.cpython-310.pyc,,
transformers/models/tapas/configuration_tapas.py,sha256=Y6fdPefBKdklapxnmdZxNjYh4iTgNgv3ysFA2yDqE10,12265
transformers/models/tapas/convert_tapas_original_tf_checkpoint_to_pytorch.py,sha256=K69dSJ-h4XbiYh_4pd7UX7KErXThx8dtDORaiCcBKTM,5048
transformers/models/tapas/modeling_tapas.py,sha256=PyVBpIw5uH5YbOt3HLjNKXv-rOvDAuOE1Hu5R2PaTJM,110443
transformers/models/tapas/modeling_tf_tapas.py,sha256=6J0U28hbN40vjhgcD9Azb7m2VpierAdxRiFtwytokug,112236
transformers/models/tapas/tokenization_tapas.py,sha256=rgiV4n3nfPFSeVaMpzzN7Ld2Fd79n3ZjnI_ihEkzcA8,117023
transformers/models/time_series_transformer/__init__.py,sha256=SGCjnebDLeCZOFRcSjeojL3IgIuW4Lu_lHwxX7XWarQ,1794
transformers/models/time_series_transformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/time_series_transformer/__pycache__/configuration_time_series_transformer.cpython-310.pyc,,
transformers/models/time_series_transformer/__pycache__/modeling_time_series_transformer.cpython-310.pyc,,
transformers/models/time_series_transformer/configuration_time_series_transformer.py,sha256=AjgVw6YjCaYCnJJiT_V5-5bgZMEvQZnlIJ6MBrXfWJ8,11657
transformers/models/time_series_transformer/modeling_time_series_transformer.py,sha256=sqUB4_ldIg_oeb80K0zaSRO4lASEupsxDhAx-nCKyHE,88553
transformers/models/timesformer/__init__.py,sha256=AnmocPlunuIAehwJ7EYSuKJCy8QWtrFLsD-7NqA6dUs,1666
transformers/models/timesformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/timesformer/__pycache__/configuration_timesformer.cpython-310.pyc,,
transformers/models/timesformer/__pycache__/convert_timesformer_to_pytorch.cpython-310.pyc,,
transformers/models/timesformer/__pycache__/modeling_timesformer.cpython-310.pyc,,
transformers/models/timesformer/configuration_timesformer.py,sha256=3FX4rs8g8vsCRE_N8GphIJ-Ib2pyiUgPjOOLa7jexMY,5534
transformers/models/timesformer/convert_timesformer_to_pytorch.py,sha256=TjOfPbEC4oVb5tlOgU2m9g36OBizDEEjm0bbcZz6Mq8,10176
transformers/models/timesformer/modeling_timesformer.py,sha256=-5LIsbKosfORf9B6JmdGZ3HsHl08uVaazDYFa2rebCA,35193
transformers/models/timm_backbone/__init__.py,sha256=rn9y1wXicP1g6IiI_tSWu7fnt5q_x6hfu3g9yQvovEU,1624
transformers/models/timm_backbone/__pycache__/__init__.cpython-310.pyc,,
transformers/models/timm_backbone/__pycache__/configuration_timm_backbone.cpython-310.pyc,,
transformers/models/timm_backbone/__pycache__/modeling_timm_backbone.cpython-310.pyc,,
transformers/models/timm_backbone/configuration_timm_backbone.py,sha256=pYzjQoWVLkxUGScEoM9UCAmTgIeBFYw2DpXOS_ZOXsg,3151
transformers/models/timm_backbone/modeling_timm_backbone.py,sha256=RWRPlTWGQ-oziBMNUCCpSdaQWiWW-uhj7RPvzjemeOI,6937
transformers/models/trocr/__init__.py,sha256=BinAFMZ1GwQ8eNwe8MJ3OC9hdExObFUud2hTrHeRT2Q,1658
transformers/models/trocr/__pycache__/__init__.cpython-310.pyc,,
transformers/models/trocr/__pycache__/configuration_trocr.cpython-310.pyc,,
transformers/models/trocr/__pycache__/convert_trocr_unilm_to_pytorch.cpython-310.pyc,,
transformers/models/trocr/__pycache__/modeling_trocr.cpython-310.pyc,,
transformers/models/trocr/__pycache__/processing_trocr.cpython-310.pyc,,
transformers/models/trocr/configuration_trocr.py,sha256=WLVxVr-Hw8LumF67LkpcqqhKMhKYjV_XEc5OJxtBDw4,6522
transformers/models/trocr/convert_trocr_unilm_to_pytorch.py,sha256=FO6U6L51DYBxCxvalXRVpsfZZWKK7PHZV-6nJgt4icI,10165
transformers/models/trocr/modeling_trocr.py,sha256=78cl_WqNYvSqBWCFKauxrulU3iphn_7rwxY_9Nregh4,45942
transformers/models/trocr/processing_trocr.py,sha256=7uB47Cr2SajBW_4MhbdEmJ6-mtfKnuKl8PhZyrIkjWY,5746
transformers/models/tvp/__init__.py,sha256=cljUuwy0tTsvdXsdZOTl8BgrTy8sRruCFWFUDobI-qI,2171
transformers/models/tvp/__pycache__/__init__.cpython-310.pyc,,
transformers/models/tvp/__pycache__/configuration_tvp.cpython-310.pyc,,
transformers/models/tvp/__pycache__/image_processing_tvp.cpython-310.pyc,,
transformers/models/tvp/__pycache__/modeling_tvp.cpython-310.pyc,,
transformers/models/tvp/__pycache__/processing_tvp.cpython-310.pyc,,
transformers/models/tvp/configuration_tvp.py,sha256=K3uECqqcf-SPODsUWZjXgOScrawxp1arjjhQElmHmCE,9906
transformers/models/tvp/image_processing_tvp.py,sha256=SiQUmjVpDimWZz_U-4U4rGX6iOw8Qh_WD5PZ5LAu70w,23178
transformers/models/tvp/modeling_tvp.py,sha256=0XzPwaI7wuulpEBCGUEsJcfTgJWVdZO9h4UhXDg0vt4,43604
transformers/models/tvp/processing_tvp.py,sha256=c9nERZMOgxsu8wLQ8KhJ8b37_LLUReURTouYA0GM6Xg,6980
transformers/models/udop/__init__.py,sha256=eopYOXkMGcU9DhpsBVSPmREG5nGQF7MRdSgCvHwTb9Y,2696
transformers/models/udop/__pycache__/__init__.cpython-310.pyc,,
transformers/models/udop/__pycache__/configuration_udop.cpython-310.pyc,,
transformers/models/udop/__pycache__/convert_udop_to_hf.cpython-310.pyc,,
transformers/models/udop/__pycache__/modeling_udop.cpython-310.pyc,,
transformers/models/udop/__pycache__/processing_udop.cpython-310.pyc,,
transformers/models/udop/__pycache__/tokenization_udop.cpython-310.pyc,,
transformers/models/udop/__pycache__/tokenization_udop_fast.cpython-310.pyc,,
transformers/models/udop/configuration_udop.py,sha256=4w_9uMxh1exqrKTHqfmUkDjuNwBs0cXu8Vkc1lAYrfA,7648
transformers/models/udop/convert_udop_to_hf.py,sha256=3HkMdxV39MaBHOzIHQnAzBnA7yxoz7Zl87pspmORr3Q,33524
transformers/models/udop/modeling_udop.py,sha256=mlLqUyk-h0lGrxcd8IC8prpoh9lsReWDK0Lx4Dy1nrM,94372
transformers/models/udop/processing_udop.py,sha256=4r21EuC0M2gF5GAl9EuSiQ5l80sv7TjiEP_v6J5saqc,10119
transformers/models/udop/tokenization_udop.py,sha256=x5CzraTwl8W7YwMb82t2AcAerKfuq6GcGdqJ1_wFvUo,70654
transformers/models/udop/tokenization_udop_fast.py,sha256=5eN17cVg7iKeV1o3MncMa3oHoo0iU8XiHhmz6mSQRQk,48940
transformers/models/umt5/__init__.py,sha256=wcKbkdS_suuZCQs52Oz0lBegIa0QDSPZW2Q-XBpM3ns,1908
transformers/models/umt5/__pycache__/__init__.cpython-310.pyc,,
transformers/models/umt5/__pycache__/configuration_umt5.cpython-310.pyc,,
transformers/models/umt5/__pycache__/convert_umt5_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/umt5/__pycache__/modeling_umt5.cpython-310.pyc,,
transformers/models/umt5/configuration_umt5.py,sha256=gI-vLFPQvvJ38LdIUM2Jhb-16XuW8hE4--gdDLUABN0,7636
transformers/models/umt5/convert_umt5_checkpoint_to_pytorch.py,sha256=cSB6TobLxWoeNNqPXPiH4YOKwj0ji9phK8gA4vzt-jo,12072
transformers/models/umt5/modeling_umt5.py,sha256=Xtgqc3FcyBJO7ms2tOxGg2fv-K8U-xdZKeNGb5OTa2c,86423
transformers/models/unispeech/__init__.py,sha256=v1Xl2KNrosFlv-ep9bXLiML1C-9lhiultR-UmlB8XGU,1830
transformers/models/unispeech/__pycache__/__init__.cpython-310.pyc,,
transformers/models/unispeech/__pycache__/configuration_unispeech.cpython-310.pyc,,
transformers/models/unispeech/__pycache__/convert_unispeech_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/unispeech/__pycache__/modeling_unispeech.cpython-310.pyc,,
transformers/models/unispeech/configuration_unispeech.py,sha256=bxlMcrmyJVytWngvIOa7vz6zvEiqI7fVud21q7lbmq0,17454
transformers/models/unispeech/convert_unispeech_original_pytorch_checkpoint_to_pytorch.py,sha256=9Sy8RKspS_mb4rTl4t9IlqNaMiXQz31ATrWRFfM5xhA,11339
transformers/models/unispeech/modeling_unispeech.py,sha256=hZvJ6GOoGaWZUztoX5S93e0wsLfsLcCOSH2EmAXD1Yk,86508
transformers/models/unispeech_sat/__init__.py,sha256=p1GTWc9LU-N1jH_I3EY5HeEZOeFgUzuHSlElpTpCynA,2063
transformers/models/unispeech_sat/__pycache__/__init__.cpython-310.pyc,,
transformers/models/unispeech_sat/__pycache__/configuration_unispeech_sat.cpython-310.pyc,,
transformers/models/unispeech_sat/__pycache__/convert_unispeech_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/unispeech_sat/__pycache__/convert_unispeech_sat_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/unispeech_sat/__pycache__/modeling_unispeech_sat.cpython-310.pyc,,
transformers/models/unispeech_sat/configuration_unispeech_sat.py,sha256=WB5cTDJdSlEIIsM-NVGHxyqb5WMIRI4OigV74_HnYLw,18796
transformers/models/unispeech_sat/convert_unispeech_original_s3prl_checkpoint_to_pytorch.py,sha256=OLOISwA82PhBuqITPvfR7bP23Fx0Gxvb2c2SbCKk_XY,4869
transformers/models/unispeech_sat/convert_unispeech_sat_original_pytorch_checkpoint_to_pytorch.py,sha256=aqmh0Am1E26ifRHt8MujjdUO9CHkVZ6ziqoOIr3n6-o,9288
transformers/models/unispeech_sat/modeling_unispeech_sat.py,sha256=PDpu0_9YcAO3skCNwWke3JQnlzibrGjUeY9yxtozNTs,100708
transformers/models/univnet/__init__.py,sha256=mvvYY7ztGirStHFoaJ3ksEf2Yt6RCI72xnBhPySISG0,1631
transformers/models/univnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/univnet/__pycache__/configuration_univnet.cpython-310.pyc,,
transformers/models/univnet/__pycache__/convert_univnet.cpython-310.pyc,,
transformers/models/univnet/__pycache__/feature_extraction_univnet.cpython-310.pyc,,
transformers/models/univnet/__pycache__/modeling_univnet.cpython-310.pyc,,
transformers/models/univnet/configuration_univnet.py,sha256=6x2-kNWG4Ah3kSqN_BpMcmK5eduT4L4Fsxp0SbcO4Ik,6728
transformers/models/univnet/convert_univnet.py,sha256=R2gqXfz8Oq2rwIUU01V7T_oSoDGG2A4Gety-R80Yn24,6364
transformers/models/univnet/feature_extraction_univnet.py,sha256=snAVdQ5ClFX_Sw7upgvWyzJq4bUNRelRQaxcWxgHIgA,22821
transformers/models/univnet/modeling_univnet.py,sha256=oItBSOWVFmgUrjzzj56HsJ9plcsx1tyX2KtSMNaigCA,26774
transformers/models/upernet/__init__.py,sha256=z2avy6tP_WpANiGPA5RCxT_9yPp0PfEDlfUjL9rQsXM,1535
transformers/models/upernet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/upernet/__pycache__/configuration_upernet.cpython-310.pyc,,
transformers/models/upernet/__pycache__/convert_convnext_upernet_to_pytorch.cpython-310.pyc,,
transformers/models/upernet/__pycache__/convert_swin_upernet_to_pytorch.cpython-310.pyc,,
transformers/models/upernet/__pycache__/modeling_upernet.cpython-310.pyc,,
transformers/models/upernet/configuration_upernet.py,sha256=f53XBT9QQpqwrJU4HK1pbW7MurCddZtL7St0fBzNbYo,6613
transformers/models/upernet/convert_convnext_upernet_to_pytorch.py,sha256=l_CJoXwANEE9rm5mwpHwbusIoJLmN8jNGjxsj6WhZrk,10271
transformers/models/upernet/convert_swin_upernet_to_pytorch.py,sha256=lHV8SE_bZnxOo-zEJ21S2nY449uPVc3bpcl2JGKNEjA,14026
transformers/models/upernet/modeling_upernet.py,sha256=g3dYlwGd8yQ4PNCki1Uuk_xYoMN5BLGvgEW9KSC_CII,17135
transformers/models/video_llava/__init__.py,sha256=gOB5OsDdV-YgmsiRMt5CDcPCntP1r5l8Vh-sKe4aEKs,2231
transformers/models/video_llava/__pycache__/__init__.cpython-310.pyc,,
transformers/models/video_llava/__pycache__/configuration_video_llava.cpython-310.pyc,,
transformers/models/video_llava/__pycache__/convert_video_llava_weights_to_hf.cpython-310.pyc,,
transformers/models/video_llava/__pycache__/image_processing_video_llava.cpython-310.pyc,,
transformers/models/video_llava/__pycache__/modeling_video_llava.cpython-310.pyc,,
transformers/models/video_llava/__pycache__/processing_video_llava.cpython-310.pyc,,
transformers/models/video_llava/configuration_video_llava.py,sha256=H5WczVY-u6h3NfrnjRkgbZJNuaAxxCZMMZvxaTYYDE8,5605
transformers/models/video_llava/convert_video_llava_weights_to_hf.py,sha256=2ubkFfdHTRc-6BPLYG1A82Daki0EBnSkmVPLQyRc7eo,6078
transformers/models/video_llava/image_processing_video_llava.py,sha256=sY3g94kDJkqFU7Wbqa6Aks_PNg6_jX4IXGhzQXg1u9M,19855
transformers/models/video_llava/modeling_video_llava.py,sha256=C2XKT5PhMSnAsH1m0K1cUPTdkyczcvpjtwUFJE9NM8M,35563
transformers/models/video_llava/processing_video_llava.py,sha256=vTzKudoWnKuJg5YnZFySpSJyr9J5p4yxN-Gy30WTwz0,8064
transformers/models/videomae/__init__.py,sha256=7mPLbj32OCbHA8_chBPNVUEbCRm4BwOs3F9fHKKShwI,2335
transformers/models/videomae/__pycache__/__init__.cpython-310.pyc,,
transformers/models/videomae/__pycache__/configuration_videomae.cpython-310.pyc,,
transformers/models/videomae/__pycache__/convert_videomae_to_pytorch.cpython-310.pyc,,
transformers/models/videomae/__pycache__/feature_extraction_videomae.cpython-310.pyc,,
transformers/models/videomae/__pycache__/image_processing_videomae.cpython-310.pyc,,
transformers/models/videomae/__pycache__/modeling_videomae.cpython-310.pyc,,
transformers/models/videomae/configuration_videomae.py,sha256=OZNNpgURTWBUC9mr8ZuhKPyMXwz-D5F_8BdoIc9-tgQ,6569
transformers/models/videomae/convert_videomae_to_pytorch.py,sha256=rq2nT2ZJekra1G38kM2DH_qOvcZBDQFNgbCvH3mKZjY,13989
transformers/models/videomae/feature_extraction_videomae.py,sha256=Hg5wmFhkbncqR3nfvtevV6msaUEqvLBf4mtO4aICYTI,1200
transformers/models/videomae/image_processing_videomae.py,sha256=yMZGcXFd8YmK1uwf9tqOFtvild9yOAf8rJeXVxX3oNo,17000
transformers/models/videomae/modeling_videomae.py,sha256=8k2lf6zmvg_rcMJsWaGo1NvmNtjG0UZI96O99mjJvow,49292
transformers/models/vilt/__init__.py,sha256=x1LOq1RWrRzvl1Zo6na-6RGCP32oIQA-DrQ5fM1k05M,2620
transformers/models/vilt/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vilt/__pycache__/configuration_vilt.cpython-310.pyc,,
transformers/models/vilt/__pycache__/convert_vilt_original_to_pytorch.cpython-310.pyc,,
transformers/models/vilt/__pycache__/feature_extraction_vilt.cpython-310.pyc,,
transformers/models/vilt/__pycache__/image_processing_vilt.cpython-310.pyc,,
transformers/models/vilt/__pycache__/modeling_vilt.cpython-310.pyc,,
transformers/models/vilt/__pycache__/processing_vilt.cpython-310.pyc,,
transformers/models/vilt/configuration_vilt.py,sha256=rd2Ee35CnNQXOjWBeTNAZZ2ugpCAiwK1Dp0-zjs9dq0,6788
transformers/models/vilt/convert_vilt_original_to_pytorch.py,sha256=PE1IIC0UxmzENP_L_Ev-2mOw3Q43N7Hpog-I8qQN_Yc,12881
transformers/models/vilt/feature_extraction_vilt.py,sha256=dC0Glwc_rDX7zqp8BxRtzaLogQGI4I4CjQCgxU7UORw,1172
transformers/models/vilt/image_processing_vilt.py,sha256=IWRKV_yd9g0sp2I4xIsIp9frK07xFqxGl7rfKwZpTQ8,23631
transformers/models/vilt/modeling_vilt.py,sha256=86fjdFWrymg99Zm6Q5zo7lrJ1R3WvjKtSz8WxhwceSM,64888
transformers/models/vilt/processing_vilt.py,sha256=0iOal8dCaE7JCQlZjbJ1-sHGxpDPZgUkMowEbxFRF2Q,6079
transformers/models/vipllava/__init__.py,sha256=ETx0yY4OemeAzBoFkOcyk-cXVVnxAB4BytukiSI6xRQ,1556
transformers/models/vipllava/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vipllava/__pycache__/configuration_vipllava.cpython-310.pyc,,
transformers/models/vipllava/__pycache__/convert_vipllava_weights_to_hf.cpython-310.pyc,,
transformers/models/vipllava/__pycache__/modeling_vipllava.cpython-310.pyc,,
transformers/models/vipllava/configuration_vipllava.py,sha256=mLczfrTFEvG6p8FT94ZXOAxEprtE2zqp2xALLE0Gjzo,4928
transformers/models/vipllava/convert_vipllava_weights_to_hf.py,sha256=u64-lOXDE0JMGhkGYJEtyrOh3gpeJtxSDC_dC08mc2c,4794
transformers/models/vipllava/modeling_vipllava.py,sha256=EgudSDf-k0I-Cd632j5YSYWm4BYMSWmsAYiF3-9q1WE,29796
transformers/models/vision_encoder_decoder/__init__.py,sha256=IRQsS-4Bz-cm6B97rSoeC62Z1l1wns0XVDZwBn1KBIU,2627
transformers/models/vision_encoder_decoder/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/configuration_vision_encoder_decoder.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_flax_vision_encoder_decoder.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_tf_vision_encoder_decoder.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/__pycache__/modeling_vision_encoder_decoder.cpython-310.pyc,,
transformers/models/vision_encoder_decoder/configuration_vision_encoder_decoder.py,sha256=6x7tdTBOrsvKOMy12NCtbPatY2qaqOJaVIGGxy3uPDw,8273
transformers/models/vision_encoder_decoder/modeling_flax_vision_encoder_decoder.py,sha256=AtoTzqUjolUXn-NBPdzhc2gNRaDJXPR92eGUj9fZmSg,41533
transformers/models/vision_encoder_decoder/modeling_tf_vision_encoder_decoder.py,sha256=rz7SPcIwpGCHnrdZk-uix9hsGOIuUcOgZjIfr9U1xxY,36237
transformers/models/vision_encoder_decoder/modeling_vision_encoder_decoder.py,sha256=t0zK9guWJ6sg6YR-hP6_MUkfaLIEfkqQHUsxJTglaUo,35239
transformers/models/vision_text_dual_encoder/__init__.py,sha256=kULrtY2Ie2eigdn63xnoEqRUlmKm31D9mUCJs4F62Lo,2730
transformers/models/vision_text_dual_encoder/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/configuration_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_flax_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_tf_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/modeling_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/__pycache__/processing_vision_text_dual_encoder.cpython-310.pyc,,
transformers/models/vision_text_dual_encoder/configuration_vision_text_dual_encoder.py,sha256=awSM75zqIk9PMkhuvMXYaxW-DoSw7sNRX1Kozg9kAOI,4895
transformers/models/vision_text_dual_encoder/modeling_flax_vision_text_dual_encoder.py,sha256=IIJwejtF8IWZCr1XwWq7G0_h0oJRJ2l5AfDpEzCEFLE,26312
transformers/models/vision_text_dual_encoder/modeling_tf_vision_text_dual_encoder.py,sha256=MWSMzdJZLiB4uLqHlAuL4ih-At3C22KGavV1xw4qx8A,28640
transformers/models/vision_text_dual_encoder/modeling_vision_text_dual_encoder.py,sha256=8XEr5XZu0igmVuD2LfIOVQNk9E-RJTLNKJV3BSt9MaU,25073
transformers/models/vision_text_dual_encoder/processing_vision_text_dual_encoder.py,sha256=Wxw-ShdBxDkWK76hcJjHrvySp-uW0yrTvoqWouovhy8,6929
transformers/models/visual_bert/__init__.py,sha256=C2L_RDRTsnoWLC-FeN3qDuCKqaf2uf6baAydgUNZ4Lc,2039
transformers/models/visual_bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/visual_bert/__pycache__/configuration_visual_bert.cpython-310.pyc,,
transformers/models/visual_bert/__pycache__/convert_visual_bert_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/visual_bert/__pycache__/modeling_visual_bert.cpython-310.pyc,,
transformers/models/visual_bert/configuration_visual_bert.py,sha256=mhddUhQY3OOqrTTsqh0W77b6oI_Mk0ipvSR7M30KSNg,6734
transformers/models/visual_bert/convert_visual_bert_original_pytorch_checkpoint_to_pytorch.py,sha256=jaKViPxrFh302Z0muKnQovpjLlenroT1RwXeXBGVh5Q,5157
transformers/models/visual_bert/modeling_visual_bert.py,sha256=MCZQ94U_DA5MHi8kOFekj1UhCaXldgOLqJ-avFNXmYE,68915
transformers/models/vit/__init__.py,sha256=1UCVk3-29ZnEgXRs-YlLMgOrxbmyt4zDbi3aWqZduls,3929
transformers/models/vit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vit/__pycache__/configuration_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/convert_dino_to_pytorch.cpython-310.pyc,,
transformers/models/vit/__pycache__/convert_vit_timm_to_pytorch.cpython-310.pyc,,
transformers/models/vit/__pycache__/feature_extraction_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/image_processing_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/image_processing_vit_fast.cpython-310.pyc,,
transformers/models/vit/__pycache__/modeling_flax_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/modeling_tf_vit.cpython-310.pyc,,
transformers/models/vit/__pycache__/modeling_vit.cpython-310.pyc,,
transformers/models/vit/configuration_vit.py,sha256=8wqeZmlMXppMsu0soRhz7ncm6ii3S4IwT79ohP2gxbM,5612
transformers/models/vit/convert_dino_to_pytorch.py,sha256=_lGR0qDnyjBxr0BtxibxxpSwo71za6iKt-CJ2cKXpHI,8853
transformers/models/vit/convert_vit_timm_to_pytorch.py,sha256=vZPsceKZQo8OOVaVEMUoy5HAM7KJFW4vG7DSqynVp_c,10889
transformers/models/vit/feature_extraction_vit.py,sha256=R-W_HNOybSpKxKGKfo4iDB4zGTRHeW1cq-29iwnbVl4,1165
transformers/models/vit/image_processing_vit.py,sha256=ZTVKB_q7T0qGYcQG6VnMVxzog4VUwTKiC10-nFrUoyY,14185
transformers/models/vit/image_processing_vit_fast.py,sha256=NUGVEvFQiwA8iOJ5eCWqMKXw8j6Ly3CzPgRcCgsv3tg,13164
transformers/models/vit/modeling_flax_vit.py,sha256=KsTqlse5b5euRgYXhrXoNqCNvo0LEPBGuU_b0uNO0yo,25340
transformers/models/vit/modeling_tf_vit.py,sha256=RorkNKdk3rzuKAI0g01rEciEGphZMdNzPkKIxpxUQWU,37326
transformers/models/vit/modeling_vit.py,sha256=z3ygHjYsj-vIm_MngnnhVOytVPsu6g5dO3jFpZJiZl0,37066
transformers/models/vit_mae/__init__.py,sha256=7qPTFWY_icEM8R7T_0JmqckCR_KBrW6PYLkyMjFNiJk,2248
transformers/models/vit_mae/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vit_mae/__pycache__/configuration_vit_mae.cpython-310.pyc,,
transformers/models/vit_mae/__pycache__/convert_vit_mae_to_pytorch.cpython-310.pyc,,
transformers/models/vit_mae/__pycache__/modeling_tf_vit_mae.cpython-310.pyc,,
transformers/models/vit_mae/__pycache__/modeling_vit_mae.cpython-310.pyc,,
transformers/models/vit_mae/configuration_vit_mae.py,sha256=dZOTKc4uXQoFPRIzyMD7pEvCtJow4r98enXIwLjdtdY,6343
transformers/models/vit_mae/convert_vit_mae_to_pytorch.py,sha256=Nj4Y5LS8H7xbyWNeLE9Vn0NFyXSQQYEcj1QQMzN1Hdg,7516
transformers/models/vit_mae/modeling_tf_vit_mae.py,sha256=HCJwfvgg-chHbrM4MKyKMkhGaqVejGb5_oD5aoAzxms,57945
transformers/models/vit_mae/modeling_vit_mae.py,sha256=_9C4PuQr2MFcBaoDO8VE1Nx9XNJhWZBdxSb_5PmNYYY,50360
transformers/models/vit_msn/__init__.py,sha256=7t6WI5W0Ny3qSJYUMxr567LtrB2wTvoZJCDrdHabKLw,1603
transformers/models/vit_msn/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vit_msn/__pycache__/configuration_vit_msn.cpython-310.pyc,,
transformers/models/vit_msn/__pycache__/convert_msn_to_pytorch.cpython-310.pyc,,
transformers/models/vit_msn/__pycache__/modeling_vit_msn.cpython-310.pyc,,
transformers/models/vit_msn/configuration_vit_msn.py,sha256=X__EgNtTLyfKHKrdifVM2GttFCCRwO83TJItk86kjdw,4835
transformers/models/vit_msn/convert_msn_to_pytorch.py,sha256=1xBjqvbviFkGxhi_xq2956R7qZpFEBdKPNOQYb-SoIA,9841
transformers/models/vit_msn/modeling_vit_msn.py,sha256=4Udw3IyIza9nxuSlqCUhz40AUL2JWtKG72ustrn0yYg,31391
transformers/models/vitdet/__init__.py,sha256=JlPOjtIlbaZxS-kmjh0dEuCHQBbRyo5Gbb4_KY8CCV4,1588
transformers/models/vitdet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vitdet/__pycache__/configuration_vitdet.cpython-310.pyc,,
transformers/models/vitdet/__pycache__/modeling_vitdet.cpython-310.pyc,,
transformers/models/vitdet/configuration_vitdet.py,sha256=KYDwqi6EGkqngqGgfRIughg5JEM_8EfbK4WwZk2pLq0,7512
transformers/models/vitdet/modeling_vitdet.py,sha256=T3_jhL91J_0nXZvETaiqiBYm8gEJt5GkJfhATM9F5wU,34825
transformers/models/vitmatte/__init__.py,sha256=vW-SReLeVNo8Th8-Xt4Tv3zBgbsXH_R0v71rThH4hVg,2055
transformers/models/vitmatte/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vitmatte/__pycache__/configuration_vitmatte.cpython-310.pyc,,
transformers/models/vitmatte/__pycache__/convert_vitmatte_to_hf.cpython-310.pyc,,
transformers/models/vitmatte/__pycache__/image_processing_vitmatte.cpython-310.pyc,,
transformers/models/vitmatte/__pycache__/modeling_vitmatte.cpython-310.pyc,,
transformers/models/vitmatte/configuration_vitmatte.py,sha256=Li26IpLl-PBoWMjqNgIGjZ6fTGXwSKfcq2aVOyhiUkc,6238
transformers/models/vitmatte/convert_vitmatte_to_hf.py,sha256=1xctm78nmCLelPMqGJepxSyq5saKgA4by5CTzyxRPvc,6404
transformers/models/vitmatte/image_processing_vitmatte.py,sha256=w9V_MhYAY38fV4Iqcup7VpftZCP23WZaP911-AZ7tRk,13880
transformers/models/vitmatte/modeling_vitmatte.py,sha256=WUnanwEVuOPVj3X77sKJ7HFCRaXAv9GeyZsfCmE3u4U,12986
transformers/models/vits/__init__.py,sha256=vlwwg8NeCwRv6rx5z2MuHwssqLSQQCt-D3djFKWpK8c,1688
transformers/models/vits/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vits/__pycache__/configuration_vits.cpython-310.pyc,,
transformers/models/vits/__pycache__/convert_original_checkpoint.cpython-310.pyc,,
transformers/models/vits/__pycache__/modeling_vits.cpython-310.pyc,,
transformers/models/vits/__pycache__/tokenization_vits.cpython-310.pyc,,
transformers/models/vits/configuration_vits.py,sha256=CuB53I2kKfMvasGrCGc6qUcghy-gcpr4YlEoX2CLO48,13857
transformers/models/vits/convert_original_checkpoint.py,sha256=N6rRzBaJlMxRwT7u33kUyJKy-4fFTWTD6nu_RTTOGt0,18610
transformers/models/vits/modeling_vits.py,sha256=_L0xvMsdU3Ns655rwsBlkAoo4pBvTFwIg1FCYawxujA,66132
transformers/models/vits/tokenization_vits.py,sha256=yCNgIASSEk7wQaAFS-o8uExjjn8L_mlWWgVGth9lqh8,8950
transformers/models/vivit/__init__.py,sha256=rjAmFZl3VXvsc_Hp8DLz-S487KwXCPg-v11KLshJkWQ,2269
transformers/models/vivit/__pycache__/__init__.cpython-310.pyc,,
transformers/models/vivit/__pycache__/configuration_vivit.cpython-310.pyc,,
transformers/models/vivit/__pycache__/convert_vivit_flax_to_pytorch.cpython-310.pyc,,
transformers/models/vivit/__pycache__/image_processing_vivit.cpython-310.pyc,,
transformers/models/vivit/__pycache__/modeling_vivit.cpython-310.pyc,,
transformers/models/vivit/configuration_vivit.py,sha256=s6k0V9KrKa6q0fTzGNjMDxJLsygNmOwDsPl-v6siz6w,5114
transformers/models/vivit/convert_vivit_flax_to_pytorch.py,sha256=7lqPLrfC2tORMS69USynZGlVZBcPv1Ljzt7UO68Zu9w,9112
transformers/models/vivit/image_processing_vivit.py,sha256=_BpiEhs2jnRut9vf_GcJBMvjCI2oMMa_7j1CFqHcAYw,19553
transformers/models/vivit/modeling_vivit.py,sha256=M2zvR_htqP0SRVPxsEq8jJqSQU-wLoxcZw1J8NhjOVA,32645
transformers/models/wav2vec2/__init__.py,sha256=2AQvLWHNJ3KqZVFlLoR-fzK9Ipgr2LIgS2HmF0upA9s,3829
transformers/models/wav2vec2/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/configuration_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/convert_wav2vec2_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/convert_wav2vec2_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/feature_extraction_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_flax_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_tf_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/modeling_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/processing_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/__pycache__/tokenization_wav2vec2.cpython-310.pyc,,
transformers/models/wav2vec2/configuration_wav2vec2.py,sha256=QCG-isZugbWEXlDpwbPGL3Z4C85dV_M2tZf4776rVHg,20045
transformers/models/wav2vec2/convert_wav2vec2_original_pytorch_checkpoint_to_pytorch.py,sha256=d3WhsQr53BCVEpENSK0N0-lWCktnul2pTyx9O2n1KV8,15166
transformers/models/wav2vec2/convert_wav2vec2_original_s3prl_checkpoint_to_pytorch.py,sha256=xtC7g9fzjpjN-Zeuk2MjhrJsSbn6hs1jhT5evZvtNUM,4837
transformers/models/wav2vec2/feature_extraction_wav2vec2.py,sha256=D-yqFIpwjn_7LYJUmdnelRsn4qsoUrkZGX4Qsp5Y9CY,11511
transformers/models/wav2vec2/modeling_flax_wav2vec2.py,sha256=XyuYnXFM6om9uhmxleAug4SXyFtxshimUwYcAjjDgTE,57330
transformers/models/wav2vec2/modeling_tf_wav2vec2.py,sha256=KD1YEUtXnsy8-UdNFoRdup4-e1Fjt_vOFT0u0bDzPno,78614
transformers/models/wav2vec2/modeling_wav2vec2.py,sha256=aNt4ofCzAbPKTKl3NXOf_X10gFD42PP1PWhGMBYzV0g,120606
transformers/models/wav2vec2/processing_wav2vec2.py,sha256=e0oC8Q7gUHDadGE2Fq-AxFsajCWeH9BgPUX6IVQRrdI,7138
transformers/models/wav2vec2/tokenization_wav2vec2.py,sha256=Quxbkmx6iq_ToId0OoexwqLpXiCKH_Ew9DqVQUSV0LQ,38408
transformers/models/wav2vec2_bert/__init__.py,sha256=4kJuF03qjYji0pDR42X-E52MqR60fAidlmqlshLhPyg,2065
transformers/models/wav2vec2_bert/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2_bert/__pycache__/configuration_wav2vec2_bert.cpython-310.pyc,,
transformers/models/wav2vec2_bert/__pycache__/convert_wav2vec2_seamless_checkpoint.cpython-310.pyc,,
transformers/models/wav2vec2_bert/__pycache__/modeling_wav2vec2_bert.cpython-310.pyc,,
transformers/models/wav2vec2_bert/__pycache__/processing_wav2vec2_bert.cpython-310.pyc,,
transformers/models/wav2vec2_bert/configuration_wav2vec2_bert.py,sha256=0L5glQSdlObZoUVFMrMYk390H-JwxxQK3nJ6pZiqnzU,18075
transformers/models/wav2vec2_bert/convert_wav2vec2_seamless_checkpoint.py,sha256=JU4IQi_3dgf_j_foK5JvpxCbk0ZMFbE9wAaA-WRnQ9s,7419
transformers/models/wav2vec2_bert/modeling_wav2vec2_bert.py,sha256=C4rCCVGmIyzLHfkUpZe6ALAfZcd61rlqQ1F75BJm_qg,74480
transformers/models/wav2vec2_bert/processing_wav2vec2_bert.py,sha256=y264p2NlHYn_cXMz9i-c0gLPQu8iOY_OGxfnltChJLw,7450
transformers/models/wav2vec2_conformer/__init__.py,sha256=ap1FmrO8hw48sMklmLMm-WlQKo8u6aChgnvG3zLXvyI,2120
transformers/models/wav2vec2_conformer/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/configuration_wav2vec2_conformer.cpython-310.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/convert_wav2vec2_conformer_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wav2vec2_conformer/__pycache__/modeling_wav2vec2_conformer.cpython-310.pyc,,
transformers/models/wav2vec2_conformer/configuration_wav2vec2_conformer.py,sha256=tkm4aUHCmupnUU6NuExs1h8vzfHBakNcLqGCyhOgGC4,20850
transformers/models/wav2vec2_conformer/convert_wav2vec2_conformer_original_pytorch_checkpoint_to_pytorch.py,sha256=gJvDTMypi4dwCM4RN4N7Zh8W1ZnIk2aYa_VtO4uarQo,13381
transformers/models/wav2vec2_conformer/modeling_wav2vec2_conformer.py,sha256=DSgth0mKnDN0aCXOxFM10KxAm8rwHgY50PRXBdBOu7M,95863
transformers/models/wav2vec2_phoneme/__init__.py,sha256=E2xRyViyzCISV8XE7YQ1gx5Wlx9_ACoPDB6ZZEm9bWo,993
transformers/models/wav2vec2_phoneme/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2_phoneme/__pycache__/tokenization_wav2vec2_phoneme.cpython-310.pyc,,
transformers/models/wav2vec2_phoneme/tokenization_wav2vec2_phoneme.py,sha256=kF6VHvIC7r3TdlbHayUbWBlq7MEei_hwqhz65e9p5Cg,23161
transformers/models/wav2vec2_with_lm/__init__.py,sha256=d_lvk8QAia4BIKN7d_Uy3HdRqrDp_ZJHTDZ-nkHKwPA,981
transformers/models/wav2vec2_with_lm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wav2vec2_with_lm/__pycache__/processing_wav2vec2_with_lm.cpython-310.pyc,,
transformers/models/wav2vec2_with_lm/processing_wav2vec2_with_lm.py,sha256=vBudrF2AwNRurSzdIS46l4T2rXhlFvFWpMsTDJOOZ58,30000
transformers/models/wavlm/__init__.py,sha256=PrI2Y7NGaErI2zAdqWvlcqk3xpQ5Ve0H6k3uOrw_W6w,1787
transformers/models/wavlm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/wavlm/__pycache__/configuration_wavlm.cpython-310.pyc,,
transformers/models/wavlm/__pycache__/convert_wavlm_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wavlm/__pycache__/convert_wavlm_original_s3prl_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/wavlm/__pycache__/modeling_wavlm.cpython-310.pyc,,
transformers/models/wavlm/configuration_wavlm.py,sha256=vUo373Hhq14_GYvQYT97zhT18ZPBFy08p7Bjp2Y8eL8,18536
transformers/models/wavlm/convert_wavlm_original_pytorch_checkpoint_to_pytorch.py,sha256=dYZIX8q3-JQ-LkhggxjbMWPjixu_ZpONwXqQRu9ImjQ,8579
transformers/models/wavlm/convert_wavlm_original_s3prl_checkpoint_to_pytorch.py,sha256=pMXFACce5UgAMK1uHdwI6ksoirFDr6G0OER6z1yFYFM,4813
transformers/models/wavlm/modeling_wavlm.py,sha256=DU3eRVWvDI5_83AZEcooYV_oGcRM1dLDv1IKE2LV6Hw,78802
transformers/models/whisper/__init__.py,sha256=e87C_luRTn1AFQiJMwAUhy9mZzOHY-Fxy5nTfkNNOag,4060
transformers/models/whisper/__pycache__/__init__.cpython-310.pyc,,
transformers/models/whisper/__pycache__/configuration_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/convert_openai_to_hf.cpython-310.pyc,,
transformers/models/whisper/__pycache__/english_normalizer.cpython-310.pyc,,
transformers/models/whisper/__pycache__/feature_extraction_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/generation_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/modeling_flax_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/modeling_tf_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/modeling_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/processing_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/tokenization_whisper.cpython-310.pyc,,
transformers/models/whisper/__pycache__/tokenization_whisper_fast.cpython-310.pyc,,
transformers/models/whisper/configuration_whisper.py,sha256=xBZbk7DY2SHfMQ9TNEEoxAs7dTjbcXeq4cT31sGNVac,16991
transformers/models/whisper/convert_openai_to_hf.py,sha256=e1E6mFHUwF4o3BvUhwNya-P5yGRGHcPCwdni4I0OdQo,14961
transformers/models/whisper/english_normalizer.py,sha256=MTJ16OhstprR2X8owfEJmONqkoSHHyzztENejmEhSBM,22822
transformers/models/whisper/feature_extraction_whisper.py,sha256=_BDJ8s8EsdX8cnQu8r6qT554tPcgKhqiY5wHgY_akS4,14823
transformers/models/whisper/generation_whisper.py,sha256=xgWR4yOfp5-raY5HmaIAPh6RWyOfTModhFDrTosGH3c,91095
transformers/models/whisper/modeling_flax_whisper.py,sha256=heYV1oX9VfM6sz2UHFani-6yRTgUhWnyiBb2aRnyKJQ,73610
transformers/models/whisper/modeling_tf_whisper.py,sha256=Gk5jMed88BU6x_zeG3o9KYM6nGHyCuSFpzAc36IBM9U,84764
transformers/models/whisper/modeling_whisper.py,sha256=AeqCxMl8F_hPae7KpkNlCFAf8Wqcll32CT1lzOl2UhA,105809
transformers/models/whisper/processing_whisper.py,sha256=_f1JxM_uoe5KJ9CuDNhTtVqJclcG4nA57RuYYviwmSI,3890
transformers/models/whisper/tokenization_whisper.py,sha256=WsnOLlIbTA-orTOzTo8rZy1qgEFTDXw9ELRAcLmjBu0,55351
transformers/models/whisper/tokenization_whisper_fast.py,sha256=N2zISmW5SBq61YwUNQc-wksS-1XVFiHj7jkWlSb1Z1M,29302
transformers/models/x_clip/__init__.py,sha256=HNjpuri5BG5qGqwuhiAYO3WFetp9aclHm31FfYzKnMU,1865
transformers/models/x_clip/__pycache__/__init__.cpython-310.pyc,,
transformers/models/x_clip/__pycache__/configuration_x_clip.cpython-310.pyc,,
transformers/models/x_clip/__pycache__/convert_x_clip_original_pytorch_to_hf.cpython-310.pyc,,
transformers/models/x_clip/__pycache__/modeling_x_clip.cpython-310.pyc,,
transformers/models/x_clip/__pycache__/processing_x_clip.cpython-310.pyc,,
transformers/models/x_clip/configuration_x_clip.py,sha256=Vgh0-scQiku0BQBWEDWivtcUbJWij4FFEaOY-1RTRCw,20308
transformers/models/x_clip/convert_x_clip_original_pytorch_to_hf.py,sha256=WzXe8IKqSz4Bi78EIvRA6C3QiLL4c-SpARggHjIWtt4,18066
transformers/models/x_clip/modeling_x_clip.py,sha256=l7ksAxdyOtoURoMEz0lDywRvtgAss6-iyXTRvKH7pGY,70077
transformers/models/x_clip/processing_x_clip.py,sha256=vuwuN_pNagPMfdvGJrSbhQVTslOHBMGFgYV2xD9BHsw,6897
transformers/models/xglm/__init__.py,sha256=yCOfidBHseDwbodeG6oNldoDi3uRb7_WotolzI1eNjc,3615
transformers/models/xglm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xglm/__pycache__/configuration_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/convert_xglm_original_ckpt_to_trfms.cpython-310.pyc,,
transformers/models/xglm/__pycache__/modeling_flax_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/modeling_tf_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/modeling_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/tokenization_xglm.cpython-310.pyc,,
transformers/models/xglm/__pycache__/tokenization_xglm_fast.cpython-310.pyc,,
transformers/models/xglm/configuration_xglm.py,sha256=FFh8lXfaYpMMXl3LpB3MgY7Bm0JgL9jRMGvmCXFPHO4,5846
transformers/models/xglm/convert_xglm_original_ckpt_to_trfms.py,sha256=9fjXP40nMFbiI9H0VV66Buqk9JQrPhAFERCOBYHl_7g,2325
transformers/models/xglm/modeling_flax_xglm.py,sha256=mO8pJ0ZwSq3YFyxEU5_iZbGQD_8bLYJZz09AzB6tZEg,33115
transformers/models/xglm/modeling_tf_xglm.py,sha256=58_Fj9tHgKJA9YsNk-MjTeQt4hirVhdgEuWtdjYfzqE,45275
transformers/models/xglm/modeling_xglm.py,sha256=Oee6984iN0-_c7AS4XM1tHJ7qVHxNWEFzpcNiFiERaw,39222
transformers/models/xglm/tokenization_xglm.py,sha256=oIF19hBaND3aIHnjkZRjhz7Y0172g0hYzdfH43LdEXo,12483
transformers/models/xglm/tokenization_xglm_fast.py,sha256=7Cicl8Ilnoq_RbwK136hhQgBiL6-tf0h3vjxifUwAzE,7588
transformers/models/xlm/__init__.py,sha256=BJ39L6oezmjY0dUv0pCCKJgCB-uvo7eBulJHjn5dIIw,3030
transformers/models/xlm/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlm/__pycache__/configuration_xlm.cpython-310.pyc,,
transformers/models/xlm/__pycache__/convert_xlm_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/xlm/__pycache__/modeling_tf_xlm.cpython-310.pyc,,
transformers/models/xlm/__pycache__/modeling_xlm.cpython-310.pyc,,
transformers/models/xlm/__pycache__/tokenization_xlm.cpython-310.pyc,,
transformers/models/xlm/configuration_xlm.py,sha256=FWSszRdcATzwOjDYUhfkhlkxzUeS8x0o3pPgVqC85fk,11010
transformers/models/xlm/convert_xlm_original_pytorch_checkpoint_to_pytorch.py,sha256=WUF2ZQtwzejYGtF54ToSahenIRvAFxgtUZ5ckzQl9Hc,2933
transformers/models/xlm/modeling_tf_xlm.py,sha256=tMH3Tn8UQzzOh787HbwEbQ1X83dTCFXYlO7ueKv0x34,56410
transformers/models/xlm/modeling_xlm.py,sha256=cQdF1w0eRIpmy8Ppexw0IfKTNuzY7XUaHAQd8yqfITQ,54676
transformers/models/xlm/tokenization_xlm.py,sha256=gLVAmEmhqwBkc6g1JUKkatD-bkFjE9mCG_z9MmHuIeg,24447
transformers/models/xlm_roberta/__init__.py,sha256=EBT6vyuHrYf1F9GWBJAyp-jhoHPFP_hwT-2fSYmoowA,5381
transformers/models/xlm_roberta/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/configuration_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_flax_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_tf_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/modeling_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/tokenization_xlm_roberta.cpython-310.pyc,,
transformers/models/xlm_roberta/__pycache__/tokenization_xlm_roberta_fast.cpython-310.pyc,,
transformers/models/xlm_roberta/configuration_xlm_roberta.py,sha256=6wwemXvnEbXKY3bERbWUEJMFCqAQTGd_cc04SDrPuew,7514
transformers/models/xlm_roberta/modeling_flax_xlm_roberta.py,sha256=OjstH2WR6oioiTGvUT6HwEvJxkOx24iDN2cUC8Py8BQ,58445
transformers/models/xlm_roberta/modeling_tf_xlm_roberta.py,sha256=ltQYeMYT2z6uFn61EuBzN7d8ufmTtOox_wWSMcDDAoU,81822
transformers/models/xlm_roberta/modeling_xlm_roberta.py,sha256=U-kD2coSvi7xhENxI_woO0B0Wfa12aXcygUNOwCi0Rs,72954
transformers/models/xlm_roberta/tokenization_xlm_roberta.py,sha256=vPcsKZVYDyWSeA48n7PkJZk5iANKIngyzpkzDV6dofY,12705
transformers/models/xlm_roberta/tokenization_xlm_roberta_fast.py,sha256=iPgRJmnSFPqIbf29saxZE--ZGApKLfsGx1VqxD4SfP8,7920
transformers/models/xlm_roberta_xl/__init__.py,sha256=1kabrrWXrbvGQNfyvEoFkAuxr3-YX3MHtJ-9J50D45Y,2181
transformers/models/xlm_roberta_xl/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/configuration_xlm_roberta_xl.cpython-310.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/convert_xlm_roberta_xl_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/xlm_roberta_xl/__pycache__/modeling_xlm_roberta_xl.cpython-310.pyc,,
transformers/models/xlm_roberta_xl/configuration_xlm_roberta_xl.py,sha256=JdiaUTf5JMNJiRcB4wfmUtyc8WJL0TzXJ3tktg6NR3g,7262
transformers/models/xlm_roberta_xl/convert_xlm_roberta_xl_original_pytorch_checkpoint_to_pytorch.py,sha256=zVa6azx9rd33D3JkH2uqJ6W20TosJyWi9eLm3LNtc5U,8228
transformers/models/xlm_roberta_xl/modeling_xlm_roberta_xl.py,sha256=_ILvIhCwz3-k08sdVKUspItJ9FqliXNuc1x3gpPTKBg,69273
transformers/models/xlnet/__init__.py,sha256=oQijY83K76LLKusrEiTIHmXp_47NeIwFobmYaWN-TD4,4014
transformers/models/xlnet/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/configuration_xlnet.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/convert_xlnet_original_tf_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/modeling_tf_xlnet.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/modeling_xlnet.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/tokenization_xlnet.cpython-310.pyc,,
transformers/models/xlnet/__pycache__/tokenization_xlnet_fast.cpython-310.pyc,,
transformers/models/xlnet/configuration_xlnet.py,sha256=MOnMVQJHTsUnr-MhJmX8ykvQT5Gh6m60O9MxgVetlP8,10925
transformers/models/xlnet/convert_xlnet_original_tf_checkpoint_to_pytorch.py,sha256=vabPDgM_imZsTVqlUq_YrKElpWhEpiNf8xkzShmgH6o,3687
transformers/models/xlnet/modeling_tf_xlnet.py,sha256=LsyGRSTR0AnPcVdwwkbhWX6SMMyeWl7QMsEH4Y5GrKg,77603
transformers/models/xlnet/modeling_xlnet.py,sha256=aR8wDx08uKcocQ3lLdlVRCIC49CUaDffN7x2lRajlbo,92841
transformers/models/xlnet/tokenization_xlnet.py,sha256=SQXMFFbN5yjD08_xoAt5l1Ruv_-e9c2Ys82oWv0Punw,15701
transformers/models/xlnet/tokenization_xlnet_fast.py,sha256=UFRytSMjW-vIZ_3Kt8acm2aNOp70i-msJibfBHOI9Sw,9364
transformers/models/xmod/__init__.py,sha256=hc6cAUa8-JQkHCPfgyOnqbXvJ9dD_EHGUl5tTbB6Cts,2149
transformers/models/xmod/__pycache__/__init__.cpython-310.pyc,,
transformers/models/xmod/__pycache__/configuration_xmod.cpython-310.pyc,,
transformers/models/xmod/__pycache__/convert_xmod_original_pytorch_checkpoint_to_pytorch.cpython-310.pyc,,
transformers/models/xmod/__pycache__/modeling_xmod.cpython-310.pyc,,
transformers/models/xmod/configuration_xmod.py,sha256=qD2eTUuRecfe4opPyy3uEoOnUqU_3B9wbKkEOrngBjs,9110
transformers/models/xmod/convert_xmod_original_pytorch_checkpoint_to_pytorch.py,sha256=yFSAtXjxbAy6uXBg2XinRbk3VSEBOsWj1ugBhVNrGjQ,9859
transformers/models/xmod/modeling_xmod.py,sha256=mFUGs9ck-8vm8r2gFOhCNQzH_Z5hPkF0TmLoOr0mD8c,76199
transformers/models/yolos/__init__.py,sha256=lWuTOt7yUlqdzoN5rO3GnI0Oz26hHgh54BW3fOmJur8,2228
transformers/models/yolos/__pycache__/__init__.cpython-310.pyc,,
transformers/models/yolos/__pycache__/configuration_yolos.cpython-310.pyc,,
transformers/models/yolos/__pycache__/convert_yolos_to_pytorch.cpython-310.pyc,,
transformers/models/yolos/__pycache__/feature_extraction_yolos.cpython-310.pyc,,
transformers/models/yolos/__pycache__/image_processing_yolos.cpython-310.pyc,,
transformers/models/yolos/__pycache__/modeling_yolos.cpython-310.pyc,,
transformers/models/yolos/configuration_yolos.py,sha256=9WA88-q7oO8vat3f0nJr72UObDTJpMQjFCj1ACAdZM0,7571
transformers/models/yolos/convert_yolos_to_pytorch.py,sha256=dDZ8lJdoh8FEi6iujUPGhnr-ejbcTGQIIRUB2c7WA8c,11258
transformers/models/yolos/feature_extraction_yolos.py,sha256=0ebN1Be4y86C2yyN2rMQ9AbguEDjcQ7fkabropUpwcs,1481
transformers/models/yolos/image_processing_yolos.py,sha256=MHyyUno6Oc_ohIcaZWDQjUZnPIqLSpqWl98p0d3bCoQ,67891
transformers/models/yolos/modeling_yolos.py,sha256=YjrKyFTOC34zXaz-3wZBsjyJjRCvKUhHz2Gg3xZRkLg,60200
transformers/models/yoso/__init__.py,sha256=NjOBUKuRta4m_jCzNmY0nT7RT53ZnXh-ucOjp0CB7ww,1906
transformers/models/yoso/__pycache__/__init__.cpython-310.pyc,,
transformers/models/yoso/__pycache__/configuration_yoso.cpython-310.pyc,,
transformers/models/yoso/__pycache__/convert_yoso_pytorch_to_pytorch.cpython-310.pyc,,
transformers/models/yoso/__pycache__/modeling_yoso.cpython-310.pyc,,
transformers/models/yoso/configuration_yoso.py,sha256=LqHGF4Au5VTYXgGw0X9KAlldG-bqjqE48pvQMV5iAxw,6688
transformers/models/yoso/convert_yoso_pytorch_to_pytorch.py,sha256=VjPOSLINfkiaHx8M3dTNMdC8hXh3M1yyhIQ9t4Vzqk0,4115
transformers/models/yoso/modeling_yoso.py,sha256=BWwP4J-OFSW6NIBEaFhT84oS9_ATxWE75C6vigYfJhk,54745
transformers/models/zoedepth/__init__.py,sha256=eGStAGKPksn9bnl7J6UdMlegf97Xm0khGgrQJ5HupKs,2148
transformers/models/zoedepth/__pycache__/__init__.cpython-310.pyc,,
transformers/models/zoedepth/__pycache__/configuration_zoedepth.cpython-310.pyc,,
transformers/models/zoedepth/__pycache__/convert_zoedepth_to_hf.cpython-310.pyc,,
transformers/models/zoedepth/__pycache__/image_processing_zoedepth.cpython-310.pyc,,
transformers/models/zoedepth/__pycache__/modeling_zoedepth.cpython-310.pyc,,
transformers/models/zoedepth/configuration_zoedepth.py,sha256=SFUH_TQgkcFJM_f482nBbEazK_QEYPmDHLGM22hROCo,12684
transformers/models/zoedepth/convert_zoedepth_to_hf.py,sha256=STtF2DGuZvFgSGpeDj8uNQRadiLQ0h17HvoKDD5TdFQ,18075
transformers/models/zoedepth/image_processing_zoedepth.py,sha256=0he1ubRABbuOFh7mLwLrkD1cYqJiNHsknPN3mSAufcw,22616
transformers/models/zoedepth/modeling_zoedepth.py,sha256=s3PVtBZYSbq2hgK7gibD0KrZSzefB6iEujvIa4lSdw0,57230
transformers/onnx/__init__.py,sha256=wALLY4TPOK2iPrFcfZf_WiEmTRAU6dAWHElxGdexr58,1548
transformers/onnx/__main__.py,sha256=JZ9ZmeRsnDitwTMWb-dFT8W9AEmMoMKLQ3SvbyCkY0w,9497
transformers/onnx/__pycache__/__init__.cpython-310.pyc,,
transformers/onnx/__pycache__/__main__.cpython-310.pyc,,
transformers/onnx/__pycache__/config.cpython-310.pyc,,
transformers/onnx/__pycache__/convert.cpython-310.pyc,,
transformers/onnx/__pycache__/features.cpython-310.pyc,,
transformers/onnx/__pycache__/utils.cpython-310.pyc,,
transformers/onnx/config.py,sha256=zPDgC_HSLmMeqPkcLv_Y8EfbfLLEDLqPrvrfQCRyhl8,32556
transformers/onnx/convert.py,sha256=ZSh9jQE6B6cCxhlSbKLHxNmj48HkXXdl-HF7iGtZy5k,19369
transformers/onnx/features.py,sha256=GSuwZj760THxAkDmJYROt43La0GaY-bA19j2bE-XYVI,28264
transformers/onnx/utils.py,sha256=39Uw_GkFBsTb6ZvMIHRTnI289aQDhc6hwfEapaBGE-o,3625
transformers/optimization.py,sha256=pOi-3ADMoveZKUqQfi3yRbzyplmi_x5z-gaUNhGXbBk,39133
transformers/optimization_tf.py,sha256=UPtbbeR__ZoPG7eBD5XMBBiYfjAZR8a5L2zWJsCLL_8,16854
transformers/pipelines/__init__.py,sha256=bLn1k52l3gKp0nLWefHMVSY6gaPJ_gpCHsMhkpquxiU,50675
transformers/pipelines/__pycache__/__init__.cpython-310.pyc,,
transformers/pipelines/__pycache__/audio_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/audio_utils.cpython-310.pyc,,
transformers/pipelines/__pycache__/automatic_speech_recognition.cpython-310.pyc,,
transformers/pipelines/__pycache__/base.cpython-310.pyc,,
transformers/pipelines/__pycache__/depth_estimation.cpython-310.pyc,,
transformers/pipelines/__pycache__/document_question_answering.cpython-310.pyc,,
transformers/pipelines/__pycache__/feature_extraction.cpython-310.pyc,,
transformers/pipelines/__pycache__/fill_mask.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_feature_extraction.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_segmentation.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_to_image.cpython-310.pyc,,
transformers/pipelines/__pycache__/image_to_text.cpython-310.pyc,,
transformers/pipelines/__pycache__/mask_generation.cpython-310.pyc,,
transformers/pipelines/__pycache__/object_detection.cpython-310.pyc,,
transformers/pipelines/__pycache__/pt_utils.cpython-310.pyc,,
transformers/pipelines/__pycache__/question_answering.cpython-310.pyc,,
transformers/pipelines/__pycache__/table_question_answering.cpython-310.pyc,,
transformers/pipelines/__pycache__/text2text_generation.cpython-310.pyc,,
transformers/pipelines/__pycache__/text_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/text_generation.cpython-310.pyc,,
transformers/pipelines/__pycache__/text_to_audio.cpython-310.pyc,,
transformers/pipelines/__pycache__/token_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/video_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/visual_question_answering.cpython-310.pyc,,
transformers/pipelines/__pycache__/zero_shot_audio_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/zero_shot_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/zero_shot_image_classification.cpython-310.pyc,,
transformers/pipelines/__pycache__/zero_shot_object_detection.cpython-310.pyc,,
transformers/pipelines/audio_classification.py,sha256=aPmCh-ONEFoevaEalW2_iRxklWOo3LvWarpwgIak14U,8820
transformers/pipelines/audio_utils.py,sha256=x5JXEWedeMlYcz32JS5HLWBTpy0FPXJvCns_WnXYOnA,9137
transformers/pipelines/automatic_speech_recognition.py,sha256=XJjsaZyGBpij8NTjsiaRR7-vuj8eY4gJ0JOtBKwdgqY,38280
transformers/pipelines/base.py,sha256=zCrUX3owYgn6hK7DM3yB3kFaN8q5ftME18yJE6bGgus,57451
transformers/pipelines/depth_estimation.py,sha256=p_2x3plQ463mFvJU1ymqJgjfPaLw2U6Q7obLcUOIzuU,4573
transformers/pipelines/document_question_answering.py,sha256=x__Ufh2r6v0MApImzRrjWCSRgaZdCIXo7wJxH1-cZsg,23709
transformers/pipelines/feature_extraction.py,sha256=Ar_hPljY1Fa_xAsRYX4cCCss1vf-iC5uuKYHp3rejd0,3374
transformers/pipelines/fill_mask.py,sha256=jnZMK5aZyxlttXtzUISh3ZgvbcI7dIj-nB3Fk37N7Qw,11634
transformers/pipelines/image_classification.py,sha256=q_rjWi9qJLSvEai80fada5A0rC6HN2S7tVZYIE3gddo,8867
transformers/pipelines/image_feature_extraction.py,sha256=KIn3yldAUTxFmal-SNF7CPkcx1h3q_KMvyjUsHzsx9o,4732
transformers/pipelines/image_segmentation.py,sha256=lL6FGBLLoRLoN9ZDMv6qqaujlPUvwkNYo-7l_LjEs1M,9308
transformers/pipelines/image_to_image.py,sha256=VHB7ElQIYrBGPQsD5QtTZ0p4piXJ_r13KU4fkWgbLms,5022
transformers/pipelines/image_to_text.py,sha256=PInORk8qbjwZ50YcKzNwHOXzwEgjlKOpczrnzlSWi0w,8436
transformers/pipelines/mask_generation.py,sha256=HRmUx-H9pl85go26t70Th7lsPQu_nDdnHgi62FkKL-s,13204
transformers/pipelines/object_detection.py,sha256=MVVBldt9VChXju2ZCVrvyGeuhKRfi9hdG1OZzie_uCk,8015
transformers/pipelines/pt_utils.py,sha256=tVI0GBVS7wbsbDEWeFLKju7fVG7I5-xVscndq-ykRko,12768
transformers/pipelines/question_answering.py,sha256=T_1wU0bfKlhF9Rv4buXGKwuMWRJq4fYNG30I4C6JXGk,29884
transformers/pipelines/table_question_answering.py,sha256=cq-xxL2izvKZIDHlCuFkKsiqmXEe37KyO4YeYZyCqQA,19830
transformers/pipelines/text2text_generation.py,sha256=VzX8ikqSAZdYJhRZnaXSqFPaG7M2SLbkxAo59oCHHXY,17245
transformers/pipelines/text_classification.py,sha256=NjJf2cr-15JU9K244MEJT0D-bCYc7R50UFUFyrojil4,10615
transformers/pipelines/text_generation.py,sha256=dKHuCkq4o1jjIEBC89Z7_rmoB-f62npZ4RmacRxLCxo,20054
transformers/pipelines/text_to_audio.py,sha256=XoYuTtUWYXTJGwjlcQO74Tp2fWsXBEA6u3eRyGevWVo,8345
transformers/pipelines/token_classification.py,sha256=nw-DEE_Pw8gZHjYi3xAONcLcAIQikgwrJRpchq6PxtU,26713
transformers/pipelines/video_classification.py,sha256=YCS4GS4H-vEOPWhlX-s0oNsiKBR_Mep7UTHFAPlnnqg,5494
transformers/pipelines/visual_question_answering.py,sha256=JX0aPb0WmSsx0-APu5t9ulPks2O9-DeHT2j-1Zxf5E8,8624
transformers/pipelines/zero_shot_audio_classification.py,sha256=IiZq9SQNnQSNo0bnA2_NU4Ym6MZjXTlVpRsN_I6HH2Y,6794
transformers/pipelines/zero_shot_classification.py,sha256=WLgjtF0fOEaCiQb9QUu9vcNfJLP9M5nRnJGTgXgRKKU,12347
transformers/pipelines/zero_shot_image_classification.py,sha256=i6P1BrxY9-SrmDD1cchUNgFImc4xpyk_U3gARODY75M,6928
transformers/pipelines/zero_shot_object_detection.py,sha256=nvabAvHT3nvu-gd3962aQsYSHaQyw7SQDJmCsLlD8sg,9580
transformers/processing_utils.py,sha256=QudTlfLt5jhKITq6dJx621E5tJDQt2zlJ76v9_v4TyM,45330
transformers/pytorch_utils.py,sha256=uCfskvCwEom7Y4BhagH1DhnGJfhy_suM4TpmYE8JSLU,11943
transformers/quantizers/__init__.py,sha256=hCprQnoI20-O1FSMSRgD-P9_NKEzN7kEfY66_BrQxz0,699
transformers/quantizers/__pycache__/__init__.cpython-310.pyc,,
transformers/quantizers/__pycache__/auto.cpython-310.pyc,,
transformers/quantizers/__pycache__/base.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_aqlm.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_awq.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_bnb_4bit.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_bnb_8bit.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_eetq.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_fbgemm_fp8.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_gptq.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_hqq.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizer_quanto.cpython-310.pyc,,
transformers/quantizers/__pycache__/quantizers_utils.cpython-310.pyc,,
transformers/quantizers/auto.py,sha256=n1Q-26YKuZvGBvvyXhpudYkZRbC-51dFtAIjfmkhmcQ,7286
transformers/quantizers/base.py,sha256=1BGAy4uqtOgYFtIkR0IcnWZSyGXgbSfySzoTZeR60Nw,9693
transformers/quantizers/quantizer_aqlm.py,sha256=cBO72I147eZzE_9W041rcY1GB5FCcxd3_ynKPGFbZWc,3681
transformers/quantizers/quantizer_awq.py,sha256=aGxcRArT9YJiMBmj9qHF9H_AV-uOQgluKd1m-vZcJOQ,5243
transformers/quantizers/quantizer_bnb_4bit.py,sha256=4MPk704BHIvRrf_n8mdnmcY0oQ4Xzozwla5-xNaFkdc,14842
transformers/quantizers/quantizer_bnb_8bit.py,sha256=pDr8PQs-T8E64-yKl41iAedy_PiIEc6DzR-spjgF6Pw,13269
transformers/quantizers/quantizer_eetq.py,sha256=NRZP2CWgrHnpf5ClHMIzlAS4R3z6I_l9sziPlJ8JpS8,6576
transformers/quantizers/quantizer_fbgemm_fp8.py,sha256=F29tNBgVZBPjHxo82w4rDityS7dTbxHfDe5eYIKeKIQ,8131
transformers/quantizers/quantizer_gptq.py,sha256=ZWNQY2WF6mzUV-SwYg1PZIM0kZ3JJyYGe3gF2mZcZ58,3878
transformers/quantizers/quantizer_hqq.py,sha256=cbx5jwVvBggYNGhFp3Qb1lh8igYHvx_5K3FMvY9F1Y8,7539
transformers/quantizers/quantizer_quanto.py,sha256=-qIlgLelWfk3ExYnWBul2z2L7B9Nw7ijxgDmC8g3Ygw,7847
transformers/quantizers/quantizers_utils.py,sha256=6bgmf8mLxow6gXonTFX7PLfqFsf6plUj7DOeXnXhwMM,1066
transformers/safetensors_conversion.py,sha256=1998DSxTsl99crqBPfNXRq7Xb6ABRc5Ts4R5oxUM9p0,4570
transformers/sagemaker/__init__.py,sha256=fKtKAHamz_CLL9jPGCa2E-1n8RmuS-58qGtzZuKc3qg,730
transformers/sagemaker/__pycache__/__init__.cpython-310.pyc,,
transformers/sagemaker/__pycache__/trainer_sm.cpython-310.pyc,,
transformers/sagemaker/__pycache__/training_args_sm.cpython-310.pyc,,
transformers/sagemaker/trainer_sm.py,sha256=7GsKLtjdMfKp98OwHD7RcBsl745OOwHAaBswkfLkfsE,1044
transformers/sagemaker/training_args_sm.py,sha256=4ZnQhITfMwT0y2Y2MvkI11PEB_yfTX5Z7WrPKt0VXD8,5389
transformers/testing_utils.py,sha256=zsUZouS0cAE_dIQ3XRev-LtOswACWEh8hbw5Pi0Sqo0,85806
transformers/tf_utils.py,sha256=v4iybFTb3eRDgGzhAUTVYim-qNZvYF2k6rlHk7wTii4,11386
transformers/time_series_utils.py,sha256=MT780YtbZhdZcz7I9WJ9XVpmZgCVUT2eJ4-g8snaYvQ,7521
transformers/tokenization_utils.py,sha256=ByW2e1bdVYIix12Qwoio43jDggVVF4oh2VULAkNcMRA,46880
transformers/tokenization_utils_base.py,sha256=DmzId3rLBp0HZ6UFhEkuapaQkOQMRz4mKZDkdRTlPDk,211607
transformers/tokenization_utils_fast.py,sha256=kDWEpIg2NKMy-uM_wqBxGMDmCkmBwj47L3efZnN1bUE,38846
transformers/trainer.py,sha256=iDIAB9W1mNPUt-SvY-pH74bhh32mpaOuciyrUXTt64Q,228749
transformers/trainer_callback.py,sha256=ysE1SKWatjBOkvNjN6AMtgxihxDFaCnSf4AjIKSU9Hc,30054
transformers/trainer_pt_utils.py,sha256=lAZvD4LlCnpSTbLbt6OiVn5AilOoAqGyOrEDsPuoNIY,61033
transformers/trainer_seq2seq.py,sha256=6oSCG9GlQmUBpasw3nFI_ngF6KCrxPixL91ob7CQMCk,17240
transformers/trainer_utils.py,sha256=FSPgGU6JD-hbH0WitCzyMZw0gRdqDGAlO7SSQBNZJvk,31155
transformers/training_args.py,sha256=_eiLO0o4G91lsbsEhuHbq6iLpnYUiKlwStcvPg8HaH8,154606
transformers/training_args_seq2seq.py,sha256=k8qyPQAo5GWlcToN3tnzW7dE4xyP7i7HRjP_sgxlllA,4308
transformers/training_args_tf.py,sha256=2m7MCkBCsSiHHmej59xpmVKgYYhoJ9sM544xRNlg6Gc,14576
transformers/utils/__init__.py,sha256=ms76nnXLef7IJXrdAcPDg-4qnw9WXHV8c5lfqVQXQJ4,8093
transformers/utils/__pycache__/__init__.cpython-310.pyc,,
transformers/utils/__pycache__/backbone_utils.cpython-310.pyc,,
transformers/utils/__pycache__/bitsandbytes.cpython-310.pyc,,
transformers/utils/__pycache__/chat_template_utils.cpython-310.pyc,,
transformers/utils/__pycache__/constants.cpython-310.pyc,,
transformers/utils/__pycache__/deprecation.cpython-310.pyc,,
transformers/utils/__pycache__/doc.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_detectron2_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_flax_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_keras_nlp_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_music_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_pt_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_sentencepiece_and_tokenizers_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_sentencepiece_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_speech_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_tensorflow_text_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_tf_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_tokenizers_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_torchaudio_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_torchvision_objects.cpython-310.pyc,,
transformers/utils/__pycache__/dummy_vision_objects.cpython-310.pyc,,
transformers/utils/__pycache__/fx.cpython-310.pyc,,
transformers/utils/__pycache__/generic.cpython-310.pyc,,
transformers/utils/__pycache__/hp_naming.cpython-310.pyc,,
transformers/utils/__pycache__/hub.cpython-310.pyc,,
transformers/utils/__pycache__/import_utils.cpython-310.pyc,,
transformers/utils/__pycache__/logging.cpython-310.pyc,,
transformers/utils/__pycache__/model_parallel_utils.cpython-310.pyc,,
transformers/utils/__pycache__/notebook.cpython-310.pyc,,
transformers/utils/__pycache__/peft_utils.cpython-310.pyc,,
transformers/utils/__pycache__/quantization_config.cpython-310.pyc,,
transformers/utils/__pycache__/sentencepiece_model_pb2.cpython-310.pyc,,
transformers/utils/__pycache__/sentencepiece_model_pb2_new.cpython-310.pyc,,
transformers/utils/__pycache__/versions.cpython-310.pyc,,
transformers/utils/backbone_utils.py,sha256=BZJOavniwDKDkz_f7yD-m8ZGDUx-li5FwqVZtJjm3rM,17431
transformers/utils/bitsandbytes.py,sha256=LzOKwcHWAxxZZv-7Ts9Q0vlEYvHd18affVgVbiR3Tzs,1040
transformers/utils/chat_template_utils.py,sha256=m1uWPAtyj6R4e9uGmdHIItJ0mIBVIvBPkeVrx06wYso,12527
transformers/utils/constants.py,sha256=sZsUwOnA3CbtN1svs9YoaNLTTsAc9RVaITsgpf8K4iI,282
transformers/utils/deprecation.py,sha256=1XHELEE0sqQkrB_FhtBcwAjHk5ETcLVCsEeakZa8Jdw,7614
transformers/utils/doc.py,sha256=1oAZT_lRqLlvI-tL9BCd_whVOvGCN1dub8b5Z85n3GA,41001
transformers/utils/dummy_detectron2_objects.py,sha256=n7Pt_7sbVBNfohKGcOARB-ZcPcJRbjEAcoLd2vTXndU,340
transformers/utils/dummy_essentia_and_librosa_and_pretty_midi_and_scipy_and_torch_objects.py,sha256=n6pY4s7zCII3dzo7Ejd0RviHa_pMateuDEwbbHgsTUY,902
transformers/utils/dummy_flax_objects.py,sha256=Sovj4TBFszbKlf71kmzGnjOuO9ghixCdidECYJ0tSpA,33505
transformers/utils/dummy_keras_nlp_objects.py,sha256=AVWt2orICCUXi754bkavvqPzYO91PjER-FlUZAw2jZc,294
transformers/utils/dummy_music_objects.py,sha256=1lxIebYUOdHJWMQ_T5IQgPgcO_wp_8YM_HGc3skuGVg,458
transformers/utils/dummy_pt_objects.py,sha256=5o2fFDIUGv-R8wv8s9hue4ZDXbR4pkhr3iOP6w0Vico,232894
transformers/utils/dummy_sentencepiece_and_tokenizers_objects.py,sha256=BgPLr8Wz8A-17K86x04N21CKXtWNQLJEWx2c4aZRqaA,286
transformers/utils/dummy_sentencepiece_objects.py,sha256=svoWyAQ-0n03L6yXXnLADyd09P_XMxkU4Gv0wKHlD8A,6455
transformers/utils/dummy_speech_objects.py,sha256=9eFm1cjdsYOPBoAz9JTgP35Bg8WF2C9AZ_y1hFpKZdQ,465
transformers/utils/dummy_tensorflow_text_objects.py,sha256=43V0IA2kb9gtuL0S1OL1eRFFxzQwKg4pPjMVuXUB5qg,306
transformers/utils/dummy_tf_objects.py,sha256=HTQh-t4P-4jxzLGMER-vqKK4ioIGBZERH1TPANz4WLc,67037
transformers/utils/dummy_tokenizers_objects.py,sha256=HW_eUXlwV3VPXxfSHSX3l4taOLbrajkziGUKTx8PCtE,11456
transformers/utils/dummy_torchaudio_objects.py,sha256=9A7Y4643_hTaqqZKlL-O524wRnrmNtODxisuDdO_7kU,488
transformers/utils/dummy_torchvision_objects.py,sha256=IJutS_GiJRCPu6fUJ4ejexaRHMBptdF5_9KmWFjXovY,482
transformers/utils/dummy_vision_objects.py,sha256=sbwQdDQVfj-v4MltbyBMCwsYokTW_40oyoHK6pNGI_A,16081
transformers/utils/fx.py,sha256=lFKOdANuKSqam3fyDaVe1iLW5tz1bkJusICKWZsq1Bg,57414
transformers/utils/generic.py,sha256=OPNckYMIA1NZzN3adlNuUdLv7sJpv5LT-G7mTJxHWYc,27969
transformers/utils/hp_naming.py,sha256=vqcOXcDOyqbISWo8-ClUJUOBVbZM1h08EcymTwcRthc,4979
transformers/utils/hub.py,sha256=O1oXy8wKKqp8hwViK8_liaSjYOTKHYL0cIPSIvZO8Ho,56951
transformers/utils/import_utils.py,sha256=wwUYqa4h0ylF9CeA8RRPheTfi7CgHxilaDTVwTcxung,55687
transformers/utils/logging.py,sha256=FfSPvB2JmPNvjjIit27MBsa_OgwQmwohqXbdHz_x0xk,11607
transformers/utils/model_parallel_utils.py,sha256=XbGU9IlFF59K_aplRxUGVnTfIZ9mpbLomKqQ08ooTew,2272
transformers/utils/notebook.py,sha256=FAbhHmNtyX95yqC1pIm04CUvx64Iwl3vNaoQRwpvYNA,15540
transformers/utils/peft_utils.py,sha256=Jw6MjvVLtQ7booot0zK6X_xqRl_PAOh3lFZj1A2Guc8,5207
transformers/utils/quantization_config.py,sha256=6BHCB3gMQDYL0GeUf1yuDv182ywArmmAQsRhyG3bPj4,49460
transformers/utils/sentencepiece_model_pb2.py,sha256=XiQs9uMEusfAZP6t6IBuTTX9yl7LiOyJEi7Ib-Wzmq0,50677
transformers/utils/sentencepiece_model_pb2_new.py,sha256=Is_lMJU8MlmXGTkRL-Ut9hDWJwEmYeXedPCHPFaqlwM,6622
transformers/utils/versions.py,sha256=C-Tqr4qGSHH64ygIBCSo8gA6azz7Dbzh8zdc_yjMkX8,4337
