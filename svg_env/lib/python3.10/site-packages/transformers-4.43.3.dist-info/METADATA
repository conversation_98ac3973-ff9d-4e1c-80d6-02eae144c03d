Metadata-Version: 2.1
Name: transformers
Version: 4.43.3
Summary: State-of-the-art Machine Learning for JAX, PyTorch and TensorFlow
Home-page: https://github.com/huggingface/transformers
Author: The Hugging Face team (past and future) with the help of all our contributors (https://github.com/huggingface/transformers/graphs/contributors)
Author-email: <EMAIL>
License: Apache 2.0 License
Keywords: NLP vision speech deep learning transformer pytorch tensorflow jax BERT GPT-2 Wav2Vec2 ViT
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.8.0
Description-Content-Type: text/markdown
Requires-Dist: filelock
Requires-Dist: huggingface-hub <1.0,>=0.23.2
Requires-Dist: numpy >=1.17
Requires-Dist: packaging >=20.0
Requires-Dist: pyyaml >=5.1
Requires-Dist: regex !=2019.12.17
Requires-Dist: requests
Requires-Dist: safetensors >=0.4.1
Requires-Dist: tokenizers <0.20,>=0.19
Requires-Dist: tqdm >=4.27
Provides-Extra: accelerate
Requires-Dist: accelerate >=0.21.0 ; extra == 'accelerate'
Provides-Extra: agents
Requires-Dist: Pillow <=15.0,>=10.0.1 ; extra == 'agents'
Requires-Dist: accelerate >=0.21.0 ; extra == 'agents'
Requires-Dist: datasets !=2.5.0 ; extra == 'agents'
Requires-Dist: diffusers ; extra == 'agents'
Requires-Dist: opencv-python ; extra == 'agents'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'agents'
Requires-Dist: torch ; extra == 'agents'
Provides-Extra: all
Requires-Dist: Pillow <=15.0,>=10.0.1 ; extra == 'all'
Requires-Dist: accelerate >=0.21.0 ; extra == 'all'
Requires-Dist: av ==9.2.0 ; extra == 'all'
Requires-Dist: codecarbon ==1.2.0 ; extra == 'all'
Requires-Dist: decord ==0.6.0 ; extra == 'all'
Requires-Dist: flax <=0.7.0,>=0.4.1 ; extra == 'all'
Requires-Dist: jax <=0.4.13,>=0.4.1 ; extra == 'all'
Requires-Dist: jaxlib <=0.4.13,>=0.4.1 ; extra == 'all'
Requires-Dist: kenlm ; extra == 'all'
Requires-Dist: keras-nlp <0.14.0,>=0.3.1 ; extra == 'all'
Requires-Dist: librosa ; extra == 'all'
Requires-Dist: onnxconverter-common ; extra == 'all'
Requires-Dist: optax <=0.1.4,>=0.0.8 ; extra == 'all'
Requires-Dist: optuna ; extra == 'all'
Requires-Dist: phonemizer ; extra == 'all'
Requires-Dist: protobuf ; extra == 'all'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'all'
Requires-Dist: ray[tune] >=2.7.0 ; extra == 'all'
Requires-Dist: scipy <1.13.0 ; extra == 'all'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'all'
Requires-Dist: sigopt ; extra == 'all'
Requires-Dist: tensorflow-text <2.16 ; extra == 'all'
Requires-Dist: tensorflow <2.16,>2.9 ; extra == 'all'
Requires-Dist: tf2onnx ; extra == 'all'
Requires-Dist: timm <=0.9.16 ; extra == 'all'
Requires-Dist: tokenizers <0.20,>=0.19 ; extra == 'all'
Requires-Dist: torch ; extra == 'all'
Requires-Dist: torchaudio ; extra == 'all'
Requires-Dist: torchvision ; extra == 'all'
Provides-Extra: audio
Requires-Dist: kenlm ; extra == 'audio'
Requires-Dist: librosa ; extra == 'audio'
Requires-Dist: phonemizer ; extra == 'audio'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'audio'
Provides-Extra: benchmark
Requires-Dist: optimum-benchmark >=0.2.0 ; extra == 'benchmark'
Provides-Extra: codecarbon
Requires-Dist: codecarbon ==1.2.0 ; extra == 'codecarbon'
Provides-Extra: deepspeed
Requires-Dist: accelerate >=0.21.0 ; extra == 'deepspeed'
Requires-Dist: deepspeed >=0.9.3 ; extra == 'deepspeed'
Provides-Extra: deepspeed-testing
Requires-Dist: GitPython <3.1.19 ; extra == 'deepspeed-testing'
Requires-Dist: accelerate >=0.21.0 ; extra == 'deepspeed-testing'
Requires-Dist: beautifulsoup4 ; extra == 'deepspeed-testing'
Requires-Dist: cookiecutter ==1.7.3 ; extra == 'deepspeed-testing'
Requires-Dist: datasets !=2.5.0 ; extra == 'deepspeed-testing'
Requires-Dist: deepspeed >=0.9.3 ; extra == 'deepspeed-testing'
Requires-Dist: dill <0.3.5 ; extra == 'deepspeed-testing'
Requires-Dist: evaluate >=0.2.0 ; extra == 'deepspeed-testing'
Requires-Dist: faiss-cpu ; extra == 'deepspeed-testing'
Requires-Dist: nltk ; extra == 'deepspeed-testing'
Requires-Dist: optuna ; extra == 'deepspeed-testing'
Requires-Dist: parameterized ; extra == 'deepspeed-testing'
Requires-Dist: protobuf ; extra == 'deepspeed-testing'
Requires-Dist: psutil ; extra == 'deepspeed-testing'
Requires-Dist: pydantic ; extra == 'deepspeed-testing'
Requires-Dist: pytest-rich ; extra == 'deepspeed-testing'
Requires-Dist: pytest-timeout ; extra == 'deepspeed-testing'
Requires-Dist: pytest-xdist ; extra == 'deepspeed-testing'
Requires-Dist: pytest <8.0.0,>=7.2.0 ; extra == 'deepspeed-testing'
Requires-Dist: rjieba ; extra == 'deepspeed-testing'
Requires-Dist: rouge-score !=0.0.7,!=0.0.8,!=0.1,!=0.1.1 ; extra == 'deepspeed-testing'
Requires-Dist: ruff ==0.4.4 ; extra == 'deepspeed-testing'
Requires-Dist: sacrebleu <2.0.0,>=1.4.12 ; extra == 'deepspeed-testing'
Requires-Dist: sacremoses ; extra == 'deepspeed-testing'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'deepspeed-testing'
Requires-Dist: tensorboard ; extra == 'deepspeed-testing'
Requires-Dist: timeout-decorator ; extra == 'deepspeed-testing'
Provides-Extra: dev
Requires-Dist: GitPython <3.1.19 ; extra == 'dev'
Requires-Dist: Pillow <=15.0,>=10.0.1 ; extra == 'dev'
Requires-Dist: accelerate >=0.21.0 ; extra == 'dev'
Requires-Dist: av ==9.2.0 ; extra == 'dev'
Requires-Dist: beautifulsoup4 ; extra == 'dev'
Requires-Dist: codecarbon ==1.2.0 ; extra == 'dev'
Requires-Dist: cookiecutter ==1.7.3 ; extra == 'dev'
Requires-Dist: datasets !=2.5.0 ; extra == 'dev'
Requires-Dist: decord ==0.6.0 ; extra == 'dev'
Requires-Dist: dill <0.3.5 ; extra == 'dev'
Requires-Dist: evaluate >=0.2.0 ; extra == 'dev'
Requires-Dist: faiss-cpu ; extra == 'dev'
Requires-Dist: flax <=0.7.0,>=0.4.1 ; extra == 'dev'
Requires-Dist: fugashi >=1.0 ; extra == 'dev'
Requires-Dist: ipadic <2.0,>=1.0.0 ; extra == 'dev'
Requires-Dist: isort >=5.5.4 ; extra == 'dev'
Requires-Dist: jax <=0.4.13,>=0.4.1 ; extra == 'dev'
Requires-Dist: jaxlib <=0.4.13,>=0.4.1 ; extra == 'dev'
Requires-Dist: kenlm ; extra == 'dev'
Requires-Dist: keras-nlp <0.14.0,>=0.3.1 ; extra == 'dev'
Requires-Dist: librosa ; extra == 'dev'
Requires-Dist: nltk ; extra == 'dev'
Requires-Dist: onnxconverter-common ; extra == 'dev'
Requires-Dist: optax <=0.1.4,>=0.0.8 ; extra == 'dev'
Requires-Dist: optuna ; extra == 'dev'
Requires-Dist: parameterized ; extra == 'dev'
Requires-Dist: phonemizer ; extra == 'dev'
Requires-Dist: protobuf ; extra == 'dev'
Requires-Dist: psutil ; extra == 'dev'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'dev'
Requires-Dist: pydantic ; extra == 'dev'
Requires-Dist: pytest-rich ; extra == 'dev'
Requires-Dist: pytest-timeout ; extra == 'dev'
Requires-Dist: pytest-xdist ; extra == 'dev'
Requires-Dist: pytest <8.0.0,>=7.2.0 ; extra == 'dev'
Requires-Dist: ray[tune] >=2.7.0 ; extra == 'dev'
Requires-Dist: rhoknp <1.3.1,>=1.1.0 ; extra == 'dev'
Requires-Dist: rjieba ; extra == 'dev'
Requires-Dist: rouge-score !=0.0.7,!=0.0.8,!=0.1,!=0.1.1 ; extra == 'dev'
Requires-Dist: ruff ==0.4.4 ; extra == 'dev'
Requires-Dist: sacrebleu <2.0.0,>=1.4.12 ; extra == 'dev'
Requires-Dist: sacremoses ; extra == 'dev'
Requires-Dist: scikit-learn ; extra == 'dev'
Requires-Dist: scipy <1.13.0 ; extra == 'dev'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'dev'
Requires-Dist: sigopt ; extra == 'dev'
Requires-Dist: sudachidict-core >=20220729 ; extra == 'dev'
Requires-Dist: sudachipy >=0.6.6 ; extra == 'dev'
Requires-Dist: tensorboard ; extra == 'dev'
Requires-Dist: tensorflow-text <2.16 ; extra == 'dev'
Requires-Dist: tensorflow <2.16,>2.9 ; extra == 'dev'
Requires-Dist: tf2onnx ; extra == 'dev'
Requires-Dist: timeout-decorator ; extra == 'dev'
Requires-Dist: timm <=0.9.16 ; extra == 'dev'
Requires-Dist: tokenizers <0.20,>=0.19 ; extra == 'dev'
Requires-Dist: torch ; extra == 'dev'
Requires-Dist: torchaudio ; extra == 'dev'
Requires-Dist: torchvision ; extra == 'dev'
Requires-Dist: unidic >=1.0.2 ; extra == 'dev'
Requires-Dist: unidic-lite >=1.0.7 ; extra == 'dev'
Requires-Dist: urllib3 <2.0.0 ; extra == 'dev'
Provides-Extra: dev-tensorflow
Requires-Dist: GitPython <3.1.19 ; extra == 'dev-tensorflow'
Requires-Dist: Pillow <=15.0,>=10.0.1 ; extra == 'dev-tensorflow'
Requires-Dist: beautifulsoup4 ; extra == 'dev-tensorflow'
Requires-Dist: cookiecutter ==1.7.3 ; extra == 'dev-tensorflow'
Requires-Dist: datasets !=2.5.0 ; extra == 'dev-tensorflow'
Requires-Dist: dill <0.3.5 ; extra == 'dev-tensorflow'
Requires-Dist: evaluate >=0.2.0 ; extra == 'dev-tensorflow'
Requires-Dist: faiss-cpu ; extra == 'dev-tensorflow'
Requires-Dist: isort >=5.5.4 ; extra == 'dev-tensorflow'
Requires-Dist: kenlm ; extra == 'dev-tensorflow'
Requires-Dist: keras-nlp <0.14.0,>=0.3.1 ; extra == 'dev-tensorflow'
Requires-Dist: librosa ; extra == 'dev-tensorflow'
Requires-Dist: nltk ; extra == 'dev-tensorflow'
Requires-Dist: onnxconverter-common ; extra == 'dev-tensorflow'
Requires-Dist: onnxruntime-tools >=1.4.2 ; extra == 'dev-tensorflow'
Requires-Dist: onnxruntime >=1.4.0 ; extra == 'dev-tensorflow'
Requires-Dist: parameterized ; extra == 'dev-tensorflow'
Requires-Dist: phonemizer ; extra == 'dev-tensorflow'
Requires-Dist: protobuf ; extra == 'dev-tensorflow'
Requires-Dist: psutil ; extra == 'dev-tensorflow'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'dev-tensorflow'
Requires-Dist: pydantic ; extra == 'dev-tensorflow'
Requires-Dist: pytest-rich ; extra == 'dev-tensorflow'
Requires-Dist: pytest-timeout ; extra == 'dev-tensorflow'
Requires-Dist: pytest-xdist ; extra == 'dev-tensorflow'
Requires-Dist: pytest <8.0.0,>=7.2.0 ; extra == 'dev-tensorflow'
Requires-Dist: rjieba ; extra == 'dev-tensorflow'
Requires-Dist: rouge-score !=0.0.7,!=0.0.8,!=0.1,!=0.1.1 ; extra == 'dev-tensorflow'
Requires-Dist: ruff ==0.4.4 ; extra == 'dev-tensorflow'
Requires-Dist: sacrebleu <2.0.0,>=1.4.12 ; extra == 'dev-tensorflow'
Requires-Dist: sacremoses ; extra == 'dev-tensorflow'
Requires-Dist: scikit-learn ; extra == 'dev-tensorflow'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'dev-tensorflow'
Requires-Dist: tensorboard ; extra == 'dev-tensorflow'
Requires-Dist: tensorflow-text <2.16 ; extra == 'dev-tensorflow'
Requires-Dist: tensorflow <2.16,>2.9 ; extra == 'dev-tensorflow'
Requires-Dist: tf2onnx ; extra == 'dev-tensorflow'
Requires-Dist: timeout-decorator ; extra == 'dev-tensorflow'
Requires-Dist: tokenizers <0.20,>=0.19 ; extra == 'dev-tensorflow'
Requires-Dist: urllib3 <2.0.0 ; extra == 'dev-tensorflow'
Provides-Extra: dev-torch
Requires-Dist: GitPython <3.1.19 ; extra == 'dev-torch'
Requires-Dist: Pillow <=15.0,>=10.0.1 ; extra == 'dev-torch'
Requires-Dist: accelerate >=0.21.0 ; extra == 'dev-torch'
Requires-Dist: beautifulsoup4 ; extra == 'dev-torch'
Requires-Dist: codecarbon ==1.2.0 ; extra == 'dev-torch'
Requires-Dist: cookiecutter ==1.7.3 ; extra == 'dev-torch'
Requires-Dist: datasets !=2.5.0 ; extra == 'dev-torch'
Requires-Dist: dill <0.3.5 ; extra == 'dev-torch'
Requires-Dist: evaluate >=0.2.0 ; extra == 'dev-torch'
Requires-Dist: faiss-cpu ; extra == 'dev-torch'
Requires-Dist: fugashi >=1.0 ; extra == 'dev-torch'
Requires-Dist: ipadic <2.0,>=1.0.0 ; extra == 'dev-torch'
Requires-Dist: isort >=5.5.4 ; extra == 'dev-torch'
Requires-Dist: kenlm ; extra == 'dev-torch'
Requires-Dist: librosa ; extra == 'dev-torch'
Requires-Dist: nltk ; extra == 'dev-torch'
Requires-Dist: onnxruntime-tools >=1.4.2 ; extra == 'dev-torch'
Requires-Dist: onnxruntime >=1.4.0 ; extra == 'dev-torch'
Requires-Dist: optuna ; extra == 'dev-torch'
Requires-Dist: parameterized ; extra == 'dev-torch'
Requires-Dist: phonemizer ; extra == 'dev-torch'
Requires-Dist: protobuf ; extra == 'dev-torch'
Requires-Dist: psutil ; extra == 'dev-torch'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'dev-torch'
Requires-Dist: pydantic ; extra == 'dev-torch'
Requires-Dist: pytest-rich ; extra == 'dev-torch'
Requires-Dist: pytest-timeout ; extra == 'dev-torch'
Requires-Dist: pytest-xdist ; extra == 'dev-torch'
Requires-Dist: pytest <8.0.0,>=7.2.0 ; extra == 'dev-torch'
Requires-Dist: ray[tune] >=2.7.0 ; extra == 'dev-torch'
Requires-Dist: rhoknp <1.3.1,>=1.1.0 ; extra == 'dev-torch'
Requires-Dist: rjieba ; extra == 'dev-torch'
Requires-Dist: rouge-score !=0.0.7,!=0.0.8,!=0.1,!=0.1.1 ; extra == 'dev-torch'
Requires-Dist: ruff ==0.4.4 ; extra == 'dev-torch'
Requires-Dist: sacrebleu <2.0.0,>=1.4.12 ; extra == 'dev-torch'
Requires-Dist: sacremoses ; extra == 'dev-torch'
Requires-Dist: scikit-learn ; extra == 'dev-torch'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'dev-torch'
Requires-Dist: sigopt ; extra == 'dev-torch'
Requires-Dist: sudachidict-core >=20220729 ; extra == 'dev-torch'
Requires-Dist: sudachipy >=0.6.6 ; extra == 'dev-torch'
Requires-Dist: tensorboard ; extra == 'dev-torch'
Requires-Dist: timeout-decorator ; extra == 'dev-torch'
Requires-Dist: timm <=0.9.16 ; extra == 'dev-torch'
Requires-Dist: tokenizers <0.20,>=0.19 ; extra == 'dev-torch'
Requires-Dist: torch ; extra == 'dev-torch'
Requires-Dist: torchaudio ; extra == 'dev-torch'
Requires-Dist: torchvision ; extra == 'dev-torch'
Requires-Dist: unidic >=1.0.2 ; extra == 'dev-torch'
Requires-Dist: unidic-lite >=1.0.7 ; extra == 'dev-torch'
Requires-Dist: urllib3 <2.0.0 ; extra == 'dev-torch'
Provides-Extra: flax
Requires-Dist: flax <=0.7.0,>=0.4.1 ; extra == 'flax'
Requires-Dist: jax <=0.4.13,>=0.4.1 ; extra == 'flax'
Requires-Dist: jaxlib <=0.4.13,>=0.4.1 ; extra == 'flax'
Requires-Dist: optax <=0.1.4,>=0.0.8 ; extra == 'flax'
Requires-Dist: scipy <1.13.0 ; extra == 'flax'
Provides-Extra: flax-speech
Requires-Dist: kenlm ; extra == 'flax-speech'
Requires-Dist: librosa ; extra == 'flax-speech'
Requires-Dist: phonemizer ; extra == 'flax-speech'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'flax-speech'
Provides-Extra: ftfy
Requires-Dist: ftfy ; extra == 'ftfy'
Provides-Extra: integrations
Requires-Dist: optuna ; extra == 'integrations'
Requires-Dist: ray[tune] >=2.7.0 ; extra == 'integrations'
Requires-Dist: sigopt ; extra == 'integrations'
Provides-Extra: ja
Requires-Dist: fugashi >=1.0 ; extra == 'ja'
Requires-Dist: ipadic <2.0,>=1.0.0 ; extra == 'ja'
Requires-Dist: rhoknp <1.3.1,>=1.1.0 ; extra == 'ja'
Requires-Dist: sudachidict-core >=20220729 ; extra == 'ja'
Requires-Dist: sudachipy >=0.6.6 ; extra == 'ja'
Requires-Dist: unidic >=1.0.2 ; extra == 'ja'
Requires-Dist: unidic-lite >=1.0.7 ; extra == 'ja'
Provides-Extra: modelcreation
Requires-Dist: cookiecutter ==1.7.3 ; extra == 'modelcreation'
Provides-Extra: natten
Requires-Dist: natten <0.15.0,>=0.14.6 ; extra == 'natten'
Provides-Extra: onnx
Requires-Dist: onnxconverter-common ; extra == 'onnx'
Requires-Dist: onnxruntime-tools >=1.4.2 ; extra == 'onnx'
Requires-Dist: onnxruntime >=1.4.0 ; extra == 'onnx'
Requires-Dist: tf2onnx ; extra == 'onnx'
Provides-Extra: onnxruntime
Requires-Dist: onnxruntime-tools >=1.4.2 ; extra == 'onnxruntime'
Requires-Dist: onnxruntime >=1.4.0 ; extra == 'onnxruntime'
Provides-Extra: optuna
Requires-Dist: optuna ; extra == 'optuna'
Provides-Extra: quality
Requires-Dist: GitPython <3.1.19 ; extra == 'quality'
Requires-Dist: datasets !=2.5.0 ; extra == 'quality'
Requires-Dist: isort >=5.5.4 ; extra == 'quality'
Requires-Dist: ruff ==0.4.4 ; extra == 'quality'
Requires-Dist: urllib3 <2.0.0 ; extra == 'quality'
Provides-Extra: ray
Requires-Dist: ray[tune] >=2.7.0 ; extra == 'ray'
Provides-Extra: retrieval
Requires-Dist: datasets !=2.5.0 ; extra == 'retrieval'
Requires-Dist: faiss-cpu ; extra == 'retrieval'
Provides-Extra: ruff
Requires-Dist: ruff ==0.4.4 ; extra == 'ruff'
Provides-Extra: sagemaker
Requires-Dist: sagemaker >=2.31.0 ; extra == 'sagemaker'
Provides-Extra: sentencepiece
Requires-Dist: protobuf ; extra == 'sentencepiece'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'sentencepiece'
Provides-Extra: serving
Requires-Dist: fastapi ; extra == 'serving'
Requires-Dist: pydantic ; extra == 'serving'
Requires-Dist: starlette ; extra == 'serving'
Requires-Dist: uvicorn ; extra == 'serving'
Provides-Extra: sigopt
Requires-Dist: sigopt ; extra == 'sigopt'
Provides-Extra: sklearn
Requires-Dist: scikit-learn ; extra == 'sklearn'
Provides-Extra: speech
Requires-Dist: kenlm ; extra == 'speech'
Requires-Dist: librosa ; extra == 'speech'
Requires-Dist: phonemizer ; extra == 'speech'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'speech'
Requires-Dist: torchaudio ; extra == 'speech'
Provides-Extra: testing
Requires-Dist: GitPython <3.1.19 ; extra == 'testing'
Requires-Dist: beautifulsoup4 ; extra == 'testing'
Requires-Dist: cookiecutter ==1.7.3 ; extra == 'testing'
Requires-Dist: datasets !=2.5.0 ; extra == 'testing'
Requires-Dist: dill <0.3.5 ; extra == 'testing'
Requires-Dist: evaluate >=0.2.0 ; extra == 'testing'
Requires-Dist: faiss-cpu ; extra == 'testing'
Requires-Dist: nltk ; extra == 'testing'
Requires-Dist: parameterized ; extra == 'testing'
Requires-Dist: psutil ; extra == 'testing'
Requires-Dist: pydantic ; extra == 'testing'
Requires-Dist: pytest-rich ; extra == 'testing'
Requires-Dist: pytest-timeout ; extra == 'testing'
Requires-Dist: pytest-xdist ; extra == 'testing'
Requires-Dist: pytest <8.0.0,>=7.2.0 ; extra == 'testing'
Requires-Dist: rjieba ; extra == 'testing'
Requires-Dist: rouge-score !=0.0.7,!=0.0.8,!=0.1,!=0.1.1 ; extra == 'testing'
Requires-Dist: ruff ==0.4.4 ; extra == 'testing'
Requires-Dist: sacrebleu <2.0.0,>=1.4.12 ; extra == 'testing'
Requires-Dist: sacremoses ; extra == 'testing'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'testing'
Requires-Dist: tensorboard ; extra == 'testing'
Requires-Dist: timeout-decorator ; extra == 'testing'
Provides-Extra: tf
Requires-Dist: keras-nlp <0.14.0,>=0.3.1 ; extra == 'tf'
Requires-Dist: onnxconverter-common ; extra == 'tf'
Requires-Dist: tensorflow-text <2.16 ; extra == 'tf'
Requires-Dist: tensorflow <2.16,>2.9 ; extra == 'tf'
Requires-Dist: tf2onnx ; extra == 'tf'
Provides-Extra: tf-cpu
Requires-Dist: keras-nlp <0.14.0,>=0.3.1 ; extra == 'tf-cpu'
Requires-Dist: keras <2.16,>2.9 ; extra == 'tf-cpu'
Requires-Dist: onnxconverter-common ; extra == 'tf-cpu'
Requires-Dist: tensorflow-cpu <2.16,>2.9 ; extra == 'tf-cpu'
Requires-Dist: tensorflow-probability <0.24 ; extra == 'tf-cpu'
Requires-Dist: tensorflow-text <2.16 ; extra == 'tf-cpu'
Requires-Dist: tf2onnx ; extra == 'tf-cpu'
Provides-Extra: tf-speech
Requires-Dist: kenlm ; extra == 'tf-speech'
Requires-Dist: librosa ; extra == 'tf-speech'
Requires-Dist: phonemizer ; extra == 'tf-speech'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'tf-speech'
Provides-Extra: timm
Requires-Dist: timm <=0.9.16 ; extra == 'timm'
Provides-Extra: tokenizers
Requires-Dist: tokenizers <0.20,>=0.19 ; extra == 'tokenizers'
Provides-Extra: torch
Requires-Dist: accelerate >=0.21.0 ; extra == 'torch'
Requires-Dist: torch ; extra == 'torch'
Provides-Extra: torch-speech
Requires-Dist: kenlm ; extra == 'torch-speech'
Requires-Dist: librosa ; extra == 'torch-speech'
Requires-Dist: phonemizer ; extra == 'torch-speech'
Requires-Dist: pyctcdecode >=0.4.0 ; extra == 'torch-speech'
Requires-Dist: torchaudio ; extra == 'torch-speech'
Provides-Extra: torch-vision
Requires-Dist: Pillow <=15.0,>=10.0.1 ; extra == 'torch-vision'
Requires-Dist: torchvision ; extra == 'torch-vision'
Provides-Extra: torchhub
Requires-Dist: filelock ; extra == 'torchhub'
Requires-Dist: huggingface-hub <1.0,>=0.23.2 ; extra == 'torchhub'
Requires-Dist: importlib-metadata ; extra == 'torchhub'
Requires-Dist: numpy >=1.17 ; extra == 'torchhub'
Requires-Dist: packaging >=20.0 ; extra == 'torchhub'
Requires-Dist: protobuf ; extra == 'torchhub'
Requires-Dist: regex !=2019.12.17 ; extra == 'torchhub'
Requires-Dist: requests ; extra == 'torchhub'
Requires-Dist: sentencepiece !=0.1.92,>=0.1.91 ; extra == 'torchhub'
Requires-Dist: tokenizers <0.20,>=0.19 ; extra == 'torchhub'
Requires-Dist: torch ; extra == 'torchhub'
Requires-Dist: tqdm >=4.27 ; extra == 'torchhub'
Provides-Extra: video
Requires-Dist: av ==9.2.0 ; extra == 'video'
Requires-Dist: decord ==0.6.0 ; extra == 'video'
Provides-Extra: vision
Requires-Dist: Pillow <=15.0,>=10.0.1 ; extra == 'vision'

<!---
Copyright 2020 The HuggingFace Team. All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->

<p align="center">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://huggingface.co/datasets/huggingface/documentation-images/raw/main/transformers-logo-dark.svg">
    <source media="(prefers-color-scheme: light)" srcset="https://huggingface.co/datasets/huggingface/documentation-images/raw/main/transformers-logo-light.svg">
    <img alt="Hugging Face Transformers Library" src="https://huggingface.co/datasets/huggingface/documentation-images/raw/main/transformers-logo-light.svg" width="352" height="59" style="max-width: 100%;">
  </picture>
  <br/>
  <br/>
</p>

<p align="center">
    <a href="https://circleci.com/gh/huggingface/transformers"><img alt="Build" src="https://img.shields.io/circleci/build/github/huggingface/transformers/main"></a>
    <a href="https://github.com/huggingface/transformers/blob/main/LICENSE"><img alt="GitHub" src="https://img.shields.io/github/license/huggingface/transformers.svg?color=blue"></a>
    <a href="https://huggingface.co/docs/transformers/index"><img alt="Documentation" src="https://img.shields.io/website/http/huggingface.co/docs/transformers/index.svg?down_color=red&down_message=offline&up_message=online"></a>
    <a href="https://github.com/huggingface/transformers/releases"><img alt="GitHub release" src="https://img.shields.io/github/release/huggingface/transformers.svg"></a>
    <a href="https://github.com/huggingface/transformers/blob/main/CODE_OF_CONDUCT.md"><img alt="Contributor Covenant" src="https://img.shields.io/badge/Contributor%20Covenant-v2.0%20adopted-ff69b4.svg"></a>
    <a href="https://zenodo.org/badge/latestdoi/155220641"><img src="https://zenodo.org/badge/155220641.svg" alt="DOI"></a>
</p>

<h4 align="center">
    <p>
        <b>English</b> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_zh-hans.md">简体中文</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_zh-hant.md">繁體中文</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_ko.md">한국어</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_es.md">Español</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_ja.md">日本語</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_hd.md">हिन्दी</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_ru.md">Русский</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_pt-br.md">Рortuguês</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_te.md">తెలుగు</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_fr.md">Français</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_de.md">Deutsch</a> |
        <a href="https://github.com/huggingface/transformers/blob/main/i18n/README_vi.md">Tiếng Việt</a> |
    </p>
</h4>

<h3 align="center">
    <p>State-of-the-art Machine Learning for JAX, PyTorch and TensorFlow</p>
</h3>

<h3 align="center">
    <a href="https://hf.co/course"><img src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/course_banner.png"></a>
</h3>

🤗 Transformers provides thousands of pretrained models to perform tasks on different modalities such as text, vision, and audio.

These models can be applied on:

* 📝 Text, for tasks like text classification, information extraction, question answering, summarization, translation, and text generation, in over 100 languages.
* 🖼️ Images, for tasks like image classification, object detection, and segmentation.
* 🗣️ Audio, for tasks like speech recognition and audio classification.

Transformer models can also perform tasks on **several modalities combined**, such as table question answering, optical character recognition, information extraction from scanned documents, video classification, and visual question answering.

🤗 Transformers provides APIs to quickly download and use those pretrained models on a given text, fine-tune them on your own datasets and then share them with the community on our [model hub](https://huggingface.co/models). At the same time, each python module defining an architecture is fully standalone and can be modified to enable quick research experiments.

🤗 Transformers is backed by the three most popular deep learning libraries — [Jax](https://jax.readthedocs.io/en/latest/), [PyTorch](https://pytorch.org/) and [TensorFlow](https://www.tensorflow.org/) — with a seamless integration between them. It's straightforward to train your models with one before loading them for inference with the other.

## Online demos

You can test most of our models directly on their pages from the [model hub](https://huggingface.co/models). We also offer [private model hosting, versioning, & an inference API](https://huggingface.co/pricing) for public and private models.

Here are a few examples:

In Natural Language Processing:
- [Masked word completion with BERT](https://huggingface.co/google-bert/bert-base-uncased?text=Paris+is+the+%5BMASK%5D+of+France)
- [Named Entity Recognition with Electra](https://huggingface.co/dbmdz/electra-large-discriminator-finetuned-conll03-english?text=My+name+is+Sarah+and+I+live+in+London+city)
- [Text generation with Mistral](https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.2)
- [Natural Language Inference with RoBERTa](https://huggingface.co/FacebookAI/roberta-large-mnli?text=The+dog+was+lost.+Nobody+lost+any+animal)
- [Summarization with BART](https://huggingface.co/facebook/bart-large-cnn?text=The+tower+is+324+metres+%281%2C063+ft%29+tall%2C+about+the+same+height+as+an+81-storey+building%2C+and+the+tallest+structure+in+Paris.+Its+base+is+square%2C+measuring+125+metres+%28410+ft%29+on+each+side.+During+its+construction%2C+the+Eiffel+Tower+surpassed+the+Washington+Monument+to+become+the+tallest+man-made+structure+in+the+world%2C+a+title+it+held+for+41+years+until+the+Chrysler+Building+in+New+York+City+was+finished+in+1930.+It+was+the+first+structure+to+reach+a+height+of+300+metres.+Due+to+the+addition+of+a+broadcasting+aerial+at+the+top+of+the+tower+in+1957%2C+it+is+now+taller+than+the+Chrysler+Building+by+5.2+metres+%2817+ft%29.+Excluding+transmitters%2C+the+Eiffel+Tower+is+the+second+tallest+free-standing+structure+in+France+after+the+Millau+Viaduct)
- [Question answering with DistilBERT](https://huggingface.co/distilbert/distilbert-base-uncased-distilled-squad?text=Which+name+is+also+used+to+describe+the+Amazon+rainforest+in+English%3F&context=The+Amazon+rainforest+%28Portuguese%3A+Floresta+Amaz%C3%B4nica+or+Amaz%C3%B4nia%3B+Spanish%3A+Selva+Amaz%C3%B3nica%2C+Amazon%C3%ADa+or+usually+Amazonia%3B+French%3A+For%C3%AAt+amazonienne%3B+Dutch%3A+Amazoneregenwoud%29%2C+also+known+in+English+as+Amazonia+or+the+Amazon+Jungle%2C+is+a+moist+broadleaf+forest+that+covers+most+of+the+Amazon+basin+of+South+America.+This+basin+encompasses+7%2C000%2C000+square+kilometres+%282%2C700%2C000+sq+mi%29%2C+of+which+5%2C500%2C000+square+kilometres+%282%2C100%2C000+sq+mi%29+are+covered+by+the+rainforest.+This+region+includes+territory+belonging+to+nine+nations.+The+majority+of+the+forest+is+contained+within+Brazil%2C+with+60%25+of+the+rainforest%2C+followed+by+Peru+with+13%25%2C+Colombia+with+10%25%2C+and+with+minor+amounts+in+Venezuela%2C+Ecuador%2C+Bolivia%2C+Guyana%2C+Suriname+and+French+Guiana.+States+or+departments+in+four+nations+contain+%22Amazonas%22+in+their+names.+The+Amazon+represents+over+half+of+the+planet%27s+remaining+rainforests%2C+and+comprises+the+largest+and+most+biodiverse+tract+of+tropical+rainforest+in+the+world%2C+with+an+estimated+390+billion+individual+trees+divided+into+16%2C000+species)
- [Translation with T5](https://huggingface.co/google-t5/t5-base?text=My+name+is+Wolfgang+and+I+live+in+Berlin)

In Computer Vision:
- [Image classification with ViT](https://huggingface.co/google/vit-base-patch16-224)
- [Object Detection with DETR](https://huggingface.co/facebook/detr-resnet-50)
- [Semantic Segmentation with SegFormer](https://huggingface.co/nvidia/segformer-b0-finetuned-ade-512-512)
- [Panoptic Segmentation with Mask2Former](https://huggingface.co/facebook/mask2former-swin-large-coco-panoptic)
- [Depth Estimation with Depth Anything](https://huggingface.co/docs/transformers/main/model_doc/depth_anything)
- [Video Classification with VideoMAE](https://huggingface.co/docs/transformers/model_doc/videomae)
- [Universal Segmentation with OneFormer](https://huggingface.co/shi-labs/oneformer_ade20k_dinat_large)

In Audio:
- [Automatic Speech Recognition with Whisper](https://huggingface.co/openai/whisper-large-v3)
- [Keyword Spotting with Wav2Vec2](https://huggingface.co/superb/wav2vec2-base-superb-ks)
- [Audio Classification with Audio Spectrogram Transformer](https://huggingface.co/MIT/ast-finetuned-audioset-10-10-0.4593)

In Multimodal tasks:
- [Table Question Answering with TAPAS](https://huggingface.co/google/tapas-base-finetuned-wtq)
- [Visual Question Answering with ViLT](https://huggingface.co/dandelin/vilt-b32-finetuned-vqa)
- [Image captioning with LLaVa](https://huggingface.co/llava-hf/llava-1.5-7b-hf)
- [Zero-shot Image Classification with SigLIP](https://huggingface.co/google/siglip-so400m-patch14-384)
- [Document Question Answering with LayoutLM](https://huggingface.co/impira/layoutlm-document-qa)
- [Zero-shot Video Classification with X-CLIP](https://huggingface.co/docs/transformers/model_doc/xclip)
- [Zero-shot Object Detection with OWLv2](https://huggingface.co/docs/transformers/en/model_doc/owlv2)
- [Zero-shot Image Segmentation with CLIPSeg](https://huggingface.co/docs/transformers/model_doc/clipseg)
- [Automatic Mask Generation with SAM](https://huggingface.co/docs/transformers/model_doc/sam)


## 100 projects using Transformers

Transformers is more than a toolkit to use pretrained models: it's a community of projects built around it and the
Hugging Face Hub. We want Transformers to enable developers, researchers, students, professors, engineers, and anyone
else to build their dream projects.

In order to celebrate the 100,000 stars of transformers, we have decided to put the spotlight on the
community, and we have created the [awesome-transformers](./awesome-transformers.md) page which lists 100
incredible projects built in the vicinity of transformers.

If you own or use a project that you believe should be part of the list, please open a PR to add it!

## If you are looking for custom support from the Hugging Face team

<a target="_blank" href="https://huggingface.co/support">
    <img alt="HuggingFace Expert Acceleration Program" src="https://cdn-media.huggingface.co/marketing/transformers/new-support-improved.png" style="max-width: 600px; border: 1px solid #eee; border-radius: 4px; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);">
</a><br>

## Quick tour

To immediately use a model on a given input (text, image, audio, ...), we provide the `pipeline` API. Pipelines group together a pretrained model with the preprocessing that was used during that model's training. Here is how to quickly use a pipeline to classify positive versus negative texts:

```python
>>> from transformers import pipeline

# Allocate a pipeline for sentiment-analysis
>>> classifier = pipeline('sentiment-analysis')
>>> classifier('We are very happy to introduce pipeline to the transformers repository.')
[{'label': 'POSITIVE', 'score': 0.9996980428695679}]
```

The second line of code downloads and caches the pretrained model used by the pipeline, while the third evaluates it on the given text. Here, the answer is "positive" with a confidence of 99.97%.

Many tasks have a pre-trained `pipeline` ready to go, in NLP but also in computer vision and speech. For example, we can easily extract detected objects in an image:

``` python
>>> import requests
>>> from PIL import Image
>>> from transformers import pipeline

# Download an image with cute cats
>>> url = "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/coco_sample.png"
>>> image_data = requests.get(url, stream=True).raw
>>> image = Image.open(image_data)

# Allocate a pipeline for object detection
>>> object_detector = pipeline('object-detection')
>>> object_detector(image)
[{'score': 0.9982201457023621,
  'label': 'remote',
  'box': {'xmin': 40, 'ymin': 70, 'xmax': 175, 'ymax': 117}},
 {'score': 0.9960021376609802,
  'label': 'remote',
  'box': {'xmin': 333, 'ymin': 72, 'xmax': 368, 'ymax': 187}},
 {'score': 0.9954745173454285,
  'label': 'couch',
  'box': {'xmin': 0, 'ymin': 1, 'xmax': 639, 'ymax': 473}},
 {'score': 0.9988006353378296,
  'label': 'cat',
  'box': {'xmin': 13, 'ymin': 52, 'xmax': 314, 'ymax': 470}},
 {'score': 0.9986783862113953,
  'label': 'cat',
  'box': {'xmin': 345, 'ymin': 23, 'xmax': 640, 'ymax': 368}}]
```

Here, we get a list of objects detected in the image, with a box surrounding the object and a confidence score. Here is the original image on the left, with the predictions displayed on the right:

<h3 align="center">
    <a><img src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/coco_sample.png" width="400"></a>
    <a><img src="https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/coco_sample_post_processed.png" width="400"></a>
</h3>

You can learn more about the tasks supported by the `pipeline` API in [this tutorial](https://huggingface.co/docs/transformers/task_summary).

In addition to `pipeline`, to download and use any of the pretrained models on your given task, all it takes is three lines of code. Here is the PyTorch version:
```python
>>> from transformers import AutoTokenizer, AutoModel

>>> tokenizer = AutoTokenizer.from_pretrained("google-bert/bert-base-uncased")
>>> model = AutoModel.from_pretrained("google-bert/bert-base-uncased")

>>> inputs = tokenizer("Hello world!", return_tensors="pt")
>>> outputs = model(**inputs)
```

And here is the equivalent code for TensorFlow:
```python
>>> from transformers import AutoTokenizer, TFAutoModel

>>> tokenizer = AutoTokenizer.from_pretrained("google-bert/bert-base-uncased")
>>> model = TFAutoModel.from_pretrained("google-bert/bert-base-uncased")

>>> inputs = tokenizer("Hello world!", return_tensors="tf")
>>> outputs = model(**inputs)
```

The tokenizer is responsible for all the preprocessing the pretrained model expects and can be called directly on a single string (as in the above examples) or a list. It will output a dictionary that you can use in downstream code or simply directly pass to your model using the ** argument unpacking operator.

The model itself is a regular [Pytorch `nn.Module`](https://pytorch.org/docs/stable/nn.html#torch.nn.Module) or a [TensorFlow `tf.keras.Model`](https://www.tensorflow.org/api_docs/python/tf/keras/Model) (depending on your backend) which you can use as usual. [This tutorial](https://huggingface.co/docs/transformers/training) explains how to integrate such a model into a classic PyTorch or TensorFlow training loop, or how to use our `Trainer` API to quickly fine-tune on a new dataset.

## Why should I use transformers?

1. Easy-to-use state-of-the-art models:
    - High performance on natural language understanding & generation, computer vision, and audio tasks.
    - Low barrier to entry for educators and practitioners.
    - Few user-facing abstractions with just three classes to learn.
    - A unified API for using all our pretrained models.

1. Lower compute costs, smaller carbon footprint:
    - Researchers can share trained models instead of always retraining.
    - Practitioners can reduce compute time and production costs.
    - Dozens of architectures with over 400,000 pretrained models across all modalities.

1. Choose the right framework for every part of a model's lifetime:
    - Train state-of-the-art models in 3 lines of code.
    - Move a single model between TF2.0/PyTorch/JAX frameworks at will.
    - Seamlessly pick the right framework for training, evaluation, and production.

1. Easily customize a model or an example to your needs:
    - We provide examples for each architecture to reproduce the results published by its original authors.
    - Model internals are exposed as consistently as possible.
    - Model files can be used independently of the library for quick experiments.

## Why shouldn't I use transformers?

- This library is not a modular toolbox of building blocks for neural nets. The code in the model files is not refactored with additional abstractions on purpose, so that researchers can quickly iterate on each of the models without diving into additional abstractions/files.
- The training API is not intended to work on any model but is optimized to work with the models provided by the library. For generic machine learning loops, you should use another library (possibly, [Accelerate](https://huggingface.co/docs/accelerate)).
- While we strive to present as many use cases as possible, the scripts in our [examples folder](https://github.com/huggingface/transformers/tree/main/examples) are just that: examples. It is expected that they won't work out-of-the-box on your specific problem and that you will be required to change a few lines of code to adapt them to your needs.

## Installation

### With pip

This repository is tested on Python 3.8+, Flax 0.4.1+, PyTorch 1.11+, and TensorFlow 2.6+.

You should install 🤗 Transformers in a [virtual environment](https://docs.python.org/3/library/venv.html). If you're unfamiliar with Python virtual environments, check out the [user guide](https://packaging.python.org/guides/installing-using-pip-and-virtual-environments/).

First, create a virtual environment with the version of Python you're going to use and activate it.

Then, you will need to install at least one of Flax, PyTorch, or TensorFlow.
Please refer to [TensorFlow installation page](https://www.tensorflow.org/install/), [PyTorch installation page](https://pytorch.org/get-started/locally/#start-locally) and/or [Flax](https://github.com/google/flax#quick-install) and [Jax](https://github.com/google/jax#installation) installation pages regarding the specific installation command for your platform.

When one of those backends has been installed, 🤗 Transformers can be installed using pip as follows:

```bash
pip install transformers
```

If you'd like to play with the examples or need the bleeding edge of the code and can't wait for a new release, you must [install the library from source](https://huggingface.co/docs/transformers/installation#installing-from-source).

### With conda

🤗 Transformers can be installed using conda as follows:

```shell script
conda install conda-forge::transformers
```

> **_NOTE:_** Installing `transformers` from the `huggingface` channel is deprecated.

Follow the installation pages of Flax, PyTorch or TensorFlow to see how to install them with conda.

> **_NOTE:_**  On Windows, you may be prompted to activate Developer Mode in order to benefit from caching. If this is not an option for you, please let us know in [this issue](https://github.com/huggingface/huggingface_hub/issues/1062).

## Model architectures

**[All the model checkpoints](https://huggingface.co/models)** provided by 🤗 Transformers are seamlessly integrated from the huggingface.co [model hub](https://huggingface.co/models), where they are uploaded directly by [users](https://huggingface.co/users) and [organizations](https://huggingface.co/organizations).

Current number of checkpoints: ![](https://img.shields.io/endpoint?url=https://huggingface.co/api/shields/models&color=brightgreen)

🤗 Transformers currently provides the following architectures: see [here](https://huggingface.co/docs/transformers/model_summary) for a high-level summary of each them.

To check if each model has an implementation in Flax, PyTorch or TensorFlow, or has an associated tokenizer backed by the 🤗 Tokenizers library, refer to [this table](https://huggingface.co/docs/transformers/index#supported-frameworks).

These implementations have been tested on several datasets (see the example scripts) and should match the performance of the original implementations. You can find more details on performance in the Examples section of the [documentation](https://github.com/huggingface/transformers/tree/main/examples).


## Learn more

| Section | Description |
|-|-|
| [Documentation](https://huggingface.co/docs/transformers/) | Full API documentation and tutorials |
| [Task summary](https://huggingface.co/docs/transformers/task_summary) | Tasks supported by 🤗 Transformers |
| [Preprocessing tutorial](https://huggingface.co/docs/transformers/preprocessing) | Using the `Tokenizer` class to prepare data for the models |
| [Training and fine-tuning](https://huggingface.co/docs/transformers/training) | Using the models provided by 🤗 Transformers in a PyTorch/TensorFlow training loop and the `Trainer` API |
| [Quick tour: Fine-tuning/usage scripts](https://github.com/huggingface/transformers/tree/main/examples) | Example scripts for fine-tuning models on a wide range of tasks |
| [Model sharing and uploading](https://huggingface.co/docs/transformers/model_sharing) | Upload and share your fine-tuned models with the community |

## Citation

We now have a [paper](https://www.aclweb.org/anthology/2020.emnlp-demos.6/) you can cite for the 🤗 Transformers library:
```bibtex
@inproceedings{wolf-etal-2020-transformers,
    title = "Transformers: State-of-the-Art Natural Language Processing",
    author = "Thomas Wolf and Lysandre Debut and Victor Sanh and Julien Chaumond and Clement Delangue and Anthony Moi and Pierric Cistac and Tim Rault and Rémi Louf and Morgan Funtowicz and Joe Davison and Sam Shleifer and Patrick von Platen and Clara Ma and Yacine Jernite and Julien Plu and Canwen Xu and Teven Le Scao and Sylvain Gugger and Mariama Drame and Quentin Lhoest and Alexander M. Rush",
    booktitle = "Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing: System Demonstrations",
    month = oct,
    year = "2020",
    address = "Online",
    publisher = "Association for Computational Linguistics",
    url = "https://www.aclweb.org/anthology/2020.emnlp-demos.6",
    pages = "38--45"
}
```


