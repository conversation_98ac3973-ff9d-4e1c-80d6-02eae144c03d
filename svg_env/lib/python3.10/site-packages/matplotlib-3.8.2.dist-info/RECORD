__pycache__/pylab.cpython-310.pyc,,
matplotlib-3.8.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
matplotlib-3.8.2.dist-info/LICENSE,sha256=WhqB6jAXKMi7opM9qDLAzWIina8giToCSrPVMkRGjbw,4830
matplotlib-3.8.2.dist-info/LICENSE_AMSFONTS,sha256=FVFB1Zh38zj24cCAXem3mWTc5x_l0qVsROOLLA9-Ne4,12675
matplotlib-3.8.2.dist-info/LICENSE_BAKOMA,sha256=k62ytTwCt84Gsmv_QTWMISSPQv7Sh7Yz7EwxA4N3zbs,1440
matplotlib-3.8.2.dist-info/LICENSE_CARLOGO,sha256=YZAtXu803SSHC3KHqWJg0zKCM7lvcgK_cK1uKg2i3j8,4455
matplotlib-3.8.2.dist-info/LICENSE_COLORBREWER,sha256=ARc2U_50XOISTsFW14tGBYLSpxnS2SEoBCZ-lf0sl-c,695
matplotlib-3.8.2.dist-info/LICENSE_COURIERTEN,sha256=rIH3F6r_lFa4gBD8VTgqjf8ZnkxLzF1h4EDD9wY760M,802
matplotlib-3.8.2.dist-info/LICENSE_JSXTOOLS_RESIZE_OBSERVER,sha256=WXdWrctR8kPvT7OGkgN39h0BKs4JBDZOGo7pquxq_IQ,6799
matplotlib-3.8.2.dist-info/LICENSE_QHULL,sha256=EG1VyTH9aoSCLlNF2QAnPQWfHCcxDQJWfMsxPF0YxV0,1720
matplotlib-3.8.2.dist-info/LICENSE_QT4_EDITOR,sha256=srUMqLYXKsojCVrfFduJ03J-nvLW7wF45CcjQBG-080,1230
matplotlib-3.8.2.dist-info/LICENSE_SOLARIZED,sha256=EtUyf7xN-EWoaIPeme1f30GYRF1W26zfX62PDv3JdRM,1121
matplotlib-3.8.2.dist-info/LICENSE_STIX,sha256=TMPvujo6YE62-TchHkbaHiFIgwBWpuCbzBnfQXDSUqQ,3914
matplotlib-3.8.2.dist-info/LICENSE_YORICK,sha256=yrdT04wJNlHo3rWrtoTj7WgCDg5BgDT5TXnokNx66E0,2313
matplotlib-3.8.2.dist-info/METADATA,sha256=xidio6o0-OgExQyT-sICrBcYxLtIzDiRETaWb5Hio6s,5760
matplotlib-3.8.2.dist-info/RECORD,,
matplotlib-3.8.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib-3.8.2.dist-info/WHEEL,sha256=V03y0Hly7-iwBL9fk1Vj_oE3j8SJsOdTO-Go5qWKIzU,152
matplotlib-3.8.2.dist-info/top_level.txt,sha256=9tEw2ni8DdgX8CceoYHqSH1s50vrJ9SDfgtLIG8e3Y4,30
matplotlib/__init__.py,sha256=NlrsFHM4j6fiYOWNP4Ug0buvxmDek9leE7lx7qZ8RcQ,53346
matplotlib/__init__.pyi,sha256=shbDzbbU57SOJAQ9yoracqXefDDKQ0AlVllY52EOHnQ,2899
matplotlib/__pycache__/__init__.cpython-310.pyc,,
matplotlib/__pycache__/_afm.cpython-310.pyc,,
matplotlib/__pycache__/_animation_data.cpython-310.pyc,,
matplotlib/__pycache__/_blocking_input.cpython-310.pyc,,
matplotlib/__pycache__/_cm.cpython-310.pyc,,
matplotlib/__pycache__/_cm_listed.cpython-310.pyc,,
matplotlib/__pycache__/_color_data.cpython-310.pyc,,
matplotlib/__pycache__/_constrained_layout.cpython-310.pyc,,
matplotlib/__pycache__/_docstring.cpython-310.pyc,,
matplotlib/__pycache__/_enums.cpython-310.pyc,,
matplotlib/__pycache__/_fontconfig_pattern.cpython-310.pyc,,
matplotlib/__pycache__/_internal_utils.cpython-310.pyc,,
matplotlib/__pycache__/_layoutgrid.cpython-310.pyc,,
matplotlib/__pycache__/_mathtext.cpython-310.pyc,,
matplotlib/__pycache__/_mathtext_data.cpython-310.pyc,,
matplotlib/__pycache__/_pylab_helpers.cpython-310.pyc,,
matplotlib/__pycache__/_text_helpers.cpython-310.pyc,,
matplotlib/__pycache__/_tight_bbox.cpython-310.pyc,,
matplotlib/__pycache__/_tight_layout.cpython-310.pyc,,
matplotlib/__pycache__/_type1font.cpython-310.pyc,,
matplotlib/__pycache__/_version.cpython-310.pyc,,
matplotlib/__pycache__/animation.cpython-310.pyc,,
matplotlib/__pycache__/artist.cpython-310.pyc,,
matplotlib/__pycache__/axis.cpython-310.pyc,,
matplotlib/__pycache__/backend_bases.cpython-310.pyc,,
matplotlib/__pycache__/backend_managers.cpython-310.pyc,,
matplotlib/__pycache__/backend_tools.cpython-310.pyc,,
matplotlib/__pycache__/bezier.cpython-310.pyc,,
matplotlib/__pycache__/category.cpython-310.pyc,,
matplotlib/__pycache__/cbook.cpython-310.pyc,,
matplotlib/__pycache__/cm.cpython-310.pyc,,
matplotlib/__pycache__/collections.cpython-310.pyc,,
matplotlib/__pycache__/colorbar.cpython-310.pyc,,
matplotlib/__pycache__/colors.cpython-310.pyc,,
matplotlib/__pycache__/container.cpython-310.pyc,,
matplotlib/__pycache__/contour.cpython-310.pyc,,
matplotlib/__pycache__/dates.cpython-310.pyc,,
matplotlib/__pycache__/dviread.cpython-310.pyc,,
matplotlib/__pycache__/figure.cpython-310.pyc,,
matplotlib/__pycache__/font_manager.cpython-310.pyc,,
matplotlib/__pycache__/gridspec.cpython-310.pyc,,
matplotlib/__pycache__/hatch.cpython-310.pyc,,
matplotlib/__pycache__/image.cpython-310.pyc,,
matplotlib/__pycache__/layout_engine.cpython-310.pyc,,
matplotlib/__pycache__/legend.cpython-310.pyc,,
matplotlib/__pycache__/legend_handler.cpython-310.pyc,,
matplotlib/__pycache__/lines.cpython-310.pyc,,
matplotlib/__pycache__/markers.cpython-310.pyc,,
matplotlib/__pycache__/mathtext.cpython-310.pyc,,
matplotlib/__pycache__/mlab.cpython-310.pyc,,
matplotlib/__pycache__/offsetbox.cpython-310.pyc,,
matplotlib/__pycache__/patches.cpython-310.pyc,,
matplotlib/__pycache__/path.cpython-310.pyc,,
matplotlib/__pycache__/patheffects.cpython-310.pyc,,
matplotlib/__pycache__/pylab.cpython-310.pyc,,
matplotlib/__pycache__/pyplot.cpython-310.pyc,,
matplotlib/__pycache__/quiver.cpython-310.pyc,,
matplotlib/__pycache__/rcsetup.cpython-310.pyc,,
matplotlib/__pycache__/sankey.cpython-310.pyc,,
matplotlib/__pycache__/scale.cpython-310.pyc,,
matplotlib/__pycache__/spines.cpython-310.pyc,,
matplotlib/__pycache__/stackplot.cpython-310.pyc,,
matplotlib/__pycache__/streamplot.cpython-310.pyc,,
matplotlib/__pycache__/table.cpython-310.pyc,,
matplotlib/__pycache__/texmanager.cpython-310.pyc,,
matplotlib/__pycache__/text.cpython-310.pyc,,
matplotlib/__pycache__/textpath.cpython-310.pyc,,
matplotlib/__pycache__/ticker.cpython-310.pyc,,
matplotlib/__pycache__/transforms.cpython-310.pyc,,
matplotlib/__pycache__/typing.cpython-310.pyc,,
matplotlib/__pycache__/units.cpython-310.pyc,,
matplotlib/__pycache__/widgets.cpython-310.pyc,,
matplotlib/_afm.py,sha256=cWe1Ib37T6ZyHbR6_hPuzAjotMmi32y-kDB-i28iyqE,16692
matplotlib/_animation_data.py,sha256=BBDhy8phg_Vxj-ZzB-tQVmX5QnfjHfemUB66KW9Zikw,7972
matplotlib/_api/__init__.py,sha256=FP8U3UtSLq0nadZEb8Aw8Kz_574EZIg0193AsVRP6JY,13290
matplotlib/_api/__init__.pyi,sha256=9xxTTkE9MIP4xFNwmRvM9j0QflFrqcFbowCAedGhdqo,2254
matplotlib/_api/__pycache__/__init__.cpython-310.pyc,,
matplotlib/_api/__pycache__/deprecation.cpython-310.pyc,,
matplotlib/_api/deprecation.py,sha256=mv0HDtr7v3S0G1OMF2sZNiXachgKd-U4yz8WWqlYN8U,19978
matplotlib/_api/deprecation.pyi,sha256=a9djyVvnX2-t-IGrk3z1mrnYHcj4qYxRD0n9KDHWXlE,2208
matplotlib/_blocking_input.py,sha256=VHNsxvX2mTx_xBknd30MSicVlRXS4dCDe9hDctbV5rk,1224
matplotlib/_c_internal_utils.cpython-310-x86_64-linux-gnu.so,sha256=0WyM5Hi-cv_FPuKXJeJlz1U9wA0Z07vGDw0XMXpZ4Q8,30400
matplotlib/_c_internal_utils.pyi,sha256=Uj2CI3zirmRnpiQ9yQNlqtzIfAx8qWV0oPpS-1lZhu8,36
matplotlib/_cm.py,sha256=GpKElUs0jR6to72ZfIs1NY8keE-6BTQgbyzvhON2dMY,66431
matplotlib/_cm_listed.py,sha256=hpgMx7bjxJx5nl1PbQvaCDUBHQf8njaRrM2iMaBeZOM,109462
matplotlib/_color_data.py,sha256=k-wdTi6ArJxksqBfMT-7Uy2qWz8XX4Th5gsjf32CwmM,34780
matplotlib/_color_data.pyi,sha256=RdBRk01yuf3jYVlCwG351tIBCxehizkZMnKs9c8gnOw,170
matplotlib/_constrained_layout.py,sha256=Yt3rZwu-U1oh8BRaY05Y3rRIdLOVHB339XL4Oab3IH0,31003
matplotlib/_docstring.py,sha256=Q4u0zJx5Agrf_Ot4kDQyji9pcVPq8FYPaU9r3_GSGlI,3069
matplotlib/_docstring.pyi,sha256=p56uCgq4BKhM7ypMMc5edhV3-AnzYIeZUw4T9GhJoUU,687
matplotlib/_enums.py,sha256=cq5dtb_qy4g3cHgr1KdVA9qzYalgz7KCtTytyFp3PAs,6474
matplotlib/_enums.pyi,sha256=B5MhNYWDyhVZteR5lo9uRJ8RiduSGKrYw1NkBTT8Mx4,364
matplotlib/_fontconfig_pattern.py,sha256=_DthzglWlbrFvCIpycF33ap7UDY1yhdZPiXNsICvZ74,4734
matplotlib/_image.cpython-310-x86_64-linux-gnu.so,sha256=gOfxSVh1lLd3w-aqlGkt_88yo7sffe3fS9qXy5v1DM0,2415672
matplotlib/_image.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/_internal_utils.py,sha256=nhK6LLWYW93fBcsFiO09JmqFj2rgHEsGYFOeaC7HRKw,2140
matplotlib/_layoutgrid.py,sha256=laNstF8ddikwhSjwLGNVjirsWeq0LhVSHVImWScAEi4,21676
matplotlib/_mathtext.py,sha256=Vmtol1VV9Souqr8Q7d8qOgv9BM9r8_YGv8Vbb6aOjEw,107430
matplotlib/_mathtext_data.py,sha256=RX5iq845Ne5nobFNM4-AhQc7Ua1UjfbXNefqJhh_vwg,51471
matplotlib/_path.cpython-310-x86_64-linux-gnu.so,sha256=vyj-2p3VdH74MAfJZXxojf3RxJQB9nFTmWC1eSv3_uc,1820712
matplotlib/_path.pyi,sha256=yznyfzoUogH9vvi0vK68ga4Shlbrn5UBhAnLX8Ght1o,325
matplotlib/_pylab_helpers.py,sha256=lIkvc-NFRMQIsXykuymE1Dcf5A-bMvu4BcnQjUL9vKg,4331
matplotlib/_pylab_helpers.pyi,sha256=7OZKr-OL3ipVt1EDZ6e-tRwHASz-ijYfcIdlPczXhvQ,1012
matplotlib/_qhull.cpython-310-x86_64-linux-gnu.so,sha256=-kydKjuCxAMmaphvTBh7eoa-kGvxJIZj1sF-TFqL-cg,2401824
matplotlib/_qhull.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/_text_helpers.py,sha256=jvCm4JYRs_YTecSO2gZJyWS9tXjy5BHTJHBh8UZux2Q,2496
matplotlib/_tight_bbox.py,sha256=ddJ5ViXulbPLocHr-RkWK27WJSuV2WXUx74jZyL0NOg,2787
matplotlib/_tight_layout.py,sha256=oWCfzymTLvdmydhrYH2--O5UFMywQkJdXwvEAU3HR7s,12675
matplotlib/_tri.cpython-310-x86_64-linux-gnu.so,sha256=Mmt0gMAEY6gyyJRd7Alc4FmHD9szflPK7srTLzSVv5Q,374600
matplotlib/_tri.pyi,sha256=UP0DudALEE02v92v0yIvlKNisqz2Opb6T0LKCIG0aeg,998
matplotlib/_ttconv.cpython-310-x86_64-linux-gnu.so,sha256=KRdSwuHUUWAicatBmTzuVLTUFiZlu_AzokPFrUGP34U,285112
matplotlib/_ttconv.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/_type1font.py,sha256=on8oxL6WDAlkXX6CMSgWyq3XWr6lkMsd1o20bOJwcAM,28308
matplotlib/_version.py,sha256=bV13UGjgKKBESjgpeQicXd0eKXwIF_qqxftysbl4rqs,411
matplotlib/animation.py,sha256=18R-POTYdxvVXKHOHtem9j7EMmh-BhOynDuHVDk5jRI,71861
matplotlib/animation.pyi,sha256=OicmH7T3wjKUgFdSlBI1FsGQXGXL4ijYRLLOd3y6jLg,6611
matplotlib/artist.py,sha256=2F2JvfVer6ibzKOXe2jv6KVCV69T_ZldNjd3TvHvZYA,63486
matplotlib/artist.pyi,sha256=cNZ0vUZYz6GjJ-CYe80M7koriHlCcvojmOn26qC6T6E,6870
matplotlib/axes/__init__.py,sha256=tlVOhR-Y-bkcTI3jm8jbXJXFLqc5yQt8n_0a-wXZa5M,368
matplotlib/axes/__init__.pyi,sha256=HP1z2v-PboHQS4dQjvJ7XjUjX-zw6taZRTTB9oVKwYE,303
matplotlib/axes/__pycache__/__init__.cpython-310.pyc,,
matplotlib/axes/__pycache__/_axes.cpython-310.pyc,,
matplotlib/axes/__pycache__/_base.cpython-310.pyc,,
matplotlib/axes/__pycache__/_secondary_axes.cpython-310.pyc,,
matplotlib/axes/_axes.py,sha256=j0RsWfaLD116PwZYCTrDjU5WBq95SUMv_4eamciKeH4,332903
matplotlib/axes/_axes.pyi,sha256=6KsCV0pQ0r4SxDjEQqn780uE8j_UEUwcTpE5Oodg1EA,25317
matplotlib/axes/_base.py,sha256=BHPsLJp2qYFhhWAto5VPWWrfu8kfmpM-lwXSL9MrSso,178148
matplotlib/axes/_base.pyi,sha256=BVoWMC0rr0TZgnrdYKrZVUyH1g9UjJvOdxK1J8eOYdI,16670
matplotlib/axes/_secondary_axes.py,sha256=1Ay4160dy9KgkoRpIPT37jXGV6aGWIsPZh9akbFsKkE,10263
matplotlib/axes/_secondary_axes.pyi,sha256=STFsxs_oHWnwk_U6lIad8sMjOx8ZPVJDxTljB6KNtHo,1320
matplotlib/axis.py,sha256=lcbiS11mEpfxnkM04kP2aOADc5b6CWf-xaqmsGRvBb8,101074
matplotlib/axis.pyi,sha256=vU-Ysz5Mac7ViAo2zTPRQK65bBC7STeN7OET2ZtyIew,10073
matplotlib/backend_bases.py,sha256=Hz2LBFuuCQc0q_nGHZyyb6iafPvILqurTIv2OQzpEWE,126310
matplotlib/backend_bases.pyi,sha256=R8yZLidg7AP8943to7nrKwwC9N__8GVrf7sO3iwhQHY,16397
matplotlib/backend_managers.py,sha256=RQheCO_cQBlaWsYMbAmswu0UPKU7bmLTI5LEFgotklA,11795
matplotlib/backend_managers.pyi,sha256=agnuM0wiZRqSgqti2AgbKJijRLvEPNLOrSY8PEwLjFE,2253
matplotlib/backend_tools.py,sha256=_qq1Sioqdl9bNhLze_ktzFJF9M30WcWGK1OrBk4iU2A,32897
matplotlib/backend_tools.pyi,sha256=Pt07U2m84j7PPH_iim0mwZtf9BUhOlQC3kqAV6oOuNM,4030
matplotlib/backends/__init__.py,sha256=zedu8Er46aBvLouOLhCfcaPPC3QTp-KnhqWB58PK0As,137
matplotlib/backends/__pycache__/__init__.cpython-310.pyc,,
matplotlib/backends/__pycache__/_backend_gtk.cpython-310.pyc,,
matplotlib/backends/__pycache__/_backend_pdf_ps.cpython-310.pyc,,
matplotlib/backends/__pycache__/_backend_tk.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_agg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_cairo.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_gtk3.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_gtk3agg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_gtk3cairo.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_gtk4.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_gtk4agg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_gtk4cairo.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_macosx.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_mixed.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_nbagg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_pdf.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_pgf.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_ps.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_qt.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_qt5.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_qt5agg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_qt5cairo.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_qtagg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_qtcairo.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_svg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_template.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_tkagg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_tkcairo.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_webagg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_webagg_core.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_wx.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_wxagg.cpython-310.pyc,,
matplotlib/backends/__pycache__/backend_wxcairo.cpython-310.pyc,,
matplotlib/backends/__pycache__/qt_compat.cpython-310.pyc,,
matplotlib/backends/_backend_agg.cpython-310-x86_64-linux-gnu.so,sha256=1lJCrIU93MiJ823KkJNOUvDZIgNmsaCvWL2QhYKlJSk,3426552
matplotlib/backends/_backend_agg.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/_backend_gtk.py,sha256=A2fFTrR_A5JT8w83eq-7N9olVsof2YsWsE_z8AR3C1M,11338
matplotlib/backends/_backend_pdf_ps.py,sha256=E799e3XOJ5mp6YoecWp63xDS-DGqFLd4JfsGc0tMLRI,4444
matplotlib/backends/_backend_tk.py,sha256=InB5j62jucTjFuZPbMuZLIvESgPR4tvyXWM6Nlv947g,42799
matplotlib/backends/_macosx.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/_tkagg.cpython-310-x86_64-linux-gnu.so,sha256=toVrRpe6hWLQhnRQxwipQpqv0f7MqcKAYdQX3vnB3IQ,56192
matplotlib/backends/_tkagg.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/backend_agg.py,sha256=mpVjm8ANUCcyvOcwwyLqXxLjKZ4CQK9orxcWOwkrFP8,20508
matplotlib/backends/backend_cairo.py,sha256=TCLk0CJKEWNDx4fNFU98XlE6zdESZE-uc4JRTeb86bI,17444
matplotlib/backends/backend_gtk3.py,sha256=MrXDKd-qDy2PDzxBx-EuvZXxUn3LwJokLtqh5sbN8FA,21805
matplotlib/backends/backend_gtk3agg.py,sha256=qvYoB5OnIgjq7apNOiqTCaNVPnpetJWgyHyBsPLmPeg,2317
matplotlib/backends/backend_gtk3cairo.py,sha256=vxoMgZBBcUiT2_yay6YJ4fK15mGuzedirMuO38kb3oI,967
matplotlib/backends/backend_gtk4.py,sha256=twufreHOOO_u_exnNfltKpt0PLIDchNpHqjaN-fC85E,21572
matplotlib/backends/backend_gtk4agg.py,sha256=lYjPDc2pt58mPYjimCdutcJtAFvkBtrC_xGCMmiOXBk,1114
matplotlib/backends/backend_gtk4cairo.py,sha256=b-CR2mJBL8CJSa3ZC0t7fOPCLa74WGdHSfe8TfYZyYI,998
matplotlib/backends/backend_macosx.py,sha256=rtfx1jcD38z_FG2OWOxEL4AVAYT4OJEDVbNdf66DxaY,8767
matplotlib/backends/backend_mixed.py,sha256=PAYTjNuunpAa4-JkBguvyOjgDlB0eg9ARDAWidKfJpc,4698
matplotlib/backends/backend_nbagg.py,sha256=Au9RHfRufpI0ngT4R0K0CUVtAMFi9Bg-YhDunlj_Lko,8000
matplotlib/backends/backend_pdf.py,sha256=eOhOv0QjtdsU3nCKlsw06vPpFxRvp6bWRw9FJKTQ_Dk,106168
matplotlib/backends/backend_pgf.py,sha256=Sg-2IDH1avBWTpTqH37FhRZ9Qw_8JQ2iNKK4xZvdc1g,39897
matplotlib/backends/backend_ps.py,sha256=_q4NhCoNPzy6CwFnWhjpO0WKyUlPXmdd9BVPPAfMDqE,47772
matplotlib/backends/backend_qt.py,sha256=GY5FbUw4ksvs-_OeVfQi_WZPY3dpXT5e-nt39Cgqvk0,39370
matplotlib/backends/backend_qt5.py,sha256=kzfoo2ksEGsiWAa2LGtZYzKvfzqJJWyGOohohcRAu1g,787
matplotlib/backends/backend_qt5agg.py,sha256=Vh7H8kqWH4X8a3VX2XZ2Vze9srwJavkNHAZxdJUz_bk,352
matplotlib/backends/backend_qt5cairo.py,sha256=Go2Y0GVkXh1xh6x4F255_e5Xbwwws-OiD1Fc0805E78,292
matplotlib/backends/backend_qtagg.py,sha256=ZjPtp5wR6tZGjbngPXRdVXYRhiPPrc5C0q2DmtdRkpY,3413
matplotlib/backends/backend_qtcairo.py,sha256=e3SUG50VGqo68eS_8ebTCVQPa4AaxLxuo1JiWX4TIWg,1770
matplotlib/backends/backend_svg.py,sha256=SzTSAUEuWKX4gfzxJmuv1uYfio2YgUQhpMHH2RTI5OQ,49886
matplotlib/backends/backend_template.py,sha256=Z352VD5tp_xsNcR-DQcqt-LOB8lXoNzkCzFaMZaS0Dg,8010
matplotlib/backends/backend_tkagg.py,sha256=z9gB16fq2d-DUNpbeSDDLWaYmc0Jz3cDqNlBKhnQg0c,592
matplotlib/backends/backend_tkcairo.py,sha256=JaGGXh8Y5FwVZtgryIucN941Olf_Pn6f4Re7Vuxl1-c,845
matplotlib/backends/backend_webagg.py,sha256=IRW8u5Std0L3qTn4FWimQkDvifYe_-0vsDHPaZVSHOU,11158
matplotlib/backends/backend_webagg_core.py,sha256=7lcoinjD0fswQ1-acN5QOB1HJ6YoUgIZGrsmgZP5cl8,18303
matplotlib/backends/backend_wx.py,sha256=bmCCvnh0iUDgjIAgayFX7fLPevV3MklCIACT59u4xiE,49080
matplotlib/backends/backend_wxagg.py,sha256=i15osXEYQ7nds3pHuCuYEnuAtSZYtOisCoYvG29jjM4,1382
matplotlib/backends/backend_wxcairo.py,sha256=TK-m3S0c1WipfKE2IpIPNeE4hoXPjfMvnWAzHpCXpFs,848
matplotlib/backends/qt_compat.py,sha256=aHBq-zksSpQ7dFlApG4g1QCUlNGOSo6skzFPyIdI46g,8276
matplotlib/backends/qt_editor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/backends/qt_editor/__pycache__/__init__.cpython-310.pyc,,
matplotlib/backends/qt_editor/__pycache__/_formlayout.cpython-310.pyc,,
matplotlib/backends/qt_editor/__pycache__/figureoptions.cpython-310.pyc,,
matplotlib/backends/qt_editor/_formlayout.py,sha256=QmqqqLO6waqeSGOKjDNUwjvon53Z7yqil5AfqfftDWY,20953
matplotlib/backends/qt_editor/figureoptions.py,sha256=xeba5ul8ZqYHgmCq_yk126Mh5lVxjGyrxxIBk1PMvn4,9521
matplotlib/backends/web_backend/.eslintrc.js,sha256=pZxDrJU80urQlDMoO6A3ylTeZ7DgFoexDhi93Yfm6DU,666
matplotlib/backends/web_backend/.prettierignore,sha256=L47QXaDPUyI-rMmNAmn-OQH-5-Gi04-ZGl7QXdjP7h8,97
matplotlib/backends/web_backend/.prettierrc,sha256=OjC7XB1lRdhntVFThQG-J-wRiqwY1fStHF2i0XTOrbk,145
matplotlib/backends/web_backend/all_figures.html,sha256=44Y-GvIJbNlqQaKSW3kwVKpTxBSG1WsdYz3ZmYHlUsA,1753
matplotlib/backends/web_backend/css/boilerplate.css,sha256=qui16QXRnQFNJDbcMasfH6KtN9hLjv8883U9cJmsVCE,2310
matplotlib/backends/web_backend/css/fbm.css,sha256=wa4vNkNv7fQ_TufjJjecFZEzPMR6W8x6uXJga_wQILw,1456
matplotlib/backends/web_backend/css/mpl.css,sha256=ruca_aA5kNnP-MZmLkriu8teVP1nIgwcFEpoB16j8Z4,1611
matplotlib/backends/web_backend/css/page.css,sha256=ca3nO3TaPw7865PN5SlGJBTc2H3rBXQCMaFPywX29y4,1623
matplotlib/backends/web_backend/ipython_inline_figure.html,sha256=wgSxUh3xpPAxOnZgSMnrhDM5hYncOfWRGgaCUezvedY,1311
matplotlib/backends/web_backend/js/mpl.js,sha256=f52UO4xag_YcbRxCd-fq10eP_oy7ihISgcnrXMEDQyo,23951
matplotlib/backends/web_backend/js/mpl_tornado.js,sha256=Zs2Uzs7YUilG765nYvanCo-IK8HkHDtIum1KAq6bQ_w,302
matplotlib/backends/web_backend/js/nbagg_mpl.js,sha256=F-By4ZjOSmwpNAkxUUxUk35qCjGlf0B28Z0aOyfpxDM,9514
matplotlib/backends/web_backend/nbagg_uat.ipynb,sha256=Fwj_C5fIbm9tkoMYfjS6fdWuOPad3l2Fji7m76xTl8k,16427
matplotlib/backends/web_backend/package.json,sha256=vl3nGaoaVKYGnxlhZP7kVyAqoNmbQbKmEBnQwUWDmwE,545
matplotlib/backends/web_backend/single_figure.html,sha256=wEBwF602JLHErBUEXiS6jXqmxYAIzHpa3MMFrnev6rs,1357
matplotlib/bezier.py,sha256=MhvxLpz4yzhRxi_2EyV0Lvd7PNeBPgWH5A8SBytAmko,18679
matplotlib/bezier.pyi,sha256=itubQt_U1IKnQ508bhy6J8LSpaspvZIeOmJqg7JvOmU,2586
matplotlib/category.py,sha256=aDGxoBkadhun-NDpc4clsA2oHS7Ehqj2g2ApnUoV3Ig,7316
matplotlib/cbook.py,sha256=Lyr9N1BR2IfNkEAu4A18og5kGO94GjsDGquj2XFaWS4,76603
matplotlib/cbook.pyi,sha256=FUsaWhu2QbmGHV3uOiH6BjCpXonfDEATcfMyrfSfD6I,5749
matplotlib/cm.py,sha256=TqwZ5r81szWIjH_2xe7-p0912YmhDh1yMp_Xma2l5gE,26156
matplotlib/cm.pyi,sha256=bgZU7a6K512niO9Rf7VqijDXqewCZZ3PKgsualbKTz4,2079
matplotlib/collections.py,sha256=pb_fcgmvZSbKDzcV8qsQ79iUx_pNDz7Obu_lun1W1Ek,89540
matplotlib/collections.pyi,sha256=J3JeQIP245Q80X648AyjPEyN0oOex79d2NC-NOe0POc,10040
matplotlib/colorbar.py,sha256=oQ1pkXtYoJ4smXMZ7zG7U4KESnAXVVE2l0T_JztnKTM,60807
matplotlib/colorbar.pyi,sha256=lXMHYAvr9GnXcHgpy40WumUaNjX9TiHhpdWTpBwEl50,4800
matplotlib/colors.py,sha256=Zk0-8Zhi4pBwA-rup2hOEOd2SkO-VWqBi5p9AoXR_BU,100572
matplotlib/colors.pyi,sha256=TFUFx4N6ATggWmfVKvaX_-luUjVlqlu49226M6ilUIA,11156
matplotlib/container.py,sha256=LfE3vTumQknj-4RB_LHTg15XM3USXshSbDlrvaxABDo,4603
matplotlib/container.pyi,sha256=DIrGBN-5ZY0vt4mY6bEWH0n54wsJ7AFA9qNMMXCMspU,1757
matplotlib/contour.py,sha256=V6A2G5uFobdJWq8pJDrQ3wlBBTs8gAjv_dyRs7gnBQI,76542
matplotlib/contour.pyi,sha256=gNFXB61_DM1aZulsvUQ03XDriae495Yy5ZTNm3ez-f8,6279
matplotlib/dates.py,sha256=cE6WN_cthEiS4JkKyjB9tGr-ylX3jlPST_PLSJTpu6Q,68104
matplotlib/dviread.py,sha256=mwDcGc4o_-QA-3WRWqpUlRu0RcRWjukuptYdANQ17Xo,41958
matplotlib/dviread.pyi,sha256=YZUFI3FQ2NDnodV7lCz9lAlsH0Yt0_mWMdZdmYOikHw,2197
matplotlib/figure.py,sha256=oINCw_2oLf2a8elwO9Y4S37rOIXVfoZSFLdt5keI8RE,137267
matplotlib/figure.pyi,sha256=Vy7SPXbU9zevRV8IJSkgYh9Cj8Bwme-2xTLrB6GeTuQ,14100
matplotlib/font_manager.py,sha256=WWFeCQBwVtvr7ihjnNcBJZiiouHJ-Mzuny5YXw_OdrA,55269
matplotlib/font_manager.pyi,sha256=Jse1IshOdjhXHdjcvTxtos3rgsuECbRJzc264A-ZGus,5052
matplotlib/ft2font.cpython-310-x86_64-linux-gnu.so,sha256=SrOI21x7ndMkFSAe4zxLcCDx-B1VQ_Cv67irlpEIvs8,5708688
matplotlib/ft2font.pyi,sha256=4TEeRwvPFm2UyYdxx0z7fmJAdb7Jk_l7CFbP5FdqVms,7046
matplotlib/gridspec.py,sha256=o8JOzqjjdI5uNJtNr6ckqQjXQXS_ybpYDEUmNM0g-jY,27861
matplotlib/gridspec.pyi,sha256=ESc41LOG7Rke9ClBtWeO79ztHUweaupvFWO4PRdbJyk,4510
matplotlib/hatch.py,sha256=x7CrM_-EnuD8RFhQiGqWqe-i_mSunW71m-ZyfIGfQzA,7449
matplotlib/hatch.pyi,sha256=OEFkNe8TgrBli_MDMW99Tvhr44S4ySPaYQomjVXpUUA,2098
matplotlib/image.py,sha256=cjoisJ3cNhoKLMi8uZu6dbvaQy3B3mW2e9cHcMVMl4o,70827
matplotlib/image.pyi,sha256=SSn2hLOi4MOa-ma4BFx1HL0L5ejs3cUWi_eWFel-GJg,6759
matplotlib/layout_engine.py,sha256=T9o4YURMFZsTGBXqWOlkOyKoj3I7dUPGfmPYHRsGSSg,11248
matplotlib/layout_engine.pyi,sha256=9cKFTFPCvwmKIygTUvrvOq1iWnFYme9Shniiv2QbC74,1788
matplotlib/legend.py,sha256=UlyOZgHCcw7qr12LD9AepANLnGFSWFGiJfPB6qJ83no,55758
matplotlib/legend.pyi,sha256=3P4GDFH1rkd6cU6U9NvkVwR5Ju_KeOidY_kVRwjob7U,5434
matplotlib/legend_handler.py,sha256=CxW3PlOMhpU6Wnp0sI0NVXomLph5haHfC1l9e-m2kvo,29915
matplotlib/legend_handler.pyi,sha256=3VEfeioGIAxhd3mhg4PXETZjCKf4OlXL0jz1MAFGtos,7655
matplotlib/lines.py,sha256=ifpdEGSMX9hW6E4CnPdxAHw5LPbgxwDwY1sC_N7QiU4,56352
matplotlib/lines.pyi,sha256=5RB6Fr5i67lRKqEiLNGIGRX5mtxT30nbt18WFrLsQao,6080
matplotlib/markers.py,sha256=OE85Pr5l8pVQJT34Gd9yHW01lczjEkL0qMrf_R9RC1s,34164
matplotlib/markers.pyi,sha256=Au4p3eMRv0zdmLkxejChESu3z2q-stPupCtGNxvPLng,1685
matplotlib/mathtext.py,sha256=FRczaMkP8Y17aSxlekQG1xdVs-51bxbo3afREoi0U2M,4943
matplotlib/mathtext.pyi,sha256=RCVxYGQ_CJ6wC7v_HkqoouU2PhtcvlJ1ffIyxzAC-so,1045
matplotlib/mlab.py,sha256=we3UDcCsfbc_qUri6PxCTZJm4TyKjRRFfkpNawlkh6w,30294
matplotlib/mlab.pyi,sha256=mkR7wbJS9eCQfCFsUWoXnqrAy0kcE3cVqpFZCeOGC_Q,3583
matplotlib/mpl-data/fonts/afm/cmex10.afm,sha256=blR3ERmrVBV5XKkAnDCj4NMeYVgzH7cXtJ3u59u9GuE,12070
matplotlib/mpl-data/fonts/afm/cmmi10.afm,sha256=5qwEOpedEo76bDUahyuuF1q0cD84tRrX-VQ4p3MlfBo,10416
matplotlib/mpl-data/fonts/afm/cmr10.afm,sha256=WDvgC_D3UkGJg9u-J0U6RaT02lF4oz3lQxHtg1r3lYw,10101
matplotlib/mpl-data/fonts/afm/cmsy10.afm,sha256=AbmzvCVWBceHRfmRfeJ9E6xzOQTFLk0U1zDfpf3_MaM,8295
matplotlib/mpl-data/fonts/afm/cmtt10.afm,sha256=4ji7_mTpeWMa93o_UHBWPKCnqsBfhJJNllat1lJArP4,6501
matplotlib/mpl-data/fonts/afm/pagd8a.afm,sha256=jjFrigwkTpYLqa26cpzZvKQNBo-PuF4bmDVqaM4pMWw,17183
matplotlib/mpl-data/fonts/afm/pagdo8a.afm,sha256=sgNQdeYyx8J-itGw9h31y95aMBiTCRvmNSPTXwwS7xg,17255
matplotlib/mpl-data/fonts/afm/pagk8a.afm,sha256=ZUtfHPloNqcvGMHMxaKDSlshhOcjwheUx143RwpGdIU,17241
matplotlib/mpl-data/fonts/afm/pagko8a.afm,sha256=Yj1wBg6Jsqqz1KBfhRoJ3ACR-CMQol8Fj_ZM5NZ1gDk,17346
matplotlib/mpl-data/fonts/afm/pbkd8a.afm,sha256=Zl5o6J_di9Y5j2EpHtjew-_sfg7-WoeVmO9PzOYSTUc,15157
matplotlib/mpl-data/fonts/afm/pbkdi8a.afm,sha256=JAOno930iTyfZILMf11vWtiaTgrJcPpP6FRTRhEMMD4,15278
matplotlib/mpl-data/fonts/afm/pbkl8a.afm,sha256=UJqJjOJ6xQDgDBLX157mKpohIJFVmHM-N6x2-DiGv14,15000
matplotlib/mpl-data/fonts/afm/pbkli8a.afm,sha256=AWislZ2hDbs0ox_qOWREugsbS8_8lpL48LPMR40qpi0,15181
matplotlib/mpl-data/fonts/afm/pcrb8a.afm,sha256=6j1TS2Uc7DWSc-8l42TGDc1u0Fg8JspeWfxFayjUwi8,15352
matplotlib/mpl-data/fonts/afm/pcrbo8a.afm,sha256=smg3mjl9QaBDtQIt06ko5GvaxLsO9QtTvYANuE5hfG0,15422
matplotlib/mpl-data/fonts/afm/pcrr8a.afm,sha256=7nxFr0Ehz4E5KG_zSE5SZOhxRH8MyfnCbw-7x5wu7tw,15339
matplotlib/mpl-data/fonts/afm/pcrro8a.afm,sha256=NKEz7XtdFkh9cA8MvY-S3UOZlV2Y_J3tMEWFFxj7QSg,15443
matplotlib/mpl-data/fonts/afm/phvb8a.afm,sha256=NAx4M4HjL7vANCJbc-tk04Vkol-T0oaXeQ3T2h-XUvM,17155
matplotlib/mpl-data/fonts/afm/phvb8an.afm,sha256=8e_myD-AQkNF7q9XNLb2m76_lX2TUr3a5wog_LIE1sk,17086
matplotlib/mpl-data/fonts/afm/phvbo8a.afm,sha256=8fkBRmJ-SWY2YrBg8fFyjJyrJp8daQ6JPO6LvhM8xPI,17230
matplotlib/mpl-data/fonts/afm/phvbo8an.afm,sha256=aeVRvV4r15BBvxuRJ0MG8ZHuH2HViuIiCYkvuapmkmM,17195
matplotlib/mpl-data/fonts/afm/phvl8a.afm,sha256=IyMYM-bgl-gI6rG0EuZZ2OLzlxJfGeSh8xqsh0t-eJQ,15627
matplotlib/mpl-data/fonts/afm/phvlo8a.afm,sha256=s12C-eNnIDHJ_UVbuiprjxBjCiHIbS3Y8ORTC-qTpuI,15729
matplotlib/mpl-data/fonts/afm/phvr8a.afm,sha256=Kt8KaRidts89EBIK29X2JomDUEDxvroeaJz_RNTi6r4,17839
matplotlib/mpl-data/fonts/afm/phvr8an.afm,sha256=lL5fAHTRwODl-sB5mH7IfsD1tnnea4yRUK-_Ca2bQHM,17781
matplotlib/mpl-data/fonts/afm/phvro8a.afm,sha256=3KqK3eejiR4hIFBUynuSX_4lMdE2V2T58xOF8lX-fwc,17919
matplotlib/mpl-data/fonts/afm/phvro8an.afm,sha256=Vx9rRf3YfasMY7tz-njSxz67xHKk-fNkN7yBi0X2IP0,17877
matplotlib/mpl-data/fonts/afm/pncb8a.afm,sha256=aoXepTcDQtQa_mspflMJkEFKefzXHoyjz6ioJVI0YNc,16028
matplotlib/mpl-data/fonts/afm/pncbi8a.afm,sha256=pCWW1MYgy0EmvwaYsaYJaAI_LfrsKmDANHu7Pk0RaiU,17496
matplotlib/mpl-data/fonts/afm/pncr8a.afm,sha256=0CIB2BLe9r-6_Wl5ObRTTf98UOrezmGQ8ZOuBX5kLks,16665
matplotlib/mpl-data/fonts/afm/pncri8a.afm,sha256=5R-pLZOnaHNG8pjV6MP3Ai-d2OTQYR_cYCb5zQhzfSU,16920
matplotlib/mpl-data/fonts/afm/pplb8a.afm,sha256=3EzUbNnXr5Ft5eFLY00W9oWu59rHORgDXUuJaOoKN58,15662
matplotlib/mpl-data/fonts/afm/pplbi8a.afm,sha256=X_9tVspvrcMer3OS8qvdwjFFqpAXYZneyCL2NHA902g,15810
matplotlib/mpl-data/fonts/afm/pplr8a.afm,sha256=ijMb497FDJ9nVdVMb21F7W3-cu9sb_9nF0oriFpSn8k,15752
matplotlib/mpl-data/fonts/afm/pplri8a.afm,sha256=8KITbarcUUMi_hdoRLLmNHtlqs0TtOSKqtPFft7X5nY,15733
matplotlib/mpl-data/fonts/afm/psyr.afm,sha256=Iyt8ajE4B2Tm34oBj2pKtctIf9kPfq05suQefq8p3Ro,9644
matplotlib/mpl-data/fonts/afm/ptmb8a.afm,sha256=bL1fA1NC4_nW14Zrnxz4nHlXJb4dzELJPvodqKnYeMg,17983
matplotlib/mpl-data/fonts/afm/ptmbi8a.afm,sha256=-_Ui6XlKaFTHEnkoS_-1GtIr5VtGa3gFQ2ezLOYHs08,18070
matplotlib/mpl-data/fonts/afm/ptmr8a.afm,sha256=IEcsWcmzJyjCwkgsw4o6hIMmzlyXUglJat9s1PZNnEU,17942
matplotlib/mpl-data/fonts/afm/ptmri8a.afm,sha256=49fQMg5fIGguZ7rgc_2styMK55Pv5bPTs7wCzqpcGpk,18068
matplotlib/mpl-data/fonts/afm/putb8a.afm,sha256=qMaHTdpkrNL-m4DWhjpxJCSmgYkCv1qIzLlFfM0rl40,21532
matplotlib/mpl-data/fonts/afm/putbi8a.afm,sha256=g7AVJyiTxeMpNk_1cSfmYgM09uNUfPlZyWGv3D1vcAk,21931
matplotlib/mpl-data/fonts/afm/putr8a.afm,sha256=XYmNC5GQgSVAZKTIYdYeNksE6znNm9GF_0SmQlriqx0,22148
matplotlib/mpl-data/fonts/afm/putri8a.afm,sha256=i7fVe-iLyLtQxCfAa4IxdxH-ufcHmMk7hbCGG5TxAY4,21891
matplotlib/mpl-data/fonts/afm/pzcmi8a.afm,sha256=wyuoIWEZOcoXrSl1tPzLkEahik7kGi91JJj-tkFRG4A,16250
matplotlib/mpl-data/fonts/afm/pzdr.afm,sha256=MyjLAnzKYRdQBfof1W3k_hf30MvqOkqL__G22mQ5xww,9467
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Bold.afm,sha256=sIDDI-B82VZ3C0mI_mHFITCZ7PVn37AIYMv1CrHX4sE,15333
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-BoldOblique.afm,sha256=zg61QobD3YU9UBfCXmvmhBNaFKno-xj8sY0b2RpgfLw,15399
matplotlib/mpl-data/fonts/pdfcorefonts/Courier-Oblique.afm,sha256=vRQm5j1sTUN4hicT1PcVZ9P9DTTUHhEzfPXqUUzVZhE,15441
matplotlib/mpl-data/fonts/pdfcorefonts/Courier.afm,sha256=Mdcq2teZEBJrIqVXnsnhee7oZnTs6-P8_292kWGTrw4,15335
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Bold.afm,sha256=i2l4gcjuYXoXf28uK7yIVwuf0rnw6J7PwPVQeHj5iPw,69269
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-BoldOblique.afm,sha256=Um5O6qK11DXLt8uj_0IoWkc84TKqHK3bObSKUswQqvY,69365
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica-Oblique.afm,sha256=hVYDg2b52kqtbVeCzmiv25bW1yYdpkZS-LXlGREN2Rs,74392
matplotlib/mpl-data/fonts/pdfcorefonts/Helvetica.afm,sha256=23cvKDD7bQAJB3kdjSahJSTZaUOppznlIO6FXGslyW8,74292
matplotlib/mpl-data/fonts/pdfcorefonts/Symbol.afm,sha256=P5UaoXr4y0qh4SiMa5uqijDT6ZDr2-jPmj1ayry593E,9740
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Bold.afm,sha256=cQTmr2LFPwKQE_sGQageMcmFicjye16mKJslsJLHQyE,64251
matplotlib/mpl-data/fonts/pdfcorefonts/Times-BoldItalic.afm,sha256=pzWOdycm6RqocBWgAVY5Jq0z3Fp7LuqWgLNMx4q6OFw,59642
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Italic.afm,sha256=bK5puSMpGT_YUILwyJrXoxjfj7XJOdfv5TQ_iKsJRzw,66328
matplotlib/mpl-data/fonts/pdfcorefonts/Times-Roman.afm,sha256=hhNrUnpazuDDKD1WpraPxqPWCYLrO7D7bMVOg-zI13o,60460
matplotlib/mpl-data/fonts/pdfcorefonts/ZapfDingbats.afm,sha256=ZuOmt9GcKofjdOq8kqhPhtAIhOwkL2rTJTmZxAjFakA,9527
matplotlib/mpl-data/fonts/pdfcorefonts/readme.txt,sha256=MRv8ppSITYYAb7lt5EOw9DWWNZIblfxsFhu5TQE7cpI,828
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf,sha256=sYS4njwQdfIva3FXW2_CDUlys8_TsjMiym_Vltyu8Wc,704128
matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf,sha256=bt8CgxYBhq9FHL7nHnuEXy5Mq_Jku5ks5mjIPCVGXm8,641720
matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf,sha256=zN90s1DxH9PdV3TeUOXmNGoaXaH1t9X7g1kGZel6UhM,633840
matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf,sha256=P99pyr8GBJ6nCgC1kZNA4s4ebQKwzDxLRPtoAb0eDSI,756072
matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf,sha256=ggmdz7paqGjN_CdFGYlSX-MpL3N_s8ngMozpzvWWUvY,25712
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf,sha256=uq2ppRcv4giGJRr_BDP8OEYZEtXa8HKH577lZiCo2pY,331536
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf,sha256=ppCBwVx2yCfgonpaf1x0thNchDSZlVSV_6jCDTqYKIs,253116
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf,sha256=KAUoE_enCfyJ9S0ZLcmV708P3Fw9e3OknWhJsZFtDNA,251472
matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf,sha256=YC7Ia4lIz82VZIL-ZPlMNshndwFJ7y95HUYT9EO87LM,340240
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf,sha256=w3U_Lta8Zz8VhG3EWt2-s7nIcvMvsY_VOiHxvvHtdnY,355692
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf,sha256=2T7-x6nS6CZ2jRou6VuVhw4V4pWZqE80hK8d4c7C4YE,347064
matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf,sha256=PnmU-8VPoQzjNSpC1Uj63X2crbacsRCbydlg9trFfwQ,345612
matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf,sha256=EHJElW6ZYrnpb6zNxVGCXgrgiYrhNzcTPhuSGi_TX_o,379740
matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf,sha256=KRTzLkfHd8J75Wd6-ufbTeefnkXeb8kJfZlJwjwU99U,14300
matplotlib/mpl-data/fonts/ttf/LICENSE_DEJAVU,sha256=11k43sCY8G8Kw8AIUwZdlPAgvhw8Yu8dwpdboVtNmw4,4816
matplotlib/mpl-data/fonts/ttf/LICENSE_STIX,sha256=urPTHf7wf0g2JPL2XycR52BluOcnMnixwHHt4QQcmVk,5476
matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf,sha256=FnN4Ax4t3cYhbWeBnJJg6aBv_ExHjk4jy5im_USxg8I,448228
matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf,sha256=6FM9xwg_o0a9oZM9YOpKg7Z9CUW86vGzVB-CtKDixqA,237360
matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf,sha256=mHiP1LpI37sr0CbA4gokeosGxzcoeWKLemuw1bsJc2w,181152
matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf,sha256=bPyzM9IrfDxiO9_UAXTxTIXD1nMcphZsHtyAFA6uhSc,175040
matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf,sha256=Ulb34CEzWsSFTRgPDovxmJZOwvyCAXYnbhaqvGU3u1c,59108
matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf,sha256=XRBqW3jR_8MBdFU0ObhiV7-kXwiBIMs7QVClHcT5tgs,30512
matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf,sha256=pb22DnbDf2yQqizotc3wBDqFGC_g27YcCGJivH9-Le8,41272
matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf,sha256=BMr9pWiBv2YIZdq04X4c3CgL6NPLUPrl64aV1N4w9Ug,46752
matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf,sha256=wYuH1gYUpCuusqItRH5kf9p_s6mUD-9X3L5RvRtKSxs,13656
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf,sha256=yNdvjUoSmsZCULmD7SVq9HabndG9P4dPhboL1JpAf0s,12228
matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf,sha256=-9xVMYL4_1rcO8FiCKrCfR4PaSmKtA42ddLGqwtei1w,15972
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf,sha256=cYexyo8rZcdqMlpa9fNF5a2IoXLUTZuIvh0JD1Qp0i4,12556
matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf,sha256=0lbHzpndzJmO8S42mlkhsz5NbvJLQCaH5Mcc7QZRDzc,19760
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf,sha256=3eBc-VtYbhQU3BnxiypfO6eAzEu8BdDvtIJSFbkS2oY,12192
matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf,sha256=XFSKCptbESM8uxHtUFSAV2cybwxhSjd8dWVByq6f3w0,15836
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf,sha256=MUCYHrA0ZqFiSE_PjIGlJZgMuv79aUgQqE7Dtu3kuo0,12116
matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf,sha256=_sdxDuEwBDtADpu9CyIXQxV7sIqA2TZVBCUiUjq5UCk,15704
matplotlib/mpl-data/fonts/ttf/cmb10.ttf,sha256=B0SXtQxD6ldZcYFZH5iT04_BKofpUQT1ZX_CSB9hojo,25680
matplotlib/mpl-data/fonts/ttf/cmex10.ttf,sha256=ryjwwXByOsd2pxv6WVrKCemNFa5cPVTOGa_VYZyWqQU,21092
matplotlib/mpl-data/fonts/ttf/cmmi10.ttf,sha256=MJKWW4gR_WpnZXmWZIRRgfwd0TMLk3-RWAjEhdMWI00,32560
matplotlib/mpl-data/fonts/ttf/cmr10.ttf,sha256=Tdl2GwWMAJ25shRfVe5mF9CTwnPdPWxbPkP_YRD6m_Y,26348
matplotlib/mpl-data/fonts/ttf/cmss10.ttf,sha256=ffkag9BbLkcexjjLC0NaNgo8eSsJ_EKn2mfpHy55EVo,20376
matplotlib/mpl-data/fonts/ttf/cmsy10.ttf,sha256=uyJu2TLz8QDNDlL15JEu5VO0G2nnv9uNOFTbDrZgUjI,29396
matplotlib/mpl-data/fonts/ttf/cmtt10.ttf,sha256=YhHwmuk1mZka_alwwkZp2tGnfiU9kVYk-_IS9wLwcdc,28136
matplotlib/mpl-data/images/back-symbolic.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back.pdf,sha256=ZR7CJo_dAeCM-KlaGvskgtHQyRtrPIolc8REOmcoqJk,1623
matplotlib/mpl-data/images/back.png,sha256=E4dGf4Gnz1xJ1v2tMygHV0YNQgShreDeVApaMb-74mU,380
matplotlib/mpl-data/images/back.svg,sha256=yRdMiKsa-awUm2x_JE_rEV20rNTa7FInbFBEoMo-6ik,1512
matplotlib/mpl-data/images/back_large.png,sha256=9A6hUSQeszhYONE4ZuH3kvOItM0JfDVu6tkfromCbsQ,620
matplotlib/mpl-data/images/filesave-symbolic.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave.pdf,sha256=P1EPPV2g50WTt8UaX-6kFoTZM1xVqo6S2H6FJ6Zd1ec,1734
matplotlib/mpl-data/images/filesave.png,sha256=b7ctucrM_F2mG-DycTedG_a_y4pHkx3F-zM7l18GLhk,458
matplotlib/mpl-data/images/filesave.svg,sha256=oxPVbLS9Pzelz71C1GCJWB34DZ0sx_pUVPRHBrCZrGs,2029
matplotlib/mpl-data/images/filesave_large.png,sha256=LNbRD5KZ3Kf7nbp-stx_a1_6XfGBSWUfDdpgmnzoRvk,720
matplotlib/mpl-data/images/forward-symbolic.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward.pdf,sha256=KIqIL4YId43LkcOxV_TT5uvz1SP8k5iUNUeJmAElMV8,1630
matplotlib/mpl-data/images/forward.png,sha256=pKbLepgGiGeyY2TCBl8svjvm7Z4CS3iysFxcq4GR-wk,357
matplotlib/mpl-data/images/forward.svg,sha256=NnQDOenfjsn-o0aJMUfErrP320Zcx9XHZkLh0cjMHsk,1531
matplotlib/mpl-data/images/forward_large.png,sha256=36h7m7DZDHql6kkdpNPckyi2LKCe_xhhyavWARz_2kQ,593
matplotlib/mpl-data/images/hand.pdf,sha256=hspwkNY915KPD7AMWnVQs7LFPOtlcj0VUiLu76dMabQ,4172
matplotlib/mpl-data/images/hand.png,sha256=2cchRETGKa0hYNKUxnJABwkyYXEBPqJy_VqSPlT0W2Q,979
matplotlib/mpl-data/images/hand.svg,sha256=tsVIES_nINrAbH4FqdsCGOx0SVE37vcofSYBhnnaOP0,4888
matplotlib/mpl-data/images/help-symbolic.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help.pdf,sha256=CeE978IMi0YWznWKjIT1R8IrP4KhZ0S7usPUvreSgcA,1813
matplotlib/mpl-data/images/help.png,sha256=s4pQrqaQ0py8I7vc9hv3BI3DO_tky-7YBMpaHuBDCBY,472
matplotlib/mpl-data/images/help.svg,sha256=KXabvQhqIWen_t2SvZuddFYa3S0iI3W8cAKm3s1fI8Q,1870
matplotlib/mpl-data/images/help_large.png,sha256=1IwEyWfGRgnoCWM-r9CJHEogTJVD5n1c8LXTK4AJ4RE,747
matplotlib/mpl-data/images/home-symbolic.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home.pdf,sha256=e0e0pI-XRtPmvUCW2VTKL1DeYu1pvPmUUeRSgEbWmik,1737
matplotlib/mpl-data/images/home.png,sha256=IcFdAAUa6_A0qt8IO3I8p4rpXpQgAlJ8ndBECCh7C1w,468
matplotlib/mpl-data/images/home.svg,sha256=n_AosjJVXET3McymFuHgXbUr5vMLdXK2PDgghX8Cch4,1891
matplotlib/mpl-data/images/home_large.png,sha256=uxS2O3tWOHh1iau7CaVV4ermIJaZ007ibm5Z3i8kXYg,790
matplotlib/mpl-data/images/matplotlib.pdf,sha256=BkSUf-2xoij-eXfpV2t7y1JFKG1zD1gtV6aAg3Xi_wE,22852
matplotlib/mpl-data/images/matplotlib.png,sha256=w8KLRYVa-voUZXa41hgJauQuoois23f3NFfdc72pUYY,1283
matplotlib/mpl-data/images/matplotlib.svg,sha256=QiTIcqlQwGaVPtHsEk-vtmJk1wxwZSvijhqBe_b9VCI,62087
matplotlib/mpl-data/images/matplotlib_large.png,sha256=ElRoue9grUqkZXJngk-nvh4GKfpvJ4gE69WryjCbX5U,3088
matplotlib/mpl-data/images/move-symbolic.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move.pdf,sha256=CXk3PGK9WL5t-5J-G2X5Tl-nb6lcErTBS5oUj2St6aU,1867
matplotlib/mpl-data/images/move.png,sha256=TmjR41IzSzxGbhiUcV64X0zx2BjrxbWH3cSKvnG2vzc,481
matplotlib/mpl-data/images/move.svg,sha256=_ZKpcwGD6DMTkZlbyj0nQbT8Ygt5vslEZ0OqXaXGd4E,2509
matplotlib/mpl-data/images/move_large.png,sha256=Skjz2nW_RTA5s_0g88gdq2hrVbm6DOcfYW4Fu42Fn9U,767
matplotlib/mpl-data/images/qt4_editor_options.pdf,sha256=2qu6GVyBrJvVHxychQoJUiXPYxBylbH2j90QnytXs_w,1568
matplotlib/mpl-data/images/qt4_editor_options.png,sha256=EryQjQ5hh2dwmIxtzCFiMN1U6Tnd11p1CDfgH5ZHjNM,380
matplotlib/mpl-data/images/qt4_editor_options.svg,sha256=E00YoX7u4NrxMHm_L1TM8PDJ88bX5qRdCrO-Uj59CEA,1244
matplotlib/mpl-data/images/qt4_editor_options_large.png,sha256=-Pd-9Vh5aIr3PZa8O6Ge_BLo41kiEnpmkdDj8a11JkY,619
matplotlib/mpl-data/images/subplots-symbolic.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots.pdf,sha256=Q0syPMI5EvtgM-CE-YXKOkL9eFUAZnj_X2Ihoj6R4p4,1714
matplotlib/mpl-data/images/subplots.png,sha256=MUfCItq3_yzb9yRieGOglpn0Y74h8IA7m5i70B63iRc,445
matplotlib/mpl-data/images/subplots.svg,sha256=8acBogXIr9OWGn1iD6mUkgahdFZgDybww385zLCLoIs,2130
matplotlib/mpl-data/images/subplots_large.png,sha256=Edu9SwVMQEXJZ5ogU5cyW7VLcwXJdhdf-EtxxmxdkIs,662
matplotlib/mpl-data/images/zoom_to_rect-symbolic.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect.pdf,sha256=SEvPc24gfZRpl-dHv7nx8KkxPyU66Kq4zgQTvGFm9KA,1609
matplotlib/mpl-data/images/zoom_to_rect.png,sha256=aNz3QZBrIgxu9E-fFfaQweCVNitGuDUFoC27e5NU2L4,530
matplotlib/mpl-data/images/zoom_to_rect.svg,sha256=1vRxr3cl8QTwTuRlQzD1jxu0fXZofTJ2PMgG97E7Bco,1479
matplotlib/mpl-data/images/zoom_to_rect_large.png,sha256=V6pkxmm6VwFExdg_PEJWdK37HB7k3cE_corLa7RbUMk,1016
matplotlib/mpl-data/kpsewhich.lua,sha256=RdyYaBnBLy3NsB5c2R5FGrKu-V-WBcZim24NWilsTfw,139
matplotlib/mpl-data/matplotlibrc,sha256=-pbT883SOe_FO5ETRVoeW9e8WcCkwK1HAfkUFvtY8EU,42212
matplotlib/mpl-data/plot_directive/plot_directive.css,sha256=utSJ1oETz0UG6AC9hU134J_JY78ENijqMZXN0JMBUfk,318
matplotlib/mpl-data/sample_data/Minduka_Present_Blue_Pack.png,sha256=XnKGiCanpDKalQ5anvo5NZSAeDP7fyflzQAaivuc0IE,13634
matplotlib/mpl-data/sample_data/README.txt,sha256=ABz19VBKfGewdY39QInG9Qccgn1MTYV3bT5Ph7TCy2Y,128
matplotlib/mpl-data/sample_data/Stocks.csv,sha256=72878aZNXGxd5wLvFUw_rnj-nfg4gqtrucZji-w830c,67924
matplotlib/mpl-data/sample_data/axes_grid/bivariate_normal.npy,sha256=DpWZ9udAh6ospYqneEa27D6EkRgORFwHosacZXVu98U,1880
matplotlib/mpl-data/sample_data/data_x_x2_x3.csv,sha256=A0SU3buOUGhT-NI_6LQ6p70fFSIU3iLFdgzvzrKR6SE,132
matplotlib/mpl-data/sample_data/eeg.dat,sha256=KGVjFt8ABKz7p6XZirNfcxSTOpGGNuyA8JYErRKLRBc,25600
matplotlib/mpl-data/sample_data/embedding_in_wx3.xrc,sha256=cUqVw5vDHNSZoaO4J0ebZUf5SrJP36775abs7R9Bclg,2186
matplotlib/mpl-data/sample_data/goog.npz,sha256=QAkXzzDmtmT3sNqT18dFhg06qQCNqLfxYNLdEuajGLE,22845
matplotlib/mpl-data/sample_data/grace_hopper.jpg,sha256=qMptc0dlcDsJcoq0f-WfRz2Trjln_CTHwCiMPHrbcTA,61306
matplotlib/mpl-data/sample_data/jacksboro_fault_dem.npz,sha256=1JP1CjPoKkQgSUxU0fyhU50Xe9wnqxkLxf5ukvYvtjc,174061
matplotlib/mpl-data/sample_data/logo2.png,sha256=DXNx4FXeyqxHy26AmvNELpwezQLxweLQY9HP7ktKIdc,22279
matplotlib/mpl-data/sample_data/membrane.dat,sha256=q3lbQpIBpbtXXGNw1eFwkN_PwxdDGqk4L46IE2b0M1c,48000
matplotlib/mpl-data/sample_data/msft.csv,sha256=GArKb0O3DgKZRsKdJf6lX3rMSf-PCekIiBoLNdgF7Mk,3211
matplotlib/mpl-data/sample_data/s1045.ima.gz,sha256=MrQk1k9it-ccsk0p_VOTitVmTWCAVaZ6srKvQ2n4uJ4,33229
matplotlib/mpl-data/sample_data/topobathy.npz,sha256=AkTgMpFwLfRQJNy1ysvE89TLMNct-n_TccSsYcQrT78,45224
matplotlib/mpl-data/stylelib/Solarize_Light2.mplstyle,sha256=aytOm4eT_SPvs7HC28ZY4GukeN44q-SE0JEMCR8kVOk,1257
matplotlib/mpl-data/stylelib/_classic_test_patch.mplstyle,sha256=iopHpMaM3im_AK2aiHGuM2DKM5i9Kc84v6NQEoSb10Q,167
matplotlib/mpl-data/stylelib/_mpl-gallery-nogrid.mplstyle,sha256=1VOL3USqD6iuGQaSynNg1QhyUwvKLnkLyUKdbBMnnqg,489
matplotlib/mpl-data/stylelib/_mpl-gallery.mplstyle,sha256=MN-q59CiDqHXB8xFKXxzCbJJbJmNDhBe9lDJJAoMTPA,504
matplotlib/mpl-data/stylelib/bmh.mplstyle,sha256=-KbhaI859BITHIoyUZIfpQDjfckgLAlDAS_ydKsm6mc,712
matplotlib/mpl-data/stylelib/classic.mplstyle,sha256=zTnF6_FU1lp9P77Y5DwkIajl8inFRpNnQSdOEeKQRnc,24599
matplotlib/mpl-data/stylelib/dark_background.mplstyle,sha256=Ht6phZUy3zNRdcfHKcSb1uh3O8DunSPX8HPt9xTyzuo,658
matplotlib/mpl-data/stylelib/fast.mplstyle,sha256=yTa2YEIIP9xi5V_G0p2vSlxghuhNwjRi9gPECMxyRiM,288
matplotlib/mpl-data/stylelib/fivethirtyeight.mplstyle,sha256=WNUmAFuBPcqQPVgt6AS1ldy8Be2XO01N-1YQL__Q6ZY,832
matplotlib/mpl-data/stylelib/ggplot.mplstyle,sha256=u2oPHMLWFtZcpIjHk2swi2Nrt4NgnEtof5lxcwM0RD0,956
matplotlib/mpl-data/stylelib/grayscale.mplstyle,sha256=KCLg-pXpns9cnKDXKN2WH6mV41OH-6cbT-5zKQotSdw,526
matplotlib/mpl-data/stylelib/seaborn-v0_8-bright.mplstyle,sha256=pDqn3-NUyVLvlfkYs8n8HzNZvmslVMChkeH-HtZuJIc,144
matplotlib/mpl-data/stylelib/seaborn-v0_8-colorblind.mplstyle,sha256=eCSzFj5_2vR6n5qu1rHE46wvSVGZcdVqz85ov40ZsH8,148
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark-palette.mplstyle,sha256=p5ABKNQHRG7bk4HXqMQrRBjDlxGAo3RCXHdQmP7g-Ng,142
matplotlib/mpl-data/stylelib/seaborn-v0_8-dark.mplstyle,sha256=I4xQ75vE5_9X4k0cNDiqhhnF3OcrZ2xlPX8Ll7OCkoE,667
matplotlib/mpl-data/stylelib/seaborn-v0_8-darkgrid.mplstyle,sha256=2bXOSzS5gmPzRBrRmzVWyhg_7ZaBRQ6t_-O-cRuyZoA,670
matplotlib/mpl-data/stylelib/seaborn-v0_8-deep.mplstyle,sha256=44dLcXjjRgR-6yaopgGRInaVgz3jk8VJVQTbBIcxRB0,142
matplotlib/mpl-data/stylelib/seaborn-v0_8-muted.mplstyle,sha256=T4o3jvqKD_ImXDkp66XFOV_xrBVFUolJU34JDFk1Xkk,143
matplotlib/mpl-data/stylelib/seaborn-v0_8-notebook.mplstyle,sha256=PcvZQbYrDdducrNlavBPmQ1g2minio_9GkUUFRdgtoM,382
matplotlib/mpl-data/stylelib/seaborn-v0_8-paper.mplstyle,sha256=n0mboUp2C4Usq2j6tNWcu4TZ_YT4-kKgrYO0t-rz1yw,393
matplotlib/mpl-data/stylelib/seaborn-v0_8-pastel.mplstyle,sha256=8nV8qRpbUrnFZeyE6VcQ1oRuZPLil2W74M2U37DNMOE,144
matplotlib/mpl-data/stylelib/seaborn-v0_8-poster.mplstyle,sha256=dUaKqTE4MRfUq2rWVXbbou7kzD7Z9PE9Ko8aXLza8JA,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-talk.mplstyle,sha256=7FnBaBEdWBbncTm6_ER-EQVa_bZgU7dncgez-ez8R74,403
matplotlib/mpl-data/stylelib/seaborn-v0_8-ticks.mplstyle,sha256=CITZmZFUFp40MK2Oz8tI8a7WRoCizQU9Z4J172YWfWw,665
matplotlib/mpl-data/stylelib/seaborn-v0_8-white.mplstyle,sha256=WjJ6LEU6rlCwUugToawciAbKP9oERFHr9rfFlUrdTx0,665
matplotlib/mpl-data/stylelib/seaborn-v0_8-whitegrid.mplstyle,sha256=ec4BjsNzmOvHptcJ3mdPxULF3S1_U1EUocuqfIpw-Nk,664
matplotlib/mpl-data/stylelib/seaborn-v0_8.mplstyle,sha256=_Xu6qXKzi4b3GymCOB1b1-ykKTQ8xhDliZ8ezHGTiAs,1130
matplotlib/mpl-data/stylelib/tableau-colorblind10.mplstyle,sha256=BsirZVd1LmPWT4tBIz6loZPjZcInoQrIGfC7rvzqmJw,190
matplotlib/offsetbox.py,sha256=bykN-_QaoeKHjWrmroDu7D-8Tgd3mVxCzZI8O4tPiLw,54026
matplotlib/offsetbox.pyi,sha256=MPWhJMP34EvzZ5n35QzoyQqCeqzBoZEkuoNnuqUW3ZU,10681
matplotlib/patches.py,sha256=AsrSJHh0RxAMIffeW_qwzg-YCkz0y_8bgBSJXdFb5Iw,159899
matplotlib/patches.pyi,sha256=tKsDamW6PLpZGTEfdRDBUo1Tb94wqt-sDqWT3nS3dAA,22429
matplotlib/path.py,sha256=U4Rqw62KPISlvkh92jizjV45-3sF7fb4H3KTXndAD7w,41951
matplotlib/path.pyi,sha256=N1XOCWSUG4RtCcMo02yvvq3HDYo_369oKQkEJUHN80c,4777
matplotlib/patheffects.py,sha256=F5cVI8OcpypYIsuI4c5kwuTltYYQ0Mi11KDDt9w5ZcA,18602
matplotlib/patheffects.pyi,sha256=dLM3AsyfdkEG-0arwrMsfcY-2pR6xaOEbH0WPjjfUtI,3614
matplotlib/projections/__init__.py,sha256=lz18DR_57H8jweKkulJbiK1bPpF2a9CkvLhaao5ENWg,4436
matplotlib/projections/__init__.pyi,sha256=g0GJdggQcdnHUqdRVOeotOJYk1NDNzh_FuEfOn2EhHQ,579
matplotlib/projections/__pycache__/__init__.cpython-310.pyc,,
matplotlib/projections/__pycache__/geo.cpython-310.pyc,,
matplotlib/projections/__pycache__/polar.cpython-310.pyc,,
matplotlib/projections/geo.py,sha256=inOLKvVTfZUTC-1Wl0Rq9eXouplbcVbWptkRVsFTkj8,17711
matplotlib/projections/geo.pyi,sha256=vPfhvj7_e0ZnKjyfDUNC89RGCktycJBPnn5D8w0A7N8,3775
matplotlib/projections/polar.py,sha256=ZNbAK3LKh99qjFvPHiMetq_lyRWr7CLdUkXNoCpkPPY,56286
matplotlib/projections/polar.pyi,sha256=GZ4ja8ONCtzLInJZ6qPoEWCg9UMmnttFpRF9LluaVdY,6627
matplotlib/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/pylab.py,sha256=SxnC4wQYLIwlYWf_C_3WJtYTKbvCEp2LbOzGHqTAPxM,2301
matplotlib/pyplot.py,sha256=Eb2REBouwVoO06BSrQKyFfZE98BZMlG3qqE8vSzHFSw,138892
matplotlib/quiver.py,sha256=r9BRMfVIVqx0InFfslfNYxhchJAVfo1BOYl60gAhp_A,46139
matplotlib/quiver.pyi,sha256=IK_Ii3ls4IFFvYYKYFhiOvv6Ds8MkafQ11UN_aE9ews,5682
matplotlib/rcsetup.py,sha256=_zv8vOnUzltGiAh4_-hJKDLAcOqJa3YfjHsXDuZVU-Y,50700
matplotlib/rcsetup.pyi,sha256=l-5PtEiXpK9UsgpMlykAp-jx8ZBMV8KbuUheU79ivsQ,4233
matplotlib/sankey.py,sha256=fLWEQqiZNZwFN5nsGGuNg6Xm2G2uOnhJsPzbDAQiFnw,36158
matplotlib/sankey.pyi,sha256=xa6EMuSEZQYycZWdqlxIgWqQ7gfWKPKF9ZDK7WYQAC0,1462
matplotlib/scale.py,sha256=JV9ypuuymtNF2I932wSYApzfIkLpIjIM1eJv46w997w,25990
matplotlib/scale.pyi,sha256=-ptRptcqiAuzfKwrjgSWWOxFmjRUTOKGwIoWtuBXKgY,5057
matplotlib/sphinxext/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
matplotlib/sphinxext/__pycache__/__init__.cpython-310.pyc,,
matplotlib/sphinxext/__pycache__/figmpl_directive.cpython-310.pyc,,
matplotlib/sphinxext/__pycache__/mathmpl.cpython-310.pyc,,
matplotlib/sphinxext/__pycache__/plot_directive.cpython-310.pyc,,
matplotlib/sphinxext/figmpl_directive.py,sha256=GaIho4pRZ9f0vKx9g6BSj64AF2BdRWV6Z_uzamCytWs,9118
matplotlib/sphinxext/mathmpl.py,sha256=R24VAgKSOkYx2sarr2MGHgsosvKopih6JEIu8VM65wc,7810
matplotlib/sphinxext/plot_directive.py,sha256=6eoNy8lyELEpjYgUvrAkjEqDOc4-5ndQ-zjpF4gKQ74,32430
matplotlib/spines.py,sha256=7M0hU6vG9u6r2cMk2GcfbYuDzG_B1ElVMuZaxMW7ZSw,21582
matplotlib/spines.pyi,sha256=QXWS95dKxHbwzrtky-lrd4bXuj7eiz8mv54qIrMd0Ks,2956
matplotlib/stackplot.py,sha256=BDnOak2iTIC8HGEwLtMfXZ083IyCAlFg2C5Z71vcui0,4186
matplotlib/stackplot.pyi,sha256=gQ-f4nSQRTyleUA7vVrDks3LdltA0XUpOcZqAis9ZBE,491
matplotlib/streamplot.py,sha256=ERfimAS26vXOJlMjOJL4vpVQcJEVl-ww9Eoi0WC4wJ8,24000
matplotlib/streamplot.pyi,sha256=wA0K5e_ZjXneS5WzonHxoKPLla3-IdflAcIrB0svtOY,2664
matplotlib/style/__init__.py,sha256=fraQtyBC3TY2ZTsLdxL7zNs9tJYZtje7tbiqEf3M56M,140
matplotlib/style/__pycache__/__init__.cpython-310.pyc,,
matplotlib/style/__pycache__/core.cpython-310.pyc,,
matplotlib/style/core.py,sha256=P67uPZwbLqggEHTpKTc17G20QeUl00P7AFSONf77L2g,8609
matplotlib/style/core.pyi,sha256=DElPp0fbkwKaaVOVb5VGHfI8ohRB9m4uVGKjZl3A65E,449
matplotlib/table.py,sha256=HDE4WlXKrjaTOHF85rrjP_nWZacwuCki5AwgaMEop6A,27097
matplotlib/table.pyi,sha256=3KmxHBUoH6OC8eK8DvPLBmeAtqPwRgJ-1QioJg-i5kI,3048
matplotlib/testing/__init__.py,sha256=d0_AebzPmSzX0_C7Jc7MyWLkFTSGd-cFoWQ52hwf0Eg,4845
matplotlib/testing/__init__.pyi,sha256=A6iMznf2TaLorBIQHMUNLDy5huP6tQ9UHHDy84J_ibs,1573
matplotlib/testing/__pycache__/__init__.cpython-310.pyc,,
matplotlib/testing/__pycache__/_markers.cpython-310.pyc,,
matplotlib/testing/__pycache__/compare.cpython-310.pyc,,
matplotlib/testing/__pycache__/conftest.cpython-310.pyc,,
matplotlib/testing/__pycache__/decorators.cpython-310.pyc,,
matplotlib/testing/__pycache__/exceptions.cpython-310.pyc,,
matplotlib/testing/__pycache__/widgets.cpython-310.pyc,,
matplotlib/testing/_markers.py,sha256=0iNyOi25XLv_gTfSUqiRizdSqJzozePPBMRo72H2Je4,1419
matplotlib/testing/compare.py,sha256=TK2cXOJRHb4zB5JxVctuc54ueCQ7l0mbZ7gk45vLYQY,18943
matplotlib/testing/compare.pyi,sha256=xlJ4chgXKe567NUavlu-dalyPr4wAQUbd2Fz6aK_JII,1192
matplotlib/testing/conftest.py,sha256=-boEzYRlszzHJ2T9ZJq5qu8B38BlJ2hNpkzpIRGCSsA,3669
matplotlib/testing/conftest.pyi,sha256=4_MStQN6gy_h0YfhlPbqR81QVeVcn-FRqaKyyXedUEk,332
matplotlib/testing/decorators.py,sha256=viNGSCIQ9Ul_wjMCjrm-ebC_BClwU1Zkhxww6dq1m2k,18021
matplotlib/testing/decorators.pyi,sha256=0fSpdLBtEH7ZP_trVJ7RPxNtOX9sJ_z-MkNsbUxF8nM,872
matplotlib/testing/exceptions.py,sha256=72QmjiHG7DwxSvlJf8mei-hRit5AH3NKh0-osBo4YbY,138
matplotlib/testing/jpl_units/Duration.py,sha256=9FMBu9uj6orCWtf23cf6_9HCFUC50xAHrCzaxATwQfM,3966
matplotlib/testing/jpl_units/Epoch.py,sha256=-FGxeq-VvCS9GVPwOEE5ind_G4Tl9ztD-gYcW9CWzjo,6100
matplotlib/testing/jpl_units/EpochConverter.py,sha256=moULv8w8ArfiKqqQOZvC6zhrwGLZApAryB9L4nKSCqA,3058
matplotlib/testing/jpl_units/StrConverter.py,sha256=codGw9b_Zc-MG_YK4CiyMrnMR8ahR9hw836O2SsV8QI,2865
matplotlib/testing/jpl_units/UnitDbl.py,sha256=EABjyEK4MVouyvlwi_9KdYDg-qbYY3aLHoUjRw37Fb0,5882
matplotlib/testing/jpl_units/UnitDblConverter.py,sha256=B8DssrQVyC4mwvSFP78cGL0vCnZgVhDaAbZE-jsXLUg,2828
matplotlib/testing/jpl_units/UnitDblFormatter.py,sha256=246hgA4_pCfJm-P94hEsxqnTS9t0XlvLC8p1v_bw2pU,657
matplotlib/testing/jpl_units/__init__.py,sha256=p__9RUwrt2LJ2eoT2JPM-42XLxSJrfA4az3rN5uP6d4,2684
matplotlib/testing/jpl_units/__pycache__/Duration.cpython-310.pyc,,
matplotlib/testing/jpl_units/__pycache__/Epoch.cpython-310.pyc,,
matplotlib/testing/jpl_units/__pycache__/EpochConverter.cpython-310.pyc,,
matplotlib/testing/jpl_units/__pycache__/StrConverter.cpython-310.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDbl.cpython-310.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblConverter.cpython-310.pyc,,
matplotlib/testing/jpl_units/__pycache__/UnitDblFormatter.cpython-310.pyc,,
matplotlib/testing/jpl_units/__pycache__/__init__.cpython-310.pyc,,
matplotlib/testing/widgets.py,sha256=5F__S7Ts1xY7IitlKjgxRwBnF8GNrqpAxVqelejvoNc,3471
matplotlib/testing/widgets.pyi,sha256=Ioau7Q2aPRDZLx8hze2DOe3E1vn7QPxePC74WMR7tFc,831
matplotlib/tests/__init__.py,sha256=XyXveEAxafB87gnbx0jkC0MggzKO8FvORq_6RtJRwo4,366
matplotlib/tests/__pycache__/__init__.cpython-310.pyc,,
matplotlib/tests/__pycache__/conftest.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_afm.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_agg.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_agg_filter.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_animation.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_api.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_arrow_patches.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_artist.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_axes.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_axis.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_bases.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_cairo.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_gtk3.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_macosx.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_nbagg.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_pdf.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_pgf.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_ps.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_qt.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_svg.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_template.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_tk.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_tools.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backend_webagg.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_backends_interactive.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_basic.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_bbox_tight.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_category.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_cbook.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_collections.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_colorbar.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_colors.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_compare_images.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_constrainedlayout.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_container.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_contour.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_cycles.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_dates.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_determinism.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_doc.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_dviread.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_figure.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_font_manager.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_fontconfig_pattern.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_ft2font.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_getattr.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_gridspec.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_image.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_legend.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_lines.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_marker.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_mathtext.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_matplotlib.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_mlab.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_offsetbox.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_patches.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_path.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_patheffects.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_pickle.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_png.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_polar.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_preprocess_data.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_pyplot.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_quiver.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_rcparams.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_sankey.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_scale.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_simplification.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_skew.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_sphinxext.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_spines.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_streamplot.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_style.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_subplots.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_table.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_testing.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_texmanager.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_text.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_textpath.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_ticker.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_tightlayout.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_transforms.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_triangulation.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_ttconv.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_type1font.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_units.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_usetex.cpython-310.pyc,,
matplotlib/tests/__pycache__/test_widgets.cpython-310.pyc,,
matplotlib/tests/conftest.py,sha256=HNR8xF4YiUoKY03WKoeABGwSRcKFMwI9cNFXZJDzz-A,119
matplotlib/tests/test_afm.py,sha256=A7jm2o-QaQH9SSpiFxGtZkbVU0LJZE69jfPv7RczOD4,3701
matplotlib/tests/test_agg.py,sha256=BwRu6BuTWPZYsc2YxcfllV9cvZ7WPbCe36Alhmkr-6k,10740
matplotlib/tests/test_agg_filter.py,sha256=3c_Smtb4OHEOfdMFCOb2qKhzMXSbNoaUtsJ0pW47Q44,1067
matplotlib/tests/test_animation.py,sha256=NPW6oWLATBsl43IATIjCcHUdxeZx7Fz7bRvNqVwyCW0,17798
matplotlib/tests/test_api.py,sha256=fTw5hpYH_N_2YeoYcKJwLwFwn4LXDAOy-ZgvyWwU6x4,3503
matplotlib/tests/test_arrow_patches.py,sha256=mo3V1gorHQiIU4Xglw_wUeyraLjXoA-HfHpH_srxtpI,6434
matplotlib/tests/test_artist.py,sha256=9ZQ82ZP4PKIuzucDD45phJZBDnPJkdbcPeHzrGNhHF8,17433
matplotlib/tests/test_axes.py,sha256=Tdz1mHNPMAGoRGp0l-CXPxOOkVF5RscpLZRCfLi0-e4,297503
matplotlib/tests/test_axis.py,sha256=ecaZ66ng6NkwrffxyEug9GzEx4YouY49xamqyvrJ0kA,268
matplotlib/tests/test_backend_bases.py,sha256=cLMAYOtYtSpezZjuwYZ9UHp8FTlRoMnKsdh4N3nMqD4,17031
matplotlib/tests/test_backend_cairo.py,sha256=O2LTYjsfPn__bKtTz4MGGBodpSshoPkzu0INsc18xmI,1821
matplotlib/tests/test_backend_gtk3.py,sha256=cQjYQLyALMXgNGHDqucC1_-gPBxFwQGQJk-6Yh1SqrM,1800
matplotlib/tests/test_backend_macosx.py,sha256=EOWBfQMJxYlo0bHmXdY-AcBZRbC23dZYghxjRJskEXM,1541
matplotlib/tests/test_backend_nbagg.py,sha256=3IfwLcdSPRupcUMWvadOWEAJG5kLywphVH5EO-ZcMx0,973
matplotlib/tests/test_backend_pdf.py,sha256=jMh8A74bMeLFiDfskQNTj3lgrZGiIMkKel0GH_Dob08,14517
matplotlib/tests/test_backend_pgf.py,sha256=CqFswrGIDSlJIiMCTLwkRsFxn1iCVg9_RZAiLD04M4M,13188
matplotlib/tests/test_backend_ps.py,sha256=CCx6iTqcQS7KP4ygHFa9NlQfEmC_ysVLow9wAAsVIWU,12573
matplotlib/tests/test_backend_qt.py,sha256=9MkT2LvT-rC1yBLWzaVZiOh-WZJVhkwFdSm-vpebj2k,12175
matplotlib/tests/test_backend_svg.py,sha256=zChB85qffPQ9KmFQQ1OCZH0F3GhqVuZiu8Ac_MjXADw,21053
matplotlib/tests/test_backend_template.py,sha256=uEubnnxnQdoODjRDuFb6wigOb43WEqhNWUI9Oq7_tr8,1781
matplotlib/tests/test_backend_tk.py,sha256=y0l2m4IgxVs3MSiUbNfrirGcOCuEhUXuOsaHsUFL0bo,8341
matplotlib/tests/test_backend_tools.py,sha256=C-B7NCkyWsQ5KzQEnI5Be16DsAHHZJU9P5v9--wsF-o,501
matplotlib/tests/test_backend_webagg.py,sha256=FAxgYBmn5G0t9dlPNbiHHgKmF18WiJaDBZ2EEpgjJCg,901
matplotlib/tests/test_backends_interactive.py,sha256=n3ll1qGGMh5DWCFBMKqWzCySapoztC3aoUln-j-brr4,29651
matplotlib/tests/test_basic.py,sha256=Foe8KBcSbULganUMVhV-Kij0IVmHbugg3JJTMUIAfSg,1067
matplotlib/tests/test_bbox_tight.py,sha256=TgKhtUXVKblIxANpoX2PfmFiuoEk0NRpSXMxLD-Vg-4,6255
matplotlib/tests/test_category.py,sha256=S6Y681G-wU1R10GCdtDMmR6fbVGURSLzFOWu0SNkON8,11700
matplotlib/tests/test_cbook.py,sha256=RnHsHYm_A9HFEFRUNHImRg7dospuqpAjj9D-IAHcqds,30222
matplotlib/tests/test_collections.py,sha256=GZzmQi4asGWy9t4bVr6yKLqYctFeM_xyQO9easLAcg8,44833
matplotlib/tests/test_colorbar.py,sha256=xw82nvsRmFf3K_QoZj3BUq0NSzkpZEjQRAbUlB5UnTs,46396
matplotlib/tests/test_colors.py,sha256=X9VQx_KOlyVaYuMB4FVBI6LpJA_D3913wxB5Mf_FZZQ,60203
matplotlib/tests/test_compare_images.py,sha256=NcBoT8HAxtzoR1zZBu0V3MFpGWtdFaDblQ8o3OTO6zM,3260
matplotlib/tests/test_constrainedlayout.py,sha256=W-MhahssaO1jZm12HGtRTdxUJh9QTXqgWckPuUS25eQ,22651
matplotlib/tests/test_container.py,sha256=FTewckOd3dJqLOzEUa29Itjqusk7Mx7MK5xPVMhzMmc,694
matplotlib/tests/test_contour.py,sha256=03TLGMrbcCGxmEE6Z7P4vwfMYo5DQApRO4yYM3flOr0,32861
matplotlib/tests/test_cycles.py,sha256=Vijb0fyTkcxgl7Hr1iXO-jP4IooxD_CsaTDsnp9htfY,5878
matplotlib/tests/test_dates.py,sha256=xKW5Zqw_sQBYftnVuFnYVm8VMMhu5bu_leEjhpZabHg,56619
matplotlib/tests/test_determinism.py,sha256=T6lmhemq2ROWsQn32qDkIqZMGOfmeG1_4Ak7QT4T0AA,4464
matplotlib/tests/test_doc.py,sha256=U56V3No8ZUcVmOrKy1NeatGmLdwOLKz9gwTC5dWmtU4,972
matplotlib/tests/test_dviread.py,sha256=JeTuA2FMUj1FddxDVBXUtnvZYTgztE-CyRXL_mI20P0,2764
matplotlib/tests/test_figure.py,sha256=zrYtgYB_R7LnxEZD3GF6GesNY-1FiPD3NUUxENgJZ4U,55334
matplotlib/tests/test_font_manager.py,sha256=xd33v4HA13Q3mKBLaTfqvZNLWCuuYFZgkLnEOqKt6CY,11751
matplotlib/tests/test_fontconfig_pattern.py,sha256=nkXV7NOYShEnD0eUnnt1MJrLRBKU2DCNOLjyb8cgXP8,2151
matplotlib/tests/test_ft2font.py,sha256=ylr6CbOjudWXC43w5NiqHw__KEBNRqLoxlKZj4OZLvQ,3783
matplotlib/tests/test_getattr.py,sha256=xUJN9q8BeShYA73CFLJl-bxU_xtbQ1mcJUxWNEU0sAg,1081
matplotlib/tests/test_gridspec.py,sha256=OOCSE5R6xq3XI8Bstm0xMR1hv4guHFBCAwfCXrk_Td0,960
matplotlib/tests/test_image.py,sha256=Jm2IMNUWOUQKuCN-iPhGRdT9xAVGhCnkXUSpGHIdKEk,49482
matplotlib/tests/test_legend.py,sha256=4ew_-UqJOLjkGj6r-bLO3_iIqd0gAei43VxcSmO2pCU,49099
matplotlib/tests/test_lines.py,sha256=pnrQmvDUthywpLenv34GfeRD4xf7vQ7fNT2JUTK4GEU,14431
matplotlib/tests/test_marker.py,sha256=w0WVHoaD-6iybjUSENoVFFdUOOR13943JcE4sgz3qhI,11410
matplotlib/tests/test_mathtext.py,sha256=E67lZISGOEgc2LsS6kvm_VwmbaG9XYiMXA1umXljvdw,24518
matplotlib/tests/test_matplotlib.py,sha256=u_7TPPxFE8Ry9GufBSg2YCa4asgNZCU3A1mUBDHmWGk,2645
matplotlib/tests/test_mlab.py,sha256=d4qMyogTFMrvlRZEpDs7SjhSmmCnBUMNgSX2bJU6eDk,42269
matplotlib/tests/test_offsetbox.py,sha256=oZebQkeaXJWffCCdFYh7pGqmLpnpKT_8OQxNf7b-ODo,15712
matplotlib/tests/test_patches.py,sha256=NK4NLaYrw2oZwEApH3iaeUPGGi50zqsAOp4sgYmxeBY,31246
matplotlib/tests/test_path.py,sha256=Gb2MNVjWe6yqJHGLtenhOuyehDcfTkesyMRSG-iPZ0Q,19078
matplotlib/tests/test_patheffects.py,sha256=Sw_9hmqIZOPmmqj3boDV9InWYVnyNBpebIkXad4mR6c,7315
matplotlib/tests/test_pickle.py,sha256=FFf9_7QoXG0ax4FNvMLkbeZ5h4-b5koOJt2E7KjZnwU,8627
matplotlib/tests/test_png.py,sha256=xyXs9STg5WyoKCeqyK7zNoj5OGYlkJZIwP_GMIJqLTA,1240
matplotlib/tests/test_polar.py,sha256=OKmlBe0Lb4F3Wks9eAJEGm38U5eMeESeA0OG6uOZf84,15285
matplotlib/tests/test_preprocess_data.py,sha256=cIVICUi1iahMQS30sqI5IlT2RYJRH2gJ0z60FyuYUFk,11363
matplotlib/tests/test_pyplot.py,sha256=oVnAzyhAdJzZY1i_Wcn6GIRWb8Yx_lo5rHdUupdryBU,13069
matplotlib/tests/test_quiver.py,sha256=XSbuKXwixVChM-VqRkfNJCTQTrufswXiAKL0XigQHVI,8531
matplotlib/tests/test_rcparams.py,sha256=19Lu1FZQI7UWrkCutCM9JChQ0pEfv5tjUglSpNFgZrs,24334
matplotlib/tests/test_sankey.py,sha256=yg4i-qHT5MljFzFGViOnHbMkumM8bhPwRgoQ5M6CUEs,3900
matplotlib/tests/test_scale.py,sha256=3jUrfVsyxRE9FpmtWDC-ySo04E26s-ahDL9O3iKeEpQ,8411
matplotlib/tests/test_simplification.py,sha256=ucPf9b5yLajXiP3FklEmwW2ys3-K1q2JpsgaHB1B2Hc,18784
matplotlib/tests/test_skew.py,sha256=tABwW01MBPdDaNkxnXsbZOB3R7-O6sPfazbrn1WUR0k,6255
matplotlib/tests/test_sphinxext.py,sha256=N-mey5MZWVaLxbN1wPvwqisV-u2ewvp9mA0uFQaPSEo,9724
matplotlib/tests/test_spines.py,sha256=q61XCKIySvm32S3imHA3SR5vVRGFVo-VUCx_LpKN_Fc,4893
matplotlib/tests/test_streamplot.py,sha256=QJwU1t8bUtunwPN22TJcHRj--ILPq_V2iwHrvbnmzCY,5723
matplotlib/tests/test_style.py,sha256=c6URenpDzmARn8CysN0y1wA1n4Ja2R_GklGMlMRn3Ic,6537
matplotlib/tests/test_subplots.py,sha256=IbjYJd7Kfdu342Y_vOdblwpYXXAjI1oOyiRkyRc4Z14,10681
matplotlib/tests/test_table.py,sha256=h6efdR5QEHY6bwbuLfi1DNWM7suPKV1xWFTBp6QIO60,6949
matplotlib/tests/test_testing.py,sha256=eh-1r4PIXcM7hfSKCNTU899QxRYWhZBg6W7x0TDVUxo,1057
matplotlib/tests/test_texmanager.py,sha256=isMI3BUIoN90zCsTHsEspp4ARAwxEuyYOnna1Zc9dH8,2595
matplotlib/tests/test_text.py,sha256=FQcuTDdQ6hLCd-IckhdFtbZ4gbQzdkmUiMdv4ktEGZY,32195
matplotlib/tests/test_textpath.py,sha256=WLXvT5OzE6Tew4pr87LH-cgioCzM7srgMNRemiMEC5o,271
matplotlib/tests/test_ticker.py,sha256=8fqi00uJugqE4yaNeb7G8Fgkn4iUmsEZDaaZOl_f-ZE,68902
matplotlib/tests/test_tightlayout.py,sha256=hcG-BqPGQZydJ9STyDkAcsSIuBMTtjvJG_BanPPYj_I,12704
matplotlib/tests/test_transforms.py,sha256=VWygTpPJywKiEAzOL6MGJAWNu8hVvGL-2v9gbVIsTco,30335
matplotlib/tests/test_triangulation.py,sha256=SlOePrPHzlwKiK9GIOXFkYEvsoO6oOSv3Cwq8ZxjMeI,54943
matplotlib/tests/test_ttconv.py,sha256=yW3YkGWTh-h9KXcyy70dEH4ytVuhMlzl-HMsNbK_gX8,540
matplotlib/tests/test_type1font.py,sha256=7TUXy8ehA3el-zvd4U9bnocYbelssDhUeph5cOFoaE4,6369
matplotlib/tests/test_units.py,sha256=Gihd679M0rN8JkvcBq3Azr9zk2or4dqVpJRo7aPD-fM,9306
matplotlib/tests/test_usetex.py,sha256=a-Y6NuyROPHDGP2ELsOZNSVwBUoGtAv1xQZfisl9lSE,6405
matplotlib/tests/test_widgets.py,sha256=aQUoeoQ00J8qGpJfZl7zzC9Ur6hHDTBj2DBuW5wjCVE,67268
matplotlib/texmanager.py,sha256=zbnzSYgmoyTB3rapePXvbH9CrLMmxIL3GEPnV2KN1Ys,15098
matplotlib/texmanager.pyi,sha256=di3gbC9muXKTo5VCrW5ye-e19A1mrOl8e8lvI3b904A,1116
matplotlib/text.py,sha256=f5PDh9VtqfPW1Y15bDG64sLgvxAOAcOEo9qob_xPtQA,70250
matplotlib/text.pyi,sha256=LGMi0PLN7hlWeNVyS57bePXGjpXWhIy1AqyS2h7mCak,8027
matplotlib/textpath.py,sha256=TUyF7xSUzj-6_agCuGl-8kX57hBqsGZIROMHwWAZ1ro,13254
matplotlib/textpath.pyi,sha256=rqOeTAeQYgm2b2NpetrEX0gMF8PzzW43xS5mNfUA98M,2529
matplotlib/ticker.py,sha256=Q-6yt8F3RvrxNTIl8zM_p8aSfFUVXa6Wb0w2dHV4m6Q,103958
matplotlib/ticker.pyi,sha256=konxZrZgNRz8PpaEHadIVa0WSIdRh5ORisDEuaUzSbA,10208
matplotlib/transforms.py,sha256=1N1WFM5KU6rTuxuYI4YSTPYp36nR3AmwGKPzcSAvQIY,99395
matplotlib/transforms.pyi,sha256=E_I9VfgTtA3Df2mUROfwG-gyvccJ4KOCp3nFt1qWixc,11978
matplotlib/tri/__init__.py,sha256=asnfefKRpJv7sGbfddCMybnJInVDPwgph7g0mpoh2u4,820
matplotlib/tri/__pycache__/__init__.cpython-310.pyc,,
matplotlib/tri/__pycache__/_triangulation.cpython-310.pyc,,
matplotlib/tri/__pycache__/_tricontour.cpython-310.pyc,,
matplotlib/tri/__pycache__/_trifinder.cpython-310.pyc,,
matplotlib/tri/__pycache__/_triinterpolate.cpython-310.pyc,,
matplotlib/tri/__pycache__/_tripcolor.cpython-310.pyc,,
matplotlib/tri/__pycache__/_triplot.cpython-310.pyc,,
matplotlib/tri/__pycache__/_trirefine.cpython-310.pyc,,
matplotlib/tri/__pycache__/_tritools.cpython-310.pyc,,
matplotlib/tri/__pycache__/triangulation.cpython-310.pyc,,
matplotlib/tri/__pycache__/tricontour.cpython-310.pyc,,
matplotlib/tri/__pycache__/trifinder.cpython-310.pyc,,
matplotlib/tri/__pycache__/triinterpolate.cpython-310.pyc,,
matplotlib/tri/__pycache__/tripcolor.cpython-310.pyc,,
matplotlib/tri/__pycache__/triplot.cpython-310.pyc,,
matplotlib/tri/__pycache__/trirefine.cpython-310.pyc,,
matplotlib/tri/__pycache__/tritools.cpython-310.pyc,,
matplotlib/tri/_triangulation.py,sha256=Ur2lKMOx4NrZxwyi0hBeBnVzicuKaCke0NkrZneSklM,9784
matplotlib/tri/_triangulation.pyi,sha256=pVw1rvpIcl00p7V7E9GcvJSqQWyoxlZXX_p0_VSxTiY,1017
matplotlib/tri/_tricontour.py,sha256=R5vUYYHwVEXwatHp3bqYSU6YctDJpihaBaI6EPbCOQ4,10318
matplotlib/tri/_tricontour.pyi,sha256=jnsAmVRX0-FOUw9ptUgci9J4T4JQRloKeH8fh8aAi-o,1155
matplotlib/tri/_trifinder.py,sha256=3gUzJZDIwfdsSJUE8hIKso9e1-UGvynUN9HxaqC1EEc,3522
matplotlib/tri/_trifinder.pyi,sha256=dXcZucacAS3Ch6nrDBPh2e3LYZLfZ7VwqpBUBb-vMPo,405
matplotlib/tri/_triinterpolate.py,sha256=4FtyJSoJpHcFxkSkZHZ1aNengVNWqVKF4l78PgCH8O0,62445
matplotlib/tri/_triinterpolate.pyi,sha256=SPuetoGqDlE5jo48yQQazqTY4NfcQ3_2ZYqEE6LFkTw,964
matplotlib/tri/_tripcolor.py,sha256=3j5J67vO3HuTAfnaZm93wyilpmEX-bX4eiJNAsbOZJM,6275
matplotlib/tri/_tripcolor.pyi,sha256=QsA-A2ohj3r_tAElt2-9pzi47JiU01tNlRPDIptqnh4,1781
matplotlib/tri/_triplot.py,sha256=jlHSz36Z5S18zBKc639PlSqdhfl7jHol8ExlddJuDI4,3102
matplotlib/tri/_triplot.pyi,sha256=9USU-BfitrcdQE8yWOUlBX59QBNoHCWivDon9JbDQ0k,446
matplotlib/tri/_trirefine.py,sha256=NG8bsDhZ5EOxMT-MsEWzJm11ZR3_8CAYHlG53IGi0ps,13178
matplotlib/tri/_trirefine.pyi,sha256=J_PmjbeI6UbLaeecgj1OCvGe_sr9UUsNK9NGBSlQ320,1056
matplotlib/tri/_tritools.py,sha256=wC9KVE6UqkWVHpyW9FU4hQdqRVRVmJlhaBF1EXsaD8U,10575
matplotlib/tri/_tritools.pyi,sha256=XWwwvH2nIAmH8k59aRjnLBVQbTwKvd_FzdsRNASCJMw,402
matplotlib/tri/triangulation.py,sha256=UPkIhytAtpBUXXz2-CmHcB_oYwca91xTwu5yBPzz34o,332
matplotlib/tri/tricontour.py,sha256=FewF6-fHvOZAptUW3z1CZvNgKbmJIQ2BE_KBWBz2KO0,329
matplotlib/tri/trifinder.py,sha256=zq8aU90q5fO3peb8s58g-DFUy4qtaD2QrfR6NkpEzPI,328
matplotlib/tri/triinterpolate.py,sha256=BxDRUN6JsFh7ZJV0hvHKZrBkhztBhuseoRZ1mz-wZXA,333
matplotlib/tri/tripcolor.py,sha256=I4wz5Bt1vmB_QznwrkUewicYN7KUDig8iT3lHRppexs,328
matplotlib/tri/triplot.py,sha256=s4M-7KIJTy9ETLSTjl9KicK8IMojE40YtzLQU9eIGqg,326
matplotlib/tri/trirefine.py,sha256=T9Oe3-WPlqM-psE7lFKH1o53bXzXE2L8mnPjuJVYD-8,328
matplotlib/tri/tritools.py,sha256=f7qZzFSh6-IKWDlIMogjlFRKabwpNJK_V-W7BQmIm1g,327
matplotlib/typing.py,sha256=CaCkp84zE_7aDG_Ee6mZfI5k4LG60f354orvBrIvLLg,2098
matplotlib/units.py,sha256=7O-llc8k3GpdotUs2tWcEGgoUHHX-Y7o0R7f-1Jve3k,6429
matplotlib/widgets.py,sha256=JiVnBrPO8412mdlL5_GcjYe8F2qxyI9ldBpOjJS8ikU,155497
matplotlib/widgets.pyi,sha256=5-S8dWT3gQQiywp5xsXXHfhqK1IL70uvKJnvw31BclU,15306
mpl_toolkits/axes_grid1/__init__.py,sha256=wiuUCQo1g20SW5T3mFOmI9dGCJY6aDmglpQw5DfszEU,371
mpl_toolkits/axes_grid1/__pycache__/__init__.cpython-310.pyc,,
mpl_toolkits/axes_grid1/__pycache__/anchored_artists.cpython-310.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_divider.cpython-310.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_grid.cpython-310.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_rgb.cpython-310.pyc,,
mpl_toolkits/axes_grid1/__pycache__/axes_size.cpython-310.pyc,,
mpl_toolkits/axes_grid1/__pycache__/inset_locator.cpython-310.pyc,,
mpl_toolkits/axes_grid1/__pycache__/mpl_axes.cpython-310.pyc,,
mpl_toolkits/axes_grid1/__pycache__/parasite_axes.cpython-310.pyc,,
mpl_toolkits/axes_grid1/anchored_artists.py,sha256=pRyYM69J3iEdIZq5IpbCOQBXD6LbkiE-SPCEh-ZDKw4,19211
mpl_toolkits/axes_grid1/axes_divider.py,sha256=Q5mdTLc7k5z660u53c-XdfNf_cDSFIGnqp9Tgb3uXJQ,24557
mpl_toolkits/axes_grid1/axes_grid.py,sha256=gQ_O-YpzkB4db3QAbDHDBHgJMwEFew54FFKIqdvIfzg,21971
mpl_toolkits/axes_grid1/axes_rgb.py,sha256=pabgaWJuLTCPw2FlT6Zfy5d0_95CEvaLeosWRTElR98,5227
mpl_toolkits/axes_grid1/axes_size.py,sha256=LKuHOh7WJ1w9QkL7CbxngeAu_WP8mD2E7GfUgoAn4_c,7012
mpl_toolkits/axes_grid1/inset_locator.py,sha256=0FzpYiTGKKHewYFzDGP_giwVm5dMGtdbbDbyjXKqD28,21133
mpl_toolkits/axes_grid1/mpl_axes.py,sha256=vFCttnj9JIgY3Mt2eOi-O_FVvdZ6SW_sBtIBFib6bz4,4251
mpl_toolkits/axes_grid1/parasite_axes.py,sha256=T-ve7kZbh3WLKPSr6X7MrFfwN9LaQh71i1ADtJ-Feyk,9416
mpl_toolkits/axes_grid1/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/axes_grid1/tests/__pycache__/__init__.cpython-310.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/conftest.cpython-310.pyc,,
mpl_toolkits/axes_grid1/tests/__pycache__/test_axes_grid1.cpython-310.pyc,,
mpl_toolkits/axes_grid1/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/axes_grid1/tests/test_axes_grid1.py,sha256=ljX55Sy4qWpOEJpHBhprHp_IMOiKrT5yYXu-T7-A1NQ,29373
mpl_toolkits/axisartist/__init__.py,sha256=w0sQlZrE1LIRd3dqCp9y42111ESJONGjZSwnhBzh8dQ,553
mpl_toolkits/axisartist/__pycache__/__init__.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/angle_helper.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_divider.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_grid.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/axes_rgb.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/axis_artist.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/axisline_style.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/axislines.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/floating_axes.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_finder.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/grid_helper_curvelinear.cpython-310.pyc,,
mpl_toolkits/axisartist/__pycache__/parasite_axes.cpython-310.pyc,,
mpl_toolkits/axisartist/angle_helper.py,sha256=-mjKpaR1pLMJuoc0sx0_V3bv0iRPMrpS7r_WI0UYrCc,12952
mpl_toolkits/axisartist/axes_divider.py,sha256=ltdw9uabafN8MXqTcmtpA2mVFKmDqswCDzPZA6Qp2oo,135
mpl_toolkits/axisartist/axes_grid.py,sha256=MiF5JTwOy8mKBpicIQk_itM0Mvcaq9-ctdayPR3JQto,593
mpl_toolkits/axisartist/axes_rgb.py,sha256=gfqcc7N3iAFGfShu1g2516unMj5hvNoeF2YRSJslopQ,532
mpl_toolkits/axisartist/axis_artist.py,sha256=lzFeyR1RCIvRFv2SCz-FNQHugQjtEMU2rX4fWn7XXnI,38158
mpl_toolkits/axisartist/axisline_style.py,sha256=weljspcXdP0y4ccjXp4NxX1A20J6clKfNS4VathUnyU,6712
mpl_toolkits/axisartist/axislines.py,sha256=kks72YQIYceqpp4QI0bX09NsGKTwTj4DGSN8HerxPks,18006
mpl_toolkits/axisartist/floating_axes.py,sha256=iXeM9w4qKTi6Sk9dYM20CIM5AYW0azDZxSDhMdLMyso,10873
mpl_toolkits/axisartist/grid_finder.py,sha256=UTGkEAvCRAwKjzLANK5EfpojAQ--A82KCmaF_cB-mx8,12167
mpl_toolkits/axisartist/grid_helper_curvelinear.py,sha256=PxUXCWC8sRYXI7IDuOQVs85th_xYmfy_nb84ti6hd7c,12644
mpl_toolkits/axisartist/parasite_axes.py,sha256=Ydi4-0Lbczr6K7Sz1-fRwK4Tm8KlHrOIumx67Xbo_9c,244
mpl_toolkits/axisartist/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/axisartist/tests/__pycache__/__init__.cpython-310.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/conftest.cpython-310.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_angle_helper.cpython-310.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axis_artist.cpython-310.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_axislines.cpython-310.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_floating_axes.cpython-310.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_finder.cpython-310.pyc,,
mpl_toolkits/axisartist/tests/__pycache__/test_grid_helper_curvelinear.cpython-310.pyc,,
mpl_toolkits/axisartist/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/axisartist/tests/test_angle_helper.py,sha256=PwhJwBm2kk4uMyhdO5arQs8IlqSX2vN0hvUzI7YHqrw,5670
mpl_toolkits/axisartist/tests/test_axis_artist.py,sha256=wt3bicVgUPnBX48-dH0Z6hboHgutIgwVpaGkcUZDeVU,2980
mpl_toolkits/axisartist/tests/test_axislines.py,sha256=fmAVzqSgbLdqtBhq1G0DADySb6Rx5wdgMJUPlfEB-pk,4361
mpl_toolkits/axisartist/tests/test_floating_axes.py,sha256=ZlFU5btNHDb8Rk5OzBY98zoN-ttGfDG7-Mi1q5Oc65Q,4027
mpl_toolkits/axisartist/tests/test_grid_finder.py,sha256=cwQLDOdcJbAY2E7dr8595yzuNh1_Yh80r_O8WGT2hMY,1156
mpl_toolkits/axisartist/tests/test_grid_helper_curvelinear.py,sha256=F3tcHrFJS7EIlWEMeiPk6KFLn46IB5LpddnNSNkPtZQ,7270
mpl_toolkits/mplot3d/__init__.py,sha256=fH9HdMfFMvjbIWqy2gjQnm2m3ae1CvLiuH6LwKHo0kI,49
mpl_toolkits/mplot3d/__pycache__/__init__.cpython-310.pyc,,
mpl_toolkits/mplot3d/__pycache__/art3d.cpython-310.pyc,,
mpl_toolkits/mplot3d/__pycache__/axes3d.cpython-310.pyc,,
mpl_toolkits/mplot3d/__pycache__/axis3d.cpython-310.pyc,,
mpl_toolkits/mplot3d/__pycache__/proj3d.cpython-310.pyc,,
mpl_toolkits/mplot3d/art3d.py,sha256=T15BprB7XRtHgIvNriItjvcD3adM-W2ri5YEMrDSYpA,42573
mpl_toolkits/mplot3d/axes3d.py,sha256=4I19Ucq6N70IlLEzah3Wvekx3lNcgXEvqfdcl45SLe0,129991
mpl_toolkits/mplot3d/axis3d.py,sha256=oU7E45pHTAFI5t3cQ_ezni-0M5f6YL25GCVetLusZqg,29488
mpl_toolkits/mplot3d/proj3d.py,sha256=exvdG39Py5cXCzzfGnLJSvn5MZ-EE-MRRGejMdAW0Aw,6933
mpl_toolkits/mplot3d/tests/__init__.py,sha256=sKLxL9jEJBX7eh5OumtXSOnTriPrJUkujTHFtnJVFrM,365
mpl_toolkits/mplot3d/tests/__pycache__/__init__.cpython-310.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/conftest.cpython-310.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_art3d.cpython-310.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_axes3d.cpython-310.pyc,,
mpl_toolkits/mplot3d/tests/__pycache__/test_legend3d.cpython-310.pyc,,
mpl_toolkits/mplot3d/tests/conftest.py,sha256=zB61sy90X97YJ16mIGiuaEAaBIjBEzRAK_qfSCichQM,147
mpl_toolkits/mplot3d/tests/test_art3d.py,sha256=wpRhsMkGD8HdBCyIXM3SnydJWUlD6I5FRlZJM5xD_5w,1817
mpl_toolkits/mplot3d/tests/test_axes3d.py,sha256=b6y24WXhStzQeTkcDLbTJwbINRsE2zLghygKN4CbVJU,76468
mpl_toolkits/mplot3d/tests/test_legend3d.py,sha256=B8g2oyMdUvRCGiWZKtD5bOhPxu1l1KirYIZEugJ49Vg,4406
pylab.py,sha256=u_By3CHla-rBMg57egFXIxZ3P_J6zEkSu_dNpBcH5pw,90
